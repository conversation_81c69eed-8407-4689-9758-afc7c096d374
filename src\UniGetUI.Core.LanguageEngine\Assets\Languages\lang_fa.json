{"\"{0}\" is a local package and can't be shared": "{0} بسته محلی قابل اشتراک گذاری نیست", "\"{0}\" is a local package and does not have available details": "{0} یک بسته محلی است و اطلاعاتی برای آن در دسترس نیست", "\"{0}\" is a local package and is not compatible with this feature": "{0} یک بسته محلی است و با این قابلیت سازگار نیست", "(Last checked: {0})": "(آخرین بار چک شده: {0})", "(Number {0} in the queue)": "(شماره {0} در صف)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@MobinMardi", "0 packages found": "بسته ای پیدا نشد", "0 updates found": "به‌روزرسانی ای پیدا نشد", "1 - Errors": "1 - خطا ها", "1 day": "۱ روز", "1 hour": "۱ ساعت", "1 month": "۱ ماه", "1 package was found": "یک بسته پیدا شد", "1 update is available": "1 به روز رسانی موجود است", "1 week": "۱ هفته", "1 year": "۱ سال", "1. Navigate to the \"{0}\" or \"{1}\" page.": "۱. به صفحه {0} یا {1} بروید", "2 - Warnings": "۲ - هشدار ها", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "۲. بسته (هایی) را که میخواهید به مجموعه اضافه کنید پیدا کرده و چک باکس سمت چپ آنها را انتخاب کنید", "3 - Information (less)": "۳ - اطلاعات (کمتر)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "۳. وقتی بسته‌هایی که می‌خواهید به بسته ساز اضافه کنید را انتخاب کردید، گزینه {0} را در نوار ابزار پیدا کرده و کلیک کنید", "4 - Information (more)": "۴ - اطلاعات (بیشتر)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "۴. بسته‌های شما به مجموعه اضافه شده‌اند. می‌توانید به اضافه کردن بسته‌های بیشتر ادامه دهید یا مجموعه را از برنامه خارج کنید", "5 - information (debug)": "۵ - اطلاعات (اشکال یابی)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "یک برنامه مدیریتی محبوب برای کتابخانه‌های C/C++. پر از کتابخانه‌ها و ابزارهای مرتبط با C/C++<br> شامل می‌شود: <b> کتابخانه‌ها و ابزارهای C/C++</b> ", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "یک مخزن پر از ابزار ها و فایل های اجرایی طراحی شده با در نظر گرفتن اکوسیستم مایکروسافت دات نت. <br> دارای: <b>ابزار ها و اسکریپت های مربوط به دات نت</b>\n", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "یک مخزن پر از ایزار هایی با در نظر گرفتن اکوسیستم مایکروسافت دات نت. <br> دارای: <b>ابزار های مربود به دات نت</b>", "A restart is required": "یک راه اندازی مجدد نیاز است", "Abort install if pre-install command fails": "اگر اجرای دستور پیش-نیاز نصب شکست خورد، نصب انجام نشه", "Abort uninstall if pre-uninstall command fails": "اگر اجرای دستور پیش نیاز حذف نصب شکست خورد، حذف نصب انجام نشه", "Abort update if pre-update command fails": "اگر اجرای دستور پیش نیاز آپدیت شکست خورد، آپدیت انجام نشه", "About": "درباره", "About Qt6": "درباره Qt6", "About WingetUI": "درباره UniGetUI", "About WingetUI version {0}": "درباره نسخه UniGetUI {0}", "About the dev": "درباره توسعه دهندگان", "Accept": "تا<PERSON><PERSON>د", "Action when double-clicking packages, hide successful installations": "رفتار هنگام دوبار کلیک کردن روی بسته ها، پنهان کردن نصب های موفق", "Add": "اضافه کردن", "Add a source to {0}": "اضافه کردن یک منبع به {0}", "Add a timestamp to the backup file names": " یک مهره زمانی به اسم فایل پشتیبان گیری اضافه شود", "Add a timestamp to the backup files": " یک مهره زمانی به فایل پشتیبان گیری اضافه شود", "Add packages or open an existing bundle": "اضافه کردن بسته ها یا باز کردن مجموعه موجود", "Add packages or open an existing package bundle": "اضافه کردن بسته ها یا باز کردن بسته مجموعه موجود", "Add packages to bundle": "افزودن بسته ها به مجموعه", "Add packages to start": "اضافه کردن بسته ها برای شروع", "Add selection to bundle": "اضافه کردن انتخاب شده ها به مجموعه", "Add source": "افزودن منبع", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "به‌روزرسانی‌هایی که با خطای «به‌روزرسانی سازگاری پیدا نشد» شکست می‌خورند را به فهرست به‌روزرسانی‌های نادیده‌گرفته‌شده اضافه کن", "Adding source {source}": "در حال اضافه کردن منبع {source}", "Adding source {source} to {manager}": "درحال اضافه منبع {source} به {manager}.", "Addition succeeded": "اضافه کردن با موفقیت انجام شد", "Administrator privileges": "دسترسی امتیازات ادمین", "Administrator privileges preferences": "ترجیحات امتیازات ادمین", "Administrator rights": "حقو<PERSON> ادمین", "Administrator rights and other dangerous settings": "دسترسی های مدیر سیستم و دیگر تنظیمات خطرناک", "Advanced options": "تنظیمات پیشرفته", "All files": "همه فایل ها", "All versions": "همه‌ی نسخه ها", "Allow changing the paths for package manager executables": "اجازه تغییر مسیر ها برای فایل های اجرایی پکیج منیجر", "Allow custom command-line arguments": "اجازه ورودی های سفارشی خط فرمان داده شود", "Allow importing custom command-line arguments when importing packages from a bundle": "اجازه ورودی های سفارشی خط فرمان هنگام ورود بسته ها از مجموعه داده شود", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "اجازه ورود پیش نیاز سفارشی و فرمان های مرحله نصب هنگام ورود بسته ها از مجموعه داده شود", "Allow package operations to be performed in parallel": "اجازه دهید عملیات بسته به صورت موازی انجام شود", "Allow parallel installs (NOT RECOMMENDED)": "اجازه نصب موازی (توصیه نمی شود)", "Allow pre-release versions": "اجازه نسخه های پیش از انتشار داده شود", "Allow {pm} operations to be performed in parallel": "اجازه دهید عملیات {pm} به صورت موازی انجام شود", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "یا به‌صورت جایگزین، می‌توانید {0} را با اجرای دستور زیر در محیط Windows PowerShell نصب کنید:", "Always elevate {pm} installations by default": "همیشه اپلیکیشن های نصب شده را به صورت پیشفرض بالا ببر {pm}", "Always run {pm} operations with administrator rights": "همیشه عملیات های {pm} را با اجازه ی ادمین باز کن", "An error occurred": "یک خطا رخ داده است", "An error occurred when adding the source: ": "یک خطا درحین اضافه کردن منبع رخ داد:", "An error occurred when attempting to show the package with Id {0}": "هنگام تلاش برای نمایش بسته با شناسهٔ {0} خطایی رخ داد", "An error occurred when checking for updates: ": "یک خطا درحین چک کردن برای به روزرسانی رخ داد:", "An error occurred while loading a backup: ": "یک خطا هنگام اجرای فایل پشتیبان رخ داد:", "An error occurred while logging in: ": "یک خطا هنگام ورود کاربر رخ داد:", "An error occurred while processing this package": "یک خطا درحال پردازش این بسته رخ داد:", "An error occurred:": "خطایی رخ داد:", "An interal error occurred. Please view the log for further details.": "یک خطای داخلی رخ داد. لطفا برای اطلاعات بیشتر لاگ را نگاه کنید.", "An unexpected error occurred:": "یک خطای غیر منتظره رخ داد:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "هنگام تلاش برای تعمیر WinGet، مشکلی غیرمنتظره رخ داد. لطفاً بعداً دوباره امتحان کنید.", "An update was found!": "یک به روز رسانی پیدا شد!", "Android Subsystem": "محیط اندروید تحت ویندوز", "Another source": "منبع دیگر", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "میان‌برها<PERSON> جدیدی که هنگام نصب یا به‌روزرسانی ایجاد می‌شوند، بدون نمایش پیغام تأیید، به‌صورت خودکار حذف خواهند شد", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "هر میان‌بری که خارج از UniGetUI ایجاد یا ویرایش شده باشد، نادیده گرفته می‌شود. می‌توانید آن‌ها را از طریق دکمهٔ {0} اضافه کنید", "Any unsaved changes will be lost": "تغییرات ذخیره‌نشده از بین خواهند رفت", "App Name": "نام برنامه", "Appearance": "ظاهر", "Application theme, startup page, package icons, clear successful installs automatically": " تم برنامه، صفحهٔ شروع، آیکون‌های بسته‌ها، پاک‌سازی خودکار نصب‌های موفق", "Application theme:": "تم برنامه:", "Apply": "اعمال", "Architecture to install:": "معماری برای نصب:", "Are these screenshots wron or blurry?": "آیا این اسکرین شات ها اشتباه یا تار هستند؟", "Are you really sure you want to enable this feature?": "آیا مطمئن هستید که این قابلیت فعال شود؟", "Are you sure you want to create a new package bundle? ": "از ایجاد مجموعهٔ جدید بسته‌ها مطمئن هستید؟", "Are you sure you want to delete all shortcuts?": "آیا مطمئن هستید که می‌خواهید همهٔ میان‌برها را حذف کنید؟", "Are you sure?": "ایا مطمئن هستید؟", "Ascendant": "بالا رونده", "Ask for administrator privileges once for each batch of operations": "هر بار که یک عملیات batch اتفاق می افتد برای دسترسی ادمین درخواست کن", "Ask for administrator rights when required": "وقتی که نیاز است برای دسترسی ادمین بپرس", "Ask once or always for administrator rights, elevate installations by default": "یکبار یا همیشه برای دسترسی ادمین بپرس، اپلیکیشن های نصب شده را به صورت پیش فرض بالا ببر", "Ask only once for administrator privileges": "فقط یکبار دسترسی مدیر سیستم درخواست شود", "Ask only once for administrator privileges (not recommended)": "فقط یک بار برای دسترسی ادمین بپرس (توصیه نمی شود)", "Ask to delete desktop shortcuts created during an install or upgrade.": "درخواست تأیید برای حذف میان‌برهای دسکتاپ هنگام نصب یا به‌روزرسانی", "Attention required": "توجه! توجه!", "Authenticate to the proxy with an user and a password": "احراز هویت پروکسی با نام کاربری و رمز عبور", "Author": "سازنده", "Automatic desktop shortcut remover": "حذف‌کنندهٔ خودکار میان‌برهای دسکتاپ", "Automatically save a list of all your installed packages to easily restore them.": "به صورت خودکار یک لیست از همه ی بسته های نصب شده ذخیره کن تا  بتوانید به آسانی آن ها را برگردانید.", "Automatically save a list of your installed packages on your computer.": "به صورت خودکار یک لیست از همه ی بسته هایی که روی رایانه ی تان نصب شده ذخیره کنید.", "Autostart WingetUI in the notifications area": "UniGetUI را به صورت خودکار در قسمت اعلان ها راه اندازی کنید\n", "Available Updates": "به روزرسانی ها موجود است", "Available updates: {0}": "به روز رسانی های موجود: {0}", "Available updates: {0}, not finished yet...": "به روز رسانی های موجود: {0}، هنوز تمام نشده...", "Backing up packages to GitHub Gist...": "درحال پشتیبان گیری بسته ها به Github Gist...", "Backup": "پشتیبان گیری", "Backup Failed": "پشتیبان گیری شکست خورد", "Backup Successful": "پشتیبان گیری با موفقیت انجام شد", "Backup and Restore": "پشتیبان گیری و بازگردانی", "Backup installed packages": "پشتیبان گیری از بسته های نصب شده", "Backup location": "مکان ذخیره سازی بک‌آپ", "Become a contributor": "یک شریک شوید", "Become a translator": "مترجم شوید", "Begin the process to select a cloud backup and review which packages to restore": "شروع فرآیند انتخاب پشتیبان ابری و بازبینی بسته ها برای بازگردانی", "Beta features and other options that shouldn't be touched": "امکانات بتا و گزینه‌های دیگر که نباید تغییر داده شوند", "Both": "هر دو", "Bundle security report": "گزارش امنیت مجموعه", "But here are other things you can do to learn about WingetUI even more:": "اما در اینجا چیزهای دیگری است که می توانید برای یادگیری بیشتر در مورد UniGetUI انجام دهید:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "با خاموش کردن مدیریت بسته ها، شما دیگر نمی توانید بسته ها را نگاه کنید یا به روزرسانی کنید. ", "Cache administrator rights and elevate installers by default": "اجازه ی ادمین را در حافظه پنهان ذخیره کن و نصب کننده ها را به صورت پیشفرض بالا ببر.", "Cache administrator rights, but elevate installers only when required": "اجازه ی ادمین را در حافظه پنهان ذخیره کن، اما نصب کننده ها را فقط وقتی نیاز است بالا ببر. ", "Cache was reset successfully!": "حافظه پنهان با موفقیت ریست شد", "Can't {0} {1}": "نمی‌توان {1} را {0} کرد", "Cancel": "لغو", "Cancel all operations": "لغو تمامی عملیات ها", "Change backup output directory": "محل جاگذاری پشتیبان گیری را تغییر بده", "Change default options": "تغییر تنظیمات پیشفرض", "Change how UniGetUI checks and installs available updates for your packages": "نحوهٔ بررسی و نصب به‌روزرسانی‌های موجود برای بسته‌های شما توسط UniGetUI را تغییر دهید", "Change how UniGetUI handles install, update and uninstall operations.": "تغییر نحوهٔ عملکرد UniGetUI در نصب، به‌روزرسانی و حذف", "Change how UniGetUI installs packages, and checks and installs available updates": "تغییر نحوهٔ نصب بسته‌ها و به‌روزرسانی‌ها توسط UniGetUI", "Change how operations request administrator rights": "تغییر نحوهٔ درخواست مجوز ادمین برای عملیات ها", "Change install location": "تغ<PERSON><PERSON>ر محل نصب", "Change this": "این رو تغییر بده", "Change this and unlock": "این رو تغییر بده و فعال کن", "Check for package updates periodically": "به‌روزرسانی‌های بسته را به صورت دوره‌ای بررسی کن", "Check for updates": "بررسی برای به‌روز رسانی ها", "Check for updates every:": "یافتن به روز رسانی در هر:", "Check for updates periodically": "به‌روزرسانی‌ها را به صورت دوره‌ای بررسی کن", "Check for updates regularly, and ask me what to do when updates are found.": "مرتباً به‌روزرسانی‌ها را بررسی کن و از من بپرس وقتی به‌روزرسانی‌ها یافت می‌شوند، چه کار کنم.", "Check for updates regularly, and automatically install available ones.": "هر چند وقت یک بار برای به روز رسانی ها چک کن، و به روز رسانی های موجود را به صورت خودکار نصب کن.", "Check out my {0} and my {1}!": "{0} و {1} من را بررسی کنید!", "Check out some WingetUI overviews": "چند تا نظرات از UniGetUI را نگاه کنید", "Checking for other running instances...": "یافتن سایر فرآیندهای فعال...", "Checking for updates...": "در حال بررسی به‌روزرسانی‌ها...", "Checking found instace(s)...": "فرآیند (های) یافت شده در حال بررسی است...\n", "Choose how many operations shouls be performed in parallel": "انتخاب تعداد عملیات های هم‌زمان", "Clear cache": "پاک کردن حافظه پنهان", "Clear finished operations": "پاکسازی عملیات های پایان یافته", "Clear selection": "پاک کردن انتخاب شده ها", "Clear successful operations": "پاک کردن عملیات های اتمام شده", "Clear successful operations from the operation list after a 5 second delay": "عملیات های موفق پس از ۵ ثانیه تاخیر از فهرست عملیات پاک می‌شوند", "Clear the local icon cache": "پاک کردن فضای اشغال شده برای آیکن", "Clearing Scoop cache - WingetUI": "پاک کردن حافظه پنهان UniGetUI - Scoop", "Clearing Scoop cache...": "در حال حذف کردن داده های موقت Scoop...", "Click here for more details": "برای اطلاعات بیشتر کلیک کنید", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "برای شروع نصب، روی «نصب» کلیک کنید. اگر نصب را رد کنید، ممکن است UniGetUI به درستی کار نکند.", "Close": "بستن", "Close UniGetUI to the system tray": "بستن UniGetUI و انتقال به نوار سیستم", "Close WingetUI to the notification area": "UniGetUI را در ناحیه اعلان خاموش کن", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "پشتیبان گیری ابری از یک Github Gist خصوصی استفاده میکنه تا لیست بسته های نصب شده رو ذخیره کنه", "Cloud package backup": "پشتیبان گیری بسته ابری", "Command-line Output": "خروجی خط فرمان", "Command-line to run:": "فرمان برای اجرا:", "Compare query against": "هر لیست را مقایسه کن در برابر", "Compatible with authentication": "سازگار با احراز هویت", "Compatible with proxy": "سازگار با پروکسی", "Component Information": "اطلاعات جزء\n", "Concurrency and execution": "همزمانی و اجرا", "Connect the internet using a custom proxy": "اتصال اینترنت با استفاده از پروکسی سفارشی", "Continue": "ادامه بده", "Contribute to the icon and screenshot repository": "به مخزن آیکون‌ها و اسکرین‌شات‌ها کمک کنید", "Contributors": "مشارکت کنندگان", "Copy": "کپی", "Copy to clipboard": "کپی به کلیپ بورد", "Could not add source": "افزودن منبع صورت نگرفت", "Could not add source {source} to {manager}": "نتوانستیم منبع {source} را به {manager} اضافه کنیم", "Could not back up packages to GitHub Gist: ": "پشتیبان گیری بسته ها به Github Gist انجام نشد:", "Could not create bundle": "امکان ایجاد بسته وجود ندارد", "Could not load announcements - ": "نتوانستیم اطلاعیه ها را بارگذاری کنیم - ", "Could not load announcements - HTTP status code is $CODE": "نتوانستیم اطلاعیه ها را بارگذاری کنیم - کد وضعیت HTTPS $CODE هست", "Could not remove source": "حذف منبع صورت نگرفت", "Could not remove source {source} from {manager}": "نتوانستیم منبع {source} را از {manager} حذف کنیم", "Could not remove {source} from {manager}": "ام<PERSON>ان حذف {source} از {manager} وجود ندارد", "Credentials": "اطلاعات ورود", "Current Version": "نسخه فعلی", "Current status: Not logged in": "وضعیت فعلی: کاربر وارد نشده", "Current user": "کاربر فعلی", "Custom arguments:": "دستور های سفارشی:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "آرگومان های سفارشی خط فرمان میتوانند روش تشخیص برنامه های نصب شده، آپدیت شده یا حذف شده را به گونه ای تغییر دهند که UniGetUI نتواند کنترل کند. استفاده از خط فرمان های سفارشی میتواند بسته ها را خراب کند. با ملاحظه و دقت ادامه دهید.", "Custom command-line arguments:": "دستور های سفارشی خط فرمان:", "Custom install arguments:": "آرگومان های سفارشی نصب:", "Custom uninstall arguments:": "آرگومان های سفارشی حذف نصب:", "Custom update arguments:": "آرگومان های سفارشی بروزرسانی:", "Customize WingetUI - for hackers and advanced users only": "شخصی‌سازی UniGetUI — فقط برای هکرها و کاربران حرفه‌ای", "DEBUG BUILD": "نسخه دیباگ", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "توجه: ما مسئول بسته‌های دانلود شده نیستیم. لطفاً مطمئن شوید که فقط نرم‌افزارهای معتبر را نصب می‌کنید.", "Dark": "تیره", "Decline": "ر<PERSON> کر<PERSON>ن", "Default": "پیش‌<PERSON><PERSON>ض ", "Default installation options for {0} packages": "تنظیمات نصب پیشفرض برای {0} بسته(ها)", "Default preferences - suitable for regular users": "تنظیمات پیش فرض - مناسب برای کاربران جدید", "Default vcpkg triplet": "سه‌تایی پیش‌فرض vcpkg", "Delete?": "حذف؟", "Dependencies:": "وابستگی ها:", "Descendant": "پایین رونده", "Description:": "توضیحات:\n", "Desktop shortcut created": "میانبر به دسکتاپ اضافه شد", "Details of the report:": "جزئیات گزارش:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "ساختن برنامه سخت است، و این اپلیکیشن رایگان است. ولی اگر از اپ خوشت آمد، همیشه می‌توانی <b> برای من یک قهوه بخری </b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "هنگام دوبار کلیک کردن روی یک مورد در برگه \"{discoveryTab}\" مستقیماً نصب کنید (به جای نمایش اطلاعات بسته)", "Disable new share API (port 7058)": "غیر فعال کردن API اشتراک گذاری جدید (پورت 7058)", "Disable the 1-minute timeout for package-related operations": "غیرفعا<PERSON>‌کردن زمان‌توقف ۱ دقیقه‌ای برای عملیات مرتبط با بسته‌ها", "Disclaimer": "توجه", "Discover Packages": "کاوش بسته ها", "Discover packages": "کاوش بسته ها", "Distinguish between\nuppercase and lowercase": "تمایز بین حروف کوچک و بزرگ", "Distinguish between uppercase and lowercase": "تمایز بین حروف کوچک و بزرگ", "Do NOT check for updates": "بروزرسانی را بررسی نکن", "Do an interactive install for the selected packages": "یک نصب ارتباطی برای بسته های انتخاب شده انجام بده", "Do an interactive uninstall for the selected packages": "یک حذف نصب ارتباطی برای بسته های انتخاب شده انجام بده", "Do an interactive update for the selected packages": "یک بروزرسانی ارتباطی برای بسته های انتخاب شده انجام بده", "Do not automatically install updates when the battery saver is on": "هنگامی که صرفه‌جویی باتری روشن است، به‌روزرسانی‌ها را به طور خودکار نصب نکن", "Do not automatically install updates when the network connection is metered": "هنگامی که اتصال شبکه محدود است، به‌روزرسانی‌ها را به طور خودکار نصب نکن", "Do not download new app translations from GitHub automatically": "ترجمه های جدید برنامه را به صورت خودکار از GitHub بارگیری نکن", "Do not ignore updates for this package anymore": "دیگر به‌روزرسانی‌های این بسته را نادیده نگیرید", "Do not remove successful operations from the list automatically": "عملیات موفقیت آمیز را به طور خودکار از لیست حذف نکن", "Do not show this dialog again for {0}": "این دیالوگ را دوباره برای {0} نشان نده", "Do not update package indexes on launch": "فهرست بسته ها را هنگام اغاز برنامه بروزرسانی نکن", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "آیا قبول دارید که UniGetUI آمار استفاده ناشناس جمع‌آوری و ارسال کند، با هدف تنها درک و بهبود تجربه کاربری؟", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "آیا UniGetUI برایتان مفید است؟ اگر امکانش را دارید، می‌توانید از کار من حمایت کنید تا بتوانم UniGetUI را به بهترین رابط مدیریت بسته تبدیل کنم.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "یا UniGetUI برایتان مفید است؟ دوست دارید از توسعه‌دهنده حمایت کنید؟ اگر اینطور است، می‌توانید {0}؛ این خیلی کمک می‌کند!", "Do you really want to reset this list? This action cannot be reverted.": "آیا واقعاً می‌خواهید این فهرست را بازنشانی کنید؟ این عمل قابل بازگشت نیست.", "Do you really want to uninstall the following {0} packages?": "آیا واقعا می خواهید تا این {0} بسته ها را حذف کنید؟", "Do you really want to uninstall {0} packages?": "آیا واقعاً میخواهید بسته های {0} را حذف کنید؟", "Do you really want to uninstall {0}?": "آیا واقعاً می خواهید {0} را حذف کنید؟", "Do you want to restart your computer now?": "آیا اکنون میخواهید رایانه خود را مجدداً راه اندازی کنید؟", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "آیا می خواهید UniGetUI را به زبان خود ترجمه کنید؟  برای آشنایی با نحوه شرکت کردن به <a style=\"color:{0}\" href=\"{1}\"a>اینجا مراجعه کنید!</a>\n", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "تمایلی به اهدا ندارید؟ نگران نباشید، همیشه می‌توانید UniGetUI را با دوستانتان به اشتراک بگذارید. درباره UniGetUI اطلاع‌رسانی کنید.", "Donate": "اهدا کنید", "Done!": "انجام شد!", "Download failed": "دان<PERSON>ود ناموفق", "Download installer": "بارگیری نصب کننده", "Download operations are not affected by this setting": "این مورد روی عملیات دانلود تاثیر ندارد", "Download selected installers": "دان<PERSON>ود نصب کننده های انتخاب شده", "Download succeeded": "بارگیری با موفقیت انجام شد", "Download updated language files from GitHub automatically": "د<PERSON><PERSON><PERSON><PERSON> خودکار فایل‌های زبان به‌روزشده از GitHub", "Downloading": "در حال بارگیری", "Downloading backup...": "درحال دانلود پشتیبان...", "Downloading installer for {package}": "دانلود نصب‌کننده برای {package}", "Downloading package metadata...": "بارگیری metadata بسته...", "Enable Scoop cleanup on launch": "فعال کردن پاکسازی Scoop  هنگام راه اندازی ", "Enable WingetUI notifications": "فعال سازی اعلان های UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "فعال کردن عیب‌یاب بهبود یافته WinGet [آزمایشی]", "Enable and disable package managers, change default install options, etc.": "فعالسازی و غیرفعالسازی مدیر های بسته، تغییر تنظیمات نصب پیشفرض و غیره.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "فعال کردن بهینه‌سازی‌های استفاده از CPU در پس‌زمینه (Pull Request #3278 را ببینید)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "فعال کردن api پس زمینه (WingetUI Widgets and Sharing، پورت 7058)", "Enable it to install packages from {pm}.": "آن را برای نصب بسته‌ها از {pm} فعال کنید.", "Enable the automatic WinGet troubleshooter": "فعال کردن عیب‌یاب خودکار WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "فعال کردن ارتقادهنده UAC جدید با برند UniGetUI", "Enable the new process input handler (StdIn automated closer)": "فعالسازی مدیریت جدید ورودی فرایند (بستن خودکار StdIn)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "تنظیمات زیر را تنها و فقط زمانی که میدانید چیکار میکنند، و چه خطر هایی را زمینه سازی میکنند، فعال کنید.", "Enable {pm}": "فعال کردن {pm}", "Enter proxy URL here": "URL پروکسی را اینجا وارد کنید", "Entries that show in RED will be IMPORTED.": "ورودی های قرمز رنگ وارد خواهند شد.", "Entries that show in YELLOW will be IGNORED.": "ورودی های زرد رنگ پشت سر گذاشته خواهند شد.", "Error": "خطا", "Everything is up to date": "همه چیز به روز است", "Exact match": "تطابق کامل", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "میان‌برهای موجود روی دسکتاپ شما اسکن می‌شوند و باید انتخاب کنید کدام را نگه دارید و کدام را حذف کنید", "Expand version": "تکامل نسخه", "Experimental settings and developer options": "تنظیمات ازمایشی و گزینه های توسعه دهنده", "Export": "استخراج", "Export log as a file": "استخراج لاگ به عنوان فایل", "Export packages": "بسته ها را استخراج کنید", "Export selected packages to a file": "استخراج بسته های انتخابی به یک فایل", "Export settings to a local file": "استخراج تنظیمات از یک فایل محلی", "Export to a file": "استخراج به یک فایل", "Failed": "شکست خورد", "Fetching available backups...": "درحال بررسی پشتیبان های در درسترس...", "Fetching latest announcements, please wait...": "در حال دریافت آخرین اطلاعیه‌ها، لطفاً صبر کنید...", "Filters": "فیلتر ها", "Finish": "پایان", "Follow system color scheme": "پیروی از طرح رنگ سیستم", "Follow the default options when installing, upgrading or uninstalling this package": "هنگام نصب، بروزرسانی، یا حذف نصب این بسته از تنظیمات پیشفرض پیروی کن", "For security reasons, changing the executable file is disabled by default": "برای دلایل امنیتی، تغییر فایل اجرایی بصورت پیشفرض غیرفعال شده است", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "برای دلایل امنیتی، آرگومان های سفارشی خط فرمان بصورت پیشفرض غیرفعال شده اند. برای تغییر این مورد به تنظیمات امنیتی UniGetUI بروید.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "برای دلایل امنیتی، اسکریپت های پیش عملیات و هنگام عملیات بصورت پیشفرض غیرفعال شده اند. برای تغییر این مورد به تنظیمات امنیتی UniGetUI بروید.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "نسخه winget کامپایل شده Force ARM (فقط برای سیستم های ARM64)", "Formerly known as WingetUI": "قبلا با نام WingetUI شناخته می شد", "Found": "پیدا شد", "Found packages: ": "بسته های پیدا شده:", "Found packages: {0}": "بسته های پیدا شده: {0}", "Found packages: {0}, not finished yet...": "بسته های پیدا شده: {0}، هنوز تمام نشده..", "General preferences": "تنظیمات دلخواه کلی", "GitHub profile": "پروف<PERSON><PERSON><PERSON>", "Global": "کلی یا جهانی", "Go to UniGetUI security settings": "برو به تنظیمات امنیتی UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "مخزن عالی از ابزارهای ناشناخته اما کاربردی و بسته‌های جالب دیگر.<br> شامل: <b> ابزارها، برنامه‌های خط فرمان، نرم‌افزارهای عمومی (نیازمند extras bucket) </b>", "Great! You are on the latest version.": "عالیه! برنامه شما آخرین نسخه هست.", "Grid": "شبکه", "Help": "راهنما", "Help and documentation": "راهنما و مستندات", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "اینجا می‌توانید رفتار UniGetUI را در مورد میانبرهای زیر تغییر دهید. علامت زدن یک میانبر باعث می‌شود UniGetUI آن را حذف کند اگر در ارتقای آینده ایجاد شود. برداشتن علامت آن را دست نخورده نگه می‌دارد", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "سلام، من <PERSON><PERSON> هستم، <i>توسعه دهنده ی</i> UniGetUI. کل UniGetUI در اوقات فراغتم ساخته شده!  ", "Hide details": "مخ<PERSON>ی کردن جزئیات", "Homepage": "صف<PERSON><PERSON> اصلی", "Hooray! No updates were found.": "هورا! هیچ به روز رسانی پیدا نشد!", "How should installations that require administrator privileges be treated?": "چگونه باید با نصب هایی که به امتیازات مدیر سیستم نیاز دارند برخورد کرد؟", "How to add packages to a bundle": "نحوه افزودن بسته‌ها به مجموعه", "I understand": "فهمیدم", "Icons": "آیکون‌ها", "Id": "شناسه", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "اگر شما پشتیبان گیری ابری را فعال کرده باشید، بصورت Github Gist روی این اکانت ذخیره خواهد شد", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "وقتی بسته ها رو از مجموعه وارد میکنی بیخیال دستورات پیش از نصب و هنگام نصب شو", "Ignore future updates for this package": "نادیده گرفتن بروزرسانی های آینده این بسته", "Ignore packages from {pm} when showing a notification about updates": "نادیده گرفتن بسته‌ها از {pm} هنگام نمایش اعلان درباره به‌روزرسانی‌ها", "Ignore selected packages": "نادیده گرفتن بسته های انتخاب شده", "Ignore special characters": "نادیده گرفتن کاراکتر های خاص", "Ignore updates for the selected packages": "بروزرسانی هارا برای بسته های انتخاب شده نادیده بگیر", "Ignore updates for this package": "نادیده گرفتن بروزرسانی های این بسته", "Ignored updates": "بروزرسانی های نادیده گرفته شده", "Ignored version": "نسخه های نادیده گرفته شده", "Import": "وارد کردن", "Import packages": "بسته های وارد شد", "Import packages from a file": "واردکردن بسته از یک فایل ", "Import settings from a local file": "وارد کردن تنظیمات از یک فایل محلی", "In order to add packages to a bundle, you will need to: ": "برای افزودن بسته‌ها به مجموعه، باید: ", "Initializing WingetUI...": "در حال راه اندازی UniGetUI...", "Install": "نصب", "Install Scoop": "نص<PERSON>", "Install and more": "نصب و بیشتر", "Install and update preferences": "تنظیمات نصب و به‌روزرسانی", "Install as administrator": "به عنوان ادمین نصب کنید", "Install available updates automatically": "به‌روزرسانی‌های موجود را به‌طور خودکار نصب کن", "Install location can't be changed for {0} packages": "محل نصب نمیتواند برای {0} بسته(ها) تغییر کند", "Install location:": "مح<PERSON> نصب:", "Install options": "تنظیمات نصب", "Install packages from a file": "بسته ها را از یک فایل نصب کن", "Install prerelease versions of UniGetUI": "نصب نسخه‌های پیش‌انتشار UniGetUI", "Install selected packages": "نصب بسته های انتخاب شده", "Install selected packages with administrator privileges": "بسته های انتخاب شده با امتیازات مدیر را نصب کنید", "Install selection": "نصب انتخاب شده ها", "Install the latest prerelease version": "آخرین نسخه پیش انتشار را نصب کنید", "Install updates automatically": "بروزرسانی هارا به صورت خودکار نصب کن", "Install {0}": "نصب {0}", "Installation canceled by the user!": "نصب توسط کاربر لغو شد!", "Installation failed": "نصب انجام نشد", "Installation options": "گزینه های نصب", "Installation scope:": "مقیاس نصب:", "Installation succeeded": "نصب با موفقیت انجام شد", "Installed Packages": "بسته های نصب شده", "Installed Version": "نسخه نصب شده", "Installed packages": "بسته‌های نصب شده", "Installer SHA256": "SHA256 نصب کننده", "Installer SHA512": "SHA512 نصب کننده", "Installer Type": "نوع نصب کننده", "Installer URL": "لینک (URL) نصب کننده", "Installer not available": "نصب کننده در دسترس نیست", "Instance {0} responded, quitting...": "نمونه {0} پاسخ داد، ترک میکند...", "Instant search": "جستجوی فوری\n", "Integrity checks can be disabled from the Experimental Settings": "بررسی های یکپارچگی از طریق تنظیمات آزمایشی میتونن غیرفعال بشن", "Integrity checks skipped": "بررسی‌های یکپارچگی رد شد", "Integrity checks will not be performed during this operation": "بررسی‌های یکپارچگی در طول این عملیات انجام نخواهد شد", "Interactive installation": "نصب تعاملی", "Interactive operation": "عملیات تعاملی", "Interactive uninstall": "حد<PERSON> برنامه به صورت تعاملی", "Interactive update": "به روز رسانی تعاملی", "Internet connection settings": "تنظیمات اتصال اینترنت", "Is this package missing the icon?": "آیا این بسته بدون ایکون است؟", "Is your language missing or incomplete?": "آیا زبان شما گم شده است یا ناقص است؟", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "تضمینی وجود ندارد که اطلاعات ورود ارائه‌شده به‌صورت ایمن ذخیره شوند، پس بهتر است از اطلاعات حساب بانکی خود استفاده نکنید.", "It is recommended to restart UniGetUI after WinGet has been repaired": "توصیه می‌شود پس از تعمیر WinGet، UniGetUI را مجدداً راه‌اندازی کنید.", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "نصب مجدد UniGetUI عمیقا پیشنهاد میشه تا وضعیت آدرس دهی بشه.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "به نظر می‌رسد WinGet به درستی کار نمی‌کند. می‌خواهید تلاش کنید WinGet را تعمیر کنید؟", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "به نظر می‌رسد که UniGetUI را با دسترسی مدیر (Administrator) اجرا کرده‌اید، که توصیه نمی‌شود.\nهنوز می‌توانید برنامه را استفاده کنید، اما شدیداً پیشنهاد می‌کنیم UniGetUI را با دسترسی مدیر اجرا نکنید.\nبرای دیدن دلیل، روی {showDetails} کلیک کنید.", "Language": "زبان", "Language, theme and other miscellaneous preferences": "زبان، تم و تنظیمات گوناگون دیگر ", "Last updated:": "اخرین به روز رسانی:", "Latest": "آخرین", "Latest Version": "آخرین نسخه", "Latest Version:": "آخرین نسخه:", "Latest details...": "اخرین جزئیات...", "Launching subprocess...": "راه اندازی فرآیند فرعی...", "Leave empty for default": "پیش فرض خالی بگذارید", "License": "مجوز", "Licenses": "مجوزها", "Light": "روشن", "List": "لیست", "Live command-line output": "خرو<PERSON>ی خط-فرمان بلادرنگ", "Live output": "خروجی بلادرنگ", "Loading UI components...": "بارگذاری اجزای فضای کاربری (UI)", "Loading WingetUI...": "در حال بارگذاری UniGetUI...", "Loading packages": "در حال بارگیری بسته ها", "Loading packages, please wait...": "در حال بارگیری بسته ها، لطفا صبر کنید...", "Loading...": "بارگذاری...", "Local": "<PERSON><PERSON><PERSON><PERSON>", "Local PC": "کامپیوتر محلی", "Local backup advanced options": "تنظیمات پیشرفته پشتیبان محلی", "Local machine": "م<PERSON><PERSON><PERSON><PERSON> محلی", "Local package backup": "پشتیبان بسته محلی", "Locating {pm}...": "مکان یابی {pm}...", "Log in": "ورود کاربر", "Log in failed: ": "ورود کاربر شکست خورد:", "Log in to enable cloud backup": "برای فعالسازی پشتیبان ابری وارد شوید", "Log in with GitHub": "با <PERSON>ithub وارد شوید", "Log in with GitHub to enable cloud package backup.": "با Github وارد شوید تا پشتیبان ابری فعال شود.", "Log level:": "سطح گزارش:", "Log out": "<PERSON>روج کاربر", "Log out failed: ": "خروج کاربر شکست خورد:", "Log out from GitHub": "خرو<PERSON> ا<PERSON>", "Looking for packages...": "در حال جستجو برای بسته ها...", "Machine | Global": "ماشین | جهانی", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "آرگومان های نادرست خط فرمان میتونن بسته ها رو خراب کنند، یا حتی به یک بدافزار دسترسی اجرا بدهند. بنابراین، ورود آرگومان های شخصی سازی شده خط فرمان بصورت پیشفرض غیرفعال شده است", "Manage": "مدی<PERSON><PERSON>ت", "Manage UniGetUI settings": "مدیریت تنظیمات UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "مدیریت رفتار شروع خودکار UniGetUI از طریق برنامه Settings", "Manage ignored packages": "مدیریت بسته های نادیده گرفته شده", "Manage ignored updates": "مدیریت بروزرسانی های نادیده گرفته شده", "Manage shortcuts": "مدیریت میانبرها", "Manage telemetry settings": "مدیریت تنظیمات تله‌متری", "Manage {0} sources": "منابع {0} را مدیریت کنید", "Manifest": "مشخصه", "Manifests": "مشخصات", "Manual scan": "اسکن دستی", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "مدیر بسته رسمی مایکروسافت. پر از بسته های شناخته شده و تأیید شده<br>شامل: <b>نرم افزار عمومی، برنامه های فروشگاه مایکروسافت</b>", "Missing dependency": "عدم وجود نیازمندی", "More": "بیشتر", "More details": "جزئیات بیشتر", "More details about the shared data and how it will be processed": "جزئیات بیشتر درباره داده‌های اشتراکی و نحوه پردازش آن‌ها", "More info": "اطلاعات بیشتر", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "توجه: این عیب‌یاب را می‌توان از تنظیمات UniGetUI، در بخش WinGet غیرفعال کرد", "Name": "نام", "New": "ج<PERSON><PERSON><PERSON>", "New Version": "نسخه جدید", "New bundle": "بان<PERSON><PERSON> جدید", "New version": "نسخه جدید", "Nice! Backups will be uploaded to a private gist on your account": "خوبه! پشتیبان ها به یک gist خصوصی روی حساب شما آپلود خواهند شد", "No": "<PERSON><PERSON><PERSON>", "No applicable installer was found for the package {0}": "هیچ نصب‌کننده قابل اعمالی برای بسته {0} یافت نشد", "No dependencies specified": "وابستگی ای مشخص نشده", "No new shortcuts were found during the scan.": "هنگام اسکن، هیچ میان‌بر جدیدی پیدا نشد.", "No packages found": "هیچ بسته ای پیدا نشد", "No packages found matching the input criteria": "هیچ بسته ای مطابق با معیار های ورودی پیدا نشد ", "No packages have been added yet": "هنوز هیچ بسته ای اضافه نشده است", "No packages selected": "هیچ بسته ای انتخاب نشد", "No packages were found": "هیچ بسته ای پیدا نشد", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "هیچ اطلاعات شخصی جمع‌آوری یا ارسال نمی‌شود و داده‌های جمع‌آوری شده ناشناس‌سازی شده‌اند، بنابراین قابل ردیابی به شما نیستند.", "No results were found matching the input criteria": "هیچ نتیجه ای مطابق با معیارهای ورودی یافت نشد", "No sources found": "هیچ منبعی یافت نشد", "No sources were found": "هیچ منبعی پیدا نشد", "No updates are available": "هیچ به روز رسانی در دسترس نیست\n", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "مدیر بسته Node JS. پر از کتابخانه ها و سایر ابزارهای کاربردی که در مدار جهان جاوا اسکریپت می گردند<br>شامل: <b>کتابخانه های جاوا اسکریپت گره و سایر ابزارهای مرتبط</b>", "Not available": "در دسترس نیست", "Not finding the file you are looking for? Make sure it has been added to path.": "فایلی که دنبالشی رو پیدا نکردی؟ مطمئن شو به مسیر اضافه شده باشه.", "Not found": "پیدا نشد\n", "Not right now": "الان نه", "Notes:": "یادداشت ها:", "Notification preferences": "تنظیمات اعلان", "Notification tray options": "گزینه های نوار اعلانات", "Notification types": "انواع اعلان", "NuPkg (zipped manifest)": "NuPkg (مشخصات فشرده شده)", "OK": "OK", "Ok": "OK", "Open": "باز کن", "Open GitHub": "GitHub را باز کن", "Open UniGetUI": "باز کردن UniGetUI", "Open UniGetUI security settings": "باز کردن تنظیمات امنیتی UniGetUI", "Open WingetUI": "باز کردن UniGetUI", "Open backup location": "باز کردن مکان ذخیره سازی بک‌آپ", "Open existing bundle": "باز کردن باندل موجود", "Open install location": "باز کردن مکان نصب", "Open the welcome wizard": "باز کردن Wizard خوش آمدگویی", "Operation canceled by user": "عملیات توسط کاربر لغو شد", "Operation cancelled": "عملیات لغو شد", "Operation history": "تاریخچه عملیات", "Operation in progress": "عملیات در حال انجام", "Operation on queue (position {0})...": "عملیات در صف (موقعیت {0})...", "Operation profile:": "پروفایل فرآیند:", "Options saved": "گزینه ها ذخیره شد", "Order by:": "مرتب‌سازی بر اساس:", "Other": "دیگر", "Other settings": "تنظیمات دیگر", "Package": "بسته", "Package Bundles": "باندل های بسته", "Package ID": "شناسه (ID) بسته", "Package Manager": "مدیریت بسته", "Package Manager logs": "لاگ های مدیریت بسته", "Package Managers": "مدیران بسته", "Package Name": "نام بسته\n\n", "Package backup": "بک آپ بسته", "Package backup settings": "تنظیمات پشتیبان گیری بسته", "Package bundle": "باندل بسته", "Package details": "جزئیات بسته", "Package lists": "لیست‌های بسته", "Package management made easy": "مدیریت بسته آسان شد", "Package manager": "مدیر بسته", "Package manager preferences": "تنظیمات مدیریت بسته", "Package managers": "مدیران بسته", "Package not found": "بسته پیدا نشد", "Package operation preferences": "تنظیمات عملیات بسته", "Package update preferences": "تنظیمات به‌روزرسانی بسته", "Package {name} from {manager}": "بسته {name} ا<PERSON> {manager}", "Package's default": "پیشفرض بسته", "Packages": "بسته ها", "Packages found: {0}": "بسته‌های یافت شده: {0}", "Partially": "تا حدی", "Password": "<PERSON><PERSON><PERSON> عبور", "Paste a valid URL to the database": "یک URL معتبر در پایگاه داده بچسبانید", "Pause updates for": "توقف بروزرسانی ها برای", "Perform a backup now": "اکنون یک نسخه بک آپ تهیه کنید", "Perform a cloud backup now": "الان پشتیبان ابری بگیر", "Perform a local backup now": "الان پشتیبان محلی بگیر", "Perform integrity checks at startup": "بررسی سلامت و یکپارچگی هنگام شروع برنامه انجام شود", "Performing backup, please wait...": "در حال انجام بک آپ گیری، لطفاً صبر کنید...", "Periodically perform a backup of the installed packages": "به صورت دوره ای از بسته های نصب شده یک نسخه بک آپ تهیه کنید", "Periodically perform a cloud backup of the installed packages": "بصورت دوره ای از بسته های نصب شده پشتیبان ابری بگیر", "Periodically perform a local backup of the installed packages": "بصورت دوره ای از بسته های نصب شده پشتیبان محلی بگیر", "Please check the installation options for this package and try again": "لطفاً گزینه‌های نصب این بسته را بررسی کرده و دوباره تلاش کنید", "Please click on \"Continue\" to continue": "لطفا برای ادامه روی \"ادامه\" کلیک کنید", "Please enter at least 3 characters": "لط<PERSON>ا حداقل ۳ کاراکتر وارد کنید", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "لطفاً توجه داشته باشید که ممکن است بسته‌های خاصی قابل نصب نباشند، زیرا مدیران بسته‌ها در این دستگاه فعال هستند.", "Please note that not all package managers may fully support this feature": "لطفاً توجه داشته باشید که همه مدیران بسته ممکن است کاملاً از این ویژگی پشتیبانی نکنند", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "لطفاً توجه داشته باشید که بسته هایی از منابع خاص ممکن است قابل استخراج نباشند. آنها خاکستری شده اند و استخراج نمیشوند.", "Please run UniGetUI as a regular user and try again.": "لطفاً UniGetUI را به عنوان کاربر عادی اجرا کرده و دوباره تلاش کنید.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "لطفاً خروجی خط فرمان را ببینید یا برای اطلاعات بیشتر در مورد این مشکل به سابقه عملیات مراجعه کنید.", "Please select how you want to configure WingetUI": "لطفاً نحوه پیکربندی UniGetUI را انتخاب کنید", "Please try again later": "لطفاً بعداً دوباره تلاش کنید", "Please type at least two characters": "لط<PERSON>ا حداقل دو کاراکتر تایپ کنید", "Please wait": "لطفا صبر کنید", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "لطفاً صبر کنید تا {0} نصب شود. ممکن است پنجره سیاه (یا آبی) ظاهر شود. لطفاً صبر کنید تا بسته شود.", "Please wait...": "لطفا صبر کنید...\n", "Portable": "قابل حمل", "Portable mode": "<PERSON>ا<PERSON><PERSON> قابل حمل", "Post-install command:": "دستور هنگام نصب:", "Post-uninstall command:": "دستور هنگام حذف نصب:", "Post-update command:": "دستور هنگام بروزرسانی:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "مدیر بسته PowerShell. یافتن کتابخانه ها و اسکریپت ها برای گسترش قابلیت های PowerShell<br>شامل: <b>ماژول ها، اسکریپت ها، Cmdlet ها</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "دستورات قبل و هنگام نصب میتوانند کارهای ناپسندی با دستگاه شما انجام دهند، این میتونه خیلی خطرناک باشه که دستورات رو از باندل وارد کنی مگر اینکه منبع اون باندل بسته مورد اعتمادت باشه.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "دستورات قبل و موقع نصب هنگامی که بسته ای نصب، بروزرسانی یا حذف نصب شود، اجرا خواهند شد. مراقب باشید چرا که میتوانند همه چیز را خراب کنند مگر اینکه با دقت استفاده شوند", "Pre-install command:": "دستور قبل-نصب:", "Pre-uninstall command:": "دستور قبل-حذف-نصب:", "Pre-update command:": "دستور قبل-بروزرسانی:", "PreRelease": "پیش انتشار", "Preparing packages, please wait...": "در حال آماده سازی بسته ها، لطفا صبر کنید...", "Proceed at your own risk.": "با مسئولیت خود ادامه دهید.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "از هرنوع افزایش سطح دسترسی با افزاینده UniGetUI یا GSudo جلوگیری کن", "Proxy URL": "URL پروکسی", "Proxy compatibility table": "جدول سازگاری پروکسی", "Proxy settings": "تنظیمات پروکسی", "Proxy settings, etc.": "تنظیمات پروکسی و غیره.", "Publication date:": "تاریخ انتشار:", "Publisher": "منتشر کننده", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "مدیر کتابخانه پایتون پر از کتابخانه‌های پایتون و سایر ابزارهای مرتبط با پایتون<br>شامل: <b>کتابخانه‌های پایتون و ابزارهای مرتبط</b>", "Quit": "خروج", "Quit WingetUI": "خروج از UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "اعلانات کنترل سطح دسترسی کاربر(UAC) رو کاهش بده، افزایش سطح دسترسی نصب رو بصورت پیشفرض افزایش بده، قفل ویژگی های مشخص خطرناک و امثالهم رو باز کن.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "به لاگ های UniGetUI رجوع شود برای جزئیات بیشتر درمورد فایل(های) تحت تاثیر", "Reinstall": "نص<PERSON> مجدد", "Reinstall package": "بازنصب بسته", "Related settings": "تنظیمات مرتبط", "Release notes": "یادداشت های انتشار", "Release notes URL": "آدرس URL یادداشت های نسخه انتشار", "Release notes URL:": "آدرس URL یادداشت های نسخه انتشار:", "Release notes:": "یادداشت‌های نسخه انتشار:", "Reload": "بارگذاری مجدد", "Reload log": "بارگیری مجدد گزارش", "Removal failed": "حذف انجام نشد", "Removal succeeded": "حذف با موفقیت انجام شد", "Remove from list": "حذ<PERSON> از لیست", "Remove permanent data": "حذف اطلاعات دائمی", "Remove selection from bundle": "حذف انتخاب شده ها از باندل", "Remove successful installs/uninstalls/updates from the installation list": "پاک کردن نصب/حذف/بروزرسانی های موفق از لیست نصب ", "Removing source {source}": "حذ<PERSON> منبع {source}", "Removing source {source} from {manager}": "در حال حذف منبع {source} از {manager}", "Repair UniGetUI": "تعمیر UniGetUI", "Repair WinGet": "تعمیر WinGet", "Report an issue or submit a feature request": "گزارش یک مشکل یا ارسال درخواست ویژگی", "Repository": "مخزن", "Reset": "بازنشانی", "Reset Scoop's global app cache": "پاک‌کردن کش کلی برنامه Scoop", "Reset UniGetUI": "بازنشانی UniGetUI", "Reset WinGet": "بازنشانی WinGet", "Reset Winget sources (might help if no packages are listed)": "بازنشانی منابع Winget (ممکن است کمک کند اگر هیچ بسته‌ای نمایش داده نمی‌شود)", "Reset WingetUI": "بازنشانی UniGetUI", "Reset WingetUI and its preferences": "بازنشانی UniGetUI و تنظیمات آن", "Reset WingetUI icon and screenshot cache": "پاک‌کردن کش آیکون‌ها و اسکرین‌شات‌های UniGetUI", "Reset list": "بازنشانی لیست", "Resetting Winget sources - WingetUI": "در حال بازنشانی منابع UniGetUI - WinGet", "Restart": "راه‌ان<PERSON><PERSON><PERSON>ی مجدد", "Restart UniGetUI": "UniGetUI را مجددا راه اندازی کنید", "Restart WingetUI": "راه اندازی مجدد UniGetUI", "Restart WingetUI to fully apply changes": "برای اعمال کامل تغییرات، UniGetUI را مجدداً راه‌اندازی کنید.", "Restart later": "بعداً راه‌اند<PERSON><PERSON>ی مجدد کن", "Restart now": "همین حالا راه‌اندازی مجدد کن", "Restart required": "راه اندازی مجدد لازم است", "Restart your PC to finish installation": "کامپیوتر خود را مجددا راه اندازی کنید تا نصب تمام شود", "Restart your computer to finish the installation": "رایانه خود را راه اندازی مجدد کنید تا نصب تمام شود", "Restore a backup from the cloud": "بازگردانی یک پشتیبان از ابر", "Restrictions on package managers": "محدودیت ها روی مدیران بسته ها", "Restrictions on package operations": "محدودیت ها روی عملیات بسته ها", "Restrictions when importing package bundles": "محدودیت ها هنگام ورود باندل بسته ها", "Retry": "تلاش مجدد", "Retry as administrator": "تلاش مجدد به عنوان ادمین", "Retry failed operations": "تلاش مجدد عملیات ناموفق", "Retry interactively": "تلاش مجدد تعاملی", "Retry skipping integrity checks": "تلاش مجدد با رد کردن بررسی‌های یکپارچگی", "Retrying, please wait...": "در حال تلاش مجدد، لطفا صبر کنید...", "Return to top": "برگشت به بالای صفحه ", "Run": "اجرا", "Run as admin": "اجرا به عنوان ادمین", "Run cleanup and clear cache": "اجرای پاک‌سازی و پاک کردن کش", "Run last": "در آخر اجرا کن", "Run next": "بعد از این اجرا کن", "Run now": "الان اجرا کن", "Running the installer...": "در حال اجرای نصب کننده...", "Running the uninstaller...": "در حال اجرای برنامه حذف کننده...", "Running the updater...": "در حال اجرای به روز رسانی کننده...", "Save": "ذخیره", "Save File": "ذخیره فایل", "Save and close": "ذخیره و بستن", "Save as": "ذخیره در", "Save bundle as": "ذخیره باندل به عنوان", "Save now": "الان ذخیره کن", "Saving packages, please wait...": "در حال ذخیره بسته ها، لطفا صبر کنید...", "Scoop Installer - WingetUI": "نصب‌کننده UniGetUI - Scoop", "Scoop Uninstaller - WingetUI": "حذف کننده UniGetUI - Scoop", "Scoop package": "بست<PERSON>oop", "Search": "جستجو ", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "نرم افزار دسکتاپ را جستجو کنید، در صورت در دسترس بودن به روز رسانی ها به من هشدار دهید و کارهای مزخرف را انجام ندهید. من نمی خواهم WingetUI بیش از حد پیچیده شود، فقط یک <b>فروشگاه نرم افزار</b> ساده می خواهم", "Search for packages": "جستجوی بسته ها", "Search for packages to start": "جستجو بسته ها برای شروع", "Search mode": "حالت جستجو", "Search on available updates": "جستجو در مورد بروزرسانی های موجود", "Search on your software": "جستجو در برنامه", "Searching for installed packages...": "در حال جستجو برای بسته های نصب شده...", "Searching for packages...": "در حال جستجو برای بسته ها...", "Searching for updates...": "درحال جستجو برای بروزرسانی ها...", "Select": "انتخاب", "Select \"{item}\" to add your custom bucket": "برای افزودن سطل سفارشی خود، «{item}» را انتخاب کنید", "Select a folder": "انتخاب پوشه", "Select all": "انتخاب همه", "Select all packages": "همه بسته ها را انتخاب کنید", "Select backup": "انتخاب پشتیبان", "Select only <b>if you know what you are doing</b>.": "فقط <b>اگر می دانید چه کاری انجام می دهید</b> انتخاب کنید.", "Select package file": "فایل بسته را انتخاب کنید", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "پشتیبانی که میخوای باز کنی رو انتخاب کن. بعدش، میتونی بسته/برنامه ای که میخوای رو بازگردانی کنی", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "پروسه هایی که بهتره قبل از نصب، بروزرسانی یا حذف نصب این بسته، پایان داده بشن رو انتخاب کن", "Select the source you want to add:": "منبعی را که می خواهید اضافه کنید انتخاب کنید:", "Select upgradable packages by default": "بسته‌های قابل ارتقا را به طور پیش‌فرض انتخاب کنید", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "انتخاب کنید از کدام <b>مد<PERSON><PERSON> بسته</b> استفاده شود ({0})، نحوه نصب بسته‌ها را پیکربندی کنید، نحوه استفاده از حقوق سرپرست را مدیریت کنید و غیره.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "دست دادن ارسال شد.  در انتظار پاسخ شنونده فرآیند (Instance)... (%{0})", "Set a custom backup file name": "تنظیم یک نام فایل بک آپ شخصی سازی شده", "Set custom backup file name": "تنظیم نام فایل بک آپ شخصی سازی شده", "Settings": "تنظیمات", "Share": "اشتراک گذاری", "Share WingetUI": "اشتراک گذاری UniGetUI", "Share anonymous usage data": "به‌اشتراک‌گذاری داده‌های استفاده به‌صورت ناشناس", "Share this package": "اشتراک گذاری این بسته", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "اگه تنظیمات رو تغییر بدی، برای اینکه تغییرات موثر واقع بشن لازمه باندل رو دوباره باز کنی.", "Show UniGetUI on the system tray": "نمایش UniGetUI در نوار سیستم", "Show UniGetUI's version and build number on the titlebar.": "نمایش نسخه UniGetUI در نوار عنوان", "Show WingetUI": "نمایش UniGetUI", "Show a notification when an installation fails": "نمایش اعلان هنگام ناموفق بودن نصب", "Show a notification when an installation finishes successfully": "نمایش اعلان هنگام اتمام موفق نصب", "Show a notification when an operation fails": "نمایش اعلان هنگام شکست عملیات", "Show a notification when an operation finishes successfully": "نمایش اعلان هنگام اتمام موفق عملیات", "Show a notification when there are available updates": "در صورت وجود بروزرسانی یک اعلان نشان داده شود ", "Show a silent notification when an operation is running": "نمایش اعلان بی‌صدا هنگام اجرای عملیات", "Show details": "نمایش جزئیات", "Show in explorer": "نمایش در اکسپلورر", "Show info about the package on the Updates tab": "نمایش اطلاعات در مورد بسته در صفحه بروزرسانی ها", "Show missing translation strings": "نمایش متون ناموجود ترجمه", "Show notifications on different events": "نمایش اعلان‌ها در رویدادهای مختلف", "Show package details": "نمایش جزئیات بسته", "Show package icons on package lists": "نمایش آیکون‌های بسته در لیست‌های بسته", "Show similar packages": "نمایش بسته های مشابه", "Show the live output": "نمایش خروجی به صورت زنده و بلادرنگ", "Size": "حج<PERSON>", "Skip": "ر<PERSON> کر<PERSON>ن", "Skip hash check": "رد کردن بررسی Hash", "Skip hash checks": "رد کردن بررسی‌های Hash", "Skip integrity checks": "چشم پوشی از بررسی های یکپارچگی", "Skip minor updates for this package": "به‌روزرسانی‌های جزئی این بسته را نادیده بگیر", "Skip the hash check when installing the selected packages": "رد کردن بررسی Hash هنگام نصب بسته‌های انتخاب شده", "Skip the hash check when updating the selected packages": "رد کردن بررسی Hash هنگام به‌روزرسانی بسته‌های انتخاب شده", "Skip this version": "رد کردن این نسخه", "Software Updates": "بروزرسانی های نرم افزار", "Something went wrong": "مشکلی پیش آمد", "Something went wrong while launching the updater.": "هنگام اجرای به‌روزرسان، مشکلی پیش آمد.", "Source": "منبع", "Source URL:": "آدرس URL منبع:", "Source added successfully": "منبع با موفقیت اضافه شد", "Source addition failed": "افزودن منبع ناموفق بود", "Source name:": "نام منبع:", "Source removal failed": "حذف منبع ناموفق بود", "Source removed successfully": "منبع با موفقیت حذف شد", "Source:": "منبع:", "Sources": "منابع", "Start": "شروع", "Starting daemons...": "شروع daemon ها ...", "Starting operation...": "شروع عملیات...", "Startup options": "تنظیمات شروع برنامه", "Status": "وضعیت", "Stuck here? Skip initialization": "گیر کردید؟ مرحلهٔ راه‌اندازی را رد کنید", "Suport the developer": "حمایت از توسعه دهنده", "Support me": "حمایت از من", "Support the developer": "حمایت از توسعه‌دهنده", "Systems are now ready to go!": "سیستم‌ها الان آماده هستند!", "Telemetry": "تله‌متری", "Text": "متن", "Text file": "فایل متنی", "Thank you ❤": "ممنونم ❤", "Thank you 😉": "متشکرم 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "مدیر بسته Rust.<br>شامل: <b>کتابخانه‌های Rust و برنامه‌های نوشته شده در Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "نسخه پشتیبان شامل هیچ فایل باینری یا داده های ذخیره شده هیچ برنامه ای نمی شود.", "The backup will be performed after login.": "بک آپ گیری پس از ورود انجام خواهد شد.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "بک آپ شامل فهرست کامل بسته‌های نصب شده و گزینه‌های نصب آن‌ها خواهد بود. به‌روزرسانی‌های نادیده گرفته شده و نسخه‌های رد شده نیز ذخیره می‌شوند.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "مجموعه‌ای که سعی می‌کنید بارگذاری کنید نامعتبر به نظر می‌رسد. لطفاً فایل را بررسی کرده و دوباره تلاش کنید.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "مقدار چکسام نصب‌کننده با مقدار مورد انتظار همخوانی ندارد و اصالت نصب‌کننده قابل تأیید نیست. اگر به ناشر اعتماد دارید، می‌توانید با رد کردن بررسی چکسام، بسته را دوباره {0} کنید.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "مدیر بسته کلاسیک برای ویندوز. همه چیز را آنجا پیدا خواهید کرد. <br>شامل: <b>نرم افزار عمومی</b>", "The cloud backup completed successfully.": "پشتیبان گیری ابر با موفقیت به پایان رسید.", "The cloud backup has been loaded successfully.": "پشتیبان ابر با موفقیت لود شد.", "The current bundle has no packages. Add some packages to get started": "باندل فعلی هیچ بسته ای ندارد. برای شروع چند بسته اضافه کنید", "The executable file for {0} was not found": "فایل اجرایی برای {0} یافت نشد", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "هربار که یک {0} بسته نصب، بروزرسانی یا حذف نصب شد تنظیمات زیر بصورت پیشفرض اعمال میشوند.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "بسته های زیر قرار است به یک فایل JSON صادر شوند. هیچ داده کاربر یا باینری ذخیره نمی شود.", "The following packages are going to be installed on your system.": "بسته‌های زیر قرار است روی سیستم شما نصب شوند.", "The following settings may pose a security risk, hence they are disabled by default.": "تنظیمات زیر ممکنه ریسک امنیتی ایجاد کنند، بنابراین بصورت پیشفرض غیرفعال شده اند.", "The following settings will be applied each time this package is installed, updated or removed.": "هر بار که این بسته نصب، به‌روزرسانی یا حذف شود، تنظیمات زیر اعمال می‌شوند.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "هر بار که این بسته نصب، به‌روزرسانی یا حذف شود، تنظیمات زیر اعمال می‌شوند. به طور خودکار ذخیره می شوند.", "The icons and screenshots are maintained by users like you!": "آیکون ها و اسکرین شات ها توسط کاربرانی مثل شما حفظ و نگهداری می شود!\n", "The installer authenticity could not be verified.": "اصالت نصب‌کننده قابل تأیید نیست.", "The installer has an invalid checksum": "نصب‌کننده چک‌سام نامعتبری دارد", "The installer hash does not match the expected value.": "Hash نصب‌کننده با مقدار مورد انتظار مطابقت ندارد.", "The local icon cache currently takes {0} MB": "حافظه پنهان آیکون محلی در حال حاضر {0} مگابایت فضا اشغال کرده است", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "هدف اصلی این پروژه ایجاد یک فضای کاربری شهودی برای رایج ترین مدیر بسته های رابط خط فرمان ویندوز است، مانند Winget و Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "بسته \"{0}\" در مدیر بسته \"{1}\" یافت نشد", "The package bundle could not be created due to an error.": "مجموعه بسته به دلیل خطا ایجاد نشد.", "The package bundle is not valid": "مجموعه بسته معتبر نیست", "The package manager \"{0}\" is disabled": "مدیر بسته \"{0}\" غیرفعال است", "The package manager \"{0}\" was not found": "مدیر بسته \"{0}\" یافت نشد", "The package {0} from {1} was not found.": "بسته {0} از {1} یافت نشد.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "بسته‌های فهرست‌شده اینجا هنگام بررسی به‌روزرسانی‌ها در نظر گرفته نمی‌شوند. برای متوقف کردن نادیده گرفتن به‌روزرسانی آن‌ها، روی هر کدام دوبار کلیک کنید یا دکمه سمت راست‌شان را بزنید.", "The selected packages have been blacklisted": "بسته‌های انتخاب شده در لیست سیاه قرار گرفته‌اند", "The settings will list, in their descriptions, the potential security issues they may have.": "تنظیمات لیست خواهند شد، در توضیحاتشان، مشکلات جدی امنیتی که ممکنه داشته باشن.", "The size of the backup is estimated to be less than 1MB.": "اندازه بک آپ تخمین زده می‌شود کمتر از 1 مگابایت باشد.", "The source {source} was added to {manager} successfully": "منبع {source} با موفقیت به {manager} اضافه شد", "The source {source} was removed from {manager} successfully": "منبع {source} با موفقیت از {manager} حذف شد", "The system tray icon must be enabled in order for notifications to work": "آیکون نوار سیستم باید فعال باشد تا اعلان‌ها کار کنند", "The update process has been aborted.": "فرآیند به‌روزرسانی متوقف شد.", "The update process will start after closing UniGetUI": "فرآیند به‌روزرسانی پس از بستن UniGetUI شروع خواهد شد", "The update will be installed upon closing WingetUI": "به‌روزرسانی پس از بستن UniGetUI نصب خواهد شد", "The update will not continue.": "به‌روزرسانی ادامه نخواهد یافت.", "The user has canceled {0}, that was a requirement for {1} to be run": "کاربر {0} را لغو کرده است که شرطی برای اجرای {1} بود", "There are no new UniGetUI versions to be installed": "هیچ نسخه جدید UniGetUI برای نصب وجود ندارد", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "برخی عملیات در حال انجام هستند. بستن UniGetUI ممکن است باعث شکست آن‌ها شود. آیا مایلید ادامه دهید؟", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "ویدیوهای عالی‌ای در یوتیوب وجود دارند که UniGetUI و قابلیت‌های آن را نمایش می‌دهند. می‌تونی نکات و ترفندهای مفیدی یاد بگیری!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "دو دلیل اصلی وجود دارد که نباید UniGetUI را با دسترسی ادمین اجرا کنید:\n\nاول اینکه مدیر بسته Scoop ممکن است هنگام اجرای برخی دستورها با دسترسی ادمین دچار مشکل شود.\nدوم اینکه اجرای UniGetUI با دسترسی ادمین باعث می‌شود هر بسته‌ای که دانلود می‌کنید هم با دسترسی ادمین اجرا شود (و این امن نیست).\n\nیادتان باشد اگر نیاز دارید یک بسته خاص را با دسترسی ادمین نصب کنید، همیشه می‌توانید روی آن راست‌کلیک کرده و گزینه نصب/به‌روزرسانی/حذف به عنوان ادمین را انتخاب کنید.", "There is an error with the configuration of the package manager \"{0}\"": "خطایی در پیکربندی مدیر بسته \"{0}\" وجود دارد", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "نصب در حال انجام است. اگر UniGetUI را ببندید، ممکن است نصب با شکست مواجه شود یا نتایج غیرمنتظره‌ای داشته باشد.\nآیا همچنان می‌خواهید UniGetUI را ببندید؟", "They are the programs in charge of installing, updating and removing packages.": "این‌ها برنامه‌هایی هستند که وظیفه نصب، به‌روزرسانی و حذف بسته‌ها را بر عهده دارند.", "Third-party licenses": "مجوزهای نرم‌افزارهای جانبی", "This could represent a <b>security risk</b>.": "این می‌تواند <b>خطر امنیتی</b> باشد.", "This is not recommended.": "این توصیه نمی‌شود.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "این احتمالاً به این دلیل است که بسته ای که ارسال شده بود حذف شده است یا در یک مدیر بسته منتشر شده است که شما آن را فعال نکرده اید. شناسه دریافتی {0} است", "This is the <b>default choice</b>.": "این <b>انتخاب پیش‌فرض</b> است.", "This may help if WinGet packages are not shown": "این ممکن است کمک کند اگر بسته‌های WinGet نشان داده نمی‌شوند", "This may help if no packages are listed": "این ممکن است کمک کند اگر هیچ بسته‌ای فهرست نشده", "This may take a minute or two": "این ممکن است یک یا دو دقیقه طول بکشد", "This operation is running interactively.": "این عملیات به صورت تعاملی در حال اجرا است.", "This operation is running with administrator privileges.": "این عملیات با دسترسی های ادمین در حال اجرا است.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "این تنظیم باعث مشکلاتی میشه. هر فرآیند نامتناسب با افزایش سطح دسترسی خودش شکست خواهد خورد. نصب/بروزرسانی/حذف نصب بعنوان ادمین سیستم کار نخواهد کرد.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "این باندل بسته تنظیماتی داشت که خطرناک هستند، و شاید بصورت پیش فرض مردود بشن.", "This package can be updated": "این بسته قابل به‌روزرسانی است", "This package can be updated to version {0}": "این بسته قابل به‌روزرسانی به نسخه {0} است", "This package can be upgraded to version {0}": "این بسته قابل ارتقا به نسخه {0} است", "This package cannot be installed from an elevated context.": "این بسته نمی‌تواند از محیط ارتقا یافته نصب شود.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "این بسته اسکرین‌شات یا آیکن ندارد؟ با اضافه کردن آیکن‌ها و اسکرین‌شات‌های گم‌شده به پایگاه‌داده عمومی و آزاد UniGetUI در بهبود آن مشارکت کنید.", "This package is already installed": "این بسته قبلاً نصب شده است", "This package is being processed": "این بسته در حال پردازش است", "This package is not available": "این بسته در دسترس نیست", "This package is on the queue": "این بسته در صف است", "This process is running with administrator privileges": "این فرایند با دسترسی های ادمین در حال اجرا است.", "This project has no connection with the official {0} project — it's completely unofficial.": "این پروژه هیچ ارتباطی با پروژه رسمی {0} ندارد — کاملا غیر رسمی است.", "This setting is disabled": "این تنظیمات غیرفعال شده است", "This wizard will help you configure and customize WingetUI!": "این قابلیت به شما کمک می‌کند UniGetUI را پیکربندی و شخصی سازی کنید!", "Toggle search filters pane": "تغییر وضعیت پنل فیلترهای جستجو", "Translators": "مترجم ها", "Try to kill the processes that refuse to close when requested to": "سعی کن پروسه هایی که درخواست پایان یافتن رو رد میکنن، به اجبار پایان بدی", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "فعال کردن این، تغییر دادن فایل قابل اجرای استفاده شده برای تعامل با مدیران بسته رو ممکن میکنه. درحالی که این سفارشی سازی دقیق تر پروسه های نصب رو ممکن میکنه، ولی میتونه خطرناک هم باشه", "Type here the name and the URL of the source you want to add, separed by a space.": "در اینجا نام و نشانی اینترنتی منبعی را که می خواهید اضافه کنید، با فاصله از هم تایپ کنید.", "Unable to find package": "بسته پیدا نشد", "Unable to load informarion": "بارگیری اطلاعات ممکن نیست\n", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI داده‌های استفاده ناشناس جمع‌آوری می‌کند تا تجربه کاربری را بهبود بخشد.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI داده‌های استفاده ناشناس را با هدف صرف درک و بهبود تجربه کاربری جمع‌آوری می‌کند.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI میانبر جدید دسکتاپی تشخیص داده که می‌تواند به طور خودکار حذف شود.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI میانبرهای دسکتاپ زیر را تشخیص داده که می‌توانند در ارتقاهای آینده به طور خودکار حذف شوند", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI {0} میان<PERSON><PERSON> جدید دسکتاپ تشخیص داده که می‌توانند به طور خودکار حذف شوند.", "UniGetUI is being updated...": "UniGetUI در حال به‌روزرسانی است...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI به هیچ یک از مدیران بسته سازگار مرتبط نیست. UniGetUI یک پروژه مستقل است.", "UniGetUI on the background and system tray": "UniGetUI در پس‌زمینه و نوار سیستم", "UniGetUI or some of its components are missing or corrupt.": "برخی از کامپوننت های UniGetUI یا کل آن از دست رفته یا خراب شده اند.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI برای کار کردن به {0} نیاز دارد، اما در سیستم شما یافت نشد.", "UniGetUI startup page:": "صفحه راه‌اندازی UniGetUI:", "UniGetUI updater": "به‌روزرسان UniGetUI", "UniGetUI version {0} is being downloaded.": "نسخه {0} UniGetUI در حال دانلود است.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} آماده نصب است.", "Uninstall": "حد<PERSON> برن<PERSON><PERSON>ه", "Uninstall Scoop (and its packages)": "<PERSON>ذ<PERSON> (و بسته های آن)", "Uninstall and more": "حذف نصب و بیشتر", "Uninstall and remove data": "حذف و پاک کردن داده‌ها", "Uninstall as administrator": "حد<PERSON> برنامه به عنوان ادمین", "Uninstall canceled by the user!": "حذف برنامه توسط کابر لغو شد!", "Uninstall failed": "حذف ناموفق", "Uninstall options": "تنظیمات حذف نصب", "Uninstall package": "حذ<PERSON> بسته", "Uninstall package, then reinstall it": "حذف بسته و سپس دوباره نصب کردن آن", "Uninstall package, then update it": "حذف بسته و سپس به روز رسانی آن", "Uninstall previous versions when updated": "حذف نصب نسخه های قبلی هنگامی که بروزرسانی شدند", "Uninstall selected packages": "حذف بسته های انتخاب شده", "Uninstall selection": "حذف نصب انتخاب شده ها", "Uninstall succeeded": "حذف موف<PERSON><PERSON>ت آ<PERSON>یز بود", "Uninstall the selected packages with administrator privileges": "حذف بسته های انتخابی با امتیازات ادمین", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "بسته های غیرقابل نصب با مبدأ فهرست شده به عنوان \"{0}\" در هیچ مدیر بسته منتشر نمی شوند، بنابراین هیچ اطلاعاتی برای نمایش در مورد آنها وجود ندارد.", "Unknown": "ناشناخته", "Unknown size": "اندازه نامشخص", "Unset or unknown": "تنظیم نشده یا ناشناخته", "Up to date": "به‌روز", "Update": "به روز رسانی", "Update WingetUI automatically": "به‌روزرسانی خودکار UniGetUI", "Update all": "همه را بروز رسانی کن", "Update and more": "بروزرسانی و بیشتر", "Update as administrator": "بروزرسانی به عنوان ادمین", "Update check frequency, automatically install updates, etc.": "فرکانس بررسی به‌روزرسانی، نصب خودکار به‌روزرسانی‌ها و غیره.", "Update date": "تاریخ به روز رسانی", "Update failed": "بروزرسانی شکست خورد", "Update found!": "به روز رسانی پیدا شد!", "Update now": "اکنون بروزرسانی کن", "Update options": "تنظیمات بروزرسانی", "Update package indexes on launch": "به روز رسانی شاخص های بسته در راه اندازی", "Update packages automatically": "بروزرسانی خودکار بسته ها", "Update selected packages": "بروزرسانی بسته های انتخاب شده", "Update selected packages with administrator privileges": "به روزرسانی بسته های انتخابی با امتیازات ادمین", "Update selection": "بروزرسانی انتخاب شده ها", "Update succeeded": "بروزرسانی با موفقیت انجام شد", "Update to version {0}": "به‌روزرسانی به نسخه {0}", "Update to {0} available": "بروزرسانی به {0} موجو<PERSON> است", "Update vcpkg's Git portfiles automatically (requires Git installed)": "به‌روزرسانی خودکار پورت‌فایل‌های Git در vcpkg (نیاز به نصب بودن Git دارد)", "Updates": "بروزرسانی ها", "Updates available!": "بروزرسانی در دسترس است!", "Updates for this package are ignored": "بروزرسانی های این بسته نادیده گرفته شود ", "Updates found!": "به روز رسانی پیدا شد!", "Updates preferences": "تنظیمات به‌روزرسانی‌ها", "Updating WingetUI": "در حال به روز رسانی UniGetUI", "Url": "آدرس URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "استفاده از WinGet بسته‌بندی شده قدیمی به جای CMDLetهای PowerShell", "Use a custom icon and screenshot database URL": "استفاده از URL پایگاه داده آیکون و تصویر شخصی سازی شده", "Use bundled WinGet instead of PowerShell CMDlets": "استفاده از WinGet بسته‌بندی شده به جای CMDletهای PowerShell", "Use bundled WinGet instead of system WinGet": "استفاده از WinGet بسته‌بندی شده به جای WinGet سیستم", "Use installed GSudo instead of UniGetUI Elevator": "برای افزایش سطح دسترسی، بجای افزاینده UniGetUI از GSudo نصب شده استفاده کن", "Use installed GSudo instead of the bundled one": "از GSudo نصب‌شده به‌جای نسخه‌ی همراه استفاده کن", "Use system Chocolatey": "استفاده از Chocolatey سیستم", "Use system Chocolatey (Needs a restart)": " از Chocolatey سیستم استفاده کن ( نیاز به راه اندازی مجدد دارد )", "Use system Winget (Needs a restart)": "از Winget سیستم استفاده کن ( نیاز به راه اندازی مجدد دارد )", "Use system Winget (System language must be set to english)": "استفاده از WinGet سیستم (زبان سیستم باید روی انگلیسی تنظیم شود)", "Use the WinGet COM API to fetch packages": "استفاده از API COM WinGet برای دریافت بسته‌ها", "Use the WinGet PowerShell Module instead of the WinGet COM API": "استفاده از ماژول PowerShell WinGet به جای API COM WinGet", "Useful links": "لینک های مفید ", "User": "کاربر", "User interface preferences": "تنظیمات رابط کاربری", "User | Local": "کاربر | محلی", "Username": "نام کاربری", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "استفاده از UniGetUI به معنای پذیرش مجوز GNU Lesser General Public License v2.1 است", "Using WingetUI implies the acceptation of the MIT License": "استفاده از UniGetUI به معنای پذیرش مجوز MIT است", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "ریشه Vcpkg یافت نشد. لطفاً متغیر محیطی %VCPKG_ROOT% را تعریف کنید یا آن را از تنظیمات UniGetUI تعریف کنید", "Vcpkg was not found on your system.": "Vcpkg در سیستم شما یافت نشد.", "Verbose": "مفصل", "Version": "نسخه", "Version to install:": "نسخه برای نصب:", "Version:": "نسخه:", "View GitHub Profile": "مشاهده پروفا<PERSON><PERSON> GitHub", "View WingetUI on GitHub": "مشاهده پروفایل UniGetUI در GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "کد منبع UniGetUI را ببینید. از آنجا می‌توانید باگ‌ها را گزارش کنید، پیشنهادات ویژگی بدهید یا حتی مستقیماً در پروژه UniGetUI مشارکت کنید.", "View mode:": "حالت نمایش:", "View on UniGetUI": "مشاهده در UniGetUI", "View page on browser": "نمایش صفحه در مرورگر", "View {0} logs": "مشاهده لاگ‌های {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "قبل از تلاش برای انجام کارهایی که نیاز به اتصال اینترنت دارند، منتظر بمانید تا دستگاه به اینترنت متصل شود.", "Waiting for other installations to finish...": "در انتظار پایان نصب های دیگر...", "Waiting for {0} to complete...": "منتظر تکمیل {0}...", "Warning": "هشدار", "Warning!": "هشدار!", "We are checking for updates.": "ما در حال بررسی به‌روزرسانی‌ها هستیم.", "We could not load detailed information about this package, because it was not found in any of your package sources": "ما نتوانستیم اطلاعات دقیق درباره این بسته را بارگیری کنیم، زیرا در هیچ یک از منابع بسته شما یافت نشد", "We could not load detailed information about this package, because it was not installed from an available package manager.": "ما نتوانستیم اطلاعات دقیق درباره این بسته را بارگیری کنیم، زیرا از یک مدیر بسته موجود نصب نشده بود.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "ما نتوانستیم {action} {package} را انجام دهیم. لطفاً بعداً دوباره امتحان کنید. برای دریافت گزارش‌ها از نصب‌کننده، روی «{showDetails}» کلیک کنید.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "ما نتوانستیم {action} {package} را انجام دهیم. لطفاً بعداً دوباره امتحان کنید. روی \"{showDetails}\" کلیک کنید تا گزارش‌ها را از برنامه حذف نصب کنید.", "We couldn't find any package": "نتوانستیم بسته ای را پیدا کنیم", "Welcome to WingetUI": "به UniGetUI خوش آمدید", "When batch installing packages from a bundle, install also packages that are already installed": "وقتی بسته ها رو بصورت دسته ای از یک باندل نصب میکنی، بسته هایی که از قبل نصب بودن هم نصب کن.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "هنگام تشخیص میانبرهای جدید، آن‌ها را به طور خودکار حذف کنید به جای نمایش این دیالوگ.", "Which backup do you want to open?": "کدوم پشتیبان رو میخوای باز کنی؟", "Which package managers do you want to use?": "کدام مدیران بسته را می‌خواهید استفاده کنید؟", "Which source do you want to add?": "کدام منبع را می‌خواهید اضافه کنید؟", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "هرچند Winget را می‌توان در UniGetUI استفاده کرد، اما UniGetUI قابلیت کار با سایر مدیرهای بسته را هم دارد که ممکن است کمی گیج‌کننده باشد. در گذشته، UniGetUI فقط برای کار با Winget طراحی شده بود، اما حالا اینطور نیست و به همین دلیل UniGetUI نمایانگر هدف اصلی این پروژه نیست.", "WinGet could not be repaired": "WinGet قابل تعمیر نبود", "WinGet malfunction detected": "نقص عملکرد WinGet تشخیص داده شد", "WinGet was repaired successfully": "WinGet با موفقیت تعمیر شد", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - همه چیز به روز است", "WingetUI - {0} updates are available": "UniGetUI - و {0} تا به‌روزرسانی‌ موجود است", "WingetUI - {0} {1}": "UniGetUI - {1} {0}", "WingetUI Homepage": "صفحه اصلی UniGetUI", "WingetUI Homepage - Share this link!": "صفحه نخست UniGetUI - این لینک را به اشتراک بگذارید!", "WingetUI License": "لایسنس UniGetUI", "WingetUI Log": "لاگ UniGetUI", "WingetUI Repository": "مخزن UniGetUI", "WingetUI Settings": "تنظیمات UniGetUI", "WingetUI Settings File": "فایل تنظیمات UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI از کتابخانه های زیر استفاده می کند. بدون آنها، UniGetUI ممکن نبود.", "WingetUI Version {0}": "UniGetUI نسخه {0}", "WingetUI autostart behaviour, application launch settings": "رفتار شروع خودکار UniGetUI, تنظیمات اجرای برنامه", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI می‌تواند بررسی کند که آیا نرم‌افزار شما به‌روزرسانی‌های موجود دارد یا خیر، و در صورت تمایل آنها را به‌طور خودکار نصب کند", "WingetUI display language:": "زبان نمایشی UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI با دسترسی ادمین اجرا شده است، که توصیه نمی‌شود. وقتی UniGetUI را با دسترسی ادمین اجرا می‌کنید، همه عملیات انجام شده توسط برنامه با دسترسی ادمین اجرا می‌شوند.\nشما همچنان می‌توانید از برنامه استفاده کنید، اما به‌شدت توصیه می‌کنیم UniGetUI را با دسترسی ادمین اجرا نکنید.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI به لطف مترجمان داوطلب به بیش از 40 زبان ترجمه شده است. ممنون 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI با ماشین ترجمه نشده است! کاربران زیر مسئولیت ترجمه را بر عهده داشته اند:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI برنامه‌ای است که مدیریت نرم‌افزار شما را با ارائه یک رابط گرافیکی همه‌جانبه برای مدیران بسته خط فرمان شما آسان‌تر می‌کند.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI به منظور تأکید بر تفاوت بین UniGetUI (رابطی که در حال حاضر استفاده می کنید) و Winget (یک مدیر بسته توسعه یافته توسط مایکروسافت که من با آن ارتباطی ندارم) تغییر نام داده می شود.", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI در حال بروزرسانی است. پس از اتمام بروز رسانی، UniGetUI خود را بروزرسانی خواهد کرد. ", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI رایگان است و برای همیشه رایگان خواهد بود. بدون تبلیغات، بدون کارت اعتباری، بدون نسخه حق بیمه. 100٪ رایگان، برای همیشه.", "WingetUI log": "لاگ UniGetUI", "WingetUI tray application preferences": "تنظیمات دلخواه آیکون وظیفه UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI از کتابخانه های زیر استفاده می کند. بدون آنها، UniGetUI ممکن نبود.", "WingetUI version {0} is being downloaded.": "نسخه {0} UniGetUI در حال دانلود است.", "WingetUI will become {newname} soon!": "UniGetUI به زودی {newname} خواهد شد!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI به‌طور دوره‌ای به‌روزرسانی‌ها را بررسی نمی‌کند. آنها همچنان در هنگام راه اندازی بررسی می شوند، اما در مورد آنها به شما هشدار داده نمی شود.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI هر بار که یک بسته برای نصب نیاز به elevation داشته باشد، یک UAC را نشان می دهد.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI به زودی {newname} نامگذاری خواهد شد. این نشان دهنده هیچ تغییری در برنامه نخواهد بود. من (توسعه دهنده) توسعه این پروژه را همانطور که در حال حاضر انجام می دهم ادامه خواهم داد، اما با نامی دیگر.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI بدون کمک مشارکت کنندگان عزیز ما امکان پذیر نبود. پروفایل هایGitHub آنها را بررسی کنید، WingetUI بدون آنها امکان پذیر نمی بود!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI بدون کمک مشارکت کنندگان ممکن نبود. ممنون از همه 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} آماده نصب است.", "Write here the process names here, separated by commas (,)": "نام پروسه ها رو اینجا بنویس، بصورت جدا شده با کاما (,)", "Yes": "بله", "You are logged in as {0} (@{1})": "شما به عنوان {0} (@{1} ) وارد شده اید", "You can change this behavior on UniGetUI security settings.": "شما میتوانید این رفتار را در تنظیمات امنیتی UniGetUI تغییر دهید.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "شما میتوانید دستوراتی را مشخص کنید که قبل یا بعد از نصب، بروزرسانی یا حذف نصب این بسته اجرا شوند. آنها روی یک Command Prompt ذخیره خواهند شد، بنابراین اسکریپت های CMD اینجا کار خواهند کرد.", "You have currently version {0} installed": "شما در حال حاضر نسخه {0} نصب دارید", "You have installed WingetUI Version {0}": "شما UniGetUI نسخه {0} نصب کرده‌اید", "You may lose unsaved data": "ممکنه شما داده های ذخیره نشده رو از دست بدهید", "You may need to install {pm} in order to use it with WingetUI.": "ممکن است نیاز داشته باشید {pm} را نصب کنید تا بتوانید آن را با UniGetUI استفاده کنید.", "You may restart your computer later if you wish": "در صورت تمایل می توانید بعداً رایانه خود را مجدداً راه اندازی کنید", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "فقط یک بار از شما سؤال خواهد شد و حقوق مدیر به بسته‌هایی که درخواست می‌کنند اعطا خواهد شد.", "You will be prompted only once, and every future installation will be elevated automatically.": "فقط یک بار از شما سؤال خواهد شد و هر نصب آینده به طور خودکار ارتقا خواهد یافت.", "You will likely need to interact with the installer.": "احتمالاً نیاز خواهید داشت با نصب‌کننده تعامل کنید.", "[RAN AS ADMINISTRATOR]": "[به عنوان ادمین اجرا شد]", "buy me a coffee": "برام قهوه بخر", "extracted": "استخراج شده", "feature": "ویژگی", "formerly WingetUI": "قبلاً WingetUI", "homepage": "وبسایت", "install": "نصب", "installation": "نصب", "installed": "نصب شده است", "installing": "در حال نصب", "library": "کتابخانه", "mandatory": "ضروری", "option": "گزینه", "optional": "اختیاری", "uninstall": "حد<PERSON> برن<PERSON><PERSON>ه", "uninstallation": "فرا<PERSON>ند حدف برنا<PERSON>ه", "uninstalled": "حذ<PERSON> ش<PERSON>", "uninstalling": "در حال حذف", "update(noun)": "بروزرسانی", "update(verb)": "بروزرسانی", "updated": "به روز شده\n", "updating": "در حال بروز رسانی\n", "version {0}": "نسخه {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} تنظیمات نصب درحال حاضر قفل هستند چون {0} از تنظیمات پیشفرض نصب پیروی میکند.", "{0} Uninstallation": "{0} مورد حذف نصب\n", "{0} aborted": "{0} متوقف شد", "{0} can be updated": "{0} را می توان به روز کرد\n", "{0} can be updated to version {1}": "{0} قابل به‌روزرسانی به نسخه {1} است", "{0} days": "{0} روز", "{0} desktop shortcuts created": "{0} میانبر دسکتاپ ایجاد شد", "{0} failed": "{0} ناموفق بود", "{0} has been installed successfully.": "{0} با موفقیت نصب شد.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} با موفقیت نصب شد. توصیه می‌شود برای تکمیل نصب، UniGetUI را راه‌اندازی مجدد کنید", "{0} has failed, that was a requirement for {1} to be run": "کاربر {0} را لغو کرده است که شرطی برای اجرای {1} بود", "{0} homepage": "{0} صف<PERSON>ه اصلی", "{0} hours": "{0} ساعت", "{0} installation": "{0} نصب", "{0} installation options": "{0} گزینه های نصب", "{0} installer is being downloaded": "{0} در حال نصب است", "{0} is being installed": "{0} در حال نصب است", "{0} is being uninstalled": "{0} در حال حذف است", "{0} is being updated": "{0} تا در حال به روز رسانی است", "{0} is being updated to version {1}": "{0} در حال به‌روزرسانی به نسخه {1} است", "{0} is disabled": "{0} تا غیر فعال شده است", "{0} minutes": "{0} دقیقه", "{0} months": "{0} ماه", "{0} packages are being updated": "{0} بسته  در حال به روز رسانی است", "{0} packages can be updated": "{0} بسته را می توان به روز کرد\n", "{0} packages found": "{0} بسته پیدا شد\n", "{0} packages were found": "{0} بسته پیدا شد", "{0} packages were found, {1} of which match the specified filters.": " {0} بسته یافت شد، که {1} از آن‌ها با فیلترهای مشخص شده مطابقت دارند.", "{0} settings": "{0} تنظیمات", "{0} status": "{0} وضعیت", "{0} succeeded": "{0} مورد موفق بود", "{0} update": "{0} به روزرسانی", "{0} updates are available": "به روز رسانی های موجود: {0}", "{0} was {1} successfully!": "{0} {1} با موفقیت انجام شد!", "{0} weeks": "{0} هفته", "{0} years": "{0} سال", "{0} {1} failed": "{1}{0} ناموفق بود", "{package} Installation": "پروسه نصب بسته {package}", "{package} Uninstall": "حذف بسته {package}", "{package} Update": "به روز رسانی بسته {package}", "{package} could not be installed": "بسته {package} نصب نمیشود", "{package} could not be uninstalled": "بسته {package} حذف نمیشود", "{package} could not be updated": "بسته {package} به روز رسانی نمیشود", "{package} installation failed": "نصب بسته {package} انجام نشد", "{package} installer could not be downloaded": "نصب کننده بسته {package} دانلود نشد", "{package} installer download": "دانلود نصب‌کننده بسته {package}", "{package} installer was downloaded successfully": "نصب کننده بسته {package} با موفقیت دانلود شد", "{package} uninstall failed": "حذف بسته {package} موفقیت آمیز نبود", "{package} update failed": "به روز رسانی بسته {package} موفقیت آمیز نبود", "{package} update failed. Click here for more details.": "بروزرسانی {package} با شکست مواجه شد. جهت جزئیات بیشتر، کلیک کنید.", "{package} was installed successfully": "{package} با موفقیت نصب شد", "{package} was uninstalled successfully": "بسته {package} با موفقیت حذف شد", "{package} was updated successfully": "بسته {package} با موفقیت به روز رسانی شد", "{pcName} installed packages": "بسته‌های نصب شده {pcName}", "{pm} could not be found": "{pm} پیدا نشد", "{pm} found: {state}": "{pm} پیدا شد: {state}", "{pm} is disabled": "{pm} غیرفعال است", "{pm} is enabled and ready to go": "{pm} فعال و آماده به کار است", "{pm} package manager specific preferences": "{pm} تنظیمات مربوط به مدیر بسته", "{pm} preferences": "تنظیمات {pm}", "{pm} version:": "نسخه {pm}:", "{pm} was not found!": "{pm} یافت نشد!"}