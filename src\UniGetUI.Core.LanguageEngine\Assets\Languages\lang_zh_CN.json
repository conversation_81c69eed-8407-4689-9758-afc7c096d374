{"\"{0}\" is a local package and can't be shared": "“{0}”是本地软件包，无法共享", "\"{0}\" is a local package and does not have available details": "“{0}”是缺乏详细信息的本地软件包", "\"{0}\" is a local package and is not compatible with this feature": "“{0}”是本地软件包，与此功能不兼容", "(Last checked: {0})": "（上一次检查的时间：{0}）", "(Number {0} in the queue)": "（队伍中的第{0}个）", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON>, BUGP Association, c<PERSON><PERSON>, <PERSON><PERSON>, CnYeSheng, adfnekc, @Ardenet, @arthurfsy2, @bai0012, @SpaceTimee, <PERSON><PERSON><PERSON>, @dongfengweixiao, @seanyu0, @Sigechaishijie, @enKl03B, @xiaopangju", "0 packages found": "未找到软件包", "0 updates found": "未找到更新", "1 - Errors": "1 - 错误", "1 day": "1 天", "1 hour": "1 小时", "1 month": "1 月", "1 package was found": "找到 1 个软件包", "1 update is available": "有 1 个可用更新", "1 week": "1 周", "1 year": "1 年", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. 导航至 “{0}” 或 “{1}” 页面。", "2 - Warnings": "2 - 警告", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. 找到要添加进捆绑包的软件包，然后选中它们最左侧的复选框。", "3 - Information (less)": "3 - 信息（简要）", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. 当你选择好要添加进捆绑包的软件包时，找到并点击工具栏上的选项 “{0}” 。", "4 - Information (more)": "4 - 信息（详情）", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. 你的软件包将被添加进捆绑包。你可以继续添加软件包，或者导出捆绑包。", "5 - information (debug)": "5 - 信息（调试）", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "一个流行的 C/C++ 库管理器。包含 C/C++ 库和其它与 C/C++ 相关的实用程序<br>包括：<b>C/C++ 库和相关的实用程序</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "一个含有所有为微软 .NET 生态设计的工具和可执行程序的存储库。<br>包括：<b>与 .NET 相关的工具和脚本</b>\n", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "一个含有所有为微软 .NET 生态设计的工具的存储库。<br>包括：<b>与 .NET 相关的工具</b>", "A restart is required": "需要重启", "Abort install if pre-install command fails": "如果安装前命令失败则中止安装操作", "Abort uninstall if pre-uninstall command fails": "如果卸载前命令失败则中止卸载操作", "Abort update if pre-update command fails": "如果更新前命令失败则中止更新操作", "About": "关于", "About Qt6": "关于 Qt6", "About WingetUI": "关于 WingetUI", "About WingetUI version {0}": "关于 UniGetUI 版本 {0}", "About the dev": "关于开发者们", "Accept": "接受", "Action when double-clicking packages, hide successful installations": "双击软件包后的行为、隐藏已成功安装项目", "Add": "添加", "Add a source to {0}": "添加安装源至 {0}", "Add a timestamp to the backup file names": "在备份文件名中添加时间戳", "Add a timestamp to the backup files": "在备份文件中添加时间戳", "Add packages or open an existing bundle": "添加软件包或打开一个已有的捆绑包", "Add packages or open an existing package bundle": "添加软件包或打开一个已有的捆绑包", "Add packages to bundle": "添加软件包进捆绑包", "Add packages to start": "从添加软件包开始", "Add selection to bundle": "添加所选项进捆绑包", "Add source": "添加安装源", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "将“未找到适用更新”错误而失败的更新添加进“已忽略更新”列表中。", "Adding source {source}": "添加源 {source}", "Adding source {source} to {manager}": "添加安装源 {source} 至软件包管理器 {manager}", "Addition succeeded": "添加成功", "Administrator privileges": "管理员权限", "Administrator privileges preferences": "管理员权限首选项", "Administrator rights": "管理员权限", "Administrator rights and other dangerous settings": "管理员权限和其它危险设置", "Advanced options": "高级选项", "All files": "所有文件", "All versions": "所有版本", "Allow changing the paths for package manager executables": "允许更改包管理器执行文件的路径", "Allow custom command-line arguments": "允许自定义命令行参数", "Allow importing custom command-line arguments when importing packages from a bundle": "从捆绑包导入软件包时允许导入自定义命令行参数", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "从捆绑包导入软件包时，允许导入自定义的安装前和安装后命令", "Allow package operations to be performed in parallel": "允许并行执行软件包操作", "Allow parallel installs (NOT RECOMMENDED)": "允许多个软件包并行安装（不推荐）", "Allow pre-release versions": "允许预发布版本", "Allow {pm} operations to be performed in parallel": "允许并行执行 {pm} 的操作", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "另外，您也可以在 Windows PowerShell 提示符中运行以下命令来安装 {0}：", "Always elevate {pm} installations by default": "默认始终以管理员身份进行 {pm} 的安装", "Always run {pm} operations with administrator rights": "始终以管理员权限执行 {pm} 的操作", "An error occurred": "出现错误", "An error occurred when adding the source: ": "添加源时出现错误：", "An error occurred when attempting to show the package with Id {0}": "尝试显示 ID 为 {0} 的软件包时出现错误", "An error occurred when checking for updates: ": "检查更新时出现错误：", "An error occurred while attempting to create an installation script:": "尝试创建安装脚本时出错：", "An error occurred while loading a backup: ": "载入备份时出错：", "An error occurred while logging in: ": "登录时出错：", "An error occurred while processing this package": "处理此软件包时出现错误", "An error occurred:": "出现错误：", "An interal error occurred. Please view the log for further details.": "程序出现内部错误，请查看日志文件获取详情。", "An unexpected error occurred:": "出现意外错误：", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "尝试修复 WinGet 时出现意外错误。请稍后再试", "An update was found!": "发现更新！", "Android Subsystem": "安卓子系统", "Another source": "其它源", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "将自动删除在安装或更新操作期间创建的任何新快捷方式，不会在首次检测到它们时显示确认提示。", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "将忽略在 UniGetUI 之外创建或修改的任何快捷方式。您可以通过 {0} 按钮来添加它们。", "Any unsaved changes will be lost": "未保存的更改将会丢失", "App Name": "应用名称", "Appearance": "外观", "Application theme, startup page, package icons, clear successful installs automatically": "应用程序主题、起始页、软件包图标、自动清除安装成功的记录", "Application theme:": "应用程序主题：", "Apply": "应用", "Architecture to install:": "安装架构：", "Are these screenshots wron or blurry?": "这些屏幕截图有错误或者模糊不清吗？", "Are you really sure you want to enable this feature?": "您确实想要启用此功能吗 ？", "Are you sure you want to create a new package bundle? ": "您确实想创建一个新的软件捆绑包吗 ？", "Are you sure you want to delete all shortcuts?": "您确实想要删除所有快捷方式吗 ？", "Are you sure?": "您确定要进行吗？", "Ascendant": "升序", "Ask for administrator privileges once for each batch of operations": "每批操作请求一次管理员权限", "Ask for administrator rights when required": "需要时请求管理员权限", "Ask once or always for administrator rights, elevate installations by default": "请求一次或始终请求管理员权限，默认情况下以管理员权限安装", "Ask only once for administrator privileges": "仅请求一次管理员权限", "Ask only once for administrator privileges (not recommended)": "仅请求一次管理员权限（不推荐）", "Ask to delete desktop shortcuts created during an install or upgrade.": "安装或升级期间询问是否要删除创建的桌面快捷方式。", "Attention required": "请注意", "Authenticate to the proxy with an user and a password": "使用用户名和密码向代理进行身份验证", "Author": "制作者", "Automatic desktop shortcut remover": "桌面快捷方式自动删除程序", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "自动保存所有已安装软件的列表以便于恢复它们。", "Automatically save a list of your installed packages on your computer.": "在您的计算机上自动保存所有已安装软件包的列表。", "Autostart WingetUI in the notifications area": "开机自动在系统托盘启动 WingetUI", "Available Updates": "可用更新", "Available updates: {0}": "可用更新：{0}", "Available updates: {0}, not finished yet...": "可用更新：{0}，仍在进行中……", "Backing up packages to GitHub Gist...": "正在将软件包备份到 GitHub Gist...", "Backup": "备份", "Backup Failed": "备份失败", "Backup Successful": "备份成功", "Backup and Restore": "备份和还原", "Backup installed packages": "备份已安装软件包", "Backup location": "备份位置", "Become a contributor": "成为贡献者", "Become a translator": "成为翻译人员", "Begin the process to select a cloud backup and review which packages to restore": "启动选择云备份并查看要还原哪些软件包的流程", "Beta features and other options that shouldn't be touched": "不建议使用的测试版功能和其它选项", "Both": "软件包名称或 ID", "Bundle security report": "捆绑包安全报告", "But here are other things you can do to learn about WingetUI even more:": "但是您可以通过以下方式更多了解 WingetUI：", "By toggling a package manager off, you will no longer be able to see or update its packages.": "关闭软件包管理器，您将无法再查看或更新其软件包。 ", "Cache administrator rights and elevate installers by default": "缓存管理员权限，并总是以管理员权限安装", "Cache administrator rights, but elevate installers only when required": "缓存管理员权限，但仅在需要时以管理员权限安装", "Cache was reset successfully!": "缓存已成功重置！", "Can't {0} {1}": "无法 {0} {1}", "Cancel": "取消", "Cancel all operations": "取消全部操作", "Change backup output directory": "更改备份输出文件夹", "Change default options": "更改默认选项", "Change how UniGetUI checks and installs available updates for your packages": "更改 UniGetUI 检查和安装软件包可用更新的方式", "Change how UniGetUI handles install, update and uninstall operations.": "更改 UniGetUI 处理安装、更新和卸载操作的方式。", "Change how UniGetUI installs packages, and checks and installs available updates": "更改 UniGetUI 安装软件包的方式，并检查和安装可用的更新", "Change how operations request administrator rights": "更改操作请求管理员权限的方式", "Change install location": "更改安装位置", "Change this": "更改", "Change this and unlock": "更改并解锁", "Check for package updates periodically": "定期检查软件包更新", "Check for updates": "检查更新", "Check for updates every:": "更新检查间隔：", "Check for updates periodically": "定期检查更新", "Check for updates regularly, and ask me what to do when updates are found.": "定期检查更新，并在发现更新时询问如何处理。", "Check for updates regularly, and automatically install available ones.": "定期检查更新并自动安装可用更新。", "Check out my {0} and my {1}!": "检查我的 {0} 和 {1}！", "Check out some WingetUI overviews": "查阅 WingetUI 的概述", "Checking for other running instances...": "正在检查其它运行实例……", "Checking for updates...": "正在检查更新……", "Checking found instace(s)...": "正在检查已找到实例……", "Choose how many operations shouls be performed in parallel": "选择可并行执行多少操作", "Clear cache": "清除缓存", "Clear finished operations": "清除已完成操作", "Clear selection": "清除已选", "Clear successful operations": "清除成功操作", "Clear successful operations from the operation list after a 5 second delay": "延迟 5 秒后从操作列表中清除成功的操作", "Clear the local icon cache": "清除本地图标缓存", "Clearing Scoop cache - WingetUI": "清理 Scoop 缓存 - WingetUI", "Clearing Scoop cache...": "正在清理 Scoop 缓存……", "Click here for more details": "单击此处可获取更多详情", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "点击安装可开始安装进程。如果您跳过安装步骤，UniGetUI 可能不会正常工作。", "Close": "关闭", "Close UniGetUI to the system tray": "关闭 UniGetUI 时将它隐藏到系统托盘", "Close WingetUI to the notification area": "关闭 WingetUI 到系统托盘", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "云备份使用私有 GitHub Gist 来存储已安装软件包的列表", "Cloud package backup": "云软件包备份", "Command-line Output": "命令行输出", "Command-line to run:": "运行命令行：", "Compare query against": "比较的查询字段", "Compatible with authentication": "兼容身份验证", "Compatible with proxy": "兼容代理", "Component Information": "组件信息", "Concurrency and execution": "并发与执行", "Connect the internet using a custom proxy": "使用自定义代理连接互联网", "Continue": "继续", "Contribute to the icon and screenshot repository": "为图标和屏幕截图存储库做出贡献", "Contributors": "贡献者", "Copy": "复制", "Copy to clipboard": "复制到剪贴板", "Could not add source": "无法添加源", "Could not add source {source} to {manager}": "无法添加安装源 {source} 至 {manager}", "Could not back up packages to GitHub Gist: ": "无法将软件包备份至 GitHub Gist ：", "Could not create bundle": "无法创建捆绑包", "Could not load announcements - ": "无法载入公告 - ", "Could not load announcements - HTTP status code is $CODE": "无法载入公告 - HTTP 状态码为 $CODE", "Could not remove source": "无法移除源", "Could not remove source {source} from {manager}": "无法从 {manager} 移除安装源 {source}", "Could not remove {source} from {manager}": "无法从 {manager} 移除 {source}", "Create .ps1 script": "创建 .ps1 脚本", "Credentials": "凭据", "Current Version": "当前版本", "Current status: Not logged in": "当前状态：未登录", "Current user": "当前用户", "Custom arguments:": "自定义参数：", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "自定义命令行参数会改变程序的安装、升级或卸载方式，而 UniGetUI 无法控制这种改变。使用自定义命令行可能会损坏软件包。请谨慎操作。", "Custom command-line arguments:": "自定义命令行参数：", "Custom install arguments:": "自定义安装参数：", "Custom uninstall arguments:": "自定义卸载参数：", "Custom update arguments:": "自定义更新参数：", "Customize WingetUI - for hackers and advanced users only": "定制 WingetUI – 仅供黑客与高级用户", "DEBUG BUILD": "调试构建", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "请注意：我们并不为您下载的软件包负责。请自行确保只安装受信任的软件。", "Dark": "深色", "Decline": "拒绝", "Default": "默认", "Default installation options for {0} packages": "{0} 程序包的默认安装选项", "Default preferences - suitable for regular users": "默认首选项 – 适用于普通用户", "Default vcpkg triplet": "默认 vcpkg triplet", "Delete?": "要删除吗 ？", "Dependencies:": "依赖项：", "Descendant": "降序", "Description:": "介绍：", "Desktop shortcut created": "已创建桌面快捷方式", "Details of the report:": "报告详情：", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "开发免费软件并非易事，如果您喜欢此软件，您可以<b>请我喝杯咖啡</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "双击“{discoveryTab}”选项卡中的项目将会直接进行安装（而不只是显示软件包信息）", "Disable new share API (port 7058)": "禁用新的分享 API（端口 7058）", "Disable the 1-minute timeout for package-related operations": "禁用包相关操作的 1 分钟超时", "Disclaimer": "免责声明", "Discover Packages": "发现软件包", "Discover packages": "发现软件包", "Distinguish between\nuppercase and lowercase": "区分大小写", "Distinguish between uppercase and lowercase": "区分大小写", "Do NOT check for updates": "不检查更新", "Do an interactive install for the selected packages": "交互式安装所选软件包", "Do an interactive uninstall for the selected packages": "交互式卸载所选软件包", "Do an interactive update for the selected packages": "交互式更新所选软件包", "Do not automatically install updates when the battery saver is on": "开启省电模式时，请勿自动安装更新", "Do not automatically install updates when the network connection is metered": "网络连接为按流量计费时，请勿自动安装更新", "Do not download new app translations from GitHub automatically": "不自动下载 GitHub 中的新应用翻译", "Do not ignore updates for this package anymore": "不再忽略此软件包的更新", "Do not remove successful operations from the list automatically": "不自动删除列表中的成功操作", "Do not show this dialog again for {0}": "不再为 {0} 显示此对话框", "Do not update package indexes on launch": "启动时不更新软件包的索引", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "您是否允许 UniGetUI 收集和发送匿名使用统计数据 ？其唯一目的是了解和改善用户体验", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "您觉得 WingetUI 对您有帮助吗？或许您可以支持我的工作，这样我便可以继续使 WingetUI 成为一个终极包管理工具。", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "您觉得 WingetUI 对您有帮助吗？您想支持开发人员吗？如果您这样想，您可以 {0}，这将对我大有帮助！", "Do you really want to reset this list? This action cannot be reverted.": "你确实想重置此列表吗 ？此操作无法撤销。", "Do you really want to uninstall the following {0} packages?": "您确定要卸载以下 {0} 个软件包吗？", "Do you really want to uninstall {0} packages?": "您确定要卸载 {0} 个软件包吗？", "Do you really want to uninstall {0}?": "您确定要卸载 {0} 吗？", "Do you want to restart your computer now?": "您想要立刻重启您的计算机吗？", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "想把 WingetUI 翻译成您的语言吗？看看<a style=\"color:{0}\" href=\"{1}\" a>这里</a>了解如何贡献吧！", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "不想捐款吗？别担心，您可以随时与朋友分享 WingetUI。传播关于 WingetUI 的信息。 ", "Donate": "赞助", "Done!": "完成！", "Download failed": "下载失败", "Download installer": "下载安装程序", "Download operations are not affected by this setting": "此设置不影响下载操作", "Download selected installers": "下载选定的安装程序", "Download succeeded": "下载成功", "Download updated language files from GitHub automatically": "自动下载 GitHub 中的更新语言文件", "Downloading": "下载中", "Downloading backup...": "正在下载备份...", "Downloading installer for {package}": "为 {package} 下载安装程序", "Downloading package metadata...": "正在下载软件包元数据……", "Enable Scoop cleanup on launch": "打开程序时清理 Scoop", "Enable WingetUI notifications": "启用 WingetUI 的通知", "Enable an [experimental] improved WinGet troubleshooter": "启用【实验性】改进版 Winget 疑难解答程序。", "Enable and disable package managers, change default install options, etc.": "启用和禁用软件包管理器、更改默认安装选项等。", "Enable background CPU Usage optimizations (see Pull Request #3278)": "启用后台 CPU 使用优化（查看 Pull Request #3278）", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "启用后台 API（用于 WingetUI 小组件与分享，端口 7058）", "Enable it to install packages from {pm}.": "启用它可从 {pm} 安装软件包。", "Enable the automatic WinGet troubleshooter": "启用自动 WinGet 故障排除程序", "Enable the new UniGetUI-Branded UAC Elevator": "启用新的 UniGetUI UAC 提升程序", "Enable the new process input handler (StdIn automated closer)": "启用新的进程输入处理程序（标准输入自动关闭器）", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "只有在您完全了解以下设置的作用及其可能产生的影响时，才启用这些设置。", "Enable {pm}": "启用 {pm}", "Enter proxy URL here": "在此输入代理网址", "Entries that show in RED will be IMPORTED.": "将导入以红色显示的项目。", "Entries that show in YELLOW will be IGNORED.": "将忽略以黄色显示的项目。", "Error": "错误", "Everything is up to date": "所有软件包均已为最新版", "Exact match": "精确匹配", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "将扫描你桌面上现有的快捷方式，请选择你要保留和删除的内容。", "Expand version": "查看版本", "Experimental settings and developer options": "实验性设置和开发者选项", "Export": "导出", "Export log as a file": "导出日志到文件", "Export packages": "导出软件包列表", "Export selected packages to a file": "导出所选软件包到文件", "Export settings to a local file": "导出设置到本地文件", "Export to a file": "导出到文件", "Failed": "失败", "Fetching available backups...": "正在获取可用的备份...", "Fetching latest announcements, please wait...": "正在获取最新公告，请稍候……", "Filters": "筛选器", "Finish": "完成", "Follow system color scheme": "跟随系统颜色方案", "Follow the default options when installing, upgrading or uninstalling this package": "安装、升级或卸载此软件包时，请遵循默认选项", "For security reasons, changing the executable file is disabled by default": "出于安全原因，默认禁用更改可执行文件", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "出于安全原因，自定义命令行参数默认处于禁用状态。可前往 UniGetUI 安全设置更改此设置。", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "出于安全原因，操作前和操作后脚本默认处于禁用状态。可前往 UniGetUI 安全设置更改此设置。", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "使用 ARM 架构的 Winget 版本（仅适用于 ARM64 系统）", "Formerly known as WingetUI": "原名 WingetUI", "Found": "已找到", "Found packages: ": "已找到软件包：", "Found packages: {0}": "已找到软件包：{0}", "Found packages: {0}, not finished yet...": "已找到 {0} 个软件包，仍在查找中……", "General preferences": "通用首选项", "GitHub profile": "GitHub 个人资料", "Global": "全局", "Go to UniGetUI security settings": "前往 UniGetUI 安全设置", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "包含不知名但有用的实用程序和其它有趣软件包的重要存储库。<br>包括：<b>实用工具、命令行程序、通用软件（需要 extras bucket）</b>", "Great! You are on the latest version.": "很好！您有最新版本。", "Grid": "网格", "Help": "帮助", "Help and documentation": "帮助和文档", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "在这里，您可以更改 UniGetUI 针对以下快捷方式的行为。勾选则 UniGetUI 会删除未来升级时创建的快捷方式。取消勾选将保持快捷方式不变", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "嗨，我是 Martí ，WingetUI 的<i>开发者</i>。WingetUI 都是我在空闲时间完成的！", "Hide details": "隐藏详情", "Homepage": "主页", "Hooray! No updates were found.": "好极了！没有待更新的软件！", "How should installations that require administrator privileges be treated?": "该怎么处理需要管理员权限的安装操作？", "How to add packages to a bundle": "如何添加软件包进捆绑包", "I understand": "我明白", "Icons": "图标", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "如果你启用了云备份，它将作为 GitHub Gist 保存到该账户中", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "从捆绑包导入软件包时，忽略自定义的安装前和安装后命令", "Ignore future updates for this package": "忽略此软件包的后续更新", "Ignore packages from {pm} when showing a notification about updates": "在显示更新通知时忽略来自 {pm} 的软件包", "Ignore selected packages": "忽略所选软件包", "Ignore special characters": "忽略特殊字符", "Ignore updates for the selected packages": "忽略所选软件包的更新", "Ignore updates for this package": "忽略此软件包的更新", "Ignored updates": "已忽略的更新", "Ignored version": "已忽略版本", "Import": "导入", "Import packages": "导入软件包", "Import packages from a file": "从文件导入软件包", "Import settings from a local file": "从本地文件导入设置", "In order to add packages to a bundle, you will need to: ": "要添加软件包进捆绑包，您需要：", "Initializing WingetUI...": "正在初始化 WingetUI……", "Install": "安装", "Install Scoop": "安装 <PERSON>oop", "Install and more": "安装及其它", "Install and update preferences": "安装和更新首选项", "Install as administrator": "以管理员身份安装", "Install available updates automatically": "自动安装可用更新", "Install location can't be changed for {0} packages": "{0} 软件包的安装位置无法更改", "Install location:": "安装位置：", "Install options": "安装选项", "Install packages from a file": "从文件安装软件包", "Install prerelease versions of UniGetUI": "安装 UniGetUI 的预发布版本", "Install script": "安装脚本", "Install selected packages": "安装所选软件包", "Install selected packages with administrator privileges": "使用管理员权限安装所选包", "Install selection": "安装所选项", "Install the latest prerelease version": "安装最新的预发布版本", "Install updates automatically": "自动安装更新", "Install {0}": "安装 {0}", "Installation canceled by the user!": "用户已取消安装！", "Installation failed": "安装失败", "Installation options": "安装选项", "Installation scope:": "安装范围：", "Installation succeeded": "安装成功", "Installed Packages": "已安装软件包", "Installed Version": "已安装版本", "Installed packages": "已安装软件包", "Installer SHA256": "安装程序 SHA256 值", "Installer SHA512": "安装程序 SHA512 值", "Installer Type": "安装类型", "Installer URL": "安装程序网址", "Installer not available": "安装程序不可用", "Instance {0} responded, quitting...": " 实例 {0} 已回应，正在退出……", "Instant search": "实时搜索", "Integrity checks can be disabled from the Experimental Settings": "可以在“实验设置”中禁用完整性检查", "Integrity checks skipped": "已跳过完整性检查", "Integrity checks will not be performed during this operation": "此操作期间将不执行完整性检查", "Interactive installation": "交互式安装", "Interactive operation": "交互式操作", "Interactive uninstall": "交互式卸载", "Interactive update": "交互式更新", "Internet connection settings": "互联网连接设置", "Is this package missing the icon?": "此软件包是否缺少图标？", "Is your language missing or incomplete?": "您的语言翻译是否缺失或不完整？ ", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "无法保证所提供的凭据会被安全存储，你最好不要使用银行账户的凭据", "It is recommended to restart UniGetUI after WinGet has been repaired": "建议在 WinGet 完成修复之后重新启动 UniGetUI", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "强烈建议重新安装 UniGetUI 以解决该情况。", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "看来 WinGet 工作不正常。您想尝试修复 WinGet 吗 ？", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "您似乎在以管理员身份运行 WingetUI，但不建议这样做。 您仍然可以使用此程序，但是我们强烈建议不要使用管理员权限运行 WingetUI 。 点击“{showDetails}”可了解原因。", "Language": "语言", "Language, theme and other miscellaneous preferences": "语言、主题和其它首选项", "Last updated:": "最近更新：", "Latest": "最新", "Latest Version": "最新版本", "Latest Version:": "最新版本：", "Latest details...": "最新详情……", "Launching subprocess...": "正在启动子进程……", "Leave empty for default": "选择默认路径请留空", "License": "许可证", "Licenses": "所有许可证", "Light": "浅色", "List": "列表", "Live command-line output": "实时命令行输出", "Live output": "实时输出", "Loading UI components...": "正在加载用户界面组件……", "Loading WingetUI...": "正在加载 WingetUI……", "Loading packages": "正在加载软件包", "Loading packages, please wait...": "正在加载软件包，请稍候……", "Loading...": "正在加载……", "Local": "本地", "Local PC": "本地电脑", "Local backup advanced options": "本地备份高级选项", "Local machine": "本地机器", "Local package backup": "本地软件包备份", "Locating {pm}...": "正在定位 {pm} ……", "Log in": "登录", "Log in failed: ": "登录失败：", "Log in to enable cloud backup": "登录以启用云备份", "Log in with GitHub": "用 GitHub 登录", "Log in with GitHub to enable cloud package backup.": "用 GitHub 登录以启用云软件包备份。", "Log level:": "日志级别：", "Log out": "注销", "Log out failed: ": "登录失败：", "Log out from GitHub": "从 GitHub 注销", "Looking for packages...": "正在搜索软件包……", "Machine | Global": "本机 | 全局", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "错误格式的命令行参数可能会破坏软件包，甚至让恶意行为者获得特权执行权限。因此，默认情况下禁用导入自定义命令行参数。", "Manage": "管理", "Manage UniGetUI settings": "管理 UniGetUI 设置", "Manage WingetUI autostart behaviour from the Settings app": "通过系统设置管理 WingetUI 开机自动启动", "Manage ignored packages": "管理已忽略软件包", "Manage ignored updates": "管理已忽略更新", "Manage shortcuts": "管理快捷方式", "Manage telemetry settings": "管理遥测设置", "Manage {0} sources": "管理 {0} 安装源", "Manifest": "清单", "Manifests": "清单", "Manual scan": "手动扫描", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "微软的官方软件包管理器。拥有知名的、经过验证的众多软件包。<br>包括：<b>通用软件、微软商店应用</b>\n", "Missing dependency": "缺少依赖项", "More": "更多", "More details": "更多详情", "More details about the shared data and how it will be processed": "有关共享数据及其处理方式的更多详细信息", "More info": "更多信息", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "注意：可在 UniGetUI 设置的 WinGet 部分中，禁用此故障排除程序", "Name": "名称", "New": "新建", "New Version": "新版本", "New bundle": "新建捆绑包", "New version": "新版本", "Nice! Backups will be uploaded to a private gist on your account": "很好！备份将上传到您账户上的一个私有代码片段。", "No": "否", "No applicable installer was found for the package {0}": "未找到适用于包 {0} 的安装程序", "No dependencies specified": "未指定依赖项", "No new shortcuts were found during the scan.": "扫描期间未发现新的快捷方式。", "No packages found": "未找到软件包", "No packages found matching the input criteria": "未找到与输入条件匹配的软件包", "No packages have been added yet": "尚未添加任何软件包", "No packages selected": "未选择软件包", "No packages were found": "未找到软件包", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "个人信息不会被收集也不会被发送，且收集的数据都已被匿名化，因此无法通过它们回溯到您。", "No results were found matching the input criteria": "未找到与输入条件匹配的结果", "No sources found": "未找到源", "No sources were found": "未找到源", "No updates are available": "没有可用更新", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS 的软件包管理器。其中包含大量库和其它实用程序，为 JavaScript 世界增添色彩<br>包括：<b>Node JavaScript 库和其他相关实用程序</b>。", "Not available": "无法获取", "Not finding the file you are looking for? Make sure it has been added to path.": "找不到您要找的文件？请确保已将其添加到路径中。", "Not found": "未找到", "Not right now": "不是现在", "Notes:": "注意：", "Notification preferences": "通知首选项", "Notification tray options": "托盘通知设置", "Notification types": "通知类型", "NuPkg (zipped manifest)": "NuPkg（压缩清单）", "OK": "确定", "Ok": "确定", "Open": "打开", "Open GitHub": "打开 GitHub", "Open UniGetUI": "打开 UniGetUI", "Open UniGetUI security settings": "打开 UniGetUI 安全设置", "Open WingetUI": "打开 WingetUI", "Open backup location": "打开备份位置", "Open existing bundle": "打开现有捆绑包", "Open install location": "打开安装位置", "Open the welcome wizard": "打开欢迎向导", "Operation canceled by user": "用户取消了操作", "Operation cancelled": "操作已取消", "Operation history": "操作历史", "Operation in progress": "操作正在进行中", "Operation on queue (position {0})...": "操作正在排队中（位置 {0}）……", "Operation profile:": "操作配置文件：", "Options saved": "选项已保存", "Order by:": "排序", "Other": "其它", "Other settings": "其它设置", "Package": "软件包", "Package Bundles": "软件捆绑包", "Package ID": "软件包 ID", "Package Manager": "软件包管理器", "Package Manager logs": "软件包管理器日志", "Package Managers": "包管理器", "Package Name": "软件包名称", "Package backup": "软件包备份", "Package backup settings": "软件包备份设置", "Package bundle": "软件捆绑包", "Package details": "软件包详情", "Package lists": "软件包列表", "Package management made easy": "让软件包管理更简单", "Package manager": "软件包管理器", "Package manager preferences": "软件包管理器首选项", "Package managers": "软件包管理器", "Package not found": "软件包未找到", "Package operation preferences": "软件包操作首选项", "Package update preferences": "软件包更新首选项", "Package {name} from {manager}": "来自 {manager} 的软件包 {name}", "Package's default": "软件包默认", "Packages": "软件包", "Packages found: {0}": "已找到软件包：{0}", "Partially": "部分", "Password": "密码", "Paste a valid URL to the database": "粘贴有效网址到数据库", "Pause updates for": "暂停更新：", "Perform a backup now": "立刻执行一次备份", "Perform a cloud backup now": "立即执行云备份", "Perform a local backup now": "立即执行本地备份", "Perform integrity checks at startup": "启动时执行完整性检查", "Performing backup, please wait...": "正在备份中，请稍候...", "Periodically perform a backup of the installed packages": "定期对已安装软件包进行备份 ", "Periodically perform a cloud backup of the installed packages": "定期对已安装的软件包执行云备份", "Periodically perform a local backup of the installed packages": "定期对已安装的软件包执行本地备份", "Please check the installation options for this package and try again": "请检查此软件包的安装选项，然后重试", "Please click on \"Continue\" to continue": "请点击“继续”执行后续操作", "Please enter at least 3 characters": "请至少输入 3 个字符", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "请注意，由于系统中已启用了软件包管理器，某些软件包可能无法安装。", "Please note that not all package managers may fully support this feature": "请注意，并非所有的软件包管理器都能完全支持此功能", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "请注意，来自某些来源的包可能无法导出。它们已标记为灰色且无法导出。", "Please run UniGetUI as a regular user and try again.": "请以普通用户身份运行 UniGetUI ，然后重试。", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "有关此问题的更多信息，请查看命令行输出或参考操作历史记录。", "Please select how you want to configure WingetUI": "请选择您想要如何配置 WingetUI", "Please try again later": "请稍后再试", "Please type at least two characters": "请至少输入两个字符", "Please wait": "请稍候", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "在安装 {0} 时请等待。可能会显示一个黑色窗口。请等待它自己关闭。", "Please wait...": "请稍候……", "Portable": "可移植", "Portable mode": "便携模式", "Post-install command:": "安装后命令：", "Post-uninstall command:": "卸载后命令：", "Post-update command:": "更新后命令：", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell 的软件包管理器。可用于寻找扩展 PowerShell 功能的库和脚本。<br>包括：<b>模块、脚本、Cmdlets</b>\n", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "安装前和安装后命令这样的设计，可能会对你的设备造成非常严重的破坏。从软件包中导入这些命令可能非常危险，除非你信任该软件包的来源。", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "安装前和安装后命令将在软件包安装、升级或卸载之前和之后运行。请谨慎使用，这些命令可能会导致问题。", "Pre-install command:": "安装前命令：", "Pre-uninstall command:": "卸载前命令：", "Pre-update command:": "更新前命令：", "PreRelease": "预发布", "Preparing packages, please wait...": "正在准备软件包，请稍候……", "Proceed at your own risk.": "继续进行，风险自负。", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "禁止通过 UniGetUI Elevator 或 GSudo 进行任何形式的提权操作", "Proxy URL": "代理网址", "Proxy compatibility table": "代理兼容表", "Proxy settings": "代理设置", "Proxy settings, etc.": "代理设置等", "Publication date:": "发布日期：", "Publisher": "发布者", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python 的软件包管理器。包含所有 Python 的库以及其它与 Python 相关的实用工具。<br>包括：<b>Python 包和相关实用工具</b>", "Quit": "退出", "Quit WingetUI": "退出 WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "减少用户账户控制（UAC）提示，默认提升安装权限，解锁某些危险功能等。", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "参考 UniGetUI 日志可获取有关受影响文件的更多详细信息。", "Reinstall": "重新安装", "Reinstall package": "重新安装软件包", "Related settings": "相关设置", "Release notes": "发布说明", "Release notes URL": "发布说明网址", "Release notes URL:": "发布说明网址：", "Release notes:": "发布说明：", "Reload": "重载", "Reload log": "重载日志", "Removal failed": "删除失败", "Removal succeeded": "删除成功", "Remove from list": "从列表中删除", "Remove permanent data": "清除常驻的永久数据", "Remove selection from bundle": "移除捆绑包中所选项", "Remove successful installs/uninstalls/updates from the installation list": "移除安装列表中成功的安装 / 卸载 / 更新项目", "Removing source {source}": "正在移除源 {source}", "Removing source {source} from {manager}": "正在从 {manager} 中移除安装源 {source}", "Repair UniGetUI": "修复UniGetUI", "Repair WinGet": "修复 WinGet", "Report an issue or submit a feature request": "报告问题或者提交功能请求", "Repository": "存储库", "Reset": "重置", "Reset Scoop's global app cache": "重置 Scoop 的全局应用缓存", "Reset UniGetUI": "重置 UniGetUI", "Reset WinGet": "重置 WinGet", "Reset Winget sources (might help if no packages are listed)": "重置 Winget 安装源（如未列出任何软件包时可尝试）", "Reset WingetUI": "重置 WingetUI", "Reset WingetUI and its preferences": "重置 WingetUI 及其首选项", "Reset WingetUI icon and screenshot cache": "重置 WingetUI 图标和屏幕截图缓存", "Reset list": "重置列表", "Resetting Winget sources - WingetUI": "正在重置 Winget 安装源 - WingetUI", "Restart": "重新启动", "Restart UniGetUI": "重启 UniGetUI", "Restart WingetUI": "重启 WingetUI", "Restart WingetUI to fully apply changes": "重启 WingetUI 以应用所有更改", "Restart later": "稍后重启", "Restart now": "立刻重启", "Restart required": "需要重启", "Restart your PC to finish installation": "重启您的电脑以便完成安装", "Restart your computer to finish the installation": "重启您的电脑以便完成安装\n", "Restore a backup from the cloud": "从云端还原备份", "Restrictions on package managers": "对包管理器的限制", "Restrictions on package operations": "对包操作的限制", "Restrictions when importing package bundles": "导入软件捆绑包时的限制", "Retry": "重试", "Retry as administrator": "以管理员身份重试", "Retry failed operations": "重试失败的操作", "Retry interactively": "交互式重试", "Retry skipping integrity checks": "尝试跳过完整性检查", "Retrying, please wait...": "正在重试，请稍候……", "Return to top": "回到顶部", "Run": "运行", "Run as admin": "以管理员身份运行", "Run cleanup and clear cache": "运行清理并清除缓存", "Run last": "最后运行", "Run next": "下个运行", "Run now": "立即运行", "Running the installer...": "正在运行安装程序……", "Running the uninstaller...": "正在运行卸载程序……", "Running the updater...": "正在运行更新程序……", "Save": "保存", "Save File": "保存文件", "Save and close": "保存并关闭", "Save as": "另存为", "Save bundle as": "另存为捆绑包\n\n", "Save now": "立刻保存", "Saving packages, please wait...": "正在保存软件包，请稍候……", "Scoop Installer - WingetUI": "Scoop 安装程序 - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop 卸载程序 - WingetUI", "Scoop package": "Scoop 软件包", "Search": "搜索", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "搜索桌面端软件，有可用更新时通知我，可别做其它傻事。我可不想 WingetUI 太复杂了，我只想要一个简单的<b>软件商店</b>。", "Search for packages": "搜索软件包", "Search for packages to start": "从搜索软件包开始", "Search mode": "搜索模式", "Search on available updates": "搜索可用更新", "Search on your software": "搜索已安装软件", "Searching for installed packages...": "正在搜索已安装软件包……", "Searching for packages...": "正在搜索软件包……", "Searching for updates...": "正在搜索更新……", "Select": "选择", "Select \"{item}\" to add your custom bucket": "选择“{item}”添加到您的自定义存储区", "Select a folder": "选择文件夹", "Select all": "全选", "Select all packages": "全选软件包", "Select backup": "选择备份", "Select only <b>if you know what you are doing</b>.": "请<b>明白您在做什么</b>再选择。", "Select package file": "选择软件包文件", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "选择你要打开的备份。稍后你将能看到要安装哪些软件包。", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "选择在安装、更新或卸载此软件包之前应关闭的进程。", "Select the source you want to add:": "请选择您想添加的安装源：", "Select upgradable packages by default": "默认选择可升级软件包", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "选择想用（{0}）的<b>包管理器</b>、配置软件包的安装方式、管理管理员权限的处理方式等等。", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "已发送握手消息。正在等待实例监听程序的应答……（{0}%）", "Set a custom backup file name": "设置一个自定义备份文件名", "Set custom backup file name": "设置自定义备份文件名", "Settings": "设置", "Share": "分享", "Share WingetUI": "分享 WingetUI", "Share anonymous usage data": "共享匿名使用数据", "Share this package": "分享此软件包", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "如果您修改了安全设置，则需要再次打开该软件包，以使更改生效。", "Show UniGetUI on the system tray": "在系统托盘中显示 UniGetUI", "Show UniGetUI's version and build number on the titlebar.": "在标题栏上显示 UniGetUI 的版本和构建编号", "Show WingetUI": "显示 WingetUI", "Show a notification when an installation fails": "安装失败时推送通知", "Show a notification when an installation finishes successfully": "安装成功时推送通知", "Show a notification when an operation fails": "当操作失败时显示通知", "Show a notification when an operation finishes successfully": "当操作成功完成时显示通知", "Show a notification when there are available updates": "有可用更新时推送通知", "Show a silent notification when an operation is running": "当操作正在运行时显示一个静默通知", "Show details": "显示详情", "Show in explorer": "在浏览器中显示", "Show info about the package on the Updates tab": "在“更新”标签页上显示软件包信息", "Show missing translation strings": "显示未翻译的字符串", "Show notifications on different events": "显示各种事件的通知", "Show package details": "显示软件包详情", "Show package icons on package lists": "在软件包列表中显示软件包图标", "Show similar packages": "显示相似软件包", "Show the live output": "显示实时输出", "Size": "尺寸", "Skip": "跳过", "Skip hash check": "跳过哈希校验", "Skip hash checks": "跳过哈希检验", "Skip integrity checks": "跳过完整性检查", "Skip minor updates for this package": "忽略此软件包的小幅更新", "Skip the hash check when installing the selected packages": "安装所选包时跳过哈希校验", "Skip the hash check when updating the selected packages": "更新所选包时跳过哈希校验", "Skip this version": "跳过此版本", "Software Updates": "软件更新", "Something went wrong": "出现了一些问题", "Something went wrong while launching the updater.": "在启动更新程序是出错。", "Source": "来源", "Source URL:": "安装源网址：", "Source added successfully": "已成功添加源", "Source addition failed": "添加源失败", "Source name:": "安装源名称：", "Source removal failed": "移除源失败", "Source removed successfully": "已成功移除源", "Source:": "来源：", "Sources": "来源", "Start": "开始", "Starting daemons...": "正在启动守护程序……", "Starting operation...": "开始操作...", "Startup options": "启动选项", "Status": "状态", "Stuck here? Skip initialization": "在这里卡住了？跳过初始化", "Success!": "成功 ！", "Suport the developer": "支持开发者", "Support me": "支持我", "Support the developer": "支持开发者", "Systems are now ready to go!": "系统已准备就绪！", "Telemetry": "遥测", "Text": "文本", "Text file": "文本文件", "Thank you ❤": "谢谢 ❤", "Thank you 😉": "谢谢 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust 的软件包管理器。<br>包含：<b>Rust 库和用 Rust 编写的程序</b>", "The backup will NOT include any binary file nor any program's saved data.": "备份将不包含任何二进制文件或任何程序的已保存数据。", "The backup will be performed after login.": "备份将在登录后执行", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "备份将包含已安装软件包及其安装选项的完整列表。已忽略更新和跳过版本也将被保存。", "The bundle was created successfully on {0}": "已成功于 {0} 创建了捆绑包", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "您尝试载入的捆绑包似乎无效。请检查此文件然后再试。", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "安装程序的校验和与预期不一致，无法验证安装程序的真实性。如果您信任发布者，再次{0}软件包将会跳过哈希校验。", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Windows 的经典软件包管理器。您可以在其中找到所有需要的东西。<br>包括：<b>通用软件</b>", "The cloud backup completed successfully.": "云备份已成功完成。", "The cloud backup has been loaded successfully.": "已成功加载云备份。", "The current bundle has no packages. Add some packages to get started": "当前捆绑包中还没有软件包。先添加一些软件包吧", "The executable file for {0} was not found": "找不到 {0} 的可执行文件", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "每次安装、升级或卸载{0}软件包时，将默认应用以下选项。", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "以下软件包将会导出到 JSON 文件中。不会保存任何用户数据或二进制文件。", "The following packages are going to be installed on your system.": "以下软件包会安装在您的系统上。", "The following settings may pose a security risk, hence they are disabled by default.": "以下设置可能存在安全风险，因此默认处于禁用状态。", "The following settings will be applied each time this package is installed, updated or removed.": "每次安装、更新或移除此软件包时都会应用以下设置。", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "每次安装、更新或删除此软件包时都会应用以下设置。 它们将会自动保存。", "The icons and screenshots are maintained by users like you!": "这些图标和屏幕截图都是由和您一样的用户维护的！", "The installation script saved to {0}": "安装脚本已保存至 {0}", "The installer authenticity could not be verified.": "无法验证安装程序的真实性。", "The installer has an invalid checksum": "安装程序的校验和无效", "The installer hash does not match the expected value.": "安装程序的哈希值与预期值不匹配", "The local icon cache currently takes {0} MB": "本地图标缓存当前占用了 {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "本项目的主要目标是为 Windows 最常见的命令行包管理器（例如 Winget 和 Scoop）创建直观的用户界面。", "The package \"{0}\" was not found on the package manager \"{1}\"": "在软件包管理器 \"{1}\" 中找不到软件包 \"{0}\"", "The package bundle could not be created due to an error.": "无法创建软件捆绑包，发生了错误。", "The package bundle is not valid": "软件捆绑包无效", "The package manager \"{0}\" is disabled": "软件管理器 \"{0}\" 已禁用", "The package manager \"{0}\" was not found": "软件管理器 \"{0}\" 找不到", "The package {0} from {1} was not found.": "未找到来自 {1} 的软件包 {0}", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "检查更新时，此处列出的软件包将会被忽略。双击它们或点击它们右侧的按钮可不再忽略其更新。", "The selected packages have been blacklisted": "选定的软件包已被列入黑名单", "The settings will list, in their descriptions, the potential security issues they may have.": "这些设置将在其描述中列出可能存在的潜在安全问题。", "The size of the backup is estimated to be less than 1MB.": "备份的大小预计小于 1MB 。", "The source {source} was added to {manager} successfully": "安装源 {source} 已成功添加到 {manager}", "The source {source} was removed from {manager} successfully": "安装源 {source} 已成功从 {manager} 中移除", "The system tray icon must be enabled in order for notifications to work": "必须启用系统托盘图标才能让通知生效", "The update process has been aborted.": "更新过程已中止。", "The update process will start after closing UniGetUI": "更新过程将在 UniGetUI 关闭后开始", "The update will be installed upon closing WingetUI": "在关闭 WingetUI 后将安装更新", "The update will not continue.": "更新将不会继续。", "The user has canceled {0}, that was a requirement for {1} to be run": "用户已取消 {0}，但这是运行 {1} 的必要条件", "There are no new UniGetUI versions to be installed": "没有可安装的新 UniGetUI 版本", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "有正在进行的操作。退出 WingetUI 可能会导致它们失败。您确定要继续吗 ？", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "YouTube 上有一些很棒的视频展示了 WingetUI 及其功能。您可以学到有用的技巧和窍门！", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "不建议以管理员身份运行 WingetUI 有两个重要原因：\n首先， Scoop 包管理器在管理员权限下运行一些命令可能会出现问题。\n其次，以管理员身份运行 WingetUI 意味着您下载的任何软件将会以管理员身份运行（这很不安全）。\n提示：如果您需要以管理员身份安装一个特定软件，您可以右键点击此项目 -> 以管理员身份安装 / 更新 / 卸载。", "There is an error with the configuration of the package manager \"{0}\"": "软件包管理器 \"{0}\" 的配置中有错误", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "有安装正在进行中。如果您关闭 WingetUI，安装可能会失败并产生意外结果。是否仍要退出 WingetUI ？", "They are the programs in charge of installing, updating and removing packages.": "它们是负责安装、更新和移除软件包的程序。", "Third-party licenses": "第三方许可证", "This could represent a <b>security risk</b>.": "这可能存在<b>安全风险</b>。", "This is not recommended.": "不建议。", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "这可能是因为您收到的包已被移除，或者发布在您尚未启用的软件包管理器上。得到的 ID 是 {0}", "This is the <b>default choice</b>.": "这是<b>默认选项</b>", "This may help if WinGet packages are not shown": "不显示 WinGet 软件包时此选项可能有帮助", "This may help if no packages are listed": "未列出任何软件包时此选项可能有帮助", "This may take a minute or two": "这可能需要一两分钟的时间", "This operation is running interactively.": "此操作正处于交互式运行。", "This operation is running with administrator privileges.": "此操作正以管理员权限运行。", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "此选项会导致一些问题。任何无法自行提升权限的操作都将失败。以管理员身份进行安装/更新/卸载将不起作用。", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "此软件包有一些设置可能存在潜在危险，默认情况下可能会被忽略。", "This package can be updated": "此软件包可以更新", "This package can be updated to version {0}": "此软件包可以更新到版本 {0}", "This package can be upgraded to version {0}": "此软件包可以升级到版本 {0}", "This package cannot be installed from an elevated context.": "无法在提升的上下文中安装此软件包。", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "此软件包缺少截图或图标吗？可以向我们的开放公共数据库添加缺失的图标或截图，为 WingetUI 做出贡献。", "This package is already installed": "此软件包已安装", "This package is being processed": "正在处理此软件包", "This package is not available": "此软件包不存在", "This package is on the queue": "此软件包正在排队", "This process is running with administrator privileges": "此进程正以管理员权限运行", "This project has no connection with the official {0} project — it's completely unofficial.": "此项目与官方的 {0} 项目没有关联 —— 它完全是非官方的。", "This setting is disabled": "此设置已禁用", "This wizard will help you configure and customize WingetUI!": "此向导将帮助您配置和自定义 WingetUI ！", "Toggle search filters pane": "切换搜索筛选器窗格", "Translators": "翻译人员", "Try to kill the processes that refuse to close when requested to": "尝试终止那些在收到关闭请求时拒绝关闭的进程", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "启用此功能后，可更改用于与软件包管理器交互的可执行文件。虽然这能让您对安装过程进行更细致的自定义，但也可能存在风险。 ", "Type here the name and the URL of the source you want to add, separed by a space.": "请输入您想添加源的名称和网址，以空格分割。", "Unable to find package": "无法找到软件包", "Unable to load informarion": "无法加载信息", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI 收集匿名使用数据，以改善用户体验。", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI 收集匿名使用数据的唯一目的是了解和改善用户体验。", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI 检测到可以自动删除的新桌面快捷方式。", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI 检测到以下桌面快捷方式，这些快捷方式会在未来升级时自动删除", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI 已检测到有 {0} 个新的桌面快捷方式可被自动删除。", "UniGetUI is being updated...": "正在更新 UniGetUI ...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI 与任何兼容的软件包管理器均无关。UniGetUI 是一个独立项目。", "UniGetUI on the background and system tray": "UniGetUI 位于后台和系统托盘", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI 或其某些组件缺失或已损坏。", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI 需要 {0} 才能运行，但在您的系统中找不到它。", "UniGetUI startup page:": "UniGetUI 启动页面：", "UniGetUI updater": "UniGetUI 更新程序", "UniGetUI version {0} is being downloaded.": "正在下载 UniGetUI 版本 {0}", "UniGetUI {0} is ready to be installed.": "已准备安装 UniGetUI {0}", "Uninstall": "卸载", "Uninstall Scoop (and its packages)": "卸载 Scoop（及其软件包）", "Uninstall and more": "卸载及其它", "Uninstall and remove data": "卸载并移除数据", "Uninstall as administrator": "以管理员身份卸载", "Uninstall canceled by the user!": "用户取消卸载！", "Uninstall failed": "卸载失败", "Uninstall options": "卸载选项", "Uninstall package": "卸载软件包", "Uninstall package, then reinstall it": "卸载并重新安装软件包", "Uninstall package, then update it": "卸载并更新软件包", "Uninstall previous versions when updated": "更新时卸载旧版本", "Uninstall selected packages": "卸载所选软件包", "Uninstall selection": "卸载所选项", "Uninstall succeeded": "卸载成功", "Uninstall the selected packages with administrator privileges": "使用管理员权限卸载所选包", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "原先列为“{0}”的可卸载软件包未在任何包管理器中发布，因此无法显示它们的信息。", "Unknown": "未知", "Unknown size": "未知大小", "Unset or unknown": "未设置或未知", "Up to date": "已是最新", "Update": "更新", "Update WingetUI automatically": "自动更新 WingetUI", "Update all": "全部更新", "Update and more": "更新及其它", "Update as administrator": "以管理员身份更新", "Update check frequency, automatically install updates, etc.": "更新检查频率、自动安装更新等", "Update checking": null, "Update date": "更新日期", "Update failed": "更新失败", "Update found!": "发现更新！", "Update now": "立刻更新", "Update options": "更新选项", "Update package indexes on launch": "启动时更新软件包索引", "Update packages automatically": "自动更新软件包", "Update selected packages": "更新所选软件包", "Update selected packages with administrator privileges": "使用管理员权限更新所选包", "Update selection": "更新所选项", "Update succeeded": "更新成功", "Update to version {0}": "更新至版本 {0}", "Update to {0} available": "可更新至 {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "自动更新 vcpkg 的 Git 配置文件（需要已安装 Git）", "Updates": "更新", "Updates available!": "有可用更新！", "Updates for this package are ignored": "已忽略该软件包的更新", "Updates found!": "发现更新！", "Updates preferences": "更新首选项", "Updating WingetUI": "正在更新 WingetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "使用旧版内置的 WinGet 而非 PowerShell CMDLets", "Use a custom icon and screenshot database URL": "使用自定义图标和截图数据库网址", "Use bundled WinGet instead of PowerShell CMDlets": "使用内置的 WinGet 而非 PowerShell CMDLets", "Use bundled WinGet instead of system WinGet": "使用内置 WinGet 而不是系统 WinGet", "Use installed GSudo instead of UniGetUI Elevator": "使用已安装的 GSudo ，而不是UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "使用已安装的 GSudo 而非内置的 GSudo", "Use system Chocolatey": "使用系统 Chocolatey", "Use system Chocolatey (Needs a restart)": "使用系统 Chocolatey（需要重启）", "Use system Winget (Needs a restart)": "使用系统 Winget（需要重启）", "Use system Winget (System language must be set to english)": "使用系统 Winget（系统语言必须设置为英文）", "Use the WinGet COM API to fetch packages": "使用 WinGet COM API 获取软件包", "Use the WinGet PowerShell Module instead of the WinGet COM API": "使用 WinGet PowerShell 模块而不是 WinGet COM API", "Useful links": "帮助链接", "User": "用户", "User interface preferences": "用户界面首选项", "User | Local": "用户 | 本地", "Username": "用户名", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "使用 WingetUI 意味着接受 GNU 宽通用公共许可证（LGPL v2.1）", "Using WingetUI implies the acceptation of the MIT License": "使用 WingetUI 意味着接受 MIT 许可证 ", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "找不到 Vcpkg 根。请定义 %CVPKG_ROOT% 环境变量或者在 UniGetUI 设置中定义 Vcpkg 根", "Vcpkg was not found on your system.": "您系统中未找到 Vcpkg", "Verbose": "详情", "Version": "版本", "Version to install:": "安装版本：", "Version:": "版本：", "View GitHub Profile": "查看 GitHub 个人资料", "View WingetUI on GitHub": "查看 GitHub 上的 WingetUI", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "查看 WingetUI 的源代码。您可以在那里报告缺陷或建议功能，甚至直接为 WingetUI 项目做出贡献", "View mode:": "视图模式", "View on UniGetUI": "在 UniGetUI 中查看", "View page on browser": "在浏览器中查看页面", "View {0} logs": "查看 {0} 日志", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "在尝试执行需要互联网连接的任务之前，请等待设备连接到互联网。", "Waiting for other installations to finish...": "正在等待其它安装完成……", "Waiting for {0} to complete...": "等待 {0} 完成...", "Warning": "警告", "Warning!": "警告！", "We are checking for updates.": "正在检查更新", "We could not load detailed information about this package, because it was not found in any of your package sources": "我们无法加载此软件包的详细信息，因为在您的任何软件包安装源中都找不到它", "We could not load detailed information about this package, because it was not installed from an available package manager.": "我们无法加载此软件包的详细信息，因为它未从任何可用软件包管理器中安装。", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "我们无法{action} {package}，请稍后再试。点击“{showDetails}”可获取安装程序的日志。", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "我们无法{action} {package}，请稍后再试。点击“{showDetails}”可获取卸载程序的日志。", "We couldn't find any package": "我们找不到任何软件包", "Welcome to WingetUI": "欢迎使用 WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": "从捆绑包中批量安装软件包时，也安装已安装的软件包", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "当检测到新的快捷方式时，自动删除它们，而不是显示此对话框。", "Which backup do you want to open?": "你想要打开哪个备份？", "Which package managers do you want to use?": "您想使用哪个包管理器？", "Which source do you want to add?": "您想要添加哪个源？", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "WingetUI 原本只设计为与 Winget 配合使用，但现在已支持其它软件包管理器，继续使用 WingetUI 的名称可能会让人感到困惑。由于 WingetUI 的发展已经超出了最初的定位，该名称不再准确地反映此项目的目标。", "WinGet could not be repaired": "无法修复 WinGet", "WinGet malfunction detected": "检测到 WinGet 发生故障", "WinGet was repaired successfully": "已成功修复 WinGet", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - 全部都是最新的", "WingetUI - {0} updates are available": "WingetUI - {0} 个可用更新", "WingetUI - {0} {1}": "WingetUI - {0}{1}", "WingetUI Homepage": "WingetUI 主页", "WingetUI Homepage - Share this link!": "WingetUI 主页 - 分享此链接！", "WingetUI License": "WingetUI 许可证", "WingetUI Log": "WingetUI 日志", "WingetUI Repository": "WingetUI 存储库", "WingetUI Settings": "WingetUI 设置", "WingetUI Settings File": "WingetUI 设置文件", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI 使用以下库。如果没有它们，就没有 WingetUI。", "WingetUI Version {0}": "WingetUI 版本 {0}", "WingetUI autostart behaviour, application launch settings": "WingetUI 开机自动启动行为、应用程序启动设置", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI 可以检查您的软件是否有可用更新，并且可按您的意愿自动安装它们", "WingetUI display language:": "WingetUI 显示语言：", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI 已使用管理员身份运行，但不建议这样做。当以管理员身份运行 WingetUI 时，WingetUI 执行的所有操作都将具有管理员权限。您仍然可以使用此程序，但我们强烈建议您不要用管理员权限运行 WingetUI 。", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI 已经由志愿翻译人员翻译成了 40 多种语言，感谢他们的辛勤工作！🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI 不是机翻的！以下用户承担了翻译工作：", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI 应用程序为您基于命令行的软件包管理器提供了一体化图形界面，使得管理软件变得更容易。", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "重新命名 WingetUI 是为了强调 WingetUI（您正在使用的界面）和 Winget（由微软开发的与我们无关的包管理器）之间的区别\n", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI 正在更新中。完成后 WingetUI 将自动重启", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI 是免费的并将永远免费。无广告，无需信用卡，无高级版本。永远 100% 免费。 ", "WingetUI log": "WingetUI 日志", "WingetUI tray application preferences": "WingetUI 托盘应用首选项", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI 使用以下库。如果没有它们，WingetUI 将不可能实现。", "WingetUI version {0} is being downloaded.": "正在下载 WingetUI 版本 {0} 。", "WingetUI will become {newname} soon!": "WingetUI 不久将更名为 {newname} ！", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI 将不会定期检查更新。在启动时仍将检查更新，但不会有提示。", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "每当某个软件包需要提升权限才能安装时，WingetUI 都将会显示 UAC 提示。", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI 不久将会重命名为 {newname} 。这并不代表该应用会发生任何变动。我（开发者）将继续项目的开发工作，只是使用不同的名称。 \n", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI 的诞生离不开我们亲爱的贡献者的帮助。请查看他们的 GitHub 个人页面，没有他们就无法成就 WingetUI！", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "如果没有众多贡献者的帮助，WingetUI 是不可能实现的。感谢大家 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} 已准备好安装。 ", "Write here the process names here, separated by commas (,)": "在此处写入进程名称，以英文逗号 (,) 分隔", "Yes": "是", "You are logged in as {0} (@{1})": "你以 {0}（@{1}）的身份登录", "You can change this behavior on UniGetUI security settings.": "你可以在 UniGetUI 安全设置中更改此行为。", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "你可以定义在安装、更新或卸载此软件包之前或之后运行的命令。这些命令将在命令提示符下运行，所以可在此处使用 CMD 脚本。", "You have currently version {0} installed": "您当前已安装版本 {0}", "You have installed WingetUI Version {0}": "您已安装 WingetUI 版本 {0} ", "You may lose unsaved data": "你可能会丢失未保存的数据", "You may need to install {pm} in order to use it with WingetUI.": "您可能需要安装 {pm} 才能将其与 WingetUI 一起使用。 ", "You may restart your computer later if you wish": "如果需要，您可以稍后重启您的电脑", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "系统只会提示您一次，且仅向要求权限的软件包授予管理员权限。", "You will be prompted only once, and every future installation will be elevated automatically.": "系统只会提示您一次，以后的每次安装都会自动提升权限。", "You will likely need to interact with the installer.": "您可能需要与安装程序交互。", "[RAN AS ADMINISTRATOR]": "以管理员身份运行", "buy me a coffee": "赞助我一杯熬夜用的咖啡", "extracted": "已提取", "feature": "功能", "formerly WingetUI": "原 WingetUI", "homepage": "主页", "install": "安装", "installation": "安装", "installed": "已安装", "installing": "正在安装", "library": "库", "mandatory": "强制", "option": "选项", "optional": "可选", "uninstall": "卸载", "uninstallation": "卸载", "uninstalled": "已卸载", "uninstalling": "正在卸载", "update(noun)": "更新", "update(verb)": "更新", "updated": "已更新", "updating": "正在更新", "version {0}": "版本 {0}", "{0} Install options are currently locked because {0} follows the default install options.": "已锁定 {0} 安装选项，因为 {0} 遵循默认安装选项。", "{0} Uninstallation": "{0} 卸载", "{0} aborted": "{0} 已中止", "{0} can be updated": "{0} 个可更新", "{0} can be updated to version {1}": "{0} 可更新至版本 {1}", "{0} days": "{0} 天", "{0} desktop shortcuts created": "已创建 {0} 个桌面快捷方式", "{0} failed": "{0} 失败", "{0} has been installed successfully.": "已成功安装 {0} 。", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "已成功安装 {0} 。建议重启 UniGetUI 以便完成此次安装", "{0} has failed, that was a requirement for {1} to be run": "{0} 失败了，但这是运行 {1} 的必要条件", "{0} homepage": "{0} 主页", "{0} hours": "{0} 小时", "{0} installation": "{0} 安装", "{0} installation options": "{0} 安装选项", "{0} installer is being downloaded": "正在下载 {0} 安装程序", "{0} is being installed": "正在安装 {0}", "{0} is being uninstalled": "正在卸载 {0}", "{0} is being updated": "正在更新 {0}", "{0} is being updated to version {1}": "正在更新 {0} 至版本 {1}", "{0} is disabled": "已禁用 {0}", "{0} minutes": "{0} 分钟", "{0} months": "{0} 月", "{0} packages are being updated": "正在更新 {0} 个软件包", "{0} packages can be updated": "可以更新 {0} 个软件包", "{0} packages found": "找到 {0} 个软件包", "{0} packages were found": "已找到 {0} 个软件包", "{0} packages were found, {1} of which match the specified filters.": "已找到 {0} 个软件包，其中 {1} 个与指定的筛选器匹配。", "{0} selected": "{0} 选中", "{0} settings": "{0} 设置", "{0} status": "{0} 状态", "{0} succeeded": "{0} 成功", "{0} update": "{0} 更新", "{0} updates are available": "{0} 个可用更新", "{0} was {1} successfully!": "{0} {1}成功", "{0} weeks": "{0} 周", "{0} years": "{0} 年", "{0} {1} failed": "{0} {1}失败", "{package} Installation": "{package} 安装", "{package} Uninstall": "{package} 卸载", "{package} Update": "{package} 更新", "{package} could not be installed": "无法安装 {package}", "{package} could not be uninstalled": "无法卸载 {package}", "{package} could not be updated": "无法更新 {package}", "{package} installation failed": "{package} 安装失败", "{package} installer could not be downloaded": "无法下载 {package} 安装程序", "{package} installer download": "{package} 安装程序下载", "{package} installer was downloaded successfully": "已成功下载 {package} 安装程序", "{package} uninstall failed": "{package} 卸载失败", "{package} update failed": "{package} 更新失败", "{package} update failed. Click here for more details.": "{package} 更新失败。请点击此处查看详情。", "{package} was installed successfully": "{package} 已成功安装", "{package} was uninstalled successfully": "{package} 已成功卸载", "{package} was updated successfully": "{package} 已成功更新", "{pcName} installed packages": "{pcName} 已安装软件包", "{pm} could not be found": "找不到 {pm}", "{pm} found: {state}": "已找到 {pm}：{state}", "{pm} is disabled": "{pm} 已禁用", "{pm} is enabled and ready to go": "{pm} 已启用且准备就绪", "{pm} package manager specific preferences": "{pm} 包管理特定首选项", "{pm} preferences": "{pm} 首选项", "{pm} version:": "{pm} 版本：", "{pm} was not found!": "找不到 {pm} ！"}