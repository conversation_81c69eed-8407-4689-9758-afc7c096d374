<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.GeneralPages.General"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Toolkit="using:CommunityToolkit.WinUI.Controls"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages.GeneralPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    mc:Ignorable="d">

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel Orientation="Vertical" Spacing="0">

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Language" />

            <widgets:ComboboxCard
                x:Name="LanguageSelector"
                CornerRadius="8"
                SettingName="PreferredLanguage"
                Text="WingetUI display language:"
                ValueChanged="ShowRestartBanner">
                <Toolkit:SettingsCard.Description>
                    <StackPanel Orientation="Horizontal" Spacing="4">
                        <widgets:TranslatedTextBlock
                            VerticalAlignment="Center"
                            Opacity=".8"
                            Text="Is your language missing or incomplete?" />
                        <HyperlinkButton Padding="0" NavigateUri="https://github.com/marticliment/WingetUI/wiki#translating-wingetui">
                            <widgets:TranslatedTextBlock Text="Become a translator" />
                        </HyperlinkButton>
                    </StackPanel>
                </Toolkit:SettingsCard.Description>
            </widgets:ComboboxCard>
            <!--
                widgets:ButtonCard
                Text="Open the welcome wizard"
                ButtonText="Open"
                Click="OpenWelcomeWizard"
                IsEnabled="False"
                /
            -->

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="UniGetUI updater" />

            <widgets:CheckboxButtonCard
                ButtonAlwaysOn="true"
                ButtonText="Check for updates"
                CheckboxText="Update WingetUI automatically"
                Click="ForceUpdateUniGetUI_OnClick"
                CornerRadius="8,8,0,0"
                SettingName="DisableAutoUpdateWingetUI" />
            <widgets:CheckboxCard
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,8,8"
                SettingName="EnableUniGetUIBeta"
                Text="Install prerelease versions of UniGetUI" />

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Telemetry" />

            <widgets:ButtonCard
                ButtonText="Manage"
                Click="ManageTelemetrySettings_Click"
                CornerRadius="8"
                Text="Manage telemetry settings" />

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Manage UniGetUI settings" />

            <widgets:ButtonCard
                ButtonText="Import"
                Click="ImportSettings_Click"
                CornerRadius="8,8,0,0"
                Text="Import settings from a local file" />
            <widgets:ButtonCard
                BorderThickness="1,0"
                ButtonText="Export"
                Click="ExportSettings_Click"
                CornerRadius="0"
                Text="Export settings to a local file" />
            <widgets:ButtonCard
                ButtonText="Reset UniGetUI"
                Click="ResetWingetUI"
                CornerRadius="0,0,8,8"
                Text="Reset WingetUI" />

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Related settings" />

            <controls:SettingsCard
                Click="InterfaceSettingsButton_Click"
                CornerRadius="8"
                IsClickEnabled="True">
                <controls:SettingsCard.Header>
                    <widgets:TranslatedTextBlock Text="User interface preferences" />
                </controls:SettingsCard.Header>
                <controls:SettingsCard.Description>
                    <widgets:TranslatedTextBlock Text="Application theme, startup page, package icons, clear successful installs automatically" />
                </controls:SettingsCard.Description>
                <controls:SettingsCard.HeaderIcon>
                    <widgets:LocalIcon Icon="Interactive" />
                </controls:SettingsCard.HeaderIcon>
            </controls:SettingsCard>

        </StackPanel>
    </ScrollViewer>
</Page>
