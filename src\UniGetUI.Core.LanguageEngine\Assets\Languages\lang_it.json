{"\"{0}\" is a local package and can't be shared": "\"{0}\" è un pacchetto locale e non può essere condiviso", "\"{0}\" is a local package and does not have available details": "\"{0}\" è un pacchetto locale e non ha dettagli disponibili", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" è un pacchetto locale e non è compatibile con questa funzionalità", "(Last checked: {0})": "(<PERSON><PERSON><PERSON> controllo: {0})", "(Number {0} in the queue)": "(Ancora {0} in coda)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON>, @giacobot, @maicol07, @mapi68, @m<PERSON><PERSON><PERSON>, Rosario <PERSON>", "0 packages found": "0 pacchetti trovati", "0 updates found": "0 aggiornamenti trovati", "1 - Errors": "1 - E<PERSON><PERSON>", "1 day": "1 giorno", "1 hour": "1 ora", "1 month": "1 mese", "1 package was found": "È stato trovato 1 pacchetto", "1 update is available": "È disponibile 1 aggiornamento", "1 week": "1 settimana", "1 year": "1 anno", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Vai alla pagina \"{0}\" o \"{1}\".", "2 - Warnings": "2 - Avvertimenti", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Individua i pacchetti che desideri aggiungere alla raccolta e seleziona la casella di controllo più a sinistra.", "3 - Information (less)": "3 - Informazioni (meno)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON> aver selezionato i pacchetti che vuoi aggiungere alla raccolta, trova e fai clic sull'opzione \"{0}\" sulla barra degli strumenti.", "4 - Information (more)": "4 - Informazioni (più)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. I tuoi pacchetti saranno stati aggiunti alla raccolta. Puoi continuare ad aggiungere pacchetti o esportare la raccolta.", "5 - information (debug)": "5 - informazioni (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Un popolare gestore di librerie C/C++. Pieno di librerie C/C++ e altre utilità correlate a C/C++<br>Contiene: <b>librerie C/C++ e utilità correlate</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Un repository pieno di strumenti ed eseguibili progettati pensando all'ecosistema .NET di Microsoft.<br>Contiene: <b>strumenti e script relativi a .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Un repository pieno di strumenti progettati pensando all'ecosistema .NET di Microsoft.<br>Contiene: <b>strumenti correlati a .NET</b>", "A restart is required": "È richiesto il riavvio", "Abort install if pre-install command fails": "Interrompi l'installazione se il comando di pre-installazione fallisce", "Abort uninstall if pre-uninstall command fails": "Interrompi la disinstallazione se il comando di pre-disinstallazione fallisce", "Abort update if pre-update command fails": "Interrompere l'aggiornamento se il comando di pre-aggiornamento fallisce", "About": "Informazioni su", "About Qt6": "Informazioni su Qt6", "About WingetUI": "Informazioni su WingetUI", "About WingetUI version {0}": "Informazioni su WingetUI versione {0}", "About the dev": "Informazioni sullo sviluppatore", "Accept": "Accetta", "Action when double-clicking packages, hide successful installations": "Azione quando si fa doppio clic sui pacchetti, nascondi le installazioni riuscite", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "Aggiungi una sorgente a {0}", "Add a timestamp to the backup file names": "Aggiungi data e ora ai nomi dei file di backup", "Add a timestamp to the backup files": "Aggiungi data e ora ai file di backup", "Add packages or open an existing bundle": "Aggiungi pacchetti o apri una raccolta esistente", "Add packages or open an existing package bundle": "Aggiungi pacchetti o apri una raccolta di pacchetti esistente", "Add packages to bundle": "Aggiungi pacchetti alla raccolta", "Add packages to start": "Aggiungi pacchetti per iniziare", "Add selection to bundle": "Aggiungi selezione alla raccolta", "Add source": "Aggiungi sorgente", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Aggiungi gli aggiornamenti che non riescono con un \"nessun aggiornamento applicabile trovato\" all'elenco degli aggiornamenti ignorati", "Adding source {source}": "Aggiungi sorgente {source}\n", "Adding source {source} to {manager}": "Aggiunta sorgente {source} a {manager}", "Addition succeeded": "Aggiunta riuscita", "Administrator privileges": "Privilegi di amministratore", "Administrator privileges preferences": "Preferenze dei privilegi di amministratore", "Administrator rights": "Diritti di amministratore", "Administrator rights and other dangerous settings": "Diritti di amministratore e altre impostazioni pericolose", "Advanced options": "Opzioni avanzate", "All files": "Tutti i file", "All versions": "<PERSON>tte le versioni", "Allow changing the paths for package manager executables": "Consenti la modifica dei percorsi per gli eseguibili del gestore pacchetti", "Allow custom command-line arguments": "Consenti argomenti personalizzati della riga di comando", "Allow importing custom command-line arguments when importing packages from a bundle": "Consenti l'importazione di argomenti personalizzati dalla riga di comando durante l'importazione di pacchetti da una raccolta", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Consenti l'importazione di comandi pre-installazione e post-installazione personalizzati durante l'importazione di pacchetti da una raccolta", "Allow package operations to be performed in parallel": "Consenti esecuzione parallela delle operazioni sui pacchetti", "Allow parallel installs (NOT RECOMMENDED)": "Consenti installazioni parallele (NON CONSIGLIATO)", "Allow pre-release versions": "Consenti le versioni di sviluppo", "Allow {pm} operations to be performed in parallel": "Consenti esecuzione in parallelo delle operazioni di {pm}", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "In alternativa, puoi anche installare {0} eseguendo il seguente comando in un prompt di Windows PowerShell:", "Always elevate {pm} installations by default": "Eleva sempe le installazioni di {pm} per impostazione predefinita", "Always run {pm} operations with administrator rights": "Esegui sempre le operazioni su {pm} con diritti di amministratore", "An error occurred": "Si è verificato un errore", "An error occurred when adding the source: ": "Si è verificato un errore durante l'aggiunta della sorgente:", "An error occurred when attempting to show the package with Id {0}": "Si è verificato un errore durante il tentativo di mostrare il pacchetto con ID {0}", "An error occurred when checking for updates: ": "Si è verificato un errore durante il controllo degli aggiornamenti:", "An error occurred while loading a backup: ": "Si è verificato un errore durante il caricamento di un backup:", "An error occurred while logging in: ": "Si è verificato un errore durante l'accesso:", "An error occurred while processing this package": "Si è verificato un errore durante l'elaborazione di questo pacchetto", "An error occurred:": "Si è verificato un errore:", "An interal error occurred. Please view the log for further details.": "Si è verificato un errore interno. Visualizza il log per ulteriori dettagli.", "An unexpected error occurred:": "Si è verificato un errore imprevisto:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Si è verificato un problema imprevisto durante il tentativo di riparare WinGet. Riprova più tardi", "An update was found!": "È stato trovato un aggiornamento!", "Android Subsystem": "Sottosistema Android", "Another source": "Un'altra sorgente", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Tutti i nuovi collegamenti creati durante un'installazione o un'operazione di aggiornamento verranno eliminati automaticamente, anziché visualizzare una richiesta di conferma la prima volta che vengono rilevati.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Tutte i collegamenti creati o modificati al di fuori di UniGetUI saranno ignorati. Potrai aggiungerli tramite il pulsante {0}.", "Any unsaved changes will be lost": "Tutte le modifiche non salvate andranno perse", "App Name": "Nome dell'app", "Appearance": "Aspetto", "Application theme, startup page, package icons, clear successful installs automatically": "Tema dell'applicazione, pagina di avvio, icone del pacchetto, cancella automaticamente le installazioni riuscite", "Application theme:": "Tema applicazione:", "Apply": "Applica", "Architecture to install:": "Architettura da installare:", "Are these screenshots wron or blurry?": "<PERSON><PERSON> screenshot sono sbagliati o sfocati?", "Are you really sure you want to enable this feature?": "Sei davvero sicuro di voler abilitare questa funzione?", "Are you sure you want to create a new package bundle? ": "Sei sicuro di voler creare una nuova raccolta di pacchetti?", "Are you sure you want to delete all shortcuts?": "Vuoi davvero eliminare tutti i collegamenti?", "Are you sure?": "Sei sicuro?", "Ascendant": "Ascendente", "Ask for administrator privileges once for each batch of operations": "Richiedi una sola volta i privilegi di amministratore per ogni operazione in serie", "Ask for administrator rights when required": "<PERSON>di i diritti di amministratore quando richiesto", "Ask once or always for administrator rights, elevate installations by default": "Richie<PERSON> una volta o sempre i diritti di amministratore, eleva le installazioni per impostazione predefinita", "Ask only once for administrator privileges": "<PERSON><PERSON> solo una volta i privilegi di amministratore", "Ask only once for administrator privileges (not recommended)": "Richiedi una sola volta i privilegi di amministratore (non consigliato)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Richiedi di eliminare i collegamenti sul desktop creati durante un'installazione o un aggiornamento.", "Attention required": "È richiesta attenzione", "Authenticate to the proxy with an user and a password": "Autenticazione al proxy con utente e password", "Author": "Autore", "Automatic desktop shortcut remover": "Rimozione automatica dei collegamenti dal desktop\n", "Automatically save a list of all your installed packages to easily restore them.": "Salva automaticamente un elenco di tutti i pacchetti installati per ripristinarli facilmente.", "Automatically save a list of your installed packages on your computer.": "Salva automaticamente l'elenco dei pacchetti installati sul computer.", "Autostart WingetUI in the notifications area": "Avvia automaticamente WingetUI nell'area delle notifiche", "Available Updates": "Aggiornamenti disponibili", "Available updates: {0}": "Aggiornamenti disponibili: {0}", "Available updates: {0}, not finished yet...": "Aggiornamenti disponibili: {0}, ma non ho ancora terminato il controllo...", "Backing up packages to GitHub Gist...": "Backup dei pacchetti su GitHub Gist...", "Backup": "Backup", "Backup Failed": "Backup non riuscito", "Backup Successful": "Backup r<PERSON><PERSON><PERSON>", "Backup and Restore": "Backup e ripristino", "Backup installed packages": "Backup dei pacchetti installati", "Backup location": "Posizione del backup", "Become a contributor": "Diventa un collaboratore", "Become a translator": "Diventa un traduttore", "Begin the process to select a cloud backup and review which packages to restore": "Inizia il processo per selezionare un backup dal cloud e rivedere quali pacchetti ripristinare", "Beta features and other options that shouldn't be touched": "Funzionalità beta e altre opzioni che non dovrebbero essere toccate", "Both": "Entrambi", "Bundle security report": "Rapporto sulla sicurezza delle raccolte", "But here are other things you can do to learn about WingetUI even more:": "Ma ecco altre cose che puoi fare per conoscere ancora di più WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Disatti<PERSON>do un gestore pacchetti, non sarai più in grado di vedere o aggiornare i suoi pacchetti.", "Cache administrator rights and elevate installers by default": "Memorizza i diritti di amministratore ed eleva i programmi di installazione per impostazione predefinita", "Cache administrator rights, but elevate installers only when required": "Memorizza i diritti di amministratore, ma eleva i programmi di installazione solo quando richiesto", "Cache was reset successfully!": "La cache è stata reimpostata correttamente!", "Can't {0} {1}": "Impossibile {0} {1}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "<PERSON><PERSON><PERSON> tutte le operazioni", "Change backup output directory": "Modifica cartella di destinazione del backup", "Change default options": "Modifica le opzioni predefinite", "Change how UniGetUI checks and installs available updates for your packages": "Modifica il modo in cui UniGetUI controlla e installa gli aggiornamenti disponibili per i tuoi pacchetti", "Change how UniGetUI handles install, update and uninstall operations.": "Modifica il modo in cui UniGetUI gestisce le operazioni di installazione, aggiornamento e disinstallazione.", "Change how UniGetUI installs packages, and checks and installs available updates": "Modifica il modo in cui UniGetUI installa i pacchetti e controlla e installa gli aggiornamenti disponibili", "Change how operations request administrator rights": "Modifica il modo in cui le operazioni richiedono i diritti di amministratore", "Change install location": "Modifica il percorso di installazione", "Change this": "Modifica questo", "Change this and unlock": "Modifica questo e sblocca", "Check for package updates periodically": "Controlla periodicamente gli aggiornamenti dei pacchetti", "Check for updates": "Controlla aggiornamenti", "Check for updates every:": "Controlla aggiornamenti ogni:", "Check for updates periodically": "Controlla aggiornamenti periodicamente", "Check for updates regularly, and ask me what to do when updates are found.": "Controlla regolarmente gli aggiornamenti e chiedimi cosa fare quando viene rilevato un nuovo aggiornamento.", "Check for updates regularly, and automatically install available ones.": "Controlla regolarmente gli aggiornamenti e installa automaticamente quelli disponibili.", "Check out my {0} and my {1}!": "Dai un'occhiata al mio {0} e al mio {1}!", "Check out some WingetUI overviews": "Dai un'occhiata ad alcune panoramiche di WingetUI", "Checking for other running instances...": "Controllo di altre istanze in esecuzione...", "Checking for updates...": "Controllo aggiornamenti...", "Checking found instace(s)...": "<PERSON><PERSON> istanze trovate...", "Choose how many operations shouls be performed in parallel": "Scegli quante operazioni devono essere eseguite in parallelo", "Clear cache": "Svuota cache", "Clear finished operations": "Cancella le operazioni terminate", "Clear selection": "Cancella selezione", "Clear successful operations": "Cancella le operazioni riuscite", "Clear successful operations from the operation list after a 5 second delay": "Cancella le operazioni riuscite dall'elenco delle operazioni dopo un ritardo di 5 secondi", "Clear the local icon cache": "Svuota cache delle icone locali", "Clearing Scoop cache - WingetUI": "Svuotamento cache di Scoop: WingetUI", "Clearing Scoop cache...": "Svuotamento cache di Scoop...", "Click here for more details": "Fai clic qui per maggiori dettagli", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Fai clic su Installa per avviare il processo di installazione. Se salti l'installazione, UniGetUI potrebbe non funzionare come previsto.", "Close": "<PERSON><PERSON>", "Close UniGetUI to the system tray": "Chiudi UniGetUI nella barra delle applicazioni", "Close WingetUI to the notification area": "Chiudi WingetUI nell'area di notifica", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Il backup sul cloud utilizza un GitHub Gist privato per archiviare un elenco dei pacchetti installati", "Cloud package backup": "Backup dei pacchetti sul cloud", "Command-line Output": "Output della riga di comando", "Command-line to run:": "Riga di comando per eseguire:", "Compare query against": "Confronta la query con", "Compatible with authentication": "Compatibile con autenticazione", "Compatible with proxy": "Compatibile con proxy", "Component Information": "Informazioni sui componenti", "Concurrency and execution": "Concorrenza ed esecuzione", "Connect the internet using a custom proxy": "Connettiti a Internet utilizzando un proxy personalizzato", "Continue": "Continua", "Contribute to the icon and screenshot repository": "Contribuisci al repository di icone e screenshot", "Contributors": "Collaboratori", "Copy": "Copia", "Copy to clipboard": "Copia negli appunti", "Could not add source": "Impossibile aggiungere la sorgente", "Could not add source {source} to {manager}": "Impossibile aggiungere la sorgente {source} a {manager}", "Could not back up packages to GitHub Gist: ": "Impossibile eseguire il backup dei pacchetti su GitHub Gist:", "Could not create bundle": "Impossibile creare la raccolta", "Could not load announcements - ": "Impossibile caricare gli annunci:", "Could not load announcements - HTTP status code is $CODE": "Impossibile caricare gli annunci: il codice di stato HTTP è $CODE", "Could not remove source": "Impossibile rimuovere la sorgente", "Could not remove source {source} from {manager}": "Impossibile rimuovere la sorgente {source} da {manager}", "Could not remove {source} from {manager}": "Impossibile rimuovere {source} da {manager}", "Credentials": "Credenziali", "Current Version": "Versione attuale", "Current status: Not logged in": "Stato attuale: Non connesso", "Current user": "Utente attuale", "Custom arguments:": "Argomenti personalizzati:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Gli argomenti personalizzati della riga di comando possono modificare il modo in cui i programmi vengono installati, aggiornati o disinstallati, in un modo che UniGetUI non può controllare. L'utilizzo di righe di comando personalizzate può danneggiare i pacchetti. Procedi con cautela.", "Custom command-line arguments:": "Argomenti a linea di comando personalizzati:", "Custom install arguments:": "Argomenti di installazione personalizzati:", "Custom uninstall arguments:": "Argomenti di disinstallazione personalizzati:", "Custom update arguments:": "Argomenti di aggiornamento personalizzati:", "Customize WingetUI - for hackers and advanced users only": "Impostazioni avanzate (solo per utenti esperti e hacker)", "DEBUG BUILD": "DEBUG COMPILAZIONE", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ESCLUSIONE DI RESPONSABILITÀ: NON SIAMO RESPONSABILI DEI PACCHETTI SCARICATI. ASSICURATI DI INSTALLARE SOLO SOFTWARE AFFIDABILI.", "Dark": "<PERSON><PERSON>", "Decline": "<PERSON><PERSON><PERSON><PERSON>", "Default": "Predefinito", "Default installation options for {0} packages": "Opzioni di installazione predefinite per i pacchetti {0}", "Default preferences - suitable for regular users": "Impostazioni predefinite (adatte per la maggior parte degli utenti)", "Default vcpkg triplet": "Triplet predefinito di vcpkg", "Delete?": "Eliminare?", "Dependencies:": "Dipendenze:", "Descendant": "<PERSON><PERSON><PERSON>", "Description:": "Descrizione:", "Desktop shortcut created": "Collegamento sul desktop creato", "Details of the report:": "Dettagli del rapporto:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Sviluppare è difficile e questa applicazione è gratuita. Ma se ti è piaciuta, puoi sempre <b>offrirmi un caffè</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installa con doppio clic su un elemento nella scheda \"{discoveryTab}\" (invece di mostrare le informazioni sul pacchetto)", "Disable new share API (port 7058)": "Disabilita la nuova API di condivisione (porta 7058)", "Disable the 1-minute timeout for package-related operations": "Disabilita il timeout di 1 minuto per le operazioni relative al pacchetto", "Disclaimer": "Esclusione di responsabilità", "Discover Packages": "Ricerca pacchetti", "Discover packages": "Ricerca pacchetti", "Distinguish between\nuppercase and lowercase": "Distingui tra\nmaiuscole e minuscole", "Distinguish between uppercase and lowercase": "Distingui tra\nmaiuscole e minuscole", "Do NOT check for updates": "NON controllare aggiornamenti", "Do an interactive install for the selected packages": "Esegui un'installazione interattiva per i pacchetti selezionati", "Do an interactive uninstall for the selected packages": "Esegui una disinstallazione interattiva per i pacchetti selezionati", "Do an interactive update for the selected packages": "Esegui un aggiornamento interattivo per i pacchetti selezionati", "Do not automatically install updates when the battery saver is on": "Non installare automaticamente gli aggiornamenti quando il risparmio batteria è attivo", "Do not automatically install updates when the network connection is metered": "Non installare automaticamente gli aggiornamenti quando la connessione di rete è a consumo", "Do not download new app translations from GitHub automatically": "Non scaricare automaticamente nuove traduzioni dell'app da Github", "Do not ignore updates for this package anymore": "Non ignorare più gli aggiornamenti per questo pacchetto", "Do not remove successful operations from the list automatically": "Non rimuovere automaticamente le operazioni riuscite dall'elenco", "Do not show this dialog again for {0}": "Non mostrare più questa finestra di dialogo per {0}", "Do not update package indexes on launch": "Non aggiornare gli indici dei pacchetti all'avvio", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Accetti che UniGetUI raccolga e invii statistiche anonime sull'utilizzo, con l'unico scopo di comprendere e migliorare l'esperienza dell'utente?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Trovi utile WingetUI? Se hai la possibilità, potresti supportare il mio lavoro per permettermi di continuare a rendere WingetUI l'interfaccia definitiva per la gestione dei pacchetti.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Trovi utile WingetUI? Vorresti supportare lo sviluppatore? Se è così, potresti {0}, per me sarebbe un grande aiuto!", "Do you really want to reset this list? This action cannot be reverted.": "Vuoi davvero reimpostare questo elenco? Questa azione non può essere annullata.", "Do you really want to uninstall the following {0} packages?": "Vuoi davvero disinstallare i seguenti {0} p<PERSON><PERSON><PERSON>?", "Do you really want to uninstall {0} packages?": "Vuoi veramente disinstallare {0} pac<PERSON><PERSON>?", "Do you really want to uninstall {0}?": "Vuoi veramente disinstallare {0}?", "Do you want to restart your computer now?": "Vuoi riavviare il computer adesso?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Vuoi tradurre WingetUI nella tua lingua? Scopri come essere parte attiva del progetto <a style=\"color:{0}\" href=\"{1}\"a>QUI</a>!", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Non ti senti di fare una donazione? Non preoccuparti, puoi sempre condividere WingetUI con i tuoi amici. Spargi la voce su WingetUI.", "Donate": "Donazione", "Done!": "Fatto!", "Download failed": "Scaricamento non riuscito", "Download installer": "Scarica programma di installazione", "Download operations are not affected by this setting": "Le operazioni di scaricamento non sono influenzate da questa impostazione", "Download selected installers": "Scarica programmi di installazione selezionati", "Download succeeded": "Scaricamento riuscito", "Download updated language files from GitHub automatically": "Scarica automaticamente da GitHub le traduzioni aggiornate", "Downloading": "Scaricamento", "Downloading backup...": "Scaricamento del backup...", "Downloading installer for {package}": "Scaricamento del programma di installazione per {package}", "Downloading package metadata...": "Scaricamento dei metadati del pacchetto in corso...", "Enable Scoop cleanup on launch": "Abilita pulizia di Scoop all'avvio", "Enable WingetUI notifications": "Abilita notifiche di WingetUI", "Enable an [experimental] improved WinGet troubleshooter": "Abilita uno strumento di risoluzione dei problemi WinGet migliorato [sperimentale]", "Enable and disable package managers, change default install options, etc.": "Abilita e disabilita i gestori di pacchetti, modifica le opzioni di installazione predefinite e altro", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Abilita le ottimizzazioni dell'utilizzo della CPU in background (vedi Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Abilita API in background (widget e condivisione WingetUI, porta 7058)", "Enable it to install packages from {pm}.": "Abilita per installare pacchetti da {pm}.", "Enable the automatic WinGet troubleshooter": "Abilita risoluzione automatica dei problemi di WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Abilita il nuovo elevatore UAC con marchio UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Abilita il nuovo gestore di input del processo (chiusura automatica StdIn)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Abilita le impostazioni sottostanti solo se hai compreso completamente a cosa servono e quali implicazioni potrebbero avere.", "Enable {pm}": "Abilita {pm}", "Enter proxy URL here": "Inserisci qui l'URL del proxy", "Entries that show in RED will be IMPORTED.": "Le voci visualizzate in ROSSO verranno IMPORTATE.", "Entries that show in YELLOW will be IGNORED.": "Le voci visualizzate in GIALLO verranno IGNORATE.", "Error": "Errore", "Everything is up to date": "<PERSON><PERSON> a<PERSON>", "Exact match": "Corrispondenza esatta", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Verranno analizzati i collegamenti presenti sul desktop e sarà necessario scegliere quali mantenere e quali rimuovere.", "Expand version": "Espandi versione", "Experimental settings and developer options": "Impostazioni sperimentali e opzioni sviluppatore", "Export": "Esporta", "Export log as a file": "Esporta log in un file", "Export packages": "Esporta <PERSON>", "Export selected packages to a file": "Esporta pacchetti selezionati in un file", "Export settings to a local file": "Esporta  impostazioni in un file locale", "Export to a file": "Esporta in un file", "Failed": "Non riuscito", "Fetching available backups...": "Recupero dei backup disponibili...", "Fetching latest announcements, please wait...": "Recupero degli ultimi annunci, attendi...", "Filters": "<PERSON><PERSON><PERSON>", "Finish": "Fine", "Follow system color scheme": "Segui schema dei colori di sistema", "Follow the default options when installing, upgrading or uninstalling this package": "Segui le opzioni predefinite durante l'installazione, l'aggiornamento o la disinstallazione di questo pacchetto", "For security reasons, changing the executable file is disabled by default": "Per motivi di sicurezza, la modifica del file eseguibile è disabilitata per impostazione predefinita", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Per motivi di sicurezza, gli argomenti personalizzati della riga di comando sono disabilitati per impostazione predefinita. Per modificare questa impostazione, vai alle impostazioni di sicurezza di UniGetUI.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Per motivi di sicurezza, gli script pre-operazione e post-operazione sono disabilitati per impostazione predefinita. Per modificare questa impostazione, vai alle impostazioni di sicurezza di UniGetUI.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Usa la versione di Winget compilata per ARM (SOLO PER SISTEMI ARM64)", "Formerly known as WingetUI": "Precedentemente noto come WingetUI", "Found": "<PERSON><PERSON><PERSON>", "Found packages: ": "<PERSON><PERSON><PERSON> trovati:", "Found packages: {0}": "<PERSON><PERSON><PERSON> trovati: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON> trovati: {0}, non ho ancora terminato...", "General preferences": "Preferenze generali", "GitHub profile": "pro<PERSON><PERSON>", "Global": "Globale", "Go to UniGetUI security settings": "Vai alle impostazioni di sicurezza di UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Ottimo repository di strumenti sconosciuti ma utili e altri pacchetti interessanti.<br>Contiene: <b>strumenti, programmi a riga di comando, software generici (è necessario un bucket extra)</b>", "Great! You are on the latest version.": "Ottimo! Sei all'ultima versione.", "Grid": "Griglia", "Help": "<PERSON><PERSON>", "Help and documentation": "Aiuto e documentazione", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Qui puoi modificare il comportamento di UniGetUI relativamente ai collegamenti seguenti. Selezionando un collegamento UniGetUI lo eliminerà se verrà creato in un futuro aggiornamento. Deselezionandolo, il collegamento rimarrà intatto", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "C<PERSON><PERSON>, mi chiamo Martí e sono lo <i>sviluppatore</i> di WingetUI. WingetUI è stato realizzato interamente nel mio tempo libero!", "Hide details": "Nascondi dettagli", "Homepage": "Homepage", "Hooray! No updates were found.": "Evviva! Non ci sono aggiornamenti!", "How should installations that require administrator privileges be treated?": "Come dovrebbero essere trattate le installazioni che richiedono privilegi amministrativi?", "How to add packages to a bundle": "Come aggiungere pacchetti a una raccolta", "I understand": "Capisco", "Icons": "Icone", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Se hai abilitato il backup sul cloud, verrà salvato come GitHub Gist su questo account", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignora i comandi pre-installazione e post-installazione personalizzati quando importi pacchetti da una raccolta", "Ignore future updates for this package": "Ignora aggiornamenti futuri per questo pacchetto", "Ignore packages from {pm} when showing a notification about updates": "Ignora i pacchetti da {pm} quando viene mostrata una notifica sugli aggiornamenti", "Ignore selected packages": "Ignora pacchetti selezionati", "Ignore special characters": "Ignora caratteri speciali", "Ignore updates for the selected packages": "Ignora aggiornamenti per i pacchetti selezionati", "Ignore updates for this package": "Ignora aggiornamenti per questo pacchetto", "Ignored updates": "Aggiornamenti ignorati", "Ignored version": "Versione ignorata", "Import": "Importa", "Import packages": "<PERSON><PERSON><PERSON>", "Import packages from a file": "<PERSON><PERSON>rta pacchetti da un file", "Import settings from a local file": "Importa impostazioni da un file locale", "In order to add packages to a bundle, you will need to: ": "Per aggiungere pacchetti a una raccolta, è necessario:", "Initializing WingetUI...": "Inizializzazione di WingetUI...", "Install": "Installa", "Install Scoop": "<PERSON><PERSON><PERSON>", "Install and more": "Installazioni e altro", "Install and update preferences": "Installa e aggiorna le preferenze", "Install as administrator": "Installa come amministratore", "Install available updates automatically": "Installa automaticamente gli aggiornamenti disponibili", "Install location can't be changed for {0} packages": "Il percorso di installazione non può essere modificato per i pacchetti {0}", "Install location:": "Percorso di installazione:", "Install options": "Opzioni di installazione", "Install packages from a file": "Installa pacchetti da un file", "Install prerelease versions of UniGetUI": "Installa le versioni di sviluppo di UniGetUI", "Install selected packages": "Installa pacchetti selezionati", "Install selected packages with administrator privileges": "Installa pacchetti selezionati con privilegi di amministratore", "Install selection": "Installa selezione", "Install the latest prerelease version": "Installa ultima versione di sviluppo", "Install updates automatically": "Installa automaticamente gli aggiornamenti", "Install {0}": "Installa {0}", "Installation canceled by the user!": "Installazione annullata dall'utente!", "Installation failed": "Installazione non riuscita", "Installation options": "Opzioni installazione", "Installation scope:": "Modalità di installazione:", "Installation succeeded": "Installazione riuscita", "Installed Packages": "<PERSON><PERSON><PERSON>", "Installed Version": "Versione installata", "Installed packages": "<PERSON><PERSON><PERSON>", "Installer SHA256": "SHA512 installer", "Installer SHA512": "SHA512 installer", "Installer Type": "Tipo installer", "Installer URL": "URL installer", "Installer not available": "Programma di installazione non disponibile", "Instance {0} responded, quitting...": "L'istanza {0} ha risposto, in chiusura...", "Instant search": "Ricerca istantanea", "Integrity checks can be disabled from the Experimental Settings": "I controlli di integrità possono essere disabilitati dalle Impostazioni sperimentali", "Integrity checks skipped": "Controlli di integrità saltati", "Integrity checks will not be performed during this operation": "Durante questa operazione non verranno eseguiti controlli di integrità", "Interactive installation": "Installazione interattiva", "Interactive operation": "Operazione interattiva", "Interactive uninstall": "Disinstallazione interattiva", "Interactive update": "Aggiornamento interattivo", "Internet connection settings": "Impostazioni connessione Internet", "Is this package missing the icon?": "Manca l'icona di questo pacchetto?", "Is your language missing or incomplete?": "La tua lingua manca o è incompleta?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Non è garantito che le credenziali fornite verranno conservate in modo sicuro, quindi potresti anche non utilizzare le credenziali del tuo conto bancario", "It is recommended to restart UniGetUI after WinGet has been repaired": "Si consiglia di riavviare UniGetUI dopo che WinGet è stato riparato", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Si consiglia vivamente di reinstallare UniGetUI per risolvere il problema.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Se<PERSON>ra che WinGet non funzioni correttamente. Vuoi provare a riparare WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Sembra che tu abbia eseguito WingetUI come amministratore, il che non è consigliato. Puoi comunque utilizzare il programma, ma ti consigliamo vivamente di non eseguire WingetUI con privilegi di amministratore. Fai clic su \"{showDetails}\" per scoprire il motivo.", "Language": "<PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "Lingua, tema e altre preferenze", "Last updated:": "Ultimo <PERSON>rnamento:", "Latest": "<PERSON><PERSON> recente", "Latest Version": "Versione più recente", "Latest Version:": "Versione più recente:", "Latest details...": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>...", "Launching subprocess...": "Avvio del sottoprocesso...", "Leave empty for default": "Lascia vuoto per impostazione predefinita", "License": "Licenza", "Licenses": "Licenze", "Light": "Chiaro", "List": "Elenco", "Live command-line output": "Output a riga di comando in tempo reale", "Live output": "Output in tempo reale", "Loading UI components...": "Caricamento componenti UI...", "Loading WingetUI...": "Caricamento WingetUI...", "Loading packages": "Caricamento p<PERSON>ti", "Loading packages, please wait...": "Caricamento dei pacchetti, attendi...", "Loading...": "Caricamento...", "Local": "Locale", "Local PC": "PC locale", "Local backup advanced options": "Opzioni avanzate del backup locale", "Local machine": "Macchina locale", "Local package backup": "Backup locale del pacchetto", "Locating {pm}...": "Individuazione di {pm}...", "Log in": "Accesso", "Log in failed: ": "Accesso non riuscito:", "Log in to enable cloud backup": "Accedi per abilitare il backup su cloud", "Log in with GitHub": "Accedi con GitHub", "Log in with GitHub to enable cloud package backup.": "Accedi con GitHub per abilitare il backup dei pacchetti sul cloud.", "Log level:": "Livello di log:", "Log out": "Disconnessione", "Log out failed: ": "Disconnessione non riuscita:", "Log out from GitHub": "Disconnessione da GitHub", "Looking for packages...": "Alla ricerca di pacchetti...", "Machine | Global": "Macchina | Globale", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Argomenti della riga di comando non validi possono danneggiare i pacchetti o persino consentire a un malintenzionato di ottenere privilegi di esecuzione. Pertanto, l'importazione di argomenti della riga di comando personalizzati è disabilitata per impostazione predefinita.", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "Gestisci le impostazioni di UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Gestisci il comportamento di avvio automatico di WingetUI dall'app Impostazioni", "Manage ignored packages": "Gestisci pacchetti ignorati", "Manage ignored updates": "Gestisci aggiornamenti ignorati", "Manage shortcuts": "<PERSON><PERSON><PERSON><PERSON> colle<PERSON>nti", "Manage telemetry settings": "Gestisci le impostazioni di telemetria", "Manage {0} sources": "<PERSON><PERSON><PERSON><PERSON> {0} sorgenti", "Manifest": "Manifesto", "Manifests": "<PERSON><PERSON><PERSON><PERSON>", "Manual scan": "Scansione manuale", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Il gestore pacchetti ufficiale di Microsoft. Pieno di pacchetti noti e verificati.<br>Contiene: <b>software generico, app dal Microsoft Store</b>", "Missing dependency": "Dipendenza mancante", "More": "Altro", "More details": "<PERSON><PERSON>", "More details about the shared data and how it will be processed": "Maggiori dettagli sui dati condivisi e su come verranno elaborati", "More info": "<PERSON><PERSON> informazioni", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOTA: questo strumento di risoluzione dei problemi può essere disabilitato dalle impostazioni di UniGetUI, nella sezione WinGet", "Name": "Nome", "New": "Nuovo", "New Version": "Nuova versione", "New bundle": "Nuova raccolta", "New version": "Nuova versione", "Nice! Backups will be uploaded to a private gist on your account": "Perfetto! I backup verranno caricati su un gist privato sul tuo account.", "No": "No", "No applicable installer was found for the package {0}": "Non è stato trovato alcun programma di installazione applicabile per il pacchetto {0}", "No dependencies specified": "Nessuna dipendenza specificata", "No new shortcuts were found during the scan.": "Durante la scansione non sono state trovati nuovi collegamenti.", "No packages found": "<PERSON><PERSON><PERSON> pacchetto trovato", "No packages found matching the input criteria": "<PERSON><PERSON><PERSON> pacchetto trovato con i criteri inseriti", "No packages have been added yet": "<PERSON><PERSON><PERSON> pacchetto è stato ancora aggiunto", "No packages selected": "<PERSON><PERSON><PERSON> pacchetto se<PERSON>o", "No packages were found": "<PERSON><PERSON><PERSON> pacchetto trovato", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Non vengono raccolte né inviate informazioni personali e i dati raccolti vengono resi anonimi, quindi non è possibile risalire alla tua identità.", "No results were found matching the input criteria": "<PERSON><PERSON>un risultato corrispondente ai criteri di ricerca", "No sources found": "Nessuna sorgente trovata", "No sources were found": "Non è stata trovata alcuna sorgente", "No updates are available": "Nessun aggiornamento disponibile", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Il gestore pacchetti di Node JS. Pieno di librerie e altre strumenti che orbitano attorno al mondo javascript.<br>Contiene: <b>librerie javascript Node e altri strumenti correlati</b>", "Not available": "Non disponibile", "Not finding the file you are looking for? Make sure it has been added to path.": "Non trovi il file che stai cercando? Assicurati che sia stato aggiunto al percorso.", "Not found": "Non trovato", "Not right now": "Non adesso", "Notes:": "Note:", "Notification preferences": "Preferenze di notifica", "Notification tray options": "Opzioni della barra delle notifiche", "Notification types": "Tipi di notifica", "NuPkg (zipped manifest)": "NuPkg (manifesto compresso)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON>i", "Open GitHub": "<PERSON><PERSON>", "Open UniGetUI": "Apri UniGetUI", "Open UniGetUI security settings": "Apri le impostazioni di sicurezza di UniGetUI", "Open WingetUI": "<PERSON><PERSON>", "Open backup location": "Apri la posizione di backup", "Open existing bundle": "Apri raccolta esistente", "Open install location": "Apri posizione di installazione", "Open the welcome wizard": "Apri procedura guidata di benvenuto", "Operation canceled by user": "Operazione annullata dall'utente", "Operation cancelled": "Operazione annullata", "Operation history": "Cronologia operazioni", "Operation in progress": "Operazione in corso", "Operation on queue (position {0})...": "Operazione in coda (posizione {0})...", "Operation profile:": "Profilo operativo:", "Options saved": "Opzioni salvate", "Order by:": "Ordina per:", "Other": "Altro", "Other settings": "Altre impostazioni", "Package": "<PERSON><PERSON><PERSON>", "Package Bundles": "<PERSON><PERSON><PERSON><PERSON>", "Package ID": "ID pacchetto", "Package Manager": "<PERSON><PERSON><PERSON>", "Package Manager logs": "Log gestore pacchetti", "Package Managers": "<PERSON><PERSON><PERSON>", "Package Name": "<PERSON><PERSON> p<PERSON>chetto", "Package backup": "Backup pacchetto", "Package backup settings": "Impostazioni di backup del pacchetto", "Package bundle": "Racco<PERSON> p<PERSON>", "Package details": "<PERSON><PERSON><PERSON> p<PERSON>", "Package lists": "<PERSON><PERSON><PERSON>", "Package management made easy": "La gestione dei pacchetti diventa incredibilmente facile", "Package manager": "<PERSON><PERSON><PERSON>", "Package manager preferences": "Preferenze del gestore pacchetti", "Package managers": "<PERSON><PERSON><PERSON>", "Package not found": "Pacchetto non trovato", "Package operation preferences": "Preferenze per le operazioni sui pacchetti", "Package update preferences": "Preferenze di aggiornamento del pacchetto", "Package {name} from {manager}": "<PERSON><PERSON><PERSON> {name} da {manager}", "Package's default": "Predefinito del pacchetto", "Packages": "<PERSON><PERSON><PERSON>", "Packages found: {0}": "<PERSON><PERSON><PERSON> trovati: {0}", "Partially": "Parzialmente", "Password": "Password", "Paste a valid URL to the database": "Incolla un URL valido nel database", "Pause updates for": "Sospendi aggiornamenti per", "Perform a backup now": "Esegui subito un backup", "Perform a cloud backup now": "Esegui subito un backup sul cloud", "Perform a local backup now": "Esegui subito un backup locale", "Perform integrity checks at startup": "Esegui controlli di integrità all'avvio", "Performing backup, please wait...": "Esecuzione del backup, attendi...", "Periodically perform a backup of the installed packages": "Esegui periodicamente un backup dei pacchetti installati", "Periodically perform a cloud backup of the installed packages": "Esegui periodicamente un backup sul cloud dei pacchetti installati", "Periodically perform a local backup of the installed packages": "Esegui periodicamente un backup locale dei pacchetti installati", "Please check the installation options for this package and try again": "Controlla le opzioni di installazione per questo pacchetto e riprova", "Please click on \"Continue\" to continue": "Fai clic su \"Continua\" per continuare", "Please enter at least 3 characters": "Inserisci almeno 3 caratteri", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Tieni presente che alcuni pacchetti potrebbero non essere installabili a causa dei gestori di pacchetti abilitati su questa macchina.", "Please note that not all package managers may fully support this feature": "Tieni presente che non tutti i gestori di pacchetti potrebbero supportare completamente questa funzionalità", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Tieni presente che i pacchetti provenienti da alcune sorgenti potrebbero non essere esportabili. Sono stati disattivati e quindi non verranno esportati.", "Please run UniGetUI as a regular user and try again.": "Esegui UniGetUI come utente normale e riprova.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Per ulteriori informazioni sul problema, consulta l'output della riga di comando o fai riferimento alla cronologia delle operazioni.", "Please select how you want to configure WingetUI": "Seleziona come desideri configurare WingetUI", "Please try again later": "R<PERSON>rova più tardi", "Please type at least two characters": "Digita almeno due caratteri", "Please wait": "<PERSON><PERSON><PERSON>", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "<PERSON>tend<PERSON> mentre {0} viene installato. Potrebbe apparire una finestra nera. Attendi che si chiuda.", "Please wait...": "Attendi...", "Portable": "Portatile", "Portable mode": "Modalità portatile", "Post-install command:": "Comando post-installazione:", "Post-uninstall command:": "Comando post-disinstallazione:", "Post-update command:": "Comando post-aggiornamento:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Il gestore pacchetti di PowerShell. Trova librerie e script per espandere le funzionalità di PowerShell.<br>Contiene: <b>moduli, script, cmdlet</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "I comandi pre e post installazione possono avere effetti molto dannosi sul dispositivo, se progettati per questo scopo. Può essere molto pericoloso importare i comandi da una raccolta, a meno che non ci si fidi della sorgente di quel pacchetto.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "I comandi pre e post installazione verranno eseguiti prima e dopo l'installazione, l'aggiornamento o la disinstallazione di un pacchetto. Si prega di notare che potrebbero causare problemi se non utilizzati con attenzione.", "Pre-install command:": "Comando pre-installazione:", "Pre-uninstall command:": "Comando pre-disinstallazione:", "Pre-update command:": "Comando pre-aggiornamento:", "PreRelease": "<PERSON>e di sviluppo", "Preparing packages, please wait...": "Preparazione dei pacchetti, attendi...", "Proceed at your own risk.": "Procedi a tuo rischio e pericolo.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Vieta qualsiasi tipo di elevazione tramite UniGetUI Elevator o GSudo", "Proxy URL": "URL proxy", "Proxy compatibility table": "Tabella di compatibilità dei proxy", "Proxy settings": "Impostazioni proxy", "Proxy settings, etc.": "Impostazioni proxy e altro", "Publication date:": "Data pubblicazione:", "Publisher": "Editore", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Il gestore libreria di Python. Pieno di librerie Python e altri strumenti relativi a Python.<br>Contiene: <b>librerie Python e strumenti correlati</b>", "Quit": "<PERSON><PERSON><PERSON>", "Quit WingetUI": "E<PERSON>ci da WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Riduci le richieste UAC, eleva le installazioni per impostazione predefinita, sblocca determinate funzionalità pericolose e altro", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Fai riferimento ai log di UniGetUI per ottenere maggiori dettagli sui file interessati", "Reinstall": "Reinstalla", "Reinstall package": "Reinstal<PERSON> pacchetto", "Related settings": "Impostazioni correlate", "Release notes": "Note sulla versione", "Release notes URL": "URL note sulla versione", "Release notes URL:": "URL note sulla versione:", "Release notes:": "Note sulla versione:", "Reload": "Ricarica", "Reload log": "Ricarica log", "Removal failed": "Rimozione non riuscita", "Removal succeeded": "Rimozione riuscita", "Remove from list": "<PERSON><PERSON><PERSON><PERSON> dall'el<PERSON>co", "Remove permanent data": "<PERSON><PERSON><PERSON><PERSON> dati permanenti", "Remove selection from bundle": "Rimuovi selezione dalla raccolta", "Remove successful installs/uninstalls/updates from the installation list": "Rimuovi installazioni/disinstallazioni/aggiornamenti riusciti dall'elenco delle installazioni", "Removing source {source}": "Rimozione della sorgente {source}", "Removing source {source} from {manager}": "Rimozione sorgente {source} da {manager}", "Repair UniGetUI": "Ripara UniGetUI", "Repair WinGet": "Ripara WinGet", "Report an issue or submit a feature request": "Segnala un problema o invia una richiesta di funzionalità", "Repository": "Repository", "Reset": "Reimposta", "Reset Scoop's global app cache": "Reimposta la cache globale dell'app di Scoop", "Reset UniGetUI": "Reimposta UniGetUI", "Reset WinGet": "Reimposta WinGet", "Reset Winget sources (might help if no packages are listed)": "Reimposta le sorgenti Winget (potrebbe aiutare se non sono elencati pacchetti)", "Reset WingetUI": "Reimposta WingetUI", "Reset WingetUI and its preferences": "Reimposta WingetUI e le sue preferenze", "Reset WingetUI icon and screenshot cache": "Reimposta la cache delle icone e degli screenshot di WingetUI", "Reset list": "Reimposta elenco", "Resetting Winget sources - WingetUI": "Reimpostazione sorgenti di Winget: WingetUI", "Restart": "Riavvia", "Restart UniGetUI": "Riavvia UniGetUI", "Restart WingetUI": "Riavvia WingetUI", "Restart WingetUI to fully apply changes": "Riavvia WingetUI per applicare completamente le modifiche", "Restart later": "Riavvia più tardi", "Restart now": "<PERSON><PERSON><PERSON><PERSON> adesso", "Restart required": "<PERSON><PERSON><PERSON><PERSON>", "Restart your PC to finish installation": "Riavvia il PC per completare l'installazione", "Restart your computer to finish the installation": "Riavvia il computer per completare l'installazione", "Restore a backup from the cloud": "<PERSON><PERSON><PERSON><PERSON> un backup dal cloud", "Restrictions on package managers": "Restrizioni sui gestori di pacchetti", "Restrictions on package operations": "Restrizioni sulle operazioni dei pacchetti", "Restrictions when importing package bundles": "Restrizioni durante l'importazione delle raccolte pacchetti", "Retry": "<PERSON><PERSON><PERSON><PERSON>", "Retry as administrator": "<PERSON><PERSON><PERSON><PERSON> come amministratore", "Retry failed operations": "Riprova le operazioni non riuscite", "Retry interactively": "Riprova in modo interattivo", "Retry skipping integrity checks": "Riprova saltando i controlli di integrità", "Retrying, please wait...": "Nuovo tentativo, attendi...", "Return to top": "<PERSON><PERSON> in alto", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Run as admin": "Amministratore", "Run cleanup and clear cache": "Esegui pulizia e svuota cache", "Run last": "Esegui l'ultimo", "Run next": "Esegui il successivo", "Run now": "<PERSON><PERSON><PERSON><PERSON> adesso", "Running the installer...": "Esecuzione del programma di installazione...", "Running the uninstaller...": "Esecuzione del programma di disinstallazione...", "Running the updater...": "Esecuzione del programma di aggiornamento...", "Save": "<PERSON><PERSON>", "Save File": "Salva file", "Save and close": "Salva e chiudi", "Save as": "<PERSON>va come", "Save bundle as": "Salva raccolta con nome", "Save now": "<PERSON><PERSON> adesso", "Saving packages, please wait...": "Salvataggio dei pacchetti, attendi...", "Scoop Installer - WingetUI": "Programma di installazione di Scoop: WingetUI", "Scoop Uninstaller - WingetUI": "Programma di disinstallazione di Scoop: WingetUI", "Scoop package": "<PERSON><PERSON><PERSON>", "Search": "Ricerca", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Ricerca software desktop, avvisami quando sono disponibili aggiornamenti e non fare cose da nerd. Non voglio che WingetUI sia eccessivamente complicato, voglio solo un semplice <b>store di software</b>", "Search for packages": "Ricerca pacchetti", "Search for packages to start": "I pacchetti trovati verranno visualizzati qui", "Search mode": "Modalità di ricerca", "Search on available updates": "Ricerca tra gli aggiornamenti disponibili", "Search on your software": "Ricerca tra il tuo software", "Searching for installed packages...": "Ricerca pacchetti installati...", "Searching for packages...": "Ricerca pacchetti...", "Searching for updates...": "Ricerca aggiornamenti...", "Select": "Seleziona", "Select \"{item}\" to add your custom bucket": "Seleziona \"{item}\" per aggiungere il tuo bucket personalizzato", "Select a folder": "Seleziona una cartella", "Select all": "Se<PERSON><PERSON>na tutto", "Select all packages": "Seleziona tutti i pacchetti", "Select backup": "Seleziona backup", "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON> solo <b>se sai cosa stai facendo</b>", "Select package file": "Seleziona file del pacchetto", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Seleziona il backup che desideri aprire. In seguito, potrai controllare quali pacchetti desideri installare.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Seleziona i processi che devono essere chiusi prima che questo pacchetto venga installato, aggiornato o disinstallato.", "Select the source you want to add:": "Seleziona la sorgente che desideri aggiungere:", "Select upgradable packages by default": "Seleziona pacchetti aggiornabili per impostazione predefinita", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Seleziona quali <b>gestori di pacchetti</b> usare ({0}), configura la modalità di installazione e aggiornament9 dei pacchetti, gestisci i diritti di amministratore.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake inviato. In attesa della risposta dell'ascoltatore dell'istanza... ({0}%)", "Set a custom backup file name": "Personalizza nome del file di backup \n", "Set custom backup file name": "Nome del file di backup personalizzato", "Settings": "Impostazioni", "Share": "Condi<PERSON><PERSON>", "Share WingetUI": "Condividi WingetUI", "Share anonymous usage data": "Condividi dati di utilizzo anonimi", "Share this package": "Condividi questo pacchetto", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Se modifichi le impostazioni di sicurezza, dovrai aprire nuovamente la raccolta affinché le modifiche abb<PERSON> e<PERSON>.", "Show UniGetUI on the system tray": "Mostra UniGetUI nella barra delle applicazioni", "Show UniGetUI's version and build number on the titlebar.": "Mostra la versione e il numero di build di UniGetUI sulla barra del titolo.", "Show WingetUI": "Mostra WingetUI", "Show a notification when an installation fails": "Mostra una notifica quando un'installazione non riesce", "Show a notification when an installation finishes successfully": "Mostra una notifica quando un'installazione viene completata correttamente", "Show a notification when an operation fails": "Mostra una notifica quando un'operazione non riesce\n", "Show a notification when an operation finishes successfully": "Mostra una notifica quando un'operazione viene completata correttamente", "Show a notification when there are available updates": "Mostra una notifica quando ci sono aggiornamenti disponibili", "Show a silent notification when an operation is running": "Mostra una notifica silenziosa quando un'operazione è in esecuzione", "Show details": "<PERSON><PERSON>", "Show in explorer": "Mostra in Explorer", "Show info about the package on the Updates tab": "Mostra informazioni sui pacchetti dalla scheda Aggiornamenti", "Show missing translation strings": "<PERSON>ra stringhe non tradotte", "Show notifications on different events": "Mostra notifiche su diversi eventi", "Show package details": "Mostra dettagli pacchetto", "Show package icons on package lists": "Mostra icone dei pacchetti negli elenchi dei pacchetti", "Show similar packages": "<PERSON>ra pacchetti simili", "Show the live output": "Mostra l'output in tempo reale", "Size": "Dimensioni", "Skip": "Salta", "Skip hash check": "Salta controllo hash", "Skip hash checks": "Salta controlli hash", "Skip integrity checks": "Salta controlli di integrità", "Skip minor updates for this package": "Salta aggiornamenti minori per questo pacchetto", "Skip the hash check when installing the selected packages": "Salta verifica hash durante l'installazione dei pacchetti selezionati", "Skip the hash check when updating the selected packages": "Salta verifica hash durante l'aggiornamento dei pacchetti selezionati", "Skip this version": "Salta questa versione", "Software Updates": "Aggiorna p<PERSON>chetti", "Something went wrong": "Si è verificato un errore", "Something went wrong while launching the updater.": "Si è verificato un problema durante l'avvio del programma di aggiornamento.", "Source": "<PERSON><PERSON><PERSON>", "Source URL:": "URL sorgente:", "Source added successfully": "Sorgente aggiunta correttamente", "Source addition failed": "Aggiunta sorgente non riuscita", "Source name:": "Nome sorgente:", "Source removal failed": "Rimozione sorgente non riuscita", "Source removed successfully": "Sorgente rimossa correttamente", "Source:": "Sorgente:", "Sources": "<PERSON><PERSON><PERSON>", "Start": "Avvia", "Starting daemons...": "Avvio demoni...", "Starting operation...": "Avvio dell'operazione...", "Startup options": "Opzioni all'avvio", "Status": "Stato", "Stuck here? Skip initialization": "Sei bloccato qui? Salta l'inizializzazione", "Suport the developer": "Supporta lo sviluppatore", "Support me": "<PERSON><PERSON>", "Support the developer": "Supporta lo sviluppatore", "Systems are now ready to go!": "È tutto pronto!", "Telemetry": "Telemetria", "Text": "<PERSON><PERSON>", "Text file": "File di testo", "Thank you ❤": "Grazie ❤", "Thank you 😉": "Grazie 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Il gestore pacchetti Rust.<br><PERSON><PERSON><PERSON>: <b>librerie e programmi Rust scritti in Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Il backup NON includerà alcun file binario né dati salvati del programma.", "The backup will be performed after login.": "Il backup verrà eseguito dopo l'accesso.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Il backup includerà l'elenco completo dei pacchetti installati e le relative opzioni di installazione. Verranno salvati anche gli aggiornamenti ignorati e le versioni saltate.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "La raccolta che stai tentando di caricare sembra non essere valida Controlla il file e riprova.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "L'hash del programma di installazione non coincide con il valore atteso, quindi l'autenticità non può essere verificata. Se ti fidi dell'editore, {0} una altra volta saltando il controllo dell'hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Il classico gestore pacchetti per Windows. Qui troverai di tutto. <br><PERSON><PERSON><PERSON>: <b>software generico</b>", "The cloud backup completed successfully.": "Backup sul cloud completato correttamente.", "The cloud backup has been loaded successfully.": "Il backup sul cloud è stato caricato correttamente.", "The current bundle has no packages. Add some packages to get started": "La raccolta attuale non ha pacchetti. Aggiungi alcuni pacchetti per iniziare.", "The executable file for {0} was not found": "Il file eseguibile per {0} non è stato trovato", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Le seguenti opzioni verranno applicate per impostazione predefinita ogni volta che un pacchetto {0} viene installato, aggiornato o disinstallato.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "I seguenti pacchetti verranno esportati in un file JSON. Nessun dato utente o file binario verrà salvato.", "The following packages are going to be installed on your system.": "Verranno installati sul tuo sistema i seguenti pacchetti.", "The following settings may pose a security risk, hence they are disabled by default.": "Le seguenti impostazioni potrebbero rappresentare un rischio per la sicurezza, pertanto sono disabilitate per impostazione predefinita.", "The following settings will be applied each time this package is installed, updated or removed.": "Le seguenti impostazioni verranno applicate ogni volta che il pacchetto viene installato, aggiornato o rimosso.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Le seguenti impostazioni verranno applicate ogni volta che il pacchetto verrà installato, aggiornato o rimosso. Esse verranno salvate automaticamente.", "The icons and screenshots are maintained by users like you!": "È merito di utenti come te se le icone e gli screenshot sono in ordine!", "The installer authenticity could not be verified.": "Non è stato possibile verificare l'autenticità del programma di installazione.", "The installer has an invalid checksum": "Il programma di installazione ha un checksum non  valido", "The installer hash does not match the expected value.": "L'hash del programma di installazione non corrisponde al valore previsto.", "The local icon cache currently takes {0} MB": "La cache delle icone locali occupa attualmente {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "L'obiettivo principale di questo progetto è creare un'interfaccia utente intuitiva per gestire i più comuni gestori di pacchetti CLI per Windows, come Winget e Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Il pacchetto \"{0}\" non è stato trovato nel gestore pacchetti \"{1}\"", "The package bundle could not be created due to an error.": "Non è stato possibile creare la raccolta di pacchetti a causa di un errore.", "The package bundle is not valid": "La raccolta di pacchetti non è valida", "The package manager \"{0}\" is disabled": "Il gestore pacchetti \"{0}\" è disabilitato", "The package manager \"{0}\" was not found": "Il gestore pacchetti \"{0}\" non è stato trovato", "The package {0} from {1} was not found.": "Il pacchetto {0} da {1} non è stato trovato.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "I pacchetti elencati qui non verranno presi in considerazione durante il controllo degli aggiornamenti. Fai doppio clic su di essi o fai clic sul pulsante alla loro destra per non ignorare gli aggiornamenti.", "The selected packages have been blacklisted": "I pacchetti selezionati sono stati messi in blacklist", "The settings will list, in their descriptions, the potential security issues they may have.": "Le impostazioni elencheranno, nelle loro descrizioni, i potenziali problemi di sicurezza che si potrebbero presentare.", "The size of the backup is estimated to be less than 1MB.": "La dimensione del backup sarà inferiore a 1 MB.", "The source {source} was added to {manager} successfully": "La sorgente {source} è stata aggiunta a {manager} correttamente", "The source {source} was removed from {manager} successfully": "La sorgente {source} è stata rimossa da {manager} correttamente", "The system tray icon must be enabled in order for notifications to work": "L'icona della barra delle applicazioni deve essere abilitata affinché le notifiche funzionino", "The update process has been aborted.": "Il processo di aggiornamento è stato interrotto.", "The update process will start after closing UniGetUI": "Il processo di aggiornamento inizierà dopo la chiusura di UniGetUI", "The update will be installed upon closing WingetUI": "L'aggiornamento verrà installato alla chiusura di WingetUI", "The update will not continue.": "L'aggiornamento non continuerà.", "The user has canceled {0}, that was a requirement for {1} to be run": "L'utente ha annullato {0}, che era un requisito per l'esecuzione di {1}", "There are no new UniGetUI versions to be installed": "Non ci sono nuove versioni di UniGetUI da installare", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Ci sono operazioni in corso. L'uscita da WingetUI potrebbe causare il loro errore. Vuoi continuare?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Ci sono alcuni fantastici video su YouTube che mostrano WingetUI e le sue potenzialità. Potresti imparare trucchi e suggerimenti utili!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Ci sono due motivi principali per non eseguire WingetUI come amministratore.\nIl primo è che il gestore pacchetti Scoop potrebbe causare problemi con alcuni comandi se eseguito con diritti di amministratore.\nIl secondo è che eseguire WingetUI come amministratore significa che qualsiasi pacchetto scaricato verrà eseguito come amministratore (e questo non è sicuro).\nRicorda che se devi installare un pacchetto specifico come amministratore, puoi sempre fare clic con il tasto destro del mouse sulla voce -> Installa/Aggiorna/Disinstalla come amministratore.", "There is an error with the configuration of the package manager \"{0}\"": "C'è un errore nella configurazione del gestore pacchetti \"{0}\"\n", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "C'è un'installazione in corso. Se chiudi WingetUI, l'installazione può fallire e avere risultati inaspettati. Vuoi comunque chiudere WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "Sono i programmi in caricamento per l'installazione, l'aggiornamento e la rimozione dei pacchetti.", "Third-party licenses": "Licenze di terze parti", "This could represent a <b>security risk</b>.": "<PERSON>o potrebbe comportare un <b>rischio per la sicurezza</b>.", "This is not recommended.": "Questa operazione non è consigliata.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Questo è probabilmente dovuto al fatto che il pacchetto che ti è stato inviato è stato rimosso o pubblicato su un gestore pacchetti che non hai abilitato. L'ID ricevuto è {0}", "This is the <b>default choice</b>.": "Questa è la <b>scelta predefinita</b>", "This may help if WinGet packages are not shown": "Questo può essere d'aiuto se i pacchetti WinGet non vengono visualizzati", "This may help if no packages are listed": "Questo può essere d'aiuto se i pacchetti non sono elencati", "This may take a minute or two": "L'operazione potrebbe richiedere un minuto o due", "This operation is running interactively.": "Questa operazione viene eseguita in modo interattivo.", "This operation is running with administrator privileges.": "Questa operazione viene eseguita con privilegi di amministratore.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Questa opzione CAUSERÀ problemi. Qualsiasi operazione che non sia in grado di elevare i propri privilegi FALLIRÀ. Installare/aggiornare/disinstallare come amministratore NON FUNZIONERÀ.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Questo pacchetto contiene alcune impostazioni potenzialmente pericolose che potrebbero essere ignorate per impostazione predefinita.", "This package can be updated": "<PERSON><PERSON> pacchetto può essere aggiornato", "This package can be updated to version {0}": "<PERSON><PERSON> pacchetto può essere aggiornato (update) alla versione {0}", "This package can be upgraded to version {0}": "Questo pacchetto può essere aggiornato (upgrade) alla versione {0}", "This package cannot be installed from an elevated context.": "<PERSON>o pacchetto non può essere installato da un contesto elevato.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Questo pacchetto non ha alcun screenshot o icona? Contribuisci a WingetUI aggiungendo le icone e gli screenshot mancanti al nostro database pubblico e aperto a tutti.", "This package is already installed": "Questo pacchetto è già installato", "This package is being processed": "Questo pacchetto è in fase di elaborazione", "This package is not available": "Questo pacchetto non è disponibile", "This package is on the queue": "<PERSON><PERSON> pacchetto è in coda", "This process is running with administrator privileges": "Il processo è in esecuzione con privilegi di amministratore", "This project has no connection with the official {0} project — it's completely unofficial.": "Questo progetto non è legato al progetto ufficiale di {0}, non ha alcuna connessione con esso.", "This setting is disabled": "Questa impostazione è disabilitata", "This wizard will help you configure and customize WingetUI!": "La procedura guidata ti aiuterà a configurare e personalizzare WingetUI", "Toggle search filters pane": "Attiva/disattiva il riquadro dei filtri di ricerca", "Translators": "<PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Prova a fermare i processi che rifiutano di chiudersi quando richiesto", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Attivando questa opzione è possibile modificare il file eseguibile utilizzato per interagire con i gestori di pacchetti. Sebbene ciò consenta una personalizzazione più precisa dei processi di installazione, potrebbe anche essere pericoloso.", "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON>vi qui il nome e l'URL della sorgente che vuoi aggiungere, separati da uno spazio.", "Unable to find package": "Impossibile trovare il pacchetto", "Unable to load informarion": "Impossibile caricare le informazioni", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI raccoglie dati di utilizzo anonimi per migliorare l'esperienza dell'utente.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI raccoglie dati di utilizzo anonimi con l'unico scopo di comprendere e migliorare l'esperienza dell'utente.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI ha rilevato un nuovo collegamento sul desktop che può essere eliminato automaticamente.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI ha rilevato i seguenti collegamenti sul desktop che possono essere rimossi automaticamente durante gli aggiornamenti futuri", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI ha rilevato {0} nuovi collegamenti sul desktop che possono essere eliminati automaticamente.", "UniGetUI is being updated...": "UniGetUI è in fase di aggiornamento...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI non è correlato a nessuno dei gestori di pacchetti compatibili. UniGetUI è un progetto indipendente.", "UniGetUI on the background and system tray": "UniGetUI in background e nella barra delle applicazioni", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI o alcuni dei suoi componenti sono mancanti o danneggiati.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI richiede {0} per funzionare, ma non è stato trovato sul tuo sistema.", "UniGetUI startup page:": "Pagina di avvio di UniGetUI:", "UniGetUI updater": "Programma di aggiornamento di UniGetUI", "UniGetUI version {0} is being downloaded.": "È in fase di scaricamento la versione {0} di UniGetUI.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} è pronto per essere installato.", "Uninstall": "Disin<PERSON>la", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON> (e i suoi pacchetti)", "Uninstall and more": "Disinstallazioni e altro", "Uninstall and remove data": "Disinstalla e rimuovi dati", "Uninstall as administrator": "Disinstalla come amministratore", "Uninstall canceled by the user!": "Disinstallazione annullata dall'utente!", "Uninstall failed": "Disinstallazione non riuscita", "Uninstall options": "Opzioni di disinstallazione", "Uninstall package": "Disin<PERSON><PERSON> pacchetto", "Uninstall package, then reinstall it": "Disinstalla e reinstalla pacchetto", "Uninstall package, then update it": "Disinstalla e aggiorna pacchetto", "Uninstall previous versions when updated": "Disinstalla le versioni precedenti quando aggiorni", "Uninstall selected packages": "Disinstalla pacchetti selezionati", "Uninstall selection": "Disinstalla selezione", "Uninstall succeeded": "Disinstallazione riuscita", "Uninstall the selected packages with administrator privileges": "Disinstalla pacchetti selezionati con privilegi di amministratore", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "I pacchetti disinstallabili con l'origine elencata come \"{0}\" non sono pubblicati su nessun gestore pacchetti, quindi non ci sono informazioni disponibili da mostrare.", "Unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unknown size": "Dimensioni sconosciute", "Unset or unknown": "Non impostato o sconosciuto", "Up to date": "Aggiornato", "Update": "Aggiorna", "Update WingetUI automatically": "Aggiorna WingetUI automaticamente", "Update all": "Aggiorna tutto", "Update and more": "Aggiornamenti e altro", "Update as administrator": "Aggiorna come amministratore", "Update check frequency, automatically install updates, etc.": "Aggiorna la frequenza di controllo, installa automaticamente gli aggiornamenti, ecc.", "Update date": "Data aggiornamento", "Update failed": "Aggiornamento non riuscito", "Update found!": "Aggiornamento trovato!", "Update now": "Aggiorna adesso", "Update options": "Opzioni di aggiornamento", "Update package indexes on launch": "Aggiorna gli indici dei pacchetti all'avvio", "Update packages automatically": "Aggiorna pacchetti automaticamente", "Update selected packages": "Aggiorna pacchetti selezionati", "Update selected packages with administrator privileges": "Aggiorna pacchetti selezionati con privilegi di amministratore", "Update selection": "Aggiorna selezione", "Update succeeded": "Aggiornamento riuscito", "Update to version {0}": "Aggiorna alla versione {0}", "Update to {0} available": "Aggiornamento a {0} disponibile", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Aggiorna automaticamente i portfile Git di vcpkg (richiede che Git sia installato)", "Updates": "Aggiornamenti", "Updates available!": "Aggiornamenti disponibili!", "Updates for this package are ignored": "Gli aggiornamenti per questo pacchetto vengono ignorati", "Updates found!": "Aggiornamenti trovati!", "Updates preferences": "Preferenze sugli aggiornamenti", "Updating WingetUI": "Aggiornamento di WingetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Usa WinGet integrato in versione compatibile invece di CMDLets di PowerShell", "Use a custom icon and screenshot database URL": "Usa icona personalizzata e URL dal database screenshot", "Use bundled WinGet instead of PowerShell CMDlets": "Usa WinGet integrato invece di CMDLets di PowerShell", "Use bundled WinGet instead of system WinGet": "Usa WinGet integrato invece di WinGet di sistema", "Use installed GSudo instead of UniGetUI Elevator": "Usa GSudo installato invece di UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Usa GSudo installato invece di quello integrato", "Use system Chocolatey": "Usa Chocolatey di sistema", "Use system Chocolatey (Needs a restart)": "Usa Chocolatey di sistema (richiede riavvio)", "Use system Winget (Needs a restart)": "Usa Winget di sistema (richiede riavvio)", "Use system Winget (System language must be set to english)": "Usa Winget di sistema (la lingua del sistema deve essere impostata su inglese)", "Use the WinGet COM API to fetch packages": "Usa l'API COM WinGet per recuperare i pacchetti", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Usa il modulo WinGet PowerShell anziché l'API COM WinGet", "Useful links": "<PERSON> utili", "User": "Utente", "User interface preferences": "Preferenze dell'interfaccia utente", "User | Local": "Utente | Locale", "Username": "Nome utente", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "L'utilizzo di WingetUI implica l'accettazione della licenza GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "L'utilizzo di WingetUI implica l'accettazione della Licenza MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "La directory radice di vcpkg non è stata trovata. Definisci la variabile di ambiente %VCPKG_ROOT% o configurala nelle impostazioni di UniGetUI", "Vcpkg was not found on your system.": "Vcpkg non è stato trovato nel tuo sistema.", "Verbose": "Verboso", "Version": "Versione", "Version to install:": "<PERSON><PERSON> da installare:", "Version:": "Versione:", "View GitHub Profile": "Visualizza profilo G<PERSON>", "View WingetUI on GitHub": "Visualizza WingetUI su GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Visualizza il codice sorgente di WingetUI. Da qui potrai segnalare bug o suggerire funzionalità o persino contribuire direttamente al progetto WingetUI", "View mode:": "Modalità di visualizzazione:", "View on UniGetUI": "Visualizza su UniGetUI", "View page on browser": "Visualizza pagina sul browser", "View {0} logs": "Visualizza {0} log", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Attendi che il dispositivo sia connesso a Internet prima di provare a svolgere attività che richiedono la connettività Internet.", "Waiting for other installations to finish...": "In attesa che le altre installazioni finiscano...", "Waiting for {0} to complete...": "In attesa del completamento di {0}...", "Warning": "Attenzione", "Warning!": "Attenzione!", "We are checking for updates.": "Stiamo verificando gli aggiornamenti.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Non è stato possibile caricare informazioni dettagliate su questo pacchetto, perché non è stato trovato in nessuna delle sorgenti dei pacchetti", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Non è stato possibile caricare informazioni dettagliate su questo pacchetto, perché non è stato installato da un gestore pacchetti disponibile.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Non è stato possibile {action} {package}, riprova più tardi. Fai clic su \"{showDetails}\" per ottenere i log dal programma di installazione.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Non è stato possibile {action} {package}, riprova più tardi. Fai clic su \"{showDetails}\" per ottenere i log dal programma di disinstallazione.", "We couldn't find any package": "Non è stato possibile trovare alcun pacchetto", "Welcome to WingetUI": "Benvenuto in WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": "<PERSON>uando si installano in batch i pacchetti da una raccolta, installa anche i pacchetti già installati", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Quando vengono rilevati nuovi collegamenti, questi vengono eliminati automaticamente anziché visualizzare questa finestra di dialogo.", "Which backup do you want to open?": "Quale backup vuoi aprire?", "Which package managers do you want to use?": "Quali gestori di pacchetti vuoi usare?", "Which source do you want to add?": "Quale sorgente vuoi aggiungere?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "<PERSON><PERSON><PERSON>et possa essere utilizzato all'interno di WingetUI, WingetUI può essere utilizzato anche con altri gestori di pacchetti, il che può creare confusione. In passato, WingetUI era progettato per funzionare solo con Winget, ma questo non è più vero, e pertanto WingetUI non rappresenta ciò che questo progetto intende diventare.", "WinGet could not be repaired": "WinGet non può essere riparato", "WinGet malfunction detected": "Malfunzionamento di WinGet rilevato", "WinGet was repaired successfully": "WinGet è stato riparato correttamente", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI: è tutto aggiornato", "WingetUI - {0} updates are available": "WingetUI: sono disponibili {0} aggiornamenti", "WingetUI - {0} {1}": "WingetUI: {0} {1}", "WingetUI Homepage": "Homepage di WingetUI", "WingetUI Homepage - Share this link!": "Homepage di WingetUI: condividi questo link!", "WingetUI License": "Licenza di WingetUI", "WingetUI Log": "Log di WingetUI", "WingetUI Repository": "Repository di WingetUI", "WingetUI Settings": "Impostazioni di WingetUI", "WingetUI Settings File": "File di impostazioni di WIngetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI si avvale delle seguenti librerie. Senza di esse, WingetUI non sarebbe stato realizzabile.", "WingetUI Version {0}": "WingetUI versione {0}", "WingetUI autostart behaviour, application launch settings": "Comportamento di avvio automatico di WingetUI, impostazioni di avvio delle applicazioni", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI può controllare se ci sono degli aggiornamenti disponibili e, se lo desideri, installarli automaticamente", "WingetUI display language:": "Lingua di visualizzazione di WingetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "Non è consigliato eseguire WingetUI con i privilegi di amministratore. In questo modo, TUTTE le operazioni avviate da WingetUI arvranno i privilegi di amministratore. È possibile continuare a utilizzare il programma, ma è fortemente sconsigliato eseguirlo come amministratore.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI è stato tradotto in oltre 40 lingue da traduttori volontari. Un grande grazie a tutti🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI non è tradotto da un robot! I seguenti utenti si sono occupati della traduzione:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI è un'applicazione che semplifica la gestione del software, fornendo un'unica interfaccia grafica per i gestori di pacchetti da riga di comando.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI sta cambiando nome per sottolineare la differenza tra WingetUI (l'interfaccia che stai utilizzando in questo momento) e Winget (un gestore pacchetti sviluppato da Microsoft con cui non è correlato)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI si sta aggiornando. Quando avrà terminato si riavvierà automaticamente", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI è gratuito e lo sarà per sempre. Nessuna pubblicità, nessuna carta di credito, nessuna versione premium. 100% gratuito, per sempre.", "WingetUI log": "Log di WingetUI", "WingetUI tray application preferences": "Preferenze del vassoio di notifica WingetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI si avvale delle seguenti librerie. Senza di esse, WingetUI non sarebbe stato realizzabile.", "WingetUI version {0} is being downloaded.": "È in fase di scaricamento la versione {0} di WingetUI.", "WingetUI will become {newname} soon!": "WingetUI diventerà presto {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI non cercherà aggiornamenti periodicamente. Verranno comunque controllati all'avvio, ma non riceverai alcuna notifica se vengono trovati.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI mostrerà un avviso di Controllo dell'account utente ogni volta che un pacchetto richiede l'installazione elevata.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI verrà presto chiamato {newname}. Ciò non rappresenterà alcuna modifica nell'applicazione. Io (lo sviluppatore) continuerò lo sviluppo di questo progetto come sto facendo adesso, ma con un nome diverso.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI non sarebbe stato possibile senza l'aiuto dei nostri collaboratori. Dai un'occhiata al loro profilo GitHub: grazie a loro, WingetUI è diventato realtà!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI non sarebbe stato possibile senza l'aiuto dei collaboratori. Grazie a tutti 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} è pronto per essere installato.", "Write here the process names here, separated by commas (,)": "Scrivi qui i nomi dei processi, separati da virgole (,)", "Yes": "Sì", "You are logged in as {0} (@{1})": "Hai effettuato l'accesso come {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "È possibile modificare questo comportamento nelle impostazioni di sicurezza di UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "È possibile definire i comandi che verranno eseguiti prima o dopo l'installazione, l'aggiornamento o la disinstallazione di questo pacchetto. Verranno eseguiti su un prompt dei comandi, quindi gli script CMD funzioneranno qui.", "You have currently version {0} installed": "Attualmente hai installata la versione {0}", "You have installed WingetUI Version {0}": "Hai installato la versione {0} di WingetUI", "You may lose unsaved data": "Potresti perdere i dati non salvati", "You may need to install {pm} in order to use it with WingetUI.": "Potrebbe essere necessario installare {pm} per poterlo utilizzare con WingetUI.", "You may restart your computer later if you wish": "Se lo desideri, puoi riavviare il computer più tardi", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Ti verrà richiesto una sola volta e i diritti di amministratore verranno concessi ai pacchetti che li richiedono.", "You will be prompted only once, and every future installation will be elevated automatically.": "Ti verrà richiesto una sola volta e ogni installazione futura verrà elevata automaticamente.", "You will likely need to interact with the installer.": "Probabilmente sarà necessario interagire con il programma di installazione.", "[RAN AS ADMINISTRATOR]": "[ESEGUITO COME AMMINISTRATORE]", "buy me a coffee": "offrimi un caffè", "extracted": "<PERSON><PERSON>to", "feature": "caratteristica", "formerly WingetUI": "precedentemente WingetUI", "homepage": "homepage", "install": "installare", "installation": "installazione", "installed": "installato", "installing": "installazione", "library": "libreria", "mandatory": "obbligatorio", "option": "opzione", "optional": "opzionale", "uninstall": "disinstallare", "uninstallation": "disinstallazione", "uninstalled": "disinstallato", "uninstalling": "disinstallazione", "update(noun)": "aggiornamento", "update(verb)": "aggiornare", "updated": "aggiornato", "updating": "aggiornamento", "version {0}": "versione {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} Le opzioni di installazione sono attualmente bloccate perché {0} segue le opzioni di installazione predefinite.", "{0} Uninstallation": "Disinstallazione di {0}", "{0} aborted": "{0} annullata", "{0} can be updated": "{0} può essere aggiornato", "{0} can be updated to version {1}": "{0} può essere aggiornato alla versione {1}", "{0} days": "{0} <PERSON><PERSON><PERSON>", "{0} desktop shortcuts created": "{0} collegamenti sul desktop creati", "{0} failed": "{0} non riuscita", "{0} has been installed successfully.": "{0} è stato installato correttamente.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} è stato installato correttamente. Si consiglia di riavviare UniGetUI per completare l'installazione", "{0} has failed, that was a requirement for {1} to be run": "{0} non è riuscito, era un requisito per l'esecuzione di {1}", "{0} homepage": "{0} homepage", "{0} hours": "{0} ore", "{0} installation": "Installazione di {0}", "{0} installation options": "Opzioni di installazione di {0}", "{0} installer is being downloaded": "Programma di installazione {0} in fase di scaricamento", "{0} is being installed": "{0} è in fase di installazione", "{0} is being uninstalled": "{0} è in fase di disinstallazione", "{0} is being updated": "{0} è in fase di aggiornamento", "{0} is being updated to version {1}": "{0} è in fase di aggiornamento alla versione {1}", "{0} is disabled": "{0} è disabilitato", "{0} minutes": "{0} minuti", "{0} months": "{0} mesi", "{0} packages are being updated": "{0} p<PERSON><PERSON><PERSON> sono in fase di aggiornamento", "{0} packages can be updated": "{0} p<PERSON><PERSON><PERSON> possono essere aggiornati", "{0} packages found": "{0} p<PERSON><PERSON><PERSON> trovati", "{0} packages were found": "Sono stati trovati {0} p<PERSON><PERSON><PERSON>", "{0} packages were found, {1} of which match the specified filters.": "<PERSON><PERSON><PERSON> trovati: {0}. <PERSON><PERSON><PERSON> che corrispondono ai filtri specificati: {1}.", "{0} settings": "{0} impostazioni", "{0} status": "Stato {0}\n", "{0} succeeded": "{0} correttamente", "{0} update": "Aggiornamento di {0}", "{0} updates are available": "Sono disponibili {0} aggiornamenti", "{0} was {1} successfully!": "{0} è stato {1} correttamente!", "{0} weeks": "{0} set<PERSON><PERSON>e", "{0} years": "{0} anni", "{0} {1} failed": "L' {1} con {0} non è riuscita", "{package} Installation": "Installazione di {package}", "{package} Uninstall": "Disinstallazione di {package}", "{package} Update": "Aggiornamento di {package}", "{package} could not be installed": "Impossibile installare {package}", "{package} could not be uninstalled": "Impossibile disinstallare {package}", "{package} could not be updated": "Impossibile aggiornare {package}", "{package} installation failed": "Installazione di {package} non riuscita", "{package} installer could not be downloaded": "Impossibile scaricare il programma di installazione {package}", "{package} installer download": "Scarica il programma di installazione {package}", "{package} installer was downloaded successfully": "Il programma di installazione {package} è stato scaricato correttamente", "{package} uninstall failed": "Disinstallazione di {package} non riuscita", "{package} update failed": "Aggiornamento di {package} non riuscito", "{package} update failed. Click here for more details.": "Aggiornamento di {package} non riuscito. Fai clic qui per maggiori dettagli.", "{package} was installed successfully": "{package} è stato installato correttamente", "{package} was uninstalled successfully": "{package} è stato disinstallato correttamente", "{package} was updated successfully": "{package} è stato aggiornato correttamente\n", "{pcName} installed packages": "<PERSON><PERSON>ti installati su {pcName}", "{pm} could not be found": "Impossibile trovare {pm}", "{pm} found: {state}": "{pm} trovato: {state}", "{pm} is disabled": "{pm} è disabilitato", "{pm} is enabled and ready to go": "{pm} è abilitato e pronto per l'uso", "{pm} package manager specific preferences": "Preferenze specifiche del gestore pacchetti {pm}", "{pm} preferences": "Preferenze di {pm}", "{pm} version:": "Versione di {pm}:", "{pm} was not found!": "{pm} non è stato trovato!"}