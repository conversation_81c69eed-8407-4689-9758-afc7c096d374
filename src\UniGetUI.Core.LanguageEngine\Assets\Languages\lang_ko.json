{"\"{0}\" is a local package and can't be shared": "\"{0}\"은(는) 로컬 패키지라서 공유할 수 없습니다.", "\"{0}\" is a local package and does not have available details": "\"{0}\"은(는) 로컬 패키지라서 사용 가능한 세부 정보가 없습니다.", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\"은(는) 로컬 패키지라서 이 기능과 호환되지 않습니다.", "(Last checked: {0})": "(마지막 확인 날짜: {0})", "(Number {0} in the queue)": "(대기열의 {0}번)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@thejjw, @VenusGirl, @shblue21, @minbert, @jihoon416, @MuscularPuky", "0 packages found": "패키지 0개 찾음", "0 updates found": "업데이트 0개 찾음", "1 - Errors": "1 - 오류", "1 day": "1 일", "1 hour": "1 시간", "1 month": "1 달", "1 package was found": "패키지 1개 찾음", "1 update is available": "업데이트 1개 사용 가능", "1 week": "1 주", "1 year": "1 년", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. \"{0}\" 또는 \"{1}\" 페이지로 탐색하세요.", "2 - Warnings": "2 - 경고", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. 번들에 추가하려는 패키지의 위치를 지정하고 가장 왼쪽의 선택란을 선택하세요.", "3 - Information (less)": "3 - 정보(더 적게)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. 번들에 추가하려는 패키지를 선택했다면 도구 모음의 \"{0}\" 옵션을 찾아서 클릭하세요.", "4 - Information (more)": "4 - 정보(더 많이)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. 패키지가 번들에 추가되었습니다. 계속해서 패키지를 추가하거나 번들을 내보낼 수 있습니다.", "5 - information (debug)": "5 - 정보(디버그)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "인기 있는 C/C++ 라이브러리 관리자입니다. C/C++ 라이브러리 및 기타 C/C++ 관련 유틸리티로 가득합니다.<br>포함 내용: <b>C/C++ 라이브러리 및 관련 유틸리티<b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Microsoft의 .NET 생태계를 염두에 두고 설계된 도구와 실행 파일로 가득한 저장소입니다.<br>포함 내용: <b>.NET 관련 도구 및 스크립트</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Microsoft의 .NET 생태계를 염두에 두고 설계된 도구로 가득한 저장소입니다.<br>포함 내용: <b>.NET 관련 도구</b>", "A restart is required": "다시 시작해야 합니다.", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "정보", "About Qt6": "Qt6 정보", "About WingetUI": "UniGetUI 정보", "About WingetUI version {0}": "UniGetUI {0} 버전 정보", "About the dev": "개발자 정보", "Accept": "수락", "Action when double-clicking packages, hide successful installations": "패키지를 두 번 클릭할 때 수행, 성공한 설치 숨기기", "Add": "추가", "Add a source to {0}": "{0}에 소스 추가", "Add a timestamp to the backup file names": "백업 파일 이름에 타임스탬프 추가", "Add a timestamp to the backup files": "백업 파일에 타임스탬프 추가", "Add packages or open an existing bundle": "패키지 추가 또는 기존 번들 열기", "Add packages or open an existing package bundle": "패키지 추가 또는 기존 패키지 번들 열기", "Add packages to bundle": "패키지를 번들에 추가", "Add packages to start": "패키지를 시작에 추가", "Add selection to bundle": "선택 항목을 번들에 추가", "Add source": "소스 추가", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "적용 가능한 업데이트가 없어서 실패한 업데이트를 업데이트 무시 목록에 추가", "Adding source {source}": "{source} 소스 추가하기", "Adding source {source} to {manager}": "{manager}에 {source} 소스 추가하기", "Addition succeeded": "추가 성공", "Administrator privileges": "관리자 권한", "Administrator privileges preferences": "관리자 권한 환경 설정", "Administrator rights": "관리자 권한", "Administrator rights and other dangerous settings": null, "Advanced options": "고급 옵션", "All files": "모든 파일", "All versions": "모든 버전", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "패키지 작업을 병렬로 수행하도록 허용", "Allow parallel installs (NOT RECOMMENDED)": "패키지를 병렬로 설치하도록 허용(권장하지 않음)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "{pm} 작업을 병렬로 수행하도록 허용", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "또는 Windows PowerShell 프롬프트에서 다음 명령을 실행하여 {0}을(를) 설치할 수도 있습니다:", "Always elevate {pm} installations by default": "기본적으로 {pm} 설치 권한 높이기", "Always run {pm} operations with administrator rights": "항상 관리자 권한으로 {pm} 작업 실행", "An error occurred": "오류가 발생했습니다.", "An error occurred when adding the source: ": "소스를 추가하는 동안 오류가 발생했습니다:", "An error occurred when attempting to show the package with Id {0}": "아이디 {0} 패키지를 표시하는 동안 오류가 발생했습니다.", "An error occurred when checking for updates: ": "업데이트를 확인하는 동안 오류가 발생했습니다:", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "이 패키지를 처리하는 동안 오류가 발생했습니다.", "An error occurred:": "오류가 발생했습니다:", "An interal error occurred. Please view the log for further details.": "내부 오류가 발생했습니다. 자세한 내용은 로그를 참고하십시오.", "An unexpected error occurred:": "예기치 않은 오류가 발생했습니다:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "WinGet을 복구하는 동안 예기치 않은 문제가 발생했습니다. 나중에 다시 시도해주세요.", "An update was found!": "업데이트를 찾았습니다!", "Android Subsystem": "Android 하위 시스템", "Another source": "다른 소스", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "설치 또는 업데이트 작업 중에 생성된 모든 새 바로 가기는 첫 감지 때 확인 메시지 표시 없이 자동으로 삭제됩니다.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "UniGetUI 외부에서 만들거나 수정한 모든 바로 가기는 무시됩니다. {0} 버튼을 통해 추가할 수 있습니다.", "Any unsaved changes will be lost": "저장하지 않은 모든 변경 내용이 손실됩니다.", "App Name": "앱 이름", "Appearance": "모양", "Application theme, startup page, package icons, clear successful installs automatically": "애플리케이션 테마, 시작 페이지, 패키지 아이콘, 성공한 설치 자동으로 지우기", "Application theme:": "애플리케이션 테마:", "Apply": null, "Architecture to install:": "설치할 아키텍처:", "Are these screenshots wron or blurry?": "이 스크린샷이 잘못되었거나 흐릿합니까?", "Are you really sure you want to enable this feature?": "이 기능을 정말로 켜시겠습니까?", "Are you sure you want to create a new package bundle? ": "새 패키지 번들을 만드시겠습니까?", "Are you sure you want to delete all shortcuts?": "모든 바로 가기를 삭제하시겠습니까?", "Are you sure?": "확실합니까?", "Ascendant": "조상", "Ask for administrator privileges once for each batch of operations": "각 작업 배치마다 한 번씩 관리자 권한 요청", "Ask for administrator rights when required": "필요한 경우 관리자 권한 요청", "Ask once or always for administrator rights, elevate installations by default": "관리자 권한을 한 번 또는 항상 요청하고 설치 권한을 기본적으로 상승합니다.", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "관리자 권한을 한 번만 요청(권장하지 않음)", "Ask to delete desktop shortcuts created during an install or upgrade.": "설치 혹은 업데이트하는 동안에 만든 바탕 화면 바로 가기 삭제할지 물어보기", "Attention required": "주의 필요", "Authenticate to the proxy with an user and a password": "사용자 및 암호를 입력하여 프록시에 인증합니다.", "Author": "작성자", "Automatic desktop shortcut remover": "바탕 화면 바로 가기 자동 제거", "Automatically save a list of all your installed packages to easily restore them.": "설치된 모든 패키지 목록을 자동으로 저장하여 쉽게 복원할 수 있습니다.", "Automatically save a list of your installed packages on your computer.": "설치된 패키지 목록을 컴퓨터에 자동으로 저장합니다.", "Autostart WingetUI in the notifications area": "UniGetUI를 알림 영역에 자동 시작", "Available Updates": "사용 가능한 업데이트", "Available updates: {0}": "사용 가능한 업데이트: {0}", "Available updates: {0}, not finished yet...": "사용 가능한 업데이트: {0}, 아직 업데이트 중...", "Backing up packages to GitHub Gist...": null, "Backup": "백업", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "설치된 패키지 백업", "Backup location": "백업 위치", "Become a contributor": "기여자 되기", "Become a translator": "번역에 기여하기", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "주의해야 할 실험적 기능 및 옵션", "Both": "모두", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "하지만 UniGetUI에 대해 더 자세히 알아볼 수 있는 다른 방법도 있습니다:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "패키지 관리자를 비활성화하면 해당 패키지 관리자의 패키지들을 확인하거나 업데이트할 수 없게 됩니다.", "Cache administrator rights and elevate installers by default": "기본적으로 관리자 권한을 캐싱 및 설치 프로그램 권한 상승", "Cache administrator rights, but elevate installers only when required": "관리자 권한을 캐싱하지만 필요한 경우에만 설치 프로그램 권한을 상승합니다.", "Cache was reset successfully!": "캐시가 성공적으로 초기화 되었습니다!", "Can't {0} {1}": "{1} {0} 할 수 없음", "Cancel": "취소", "Cancel all operations": "모든 작업 취소", "Change backup output directory": "백업 출력 디렉토리 변경", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "UniGetUI가 사용 가능한 업데이트를 확인하고 설치하는 방식을 설정", "Change how UniGetUI handles install, update and uninstall operations.": "UniGetUI가 설치, 업데이트 및 제거를 처리하는 방식을 설정", "Change how UniGetUI installs packages, and checks and installs available updates": "UniGetUI가 패키지를 설치하고, 사용 가능한 업데이트를 확인하고 설치하는 방식을 설정", "Change how operations request administrator rights": "작업이 관리자 권한을 요청하는 방식을 설정", "Change install location": "설치 위치 변경", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "주기적으로 패키지 업데이트 확인", "Check for updates": "업데이트 확인", "Check for updates every:": "업데이트 확인 빈도:", "Check for updates periodically": "주기적으로 업데이트 확인", "Check for updates regularly, and ask me what to do when updates are found.": "정기적으로 업데이트를 확인하고 업데이트가 발견되면 무엇을 할 지 물어봅니다.", "Check for updates regularly, and automatically install available ones.": "정기적으로 업데이트를 확인하고 사용 가능한 업데이트를 자동으로 설치합니다.", "Check out my {0} and my {1}!": "제 {0}과 {1}를 확인해보세요!", "Check out some WingetUI overviews": "UniGetUI 개요를 확인하세요.", "Checking for other running instances...": "실행 중인 다른 인스턴스 확인 중...", "Checking for updates...": "업데이트 확인 중...", "Checking found instace(s)...": "찾은 인스턴스 확인 중...", "Choose how many operations shouls be performed in parallel": "병렬로 수행할 작업의 개수를 선택하세요", "Clear cache": "캐시 지우기", "Clear finished operations": null, "Clear selection": "모두 선택 해제", "Clear successful operations": "성공한 작업 지우기", "Clear successful operations from the operation list after a 5 second delay": "성공한 작업을 작업 목록에서 5초 후에 지우기", "Clear the local icon cache": "로컬 아이콘 캐시 지우기", "Clearing Scoop cache - WingetUI": "Scoop 캐시 지우기 - UniGetUI", "Clearing Scoop cache...": "Scoop 캐시 지우는 중...", "Click here for more details": "여기를 눌러 자세한 정보 확인", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "설치 버튼을 눌러 설치 프로세스를 시작하세요.  설치를 건너뛰면 UniGetUI가 예상대로 작동하지 않을 수 있습니다.", "Close": "닫기", "Close UniGetUI to the system tray": "UniGetUI를 닫으면 시스템 트레이로 내리기", "Close WingetUI to the notification area": "UniGetUI를 알림 영역으로 닫기", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "명령줄 출력", "Command-line to run:": "실행할 명령줄:", "Compare query against": "쿼리 비교", "Compatible with authentication": "인증 호환 여부", "Compatible with proxy": "프록시 호환 여부", "Component Information": "구성 요소 정보", "Concurrency and execution": "동시성 및 실행", "Connect the internet using a custom proxy": "사용자 지정 프록시를 사용하여 인터넷 연결", "Continue": "계속", "Contribute to the icon and screenshot repository": "아이콘 및 스크린샷을 저장소에 기여", "Contributors": "기여자", "Copy": "복사", "Copy to clipboard": "클립보드에 복사", "Could not add source": "소스를 추가할 수 없습니다.", "Could not add source {source} to {manager}": "{manager}에 {source} 소스를 추가할 수 없습니다.", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "번들을 만들 수 없습니다.", "Could not load announcements - ": "공지 사항을 불러올 수 없음 - ", "Could not load announcements - HTTP status code is $CODE": "공지 사항을 불러올 수 없음 - HTTP 상태 코드는 $CODE입니다.", "Could not remove source": "소스를 제거할 수 없습니다.", "Could not remove source {source} from {manager}": "{manager}에서 {source} 소스를 제거할 수 없습니다.", "Could not remove {source} from {manager}": "{manager}에서 {source}(을)를 제거할 수 없습니다.", "Credentials": "자격 증명", "Current Version": "현재 버전", "Current status: Not logged in": null, "Current user": "현재 사용자", "Custom arguments:": "사용자 지정 인자:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "사용자 정의 명령줄 인자:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "UniGetUI 사용자 정의 - 해커 및 고급 사용자 전용", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "면책 조항: 당사는 다운로드한 패키지에 대해 책임을 지지 않습니다. 신뢰할 수 있는 소프트웨어만 설치하시기 바랍니다.", "Dark": "어두운 테마", "Decline": "거부", "Default": "기본값", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "기본 환경 설정 - 일반 사용자에게 적합", "Default vcpkg triplet": "vcpkg 기본값 삼중 변수", "Delete?": "삭제할까요?", "Dependencies:": null, "Descendant": "자손", "Description:": "설명:", "Desktop shortcut created": "바탕 화면 바로 가기 만듦", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "개발은 어렵고 이 애플리케이션은 무료입니다. 하지만 애플리케이션이 마음에 들면 언제든지 <b>제게 커피 한 잔을 사 주세요.</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "패키지 정보를 표시하는 대신 \"{discoveryTab}\" 탭에서 항목을 두 번 클릭하여 바로 설치", "Disable new share API (port 7058)": "새 공유 API 끄기(포트 7058)", "Disable the 1-minute timeout for package-related operations": "패키지 관련 작업에서 1분 시간 제한 끄기", "Disclaimer": "면책 조항", "Discover Packages": "패키지 찾아보기", "Discover packages": "패키지 찾아보기", "Distinguish between\nuppercase and lowercase": "대소문자 구분하기", "Distinguish between uppercase and lowercase": "대소문자 구분하기", "Do NOT check for updates": "업데이트 확인 안 함", "Do an interactive install for the selected packages": "선택한 패키지에 대해 대화형 설치 수행", "Do an interactive uninstall for the selected packages": "선택한 패키지에 대해 대화형 제거 수행", "Do an interactive update for the selected packages": "선택한 패키지에 대해 대화형 업데이트 수행", "Do not automatically install updates when the battery saver is on": "배터리 절약 모드(절전 모드)가 켜져 있을 때 자동 업데이트 안 함", "Do not automatically install updates when the network connection is metered": "데이터 통신 연결 네트워크에서 자동 업데이트 안 함", "Do not download new app translations from GitHub automatically": " GitHub에서 앱 번역 자동 업데이트 안 함", "Do not ignore updates for this package anymore": "이 패키지에 대한 업데이트 무시 안 함", "Do not remove successful operations from the list automatically": "성공한 작업을 목록에서 자동으로 제거 안 함", "Do not show this dialog again for {0}": "{0}에 대해 이 대화 상자를 다시 표시 안 함", "Do not update package indexes on launch": "실행 시 패키지 인덱스 업데이트 안 함", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "사용자 경험을 이해 및 개선하기 위해 UniGetUI가 익명 사용 통계를 수집하고 보내도록 허용하시겠습니까?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "UniGetUI가 유용한가요? 가능하시다면 UniGetUI를 최고의 패키지 관리 인터페이스로 계속 만들 수 있도록 제 작업을 지원해 주세요.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "UniGetUI가 유용한가요? 개발자에게 도움이 되고 싶나요? 그렇다면 {0} 할 수 있습니다. 큰 도움이 됩니다!", "Do you really want to reset this list? This action cannot be reverted.": "정말로 이 목록을 초기화하시겠습니까? 이 동작은 되돌릴 수 없습니다.", "Do you really want to uninstall the following {0} packages?": "다음 {0} 패키지를 정말로 제거하시겠습니까?", "Do you really want to uninstall {0} packages?": "정말로 {0} 패키지를 제거하겠습니까?", "Do you really want to uninstall {0}?": "{0}을(를) 제거하시겠습니까?", "Do you want to restart your computer now?": "컴퓨터를 지금 다시 시작하시겠습니까?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "UniGetUI를 당신의 언어로 번역하고 싶나요? <a style=\"color:{0}\" href=\"{1}\">여기서</a> 어떻게 기여하는지 확인하세요!", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "후원하고 싶지 않으신가요? 괜찮아요, 대신 언제든지 친구들과 UniGetUI를 공유 해 주세요. UniGetUI에 대해 널리 알려주세요.", "Donate": "후원", "Done!": null, "Download failed": "다운로드 실패", "Download installer": "설치 프로그램 다운로드", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "다운로드 성공", "Download updated language files from GitHub automatically": "GitHub에서 업데이트된 언어 파일 자동 다운로드", "Downloading": "다운로드 중", "Downloading backup...": null, "Downloading installer for {package}": "{package} 설치 프로그램 다운로드 중", "Downloading package metadata...": "패키지 메타데이터 다운로드 중...", "Enable Scoop cleanup on launch": "프로그램 실행 시 Scoop 정리", "Enable WingetUI notifications": "UniGetUI 알림 켜기", "Enable an [experimental] improved WinGet troubleshooter": "향상된 WinGet 문제 해결사 사용(실험적)", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "백그라운드 CPU 사용량 최적화(see Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "백그라운드 API 사용(UniGetUI 위젯 및 공유 용도, 포트 7058)", "Enable it to install packages from {pm}.": "{pm}에서 패키지를 설치하려면 켭니다.", "Enable the automatic WinGet troubleshooter": "WinGet 문제 해결사 자동으로 켜기", "Enable the new UniGetUI-Branded UAC Elevator": "UniGetUI로 브랜딩된 새로운 UAC 권한 상승 사용", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "{pm} 켜기", "Enter proxy URL here": "여기에 프록시 URL 입력", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "오류", "Everything is up to date": "모두 최신 상태입니다.", "Exact match": "정확히 일치", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "바탕 화면에 이미 존재하는 바로 가기를 스캔하며, 어떤 바로 가기를 유지하고 어떤 바로 가기를 제거할 지 골라야 합니다.", "Expand version": "버전 확장", "Experimental settings and developer options": "실험적 설정 및 개발자 옵션", "Export": "내보내기", "Export log as a file": "로그를 파일로 내보내기", "Export packages": "패키지 내보내기", "Export selected packages to a file": "선택한 패키지를 파일로 내보내기", "Export settings to a local file": "설정을 로컬 파일로 내보내기", "Export to a file": "파일로 내보내기", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "최신 공지 사항을 가져오는 중입니다. 잠시 기다려 주십시오...", "Filters": "필터", "Finish": "완료", "Follow system color scheme": "시스템 색상 구성표 따르기", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "ARM 컴파일된 winget 버전 강제 사용(ARM64 시스템 전용)", "Formerly known as WingetUI": "이전 명칭: WingetUI", "Found": "찾음", "Found packages: ": "찾은 패키지:", "Found packages: {0}": "찾은 패키지: {0}", "Found packages: {0}, not finished yet...": "찾은 패키지: {0}, 아직 검색 중...", "General preferences": "일반 환경 설정", "GitHub profile": "<PERSON><PERSON><PERSON> 프로필", "Global": "글로벌", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "알려지지 않았지만 유용한 유틸리티 및 기타 흥미로운 패키지의 훌륭한 저장소 <br>포함: <b>유틸리티, 명령줄 프로그램, 일반 소프트웨어(추가 버킷 필요)</b>", "Great! You are on the latest version.": "훌룡해요! 최신 버전을 사용하고 있습니다.", "Grid": "그리드(격자)", "Help": "도움말", "Help and documentation": "도움말 및 문서", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "다음은 바로 가기에 대한 UniGetUI의 동작을 변경할 수 있는 곳입니다. 바로 가기를 선택하면 향후 업그레이드 시 UniGetUI가 해당 바로 가기를 삭제합니다. 선택을 해제하면 바로 가기가 그대로 유지됩니다.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "안녕, 제 이름은 Martí고, UniGetUI <i>개발자</i>예요. UniGetUI는 오로지 제 여가시간에 만들었죠!", "Hide details": "세부 정보 숨기기", "Homepage": "홈페이지", "Hooray! No updates were found.": "야호! 업데이트할 항목이 없습니다.", "How should installations that require administrator privileges be treated?": "관리자 권한이 필요한 설치는 어떻게 처리해야 하나요?", "How to add packages to a bundle": "패키지를 번들에 추가하는 방법", "I understand": "알겠습니다", "Icons": "아이콘", "Id": "아이디", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "이 패키지에 대한 향후 업데이트 무시", "Ignore packages from {pm} when showing a notification about updates": "업데이트 알림을 표시할 때 {pm}에서 패키지 무시", "Ignore selected packages": "선택한 패키지 무시", "Ignore special characters": "특수 문자 무시", "Ignore updates for the selected packages": "선택한 패키지에 대한 업데이트 무시", "Ignore updates for this package": "이 패키지에 대한 업데이트 무시", "Ignored updates": "무시된 업데이트", "Ignored version": "무시된 버전", "Import": "가져오기", "Import packages": "패키지 가져오기", "Import packages from a file": "파일에서 패키지 가져오기", "Import settings from a local file": "로컬 파일에서 설정 가져오기", "In order to add packages to a bundle, you will need to: ": "패키지를 번들에 추가하려면 다음이 필요합니다:", "Initializing WingetUI...": "UniGetUI 초기 설정 중...", "Install": "설치", "Install Scoop": "Scoop 설치", "Install and more": null, "Install and update preferences": "설치 및 업데이트 환경 설정", "Install as administrator": "관리자 권한으로 설치", "Install available updates automatically": "사용 가능한 업데이트 자동으로 설치", "Install location can't be changed for {0} packages": null, "Install location:": "설치 위치:", "Install options": null, "Install packages from a file": "파일로부터 패키지 설치", "Install prerelease versions of UniGetUI": "UniGetUI의 시험판 버전 설치", "Install selected packages": "선택한 패키지 설치", "Install selected packages with administrator privileges": "선택한 패키지를 관리자 권한으로 설치", "Install selection": "선택 항목 설치", "Install the latest prerelease version": "최신 시험판 버전 설치", "Install updates automatically": "자동으로 업데이트 설치", "Install {0}": "{0} 설치", "Installation canceled by the user!": "사용자가 설치 작업을 취소했습니다!", "Installation failed": "설치 실패", "Installation options": "설치 옵션", "Installation scope:": "설치 범위:", "Installation succeeded": "설치 성공", "Installed Packages": "설치된 패키지", "Installed Version": "설치된 버전", "Installed packages": "설치된 패키지", "Installer SHA256": "설치 프로그램의 SHA256", "Installer SHA512": "설치 프로그램의 SHA512", "Installer Type": "설치 프로그램 유형", "Installer URL": "설치 프로그램 URL", "Installer not available": "설치 프로그램을 사용할 수 없음", "Instance {0} responded, quitting...": "인스턴스 {0}이(가) 응답하여 끝내는 중입니다...", "Instant search": "즉시 검색", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "무결성 검사 건너뜀.", "Integrity checks will not be performed during this operation": "이 작업 중에는 무결성 검사를 수행하지 않습니다.", "Interactive installation": "대화형 설치", "Interactive operation": "대화형 작업", "Interactive uninstall": "대화형 제거", "Interactive update": "대화형 업데이트", "Internet connection settings": "인터넷 연결 설정", "Is this package missing the icon?": "이 패키지에 아이콘이 없습니까?", "Is your language missing or incomplete?": "언어가 누락되었거나 불완전합니까?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "제공된 자격 증명이 안전하게 저장된다고 보장할 수 없으므로 인터넷 뱅킹의 자격 증명을 사용하지 않는 것이 좋습니다.", "It is recommended to restart UniGetUI after WinGet has been repaired": "WinGet이 복구된 후에는 UniGetUI를 다시 시작하는 것을 권장합니다.", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "WinGet이 올바르게 작동하지 않는 것 같습니다. WinGet 복구를 시도하시겠어요?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "UniGetUI를 관리자 권한으로 실행한 것 같습니다. 그러나 이는 권장하지 않습니다. 프로그램을 계속 사용할 수는 있지만 UniGetUI를 관리자 권한으로 실행하지 않는 것이 좋습니다. 이유를 확인하려면 \"{showDetails}\"를 클릭하세요.", "Language": "언어", "Language, theme and other miscellaneous preferences": "언어, 테마 및 기타 환경 설정", "Last updated:": "마지막 업데이트:", "Latest": "최신", "Latest Version": "최신 버전", "Latest Version:": "최신 버전:", "Latest details...": "최신 세부 정보...", "Launching subprocess...": "하위 프로세스 실행 중...", "Leave empty for default": "공백은 기본값", "License": "라이선스", "Licenses": "라이선스", "Light": "밝은 테마", "List": "리스트", "Live command-line output": "실시간 명령줄 출력", "Live output": "실시간 출력", "Loading UI components...": "UI 구성 요소 불러오는 중...", "Loading WingetUI...": "UniGetUI 불러오는 중...", "Loading packages": "패키지 불러오는 중", "Loading packages, please wait...": "패키지를 불러오는 중입니다. 잠시만 기다려주세요...", "Loading...": "불러오는 중...", "Local": "로컬", "Local PC": "로컬 PC", "Local backup advanced options": null, "Local machine": "로컬 컴퓨터", "Local package backup": null, "Locating {pm}...": "{pm} 찾는 중...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "로그 수준:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "패키지를 찾는 중...", "Machine | Global": "컴퓨터 | 글로벌", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "관리", "Manage UniGetUI settings": "UniGetUI 설정 관리", "Manage WingetUI autostart behaviour from the Settings app": "[설정] 앱에서 UniGetUI 자동 시작 동작 관리", "Manage ignored packages": "무시된 패키지 관리", "Manage ignored updates": "무시된 업데이트 관리", "Manage shortcuts": "바로 가기 관리", "Manage telemetry settings": "진단 설정 관리", "Manage {0} sources": "{0} 소스 관리", "Manifest": "매니페스트", "Manifests": "매니페스트", "Manual scan": "수동 스캔", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "마이크로소프트의 공식 패키지 관리자. 잘 알려져 있고 검증된 패키지로 가득합니다.<br>포함 내용: <b>일반 소프트웨어, Microsoft Store 앱</b>", "Missing dependency": "누락된 의존성", "More": "자세히 보기", "More details": "자세한 정보", "More details about the shared data and how it will be processed": "공유 데이터 및 처리 방식에 대한 자세한 정보", "More info": "자세한 정보", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "참고: 이 문제 해결사는 UniGetUI 설정의 WinGet 섹션에서 끌 수 있습니다.", "Name": "이름", "New": null, "New Version": "새 버전", "New bundle": "새 번들", "New version": "새 버전", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "아니요", "No applicable installer was found for the package {0}": "패키지 {0}에 적용 가능한 설치 프로그램을 찾을 수 없습니다.", "No dependencies specified": null, "No new shortcuts were found during the scan.": "스캔하는 동안 새 바로 가기를 찾지 못했습니다.", "No packages found": "패키지를 찾을 수 없습니다.", "No packages found matching the input criteria": "입력 조건과 일치하는 패키지를 찾을 수 없습니다.", "No packages have been added yet": "아직 패키지가 추가되지 않았습니다", "No packages selected": "선택한 패키지 없음", "No packages were found": "패키지를 찾을 수 없습니다", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "개인 사용자 정보는 수집되지도 전송되지 않으며 역추적할 수 없도록 익명 처리되어 수집됩니다.", "No results were found matching the input criteria": "입력 조건과 일치하는 결과를 찾을 수 없습니다.", "No sources found": "소스를 찾을 수 없음", "No sources were found": "소스를 찾을 수 없습니다.", "No updates are available": "사용 가능한 업데이트 없음", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS의 패키지 관리자. 자바스크립트와 관련된 라이브러리와 기타 유틸리티로 가득합니다. <br>포함 내용: <b>노드 자바스크립트 라이브러리 및 기타 관련 유틸리티</b>", "Not available": "사용할 수 없음", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "찾을 수 없음", "Not right now": "지금은 아님", "Notes:": "참고:", "Notification preferences": "알림 환경 설정", "Notification tray options": "알림 영역 옵션", "Notification types": "알림 종류", "NuPkg (zipped manifest)": "NuPkg(압축된 매니페스트)", "OK": "확인", "Ok": "확인", "Open": "열기", "Open GitHub": "GitHub 열기", "Open UniGetUI": "UniGetUI 열기", "Open UniGetUI security settings": null, "Open WingetUI": "UniGetUI 열기", "Open backup location": "백업 위치 열기", "Open existing bundle": "기존 번들 열기", "Open install location": "설치 위치 열기", "Open the welcome wizard": "시작 마법사 열기", "Operation canceled by user": "사용자가 작업을 취소했습니다.", "Operation cancelled": "작업 취소됨", "Operation history": "작업 기록", "Operation in progress": "작업 진행 중", "Operation on queue (position {0})...": "작업 대기 중(위치 {0})...", "Operation profile:": null, "Options saved": "옵션이 저장되었습니다", "Order by:": "정렬:", "Other": "기타", "Other settings": "기타 설정", "Package": null, "Package Bundles": "패키지 번들", "Package ID": "패키지 ID", "Package Manager": "패키지 관리자", "Package Manager logs": "패키지 관리자 로그", "Package Managers": "패키지 관리자", "Package Name": "패키지 이름", "Package backup": "패키지 백업", "Package backup settings": null, "Package bundle": "패키지 번들", "Package details": "패키지 세부 정보", "Package lists": "패키지 목록", "Package management made easy": "패키지 관리가 더욱 쉬워졌습니다.", "Package manager": "패키지 관리", "Package manager preferences": "패키지 관리자 환경 설정", "Package managers": "패키지 관리자", "Package not found": "패키지를 찾을 수 없음", "Package operation preferences": "패키지 작업 환경 설정", "Package update preferences": "패키지 업데이트 환경 설정", "Package {name} from {manager}": "{manager}의 {name} 패키지", "Package's default": null, "Packages": "패키지", "Packages found: {0}": "찾은 패키지: {0}", "Partially": "부분적", "Password": "암호", "Paste a valid URL to the database": "유효한 URL을 데이터베이스에 붙여넣으세요.", "Pause updates for": null, "Perform a backup now": "지금 백업 수행", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "백업을 수행하는 중. 잠시만 기다려주세요...", "Periodically perform a backup of the installed packages": "설치된 패키지의 백업을 주기적으로 수행", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "이 패키지의 설치 옵션을 확인하고 다시 시도해주세요.", "Please click on \"Continue\" to continue": "계속하려면 [계속]을 클릭하세요.", "Please enter at least 3 characters": "3자 이상 입력하세요", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "이 컴퓨터에서 사용 중인 패키지 관리자로 인해 특정 패키지를 설치하지 못할 수도 있습니다.", "Please note that not all package managers may fully support this feature": "이 모든 패키지 관리자가 기능을 완벽하게 지원하지는 않습니다.", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "특정 소스의 패키지는 내보내지 못할 수 있습니다. 해당 패키지는 회색으로 표시되어 내보낼 수 없습니다.", "Please run UniGetUI as a regular user and try again.": "UniGetUI를 일반 사용자로 실행하고 다시 시도해주세요.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "이 문제에 대한 자세한 내용은 [명령줄 출력]을 참조하거나 [작업 기록]을 참조하세요.", "Please select how you want to configure WingetUI": "UniGetUI를 구성하는 방법을 선택하세요.", "Please try again later": "나중에 다시 시도해주세요.", "Please type at least two characters": "두 글자 이상 입력하세요.", "Please wait": "잠시만 기다려주세요", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "{0}을(를) 설치하는 동안 잠시만 기다려주세요. 검은(또는 파란) 창이 표시될 수 있습니다. 닫힐 때까지 기다려주세요.", "Please wait...": "잠시만 기다려주세요...", "Portable": "포터블 ", "Portable mode": "포터블 모드", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell의 패키지 관리자. PowerShell 기능을 확장할 라이브러리 및 스크립트를 찾아보세요. <br>포함 내용: <b>모듈, 스크립트, Cmdlet</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "시험판", "Preparing packages, please wait...": "패키지 준비 중, 잠시만 기다려주세요...", "Proceed at your own risk.": "모든 책임은 사용자에게 있습니다.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "프록시 URL", "Proxy compatibility table": "프록시 호환성 표", "Proxy settings": "프록시 설정", "Proxy settings, etc.": "프록시 설정 등", "Publication date:": "게시 날짜:", "Publisher": "게시자", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python 라이브러리 관리자. Python 라이브러리 및 기타 Python 관련 유틸리티가 가득합니다<br>포함 내용: <b>Python 라이브러리 및 관련 유틸리티</b>", "Quit": "끝내기", "Quit WingetUI": "UniGetUI 끝내기", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "재설치", "Reinstall package": "패키지 재설치", "Related settings": "관련 설정", "Release notes": "릴리스 노트", "Release notes URL": "릴리스 노트 URL", "Release notes URL:": "릴리스 노트 URL:", "Release notes:": "릴리스 노트:", "Reload": "다시 불러오기", "Reload log": "로그 다시 불러오기", "Removal failed": "제거 실패", "Removal succeeded": "제거 성공", "Remove from list": "목록에서 제거", "Remove permanent data": "저장된 데이터 제거", "Remove selection from bundle": "선택 항목을 번들에서 제거", "Remove successful installs/uninstalls/updates from the installation list": "설치/제거/업데이트 성공한 항목을 설치 목록에서 삭제", "Removing source {source}": "{source} 소스 제거", "Removing source {source} from {manager}": "{manager}에서 {source} 소스 제거", "Repair UniGetUI": null, "Repair WinGet": "WinGet 복구", "Report an issue or submit a feature request": "문제 신고 또는 기능 요청 제출", "Repository": "저장소", "Reset": "초기화", "Reset Scoop's global app cache": "Scoop 글로벌 앱 캐시 초기화", "Reset UniGetUI": "UniGetUI 초기화", "Reset WinGet": "WinGet 초기화", "Reset Winget sources (might help if no packages are listed)": "Winget 소스 초기화(패키지가 목록에 표시되지 않는 경우 도움이 될 수 있음)", "Reset WingetUI": "UniGetUI 초기화", "Reset WingetUI and its preferences": "UniGetUI 및 설정 초기화", "Reset WingetUI icon and screenshot cache": "UniGetUI 아이콘 및 스크린샷 캐시 초기화", "Reset list": "목록 초기화", "Resetting Winget sources - WingetUI": "WinGet 소스 초기화 - UniGetUI", "Restart": "다시 시작", "Restart UniGetUI": "UniGet 다시 시작", "Restart WingetUI": "UniGetUI 다시 시작", "Restart WingetUI to fully apply changes": "변경 사항을 완전히 적용하려면 UniGetUI를 다시 시작하세요.", "Restart later": "나중에 다시 시작", "Restart now": "지금 다시 시작", "Restart required": "다시 시작 필요", "Restart your PC to finish installation": "설치를 완료하려면 PC를 다시 시작하세요.", "Restart your computer to finish the installation": "설치를 완료하려면 컴퓨터를 다시 시작하십시오.", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "다시 시도", "Retry as administrator": "관리자 권한으로 다시 시도", "Retry failed operations": "실패한 작업 다시 시도", "Retry interactively": "대화형으로 다시 시도", "Retry skipping integrity checks": "건너뛴 무결성 검사 다시 시도", "Retrying, please wait...": "다시 시도 중. 잠시만 기다려주세요...", "Return to top": "맨 위로 돌아가기", "Run": "실행 ", "Run as admin": "관리자 권한으로 실행", "Run cleanup and clear cache": "정리 실행 및 캐시 지우기", "Run last": "이전 항목 실행", "Run next": "다음 항목 실행", "Run now": "지금 실행", "Running the installer...": "설치 프로그램 실행 중...", "Running the uninstaller...": "제거 프로그램 실행 중...", "Running the updater...": "업데이트 프로그램 실행 중...", "Save": null, "Save File": "파일 저장", "Save and close": "저장하고 닫기", "Save as": null, "Save bundle as": "번들을 다른 이름으로 저장", "Save now": "지금 저장", "Saving packages, please wait...": "패키지 저장 중. 잠시만 기다려주세요...", "Scoop Installer - WingetUI": "Scoop 설치 프로그램 - UniGetUI", "Scoop Uninstaller - WingetUI": "Scoop 제거 프로그램 - UniGetUI", "Scoop package": "Scoop 패키지", "Search": "검색", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "바탕 화면 소프트웨어를 검색해서 업데이트가 있을 때만 알리고 다른 일은 안 합니다. UniGetUI가 복잡해지지 않고 단순히 <b>소프트웨어 스토어</b>이도록 합니다.", "Search for packages": "패키지 검색", "Search for packages to start": "시작할 패키지 검색", "Search mode": "검색 모드", "Search on available updates": "사용 가능한 업데이트 검색", "Search on your software": "설치된 소프트웨어 검색", "Searching for installed packages...": "설치된 패키지 검색 중...", "Searching for packages...": "패키지 검색 중...", "Searching for updates...": "업데이트 검색 중...", "Select": "선택", "Select \"{item}\" to add your custom bucket": "사용자 버킷을 추가하려면 \"{item}\"을(를) 선택하세요.", "Select a folder": "폴더 선택", "Select all": "모두 선택", "Select all packages": "모든 패키지 선택", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<b>자신이 무엇을 하고 있는지 알고 있는 경우에만</b> 선택합니다.", "Select package file": "패키지 파일 선택", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "추가하고 싶은 소스를 선택하세요:", "Select upgradable packages by default": "기본적으로 업그레이드 가능한 패키지 선택", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "사용할 <b>패키지 관리자</b> 선택({0}), 패키지 설치 방법 구성, 관리자 권한 처리 방법 등을 관리합니다.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "핸드셰이크를 보내고 인스턴스 응답을 기다리는 중...({0}%)\n", "Set a custom backup file name": "사용자 정의 백업 파일 이름 설정", "Set custom backup file name": "사용자 정의 백업 파일 이름 설정", "Settings": "설정", "Share": "공유", "Share WingetUI": "UniGetUI 공유하기", "Share anonymous usage data": "익명 사용 데이터 공유", "Share this package": "이 패키지 공유", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "시스템 트레이에 UniGetUI 표시", "Show UniGetUI's version and build number on the titlebar.": "UniGetUI 버전 제목 표시줄에 보이기", "Show WingetUI": "UniGetUI 창 표시", "Show a notification when an installation fails": "설치 실패 시 알림 표시", "Show a notification when an installation finishes successfully": "설치가 성공적으로 완료되면 알림 표시", "Show a notification when an operation fails": "작업 실패 시 알림 표시", "Show a notification when an operation finishes successfully": "작업이 성공적으로 완료되면 알림 표시", "Show a notification when there are available updates": "사용 가능한 업데이트가 있을 때 알림 표시", "Show a silent notification when an operation is running": "작업 실행 중일 때 조용한 알림 표시", "Show details": "세부 정보 보기", "Show in explorer": "탐색기에 표시", "Show info about the package on the Updates tab": "[업데이트] 탭에서 패키지에 대한 정보 표시", "Show missing translation strings": "번역되지 않은 문자열 표시", "Show notifications on different events": "다양한 이벤트에 대한 알림 표시", "Show package details": "패키지 세부 정보 표시", "Show package icons on package lists": "패키지 목록에 패키지 아이콘 표시", "Show similar packages": "유사한 패키지 표시", "Show the live output": "실시간 출력 표시하기", "Size": "크기", "Skip": "건너뛰기", "Skip hash check": "해시 검사 건너뛰기", "Skip hash checks": "해시 검사 건너뛰기", "Skip integrity checks": "무결성 검사 건너뛰기", "Skip minor updates for this package": "이 패키지의 마이너 업데이트 건너뛰기", "Skip the hash check when installing the selected packages": "선택한 패키지를 설치할 때 해시 확인 건너뛰기", "Skip the hash check when updating the selected packages": "선택한 패키지를 업데이트할 때 해시 확인 건너뛰기", "Skip this version": "이 버전 건너뛰기", "Software Updates": "소프트웨어 업데이트", "Something went wrong": "문제가 발생했습니다.", "Something went wrong while launching the updater.": "업데이트 프로그램을 실행하는 도중 문제가 발생했습니다.", "Source": "공급자", "Source URL:": "공급자 URL", "Source added successfully": "공급자를 성공적으로 추가했습니다.", "Source addition failed": "공급자 추가 실패", "Source name:": "공급자 이름", "Source removal failed": "공급자 제거 실패", "Source removed successfully": "공급자를 성공적으로 제거했습니다.", "Source:": "공급자:", "Sources": "공급자", "Start": "시작", "Starting daemons...": "서비스 시작 중...", "Starting operation...": "작업 시작 중...", "Startup options": "시작 옵션", "Status": "상태", "Stuck here? Skip initialization": "여기서 멈췄나요? 초기 설정 건너뛰기", "Suport the developer": "개발자 지원하기", "Support me": "지원해주세요", "Support the developer": "개발자 지원하기", "Systems are now ready to go!": "이제 시스템을 사용할 준비가 되었습니다!", "Telemetry": "진단", "Text": "텍스트", "Text file": "텍스트 파일", "Thank you ❤": "고마워요❤", "Thank you 😉": "고마워요😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust의 패키지 관리자.<br>포함 내용: <b>Rust 라이브러리 및 Rust로 작성된 프로그램들</b>", "The backup will NOT include any binary file nor any program's saved data.": "백업에는 바이너리 파일이나 프로그램의 저장 데이터가 포함되지 않습니다.", "The backup will be performed after login.": "로그인 후 백업이 수행됩니다.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "백업에는 설치된 패키지의 전체 목록과 해당 설치 옵션이 포함됩니다. 무시된 업데이트와 건너뛴 버전도 저장됩니다.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "불러오려는 번들이 유효하지 않은 것으로 보입니다. 파일을 확인하고 다시 시도해주세요.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "설치 프로그램의 체크섬이 예상 값과 일치하지 않아 설치 프로그램의 진위 여부를 확인할 수 없습니다. 게시자를 신뢰하는 경우 {0} 패키지의 해시 확인을 건너뛰세요.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Windows용 클래식 패키지 관리자. 뭐든지 찾을 수 있습니다. <br>포함 내용: <b>일반 소프트웨어</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "현재 번들에 패키지가 없습니다. 시작하려면 패키지를 추가하세요.", "The executable file for {0} was not found": "{0}에 대한 실행 파일을 찾을 수 없습니다.", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "다음 패키지를 JSON 파일로 내보냅니다. 사용자 데이터나 바이너리는 저장되지 않습니다.", "The following packages are going to be installed on your system.": "다음 패키지가 시스템에 설치됩니다.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "이 패키지를 설치, 업데이트 또는 제거할 때마다 다음 설정이 적용됩니다.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "이 패키지를 설치, 업데이트 또는 제거할 때마다 다음 설정이 적용됩니다. 이 설정은 자동으로 저장됩니다.", "The icons and screenshots are maintained by users like you!": "아이콘과 스크린샷은 여러분과 같은 사용자들에 의해 유지되고 있습니다!", "The installer authenticity could not be verified.": "설치 프로그램의 신뢰성을 검증할 수 없습니다.", "The installer has an invalid checksum": "설치 프로그램에 잘못된 체크섬이 있습니다.", "The installer hash does not match the expected value.": "설치 프로그램의 해시가 예상한 값과 일치하지 않습니다.", "The local icon cache currently takes {0} MB": "로컬 아이콘 캐시가 {0} MB를 차지하고 있습니다.", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "이 프로젝트의 주요 목표는 Winget 및 Scoop과 같은 가장 일반적인 Windows용 CLI 패키지 관리자를 관리하기 위한 직관적인 UI를 만드는 것입니다.", "The package \"{0}\" was not found on the package manager \"{1}\"": "패키지 관리자 \"{1}\"에서 \"{0}\" 패키지를 찾을 수 없습니다.", "The package bundle could not be created due to an error.": "오류로 인해 패키지 번들을 만들 수 없습니다.", "The package bundle is not valid": "패키지 번들이 유효하지 않습니다.", "The package manager \"{0}\" is disabled": "패키지 관리자 \"{0}\"이(가) 꺼져 있습니다.", "The package manager \"{0}\" was not found": "패키지 관리자 \"{0}\"을(를) 찾을 수 없습니다.", "The package {0} from {1} was not found.": "{1}에서 {0} 패키지를 찾을 수 없습니다.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "여기에 나열된 패키지는 업데이트를 확인할 때 고려되지 않습니다. 업데이트 무시를 중지하려면 해당 항목을 두 번 클릭하거나 오른쪽에 있는 버튼을 클릭하십시오.", "The selected packages have been blacklisted": "선택한 패키지를 블랙리스트에 추가했습니다.", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "백업 크기는 1MB 미만으로 예상됩니다.", "The source {source} was added to {manager} successfully": "공급자 {source}을(를) {manager}에 성공적으로 추가했습니다.", "The source {source} was removed from {manager} successfully": "공급자 {source}을(를) {manager}에서 성공적으로 제거했습니다.", "The system tray icon must be enabled in order for notifications to work": "알림이 작동하려면 시스템 트레이 아이콘을 켜야 합니다.", "The update process has been aborted.": "업데이트 프로세스가 중단되었습니다.", "The update process will start after closing UniGetUI": "UniGetUI가 닫힌 후에 업데이트 프로세스가 시작됩니다.", "The update will be installed upon closing WingetUI": "UniGetUI를 닫으면 업데이트가 설치됩니다.", "The update will not continue.": "업데이트를 계속하지 않습니다.", "The user has canceled {0}, that was a requirement for {1} to be run": "사용자가 {0}을(를) 취소했습니다. {1}을(를) 실행하려면 {0}이(가) 필요합니다.", "There are no new UniGetUI versions to be installed": "설치 가능한 새 UniGetUI 버전이 없습니다.", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "진행 중인 작업이 있습니다. UniGetUI를 끝내면 작업이 실패할 수 있습니다. 계속할까요?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "YouTube에는 UniGetUI와 그 기능을 보여주는 멋진 동영상이 있습니다. 유용한 방법과 팁을 배울 수 있습니다!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "UniGetUI를 관리자로 실행하지 않는 데에는 두 가지 이유가 있습니다. 첫 번째는 Scoop 패키지 관리자가 관리자 권한으로 실행할 때 일부 명령에 문제가 발생할 수 있다는 것입니다. UniGetUI를 관리자 권한으로 실행하면 다운로드하는 모든 패키지가 관리자 권한으로 실행됩니다(안전하지 않음). 특정 패키지를 관리자 권한으로 설치해야 하는 경우 항목을 마우스 오른쪽 버튼으로 클릭하여 관리자 권한으로 설치/업데이트/제거할 수 있습니다.", "There is an error with the configuration of the package manager \"{0}\"": "패키지 관리자 \"{0}\" 구성에 오류가 발생했습니다.", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "설치가 진행 중입니다. UniGetUI를 닫으면 설치가 실패하고 예기치 않은 결과가 발생할 수 있습니다. 그래도 UniGetUI를 끝내시겠습니까?", "They are the programs in charge of installing, updating and removing packages.": "패키지 설치, 업데이트 및 제거를 담당하는 프로그램입니다.", "Third-party licenses": "제3자 라이선스", "This could represent a <b>security risk</b>.": "이는 <b>보안 위험</b>을 발생시킬 수 있습니다.", "This is not recommended.": "권장하지 않습니다.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "이는 보낸 패키지가 제거되었거나 사용하도록 설정하지 않은 패키지 관리자에 게시되었기 때문일 수 있습니다. 받은 ID는 {0}입니다.", "This is the <b>default choice</b>.": "이것은 <b>기본 선택</b>입니다.", "This may help if WinGet packages are not shown": "WinGet 패키지가 표시되지 않으면 도움이 될 수 있습니다.", "This may help if no packages are listed": "패키지가 표시되지 않으면 도움이 될 수 있습니다.", "This may take a minute or two": "1~2분 소요될 수 있습니다.", "This operation is running interactively.": "이 작업은 대화형으로 실행 중입니다.", "This operation is running with administrator privileges.": "이 작업은 관리자 권한으로 실행 중입니다.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "이 패키지를 업데이트할 수 있습니다.", "This package can be updated to version {0}": "이 패키지를 {0} 버전으로 업데이트할 수 있습니다.", "This package can be upgraded to version {0}": "이 패키지는 {0} 버전으로 업그레이드할 수 있습니다.", "This package cannot be installed from an elevated context.": "이 패키지는 권한 상승된 컨텍스트에서는 설치할 수 없습니다.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "이 패키지가 스크린샷 또는 아이콘이 없나요? 우리의 오픈 및 공용 데이터베이스에 누락된 아이콘 및 스크린샷을 추가하여 UniGetUI에 기여하세요.", "This package is already installed": "이 패키지는 이미 설치되어 있습니다", "This package is being processed": "이 패키지는 처리 중입니다.", "This package is not available": "이 패키지는 사용할 수 없습니다.", "This package is on the queue": "이 패키지는 대기열에 있습니다.", "This process is running with administrator privileges": "이 프로세스는 관리자 권한으로 실행 중입니다.", "This project has no connection with the official {0} project — it's completely unofficial.": "이 프로젝트는 공식 {0} 프로젝트와 관련이 없습니다. — 완전히 비공식적인 프로젝트입니다.", "This setting is disabled": "이 설정은 꺼져 있습니다.", "This wizard will help you configure and customize WingetUI!": "이 마법사는 UniGetUI를 구성하고 사용자 지정하는 데 도움이 됩니다!", "Toggle search filters pane": "검색 필터 창 전환", "Translators": "번역자", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "여기에 추가하려는 소스의 이름과 URL을 공백으로 구분하여 입력하세요.", "Unable to find package": "패키지를 찾을 수 없습니다.", "Unable to load informarion": "패키지 정보를 불러올 수 없음", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI는 사용자 경험을 개선하기 위해 익명 사용 데이터를 수집합니다.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI는 오로지 사용자 경험을 이해 및 개선하기 위해 익명 사용 통계를 수집합니다.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI가 자동으로 삭제할 수 있는 새 바탕 화면 바로 가기를 감지했습니다.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI가 향후 업그레이드 시 자동으로 제거할 수 있는 바탕 화면 바로 가기를 다음과 같이 감지했습니다.", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI가 자동으로 삭제할 수 있는 {0}개의 새 바탕 화면 바로 가기를 감지했습니다.", "UniGetUI is being updated...": "UniGetUI를 업데이트하는 중...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI는 호환되는 패키지 관리자와 관련이 없습니다. UniGetUI는 독립적인 프로젝트입니다.", "UniGetUI on the background and system tray": "백그라운드 및 시스템 트레이의 UniGetUI", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI가 작업을 실행하려면 {0}이(가) 필요하지만 시스템에서 찾을 수 없습니다.", "UniGetUI startup page:": "UniGetUI 시작 페이지", "UniGetUI updater": "UniGetUI 업데이트 프로그램", "UniGetUI version {0} is being downloaded.": "UniGetUI {0} 버전을 다운로드하고 있습니다.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} 설치가 준비되었습니다.", "Uninstall": "제거", "Uninstall Scoop (and its packages)": "Scoop 제거 및 Scoop으로 설치된 패키지 제거", "Uninstall and more": null, "Uninstall and remove data": "제거 및 데이터 삭제", "Uninstall as administrator": "관리자 권한으로 제거", "Uninstall canceled by the user!": "사용자가 제거 작업을 취소했습니다!", "Uninstall failed": "제거 실패", "Uninstall options": null, "Uninstall package": "패키지 제거", "Uninstall package, then reinstall it": "패키지 제거 후 재설치", "Uninstall package, then update it": "패키지 제거 후 업데이트", "Uninstall previous versions when updated": null, "Uninstall selected packages": "선택한 패키지 제거", "Uninstall selection": null, "Uninstall succeeded": "제거 성공", "Uninstall the selected packages with administrator privileges": "선택한 패키지를 관리자 권한으로 제거합니다.", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "원본이 \"{0}\"(으)로 나열된 제거할 수 없는 패키지는 패키지 관리자에 게시되지 않으므로, 해당 패키지에 대해 불러올 정보가 없습니다.", "Unknown": "알 수 없음", "Unknown size": "알 수 없는 크기", "Unset or unknown": "설정 안 됨 또는 알 수 없음", "Up to date": "최신 상태", "Update": "업데이트", "Update WingetUI automatically": "자동으로 UniGetUI 업데이트", "Update all": "모두 업데이트", "Update and more": null, "Update as administrator": "관리자 권한으로 업데이트", "Update check frequency, automatically install updates, etc.": "업데이트 주기적 확인, 업데이트 자동 설치 등", "Update date": "업데이트 날짜", "Update failed": "업데이트 실패", "Update found!": "업데이트를 찾았습니다!", "Update now": "지금 업데이트", "Update options": null, "Update package indexes on launch": "실행 시 업데이트 패키지 인덱싱", "Update packages automatically": "자동으로 패키지 업데이트", "Update selected packages": "선택한 패키지 업데이트", "Update selected packages with administrator privileges": "선택한 패키지를 관리자 권한으로 업데이트", "Update selection": null, "Update succeeded": "업데이트 성공", "Update to version {0}": "{0} 버전으로 업데이트", "Update to {0} available": "{0}(으)로 업데이트 가능", "Update vcpkg's Git portfiles automatically (requires Git installed)": "vcpkg의 Git 포트파일 자동 업데이트(Git 설치 필요)", "Updates": "업데이트", "Updates available!": "업데이트를 사용할 수 있음!", "Updates for this package are ignored": "이 패키지에 대한 업데이트 무시", "Updates found!": "업데이트를 찾았습니다!", "Updates preferences": "업데이트 환경 설정", "Updating WingetUI": "UniGetUI 업데이트", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "PowerShell CMDLet 대신 레거시 번들 WinGet 사용", "Use a custom icon and screenshot database URL": "사용자 지정 아이콘 및 스크린샷 데이터베이스 URL 사용", "Use bundled WinGet instead of PowerShell CMDlets": "PowerShell CMDLet 대신 번들 WinGet 사용", "Use bundled WinGet instead of system WinGet": "시스템 WinGet 대신 번들 WinGet 사용", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "번들 GSudo 대신 설치된 GSudo 사용", "Use system Chocolatey": "시스템 Chocolatey 사용", "Use system Chocolatey (Needs a restart)": "시스템 Chocolatey 사용(다시 시작 필요)", "Use system Winget (Needs a restart)": "시스템 Winget 사용(다시 시작 필요)", "Use system Winget (System language must be set to english)": "시스템 WinGet 사용(시스템 언어가 영어여야 함)", "Use the WinGet COM API to fetch packages": "패키지를 페치하기 위해 WinGet COM API 사용", "Use the WinGet PowerShell Module instead of the WinGet COM API": "WinGet COM API 대신 WinGet PowerShell 모듈 사용", "Useful links": "유용한 링크", "User": "사용자", "User interface preferences": "사용자 인터페이스 환경 설정", "User | Local": "사용자 | 로컬", "Username": "사용자 이름", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "UniGetUI를 사용하는 것은 GNU Lesser General Public License v2.1 라이선스에 동의하는 것입니다.", "Using WingetUI implies the acceptation of the MIT License": "UniGetUI를 사용하는 것은 MIT 라이선스에 동의하는 것입니다.", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg 최상위 경로를 찾을 수 없습니다. UniGetUI에서 설정하거나 %VCPKG_ROOT% 환경 변수를 정의해주세요.", "Vcpkg was not found on your system.": "Vcpkg를 시스템에서 찾을 수 없습니다.", "Verbose": "세부 정보", "Version": "버전", "Version to install:": "설치할 버전:", "Version:": null, "View GitHub Profile": "GitHub 프로필 보기", "View WingetUI on GitHub": "GitHub에서 UniGetUI 보기", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "UniGetUI의 소스 코드를 확인하세요. 여기에서 버그를 보고하거나 기능을 제안할 수 있으며, UniGetUI 프로젝트에 직접 기여할 수도 있습니다.", "View mode:": "보기 모드:", "View on UniGetUI": "UniGetUI에서 보기", "View page on browser": "브라우저에서 페이지 보기", "View {0} logs": "{0} 로그 보기", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "인터넷 연결이 필요한 작업을 수행하기 전에 장치가 인터넷에 연결될 때까지 기다리기.", "Waiting for other installations to finish...": "다른 설치 작업이 끝날 때까지 기다리는 중...", "Waiting for {0} to complete...": "완료하기 위해 {0} 기다리는 중...", "Warning": "경고", "Warning!": "경고!", "We are checking for updates.": "업데이트를 확인하고 있습니다.", "We could not load detailed information about this package, because it was not found in any of your package sources": "패키지 공급자에서 이 패키지에 대한 자세한 정보를 찾을 수 없으므로 불러올 수 없습니다.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "사용 가능한 패키지 관리자에서 설치되지 않았기 때문에 이 패키지에 대한 자세한 정보를 불러올 수 없습니다.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "{package} {action} 할 수 없습니다. 나중에 다시 시도 해주십시오. 설치 프로그램에서 로그를 가져오라면 \"{showDetails}\"(을)를 클릭하십시오.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "{package}을(를) {action}할 수 없습니다. 나중에 다시 시도하십시오. 제거 프로그램에서 로그를 가져오려면 \"{showDetails}\"를 클릭하십시오.", "We couldn't find any package": "아무 패키지도 찾을 수 없습니다.", "Welcome to WingetUI": "UniGetUI에 오신 것을 환영합니다", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "새 바로 가기가 감지되면 이 대화 상자를 표시하지 않고 자동으로 지우기", "Which backup do you want to open?": null, "Which package managers do you want to use?": "어떤 패키지 관리자를 사용하시겠습니까?", "Which source do you want to add?": "어떤 공급자를 추가하시겠습니까?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "UniGet이 Winget을 사용하는 동안 다른 패키지 관리자가 UniGet하여 혼란스러울 수 있습니다. 이전에는 UniGetUI는 Winget으로만 작동하도록 설계되었으나 이제는 더 이상 아닙니다. 그러므로 UniGetUI는 이 프로젝트의 목표를 나타내지 않습니다.", "WinGet could not be repaired": "WinGet을 복구할 수 없습니다.", "WinGet malfunction detected": "WinGet 부조 감지됨", "WinGet was repaired successfully": "WinGet을 성공적으로 복구했습니다.", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGet - 모두 최신 상태입니다.", "WingetUI - {0} updates are available": "UniGetUI - {0}개의 업데이트가 있습니다.\n", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "UniGetUI 홈페이지", "WingetUI Homepage - Share this link!": "UniGetUI 홈페이지 - 이 링크를 공유하세요!", "WingetUI License": "UniGetUI 라이선스", "WingetUI Log": "UniGetUI 로그", "WingetUI Repository": "UniGetUI 저장소", "WingetUI Settings": "UniGetUI 설정", "WingetUI Settings File": "UniGetUI 설정 파일", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI는 다음 라이브러리를 사용합니다. 그것들 없이는 UniGetUI가 존재할 수 없습니다.", "WingetUI Version {0}": "UniGetUI 버전 {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI 자동 시작 동작, 애플리케이션 시작 설정", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI는 소프트웨어에 사용 가능한 업데이트가 있는지 확인하고 원하는 경우 자동으로 설치할 수 있습니다.", "WingetUI display language:": "UniGetUI 표시 언어:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI를 관리자 권한으로 실행한 것 같습니다. 그러나 이는 권장하지 않습니다. UniGetUI를 관리자 권한으로 실행하면 UniGetUI에서 실행되는 모든 작업이 관리자 권한으로 실행됩니다. 프로그램을 계속 사용할 수는 있지만 UniGetUI를 관리자 권한으로 실행하지 않는 것이 좋습니다.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI는 자원봉사 번역가들 덕분에 40개가 넘는 언어로 번역되었습니다. 고마워요🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI는 기계 번역된 게 아닙니다! 다음 사용자들이 번역했습니다:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI는 명령줄 패키지 관리자를 위한 올인원 그래픽 인터페이스를 제공함으로써 소프트웨어를 보다 쉽게 관리할 수 있게 해주는 애플리케이션입니다.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI는 UniGetUI(지금 사용 중인 인터페이스)와 WinGet(저와 관련이 없는 Microsoft에서 개발한 패키지 관리자)의 차이점을 강조하기 위해 이름이 바뀌었습니다.", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI가 업데이트 중입니다. 완료되면 UniGetUI가 자동으로 다시 시작됩니다.", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI는 무료이며, 앞으로도 쭉 무료입니다. 광고 없음, 신용카드 없음, 프리미엄 버전 없음. 영원히 100% 무료입니다.", "WingetUI log": "UniGetUI 로그", "WingetUI tray application preferences": "UniGetUI 트레이 애플리케이션 환경 설정", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI는 다음 라이브러리를 사용합니다. 그것들 없이는 UniGetUI가 존재할 수 없습니다.", "WingetUI version {0} is being downloaded.": "UniGetUI {0} 버전을 다운로드하고 있습니다.", "WingetUI will become {newname} soon!": "WingetUI가 {newname}가 됩니다!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI는 주기적으로 업데이트를 확인하지 않습니다. 프로그램 시작 시 계속 확인하지만 이에 대한 경고는 표시되지 않습니다.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI는 패키지를 설치를 위해 권한 상승이 필요할 때마다 UAC 프롬프트를 표시합니다.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI가 곧 {newname}로 명명됩니다. 이는 애플리케이션 자체의 변경 사항을 의미하지 않습니다. 저(개발자)는 현재 진행 중인 프로젝트와 마찬가지로 다른 이름으로 이 프로젝트를 계속 개발할 것입니다.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI는 소중한 기여자분들의 도움 없이는 불가능했을 것입니다. GitHub 프로필을 확인해 보세요. 기여자분들이 없었다면 UniGetUI는 불가능했을 것입니다!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI는 기여자들의 도움 없이는 불가능했을 것입니다. 모두들 고마워요🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} 설치가 준비되었습니다.", "Write here the process names here, separated by commas (,)": null, "Yes": "예", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "현재 {0} 버전이 설치되어 있습니다.", "You have installed WingetUI Version {0}": "UniGetUI {0} 버전을 설치했습니다.", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "UniGetUI로 사용하려면 {pm}을(를) 설치해야 할 수 있습니다.", "You may restart your computer later if you wish": "원하는 경우 컴퓨터를 나중에 다시 시작할 수 있습니다.", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "메시지는 한 번만 표시되며, 관리자 권한을 요청하는 패키지에 해당 권한이 부여됩니다.", "You will be prompted only once, and every future installation will be elevated automatically.": "한 번만 메시지가 표시되며 이후 모든 설치는 자동으로 승격됩니다.", "You will likely need to interact with the installer.": "설치 프로그램과 상호 작용이 필요할 수 있습니다.", "[RAN AS ADMINISTRATOR]": "\"관리자 권한으로 실행함\"", "buy me a coffee": "커피 한 잔 사주기", "extracted": "압축 해제됨", "feature": "기능", "formerly WingetUI": "전 WingetUI", "homepage": "웹사이트", "install": "설치", "installation": "설치", "installed": "설치됨", "installing": "설치 중", "library": "라이브러리", "mandatory": null, "option": "옵션", "optional": null, "uninstall": "제거", "uninstallation": "제거", "uninstalled": "제거", "uninstalling": "제거 중", "update(noun)": "업데이트", "update(verb)": "업데이트", "updated": "업데이트", "updating": "업데이트 중", "version {0}": "버전 {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} 제거", "{0} aborted": "{0} 중단됨", "{0} can be updated": "{0}을(를) 업데이트할 수 있습니다", "{0} can be updated to version {1}": "{0}을(를) {1} 버전으로 업데이트할 수 있습니다.", "{0} days": "{0} 일", "{0} desktop shortcuts created": "{0} 바탕 화면 바로 가기 만듦", "{0} failed": "{0} 실패", "{0} has been installed successfully.": "{0}을(를) 성공적으로 설치했습니다.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0}을(를) 성공적으로 설치했습니다. 설치를 마치려면 UniGetUI를 다시 시작하기를 권장합니다.", "{0} has failed, that was a requirement for {1} to be run": "{0}을(를) 실패했습니다. {1}을(를) 실행하려면 {0}이(가) 필요합니다.", "{0} homepage": "{0} 홈페이지", "{0} hours": "{0} 시간", "{0} installation": "{0} 설치", "{0} installation options": "{0} 설치 옵션", "{0} installer is being downloaded": "{0} 설치 프로그램을 다운로드 하고 있습니다.", "{0} is being installed": "{0}을(를) 설치하고 있습니다.", "{0} is being uninstalled": "{0}을(를) 제거하고 있습니다.", "{0} is being updated": "{0}을(를) 업데이트하는 중입니다.", "{0} is being updated to version {1}": "{0}을(를) {1} 버전으로 업데이트하는 중입니다.", "{0} is disabled": "{0}이(가) 비활성화 됨", "{0} minutes": "{0} 분", "{0} months": "{0} 달", "{0} packages are being updated": "패키지 {0}개 업데이트 중", "{0} packages can be updated": "패키지 {0}개 업데이트 가능", "{0} packages found": "패키지 {0}개 찾음", "{0} packages were found": "패키지를 {0}개 찾았습니다.", "{0} packages were found, {1} of which match the specified filters.": "패키지를 {0}개 찾았으며, 그 중 {1}개가 지정한 필터와 일치합니다.", "{0} settings": "{0} 설정", "{0} status": "{0} 상태", "{0} succeeded": "{0} 성공", "{0} update": "{0} 업데이트", "{0} updates are available": "{0} 업데이트 사용 가능", "{0} was {1} successfully!": "{0}을(를) 성공적으로 {1}했습니다!", "{0} weeks": "{0} 주", "{0} years": "{0} 년", "{0} {1} failed": "{0} {1} 실패", "{package} Installation": "{package} 설치", "{package} Uninstall": "{package} 제거", "{package} Update": "{package} 업데이트", "{package} could not be installed": "{package}을(를) 설치할 수 없습니다.", "{package} could not be uninstalled": "{package}를 제거할 수 없습니다.", "{package} could not be updated": "{package}를 업데이트할 수 없습니다", "{package} installation failed": "{package} 설치 실패", "{package} installer could not be downloaded": "{package} 설치 프로그램을 다운로드할 수 없습니다.", "{package} installer download": "{package} 설치 프로그램 다운로드", "{package} installer was downloaded successfully": "{package} 설치 프로그램을 성공적으로 다운로드했습니다.", "{package} uninstall failed": "{package} 제거 실패", "{package} update failed": "{package} 업데이트 실패", "{package} update failed. Click here for more details.": "{package} 업데이트 실패. 여기를 눌러 자세한 정보를 확인하세요.", "{package} was installed successfully": "{package}을(를) 성공적으로 설치했습니다.", "{package} was uninstalled successfully": "{package}을(를) 성공적으로 제거했습니다.", "{package} was updated successfully": "{package}을(를) 성공적으로 업데이트했습니다.", "{pcName} installed packages": "{pcName} 설치된 패키지", "{pm} could not be found": "{pm}을(를) 찾을 수 없습니다.", "{pm} found: {state}": "{pm} 찾음: {state}", "{pm} is disabled": "{pm}이(가) 비활성화 되었습니다.", "{pm} is enabled and ready to go": "{pm}이(가) 활성화되었으며 사용할 준비가 되었습니다.", "{pm} package manager specific preferences": "{pm} 패키지 관리자 환경 설정", "{pm} preferences": "{pm} 환경 설정", "{pm} version:": "{pm} 버전:", "{pm} was not found!": "{pm}을(를) 찾을 수 없습니다!"}