name: Retrieve latest icons from excel file

on:
  schedule:
    - cron: 0 0 * * *
  workflow_dispatch:

jobs:
  load-icons:
    if: github.repository == 'marticliment/UniGetUI'
    runs-on: windows-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v5

      - name: Download icons json file
        run: |
          python -m pip install xlrd==1.2.0
          git config user.email "<EMAIL>"
          git config user.name "Excel Bot"
          python scripts/generate_json_from_excel.py 
          git commit WebBasedData/screenshot-database.json -m "Update icons and screenshots"
          exit 0

      - name: Prepare PR Body with Images
        id: prepare_pr_body
        run: |
          echo "## New Images" > pr_body.md
          Get-Content WebBasedData/new_urls.txt | ForEach-Object {
            if (![string]::IsNullOrWhiteSpace($_)) {
              Add-Content pr_body.md "<a href='$_'><img src='$_' width='200' height='200' /></a>" -NoNewline
            }
          }
      - name: <PERSON>reate Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          delete-branch: true
          base: main
          title: "Update icons and screenshots from the excel file"
          reviewers: "${{ github.repository_owner }}"
          author: "Excel Bot <<EMAIL>>"
          commit-message: "Update icons and screenshots from the excel file"
          branch: pull-request/update-icons-and-screenshots
          body-path: pr_body.md
