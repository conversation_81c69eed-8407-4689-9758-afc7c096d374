<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Interface.Dialogs.InstallOptionsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:animations="using:CommunityToolkit.WinUI.Animations"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Interface.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:media="using:CommunityToolkit.WinUI.Media"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    MaxWidth="700"
    HorizontalAlignment="Stretch"
    mc:Ignorable="d">
    <Page.Resources>
        <DataTemplate x:Key="ProcessTemplate" x:DataType="local:IOP_Proc">
            <StackPanel Orientation="Horizontal">
                <Viewbox Width="16">
                    <FontIcon Glyph="&#xECAA;" />
                </Viewbox>
                <TextBlock Padding="8,0,0,0" Text="{x:Bind Name}" />
            </StackPanel>
        </DataTemplate>
    </Page.Resources>
    <Grid HorizontalAlignment="Stretch">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <ScrollViewer
            Name="Scroller"
            Grid.Row="0"
            Grid.Column="0"
            HorizontalAlignment="Stretch"
            SizeChanged="LayoutGrid_SizeChanged">
            <Grid
                Name="LayoutGrid"
                HorizontalAlignment="Stretch"
                RowSpacing="16"
                SizeChanged="LayoutGrid_SizeChanged">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!--  Title, Package Icon and operation profile selector  -->
                <Grid
                    x:Name="HeaderBar"
                    Grid.Row="0"
                    Margin="8,0,16,0"
                    ColumnSpacing="8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="*" MaxWidth="150" />
                    </Grid.ColumnDefinitions>
                    <Image
                        x:Name="PackageIcon"
                        Grid.Column="0"
                        Width="32"
                        Height="32"
                        VerticalAlignment="Center" />
                    <TextBlock
                        x:Name="DialogTitle"
                        Grid.Column="1"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        FontSize="24"
                        FontWeight="Bold"
                        TextWrapping="Wrap" />
                    <StackPanel
                        Grid.Column="2"
                        Orientation="Vertical"
                        Spacing="4">
                        <widgets:TranslatedTextBlock Margin="4,0" Text="Operation profile:" />
                        <ComboBox x:Name="ProfileComboBox" HorizontalAlignment="Stretch" />
                    </StackPanel>
                </Grid>

                <!--  Override global options switch  -->
                <Border
                    Grid.Row="1"
                    Padding="16,4,0,4"
                    HorizontalAlignment="Stretch"
                    Background="{ThemeResource SystemChromeAltHighColor}"
                    CornerRadius="8">
                    <Grid
                        Grid.Row="1"
                        ColumnSpacing="8"
                        RowSpacing="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <widgets:TranslatedTextBlock
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Text="Follow the default options when installing, upgrading or uninstalling this package"
                            WrappingMode="Wrap" />
                        <HyperlinkButton
                            x:Name="GoToDefaultOptionsSettings"
                            Grid.Row="1"
                            Margin="0,0,0,0"
                            Padding="0"
                            Click="GoToDefaultOptionsSettings_Click">
                            <widgets:TranslatedTextBlock Text="Change default options" />
                        </HyperlinkButton>
                        <ToggleSwitch
                            x:Name="FollowGlobalOptionsSwitch"
                            Grid.RowSpan="2"
                            Grid.Column="1"
                            VerticalAlignment="Center" />
                    </Grid>
                </Border>

                <!--  Main UI: main controls and overlay  -->
                <Grid Grid.Row="2" RowSpacing="8">
                    <!--  All install options are placed here  -->
                    <UserControl Name="OptionsPanelBase">
                        <Grid RowSpacing="8">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <widgets:TranslatedTextBlock
                                x:Name="OptionsPanel0"
                                Grid.Row="0"
                                Margin="8,0"
                                Text="The following settings will be applied each time this package is installed, updated or removed." />

                            <Border
                                Grid.Row="1"
                                Margin="0,0,0,-8"
                                HorizontalAlignment="Stretch"
                                CornerRadius="8,8,0,0">
                                <TabView
                                    x:Name="SettingsTabBar"
                                    HorizontalAlignment="Stretch"
                                    Background="{ThemeResource SystemChromeAltHighColor}"
                                    CanReorderTabs="False"
                                    IsAddTabButtonVisible="False"
                                    SelectionChanged="SettingsTabBar_SelectionChanged"
                                    TabWidthMode="SizeToContent">
                                    <widgets:BetterTabViewItem
                                        IconName="Settings"
                                        IsSelected="True"
                                        Line1="General"
                                        Line2="Version" />
                                    <widgets:BetterTabViewItem
                                        IconName="OpenFolder"
                                        Line1="Architecture"
                                        Line2="Location and Scope" />
                                    <widgets:BetterTabViewItem
                                        IconName="Console"
                                        Line1="Command-line"
                                        Line2="Arguments" />
                                    <widgets:BetterTabViewItem
                                        IconName="Cross"
                                        Line1="Close apps"
                                        Line2="before installing" />
                                    <widgets:BetterTabViewItem
                                        IconName="ClipboardList"
                                        Line1="Pre-install"
                                        Line2="Post-install" />
                                </TabView>
                            </Border>

                            <controls:SwitchPresenter
                                x:Name="SettingsSwitchPresenter"
                                Grid.Row="2"
                                Padding="16"
                                Background="{ThemeResource SystemChromeAltHighColor}"
                                CornerRadius="0,0,8,8"
                                TargetType="x:Int32"
                                Value="{x:Bind SettingsTabBar.SelectedIndex, Mode=OneWay}">
                                <!--  General settings  -->
                                <controls:Case IsDefault="True" Value="0">
                                    <StackPanel Spacing="8">
                                        <animations:Implicit.ShowAnimations>
                                            <animations:TranslationAnimation
                                                From="0,20,0"
                                                To="0"
                                                Duration="0:0:0.3" />
                                            <animations:OpacityAnimation
                                                From="0"
                                                To="1.0"
                                                Duration="0:0:0.3" />
                                        </animations:Implicit.ShowAnimations>
                                        <controls:WrapPanel HorizontalSpacing="16" VerticalSpacing="0">
                                            <CheckBox Name="AdminCheckBox" Click="AdminCheckBox_Click">
                                                <widgets:TranslatedTextBlock Text="Run as admin" />
                                            </CheckBox>
                                            <CheckBox Name="InteractiveCheckBox" Click="InteractiveCheckBox_Click">
                                                <widgets:TranslatedTextBlock Text="Interactive installation" />
                                            </CheckBox>
                                            <CheckBox Name="HashCheckbox" Click="HashCheckbox_Click">
                                                <widgets:TranslatedTextBlock HorizontalAlignment="Stretch" Text="Skip hash check" />
                                            </CheckBox>
                                            <CheckBox Name="UninstallPreviousOnUpdate">
                                                <widgets:TranslatedTextBlock HorizontalAlignment="Stretch" Text="Uninstall previous versions when updated" />
                                            </CheckBox>
                                        </controls:WrapPanel>
                                        <Grid
                                            Padding="4,8"
                                            BorderBrush="{ThemeResource AppBarToggleButtonCheckedDisabledBackgroundThemeBrush}"
                                            BorderThickness="0,1,0,1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" MaxWidth="200" />
                                            </Grid.ColumnDefinitions>
                                            <widgets:TranslatedTextBlock
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Text="Version to install:" />

                                            <ComboBox
                                                Name="VersionComboBox"
                                                Grid.Column="1"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Center"
                                                SelectionChanged="VersionComboBox_SelectionChanged" />

                                            <ProgressBar
                                                Name="VersionProgress"
                                                Grid.Column="1"
                                                Margin="1,0,1,0"
                                                VerticalAlignment="Top"
                                                CornerRadius="4,4,0,0"
                                                IsIndeterminate="True"
                                                Visibility="Visible" />
                                        </Grid>
                                        <controls:WrapPanel HorizontalSpacing="16" VerticalSpacing="0">
                                            <CheckBox Name="IgnoreUpdatesCheckbox">
                                                <widgets:TranslatedTextBlock Text="Ignore future updates for this package" />
                                            </CheckBox>
                                            <CheckBox Name="SkipMinorUpdatesCheckbox">
                                                <widgets:TranslatedTextBlock Text="Skip minor updates for this package" />
                                            </CheckBox>
                                        </controls:WrapPanel>
                                    </StackPanel>
                                </controls:Case>

                                <!--  Scope, location and architecture  -->
                                <controls:Case Value="1">
                                    <StackPanel Spacing="8">

                                        <animations:Implicit.ShowAnimations>
                                            <animations:TranslationAnimation
                                                From="0,20,0"
                                                To="0"
                                                Duration="0:0:0.3" />
                                            <animations:OpacityAnimation
                                                From="0"
                                                To="1.0"
                                                Duration="0:0:0.3" />
                                        </animations:Implicit.ShowAnimations>
                                        <Grid Padding="4,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" MaxWidth="200" />
                                            </Grid.ColumnDefinitions>
                                            <widgets:TranslatedTextBlock
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Text="Architecture to install:" />
                                            <ComboBox
                                                Name="ArchitectureComboBox"
                                                Grid.Column="1"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Center"
                                                SelectedIndex="0"
                                                SelectionChanged="ArchitectureComboBox_SelectionChanged" />
                                        </Grid>
                                        <Grid
                                            Padding="4,8"
                                            BorderBrush="{ThemeResource AppBarToggleButtonCheckedDisabledBackgroundThemeBrush}"
                                            BorderThickness="0,1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" MaxWidth="200" />
                                            </Grid.ColumnDefinitions>
                                            <widgets:TranslatedTextBlock
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Text="Installation scope:" />
                                            <ComboBox
                                                Name="ScopeCombo"
                                                Grid.Column="1"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Center"
                                                SelectionChanged="ScopeCombo_SelectionChanged" />
                                        </Grid>
                                        <Grid Padding="4,0" ColumnSpacing="8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" MaxWidth="96" />
                                                <ColumnDefinition Width="*" MaxWidth="96" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>
                                            <widgets:TranslatedTextBlock
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Text="Install location:" />
                                            <HyperlinkButton
                                                Name="SelectDir"
                                                Grid.Column="1"
                                                HorizontalAlignment="Stretch"
                                                Click="SelectDir_Click">
                                                <widgets:TranslatedTextBlock Text="Select" />
                                            </HyperlinkButton>
                                            <HyperlinkButton
                                                Name="ResetDir"
                                                Grid.Column="2"
                                                HorizontalAlignment="Stretch"
                                                Click="ResetDir_Click">
                                                <widgets:TranslatedTextBlock Text="Reset" />
                                            </HyperlinkButton>
                                            <TextBlock
                                                Name="CustomInstallLocation"
                                                Grid.Row="2"
                                                Grid.ColumnSpan="3"
                                                MaxWidth="700"
                                                VerticalAlignment="Center"
                                                FontFamily="Consolas"
                                                Opacity="0.6"
                                                TextWrapping="Wrap" />
                                        </Grid>
                                    </StackPanel>
                                </controls:Case>

                                <!--  Close apps before installing  -->
                                <controls:Case Value="3">
                                    <StackPanel HorizontalAlignment="Center" Spacing="8">
                                        <animations:Implicit.ShowAnimations>
                                            <animations:TranslationAnimation
                                                From="0,20,0"
                                                To="0"
                                                Duration="0:0:0.3" />
                                            <animations:OpacityAnimation
                                                From="0"
                                                To="1.0"
                                                Duration="0:0:0.3" />
                                        </animations:Implicit.ShowAnimations>

                                        <TextBlock
                                            x:Name="KillProcessesLabel"
                                            Margin="8,0,8,-8"
                                            Text="Select the processes that should be closed before this package is installed, updated or uninstalled:" />
                                        <controls:TokenizingTextBox
                                            x:Name="KillProcessesBox"
                                            HorizontalAlignment="Left"
                                            ItemsSource="{x:Bind ProcessesToKill, Mode=TwoWay}"
                                            PlaceholderText=""
                                            SuggestedItemTemplate="{StaticResource ProcessTemplate}"
                                            SuggestedItemsSource="{x:Bind SuggestedProcesses, Mode=OneWay}"
                                            TextChanged="KillProcessesBox_TextChanged"
                                            TextMemberPath="Text"
                                            TokenDelimiter=","
                                            TokenItemAdding="KillProcessesBox_TokenItemAdding"
                                            TokenItemTemplate="{StaticResource ProcessTemplate}" />
                                        <CheckBox x:Name="KillProcessesThatWontDie">
                                            <widgets:TranslatedTextBlock Text="Try to kill the processes that refuse to close when requested to" />
                                        </CheckBox>
                                    </StackPanel>
                                </controls:Case>

                                <!--  Custom command-line arguments  -->
                                <controls:Case Value="2">
                                    <StackPanel Spacing="8">
                                        <animations:Implicit.ShowAnimations>
                                            <animations:TranslationAnimation
                                                From="0,20,0"
                                                To="0"
                                                Duration="0:0:0.3" />
                                            <animations:OpacityAnimation
                                                From="0"
                                                To="1.0"
                                                Duration="0:0:0.3" />
                                        </animations:Implicit.ShowAnimations>
                                        <widgets:TranslatedTextBlock
                                            x:Name="CLIDisabled"
                                            Foreground="{ThemeResource SystemErrorTextColor}"
                                            Text="For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. " />
                                        <HyperlinkButton
                                            x:Name="GoToCLISettings"
                                            Margin="0,-8,0,0"
                                            Padding="0"
                                            HorizontalAlignment="Center"
                                            Click="GoToSecureSettings_Click">
                                            <widgets:TranslatedTextBlock FontWeight="SemiBold" Text="Go to UniGetUI security settings" />
                                        </HyperlinkButton>
                                        <Grid ColumnSpacing="8" RowSpacing="8">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="2*" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>
                                            <widgets:TranslatedTextBlock
                                                x:Name="CustomParametersLabel1"
                                                Grid.Row="0"
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Text="Custom install arguments:" />
                                            <TextBox
                                                Name="CustomParameters1"
                                                Grid.Row="0"
                                                Grid.Column="1"
                                                Padding="5,7,5,5"
                                                FontFamily="Consolas"
                                                TextChanged="CustomParameters_TextChanged"
                                                TextWrapping="Wrap" />

                                            <widgets:TranslatedTextBlock
                                                x:Name="CustomParametersLabel2"
                                                Grid.Row="1"
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Text="Custom update arguments:" />
                                            <TextBox
                                                Name="CustomParameters2"
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                Padding="5,7,5,5"
                                                FontFamily="Consolas"
                                                TextChanged="CustomParameters_TextChanged"
                                                TextWrapping="Wrap" />

                                            <widgets:TranslatedTextBlock
                                                x:Name="CustomParametersLabel3"
                                                Grid.Row="2"
                                                Grid.Column="0"
                                                VerticalAlignment="Center"
                                                Text="Custom uninstall arguments:" />
                                            <TextBox
                                                Name="CustomParameters3"
                                                Grid.Row="2"
                                                Grid.Column="1"
                                                Padding="5,7,5,5"
                                                FontFamily="Consolas"
                                                TextChanged="CustomParameters_TextChanged"
                                                TextWrapping="Wrap" />
                                        </Grid>
                                    </StackPanel>
                                </controls:Case>

                                <!--  Pre-install and post-install operations  -->
                                <controls:Case Value="4">
                                    <StackPanel Spacing="8">
                                        <animations:Implicit.ShowAnimations>
                                            <animations:TranslationAnimation
                                                From="0,20,0"
                                                To="0"
                                                Duration="0:0:0.3" />
                                            <animations:OpacityAnimation
                                                From="0"
                                                To="1.0"
                                                Duration="0:0:0.3" />
                                        </animations:Implicit.ShowAnimations>
                                        <widgets:TranslatedTextBlock
                                            x:Name="PrePostDisabled"
                                            Foreground="{ThemeResource SystemErrorTextColor}"
                                            Text="For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. " />
                                        <HyperlinkButton
                                            x:Name="GoToPrePostSettings"
                                            Margin="0,-8,0,0"
                                            Padding="0"
                                            HorizontalAlignment="Center"
                                            Click="GoToSecureSettings_Click">
                                            <widgets:TranslatedTextBlock FontWeight="SemiBold" Text="Go to UniGetUI security settings" />
                                        </HyperlinkButton>

                                        <widgets:TranslatedTextBlock x:Name="CustomCommandsHeaderExplainer" Text="You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here." />

                                        <Grid
                                            Padding="8"
                                            BorderBrush="{ThemeResource AppBarToggleButtonCheckedDisabledBackgroundThemeBrush}"
                                            BorderThickness="0,1,0,1"
                                            ColumnSpacing="8"
                                            RowSpacing="4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <widgets:TranslatedTextBlock
                                                x:Name="PeInsLabel"
                                                FontSize="12"
                                                Text="Pre-install command:" />
                                            <widgets:TranslatedTextBlock
                                                x:Name="PoInsLabel"
                                                Grid.Column="1"
                                                FontSize="12"
                                                Text="Post-install command:" />
                                            <TextBox
                                                x:Name="PreInstallCommandBox"
                                                Grid.Row="1"
                                                AcceptsReturn="True"
                                                FontFamily="Consolas"
                                                TextWrapping="Wrap" />
                                            <TextBox
                                                x:Name="PostInstallCommandBox"
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                AcceptsReturn="True"
                                                FontFamily="Consolas"
                                                TextWrapping="Wrap" />
                                            <CheckBox
                                                x:Name="AbortInsFailedCheck"
                                                Grid.Row="2"
                                                Grid.ColumnSpan="2">
                                                <widgets:TranslatedTextBlock Text="Abort install if pre-install command fails" />
                                            </CheckBox>
                                        </Grid>

                                        <Grid
                                            Margin="0,-8,0,0"
                                            Padding="8"
                                            BorderBrush="{ThemeResource AppBarToggleButtonCheckedDisabledBackgroundThemeBrush}"
                                            BorderThickness="0,0,0,0"
                                            ColumnSpacing="8"
                                            RowSpacing="4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <widgets:TranslatedTextBlock
                                                x:Name="PeUpdLabel"
                                                FontSize="12"
                                                Text="Pre-update command:" />
                                            <widgets:TranslatedTextBlock
                                                x:Name="PoUpdLabel"
                                                Grid.Column="1"
                                                FontSize="12"
                                                Text="Post-update command:" />
                                            <TextBox
                                                x:Name="PreUpdateCommandBox"
                                                Grid.Row="1"
                                                AcceptsReturn="True"
                                                FontFamily="Consolas"
                                                TextWrapping="Wrap" />
                                            <TextBox
                                                x:Name="PostUpdateCommandBox"
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                AcceptsReturn="True"
                                                FontFamily="Consolas"
                                                TextWrapping="Wrap" />
                                            <CheckBox
                                                x:Name="AbortUpdFailedCheck"
                                                Grid.Row="2"
                                                Grid.ColumnSpan="2">
                                                <widgets:TranslatedTextBlock Text="Abort update if pre-update command fails" />
                                            </CheckBox>
                                        </Grid>

                                        <Grid
                                            Margin="0,-8,0,0"
                                            Padding="8"
                                            BorderBrush="{ThemeResource AppBarToggleButtonCheckedDisabledBackgroundThemeBrush}"
                                            BorderThickness="0,1,0,0"
                                            ColumnSpacing="8"
                                            RowSpacing="4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>

                                            <widgets:TranslatedTextBlock
                                                x:Name="PeUniLabel"
                                                FontSize="12"
                                                Text="Pre-uninstall command:" />
                                            <widgets:TranslatedTextBlock
                                                x:Name="PoUniLabel"
                                                Grid.Column="1"
                                                FontSize="12"
                                                Text="Post-uninstall command:" />
                                            <TextBox
                                                x:Name="PreUninstallCommandBox"
                                                Grid.Row="1"
                                                AcceptsReturn="True"
                                                FontFamily="Consolas"
                                                TextWrapping="Wrap" />
                                            <TextBox
                                                x:Name="PostUninstallCommandBox"
                                                Grid.Row="1"
                                                Grid.Column="1"
                                                AcceptsReturn="True"
                                                FontFamily="Consolas"
                                                TextWrapping="Wrap" />
                                            <CheckBox
                                                x:Name="AbortUniFailedCheck"
                                                Grid.Row="2"
                                                Grid.ColumnSpan="2">
                                                <widgets:TranslatedTextBlock Text="Abort uninstall if pre-uninstall command fails" />
                                            </CheckBox>
                                        </Grid>
                                    </StackPanel>
                                </controls:Case>
                            </controls:SwitchPresenter>
                        </Grid>
                    </UserControl>

                    <!--  Overlay to warn user that options are not overriden  -->
                    <Border
                        x:Name="PlaceholderBanner"
                        Grid.RowSpan="5"
                        Margin="-8"
                        Padding="32,0">
                        <Border.Background>
                            <media:BackdropBlurBrush Amount="5.0" />
                        </Border.Background>
                        <Grid>
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Orientation="Vertical"
                                Spacing="12">
                                <widgets:TranslatedTextBlock
                                    x:Name="PlaceholderText"
                                    HorizontalContentAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    FontSize="20"
                                    FontWeight="SemiBold"
                                    WrappingMode="Wrap" />
                                <Button
                                    x:Name="UnlockSettingsButton"
                                    HorizontalAlignment="Center"
                                    Click="UnlockSettingsButton_Click">
                                    <widgets:TranslatedTextBlock Text="Change this and unlock" />
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>

                <!--  Current command-line display  -->
                <Border
                    x:Name="CommandLineViewBox"
                    Grid.Row="3"
                    Padding="16,12,16,12"
                    Background="{ThemeResource SystemChromeAltHighColor}"
                    CornerRadius="8">
                    <StackPanel Spacing="8">
                        <widgets:TranslatedTextBlock
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Text="Command-line to run:" />
                        <Border Background="{ThemeResource ControlFillColorSecondaryBrush}" CornerRadius="4">
                            <TextBlock
                                Name="CommandBox"
                                MaxWidth="700"
                                Padding="8"
                                HorizontalAlignment="Stretch"
                                FontFamily="Consolas"
                                IsTextSelectionEnabled="True"
                                TextWrapping="Wrap" />
                        </Border>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>
        <!--  Close Button  -->
        <widgets:DialogCloseButton
            x:Name="CloseButton"
            Margin="0,-24,-24,0"
            Click="CloseButton_Click" />
    </Grid>
</Page>
