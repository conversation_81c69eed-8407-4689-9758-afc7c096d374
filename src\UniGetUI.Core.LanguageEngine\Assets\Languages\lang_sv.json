{"\"{0}\" is a local package and can't be shared": "\"{0}\" är ett lokalt paket och kan inte delas", "\"{0}\" is a local package and does not have available details": "\"{0}\" är ett lokalt paket och har inga detaljer tillgängliga", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" är ett lokalt paket och är inte kompatibelt med den här funktionen", "(Last checked: {0})": "(Kontrollerades senast: {0})", "(Number {0} in the queue)": "(Nummer {0} i kön)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@curudel, @kak<PERSON><PERSON>, @umeaboy, @Hi-there-how-are-u", "0 packages found": "0 paket hittades", "0 updates found": "0 uppdateringar hittades", "1 - Errors": "1 - <PERSON><PERSON>", "1 day": "1 dag", "1 hour": "1 timme", "1 month": "1 månad", "1 package was found": "1 paket hittades", "1 update is available": "1 uppdatering är tillgänglig", "1 week": "1 vecka", "1 year": "1 år", "1. Navigate to the \"{0}\" or \"{1}\" page.": "Navi<PERSON><PERSON> till sidan \"{0}\" eller \"{1}\".", "2 - Warnings": "2 - <PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Lokalisera paket(en) du vill lägga till i paketgruppen och bocka i den vänstra rutan.", "3 - Information (less)": "3 - Information (mindre)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON><PERSON> du valt paketen du vill lägga till paketgruppen, leta upp och klicka i valet \"{0}\" i verktygsfältet.", "4 - Information (more)": "4 - information (mer)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON> paket har nu lagts till i paketgruppen. Du kan lägga till fler paket eller exportera hela paketgruppen.", "5 - information (debug)": "5 - Information (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "En populär hanterare för C/C++bibliotek. Fylld av C/C++bibliotek och andra C/C++relaterade verktyg<br>\nInnehåller: <b>C/C++bibliotek och relaterade verktyg</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "En förvaringsplats fullt av verktyg och körbara filer som är designade med Microsofts .NET-ekosystem i åtanke.<br>Innehåller: <b>.NET-relaterade verktyg och skript</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "En förvaringsplats full av verktyg utformade med Microsofts .NET-ekosystem i åtanke.<br>Innehåller: <b>.NET-relaterade verktyg</b>", "A restart is required": "Omstart krävs", "Abort install if pre-install command fails": "Avbryt installationen om pre-install kommandot misslyckas", "Abort uninstall if pre-uninstall command fails": "A<PERSON><PERSON><PERSON>t avinstallationen om pre-install kommandot misslyckas", "Abort update if pre-update command fails": "<PERSON><PERSON><PERSON><PERSON><PERSON> uppdatering ifall pre-update kommandot misslyckas", "About": "Om", "About Qt6": "Om Qt6", "About WingetUI": "Om UniGetUI", "About WingetUI version {0}": "Om WingetUI version {0}", "About the dev": "<PERSON>m utvecklaren", "Accept": "Acceptera", "Action when double-clicking packages, hide successful installations": "<PERSON><PERSON><PERSON><PERSON><PERSON> när du dubbelklickar på paket, d<PERSON><PERSON><PERSON> l<PERSON>er", "Add": "<PERSON><PERSON><PERSON> till", "Add a source to {0}": "<PERSON><PERSON><PERSON> till en källa till {0}", "Add a timestamp to the backup file names": "Lägg till en tidsstämpel till säkerhetskopiornas filnamn", "Add a timestamp to the backup files": "Lägg till en tidsstämpel till säkerhetskopiorna", "Add packages or open an existing bundle": "Lägg till paket eller öppna ett befintligt paketgrupp", "Add packages or open an existing package bundle": "Lägg till paket eller öppna ett befintligt paketgrupp", "Add packages to bundle": "Lägg till fler paket i paketgruppen", "Add packages to start": "Lägg till paket för att börja", "Add selection to bundle": "Lägg till det valda i paketgruppen", "Add source": "Lägg till källa", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Lägg till uppdateringar som misslyckas med felet \"ingen passande uppdatering funnen\" till listan över ignorerade uppdateringar,", "Adding source {source}": "<PERSON><PERSON><PERSON> till källa {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON> till kä<PERSON> {source} till {manager}", "Addition succeeded": "Tillägget lyckades", "Administrator privileges": "Administratörsrättigheter", "Administrator privileges preferences": "Inställningar för administratörsrättigheter", "Administrator rights": "Administratörsrättigheter", "Administrator rights and other dangerous settings": "Administratorrättigheter och andra farliga inställningar", "Advanced options": "Avancerade alternativ", "All files": "Alla filer", "All versions": "Alla versioner", "Allow changing the paths for package manager executables": "Tillåt ändring av sökväg för pakethanterarens körbara filer", "Allow custom command-line arguments": "Till<PERSON>tt anpassade kommandoradsargument", "Allow importing custom command-line arguments when importing packages from a bundle": "Tillåt import av anpassade kommandorads argument vid import av paket från ett paketgrupp", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Tillåt import av anpassade pre-install och post-install kommandon vid import av paket från ett paketgrupp", "Allow package operations to be performed in parallel": "Till<PERSON>t att paketåtgärder utföras parallellt", "Allow parallel installs (NOT RECOMMENDED)": "<PERSON><PERSON><PERSON> parallella installationer (REKOMMENDERAS INTE)", "Allow pre-release versions": "<PERSON><PERSON><PERSON>", "Allow {pm} operations to be performed in parallel": "<PERSON><PERSON>t att {pm} operationer utförs parallellt", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativt så kan du även installera {0} genom att köra följande kommando i en Windows PowerShell-prompt:", "Always elevate {pm} installations by default": "<PERSON><PERSON><PERSON> alltid {pm} installationer som standard", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON> alltid {pm} åtgärder med administratörsrättigheter", "An error occurred": "<PERSON>tt fel uppstod", "An error occurred when adding the source: ": "Ett fel uppstod när källan lades till:", "An error occurred when attempting to show the package with Id {0}": "Ett fel uppstod vid visning av paket med id {0}", "An error occurred when checking for updates: ": "Ett fel uppstod vid sökning efter uppdateringar:", "An error occurred while attempting to create an installation script:": "Ett fel uppstod vid försök att skapa ett installationsskript:", "An error occurred while loading a backup: ": "<PERSON><PERSON> fel uppstod vid inläsning av backup:", "An error occurred while logging in: ": "Ett fel uppstod vid loggning i:", "An error occurred while processing this package": "<PERSON>tt fel uppstod när det här paketet bearbetades", "An error occurred:": "<PERSON>tt fel uppstod:", "An interal error occurred. Please view the log for further details.": "Ett internt fel uppstod. Se loggen för mer information.", "An unexpected error occurred:": "Ett oväntat fel uppstod:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Ett oväntat fel uppstod vid försök att reparera WinGet. Vänligen prova igen senare", "An update was found!": "En uppdatering hittades!", "Android Subsystem": "Android subsystem", "Another source": "<PERSON><PERSON> k<PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Nya genvägar skapade vid en installation eller efter en uppdatering kommer tas bort automatiskt istället för att visa en bekräftelse första gången de upptäckts.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Genvägar skapade eller modifierade utanför UniGetUI kommer ignoreras. Du kan lägga till dessa genom knappen {0}.", "Any unsaved changes will be lost": "Ändringar som inte sparats kommer att försvinna", "App Name": "App-namn", "Appearance": "Utseende", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> och paketikoner installeras automatiskt", "Application theme:": "Programtema:", "Apply": "Tillämpa", "Architecture to install:": "Arkitektur att installera:", "Are these screenshots wron or blurry?": "Är dessa skärmdumpar felaktiga eller suddiga?", "Are you really sure you want to enable this feature?": "Är du säker på att du vill aktivera den här funktionen?", "Are you sure you want to create a new package bundle? ": "Är du säker på att du vill skapa en nytt paketgrupp?", "Are you sure you want to delete all shortcuts?": "Är du säker på att du vill ta bort alla genvägar?", "Are you sure?": "<PERSON>r du säker?", "Ascendant": "Stigande", "Ask for administrator privileges once for each batch of operations": "Be om administratörsrättigheter en gång för varje omgång av åtgärder", "Ask for administrator rights when required": "Be om administratörsrättigheter vid behov", "Ask once or always for administrator rights, elevate installations by default": "Fråga en gång eller alltid efter administratörsrättigheter, höj installationer som standard", "Ask only once for administrator privileges": "Fråga om administratörsrättigheter endast en gång", "Ask only once for administrator privileges (not recommended)": "Fråga endast en gång om administratörsrättigheter (rekommenderas inte)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Fråga om att ta bort skrivbordsgenvägar som har skapats under en installation eller uppgradering.", "Attention required": "Uppmärksamhet krävs", "Authenticate to the proxy with an user and a password": "Autentisera till proxyn med ett användarnamn och lösenord", "Author": "<PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "Automatisk borttagning av skrivbordsgenvägar", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Spara automatiskt en lista över alla dina installerade paket för att enkelt återställa dem.", "Automatically save a list of your installed packages on your computer.": "Spara automatiskt en lista över dina installerade paket på din dator.", "Autostart WingetUI in the notifications area": "Starta UniGetUI automatiskt i meddelandefältet", "Available Updates": "Tillgängliga uppdateringar", "Available updates: {0}": "Tillgängliga uppdateringar: {0}", "Available updates: {0}, not finished yet...": "Tillgängliga uppdateringar: {0}, inte klar än...", "Backing up packages to GitHub Gist...": "Säkerhetskopierar paket till GitHub Gist...", "Backup": "Säkerhetskopiera", "Backup Failed": "Säkerhetskopiering misslyckades", "Backup Successful": "Säkerhetskopieringen lyckades", "Backup and Restore": "Säkerhetskopiera och återställ", "Backup installed packages": "Säkerhetskopiera installerade paket", "Backup location": "Säkerhetskopians plats", "Become a contributor": "Bli en bidragare", "Become a translator": "Bli en översättare", "Begin the process to select a cloud backup and review which packages to restore": "Starta processen med att välja molnsäkerhetskopia och välj vilka paket som ska återställas", "Beta features and other options that shouldn't be touched": "Betafunktioner och andra alternativ som inte bör röras", "Both": "Båda", "Bundle security report": "Säkerhetsrapport för paketgruppen", "But here are other things you can do to learn about WingetUI even more:": "Men här är andra saker du kan göra för att lära dig ännu mer om UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Genom att sätta en stänga av en pakethanterare kommer du inte att kunna se eller uppdateras dess paket.", "Cache administrator rights and elevate installers by default": "Cachelagra administratörsrättigheter och höj installationsprogram som standard", "Cache administrator rights, but elevate installers only when required": "Cachelagra administra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, men höj installationsprogram endast när det behövs", "Cache was reset successfully!": "<PERSON><PERSON>lldes framgångsrikt!", "Can't {0} {1}": "Kan inte {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": "Avbryt alla åtgärder", "Change backup output directory": "<PERSON>ndra katal<PERSON> för säkerhetskopiering", "Change default options": "<PERSON><PERSON>", "Change how UniGetUI checks and installs available updates for your packages": "Ändra hur UniGetUI kontrollerar och installerar tillgängliga uppdateringar till dina paket", "Change how UniGetUI handles install, update and uninstall operations.": "<PERSON>ndra hur UniGetUI hanterar installation, uppdatering och avinstallation.", "Change how UniGetUI installs packages, and checks and installs available updates": "Ändra hur UniGetUI installerar paket och kontrollerar och installerar tillgängliga uppdateringar", "Change how operations request administrator rights": "<PERSON><PERSON> hur <PERSON><PERSON>g<PERSON><PERSON><PERSON> begär adminrättigheter", "Change install location": "<PERSON><PERSON>", "Change this": "<PERSON><PERSON>", "Change this and unlock": "<PERSON><PERSON> denna och lås upp", "Check for package updates periodically": "<PERSON><PERSON><PERSON> efter paketuppdateringar med jämna mellanrum", "Check for updates": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>", "Check for updates every:": "<PERSON><PERSON><PERSON> efter uppdateringar varje:", "Check for updates periodically": "<PERSON><PERSON><PERSON> efter uppdateringar med jämna mellanrum", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON><PERSON><PERSON> efter uppdateringar regelbundet och fråga mig vad jag ska göra när uppdateringar hittas.", "Check for updates regularly, and automatically install available ones.": "Sök efter uppdateringar regelbundet och installera tillgängliga automatiskt.", "Check out my {0} and my {1}!": "<PERSON><PERSON> in min {0} och min {1}!", "Check out some WingetUI overviews": "<PERSON><PERSON> in några översikter av WingetUI", "Checking for other running instances...": "<PERSON><PERSON><PERSON> efter andra instanser som körs...", "Checking for updates...": "<PERSON><PERSON><PERSON> efter uppdateringar...", "Checking found instace(s)...": "Kontrollerar hittade instans(er)...", "Choose how many operations shouls be performed in parallel": "Vä<PERSON>ja hur många åtgärder ska kunna utföras parallellt", "Clear cache": "Rensa cache", "Clear finished operations": "Rensa utförda <PERSON>gä<PERSON>", "Clear selection": "Rensa val", "Clear successful operations": "Rensa l<PERSON>ade åtgärder", "Clear successful operations from the operation list after a 5 second delay": "Rensa lyckade åtgärder från åtgärdslitan med 5 sekunders fördröjning", "Clear the local icon cache": "Rensa lokala i<PERSON>", "Clearing Scoop cache - WingetUI": "<PERSON><PERSON>-cache - WingetUI", "Clearing Scoop cache...": "<PERSON><PERSON>-cache...", "Click here for more details": "<PERSON><PERSON><PERSON> här för fler <PERSON>", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Klicka på installera för att påbörja installation. UniGetUI kan bete sig annorlunda ifall du hoppas över installationen.", "Close": "Stäng", "Close UniGetUI to the system tray": "Stäng ner UniGetUI till systemfältet", "Close WingetUI to the notification area": "Stäng WingetUI till meddelandefältet", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Molnsäkerhetskopian använder en privat GitHub Gist för att lagra en lista över installerade paket", "Cloud package backup": "Säkerhetskopia av molnpaketet", "Command-line Output": "Kommandoradsutgång", "Command-line to run:": "Kommando att köra:", "Compare query against": "Jämför sökning mot", "Compatible with authentication": "Kompatibel med autentisering", "Compatible with proxy": "Kompatibel med proxy", "Component Information": "Komponent information", "Concurrency and execution": "Samtidighet och utförande", "Connect the internet using a custom proxy": "Anslut internet genom att använda en anpassad proxy", "Continue": "Fortsätt", "Contribute to the icon and screenshot repository": "Bidra till ikonen och skärmbildsförrådet", "Contributors": "B<PERSON><PERSON><PERSON>givar<PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopiera till urklipp", "Could not add source": "Kunde inte lägga till källa", "Could not add source {source} to {manager}": "Det gick inte att lägga till källan {source} till {manager}", "Could not back up packages to GitHub Gist: ": "Kunde inte göra en säkerhetskopia till GitHub Gist:", "Could not create bundle": "Kunde inte skapa ett paketgrupp", "Could not load announcements - ": "Det gick inte att läsa in meddelanden -", "Could not load announcements - HTTP status code is $CODE": "Det gick inte att läsa in meddelanden - HTTP-statuskoden är $CODE", "Could not remove source": "Kunde inte ta bort källa", "Could not remove source {source} from {manager}": "Det gick inte att ta bort källan {source} f<PERSON><PERSON><PERSON> {manager}", "Could not remove {source} from {manager}": "<PERSON>nde inte ta bort {source} f<PERSON><PERSON><PERSON> {manager}", "Create .ps1 script": "Skapa .ps1 skript", "Credentials": "Inloggningsuppgifter", "Current Version": "Aktuell version", "Current status: Not logged in": "Nuvarande status: Inte inloggad", "Current user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom arguments:": "Anpassade argument:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Anpassade kommandoradsargument kan ändra hur program installeras, uppgraders eller avinstalleras på ett sätt som UniGetUI inte kan kontrollera.  Användning av anpassad kommandorad kan bryta paket. Fortsätt med försiktighet.", "Custom command-line arguments:": "Anpassade kommandoradsargument:", "Custom install arguments:": "Anpassade installationsargument:", "Custom uninstall arguments:": "Anpassade avinstallationsargument", "Custom update arguments:": "Anpassade uppdateringsargument", "Customize WingetUI - for hackers and advanced users only": "Anpassa WingetUI - endast för hackare och avancerade användare", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ANSVARSFRISKRIVNING: VI ÄR INTE ANSVARIGA FÖR DE NEDLADDADE PAKETET. SE TILL ATT INSTALLERA ENDAST BETROD MJUKVARA.", "Dark": "M<PERSON><PERSON>", "Decline": "Avböj", "Default": "Standard", "Default installation options for {0} packages": "Standard installationsinställning för {0} paket", "Default preferences - suitable for regular users": "Standardinställningar - lämplig för vanliga användare", "Default vcpkg triplet": "Standard vcpkg triplet", "Delete?": "Ta bort?", "Dependencies:": "Underordnad:", "Descendant": "<PERSON><PERSON><PERSON>", "Description:": "Beskrivning:", "Desktop shortcut created": "Skrivbordsgenväg skapad", "Details of the report:": "Rapportdetaljer:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Det är svårt att utveckla och den här applikationen är gratis. Men om du gillade applikationen kan du alltid <b>köpa en kaffe till mig</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installera direkt när du dubbelklickar på ett objekt i fliken \"{discoveryTab}\" (istället för att visa paketinformationen)", "Disable new share API (port 7058)": "Inaktivera nytt delnings-API (port 7058)", "Disable the 1-minute timeout for package-related operations": "Inaktivera 1-minut timeout f<PERSON><PERSON> paketrelaterade åtgärder", "Disclaimer": "Ansvarsfriskrivning", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON> paket", "Discover packages": "<PERSON><PERSON><PERSON><PERSON> paket", "Distinguish between\nuppercase and lowercase": "<PERSON><PERSON><PERSON> mellan versaler och gemener", "Distinguish between uppercase and lowercase": "<PERSON><PERSON><PERSON> mellan stora och små bokstäver", "Do NOT check for updates": "Sök INTE efter uppdateringar", "Do an interactive install for the selected packages": "Gör en interaktiv installation för de valda paketen", "Do an interactive uninstall for the selected packages": "Gör en interaktiv avinstallation för de valda paketen", "Do an interactive update for the selected packages": "Gör en interaktiv uppdatering för de valda paketen", "Do not automatically install updates when the battery saver is on": "Installera inte uppdateringar automatiskt när strömsparningen är på", "Do not automatically install updates when the network connection is metered": "Installera inte automatiska uppdateringar vid användning av uppkoppling med datapriser", "Do not download new app translations from GitHub automatically": "Ladda inte ner nya appöversättningar från GitHub automatiskt", "Do not ignore updates for this package anymore": "Ignorera inte uppdateringar för denna paket längre", "Do not remove successful operations from the list automatically": "Ta inte bort framgångsrika åtgärder från listan automatiskt", "Do not show this dialog again for {0}": "Visa inte denna dialog igen för {0}", "Do not update package indexes on launch": "Uppdatera inte paketindex vid start", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Accepterar du att UniGetUI samlar in och skickar anonym användarstatistik? Den används endast för att förstå och förbättra använderupplevelsen.", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Tycker du att WingetUI är användbart? Om du kan, kanske du vill stödja mitt arbete, så att jag kan fortsätta att göra WingetUI till det ultimata pakethanteringsgränssnittet.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Tycker du att WingetUI är användbart? Vill du stödja utvecklaren? I så fall kan du {0}, det hjälper mycket!", "Do you really want to reset this list? This action cannot be reverted.": "Vill du verkligen nollställa denna lista? Åtgärden kan inte ångras.", "Do you really want to uninstall the following {0} packages?": "Vill du verkligen avinstallera följande {0}-paket?", "Do you really want to uninstall {0} packages?": "Vill du verkligen avinstallera {0} paket?", "Do you really want to uninstall {0}?": "Vill du verkligen avinstallera {0}?", "Do you want to restart your computer now?": "Vill du starta om din dator nu?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Vill du översätta WingetUI till ditt språk? Se hur du bidrar <a style=\"color:{0}\" href=\"{1}\"a>HÄR!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Känner du inte för att donera? Oroa dig inte, du kan alltid dela WingetUI med dina vänner. Sprid ordet om WingetUI.", "Donate": "<PERSON><PERSON>", "Done!": "<PERSON>lart!", "Download failed": "<PERSON><PERSON><PERSON><PERSON> misslyckad", "Download installer": "Hämta installationsprogrammet", "Download operations are not affected by this setting": "Nedladdningsåtgärder är inte påverkade av denna inställning", "Download selected installers": "Ladda ner valda installationer", "Download succeeded": "Nedladdningen lyckades", "Download updated language files from GitHub automatically": "<PERSON>dda ner upp<PERSON>rade språkfiler från GitHub automatiskt", "Downloading": "<PERSON><PERSON><PERSON> ner", "Downloading backup...": "Laddar ner säkerhetskopia...", "Downloading installer for {package}": "Hämtar installationsfil för {package}", "Downloading package metadata...": "Laddar ned paketets metadata...", "Enable Scoop cleanup on launch": "Aktivera <PERSON>-rensning vid start", "Enable WingetUI notifications": "Aktivera WingetUI-aviseringar", "Enable an [experimental] improved WinGet troubleshooter": "Aktivera en [experimentell] förbättrad WinGet-felsökare", "Enable and disable package managers, change default install options, etc.": "Aktivera och avaktivera pakethanterare, ställa in standardinställningar, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Aktivera bakgrunds CPU användningsoptimering (se Pull-begäran #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktivera bakgrunds-API (WingetUI Widgets and Sharing, port 7058)", "Enable it to install packages from {pm}.": "Aktivera den för att installera paket från {pm}.", "Enable the automatic WinGet troubleshooter": "Aktivera den automatiska WinGet felsökningen", "Enable the new UniGetUI-Branded UAC Elevator": "Aktivera den nya UniGetUI UAC elevatorn", "Enable the new process input handler (StdIn automated closer)": "Aktivera nya processen för inputhan<PERSON>ren (StdIn automatiskt stängning)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Aktivera nedanstående inställningar BARA OCH ENDAST om du helt och fullt förstår vad de gö<PERSON>, eventuella konsekvenser och vilken fara detta kan innebära.", "Enable {pm}": "Aktivera {pm}", "Enter proxy URL here": "Ange URL för proxyn här", "Entries that show in RED will be IMPORTED.": "Inlägg i RÖTT kommer att IMPORTERAS.", "Entries that show in YELLOW will be IGNORED.": "Inlägg i GULT kommer att IGNORERAS.", "Error": "<PERSON><PERSON>", "Everything is up to date": "Allt är uppdaterat", "Exact match": "Exakt matchning", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Befintliga genvägar på ditt skrivbord kommer att skannas och du får välja vilka som ska behållas och vilka ska tas bort.", "Expand version": "Expandera version", "Experimental settings and developer options": "Experimentella inställningar och utvecklaralternativ", "Export": "Exportera", "Export log as a file": "Exportera logg som en fil", "Export packages": "Exportera paket", "Export selected packages to a file": "Exportera valda paket till en fil", "Export settings to a local file": "Exportera inställningar till en lokal fil", "Export to a file": "Exportera till en fil", "Failed": "<PERSON><PERSON><PERSON><PERSON>", "Fetching available backups...": "Hämtar tillgängliga säkerhetskopior...", "Fetching latest announcements, please wait...": "<PERSON><PERSON>m<PERSON> senaste medd<PERSON>, vänligen vänta ...", "Filters": "Filter", "Finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Follow system color scheme": "Följ systemets färgschema", "Follow the default options when installing, upgrading or uninstalling this package": "Följ standardinställningen när du installerar, uppgraderar och avinstallerar denna paket", "For security reasons, changing the executable file is disabled by default": "Av säkerhetsskäl är ändring av körbara filer inte tillåtet som standard", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Av säkerhetsskäl är anpassade kommandoradsargument avstängda som standard. Gå till UniGetUI säkerhetsinställningar för att ändra detta.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Av säkerhetsskäl är pre-operation och post-operation skript avstängda som standard. Gå till UniGetUI säkerhetsinställningar för att ändra detta.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Tvinga ARM-kompilerad winget-version (ENDAST FÖR ARM64-SYSTEM)", "Formerly known as WingetUI": "T<PERSON><PERSON><PERSON> känt som WingetUI", "Found": "Hittat", "Found packages: ": "Hittat paket:", "Found packages: {0}": "Hittade paket: {0}", "Found packages: {0}, not finished yet...": "Hittade paket: {0}, inte klara ännu...", "General preferences": "Allmänna inställningar", "GitHub profile": "GitHub-profil", "Global": "Global", "Go to UniGetUI security settings": "Gå till UniGetUI  säkerhetsinställningar", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Stort lager av okända men användbara verktyg och andra in<PERSON> paket.<br>Innehåller: <b>Verktyg, kommandoradsprogram, allm<PERSON><PERSON> (extra hink krävs)</b>", "Great! You are on the latest version.": "Grattis! Du använder senaste versionen.", "Grid": "Nät", "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help and documentation": "Hjälp och dokumentation", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Här kan du ändra hur UniGetUI beter sig gällande följande genvägar. Markera en genväg och UniGetUI tar bort den vid en framtida uppgradering. Utan markering kommer genvägen bevaras intakt", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, jag heter Martí och jag är <i>utvecklaren</i> av WingetUI. WingetUI har gjorts helt på min fritid!", "Hide details": "<PERSON><PERSON><PERSON><PERSON>", "Homepage": "<PERSON><PERSON><PERSON>", "Hooray! No updates were found.": "Hurra! Inga uppdateringar hittades.", "How should installations that require administrator privileges be treated?": "<PERSON>r ska installationer som kräver administratörsrättigh<PERSON> behandlas?", "How to add packages to a bundle": "<PERSON><PERSON> lägger man till paket i en paketgrupp", "I understand": "Jag förstår", "Icons": "<PERSON><PERSON><PERSON>", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Om du har molnsäkerheskopia valt, kommer den sparas som en GitHub Gist på denna konto", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignorera anpassade pre-install och post-install kommandon vid import av paket från en paketgrupp", "Ignore future updates for this package": "Ignorera framtida upp<PERSON>ringar för de<PERSON> paket", "Ignore packages from {pm} when showing a notification about updates": "<PERSON><PERSON>rera paket från {pm} när en notifiering om uppdatering visas", "Ignore selected packages": "Ignorera valda paket", "Ignore special characters": "Ignorera specialtecken", "Ignore updates for the selected packages": "Ignorera upp<PERSON><PERSON><PERSON> för de valda paketen", "Ignore updates for this package": "Ignorera upp<PERSON><PERSON><PERSON> för <PERSON> paket", "Ignored updates": "Ignorerade uppdateringar", "Ignored version": "Ignorerad version", "Import": "Importera", "Import packages": "Importera paket", "Import packages from a file": "Importera paket från en fil", "Import settings from a local file": "Importera inställningar från en lokal fil", "In order to add packages to a bundle, you will need to: ": "<PERSON><PERSON><PERSON> att lägga till paket i en paketgrupp behöver du:", "Initializing WingetUI...": "Initierar WingetUI...", "Install": "Installera", "Install Scoop": "In<PERSON><PERSON><PERSON>", "Install and more": "Installera och mer", "Install and update preferences": "Installera och uppdatera inställningar", "Install as administrator": "Installera som administratör", "Install available updates automatically": "Installera tillgängliga uppdateringar automatiskt", "Install location can't be changed for {0} packages": "Installationsplatsen kan inte ändras för {0} paketen", "Install location:": "Installationsplats:", "Install options": "Installationsinställningar", "Install packages from a file": "Installera paket från en fil", "Install prerelease versions of UniGetUI": "Installera förhandsversioner av UniGetUI", "Install script": "Installationsskript", "Install selected packages": "Installera valda paket", "Install selected packages with administrator privileges": "Installera valda paket med administratörsbehörighet", "Install selection": "Installera val", "Install the latest prerelease version": "Installera den senaste förhandsversionen", "Install updates automatically": "Installera uppdateringar automatiskt", "Install {0}": "Installera {0}", "Installation canceled by the user!": "Installationen avbröts av användaren!", "Installation failed": "Instal<PERSON><PERSON> misslyckades", "Installation options": "Installationsalternativ", "Installation scope:": "Installationsomfång:", "Installation succeeded": "Installationen lyckades", "Installed Packages": "Installerade paket", "Installed Version": "Installerad version", "Installed packages": "Installerade paket", "Installer SHA256": "Installation SHA256", "Installer SHA512": "Installation SHA512", "Installer Type": "Installations typ", "Installer URL": "Installations URL", "Installer not available": "Installeraren inte tillgänglig", "Instance {0} responded, quitting...": "<PERSON><PERSON><PERSON>omsten {0} svar<PERSON>, avslutar...", "Instant search": "Direktsökning", "Integrity checks can be disabled from the Experimental Settings": "Integritetskontroller kan stängas av från Experimentinställningar", "Integrity checks skipped": "Integritetskontrollerna ignorerades", "Integrity checks will not be performed during this operation": "Integritetskontroller kommer inte utföras i denna åtgärd", "Interactive installation": "Interaktiv installation", "Interactive operation": "Interaktiv åtgärd", "Interactive uninstall": "Interaktiv avinstallation", "Interactive update": "Interaktiv uppdatering", "Internet connection settings": "Inställningar för internetanslutning", "Is this package missing the icon?": "<PERSON>knar detta paket ikonen?", "Is your language missing or incomplete?": "<PERSON>knas ditt språk eller är det ofullständigt?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Kan inte garantera att inloggningsuppgifter kommer att lagras sä<PERSON>t, så du bör inte använda samma inloggning som till din bank", "It is recommended to restart UniGetUI after WinGet has been repaired": "Det rekommenderas att du startar om UniGetUI efter att Winget blivit reparerad", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Rekommenderar starkt att du ominstallerar UniGetUI för att lösa situationen", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Det verkar som om Winget inte fungerar korrekt. Vill du reparera WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Det ser ut som att du körde WingetUI som administratör, vilket inte rekommenderas. Du kan fortfarande använda programmet, men vi rekommenderar starkt att du inte kör WingetUI med administratörsbehörighet. Klicka på \"{showDetails}\" för att se varför.", "Language": "Språk", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tema och andra <PERSON>a preferenser", "Last updated:": "Senast uppdaterad:", "Latest": "Senaste", "Latest Version": "Senaste versionen", "Latest Version:": "Senaste versionen:", "Latest details...": "Senaste <PERSON>...", "Launching subprocess...": "Startar underprocess...", "Leave empty for default": "Lämna tomt som standard", "License": "Licens", "Licenses": "Licenser", "Light": "<PERSON><PERSON><PERSON>", "List": "Lista", "Live command-line output": "Live kommandoradsutgång", "Live output": "Live-utgång", "Loading UI components...": "Laddar UI-komponenter ...", "Loading WingetUI...": "Laddar WingetUI...", "Loading packages": "<PERSON>dd<PERSON> paket", "Loading packages, please wait...": "<PERSON><PERSON><PERSON> paket, vänligen vänta ...", "Loading...": "Laddar...", "Local": "<PERSON><PERSON>", "Local PC": "Lokal PC", "Local backup advanced options": "Avancerade inställningar för lokala säkerhetskopior", "Local machine": "Lokal maskin", "Local package backup": "Lokal säkerhetskopia av paket", "Locating {pm}...": "Letar upp {pm}...", "Log in": "Logga in", "Log in failed: ": "Inloggning misslyckades:", "Log in to enable cloud backup": "Logga in för att aktivera molnsäkerhetskopia", "Log in with GitHub": "Logga in med GitHub", "Log in with GitHub to enable cloud package backup.": "Logga in med GitHub för att aktivera molnsäkerhetskopia av paket", "Log level:": "Loggnivå", "Log out": "Logga ut", "Log out failed: ": "<PERSON><PERSON><PERSON><PERSON><PERSON> missly<PERSON>", "Log out from GitHub": "<PERSON><PERSON>a ut frå<PERSON>", "Looking for packages...": "Letar efter paket...", "Machine | Global": "Maskin | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Felaktig kommandoradsargument kan förstöra paket eller tillåta att angripare utnyttjar för att få höga rättigheter. Därför är import av anpassade kommandoradsargument avstängt som standard.", "Manage": "Hantera", "Manage UniGetUI settings": "Hantera UniGetUI inställningar", "Manage WingetUI autostart behaviour from the Settings app": "Hantera WingetUIs autostartbeteende från Inställningar-appen", "Manage ignored packages": "Hantera ignorerade paket", "Manage ignored updates": "Hantera ignorerade uppdateringar", "Manage shortcuts": "Hantera genvägar", "Manage telemetry settings": "Hantera telemetri-inställningar", "Manage {0} sources": "Hantera {0} k<PERSON><PERSON><PERSON>", "Manifest": "Manifest", "Manifests": "Manifester", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsofts officiella pakethanterare. Full av välkända och verifierade paket<br>Innehåller: <b><PERSON><PERSON><PERSON><PERSON> <PERSON>var<PERSON>, Microsoft Store-appar</b>", "Missing dependency": "<PERSON><PERSON><PERSON> be<PERSON>e", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON>", "More details about the shared data and how it will be processed": "Mer information gällande delad data och hur det hanteras", "More info": "Mer info", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "OBS: <PERSON><PERSON> f<PERSON> kan avaktiveras i inställningar för UniGetUI på WinGet avsnittet", "Name": "<PERSON><PERSON>", "New": "Nytt", "New Version": " Ny version", "New bundle": "Ny paketgrupp", "New version": "Ny version", "Nice! Backups will be uploaded to a private gist on your account": "Fint! Säkerhetskopior kommer att laddas upp till en privat gist på ditt konto", "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": "Ingen lämplig installerare kunde hittas för paket {0}", "No dependencies specified": "Inga specificerade komponenter", "No new shortcuts were found during the scan.": null, "No packages found": "Inga paket hittades", "No packages found matching the input criteria": "Inga paket hittades som matchar inmatningskriterierna", "No packages have been added yet": "Inga paket har lagts till ännu", "No packages selected": "<PERSON>ga paket har valts", "No packages were found": "Inga paket hittades", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Personlig information samlas inte in eller skickas vidare. All insamlad data är anonymiserat och kan inte spåras tillbaka till dig.", "No results were found matching the input criteria": "Inga resultat hittades som matchade inmatningskriterierna", "No sources found": "Inga källor hittades", "No sources were found": "Inga källor hittades", "No updates are available": "Inga uppdateringar är tillgängliga", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS:s pakethanterare. Fullt av bibliotek och andra verktyg som kretsar runt Javascript-världen<br>Innehåller: <b>Node javascript-bibliotek och andra relaterade verktyg</b>", "Not available": "Inte tillgänglig", "Not finding the file you are looking for? Make sure it has been added to path.": "Hittar du inte filen du söker? Säkerställ att den har lagts in i sökvägen.", "Not found": "Hittades inte", "Not right now": "Inte just nu", "Notes:": "Anmärkningar:", "Notification preferences": "Notifieringsinställningar", "Notification tray options": "Alternativ för a<PERSON>", "Notification types": "Notifieringstyper", "NuPkg (zipped manifest)": "NuPkg (zippad manifest)", "OK": "OK", "Ok": "Ok", "Open": "Öppna", "Open GitHub": "Öppna GitHub", "Open UniGetUI": "Öppna UniGetUI", "Open UniGetUI security settings": "Öppna säkerhetsinställningar för UniGetUI", "Open WingetUI": "Öppna WingetUI", "Open backup location": "Öppna plats för säkerhetskopiering", "Open existing bundle": "Öppna befintlig paketgrupp", "Open install location": "Öppna installationsplatsen", "Open the welcome wizard": "Öppna välkomstguiden", "Operation canceled by user": "Åtgärden avbröts av användaren", "Operation cancelled": "Åtgärden avbröts", "Operation history": "Åtgärdshistorik", "Operation in progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Operation on queue (position {0})...": "<PERSON><PERSON><PERSON><PERSON><PERSON> på kö (position {0})...", "Operation profile:": "Åtgärdsprofil", "Options saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Order by:": "Ordning:", "Other": "<PERSON><PERSON><PERSON><PERSON>", "Other settings": "Andra inställningar", "Package": "<PERSON><PERSON>", "Package Bundles": "Paketgrupper", "Package ID": "Paket ID", "Package Manager": "Pakethanterare", "Package Manager logs": "Pakethanteraren loggar", "Package Managers": "Pakethanterare", "Package Name": "Paketnamn", "Package backup": "Säkerhetskopia av paket", "Package backup settings": "Inställningar för paketets säkerhetskopia", "Package bundle": "Paketgrupp", "Package details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package lists": "<PERSON><PERSON><PERSON><PERSON>", "Package management made easy": "Pakethantering på ett enkelt sätt", "Package manager": "Pakethanterare", "Package manager preferences": "Pakethanterarens preferenser", "Package managers": "Pakethanterare", "Package not found": "Paketet hittades inte", "Package operation preferences": "Inställningar för p<PERSON>gärder", "Package update preferences": "Inställningr för <PERSON>", "Package {name} from {manager}": "<PERSON><PERSON> {name} f<PERSON><PERSON><PERSON> {manager}", "Package's default": "Paketets standardvärden", "Packages": "<PERSON><PERSON>", "Packages found: {0}": "Hittade paket: {0}", "Partially": "<PERSON><PERSON>", "Password": "L<PERSON>senord", "Paste a valid URL to the database": "Klistra in en giltig URL i databasen", "Pause updates for": "Pausa uppdateringar för", "Perform a backup now": "G<PERSON>r en säkerhetskopia nu", "Perform a cloud backup now": "G<PERSON>r en molnsäkerhetskopia nu", "Perform a local backup now": "Gör en lokal säkerhetskopia nu", "Perform integrity checks at startup": "Genomför integritetskontroller vid start", "Performing backup, please wait...": "Säkerhetskopiering utförs, vänligen vänta...", "Periodically perform a backup of the installed packages": "Utför regelbundet säkerhetskopiering av de installerade paketen", "Periodically perform a cloud backup of the installed packages": "G<PERSON>r periodiska molnsäkerhetskopior av installerade paket", "Periodically perform a local backup of the installed packages": "G<PERSON>r periodiska lokala säkerhetskopior av installerade paket", "Please check the installation options for this package and try again": "Vänlgien kontrollera installationsinställningarna för paketet och prova igen", "Please click on \"Continue\" to continue": "Vänligen klicka på \"Fortsätt\" för att fortsätta", "Please enter at least 3 characters": "Vänligen ange minst 3 tecken", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Observera att vissa paket kanske inte går att installera på grund av de pakethanterare som är aktiverade på den här maskinen.", "Please note that not all package managers may fully support this feature": "Vänligen notera att alla pakethanterare kanske inte fullt stöder denna funktion", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Observera att paket från vissa källor kanske inte går att exportera. De är utgråade och kommer inte att exporteras.", "Please run UniGetUI as a regular user and try again.": "Vänligen kör UniGetUI  som en vanlig användare och prova igen.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Se kommandoradens utdata eller se Operation History för mer information om problemet.", "Please select how you want to configure WingetUI": "Var vänlig välj hur du vill konfigurera WingetUI", "Please try again later": "<PERSON><PERSON>rs<PERSON>k igen senare", "Please type at least two characters": "Vänligen skriv minst två tecken", "Please wait": "Vänligen vänta", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Vänligen vänta medan {0} blir installerad. En svart (eller blå) fönster kan dyka upp. Vänta tills den stängs.", "Please wait...": "Vänligen vänta...", "Portable": "Portabel", "Portable mode": null, "Post-install command:": "Post-install kommando:", "Post-uninstall command:": "Post-uninstall kommando:", "Post-update command:": "Post-update kommando:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShells pakethanterare. Hitta bibliotek och skript för att utöka PowerShell-funktionerna<br>Innehåller: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Pre och post install kommandon kan orsaka otäcka saker på din enhet. Det kan bli farligt att importera kommandon från en paketgrupp såvida du inte litar på källa till paketgruppen.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Pre och post install kommandon kommer att köras före och efter paketet blir installerad, uppgraderad eller avinstallerad. Var medveten om att de kan göra skada om man inte är försiktig", "Pre-install command:": "Pre-install kommando:", "Pre-uninstall command:": "Pre-uninstall kommando:", "Pre-update command:": "Pre-update kommando:", "PreRelease": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON> pake<PERSON>, vänligen vänta ...", "Proceed at your own risk.": "Fortsätt på egen risk", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Förhindra all typ av behörighetshöjning via UniGetUI Elevator eller GSudo", "Proxy URL": "Proxy URL", "Proxy compatibility table": "Proxy kompabilitetstabell", "Proxy settings": "Proxyinställningar", "Proxy settings, etc.": "Proxyinställningar m.m.", "Publication date:": "Utgivningsdatum", "Publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Pythons pakethanterare. Fullt med pythonbibliotek och andra pythonrelaterade verktyg<br><PERSON><PERSON><PERSON><PERSON>: <b>Pythonbibliotek och relaterade verktyg</b>", "Quit": "Stäng", "Quit WingetUI": "Stäng WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Reducera UAC-rutor, förhöj installationen som standard, l<PERSON>s upp vissa farliga funktioner m.m.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Hänvisar till UniGetUI:s loggar för mer detaljerad information om påverkad(e) filer.", "Reinstall": "Ominstallera", "Reinstall package": "Installera om paketet", "Related settings": "Relaterade inställningar", "Release notes": "Versionsinformation", "Release notes URL": " Versionsinformation URL", "Release notes URL:": " Versionsinformation URL:", "Release notes:": "Versionsinformation:", "Reload": "Ladda om", "Reload log": "Ladda om loggen", "Removal failed": "<PERSON>rt<PERSON><PERSON> misslyckades", "Removal succeeded": "Borttagning lyckades", "Remove from list": "<PERSON> bort från listan", "Remove permanent data": "Ta bort permanent data", "Remove selection from bundle": "Ta bort urval från paketgruppen", "Remove successful installs/uninstalls/updates from the installation list": "Ta bort lyckade installationer/avinstallationer/uppdateringar från installationslistan", "Removing source {source}": "Tar bort k<PERSON>llan {source}", "Removing source {source} from {manager}": "<PERSON> bort källa {source} f<PERSON><PERSON><PERSON> {manager} ", "Repair UniGetUI": "Reparera UniGetUI", "Repair WinGet": "Reparera WinGet", "Report an issue or submit a feature request": "Rapportera fel eller skicka begäran om förbättringar", "Repository": "Källkodsförråd", "Reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reset Scoop's global app cache": "Nollställ Scoops globala appcache", "Reset UniGetUI": "Återställ UniGetUI", "Reset WinGet": "Återställ WinGet", "Reset Winget sources (might help if no packages are listed)": "Nolställ WinGets källor (kan hjälpa ifall paket inte listas)", "Reset WingetUI": "Återställ UniGetUI", "Reset WingetUI and its preferences": "Nollställ UniGetUI och dess inställningar", "Reset WingetUI icon and screenshot cache": "Nollställ UniGetUI ikonen och skärmdumpscachen", "Reset list": "Återställ lista", "Resetting Winget sources - WingetUI": "Nollställer WinGets källor - UniGetUI ", "Restart": "Starta om", "Restart UniGetUI": "Starta om UniGetUI", "Restart WingetUI": "Starta om UniGetUI", "Restart WingetUI to fully apply changes": "Starta om UniGetUI för ändringarna ska tillämpas", "Restart later": "Starta om senare", "Restart now": "Starta om nu", "Restart required": "Omstart krävs", "Restart your PC to finish installation": "Starta om din PC för att slutföra installationen", "Restart your computer to finish the installation": "Starta om din dator för att avsluta installationen", "Restore a backup from the cloud": "Återställ säkerhetskopia från molnet", "Restrictions on package managers": "Restriktion<PERSON> för <PERSON>", "Restrictions on package operations": "Restriktioner för <PERSON>gärder", "Restrictions when importing package bundles": "Restriktioner vid import av paketgrupper", "Retry": "Försök igen", "Retry as administrator": "Försök igen som administatör", "Retry failed operations": "Försök igen de åtgärder som misslyckades", "Retry interactively": "Försök igen interaktivt", "Retry skipping integrity checks": "Försök att ignorera integritetskontrollerna igen", "Retrying, please wait...": "Förs<PERSON>ker igen, vänligen vänta...", "Return to top": "Gå överst", "Run": "<PERSON><PERSON><PERSON>", "Run as admin": "<PERSON><PERSON><PERSON> som admin", "Run cleanup and clear cache": "<PERSON><PERSON><PERSON> städning och töm cachen", "Run last": "<PERSON><PERSON><PERSON>", "Run next": "<PERSON><PERSON><PERSON>", "Run now": "<PERSON><PERSON><PERSON> nu", "Running the installer...": "<PERSON><PERSON><PERSON>...", "Running the uninstaller...": "<PERSON><PERSON><PERSON>", "Running the updater...": "<PERSON><PERSON><PERSON> upp<PERSON>...", "Save": "Spara", "Save File": "Spara fil", "Save and close": "Spara och stäng", "Save as": "Spara som", "Save bundle as": "Spara paketgruppen som", "Save now": "Spara nu", "Saving packages, please wait...": "<PERSON><PERSON> paket, vänligen vänta...", "Scoop Installer - WingetUI": "Scoop installerare - UniGetUI", "Scoop Uninstaller - WingetUI": "Scoop avinstallerare - UniGetUI", "Scoop package": "<PERSON><PERSON>-paket", "Search": "<PERSON>ö<PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "<PERSON><PERSON><PERSON> för skri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, varna mig när uppdateringar är tillgänliga och gör inga nördiga saker. Jag vill inte att UniGetUI ska överkomplicera saker och ting, jag vill bara ha en enkel <b>mjukvara butik</b>", "Search for packages": "<PERSON><PERSON><PERSON> efter paket", "Search for packages to start": "<PERSON><PERSON><PERSON> efter paket att starta", "Search mode": "Sökläge", "Search on available updates": "Sök efter tillgängliga uppdateringar", "Search on your software": "Sök på din mjukvara", "Searching for installed packages...": "Söker efter installerade paket...", "Searching for packages...": "Letar efter paket...", "Searching for updates...": "Letar efter uppdateringar...", "Select": "<PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Väl \"{item}\" att läcka till din anpassade katalog", "Select a folder": "<PERSON><PERSON><PERSON><PERSON> mapp", "Select all": "<PERSON><PERSON><PERSON><PERSON> alla", "Select all packages": "<PERSON><PERSON><PERSON><PERSON> alla paket", "Select backup": "Välj säkerhetskopia", "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON>a <b>om du vet vad du gör</b>.", "Select package file": "<PERSON><PERSON><PERSON><PERSON>", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Välj säkerhetskopian du vill öppna. Senare får du möjlighet att välja vilka paket/program du vill återställa.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "<PERSON><PERSON><PERSON><PERSON> de processer som bör stängas innan paketet installeras, uppdateras eller avinstalleras.", "Select the source you want to add:": "<PERSON><PERSON><PERSON><PERSON> källan du vill lägga till:", "Select upgradable packages by default": "Välj uppgraderingsbara paket som standard", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Vä<PERSON>j vilket <b>pakethanterare</b> du vill använda ({0}), st<PERSON><PERSON> hur paketen ska installeras, hantera hur adminrättigheter hanteras m.m.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handskakning skickad. Väntar för mottagarens svar... ({0}%)", "Set a custom backup file name": "St<PERSON>ll in filnamnet för den anpassade säkerhetskopian", "Set custom backup file name": "St<PERSON>ll in filnamnet för den anpassade säkerhetskopian", "Settings": "Inställningar", "Share": "Dela", "Share WingetUI": "Dela UniGetUI", "Share anonymous usage data": "Dela anonym användardata", "Share this package": "<PERSON>a detta paket", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Skulle du ändra säkerhetsinställningarna behöver du öppna paketgruppen igen för att ändringarna ska slå igenom.", "Show UniGetUI on the system tray": "Visa UniGetUI i aktivitetsfältet", "Show UniGetUI's version and build number on the titlebar.": "Visa UniGetUI:s version i titelraden", "Show WingetUI": "Visa UniGetUI", "Show a notification when an installation fails": "Visa notifikation om en installation misslyckas", "Show a notification when an installation finishes successfully": "Visa notifikation när en installation lyckas", "Show a notification when an operation fails": "Visa en notifiering när en <PERSON><PERSON><PERSON><PERSON><PERSON> miss<PERSON>as", "Show a notification when an operation finishes successfully": "Visa en notering när en åtg<PERSON><PERSON> ly<PERSON>as", "Show a notification when there are available updates": "Visa en notifiering när en uppdatering finns tillgänglig", "Show a silent notification when an operation is running": "Visa en tyst notifiering när en åtgärd körs", "Show details": "Visa detaljer", "Show in explorer": "Visa i utforskaren", "Show info about the package on the Updates tab": "Visa information om paketet i uppdateringsfliken", "Show missing translation strings": "Visa saknade översättningssträngar", "Show notifications on different events": "Visa notifiering om olika händelser", "Show package details": "Visa paket-detaljer", "Show package icons on package lists": "Visa paketikoner på paketlistan", "Show similar packages": "Visa liknande paket", "Show the live output": "Visa realtidsinformation", "Size": "Storlek", "Skip": "<PERSON><PERSON>", "Skip hash check": "Ignorera hash kontrollen", "Skip hash checks": "Ignorera hash kontrollerna", "Skip integrity checks": "Ignorera integritetskontrollerna", "Skip minor updates for this package": "Ignorera mindre uppdate<PERSON><PERSON> för denna paket", "Skip the hash check when installing the selected packages": "Ignorera hash kontrollen när valda paketen installeras", "Skip the hash check when updating the selected packages": "Ignorera hash kontrollen när valda paketen uppdateras", "Skip this version": "Ignorera denna version", "Software Updates": "Uppdateringar", "Something went wrong": "<PERSON><PERSON><PERSON> gick fel", "Something went wrong while launching the updater.": "Något gick fel vid starten av uppdateraren", "Source": "<PERSON><PERSON><PERSON>", "Source URL:": "Källa URL:", "Source added successfully": "<PERSON><PERSON><PERSON> lades till", "Source addition failed": "Inläggning av källan misslyckades", "Source name:": "Källnamnet:", "Source removal failed": "Borttagning av källan misslyckades", "Source removed successfully": "Borttagning av källan lyckades", "Source:": "Källa:", "Sources": "<PERSON><PERSON><PERSON><PERSON>", "Start": "Start", "Starting daemons...": "Startar daemons...", "Starting operation...": "<PERSON>ar å<PERSON>...", "Startup options": "Uppstartsalternativ", "Status": "Status", "Stuck here? Skip initialization": "Fastnade här? Hoppa över initieringen", "Success!": "Lyckades!", "Suport the developer": "<PERSON><PERSON><PERSON>", "Support me": "<PERSON><PERSON><PERSON> mig", "Support the developer": "<PERSON><PERSON><PERSON>", "Systems are now ready to go!": "Systemen är nu redo att köras!", "Telemetry": "Telemetri", "Text": "Text", "Text file": "Textfil", "Thank you ❤": "Tack ❤", "Thank you 😉": "Tack 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust pakethanteraren. <br><PERSON><PERSON><PERSON><PERSON>. <b>Rust bibliotek och program skriva i Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Säkerhetskopian kommer INTE att inkludera några binärä filer eller sparas data från något program.", "The backup will be performed after login.": "Säkerhetskopiering kommer att utföras efter inloggning.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Säkerhetskopia inkluderar en komplett lista över installerade paket och dess installationsinställningar. Ignorerade uppdateringar och version kommer också att sparas.", "The bundle was created successfully on {0}": "Paketgruppen skapades den {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Paketgruppen du försöker ladda in verkar vara defekt. Vänligen kontrollera filen och prova igen.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Installerarens checksum överensstämmer inte med förväntad värde och därför kan inte dess äkthet bekräftas. Om du litar på utgivaren, {0} paketet igen och ignorera hash kontrollen.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Den klassiska pakethanteren för Windows. Du hittar allt där. <br><PERSON>ehåller: <b><PERSON><PERSON>ä<PERSON> mju<PERSON></b>", "The cloud backup completed successfully.": "Molnsäkerhetskopian färdigställdes.", "The cloud backup has been loaded successfully.": "Molnsäkerhetskopian har laddats.", "The current bundle has no packages. Add some packages to get started": "Nuvarande paketgruppen innehåller inga paket. Lägg till några paket för att komma igång", "The executable file for {0} was not found": "Körbara filen för {0} kunde inte hittas", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Följäande åtgärder kommer tillämpas som standard varje gång en {0} paket installeras, uppgraderas eller avinstalleras.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Följande paket kommer att exporteras till en JSON-fil.  Användardata och binärfiler kommer inte att sparas.", "The following packages are going to be installed on your system.": "Följande paket kommer att installeras på ditt system.", "The following settings may pose a security risk, hence they are disabled by default.": "Följander inställningar kan innebär en säkerhetsrisk och därför är det inaktiverad som standard.", "The following settings will be applied each time this package is installed, updated or removed.": "Följande inställningar kommer att tillämpas varje gång detta paket installeras, uppdateras eller avinstalleras.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Följande inställningar kommer att tillämpas varje gånger detta paket installeras, uppdateras eller avinstalleras. Inställningarna sparas automatiskt.", "The icons and screenshots are maintained by users like you!": "Ikoner och skärmdumpar upprätthålls av användare som dig!", "The installation script saved to {0}": "Installationskriptet sparades till {0}", "The installer authenticity could not be verified.": "Installerarens äkthet kunde inte verifieras.", "The installer has an invalid checksum": "Installeraren har en ogiltig checksum.", "The installer hash does not match the expected value.": "Installerarens hash överensstämmer inte med förväntat värde.", "The local icon cache currently takes {0} MB": "Lokala ikoncache upptar för n<PERSON>  {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Syftet med detta projekt är att skapa en naturlig UI för de vanligaste CLI-pakethanterare för Windows, såsom WinGet och Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Paket {0} kunde inte hittas i pakethanteraren {1}", "The package bundle could not be created due to an error.": "Paketgruppen kunde inte skapas på grund av något fel.", "The package bundle is not valid": "Paketgruppen är inte giltig", "The package manager \"{0}\" is disabled": "Pakethanterare {0} är inaktiverad", "The package manager \"{0}\" was not found": "Pakethanteraren {0} kunde inte hittas", "The package {0} from {1} was not found.": "<PERSON><PERSON> {0} f<PERSON><PERSON><PERSON> {1} kunde inte hittas.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Paketen listade här kommer inte tas hänsyn till vid uppdatering. Dubbelklicka på dem eller klicka på knappen till höger om dessa för att sluta ignorera dessa uppdateringar.", "The selected packages have been blacklisted": "Valda paket har blivit svartl<PERSON>de", "The settings will list, in their descriptions, the potential security issues they may have.": "Inställningarna visar i dess beskrivning vilka eventuella säkerhetsrisker de kan ha.", "The size of the backup is estimated to be less than 1MB.": "Säkerhetskopians storlek är beräknad till mindre än 1 MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON> {source} har lagts till i {manager}", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON> {source} to<PERSON> bort från {manager}", "The system tray icon must be enabled in order for notifications to work": "Systemfältsikonen måste aktiveras för att notifieringar ska fungera", "The update process has been aborted.": "Uppdateringsprocessen har avbrutits", "The update process will start after closing UniGetUI": "Uppdateringen startar när UniGetUI stängs", "The update will be installed upon closing WingetUI": "Uppdateringen startar när UniGetUI stängs", "The update will not continue.": "Uppdateringen kommer inte att fortsätta.", "The user has canceled {0}, that was a requirement for {1} to be run": "<PERSON>v<PERSON><PERSON><PERSON> har avbrutit {0}, det fanns ett krav att {1} måste köras", "There are no new UniGetUI versions to be installed": "Det finns ingen nyare UniGetUI version att installera", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Det finns pågående åtgärder. Genom att stänga UniGetUI kan dessa misslyckas. Vill du fortsätta?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Det finns några bra videor på YouTube som visar UniGetUI och dessa funktioner. <PERSON>är kan du lära dig användbara tips och trix!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Det finns två orsaker till att inte köra UniGetUI som admin:\nDet första är att Scoop pakethanterare kan orsaka problem med vissa kommandon om dessa körs med adminrättigheter.\nDet andra är att om UniGetUI körs med adminrättigheter så kan alla paket du laddar ner kunna köras som admin och det är inte säkert.", "There is an error with the configuration of the package manager \"{0}\"": "Ett fel uppstod vid inställning av pakethanteraren {0}", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Det finns en pågående installation. Om du stänger UniGetUI kan installationen misslyckas eller orsaka problem. Vill du ändå stänga av UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Dessa är programmet som är ansvariga för installation, uppdatering och borttagning av paket.", "Third-party licenses": "Tredjepartslicenser", "This could represent a <b>security risk</b>.": "<PERSON>ta kan innebära en <b>en säkerhetsrisk</b>.", "This is not recommended.": "Detta är inte rekommenderat.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Detta orsakades troligen av att paketet du skickade är borttagen eller publicerad av en pakethanterare du inte har aktiverat. Mottagar ID:n är {0}", "This is the <b>default choice</b>.": "<PERSON><PERSON> <b>standardvalet</b>.", "This may help if WinGet packages are not shown": "<PERSON>ta kan hjä<PERSON>pa ifall WinGet paketen inte visas", "This may help if no packages are listed": "<PERSON>ta kan hjä<PERSON>pa ifall inga paket listas.", "This may take a minute or two": "<PERSON>ta kan ta en minut eller två", "This operation is running interactively.": "<PERSON><PERSON> körs interaktivt.", "This operation is running with administrator privileges.": "<PERSON><PERSON> körs med adminrättigheter.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "<PERSON>na inställning KOMMER att skapa problem. Alla åtgärder som inte kan höja sig själv KOMMER MISSLYCKAS. Installering/uppdatering/avinstallering som administrator kommer INTE FUNGERA.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Denna paketgrupp har några inställningar som kan vara farliga och som kan åsidosättas som standard.", "This package can be updated": "<PERSON>na paket kan upp<PERSON>ras", "This package can be updated to version {0}": "<PERSON>na paket kan uppdateras till version {0}", "This package can be upgraded to version {0}": "<PERSON>na paket kan uppgraderas till version {0}", "This package cannot be installed from an elevated context.": "<PERSON>na paket kan inte installeras med adminrättigheter.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Denna paket har ingen skärmdump eller saknas ikonen. Bidra till UniGetUI genom att lägga till den saknade ikonen och skärmdumpa den till vår öppna och publika databas.", "This package is already installed": "<PERSON><PERSON> paket är redan installerad", "This package is being processed": "<PERSON><PERSON>", "This package is not available": "Paketet är inte tillgängligt", "This package is on the queue": "Paketet är köat", "This process is running with administrator privileges": "<PERSON><PERSON> körs med adminrättigheter", "This project has no connection with the official {0} project — it's completely unofficial.": "Denna projekt har ingen koppling med den officiella {0} projektet, den är helt inofficiell.", "This setting is disabled": "Inställningen är avaktiverad", "This wizard will help you configure and customize WingetUI!": "Guiden vill hjä<PERSON>pa dig att ställa in och anpassa UniGetUI!", "Toggle search filters pane": "Växla sökfiltrets fönster", "Translators": "Översättare", "Try to kill the processes that refuse to close when requested to": "Försök att avsluta den process som vägrar att stänga", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Genom att aktivera denna kan körbara filer interagera med pakethanterare. Detta möjliggör en mer exakt anpassning av installationsprocessen, men den gör den också mer farlig", "Type here the name and the URL of the source you want to add, separed by a space.": "Skriv in namn och URL till källan du vill lägga till, separera med ett mellanslag.", "Unable to find package": "Kan inte hitta paketet", "Unable to load informarion": "Kan inte läsa in informationen", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI hämtar anonym data för att förbättra användarupplevelsen.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI hämtar anonymiserad användardata endast för att förstå och förbättra användarupplevelsen.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI  har hittat ett nytt skrivbordsgenvän som kan tas bort automatiskt.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI har hittat följande skrivsbordsgenväg som kan tas bort automatiskt på kommande uppgraderingar", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI  har hittat {0} nytt skrivbordsgenväg som kan tas bort automatiskt.", "UniGetUI is being updated...": "UniGetUI uppdateras...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI har inget att göra med de kompatibla pakethanterarna. UniGetUI  är en självständigt projekt.", "UniGetUI on the background and system tray": "UniGetUI i bakgrunden och i systemfältet", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI  eller någon av dess komponenter saknas eller är korrupta.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI kräver {0} för att fungera, men den kan inte hittas i ditt system.", "UniGetUI startup page:": "UniGetUI:s startsida:", "UniGetUI updater": "UniGetUI updaterare", "UniGetUI version {0} is being downloaded.": "UniGetUI version {0} laddas nu ner.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} är redo att installeras.", "Uninstall": "Avin<PERSON>lera", "Uninstall Scoop (and its packages)": "Avinstallera Scoop och dess paket", "Uninstall and more": "Avinstallera och mera", "Uninstall and remove data": "Avinstallera och ta bort data", "Uninstall as administrator": "Avinstallera som administratör", "Uninstall canceled by the user!": "Avinstallation avbröts av användaren!", "Uninstall failed": "Avinstallation misslyckad", "Uninstall options": "Avinstallationinställningar", "Uninstall package": "<PERSON><PERSON><PERSON><PERSON><PERSON> paket", "Uninstall package, then reinstall it": "Avinstallera paket och sedan ominstallera den", "Uninstall package, then update it": "Avinstallera paket, uppdatera den sedan", "Uninstall previous versions when updated": "Avinstallera föregående versioner vid uppdatering", "Uninstall selected packages": "Avinstallera valda paket", "Uninstall selection": "Avinstallera urval", "Uninstall succeeded": "Avinstallation lyckad", "Uninstall the selected packages with administrator privileges": "Avinstallera valda paket med adminrättighet", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Paket som inte går att avinstallera listande som {0} är inte publicerade i någon pakethantera. Så det finns ingen information tillgänglig.", "Unknown": "Okä<PERSON>", "Unknown size": "<PERSON>änd storlek", "Unset or unknown": "Avstängt eller okänt", "Up to date": "Senaste versionen", "Update": "Uppdatera", "Update WingetUI automatically": "Uppdatera UniGetUI automatiskt", "Update all": "Uppdatera alla", "Update and more": "<PERSON>p<PERSON><PERSON> och mer", "Update as administrator": "Uppdatera som administratör", "Update check frequency, automatically install updates, etc.": "Uppdate<PERSON><PERSON><PERSON><PERSON><PERSON>, automatiska uppdateringar m.m.", "Update checking": null, "Update date": "<PERSON><PERSON> för uppdate<PERSON>", "Update failed": "Upp<PERSON><PERSON> misslyck<PERSON>", "Update found!": "Uppdatering hittad!", "Update now": "Uppdatera nu", "Update options": "Uppdateringsinställningar", "Update package indexes on launch": "Uppdateringspaketet indexeras vid start", "Update packages automatically": "Uppdatera paket automatiskt", "Update selected packages": "Upp<PERSON><PERSON> valda paket", "Update selected packages with administrator privileges": "Uppdatera valda paket med adminrättigheter", "Update selection": "Uppdatera valda", "Update succeeded": "Upp<PERSON><PERSON> lyckad", "Update to version {0}": "Uppdatera till version {0}", "Update to {0} available": "Uppdatering till {0} är tillg<PERSON>g", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Uppdatera vcpkg:s Git portfiler automatiskt (kräver installerad Git)", "Updates": "Uppdateringar", "Updates available!": "Uppdateringar tillgängliga!", "Updates for this package are ignored": "Upp<PERSON><PERSON><PERSON> för denna paket <PERSON>", "Updates found!": "Uppdateringar hittade!", "Updates preferences": "Uppdateringsinställningar", "Updating WingetUI": "Uppdaterar UniGetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Använd Legacy paketgruppen av WinGet istället för PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Använd en anpassad icon och skärmdumps databas URL", "Use bundled WinGet instead of PowerShell CMDlets": "Använd paketgruppen WinGet istället för PowerShell CMDLets", "Use bundled WinGet instead of system WinGet": "Använd paketgruppen WinGet istället för system WinGet", "Use installed GSudo instead of UniGetUI Elevator": "Används installerad GSudo istället för UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Använd installerad GSudir istället för paketgruppsvarianten", "Use system Chocolatey": "Använd Chocolatey-systemet", "Use system Chocolatey (Needs a restart)": "Används Chocolatey-systemet (kräver omstart)", "Use system Winget (Needs a restart)": "Använd Winget-systemet (kräver omstart)", "Use system Winget (System language must be set to english)": "<PERSON><PERSON><PERSON><PERSON> WinGet (Systemspråken måste vara Engelska)", "Use the WinGet COM API to fetch packages": "Använder WinGet COM API för att hämta paket", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Använd WinGet Poweshell modul istället för WinGet COM API", "Useful links": "Användbara länkar", "User": "Användare", "User interface preferences": "Utseendeinstälningar", "User | Local": "Användare | Lokal", "Username": "Användarnamn", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Användning av UniGetUI innebär att du accepterar GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Användning av UniGetUI innebär att du accepterar MIT licensen", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "VCPkg roten kunde inte hittas. Vänligen definiera %VCPKG_ROOT% miljövariabeln eller ställ in det i UniGetUI:s inställningar.", "Vcpkg was not found on your system.": "Vcpkg kunde inte hittas på ditt system.", "Verbose": "Utökad", "Version": "Version", "Version to install:": "Version att installera:", "Version:": "Version:", "View GitHub Profile": "Visa GitHub-profil", "View WingetUI on GitHub": "Visa UniGetUI på GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Se UniGetUI:s källkod. Där kan du anmäla buggar eller förslå förbättringar eller bidra direkt till UniGetUI projektet.", "View mode:": "Visningsläge:", "View on UniGetUI": "Visa i UniGetUI", "View page on browser": "Visa sidan i webbläsaren", "View {0} logs": "Se {0} loggar", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Vänta tills enheten är ansluten till internet innan du börjar med uppgifter som kräver internetuppkoppling.", "Waiting for other installations to finish...": "Väntar på att andra installationer blir klara...", "Waiting for {0} to complete...": "<PERSON>ä<PERSON>r på att {0} blir klar...", "Warning": "Varning", "Warning!": "Varning!", "We are checking for updates.": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Kunde inte ladda detaljerad information om denna paket, det fanns ingen information i paketets källa.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Kunde inte ladda detaljerad information om denna paket, den blev inte installerad med någon tillgängliga pakethanterare.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Kunde inte {action} {package}. Vänligen prova igen senare. Klicka på {showDetails} för att få loggfiler från installeraren.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Kunde inte {action} {package}. Vänligen prova igen senare. Klicka på {showDetails} för att få loggfiler från avinstalleraren.", "We couldn't find any package": "Kunde inte hitta några paket", "Welcome to WingetUI": "Välkommen till UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Vid batch-installation av paket från en paketgruppen, installera även paket som är redan installerade.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON><PERSON>r nya genv<PERSON><PERSON> hittas, raderas de automatiskt istället för att visa denna dialog.", "Which backup do you want to open?": "Vilken säkerhetskopia vill du öppna?", "Which package managers do you want to use?": "Vilken pakethanterare vill du använda?", "Which source do you want to add?": "Vilken källa vill du lägga till?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "WinGet kan användas inom UniGetUI. UniGetUI kan användas av andra pakethanterare och detta kan upplevas förvirrande. Men förr så var UniGetUI gjord för att endast fungera med WinGet. Så är det inte längre och därför kan inte det som då hette WinGetUI längre representera vad det här projektet siktar på att bli", "WinGet could not be repaired": "WinGet kunde inte repareras", "WinGet malfunction detected": "WinGet fel hittades", "WinGet was repaired successfully": "WinGet har reparerats", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Allt är uppdaterat", "WingetUI - {0} updates are available": "UniGetUI - {0} uppdateringar är tillgängliga", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "UniGetUI Websida", "WingetUI Homepage - Share this link!": "UniGetUI Websida - Dela länken!", "WingetUI License": "UniGetUI-licens", "WingetUI Log": "UniGetUI logg", "WingetUI Repository": "UniGetUI Repository", "WingetUI Settings": "UniGetUI inställningar", "WingetUI Settings File": "UniGetUI inställningsfil", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI använder följande bibliotek. Utan dessa hade inte UniGetUI funnits.", "WingetUI Version {0}": "UniGetUI version {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI autostartsbeteende, startinställningar", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI kan kontrollera om din mjukvara har tillgängliga uppdateringar och installera dessa automatiskt", "WingetUI display language:": "UniGetUI visningsspråk", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI har körts med adminrättigheter vilket inte är att rekommendera. VARJE åtgärd kommer då ha adminrättigheter. Du kan fortfarande använda programmet men vi rekommenderar starkt att INTE köra UniGetUI med adminrättigheter.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI är översatt till mer än 40 språk tack vara våra frivilliga översättare. Tack 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": " UniGetUI är inte maskinöversatt! Följande användare har varit ansvariga för översättningarna:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI är ett program som mjukvaruhanteringen enklare genom att erbjuda ett tilltalande grafiskt gränssnitt för dina kommandorads pakethanterare.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI har fått en nytt namn för att tydligt visa skillnaden mellan UniGetUI (gränssnittet du använder nu) och WinGet (en pakethanterare utvecklad av Microsoft vilka vi inte har någon koppling med alls)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI uppdateras. När den är klar så kommer UniGetUI att starta om sig själv", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI är gratis och kommer alltid att vara gratis. Ingen reklam, inga kredit<PERSON>t, ingen premiumversio. 100% gratis, för alltid.", "WingetUI log": "UniGetUI logg", "WingetUI tray application preferences": "UniGetUI systemfältsinställningar", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI använder följande bibliotek. Utan dessa hade inte UniGetUI funnits.", "WingetUI version {0} is being downloaded.": "UniGetUI version {0} laddas nu ner.", "WingetUI will become {newname} soon!": "WingetUI blir snart {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI kommer inte kontrollera uppdateringar periodiskt. De kontrolleras vid start och du får får ingen varning.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI kommer via en UAC dialogruta varje gång ett paket kräver förhöjd behörighet för att installeras.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "UniGetUI kommer snart att heta {newname}. Det betyder ingen ändring i programmet. Jag, utvecklaren kommer att fortsätta utveckla projektet precis som idag fast under ett annat namn.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI skulle inte vara möjlig utan all hjälp av våra medarbetare. Koll ain deras GitHub.produkter. Utan dem skulle inte UniGetUI vara möjlig!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI skulle inte finns utan all hjälp från våra medarbetare. Ett stort tack! 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} är redo att installeras.", "Write here the process names here, separated by commas (,)": "Skriv in processnamnen här, skilj <PERSON>t med komma (,)", "Yes": "<PERSON>a", "You are logged in as {0} (@{1})": "Du är inloggad som {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Du kan ändra detta beteende i UniGetUI:s säkerhetsinställningar.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Du kan definiera kommandon som ska köras före eller efter att paketet installeras, uppdateras eller avinstalleras. De körs på en kommandotolk, så CMD skript kommer att fungera här.", "You have currently version {0} installed": "Du har just nu version {0} installerad", "You have installed WingetUI Version {0}": "Du har installerat UniGetUI version {0}", "You may lose unsaved data": "Osparad data kan förloras", "You may need to install {pm} in order to use it with WingetUI.": "Du kan behöva installera {pm} för att kunna använda den med UniGetUI.", "You may restart your computer later if you wish": "Du kan starta om din dator senare om du vill", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Du kommer bli påmind endast en gång och adminrättigheter kommer att ges till paket som önskar ha det.", "You will be prompted only once, and every future installation will be elevated automatically.": "Du kommer bli påmind endast en gång och varje framtida installation kommer att få ökade rättigheter automatiskt.", "You will likely need to interact with the installer.": "Du måste troligen interagera med installeraren.", "[RAN AS ADMINISTRATOR]": "KÖRS SOM ADMIN", "buy me a coffee": "köp mig en kaffe", "extracted": "extraherad", "feature": "funktion", "formerly WingetUI": "<PERSON><PERSON><PERSON>", "homepage": "websida", "install": "installera", "installation": "installation", "installed": "installerad", "installing": "installeras", "library": "bibliotek", "mandatory": "obligatorisk", "option": "inställning", "optional": "val<PERSON>ri", "uninstall": "avinstallera", "uninstallation": "avinstallation", "uninstalled": "avinstallerad", "uninstalling": "avinstallerar", "update(noun)": "uppdatering", "update(verb)": "uppdatera", "updated": "uppdaterad", "updating": "Uppdaterar", "version {0}": "version {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} installationsinställningar är låsta på grund av att {0} följer standardinställningarna.", "{0} Uninstallation": "{0} avinstallation", "{0} aborted": "{0} a<PERSON><PERSON><PERSON><PERSON>", "{0} can be updated": "{0} kan uppdateras", "{0} can be updated to version {1}": "{0} kan uppdateras till version {1}", "{0} days": "{0} dagar", "{0} desktop shortcuts created": "{0} skrivbordsgenväg skapades", "{0} failed": "{0} miss<PERSON><PERSON><PERSON>", "{0} has been installed successfully.": "{0} installerades", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} installerades. Rekommenderar omstart av UniGetUI för att slutföra installationen", "{0} has failed, that was a requirement for {1} to be run": "{0} <PERSON><PERSON><PERSON><PERSON>, den krävdes för att {1} <PERSON>e kunna köras", "{0} homepage": "{0} websida", "{0} hours": "{0} timmar", "{0} installation": "{0} installation", "{0} installation options": "{0} installationsalternativ", "{0} installer is being downloaded": "{0} <PERSON><PERSON><PERSON> ner", "{0} is being installed": "{0} installeras", "{0} is being uninstalled": "{0} avinstalleras", "{0} is being updated": "{0} är under uppdatering", "{0} is being updated to version {1}": "{0} uppdateras till version {1}", "{0} is disabled": "{0} <PERSON><PERSON> in<PERSON>", "{0} minutes": "{0} minuter", "{0} months": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} packages are being updated": "{0} paket uppdateras", "{0} packages can be updated": "{0} paket kan uppdateras", "{0} packages found": "{0} paket hittades", "{0} packages were found": "{0} paket hittades", "{0} packages were found, {1} of which match the specified filters.": "{0} paket hittades, varav {1} matchade filtret", "{0} selected": "{0} valda", "{0} settings": "{0} inställningar", "{0} status": "{0} status", "{0} succeeded": "{0} ly<PERSON><PERSON>", "{0} update": "{0} uppdatering", "{0} updates are available": "{0} uppdateringar är tillgängliga", "{0} was {1} successfully!": "{0} var {1} framgångsrik!", "{0} weeks": "{0} veckor", "{0} years": "{0} år", "{0} {1} failed": "{0} {1} misslyck<PERSON>", "{package} Installation": "{package} installation", "{package} Uninstall": "{package} avinstallera", "{package} Update": "{package} Uppdatera", "{package} could not be installed": "{package} kunde inte installeras", "{package} could not be uninstalled": "{package} kunde inte avinstalleras", "{package} could not be updated": "{package} kunde inte uppdateras", "{package} installation failed": "{package} installation misslyckad", "{package} installer could not be downloaded": "{package} installeraren kunde inte laddas ner", "{package} installer download": "{package} installerarens nedladdning", "{package} installer was downloaded successfully": "{package} installeraren laddades ner", "{package} uninstall failed": "{package} avinstallation misslyckad", "{package} update failed": "{package} uppdatering misslyckad", "{package} update failed. Click here for more details.": "{package} uppdatering misslyckades. Klicka här för mer information.", "{package} was installed successfully": "{package} installerades", "{package} was uninstalled successfully": "{package} avinstallerades", "{package} was updated successfully": "{package} uppdaterades", "{pcName} installed packages": "{pcName} installerade paket", "{pm} could not be found": "{pm} kunde inte hittas", "{pm} found: {state}": "{pm} hittade: {state}", "{pm} is disabled": "{pm} är inaktiverad", "{pm} is enabled and ready to go": "{pm} är tillgänglig och redo att användas", "{pm} package manager specific preferences": "{pm} pakethanterarens specifika inställningar", "{pm} preferences": "{pm} inställningar", "{pm} version:": "{pm} version:", "{pm} was not found!": "{pm} kunde inte hittas!"}