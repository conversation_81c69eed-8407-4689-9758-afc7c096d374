{"\"{0}\" is a local package and can't be shared": "\"{0}\" är ett lokalt paket och kan inte delas ut", "\"{0}\" is a local package and does not have available details": "\"{0}\" är ett lokalt paket och har inga detaljer tillgängliga", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" är ett lokalt paket och är inte kompatibelt med den här funktionen", "(Last checked: {0})": "(Kontrollerades senast: {0})", "(Number {0} in the queue)": "(Nummer {0} i kön)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@curudel, @kak<PERSON><PERSON>, @umeaboy", "0 packages found": "0 paket hittades", "0 updates found": "0 uppdateringar hittades", "1 - Errors": "1 - <PERSON><PERSON>", "1 day": "1 dag", "1 hour": "1 timme", "1 month": "1 månad", "1 package was found": "1 paket hittades", "1 update is available": "1 uppdatering är tillgänglig", "1 week": "1 vecka", "1 year": "1 år", "1. Navigate to the \"{0}\" or \"{1}\" page.": "Navi<PERSON><PERSON> till sidan \"{0}\" eller \"{1}\".", "2 - Warnings": "2 - var<PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": "3 - Information (mindre)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": "4 - information (mer)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": "5 - Information (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "En populär hanterare för C/C++-bibliotek. Fylld av C/C++-bibliotek och andra C/C++-relaterade verktyg<br>\nInnehåller: <b>C/C++-bibliotek och relaterade verktyg</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "En förvaringsplats fullt av verktyg och körbara filer som är designade med Microsofts .NET-ekosystem i åtanke.<br>Innehåller: <b>.NET-relaterade verktyg och skript</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "En förvaringsplats full av verktyg utformade med Microsofts .NET-ekosystem i åtanke.<br>Innehåller: <b>.NET-relaterade verktyg</b>", "A restart is required": "En omstart krävs", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "Om", "About Qt6": "Om Qt6", "About WingetUI": "Om UniGetUI", "About WingetUI version {0}": "Om WingetUI version {0}", "About the dev": "<PERSON>m utvecklaren", "Accept": "Acceptera", "Action when double-clicking packages, hide successful installations": "<PERSON><PERSON><PERSON><PERSON><PERSON> när du dubbelklickar på paket, d<PERSON><PERSON><PERSON> l<PERSON>er", "Add": "<PERSON><PERSON><PERSON> till", "Add a source to {0}": "<PERSON><PERSON><PERSON> till en källa till {0}", "Add a timestamp to the backup file names": "Lägg till en tidsstämpel till säkerhetskopiornas filnamn", "Add a timestamp to the backup files": "Lägg till en tidsstämpel till säkerhetskopiorna", "Add packages or open an existing bundle": "Lägg till paket eller öppna ett befintligt paket", "Add packages or open an existing package bundle": null, "Add packages to bundle": null, "Add packages to start": "Lägg till paket för att börja", "Add selection to bundle": "Lägg till urval i paketet", "Add source": "Lägg till källa", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": "<PERSON><PERSON><PERSON> till källa {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON> till kä<PERSON> {source} till {manager}", "Addition succeeded": "Tillägget lyckades", "Administrator privileges": "Administratörsrättigheter", "Administrator privileges preferences": "Inställningar för administratörsbehörighet", "Administrator rights": "Administratörsrättigheter", "Administrator rights and other dangerous settings": null, "Advanced options": "Avancerade alternativ", "All files": "Alla filer", "All versions": "Alla versioner", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": null, "Allow parallel installs (NOT RECOMMENDED)": "<PERSON><PERSON><PERSON> parallella installationer (REKOMMENDERAS INTE)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "<PERSON><PERSON>t att {pm} operationer utförs parallellt", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativt så kan du även installera {0} genom att köra följande kommando i en Windows PowerShell-prompt:", "Always elevate {pm} installations by default": "<PERSON><PERSON><PERSON>t att {pm} operationer utförs parallellt", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON> alltid {pm} åtgärder med administratörsrättigheter", "An error occurred": "Ett fel har inträffat", "An error occurred when adding the source: ": "Ett fel uppstod när källan lades till:", "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": "Ett fel uppstod vid sökning efter uppdateringar:", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "<PERSON>tt fel uppstod när det här paketet bearbetades", "An error occurred:": "Ett fel inträffade:", "An interal error occurred. Please view the log for further details.": "Ett internt fel uppstod. Se loggen för mer information.", "An unexpected error occurred:": "Ett oväntat fel uppstod:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": "En uppdatering hittades!", "Android Subsystem": "Android subsystem", "Another source": "<PERSON> annan källa", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": "Alla ändringar som inte har sparats kommer att förloras", "App Name": "App-namn", "Appearance": "Utseende", "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "Applikationstema:", "Apply": null, "Architecture to install:": "Arkitektur att installera:", "Are these screenshots wron or blurry?": "Är dessa skärmdumpar felaktiga eller suddiga?", "Are you really sure you want to enable this feature?": "Är du säker på att du vill aktivera den här funktionen?", "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": "Är du säker på att du vill ta bort alla genvägar?", "Are you sure?": "<PERSON>r du säker?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "Be om administratörsbehörigheter en gång för varje sats av operationer", "Ask for administrator rights when required": "Be om administratörsrättigheter vid behov", "Ask once or always for administrator rights, elevate installations by default": "Fråga en gång eller alltid efter administratörsrättigheter, höj installationer som standard", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Fråga endast en gång om administratörsbehörigheter (rekommenderas inte)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Fråga om att ta bort skrivbordsgenvägar som har skapats under en installation eller uppgradering.", "Attention required": "Uppmärksamhet krävs", "Authenticate to the proxy with an user and a password": "Autentisera till proxy'n med ett användarnamn och ett lösenord", "Author": "<PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": null, "Automatically save a list of all your installed packages to easily restore them.": "Spara automatiskt en lista över alla dina installerade paket för att enkelt återställa dem.", "Automatically save a list of your installed packages on your computer.": "Spara automatiskt en lista över dina installerade paket på din dator.", "Autostart WingetUI in the notifications area": "Starta WingetUI automatiskt i meddelandefältet", "Available Updates": "Tillgängliga uppdateringar", "Available updates: {0}": "Tillgängliga uppdateringar: {0}", "Available updates: {0}, not finished yet...": "Tillgängliga uppdateringar: {0}, inte klar än...", "Backing up packages to GitHub Gist...": null, "Backup": "Säkerhetskopiera", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Säkerhetskopiera installerade paket", "Backup location": null, "Become a contributor": "Bli en bidragare", "Become a translator": "Bli en översättare", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Betafunktioner och andra alternativ som inte bör röras", "Both": "Båda", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Men här är andra saker du kan göra för att lära dig ännu mer om WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": null, "Cache administrator rights and elevate installers by default": "Cachelagra administratörsrättigheter och höj installationsprogram som standard", "Cache administrator rights, but elevate installers only when required": "Cachelagra administra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, men höj installationsprogram endast när det behövs", "Cache was reset successfully!": "<PERSON><PERSON>lldes framgångsrikt!", "Can't {0} {1}": "Kan inte {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": "Avbryt alla åtgärder", "Change backup output directory": "<PERSON>ndra katal<PERSON> för säkerhetskopiering", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": null, "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": "<PERSON><PERSON>", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "<PERSON><PERSON><PERSON> efter paketuppdateringar med jämna mellanrum", "Check for updates": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>", "Check for updates every:": "<PERSON><PERSON><PERSON> efter uppdateringar varje:", "Check for updates periodically": "<PERSON><PERSON><PERSON> efter uppdateringar med jämna mellanrum", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON><PERSON><PERSON> efter uppdateringar regelbundet och fråga mig vad jag ska göra när uppdateringar hittas.", "Check for updates regularly, and automatically install available ones.": "Sök efter uppdateringar regelbundet och installera tillgängliga automatiskt.", "Check out my {0} and my {1}!": "<PERSON><PERSON> in min {0} och min {1}!", "Check out some WingetUI overviews": "<PERSON><PERSON> in några översikter av WingetUI", "Checking for other running instances...": "<PERSON><PERSON><PERSON> efter andra instanser som körs...", "Checking for updates...": "<PERSON><PERSON><PERSON> efter uppdateringar...", "Checking found instace(s)...": "Kontrollerar hittade instans(er)...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": "Rensa cache", "Clear finished operations": null, "Clear selection": "Rensa val", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": "<PERSON><PERSON>-cache - WingetUI", "Clearing Scoop cache...": "<PERSON><PERSON>-cache...", "Click here for more details": "<PERSON><PERSON><PERSON> här för fler <PERSON>", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": null, "Close": "Stäng", "Close UniGetUI to the system tray": "Stäng ner UniGetUI till systemfältet", "Close WingetUI to the notification area": "Stäng WingetUI till meddelandefältet", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "Kommandoradsutgång", "Command-line to run:": "Kommando att köra:", "Compare query against": "Jämför sökning mot", "Compatible with authentication": "Kompatibel med autentisering", "Compatible with proxy": "Kompatibel med proxy", "Component Information": "Komponent information", "Concurrency and execution": null, "Connect the internet using a custom proxy": "Anslut internet genom att använda en anpassad proxy", "Continue": "Fortsätt", "Contribute to the icon and screenshot repository": "Bidra till ikonen och skärmbildsförrådet", "Contributors": "B<PERSON><PERSON><PERSON>givar<PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopiera till urklipp", "Could not add source": "Kunde inte lägga till källa", "Could not add source {source} to {manager}": "Det gick inte att lägga till källan {source} till {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": "Det gick inte att läsa in meddelanden -", "Could not load announcements - HTTP status code is $CODE": "Det gick inte att läsa in meddelanden - HTTP-statuskoden är $CODE", "Could not remove source": "Kunde inte ta bort källa", "Could not remove source {source} from {manager}": "Det gick inte att ta bort källan {source} f<PERSON><PERSON><PERSON> {manager}", "Could not remove {source} from {manager}": null, "Credentials": "Inloggningsuppgifter", "Current Version": "Aktuell version", "Current status: Not logged in": null, "Current user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom arguments:": "Anpassade argument:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Anpassade kommandoradsargument:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Anpassa WingetUI - endast för hackare och avancerade användare", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ANSVARSFRISKRIVNING: VI ÄR INTE ANSVARIGA FÖR DE NEDLADDADE PAKETET. SE TILL ATT INSTALLERA ENDAST BETROD MJUKVARA.", "Dark": "M<PERSON><PERSON>", "Decline": "Avböj", "Default": "Standard", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Standardinställningar - lämplig för vanliga användare", "Default vcpkg triplet": null, "Delete?": "Ta bort?", "Dependencies:": null, "Descendant": null, "Description:": "Beskrivning:", "Desktop shortcut created": "Skrivbordsgenväg skapad", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Det är svårt att utveckla och den här applikationen är gratis. Men om du gillade applikationen kan du alltid <b>köpa en kaffe till mig</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installera direkt när du dubbelklickar på ett objekt i fliken \"{discoveryTab}\" (istället för att visa paketinformationen)", "Disable new share API (port 7058)": "Inaktivera nytt delnings-API (port 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": "Ansvarsfriskrivning", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON> paket", "Discover packages": "<PERSON><PERSON><PERSON><PERSON> paket", "Distinguish between\nuppercase and lowercase": "<PERSON><PERSON><PERSON> mellan versaler och gemener", "Distinguish between uppercase and lowercase": "<PERSON><PERSON><PERSON> mellan stora och små bokstäver", "Do NOT check for updates": "Sök INTE efter uppdateringar", "Do an interactive install for the selected packages": "Gör en interaktiv installation för de valda paketen", "Do an interactive uninstall for the selected packages": "Gör en interaktiv avinstallation för de valda paketen", "Do an interactive update for the selected packages": "Gör en interaktiv uppdatering för de valda paketen", "Do not automatically install updates when the battery saver is on": "Installera inte uppdateringar automatiskt när strömsparningen är på", "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Ladda inte ner nya appöversättningar från GitHub automatiskt", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": "Ta inte bort framgångsrika åtgärder från listan automatiskt", "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "Uppdatera inte paketindex vid start", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Tycker du att WingetUI är användbart? Om du kan, kanske du vill stödja mitt arbete, så att jag kan fortsätta att göra WingetUI till det ultimata pakethanteringsgränssnittet.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Tycker du att WingetUI är användbart? Vill du stödja utvecklaren? I så fall kan du {0}, det hjälper mycket!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": "Vill du verkligen avinstallera följande {0}-paket?", "Do you really want to uninstall {0} packages?": "Vill du verkligen avinstallera {0} paket?", "Do you really want to uninstall {0}?": "Vill du verkligen avinstallera {0}?", "Do you want to restart your computer now?": "Vill du starta om din dator nu?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Vill du översätta WingetUI till ditt språk? Se hur du bidrar <a style=\"color:{0}\" href=\"{1}\"a>HÄR!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Känner du inte för att donera? Oroa dig inte, du kan alltid dela WingetUI med dina vänner. Sprid ordet om WingetUI.", "Donate": "<PERSON><PERSON>", "Done!": null, "Download failed": "<PERSON><PERSON><PERSON><PERSON> misslyckad", "Download installer": "Hämta installationsprogrammet", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Nedladdningen lyckades", "Download updated language files from GitHub automatically": "<PERSON>dda ner upp<PERSON>rade språkfiler från GitHub automatiskt", "Downloading": "<PERSON><PERSON><PERSON> ner", "Downloading backup...": null, "Downloading installer for {package}": "Hämtar installationsfil för {package}", "Downloading package metadata...": "Laddar ned paketets metadata...", "Enable Scoop cleanup on launch": "Aktivera <PERSON>-rensning vid start", "Enable WingetUI notifications": "Aktivera WingetUI-aviseringar", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktivera bakgrunds-API (WingetUI Widgets and Sharing, port 7058)", "Enable it to install packages from {pm}.": "Aktivera den för att installera paket från {pm}.", "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Aktivera {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "<PERSON><PERSON>", "Everything is up to date": "Allt är uppdaterat", "Exact match": "Exakt matchning", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": "Expandera version", "Experimental settings and developer options": "Experimentella inställningar och utvecklaralternativ", "Export": "Exportera", "Export log as a file": "Exportera logg som en fil", "Export packages": "Exportera paket", "Export selected packages to a file": "Exportera valda paket till en fil", "Export settings to a local file": "Exportera inställningar till en lokal fil", "Export to a file": "Exportera till en fil", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "<PERSON><PERSON>m<PERSON> senaste medd<PERSON>, vänligen vänta ...", "Filters": "Filter", "Finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Follow system color scheme": "Följ systemets färgschema", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Tvinga ARM-kompilerad winget-version (ENDAST FÖR ARM64-SYSTEM)", "Formerly known as WingetUI": null, "Found": "Hittat", "Found packages: ": "Hittat paket:", "Found packages: {0}": "Hittade paket: {0}", "Found packages: {0}, not finished yet...": "Hittade paket: {0}, inte klara ännu...", "General preferences": "Allmänna inställningar", "GitHub profile": "GitHub-profil", "Global": "Global", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Stort lager av okända men användbara verktyg och andra in<PERSON> paket.<br>Innehåller: <b>Verktyg, kommandoradsprogram, allm<PERSON><PERSON> (extra hink krävs)</b>", "Great! You are on the latest version.": "Grattis! Du använder senaste versionen.", "Grid": null, "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help and documentation": "Hjälp och dokumentation", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, jag heter Martí och jag är <i>utvecklare</i> av WingetUI. WingetUI har gjorts helt på min fritid!", "Hide details": "<PERSON><PERSON><PERSON><PERSON>", "Homepage": "<PERSON><PERSON><PERSON>", "Hooray! No updates were found.": "Hurra! Inga uppdateringar hittades.", "How should installations that require administrator privileges be treated?": "<PERSON>r ska installationer som kräver administratörsrättigh<PERSON> behandlas?", "How to add packages to a bundle": null, "I understand": "Jag förstår", "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Ignorera framtida upp<PERSON>ringar för de<PERSON> paket", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "Ignorera valda paket", "Ignore special characters": "Ignorera specialtecken", "Ignore updates for the selected packages": "Ignorera upp<PERSON><PERSON><PERSON> för de valda paketen", "Ignore updates for this package": "Ignorera upp<PERSON><PERSON><PERSON> för <PERSON> paket", "Ignored updates": "Ignorerade uppdateringar", "Ignored version": "Ignorerad version", "Import": "Importera", "Import packages": "Importera paket", "Import packages from a file": "Importera paket från en fil", "Import settings from a local file": "Importera inställningar från en lokal fil", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "Initierar WingetUI...", "Install": "Installera", "Install Scoop": "In<PERSON><PERSON><PERSON>", "Install and more": null, "Install and update preferences": null, "Install as administrator": "Installera som administratör", "Install available updates automatically": null, "Install location can't be changed for {0} packages": null, "Install location:": "Installationsplats:", "Install options": null, "Install packages from a file": "Installera paket från en fil", "Install prerelease versions of UniGetUI": null, "Install selected packages": "Installera valda paket", "Install selected packages with administrator privileges": "Installera valda paket med administratörsbehörighet", "Install selection": "Installera val", "Install the latest prerelease version": "Installera den senaste förhandsversionen", "Install updates automatically": "Installera uppdateringar automatiskt", "Install {0}": "Installera {0}", "Installation canceled by the user!": "Installationen avbröts av användaren!", "Installation failed": "Instal<PERSON><PERSON> misslyckades", "Installation options": "Installationsalternativ", "Installation scope:": "Installationsomfång:", "Installation succeeded": "Installationen lyckades", "Installed Packages": "Installerade paket", "Installed Version": "Installerad version", "Installed packages": null, "Installer SHA256": null, "Installer SHA512": null, "Installer Type": "Installations typ", "Installer URL": "Installations URL", "Installer not available": null, "Instance {0} responded, quitting...": "<PERSON><PERSON><PERSON>omsten {0} svar<PERSON>, avslutar...", "Instant search": "<PERSON><PERSON><PERSON><PERSON>", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "Interaktiv installation", "Interactive operation": null, "Interactive uninstall": "Interaktiv avinstallation", "Interactive update": "Interaktiv uppdatering", "Internet connection settings": null, "Is this package missing the icon?": "<PERSON>knar detta paket ikonen?", "Is your language missing or incomplete?": "<PERSON>knas ditt språk eller är det ofullständigt?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Det ser ut som att du körde WingetUI som administratör, vilket inte rekommenderas. Du kan fortfarande använda programmet, men vi rekommenderar starkt att du inte kör WingetUI med administratörsbehörighet. Klicka på \"{showDetails}\" för att se varför.", "Language": null, "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tema och andra <PERSON>a preferenser", "Last updated:": "Senast uppdaterad:", "Latest": "Senaste", "Latest Version": "Senaste versionen", "Latest Version:": "Senaste versionen:", "Latest details...": "Senaste <PERSON>...", "Launching subprocess...": "Startar underprocess...", "Leave empty for default": "Lämna tomt som standard", "License": "Licens", "Licenses": "Licenser", "Light": "<PERSON><PERSON><PERSON>", "List": null, "Live command-line output": "Live kommandoradsutgång", "Live output": "Live-utgång", "Loading UI components...": "Laddar UI-komponenter ...", "Loading WingetUI...": "Laddar WingetUI...", "Loading packages": "<PERSON>dd<PERSON> paket", "Loading packages, please wait...": "<PERSON><PERSON><PERSON> paket, vänligen vänta ...", "Loading...": "Laddar...", "Local": "<PERSON><PERSON>", "Local PC": "Lokal PC", "Local backup advanced options": null, "Local machine": "Lokal maskin", "Local package backup": null, "Locating {pm}...": "Letar upp {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": null, "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Letar efter paket...", "Machine | Global": "Maskin | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "Hantera", "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "Hantera WingetUIs autostartbeteende från Inställningar-appen", "Manage ignored packages": "Hantera ignorerade paket", "Manage ignored updates": "Hantera ignorerade uppdateringar", "Manage shortcuts": "Hantera genvägar", "Manage telemetry settings": "Hantera telemetri-inställningar", "Manage {0} sources": "Hantera {0} k<PERSON><PERSON><PERSON>", "Manifest": "Manifest", "Manifests": "Manifester", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsofts officiella pakethanterare. Full av välkända och verifierade paket<br>Innehåller: <b><PERSON><PERSON><PERSON><PERSON> <PERSON>var<PERSON>, Microsoft Store-appar</b>", "Missing dependency": null, "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON>", "More details about the shared data and how it will be processed": null, "More info": "Mer info", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "<PERSON><PERSON>", "New": null, "New Version": " Ny version", "New bundle": "<PERSON>y bunt", "New version": "Ny version", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "Inga packet hittades", "No packages found matching the input criteria": "Inga paket hittades som matchar inmatningskriterierna", "No packages have been added yet": "Inga paket har lagts till ännu", "No packages selected": "<PERSON>ga paket har valts", "No packages were found": "Inga paket hittades", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": "Inga resultat hittades som matchade inmatningskriterierna", "No sources found": "Inga källor hittades", "No sources were found": "Inga källor hittades", "No updates are available": "Inga uppdateringar är tillgängliga", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS:s pakethanterare. Fullt av bibliotek och andra verktyg som kretsar runt Javascript-världen<br>Innehåller: <b>Node javascript-bibliotek och andra relaterade verktyg</b>", "Not available": "Inte tillgänglig", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Hittades inte", "Not right now": null, "Notes:": "Anmärkningar:", "Notification preferences": null, "Notification tray options": "Alternativ för a<PERSON>", "Notification types": null, "NuPkg (zipped manifest)": "NuPkg (zippad manifest)", "OK": "OK", "Ok": "Ok", "Open": "Öppna", "Open GitHub": "Öppna GitHub", "Open UniGetUI": "Öppna UniGetUI", "Open UniGetUI security settings": null, "Open WingetUI": "Öppna WingetUI", "Open backup location": "Öppna plats för säkerhetskopiering", "Open existing bundle": "<PERSON><PERSON><PERSON> befintligt paket", "Open install location": null, "Open the welcome wizard": "Öppna välkomstguiden", "Operation canceled by user": null, "Operation cancelled": "Åtgärden avbröts", "Operation history": "Åtgärdshistorik", "Operation in progress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Operation on queue (position {0})...": "<PERSON><PERSON><PERSON><PERSON><PERSON> på kö (position {0})...", "Operation profile:": null, "Options saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Order by:": null, "Other": "<PERSON><PERSON><PERSON><PERSON>", "Other settings": null, "Package": null, "Package Bundles": "Paketbuntar", "Package ID": "Paket ID", "Package Manager": "Pakethanterare", "Package Manager logs": "Pakethanteraren loggar", "Package Managers": "Pakethanterare", "Package Name": "Paketnamn", "Package backup": null, "Package backup settings": null, "Package bundle": "Paketbunt", "Package details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "Pakethanterarens preferenser", "Package managers": null, "Package not found": "Paketet hittades inte", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": "<PERSON><PERSON> {name} f<PERSON><PERSON><PERSON> {manager}", "Package's default": null, "Packages": "<PERSON><PERSON>", "Packages found: {0}": "Hittade paket: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "Klistra in en giltig URL i databasen", "Pause updates for": null, "Perform a backup now": "G<PERSON>r en säkerhetskopia nu", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "Säkerhetskopiering utförs, vänligen vänta...", "Periodically perform a backup of the installed packages": "Utför regelbundet säkerhetskopiering av de installerade paketen", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": null, "Please enter at least 3 characters": "Vänligen ange minst 3 tecken", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Observera att vissa paket kanske inte går att installera på grund av de pakethanterare som är aktiverade på den här maskinen.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Observera att paket från vissa källor kanske inte går att exportera. De är utgråade och kommer inte att exporteras.", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Se kommandoradens utdata eller se Operation History för mer information om problemet.", "Please select how you want to configure WingetUI": "Var vänlig välj hur du vill konfigurera WingetUI", "Please try again later": "<PERSON><PERSON>rs<PERSON>k igen senare", "Please type at least two characters": "Vänligen skriv minst två tecken", "Please wait": "Vänligen vänta", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "Vänligen vänta...", "Portable": "Portabel", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShells pakethanterare. Hitta bibliotek och skript för att utöka PowerShell-funktionerna<br>Innehåller: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON> pake<PERSON>, vänligen vänta ...", "Proceed at your own risk.": "Fortsätt på egen risk", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "Utgivningsdatum", "Publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Pythons pakethanterare. Fullt med pythonbibliotek och andra pythonrelaterade verktyg<br><PERSON><PERSON><PERSON><PERSON>: <b>Pythonbibliotek och relaterade verktyg</b>", "Quit": "Stäng", "Quit WingetUI": "Stäng WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "Ominstallera", "Reinstall package": "Installera om paketet", "Related settings": null, "Release notes": "Versionsinformation", "Release notes URL": " Versionsinformation URL", "Release notes URL:": " Versionsinformation URL:", "Release notes:": "Versionsinformation:", "Reload": "Ladda om", "Reload log": "Ladda om loggen", "Removal failed": "<PERSON>rt<PERSON><PERSON> misslyckades", "Removal succeeded": "Borttagning lyckades", "Remove from list": "<PERSON> bort från listan", "Remove permanent data": "Ta bort permanent data", "Remove selection from bundle": "Ta bort urval från paketet", "Remove successful installs/uninstalls/updates from the installation list": "Ta bort lyckade installationer/avinstallationer/uppdateringar från installationslistan", "Removing source {source}": null, "Removing source {source} from {manager}": "<PERSON> bort källa {source} f<PERSON><PERSON><PERSON> {manager} ", "Repair UniGetUI": null, "Repair WinGet": "Reparera WinGet", "Report an issue or submit a feature request": null, "Repository": null, "Reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reset Scoop's global app cache": null, "Reset UniGetUI": "Återställ UniGetUI", "Reset WinGet": "Återställ WinGet", "Reset Winget sources (might help if no packages are listed)": null, "Reset WingetUI": "Återställ UniGetUI", "Reset WingetUI and its preferences": null, "Reset WingetUI icon and screenshot cache": null, "Reset list": "Återställ lista", "Resetting Winget sources - WingetUI": null, "Restart": "Starta om", "Restart UniGetUI": "Starta om UniGetUI", "Restart WingetUI": "Starta om UniGetUI", "Restart WingetUI to fully apply changes": null, "Restart later": "Starta om senare", "Restart now": "Starta om nu", "Restart required": "Omstart krävs", "Restart your PC to finish installation": "Starta om din PC för att slutföra installationen", "Restart your computer to finish the installation": "Starta om din dator för att avsluta installationen", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Försök igen", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": null, "Return to top": null, "Run": "<PERSON><PERSON><PERSON>", "Run as admin": "<PERSON><PERSON><PERSON> som admin", "Run cleanup and clear cache": null, "Run last": null, "Run next": null, "Run now": null, "Running the installer...": null, "Running the uninstaller...": null, "Running the updater...": null, "Save": null, "Save File": "Spara fil", "Save and close": "Spara och stäng", "Save as": null, "Save bundle as": null, "Save now": "Spara nu", "Saving packages, please wait...": null, "Scoop Installer - WingetUI": null, "Scoop Uninstaller - WingetUI": null, "Scoop package": null, "Search": "<PERSON>ö<PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": null, "Search for packages": "<PERSON><PERSON><PERSON> efter paket", "Search for packages to start": null, "Search mode": null, "Search on available updates": null, "Search on your software": null, "Searching for installed packages...": null, "Searching for packages...": "Letar efter paket...", "Searching for updates...": "Letar efter uppdateringar...", "Select": "<PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": null, "Select a folder": "<PERSON><PERSON><PERSON><PERSON> mapp", "Select all": null, "Select all packages": "<PERSON><PERSON><PERSON><PERSON> alla paket", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON>a <b>om du vet vad du gör</b>.", "Select package file": "<PERSON><PERSON><PERSON><PERSON>", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "<PERSON><PERSON><PERSON><PERSON> källan du vill lägga till:", "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": null, "Sent handshake. Waiting for instance listener's answer... ({0}%)": null, "Set a custom backup file name": null, "Set custom backup file name": null, "Settings": "Inställningar", "Share": "Dela", "Share WingetUI": "Dela UniGetUI", "Share anonymous usage data": "Dela anonym användardata", "Share this package": "<PERSON>a detta paket", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": null, "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "Visa UniGetUI", "Show a notification when an installation fails": "Visa notifikation om en installation misslyckas", "Show a notification when an installation finishes successfully": "Visa notifikation när en installation lyckas", "Show a notification when an operation fails": null, "Show a notification when an operation finishes successfully": null, "Show a notification when there are available updates": null, "Show a silent notification when an operation is running": null, "Show details": "Visa detaljer", "Show in explorer": "Visa i utforskaren", "Show info about the package on the Updates tab": null, "Show missing translation strings": null, "Show notifications on different events": null, "Show package details": "Visa paket-detaljer", "Show package icons on package lists": null, "Show similar packages": "Visa liknande paket", "Show the live output": null, "Size": "Storlek", "Skip": null, "Skip hash check": null, "Skip hash checks": null, "Skip integrity checks": null, "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": null, "Skip the hash check when updating the selected packages": null, "Skip this version": null, "Software Updates": "Programuppdateringar", "Something went wrong": "<PERSON><PERSON><PERSON> gick fel", "Something went wrong while launching the updater.": null, "Source": "<PERSON><PERSON><PERSON>", "Source URL:": null, "Source added successfully": null, "Source addition failed": null, "Source name:": null, "Source removal failed": null, "Source removed successfully": null, "Source:": "Källa:", "Sources": "<PERSON><PERSON><PERSON><PERSON>", "Start": "Start", "Starting daemons...": null, "Starting operation...": null, "Startup options": null, "Status": "Status", "Stuck here? Skip initialization": null, "Suport the developer": "<PERSON><PERSON><PERSON>", "Support me": "<PERSON><PERSON><PERSON> mig", "Support the developer": "<PERSON><PERSON><PERSON>", "Systems are now ready to go!": null, "Telemetry": null, "Text": "Text", "Text file": "Textfil", "Thank you ❤": "Tack ❤", "Thank you 😉": "Tack 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": null, "The backup will be performed after login.": null, "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": null, "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": null, "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": null, "The following packages are going to be installed on your system.": null, "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": null, "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": null, "The icons and screenshots are maintained by users like you!": null, "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": null, "The installer hash does not match the expected value.": null, "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": null, "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": null, "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": null, "The selected packages have been blacklisted": null, "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": null, "The source {source} was added to {manager} successfully": null, "The source {source} was removed from {manager} successfully": null, "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": null, "The update will not continue.": null, "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": null, "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": null, "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": null, "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": null, "They are the programs in charge of installing, updating and removing packages.": null, "Third-party licenses": "Tredjepartslicenser", "This could represent a <b>security risk</b>.": null, "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": null, "This is the <b>default choice</b>.": null, "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": null, "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": null, "This package can be updated to version {0}": null, "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": null, "This package is already installed": null, "This package is being processed": null, "This package is not available": null, "This package is on the queue": null, "This process is running with administrator privileges": null, "This project has no connection with the official {0} project — it's completely unofficial.": null, "This setting is disabled": null, "This wizard will help you configure and customize WingetUI!": null, "Toggle search filters pane": null, "Translators": "Översättare", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": null, "Unable to find package": null, "Unable to load informarion": null, "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": "UniGetUI uppdateras...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": null, "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "Avin<PERSON>lera", "Uninstall Scoop (and its packages)": null, "Uninstall and more": null, "Uninstall and remove data": "Avinstallera och ta bort data", "Uninstall as administrator": "Avinstallera som administratör", "Uninstall canceled by the user!": null, "Uninstall failed": "Avinstallation misslyckad", "Uninstall options": null, "Uninstall package": "<PERSON><PERSON><PERSON><PERSON><PERSON> paket", "Uninstall package, then reinstall it": null, "Uninstall package, then update it": null, "Uninstall previous versions when updated": null, "Uninstall selected packages": "Avinstallera valda paket", "Uninstall selection": null, "Uninstall succeeded": "Avinstallation lyckad", "Uninstall the selected packages with administrator privileges": null, "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": null, "Unknown": "Okä<PERSON>", "Unknown size": "<PERSON>änd storlek", "Unset or unknown": null, "Up to date": null, "Update": "Uppdatera", "Update WingetUI automatically": null, "Update all": "Uppdatera alla", "Update and more": null, "Update as administrator": "Uppdatera som administratör", "Update check frequency, automatically install updates, etc.": null, "Update date": null, "Update failed": "Upp<PERSON><PERSON> misslyck<PERSON>", "Update found!": "Uppdatering hittad!", "Update now": "Uppdatera nu", "Update options": null, "Update package indexes on launch": null, "Update packages automatically": "Uppdatera paket automatiskt", "Update selected packages": "Upp<PERSON><PERSON> valda paket", "Update selected packages with administrator privileges": null, "Update selection": null, "Update succeeded": "Upp<PERSON><PERSON> lyckad", "Update to version {0}": null, "Update to {0} available": null, "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "Uppdateringar", "Updates available!": "Uppdateringar tillgängliga!", "Updates for this package are ignored": null, "Updates found!": "Uppdateringar hittade!", "Updates preferences": null, "Updating WingetUI": "Uppdaterar UniGetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": null, "Use a custom icon and screenshot database URL": null, "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": null, "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": null, "Use system Winget (Needs a restart)": null, "Use system Winget (System language must be set to english)": null, "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": "Användbara länkar", "User": "Användare", "User interface preferences": null, "User | Local": null, "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": null, "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": "Version", "Version to install:": null, "Version:": null, "View GitHub Profile": "Visa GitHub-profil", "View WingetUI on GitHub": "Visa UniGetUI på GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": null, "View mode:": null, "View on UniGetUI": null, "View page on browser": null, "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": null, "Waiting for {0} to complete...": null, "Warning": "Varning", "Warning!": "Varning!", "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": null, "We could not load detailed information about this package, because it was not installed from an available package manager.": null, "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": null, "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": null, "We couldn't find any package": null, "Welcome to WingetUI": "Välkommen till UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": null, "Which source do you want to add?": null, "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": null, "WinGet could not be repaired": "WinGet kunde inte repareras", "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": null, "WingetUI - {0} updates are available": null, "WingetUI - {0} {1}": null, "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": "UniGetUI-licens", "WingetUI Log": null, "WingetUI Repository": null, "WingetUI Settings": null, "WingetUI Settings File": null, "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": null, "WingetUI autostart behaviour, application launch settings": null, "WingetUI can check if your software has available updates, and install them automatically if you want to": null, "WingetUI display language:": null, "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": null, "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": null, "WingetUI has not been machine translated. The following users have been in charge of the translations:": null, "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": null, "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": null, "WingetUI log": null, "WingetUI tray application preferences": null, "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": null, "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": null, "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": null, "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": null, "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": null, "WingetUI {0} is ready to be installed.": null, "Write here the process names here, separated by commas (,)": null, "Yes": null, "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": null, "You have installed WingetUI Version {0}": null, "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": null, "You will be prompted only once, and administrator rights will be granted to packages that request them.": null, "You will be prompted only once, and every future installation will be elevated automatically.": null, "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": null, "extracted": null, "feature": null, "formerly WingetUI": null, "homepage": "hemsida", "install": "installera", "installation": "installation", "installed": "installerad", "installing": "installeras", "library": "bibliotek", "mandatory": null, "option": null, "optional": null, "uninstall": "avinstallera", "uninstallation": null, "uninstalled": null, "uninstalling": null, "update(noun)": null, "update(verb)": null, "updated": "uppdaterad", "updating": null, "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": null, "{0} aborted": null, "{0} can be updated": null, "{0} can be updated to version {1}": null, "{0} days": null, "{0} desktop shortcuts created": null, "{0} failed": "{0} miss<PERSON><PERSON><PERSON>", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} timmar", "{0} installation": null, "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": null, "{0} is being updated to version {1}": null, "{0} is disabled": null, "{0} minutes": null, "{0} months": null, "{0} packages are being updated": null, "{0} packages can be updated": null, "{0} packages found": null, "{0} packages were found": null, "{0} packages were found, {1} of which match the specified filters.": null, "{0} settings": null, "{0} status": null, "{0} succeeded": null, "{0} update": null, "{0} updates are available": null, "{0} was {1} successfully!": null, "{0} weeks": "{0} veckor", "{0} years": "{0} år", "{0} {1} failed": null, "{package} Installation": null, "{package} Uninstall": null, "{package} Update": "{package} Uppdatera", "{package} could not be installed": "{package} kunde inte installeras", "{package} could not be uninstalled": "{package} kunde inte avinstalleras", "{package} could not be updated": "{package} kunde inte uppdateras", "{package} installation failed": "{package} installation misslyckad", "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": "{package} avinstallation misslyckad", "{package} update failed": "{package} uppdatering misslyckad", "{package} update failed. Click here for more details.": null, "{package} was installed successfully": null, "{package} was uninstalled successfully": null, "{package} was updated successfully": null, "{pcName} installed packages": null, "{pm} could not be found": null, "{pm} found: {state}": null, "{pm} is disabled": null, "{pm} is enabled and ready to go": null, "{pm} package manager specific preferences": null, "{pm} preferences": null, "{pm} version:": null, "{pm} was not found!": "{pm} kunde inte hittas!"}