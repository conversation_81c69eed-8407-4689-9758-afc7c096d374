<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Interface.Dialogs.HelpPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:animations="using:CommunityToolkit.WinUI.Animations"
    xmlns:controls="using:Microsoft.UI.Xaml.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    HorizontalAlignment="Stretch"
    VerticalAlignment="Stretch"
    NavigationCacheMode="Required"
    mc:Ignorable="d">

    <animations:Implicit.ShowAnimations>
        <animations:TranslationAnimation
            From="0,100,0"
            To="0"
            Duration="0:0:0.25" />
        <animations:OpacityAnimation
            From="0"
            To="1"
            Duration="0:0:0.25" />
    </animations:Implicit.ShowAnimations>

    <Grid
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        RowSpacing="4">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="35" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid ColumnSpacing="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Button
                Name="BackButton"
                Grid.Column="0"
                Width="35"
                Height="35"
                Padding="5"
                Click="BackButton_Click"
                CornerRadius="4,0,0,4">
                <FontIcon FontSize="20" Glyph="&#xE76B;" />
            </Button>
            <Button
                Name="RightButton"
                Grid.Column="1"
                Width="35"
                Height="35"
                Padding="5"
                Click="RightButton_Click"
                CornerRadius="0">
                <FontIcon FontSize="20" Glyph="&#xE76C;" />
            </Button>
            <Button
                Name="HomeButton"
                Grid.Column="2"
                Width="35"
                Height="35"
                Padding="5"
                Click="HomeButton_Click"
                CornerRadius="0">
                <FontIcon FontSize="16" Glyph="&#xE80F;" />
            </Button>
            <Button
                Name="ReloadButton"
                Grid.Column="3"
                Width="35"
                Height="35"
                Padding="5"
                Click="ReloadButton_Click"
                CornerRadius="0,4,4,0">
                <FontIcon FontSize="16" Glyph="&#xE72C;" />
            </Button>
            <Button
                Name="BrowserButton"
                Grid.Column="5"
                Height="35"
                Click="BrowserButton_Click">
                <widgets:TranslatedTextBlock Text="View page on browser" />
            </Button>
        </Grid>
        <Border
            Grid.Row="1"
            Grid.Column="0"
            x:Name="WebViewBorder"
            Background="{ThemeResource ApplicationPageBackgroundThemeBrush}"
            CornerRadius="6">
            <!--controls:WebView2 Name="WebView" Source="https://www.google.com/" /-->
        </Border>
        <ProgressBar
            Name="ProgressBar"
            Grid.Row="1"
            Grid.Column="0"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Top"
            CornerRadius="4,4,0,0"
            IsIndeterminate="True" />
    </Grid>
</Page>
