{"\"{0}\" is a local package and can't be shared": "„{0}“ ist ein lokales Paket und kann nicht geteilt werden", "\"{0}\" is a local package and does not have available details": "„{0}“ ist ein lokales Paket und hat keine verfügbaren Details", "\"{0}\" is a local package and is not compatible with this feature": "„{0}“ ist ein lokales Paket und ist mit dieser Funktion nicht kompatibel", "(Last checked: {0})": "(Letzte Überprüfung: {0})", "(Number {0} in the queue)": "(Position {0} in der Warteschlange)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@Datacra5H, @ebnater, @<PERSON><PERSON><PERSON><PERSON>, @mi<PERSON><PERSON><PERSON><PERSON><PERSON>, @CanePlayz, @1270o1, @yrjarv, @alxhu-dev, @Araxxas, @martinwilco, @t<PERSON><PERSON><PERSON>er, @TheScarfix, @VfBFan", "0 packages found": "0 Pakete gefunden", "0 updates found": "0 Updates gefunden", "1 - Errors": "1 <PERSON> <PERSON><PERSON>", "1 day": "1 Tag", "1 hour": "1 Stunde", "1 month": "1 Monat", "1 package was found": "1 Paket gefunden", "1 update is available": "1 Update verfügbar", "1 week": "1 Woche", "1 year": "1 Jahr", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON> Sie die Seite „{0}“ oder „{1}“ auf.", "2 - Warnings": "2 – Warnungen", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON> das/die Paket(e), das/die Sie dem Bündel hinzufügen möchten, und aktivieren Sie das Kontrollkästchen ganz links.", "3 - Information (less)": "3 – <PERSON><PERSON> (weniger)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON> die Pakete, die Si<PERSON> dem Paket hinzufügen möchten, ausgewählt sind, klicken Si<PERSON> in der Symbolleiste auf die Option „{0}“.", "4 - Information (more)": "4 – <PERSON><PERSON> (mehr)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Ihre Pakete wurden dem Bündel hinzugefügt. Sie können weitere Pakete hinzufügen oder das Bündel exportieren.", "5 - information (debug)": "5 – <PERSON><PERSON> (Debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Eine beliebte C/C++-Bibliotheksverwaltung. Voll mit Bibliotheken und Werkzeugen zu C/C++. <br>Enthält: <b>C/C++-Bibliotheken und -Werkzeuge</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Ein Repository mit Tools und ausführbaren Date<PERSON>, designt für das .NET-Ökosystem von Microsoft.<br>Enthält: <b>.NET-bezogene Tools und Skripte</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Ein Repository mit Tools, designt für das .NET-Ökosystem von Microsoft.<br>Enthält: <b>.NET-bezo<PERSON>ls</b>", "A restart is required": "Ein Neustart ist notwendig", "Abort install if pre-install command fails": "Installation abbrechen, wenn der Befehl vor der Installation fehlschlägt", "Abort uninstall if pre-uninstall command fails": "Deinstallation abbrechen, wenn der Befehl vor der Deinstallation fehlschlägt", "Abort update if pre-update command fails": "Aktualisierung abbrechen, wenn der Befehl vor der Aktualisierung fehlschlägt", "About": "<PERSON><PERSON>", "About Qt6": "Über Qt6", "About WingetUI": "Über UniGetUI", "About WingetUI version {0}": "Über UniGetUI-Version {0}", "About the dev": "Über den Entwickler", "Accept": "Akzeptieren", "Action when double-clicking packages, hide successful installations": "Aktion bei Doppelklick auf Pakete, erfolgreiche Installationen ausblenden", "Add": "Hinzufügen", "Add a source to {0}": "<PERSON><PERSON> zu {0} hi<PERSON><PERSON><PERSON><PERSON>", "Add a timestamp to the backup file names": "Zeitstempel an den Sicherungsdateinamen anhängen", "Add a timestamp to the backup files": "Zeitstempel an die Sicherungsdateien anhängen", "Add packages or open an existing bundle": "Pakete hinzufügen oder ein vorhandenes Bündel öffnen", "Add packages or open an existing package bundle": "Pakete hinzufügen oder ein vorhandenes Paketbündel öffnen", "Add packages to bundle": "Pakete zum Bündel hinzufügen", "Add packages to start": "Pakete hinzufügen", "Add selection to bundle": "Auswahl zum Bündel hinzufügen", "Add source": "<PERSON><PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Updates, die mit der Meldung „kein anwendbares Update gefunden“ fehlschlagen, zur Liste der ignorierten Updates hinzufügen", "Adding source {source}": "<PERSON><PERSON> {source} hinzufügen", "Adding source {source} to {manager}": "<PERSON><PERSON> {source} wird zu {manager} hinz<PERSON><PERSON><PERSON>gt", "Addition succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "Administrator privileges": "<PERSON><PERSON><PERSON>", "Administrator privileges preferences": "<PERSON><PERSON><PERSON>", "Administrator rights": "<PERSON><PERSON><PERSON>", "Administrator rights and other dangerous settings": "Administratorrechte und andere potenziell riskante Einstellungen", "Advanced options": "Erweiterte Optionen", "All files": "Alle Dateien", "All versions": "Alle Versionen", "Allow changing the paths for package manager executables": "Ändern der Pfade für ausführbare Dateien des Paketmanagers zulassen", "Allow custom command-line arguments": "Benutzerdefinierte Befehlszeilenargumente zulassen", "Allow importing custom command-line arguments when importing packages from a bundle": "Das Importieren benutzerdefinierter Befehlszeilenargumente beim Importieren von Paketen aus einem Bündel erlauben.", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Import benutzerdefinierter Vor- und Nachinstallationsbefehle beim Importieren von Paketen aus einem Bündel zulassen", "Allow package operations to be performed in parallel": "Parallele Ausführung bei Paketvorgängen erlauben", "Allow parallel installs (NOT RECOMMENDED)": "Parallele Installationen erlauben (NICHT EMPFOHLEN)", "Allow pre-release versions": "Vorabversionen zulassen", "Allow {pm} operations to be performed in parallel": "Für {pm}-Vorgänge eine parallele Ausführung erlauben", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativ können Sie {0} auch mit dem Ausführen des folgenden Befehls in einer Windows PowerShell-Eingabeaufforderung installieren:", "Always elevate {pm} installations by default": "{pm} immer mit erhöhten Rechten installieren", "Always run {pm} operations with administrator rights": "{pm}-Vorgänge immer mit Administratorrechten ausführen", "An error occurred": "Ein Fehler trat auf", "An error occurred when adding the source: ": "Fehler beim Hinzufügen der Quelle:", "An error occurred when attempting to show the package with Id {0}": "<PERSON><PERSON> beim V<PERSON>uch, das Paket mit der ID {0} anzuzeigen", "An error occurred when checking for updates: ": "Fehler beim Suchen nach Updates:", "An error occurred while attempting to create an installation script:": "<PERSON><PERSON>, ein Installationsskript zu erstellen, ist ein Fehler aufgetreten:", "An error occurred while loading a backup: ": "<PERSON>im <PERSON> einer Sicherung ist ein Fehler aufgetreten:", "An error occurred while logging in: ": "<PERSON><PERSON> ist ein Fehler aufgetreten:", "An error occurred while processing this package": "Fehler beim Verarbeiten des Pakets", "An error occurred:": "Ein Fehler ist aufgetreten:", "An interal error occurred. Please view the log for further details.": "Ein interner Fehler ist aufgetreten. Bitte sehen Si<PERSON> sich das Protokoll für weitere Details an.", "An unexpected error occurred:": "Ein unerwarteter Fehler trat auf:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> zu reparieren, ist ein unerwartetes Problem aufgetreten. Bitte versuchen Sie es später erneut", "An update was found!": "Ein Update wurde gefunden!", "Android Subsystem": "Android-Subsystem", "Another source": "<PERSON><PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Alle neuen Verknüpfungen, die während einer Installation oder einer Aktualisierung erstellt werden, werden automatisch gelöscht, anstatt dass beim ersten Auffinden eine Bestätigungsaufforderung angezeigt wird.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "<PERSON>e Shortcuts, die außerhalb von UniGetUI erstellt oder geändert wurden, werden ignoriert. Sie können sie über die Schaltfläche {0} hinzufügen.", "Any unsaved changes will be lost": "Nicht gesicherte Änderungen gehen verloren", "App Name": "Anwendungsname", "Appearance": "Darstellung", "Application theme, startup page, package icons, clear successful installs automatically": "Farbschema, Startseite, Paketsymbole, erfolgreiche Installationen automatisch leeren", "Application theme:": "Farbschema:", "Apply": "<PERSON><PERSON><PERSON>", "Architecture to install:": "Installationsarchitektur:", "Are these screenshots wron or blurry?": "Sind diese Screenshots falsch oder verschwommen?", "Are you really sure you want to enable this feature?": "Möchten Sie diese Funktion wirklich aktivieren?", "Are you sure you want to create a new package bundle? ": "Sind <PERSON> sic<PERSON>, dass Si<PERSON> ein neues Paketbündel erstellen möchten?", "Are you sure you want to delete all shortcuts?": "Möchten Si<PERSON> wirklich alle Verknüpfungen löschen?", "Are you sure?": "Sind Sie sicher?", "Ascendant": "Aufsteigend", "Ask for administrator privileges once for each batch of operations": "Nur ein<PERSON> nach Administratorrechten für jeden Stapel von Vorgängen fragen", "Ask for administrator rights when required": "<PERSON><PERSON>, nach Administratorrechten fragen", "Ask once or always for administrator rights, elevate installations by default": "Ein<PERSON> oder immer nach Administratorrechten fragen, Installationen standardmäßig mit erhöhten Berechtigungen ausführen", "Ask only once for administrator privileges": "<PERSON><PERSON> ein<PERSON> nach Administratorrechten fragen", "Ask only once for administrator privileges (not recommended)": "<PERSON>ur einmal nach Administratorrechten fragen (nicht empfohlen)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Zum Löschen von Desktopverknüpfungen auffordern, die während einer Installation oder eines Upgrades erstellt wurden.", "Attention required": "Aufmerksamkeit erforderlich", "Authenticate to the proxy with an user and a password": "Am Proxy mit Benutzernamen und Passwort authentifizieren", "Author": "Autor", "Automatic desktop shortcut remover": "Desktopverknüpfung automatisch entfernen", "Automatic updates": "Automatische Updates", "Automatically save a list of all your installed packages to easily restore them.": "Liste von allen auf dem Computer installierten Paketen automatisch speichern", "Automatically save a list of your installed packages on your computer.": "Liste von auf dem Computer installierten Paketen automatisch speichern", "Autostart WingetUI in the notifications area": "UniGetUI automatisch im Infobereich starten", "Available Updates": "Verfügbare Updates", "Available updates: {0}": "Verfügbare Updates: {0}", "Available updates: {0}, not finished yet...": "Verfügbare Updates: {0}, noch nicht abgeschlossen...", "Backing up packages to GitHub Gist...": "Pakete werden auf GitHub Gist gesichert...", "Backup": "<PERSON><PERSON><PERSON>", "Backup Failed": "Sicherung fehlgeschlagen", "Backup Successful": "Sicherung erfolgreich", "Backup and Restore": "Sichern und Wiederherstellen", "Backup installed packages": "Sicherung von installierten Paketen erstellen", "Backup location": "Sicherungsort", "Become a contributor": "Werden Sie Mitwirkender", "Become a translator": "Werden Sie Übersetzer", "Begin the process to select a cloud backup and review which packages to restore": "Beginnen Sie mit der Auswahl einer Cloud-Sicherung und prüfen Sie, welche Pakete Sie wiederherstellen möchten.", "Beta features and other options that shouldn't be touched": "Beta-Funktionen und andere Optionen, die nicht geändert werden sollten", "Both": "<PERSON><PERSON>", "Bundle security report": "Bündel-Sicherheitsbericht", "But here are other things you can do to learn about WingetUI even more:": "<PERSON><PERSON> hier sind andere Din<PERSON>, die <PERSON> tun können, um UniGetUI noch besser kennenzulernen:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "<PERSON>n Si<PERSON> einen Paketmanager ausschalten, können Sie zugehörige Pakete nicht mehr sehen oder aktualisieren.", "Cache administrator rights and elevate installers by default": "Administratorrechte zwischenspeichern und Installation standardmäßig mit erhöhten Rechten ausführen", "Cache administrator rights, but elevate installers only when required": "Administrator<PERSON><PERSON> zwischenspeichern, Installation jedoch nur mit erhöhten Rechten ausführen, wenn notwendig", "Cache was reset successfully!": "Cache wurde erfolgreich zurückgesetzt!", "Can't {0} {1}": "Kann {1} nicht {0}", "Cancel": "Abbrechen", "Cancel all operations": "Alle Vorgänge abbrechen", "Change backup output directory": "Sicherungsverzeichnis ändern", "Change default options": "Standardoptionen ändern", "Change how UniGetUI checks and installs available updates for your packages": "Legen Sie fest, wie UniGetUI nach verfügbaren Updates für Ihre Pakete sucht und installiert", "Change how UniGetUI handles install, update and uninstall operations.": "Festlegen, wie UniGetUI Installations-, Aktualisierungs- und Deinstallationsvorgänge ausführt", "Change how UniGetUI installs packages, and checks and installs available updates": "Legen Sie fest, wie UniGetUI Pakete installiert sowie nach verfügbaren Updates sucht und installiert", "Change how operations request administrator rights": "<PERSON><PERSON><PERSON>, wie Vorgänge Administratorrechte anfordern", "Change install location": "Installationsort ändern", "Change this": "<PERSON><PERSON>", "Change this and unlock": "Dies ändern und entsperren", "Check for package updates periodically": "Regelmäßig nach Paketupdates suchen", "Check for updates": "<PERSON>ch Updates suchen", "Check for updates every:": "<PERSON>ch Updates suchen alle:", "Check for updates periodically": "Regelmäßig nach Updates suchen.", "Check for updates regularly, and ask me what to do when updates are found.": "Regelmäßig nach Updates suchen; bei Verfügbarkeit von Updates nach weiteren Aktionen fragen.", "Check for updates regularly, and automatically install available ones.": "Regelmäßig nach Updates suchen und verfügbare Updates automatisch installieren.", "Check out my {0} and my {1}!": "<PERSON><PERSON><PERSON> auch mein {0} und {1} an!", "Check out some WingetUI overviews": "Sc<PERSON>uen Sie sich einige UniGetUI-Berichte an", "Checking for other running instances...": "<PERSON>f andere laufende Instanzen prüfen...", "Checking for updates...": "Nach Updates suchen...", "Checking found instace(s)...": "Gefundene Instanz(en) prüfen...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON> an Vorgängen, die parallel ausgeführt werden sollen:", "Clear cache": "<PERSON><PERSON> le<PERSON>n", "Clear finished operations": "Beendete Vorgänge löschen", "Clear selection": "Auswahl aufheben", "Clear successful operations": "Erfolgreiche Vorgänge entfernen", "Clear successful operations from the operation list after a 5 second delay": "Erfolgreiche Vorgänge nach einer Verzögerung von 5 Sekunden aus der Liste entfernen", "Clear the local icon cache": "Lokalen Symbol-Cache leeren", "Clearing Scoop cache - WingetUI": "<PERSON><PERSON>-<PERSON><PERSON> le<PERSON> – UniGetUI", "Clearing Scoop cache...": "<PERSON><PERSON>-<PERSON><PERSON> le<PERSON>...", "Click here for more details": "Hier klicken für mehr Details", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Klicken Sie auf Installieren, um den Installationsprozess zu starten. Wenn Sie die Installation überspringen, funktioniert UniGetUI möglicherweise nicht wie erwartet.", "Close": "Schließen", "Close UniGetUI to the system tray": "UniGetUI nach dem Schließen im Infobereich anzeigen", "Close WingetUI to the notification area": "UniGetUI in den Infobereich schließen", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Die Cloud-Sicherung verwendet ein privates GitHub Gist, um eine Liste der installierten Pakete zu speichern.", "Cloud package backup": "Cloud-Paketsicherung", "Command-line Output": "Kommandozeilen-Ausgabe", "Command-line to run:": "Auszuführende Befehlszeile:", "Compare query against": "Abfrage vergleichen mit", "Compatible with authentication": "Kompatibel mit Authentifizierung", "Compatible with proxy": "Kompatibel mit Proxy", "Component Information": "Installierte Komponenten", "Concurrency and execution": "<PERSON><PERSON>le Ausführung", "Connect the internet using a custom proxy": "Über einen benutzerdefinierten Proxy mit dem Internet verbinden", "Continue": "<PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Zum Symbol- und Screenshot-Repository beitragen", "Contributors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "In Zwischenablage kopieren", "Could not add source": "Konnte Quelle nicht hinzufügen", "Could not add source {source} to {manager}": "<PERSON><PERSON> {source} kon<PERSON> nicht zu {manager} hinz<PERSON><PERSON><PERSON><PERSON> werden", "Could not back up packages to GitHub Gist: ": "Die Pakete konnten nicht auf GitHub Gist gesichert werden:", "Could not create bundle": "Bündel konnte nicht erstellt werden", "Could not load announcements - ": "Ankündigungen konnten nicht geladen werden –", "Could not load announcements - HTTP status code is $CODE": "Ankündigungen konnten nicht geladen werden – HTTP Status-Code ist $CODE", "Could not remove source": "Konnte Quelle nicht entfernen", "Could not remove source {source} from {manager}": "<PERSON><PERSON> {source} kon<PERSON> nicht von {manager} entfernt werden", "Could not remove {source} from {manager}": "{source} kon<PERSON> nicht aus {manager} entfernt werden", "Create .ps1 script": ".ps1-<PERSON><PERSON><PERSON><PERSON> erstellen", "Credentials": "Anmeldedaten", "Current Version": "Aktuelle Version", "Current status: Not logged in": "Aktueller Status: Nicht angemeldet", "Current user": "Aktueller Benutzer", "Custom arguments:": "Benutzerdefinierte Befehle:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Benutzerdefinierte Befehlszeilenargumente können die Art und Weise ändern, wie Programme installiert, aktualisiert oder deinstalliert werden, und zwar in einer Weise, die UniGetUI nicht kontrollieren kann. Die Verwendung benutzerdefinierter Befehlszeilen kann Pakete beschädigen. Gehen Sie daher mit Vorsicht vor.", "Custom command-line arguments:": "Benutzerdefinierte Kommandozeilen-Argumente:", "Custom install arguments:": "Benutzerdefinierte Installationsargumente:", "Custom uninstall arguments:": "Benutzerdefinierte Deinstallationsargumente:", "Custom update arguments:": "Benutzerdefinierte Aktualisierungsargumente:", "Customize WingetUI - for hackers and advanced users only": "UniGetUI anpassen – nur für Hacker und fortgeschrittene Benutzer", "DEBUG BUILD": "DEBUG-BUILD", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "HAFTUNGSAUSSCHLUSS: WIR SIND NICHT VERANTWORTLICH FÜR DIE HERUNTERGELADENEN PAKETE. BITTE STELLEN SIE SICHER, DASS SIE NUR VERTRAUE<PERSON><PERSON>ÜRD<PERSON><PERSON> SOFTWARE INSTALLIEREN.", "Dark": "<PERSON><PERSON><PERSON>", "Decline": "<PERSON><PERSON><PERSON><PERSON>", "Default": "Standard", "Default installation options for {0} packages": "Standardinstallationsoptionen für {0} Pakete", "Default preferences - suitable for regular users": "Standardeinstellungen – geeignet für normale Benutzer", "Default vcpkg triplet": "Standard vcpkg-Triplett", "Delete?": "Löschen?", "Dependencies:": "Abhängigkeiten:", "Descendant": "Absteigend", "Description:": "Beschreibung:", "Desktop shortcut created": "Desktopverknüpfung angelegt", "Details of the report:": "Berichtsdetails:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Entwickeln ist hart und diese App ist kostenlos. Wenn sie diese App mögen, können sie mir jederzeit <b>einen <PERSON>ffee kaufen</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "<PERSON><PERSON> Doppelklick auf ein Element in „{discoveryTab}“ direkt installieren (anstatt die Paketinfos anzuzeigen)", "Disable new share API (port 7058)": "Die neue Teilen-API (Port 7058) deaktivieren", "Disable the 1-minute timeout for package-related operations": "Einminütige Zeitüberschreitung für paketbezogene Vorgänge deaktivieren", "Disclaimer": "Haftungsausschluss", "Discover Packages": "Pakete suchen", "Discover packages": "Pakete suchen", "Distinguish between\nuppercase and lowercase": "Groß-/Kleinschreibung beachten", "Distinguish between uppercase and lowercase": "Groß-/Kleinschreibung beachten", "Do NOT check for updates": "NICHT nach Updates suchen", "Do an interactive install for the selected packages": "Ausgewählte Pakete interaktiv installieren", "Do an interactive uninstall for the selected packages": "Ausgewählte Pakete interaktiv deinstallieren", "Do an interactive update for the selected packages": "Ausgewählte Pakete interaktiv aktualisieren", "Do not automatically install updates when the battery saver is on": "Keine automatischen Updates im Energiesparmodus installieren", "Do not automatically install updates when the network connection is metered": "Keine automatischen Updates bei getakteter Verbindung installieren", "Do not download new app translations from GitHub automatically": "Neue Übersetzungen nicht automatisch von GitHub herunterladen", "Do not ignore updates for this package anymore": "Updates für dieses Paket nicht mehr ignorieren", "Do not remove successful operations from the list automatically": "Erfolgreiche Vorgänge nicht automatisch aus der Liste entfernen", "Do not show this dialog again for {0}": "<PERSON>sen Dialog für {0} nicht mehr anzeigen", "Do not update package indexes on launch": "Paketindizes beim Start nicht aktualisieren", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Sind Sie damit ein<PERSON>den, dass UniGetUI anonyme Nutzungsstatistiken sammelt und übermittelt, mit dem alleinigen Zweck, die Nutzererfahrung zu verstehen und zu verbessern?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Finden Sie UniGetUI nützlich? Wenn Si<PERSON> möchten, können Sie meine Arbeit unterstützen, damit ich UniGetUI zur ultimativen Paketverwaltungsoberfläche weiterentwickeln kann.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Finden Sie UniGetUI nützlich? Möchten Sie den Entwickler unterstützen? Wenn ja, können <PERSON> {0}, es hilft sehr!", "Do you really want to reset this list? This action cannot be reverted.": "Möchten Si<PERSON> wirklich diese Liste zurücksetzen? Diese Aktion kann nicht rückgängig getan werden.", "Do you really want to uninstall the following {0} packages?": "Möchten Sie wirklich die folgenden {0} Pakete deinstallieren?", "Do you really want to uninstall {0} packages?": "Möchten Sie wirklich {0} Pakete deinstallieren?", "Do you really want to uninstall {0}?": "Möchten Sie wirklich {0} deinstallieren?", "Do you want to restart your computer now?": "Soll der PC jetzt neu gestartet werden?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Möchten Sie UniGetUI in Ihre Sprache übersetzen? <a style=\"color:{0}\" href=\"{1}\"a>HIER</a> können Sie unterstützen!", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Sie möchten lieber nicht spenden? <PERSON><PERSON>, Sie können UniGetUI jederzeit mit Ihren Freunden teilen. Verbreiten Sie einfach UnigetUI.", "Donate": "<PERSON><PERSON><PERSON>", "Done!": "Fertig!", "Download failed": "Download fehlgeschlagen", "Download installer": "Installations<PERSON><PERSON> her<PERSON>n", "Download operations are not affected by this setting": "Download-Vorgänge sind von dieser Einstellung nicht betroffen", "Download selected installers": "Ausgewählte Installationsdateien herunterladen", "Download succeeded": "Download erfolgreich", "Download updated language files from GitHub automatically": "Aktualisierte Sprachdateien automatisch von GitHub herunterladen", "Downloading": "Wird <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Downloading backup...": "Sicherung wird heruntergeladen...", "Downloading installer for {package}": "Installationsdatei für {package} wird heruntergeladen", "Downloading package metadata...": "<PERSON>et-<PERSON><PERSON>ten werden heruntergeladen...", "Enable Scoop cleanup on launch": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON> beim Start aktivieren", "Enable WingetUI notifications": "UniGetUI-Benachrichtigungen aktivieren", "Enable an [experimental] improved WinGet troubleshooter": "Aktivieren einer [experimentellen] verbesserten WinGet-Fehlerbehebungsfunktion", "Enable and disable package managers, change default install options, etc.": "Paketmanager aktivieren und deaktivieren, Standardinstallationsoptionen ändern usw.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Optimierungen der Hintergrund-CPU-Auslastung aktivieren (siehe Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Hintergrund-API aktivieren (UniGetUI-Widgets und -Zugriff, Port 7058)", "Enable it to install packages from {pm}.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, um Pakete von {pm} zu installieren.", "Enable the automatic WinGet troubleshooter": "Automatische WinGet-Fehlerbehebung aktivieren", "Enable the new UniGetUI-Branded UAC Elevator": "Den neuen UniGetUI-gebrandeten UAC-Elevator aktivieren", "Enable the new process input handler (StdIn automated closer)": "Den neuen Prozess-Eingabe-Handler aktivieren (StdIn automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Aktivieren Sie die nachstehenden Einstellungen nur, WENN UND NUR WENN Sie sich über deren Funktion und die damit verbundenen Auswirkungen und Gefahren im Klaren sind.", "Enable {pm}": "{pm} aktivieren", "Enter proxy URL here": "Proxy-URL hier e<PERSON>ben", "Entries that show in RED will be IMPORTED.": "<PERSON><PERSON><PERSON><PERSON>, die in ROT angezeigt werden, werden IMPORTIERT.", "Entries that show in YELLOW will be IGNORED.": "<PERSON><PERSON><PERSON><PERSON>, die in GELB angezeigt werden, werden IGNORIERT.", "Error": "<PERSON><PERSON>", "Everything is up to date": "Alles ist auf dem neuesten Stand", "Exact match": "<PERSON><PERSON><PERSON>", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Vorhandene Verknüpfungen auf Ihrem Desktop werden gescannt, und Sie müssen auswählen, welche Si<PERSON> behalten und welche Sie entfernen möchten.", "Expand version": "Version erweitern", "Experimental settings and developer options": "Experimentelle und Entwickleroptionen", "Export": "Exportieren", "Export log as a file": "Protokoll als Datei exportieren", "Export packages": "Pakete exportieren", "Export selected packages to a file": "Ausgewählte Pakete in eine Datei exportieren", "Export settings to a local file": "Einstellungen in lokale Datei exportieren", "Export to a file": "Als Datei exportieren", "Failed": "Fehlgeschlagen", "Fetching available backups...": "Verfügbare Sicherungen abrufen...", "Fetching latest announcements, please wait...": "Neueste Ankündigungen werden abgerufen, bitte warten...", "Filters": "Filter", "Finish": "Abschließen", "Follow system color scheme": "Farbschema des Systems verwenden", "Follow the default options when installing, upgrading or uninstalling this package": "Die Standardoptionen verwenden, wenn dieses Paket installiert, aktualisiert oder deinstalliert wird.", "For security reasons, changing the executable file is disabled by default": "Aus Sicherheitsgründen ist das Ändern der ausführbaren Datei standardmäßig deaktiviert.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Aus Sicherheitsgründen sind benutzerdefinierte Kommandozeilenargumente standardmäßig deaktiviert. Gehen Sie zu UniGetUI-Sicherheitseinstellungen, um dies zu ändern.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Aus Sicherheitsgründen sind die Skripte vor und nach der Operation standardmäßig deaktiviert. Gehen Sie zu UniGetUI-Sicherheitseinstellungen, um dies zu ändern.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "ARM-kompilierte WinGet-Version erzwingen (NUR FÜR ARM64-SYSTEME)", "Formerly known as WingetUI": "Ehemals bekannt als WingetUI", "Found": "Gefunden", "Found packages: ": "Gefundene Pakete:", "Found packages: {0}": "Gefundene Pakete: {0}", "Found packages: {0}, not finished yet...": "Gefundene Pakete: {0}, noch nicht abgeschlossen...", "General preferences": "Allgemeines", "GitHub profile": "GitHub-Profil", "Global": "Global", "Go to UniGetUI security settings": "Zu den UniGetUI-Sicherheitseinstellungen gehen", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Große Sammlung nützlicher Tools und anderer interessanter Pakete.<br>Enthält: <b>Utilities, Befehlszeilenprogramme, allgemeine Software (Zusatzpaket erforderlich)</b>", "Great! You are on the latest version.": "Sehr gut! Sie haben die neueste Version.", "Grid": "<PERSON><PERSON>", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Hilfe und Dokumentation", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Hier können Sie das Verhalten von UniGetUI bezüglich der folgenden Verknüpfungen ändern. Wenn Sie ein Häkchen setzen, wird UniGetUI die Verknüpfung löschen, wenn sie bei einem zukünftigen Upgrade erstellt wird. Wenn Sie das Häkchen entfernen, bleibt die Verknüpfung bestehen.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, mein <PERSON> ist Martí, und ich bin der <i>Entwickler</i> von UniGetUI. UniGetUI ist komplett in meiner Freizeit entstanden!", "Hide details": "Details ausblenden", "Homepage": "Homepage", "Hooray! No updates were found.": "Hurra! Es wurden keine Updates gefunden.", "How should installations that require administrator privileges be treated?": "Wie sollen Installationen, die Administratorrechte benötigen, behandelt werden?", "How to add packages to a bundle": "So fügen Sie Pakete zu einem Bündel hinzu", "I understand": "<PERSON>ch verstehe", "Icons": "Symbole", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "<PERSON>n Sie die Cloud-Sicherung aktiviert haben, wird sie als GitHub Gist auf diesem Konto gespeichert", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Benutzerdefinierte Vor- und Nachinstallationsbefehle beim Importieren von Paketen aus einem Bündel ignorieren", "Ignore future updates for this package": "Zukünftige Updates für dieses Paket ignorieren", "Ignore packages from {pm} when showing a notification about updates": "<PERSON><PERSON> von {pm} ignorieren, wenn eine Benachrichtigung über Updates angezeigt wird", "Ignore selected packages": "Ausgewählte Pakete ignorieren", "Ignore special characters": "Sonderzeichen ignorieren", "Ignore updates for the selected packages": "Updates für ausgewählte Pakete ignorieren", "Ignore updates for this package": "Updates für dieses Paket ignorieren", "Ignored updates": "Ignorierte Updates", "Ignored version": "Ignorierte Version", "Import": "Importieren", "Import packages": "Pakete importieren", "Import packages from a file": "Pakete aus einer Datei importieren", "Import settings from a local file": "Einstellungen aus lokaler Datei importieren", "In order to add packages to a bundle, you will need to: ": "Um Pakete zu einem Bündel hinzuzufügen, müssen Sie Folgendes tun:", "Initializing WingetUI...": "UniGetUI wird initialisiert...", "Install": "Installieren", "Install Scoop": "Scoop installieren", "Install and more": "Installieren und mehr", "Install and update preferences": "Installations- und Updateeinstellungen", "Install as administrator": "Als Administrator install<PERSON><PERSON>", "Install available updates automatically": "Verfügbare Updates automatisch installieren", "Install location can't be changed for {0} packages": "Der Installationsort kann für {0} Pakete nicht geändert werden", "Install location:": "Installationsort:", "Install options": "Installationsoptionen", "Install packages from a file": "Paket aus einer Datei installieren", "Install prerelease versions of UniGetUI": "Auch Vorabversionen von UniGetUI installieren", "Install script": "Installationsskript", "Install selected packages": "Ausgewählte Pakete installieren", "Install selected packages with administrator privileges": "Ausgewählte Pakete mit Administratorrechten installieren", "Install selection": "Auswahl installieren", "Install the latest prerelease version": "Neuste Vorabversion installieren", "Install updates automatically": "Updates automatisch installieren", "Install {0}": "{0} installieren", "Installation canceled by the user!": "Installation durch den Benutzer abgebrochen!", "Installation failed": "Installation fehlgeschlagen", "Installation options": "Installationsoptionen", "Installation scope:": "Installationsumgebung:", "Installation succeeded": "Installation erfolgreich", "Installed Packages": "Installierte Pakete", "Installed Version": "Installierte Version:", "Installed packages": "Installierte Pakete", "Installer SHA256": "Installations-SHA256", "Installer SHA512": "Installations-SHA512", "Installer Type": "Installations-Typ", "Installer URL": "Installations-URL", "Installer not available": "Installationsdatei nicht verfügbar", "Instance {0} responded, quitting...": "Ant<PERSON><PERSON> von In<PERSON>z {0} erhalten, wird beendet...", "Instant search": "Schnellsuche", "Integrity checks can be disabled from the Experimental Settings": "Integritätsprüfungen können in den experimentellen Einstellungen deaktiviert werden.", "Integrity checks skipped": "Integritätsüberprüfungen übersprungen", "Integrity checks will not be performed during this operation": "Integritätsprüfungen werden nicht während dieses Vorgangs ausgeführt", "Interactive installation": "Interaktiv installieren", "Interactive operation": "Interaktiver Vorgang", "Interactive uninstall": "Interaktiv deinstallieren", "Interactive update": "Interaktiv aktualisieren", "Internet connection settings": "Internetverbindung", "Is this package missing the icon?": "<PERSON><PERSON>t diesem Paket das Symbol?", "Is your language missing or incomplete?": "Fe<PERSON>t Ihre Sprache oder ist diese unvollständig?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Es wird nicht garantiert, dass die angegebenen Anmeldedaten sicher gesichert werden. Verwenden Sie daher besser nicht Ihre Bankdaten.", "It is recommended to restart UniGetUI after WinGet has been repaired": "<PERSON><PERSON> wird <PERSON><PERSON><PERSON>, UniGetUI nach der Reparatur von WinGet neu zu starten", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "<PERSON>s wird dringend em<PERSON><PERSON>, UniGetUI neu zu installieren, um das Problem zu beheben.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "WinGet scheint nicht richtig zu funktionieren. <PERSON><PERSON><PERSON><PERSON> Si<PERSON> versuchen, WinGet zu reparieren?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON> sieht so aus, als hätten Sie UniGetUI als Administrator ausgeführt, was nicht empfohlen wird. Sie können das Programm weiterhin verwenden, aber wir empfehlen dringend, UniGetUI nicht mit Administratorrechten auszuführen. Klicken Sie auf „{showDetails}“, um den Grund dafür zu erfahren.", "Language": "<PERSON><PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "Sprache und andere Einstellungen", "Last updated:": "Zuletzt aktualisiert:", "Latest": "Neueste", "Latest Version": "Aktuellste Version", "Latest Version:": "Aktuellste Version:", "Latest details...": "Neueste Details...", "Launching subprocess...": "Subprozess starten...", "Leave empty for default": "<PERSON><PERSON> lassen für Standard", "License": "<PERSON><PERSON><PERSON>", "Licenses": "<PERSON><PERSON><PERSON>", "Light": "Hell", "List": "Liste", "Live command-line output": "Live-Kommandozeilenausgabe", "Live output": "Live-Ausgabe", "Loading UI components...": "Bedienoberfläche wird geladen...", "Loading WingetUI...": "UniGetUI wird geladen...", "Loading packages": "Pakete werden geladen", "Loading packages, please wait...": "Pakete werden geladen, bitte warten...", "Loading...": "Laden...", "Local": "<PERSON><PERSON>", "Local PC": "Lokaler PC", "Local backup advanced options": "Erweiterte Optionen für die lokale Sicherung", "Local machine": "Lokaler Computer", "Local package backup": "Lokale Paketsicherung", "Locating {pm}...": "{pm} lokalisieren...", "Log in": "Anmelden", "Log in failed: ": "Anmeldung fehlgeschlagen:", "Log in to enable cloud backup": "<PERSON><PERSON><PERSON>, um die Cloud-Sicherung zu aktivieren", "Log in with GitHub": "Bei GitHub anmelden", "Log in with GitHub to enable cloud package backup.": "<PERSON>i GitHub anmelden, um die Cloud-Paketsicherung zu aktivieren.", "Log level:": "Protokollebene:", "Log out": "Abmelden", "Log out failed: ": "Abmeldung fehlgeschlagen:", "Log out from GitHub": "<PERSON> abmelden", "Looking for packages...": "Suche nach Paketen...", "Machine | Global": "Computer | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Fehlerhafte Befehlszeilenargumente können Pakete beschädigen oder sogar einem böswilligen Akteur die Ausführung mit erhöhten Rechten ermöglichen. Daher ist das Importieren benutzerdefinierter Befehlszeilenargumente standardmäßig deaktiviert.", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "UniGetUI-Einstellungen verwalten", "Manage WingetUI autostart behaviour from the Settings app": "Autostartverhalten von UniGetUI über die Einstellungen-App verwalten", "Manage ignored packages": "Ignorierte Pakete verwalten", "Manage ignored updates": "Ignorierte Updates ver<PERSON>ten", "Manage shortcuts": "Verknüpfungen verwalten", "Manage telemetry settings": "Telemetrieeinstellungen verwalten", "Manage {0} sources": "{0} <PERSON><PERSON> ver<PERSON>", "Manifest": "Manifest", "Manifests": "Manifeste", "Manual scan": "<PERSON><PERSON>", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Offizieller Paketmanager von Microsoft. Enthält viele bekannte und verifizierte Pakete.<br>Enthält: <b>Allgemeine Software, Microsoft Store-Apps</b>", "Missing dependency": "Fehlende Abhängigkeit", "More": "<PERSON><PERSON>", "More details": "Mehr Details", "More details about the shared data and how it will be processed": "Mehr Details über die geteilten Daten und wie sie verarbeitet werden", "More info": "Me<PERSON> Infos", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "HINWEIS: Diese Funktion zur Fehlersuche kann in den Paketmanager-Einstellungen im Abschnitt „WinGet“ deaktiviert werden.", "Name": "Name", "New": "<PERSON>eu", "New Version": "Neue Version", "New bundle": "Neues Bündel", "New version": "Neue Version", "Nice! Backups will be uploaded to a private gist on your account": "Super! Die Sicherungen werden in einen privaten Gist auf Ihrem Konto hochgeladen", "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": "Es wurde keine geeignete Installationsdatei für das Paket {0} gefunden", "No dependencies specified": "<PERSON><PERSON> A<PERSON>hängigkeiten angegeben", "No new shortcuts were found during the scan.": "Während des Scans wurden keine neuen Verknüpfungen gefunden.", "No packages found": "<PERSON><PERSON> gefunden", "No packages found matching the input criteria": "<PERSON><PERSON> gefunden, die den Eingabekriterien entsprechen", "No packages have been added yet": "<PERSON><PERSON> wurden bisher keine Pakete hinzugefügt.", "No packages selected": "<PERSON>ine Pakete ausgewählt", "No packages were found": "<PERSON><PERSON> gefunden", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Persönliche Daten werden weder gesammelt noch gesendet, und die gesammelten Daten sind anonymisiert, sodass sie nicht zu Ihnen zurückverfolgt werden können.", "No results were found matching the input criteria": "<PERSON>s wurden keine Ergebnisse gefunden, die den eingegebenen Kriterien entsprechen.", "No sources found": "<PERSON><PERSON> gefunden", "No sources were found": "<PERSON><PERSON> gefunden", "No updates are available": "<PERSON><PERSON> sind keine Updates verfügbar", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node.js-Paketmanager. Enthält viele Bibliotheken und andere Dienstprogramme aus der JavaScript-Welt.<br>Enthält: <b>Node JavaScript-Bibliotheken und andere zugehörige Dienstprogramme</b>", "Not available": "Nicht verfügbar", "Not finding the file you are looking for? Make sure it has been added to path.": "Sie können die gesuchte Datei nicht finden? <PERSON><PERSON><PERSON> sicher, dass sie zum Pfad hinzugefügt wurde.", "Not found": "Nicht gefunden", "Not right now": "<PERSON><PERSON> nicht", "Notes:": "Bemerkungen:", "Notification preferences": "Benachrichtigungen", "Notification tray options": "Benachrichtigungsleisten-Optionen", "Notification types": "Benachrichtigungstypen", "NuPkg (zipped manifest)": "NuPkg (komprimiertes Manifest)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON><PERSON>", "Open GitHub": "GitHub <PERSON>", "Open UniGetUI": "UniGetUI öffnen", "Open UniGetUI security settings": "UniGetUI-Sicherheitseinstellungen öffnen", "Open WingetUI": "UniGetUI öffnen", "Open backup location": "Sicherungsverzeichnis öffnen", "Open existing bundle": "Vorhandenes Bündel öffnen", "Open install location": "Installationsort öffnen", "Open the welcome wizard": "Begrüßungsbildschirm öffnen", "Operation canceled by user": "Vorgang vom Benutzer abgebrochen", "Operation cancelled": "Vorgang abgebrochen", "Operation history": "Vorgangsverlauf", "Operation in progress": "Vorgang l<PERSON>", "Operation on queue (position {0})...": "Vorgang in Warteschlange (Position {0})...", "Operation profile:": "Vorgangsprofil:", "Options saved": "Optionen g<PERSON>chert", "Order by:": "Sortieren nach:", "Other": "<PERSON><PERSON>", "Other settings": "Weitere Einstellungen", "Package": "<PERSON><PERSON>", "Package Bundles": "Paketbündel", "Package ID": "Paket-ID", "Package Manager": "Paketmanager", "Package Manager logs": "Paketmanager-Protokolle", "Package Managers": "Paketmanager", "Package Name": "Paketname", "Package backup": "Paketsicherung", "Package backup settings": "Einstellungen für die Paketsicherung", "Package bundle": "Paketbündel", "Package details": "Paketdetails", "Package lists": "Paketlisten", "Package management made easy": "Paketverwaltung leicht gemacht", "Package manager": "Paketmanager", "Package manager preferences": "Paketmanager-Einstellungen", "Package managers": "Paketmanager", "Package not found": "<PERSON>et nicht gefunden", "Package operation preferences": "Paketvorgänge", "Package update preferences": "<PERSON><PERSON><PERSON><PERSON>", "Package {name} from {manager}": "<PERSON><PERSON> {name} von {manager}", "Package's default": "Standardeinstellung des Pakets", "Packages": "<PERSON><PERSON>", "Packages found: {0}": "Pakete gefunden: {0}", "Partially": "Teilweise", "Password": "Passwort", "Paste a valid URL to the database": "Fügen Sie eine gültige URL zur Datenbank ein", "Pause updates for": "Updates au<PERSON><PERSON><PERSON> für", "Perform a backup now": "Jetzt eine Sicherung durchführen", "Perform a cloud backup now": "Jetzt eine Cloud-Sicherung durchführen", "Perform a local backup now": "Jetzt eine lokale Sicherung durchführen", "Perform integrity checks at startup": "<PERSON><PERSON> Start Integritätsprüfungen durchführen", "Performing backup, please wait...": "Sicherung wird erstellt, bitte warten...", "Periodically perform a backup of the installed packages": "Regelmäßig eine Sicherung der installierten Pakete durchführen", "Periodically perform a cloud backup of the installed packages": "Regelmäßig eine Cloud-Sicherung der installierten Pakete durchführen", "Periodically perform a local backup of the installed packages": "Regelmäßig eine lokale Sicherung der installierten Pakete durchführen", "Please check the installation options for this package and try again": "Prüfen Sie bitte die Installationsoptionen des Pakets und versuchen Si<PERSON> es noch einmal", "Please click on \"Continue\" to continue": "Bitte klicken Sie auf „Weiter“, um fortzufahren", "Please enter at least 3 characters": "<PERSON>te geben Si<PERSON> mindestens drei Zeichen ein", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON> Si<PERSON>, dass manche Pakete aufgrund der Paketmanager, die auf diesem Gerät aktiviert sind, nicht installierbar sind.", "Please note that not all package managers may fully support this feature": "<PERSON>te beachten Si<PERSON>, dass nicht alle Paketmanager diese Funktion vollständig unterstützen.", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "<PERSON>te beach<PERSON> Si<PERSON>, dass Pakete von manchen Quellen nicht exportiert werden können. Diese wurden ausgegraut und können nicht exportiert werden.", "Please run UniGetUI as a regular user and try again.": "Starten Sie bitte UniGetUI als normaler Benutzer und versuchen Sie es noch einmal.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Bitte prüfen Sie die Kommandozeilen-Ausgabe oder schauen Sie im Vorgangsverlauf nach, um weitere Informationen über den Fehler zu erhalten.", "Please select how you want to configure WingetUI": "Bitte wählen Sie, wie Sie UniGetUI konfigurieren möchten", "Please try again later": "Versuchen Sie es bitte später noch einmal", "Please type at least two characters": "Bitte geben Sie mindestens zwei Zeichen ein", "Please wait": "Bitte warten", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "<PERSON>te warten, w<PERSON>hrend {0} installiert wird. Möglicherweise erscheint ein schwarzes Fenster. Bitte warten Si<PERSON>, bis es sich schließt.", "Please wait...": "Bitte warten...", "Portable": "Portabel", "Portable mode": "Portabler Modus", "Post-install command:": "Befehl nach der Installation:", "Post-uninstall command:": "Befehl nach der Deinstallation:", "Post-update command:": "Befehl nach der Aktualisierung:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell-Paketmanager. Finden Sie Bibliotheken und Skripte zur Erweiterung der PowerShell-Funktionen.<br>Enthält: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Vorinstallations- und Nachinstallationsbefehle können Ihrem System erheblichen Schaden zufügen, wenn sie entsprechend programmiert sind. Es kann sehr riskant sein, <PERSON><PERSON><PERSON>e aus einem Bündel zu importieren, es sei denn, <PERSON><PERSON> vertrauen der Quelle dieses Paketbündels.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Vorinstallations- und Nachinstallationsbefehle werden vor und nach der Installation, Aktualisierung oder Deinstallation eines Pakets ausgeführt. <PERSON><PERSON>, dass sie zu Problemen führen können, wenn sie nicht sorgfältig verwendet werden.", "Pre-install command:": "Befehl vor der Installation:", "Pre-uninstall command:": "Befehl vor der Deinstallation:", "Pre-update command:": "Befehl vor der Aktualisierung:", "PreRelease": "Vorabversion", "Preparing packages, please wait...": "Pakete werden vorbereitet, bitte warten...", "Proceed at your own risk.": "Fortfahren auf eigenes Risiko", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Jede Art von Berechtigungserhöhung über UniGetUI-Elevator oder GSudo verbieten", "Proxy URL": "Proxy-URL", "Proxy compatibility table": "Proxy-Kompatibilitätstabelle", "Proxy settings": "Proxy-Einstellungen", "Proxy settings, etc.": "Proxy-Einstellungen usw.", "Publication date:": "Veröffentlichungsdatum:", "Publisher": "Herausgeber", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Paketmanager von Python. Enthält viele Python-Bibliotheken und andere mit Python verwandte Dienstprogramme.<br>Enthält: <b>Python-Bibliotheken und zugehörige Dienstprogramme</b>", "Quit": "<PERSON>den", "Quit WingetUI": "UniGetUI beenden", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Benutzerkontensteuerung-Eingabeaufforderungen reduzieren, Installationen standardmäßig mit erhöhten Berechtigungen ausführen, bestimmte riskante Funktionen freischalten usw.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Weitere Informationen zu den betroffenen Dateien finden Sie in den UniGetUI-Protokollen.", "Reinstall": "Neu installieren", "Reinstall package": "<PERSON>et neu installieren", "Related settings": "Verwandte Einstellungen", "Release notes": "Versionshinweise", "Release notes URL": "Versionshinweise-URL", "Release notes URL:": "Versionshinweise-URL:", "Release notes:": "Versionshinweise:", "Reload": "Neu laden", "Reload log": "Protokoll neu laden", "Removal failed": "Entfernen fehlgeschlagen", "Removal succeeded": "Erfolgreich entfernt", "Remove from list": "Aus der Liste entfernen", "Remove permanent data": "Permanente Daten entfernen", "Remove selection from bundle": "Auswahl aus Bündel entfernen", "Remove successful installs/uninstalls/updates from the installation list": "Erfolgreiche Installationen/Deinstallationen/Updates automatisch aus der Installationsliste entfernen", "Removing source {source}": "<PERSON><PERSON> {source} entfernen", "Removing source {source} from {manager}": "<PERSON><PERSON> {source} aus {manager} entfernen", "Repair UniGetUI": "UniGetUI reparieren", "Repair WinGet": "WinGet reparieren", "Report an issue or submit a feature request": "Ein Problem melden oder eine Funktionsanfrage stellen", "Repository": "Repository", "Reset": "Z<PERSON>ücksetzen", "Reset Scoop's global app cache": "Scoop's globalen App-<PERSON><PERSON>", "Reset UniGetUI": "UniGetUI zurücksetzen", "Reset WinGet": "WinGet zurücksetzen", "Reset Winget sources (might help if no packages are listed)": "WinGet-Quellen zurücksetzen (könnte helfen, wenn keine Pakete aufgelistet sind)", "Reset WingetUI": "UniGetUI zurücksetzen", "Reset WingetUI and its preferences": "UniGetUI inkl. Einstellungen zurücksetzen", "Reset WingetUI icon and screenshot cache": "UniGetUI-Symbol- und Screenshot-Cache zurücksetzen", "Reset list": "Liste zurücksetzen", "Resetting Winget sources - WingetUI": "WinGet-Quellen zurücksetzen – UniGetUI", "Restart": "<PERSON>eu starten", "Restart UniGetUI": "UniGetUI neu starten", "Restart WingetUI": "UniGetUI neu starten", "Restart WingetUI to fully apply changes": "Zum Anwenden der Änderungen UniGetUI neu starten", "Restart later": "Später neu starten", "Restart now": "Jetzt neu starten", "Restart required": "<PERSON><PERSON><PERSON><PERSON>", "Restart your PC to finish installation": "Starten Sie Ihren PC neu, um die Installation abzuschließen", "Restart your computer to finish the installation": "Starten Sie Ihren Computer neu, um die Installation abzuschließen", "Restore a backup from the cloud": "Eine Sicherung aus der Cloud wiederherstellen", "Restrictions on package managers": "Beschränkungen für Paketmanager", "Restrictions on package operations": "Beschränkungen für Paketvorgänge", "Restrictions when importing package bundles": "Beschränkungen beim Import von Paketbündeln", "Retry": "<PERSON><PERSON><PERSON> versuchen", "Retry as administrator": "Erneut als Administrator vers<PERSON>n", "Retry failed operations": "Fehlgeschlagene Vorgänge erneut versuchen", "Retry interactively": "Interaktiv erneut versuchen", "Retry skipping integrity checks": "Erneut versuchen und dabei Integritätsprüfungen überspringen", "Retrying, please wait...": "<PERSON><PERSON><PERSON> V<PERSON>, bitte warten...", "Return to top": "Z<PERSON><PERSON> zum Anfang", "Run": "Ausführen", "Run as admin": "Als Administrator au<PERSON><PERSON><PERSON><PERSON>", "Run cleanup and clear cache": "Bereinigung ausführen und Cache leeren", "Run last": "Als Letztes ausführen", "Run next": "Als Nächstes ausführen", "Run now": "Jetzt ausführen", "Running the installer...": "Installation wird ausgeführt...", "Running the uninstaller...": "Deinstallation wird ausgeführt...", "Running the updater...": "Updater wird ausgeführt...", "Save": "Speichern", "Save File": "<PERSON><PERSON> s<PERSON>ichern", "Save and close": "Speichern und schließen", "Save as": "Speichern unter", "Save bundle as": "Bündel speichern als", "Save now": "Jetzt speichern", "Saving packages, please wait...": "Pakete werden gesichert, bitte warten...", "Scoop Installer - WingetUI": "Scoop-Installer – UniGetUI", "Scoop Uninstaller - WingetUI": "Scoop-Uninstaller – UniGetUI", "Scoop package": "Scoop-<PERSON><PERSON>", "Search": "<PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Nach Desktop-Software suchen, bei Updates benachrichtigen und keine komplizierten Sachen durchführen. Ich möchte einen einfachen <b>Software-Store</b> und nicht, dass UniGetUI zu kompliziert wird.", "Search for packages": "<PERSON><PERSON> suchen", "Search for packages to start": "<PERSON>ch <PERSON> suchen, um zu starten", "Search mode": "<PERSON><PERSON><PERSON>", "Search on available updates": "Suche nach verfügbaren Updates", "Search on your software": "Suche nach installierten Anwendungen", "Searching for installed packages...": "Suche nach installierten Paketen...", "Searching for packages...": "Suche nach Paketen...", "Searching for updates...": "Such<PERSON> nach Updates...", "Select": "Auswählen", "Select \"{item}\" to add your custom bucket": "<PERSON><PERSON><PERSON><PERSON> Sie „{item}“, um es dem benutzerdefinierten Bucket hinzuzufügen", "Select a folder": "Verzeichnis auswählen", "Select all": "Alle auswählen", "Select all packages": "Alle Pakete auswählen", "Select backup": "Sicherung auswählen", "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON> Sie die Option nur, <b>wenn <PERSON><PERSON> wissen, was <PERSON><PERSON> tun</b>.", "Select package file": "Paketdatei auswählen", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Wählen Sie die Sicherung aus, die Si<PERSON> öffnen möchten. Später können Sie überprüfen, welche Pakete/Programme Sie wiederherstellen möchten.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "W<PERSON>hlen Sie die Prozesse aus, die geschlossen werden sollen, bevor dieses <PERSON> installiert, aktualisiert oder deinstalliert wird.", "Select the source you want to add:": "W<PERSON><PERSON>en Sie die Quelle, die hinzugefügt werden soll:", "Select upgradable packages by default": "Pakete mit Updates automatisch auswählen", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> au<PERSON>, wel<PERSON> <b>Paketmanager</b> verwendet werden sollen ({0}). Konfigurieren Sie, wie Pakete installiert werden, wie Administratorrechte gehandhabt werden, usw.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake gesendet. Warte auf die Antwort der Instanz... ({0}%)", "Set a custom backup file name": "Einen benutzerdefinierten Dateinamen für Sicherungen festlegen", "Set custom backup file name": "Benutzerdefinierten Dateinamen für Sicherungen festlegen", "Settings": "Einstellungen", "Share": "Teilen", "Share WingetUI": "UniGetUI teilen", "Share anonymous usage data": "Anonyme Nutzungsdaten teilen", "Share this package": "<PERSON><PERSON> teilen", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Sollten Sie die Sicherheitseinstellungen ändern, müssen Sie das Bündel erneut öffnen, damit die Änderungen wirksam werden.", "Show UniGetUI on the system tray": "UniGetUI im Infobereich anzeigen", "Show UniGetUI's version and build number on the titlebar.": "UniGetUI-Version und Build-Nummer in der Titelleiste anzeigen", "Show WingetUI": "UniGetUI anzeigen", "Show a notification when an installation fails": "Benachrichtigung anzeigen, wenn eine Installation fehlschlägt", "Show a notification when an installation finishes successfully": "Benachrichtigung anzeigen, wenn eine Installation erfolgreich abgeschlossen wurde", "Show a notification when an operation fails": "Benachrichtigung anzeigen, wenn ein Vorgang fehlschlägt", "Show a notification when an operation finishes successfully": "Benachrichtigung anzeigen, wenn ein Vorgang erfolgreich abgeschlossen wurde", "Show a notification when there are available updates": "Benachrichti<PERSON>g anzeigen, wenn Updates verfügbar sind", "Show a silent notification when an operation is running": "Stille Benachrichtigung anzeigen, wenn ein Vorgang ausgeführt wird", "Show details": "Details anzeigen", "Show in explorer": "In Explorer anzeigen", "Show info about the package on the Updates tab": "Informationen über das Paket auf der Registerkarte „Updates“ anzeigen", "Show missing translation strings": "Fehlende Übersetzungen anzeigen", "Show notifications on different events": "Benachrichtigungen über verschiedene Ereignisse anzeigen", "Show package details": "Paketdetails anzeigen", "Show package icons on package lists": "Paket-Symbole in Paketlisten anzeigen", "Show similar packages": "Ähnliche Pakete anzeigen", "Show the live output": "Live-Ausgabe anzeigen", "Size": "Größe", "Skip": "Überspringen", "Skip hash check": "Hash-Prüfung überspringen", "Skip hash checks": "Hash-Prüfungen überspringen", "Skip integrity checks": "Integritätsprüfungen überspringen", "Skip minor updates for this package": "Kleinere Updates für dieses Paket überspringen", "Skip the hash check when installing the selected packages": "Hash-Prüfung bei der Installation der ausgewählten Pakete überspringen", "Skip the hash check when updating the selected packages": "Hash-Prüfung bei der Aktualisierung der ausgewählten Pakete überspringen", "Skip this version": "Diese Version überspringen", "Software Updates": "Software-Updates", "Something went wrong": "Etwas ist schiefgelaufen", "Something went wrong while launching the updater.": "<PERSON><PERSON>-Start lief etwas schief.", "Source": "<PERSON><PERSON>", "Source URL:": "URL der Quelle:", "Source added successfully": "<PERSON><PERSON> er<PERSON><PERSON>g<PERSON><PERSON> hinz<PERSON>gt", "Source addition failed": "Hinzufügen der Quelle fehlgeschlagen", "Source name:": "Name der Quelle:", "Source removal failed": "Entfernen der Quelle fehlgeschlagen", "Source removed successfully": "<PERSON><PERSON> erfolg<PERSON>ich entfernt", "Source:": "Quelle:", "Sources": "<PERSON><PERSON>", "Start": "Start", "Starting daemons...": "Daemons starten...", "Starting operation...": "Vorgang starten...", "Startup options": "Startoptionen", "Status": "Status", "Stuck here? Skip initialization": "Probleme? Initialisierung überspringen", "Success!": "Fertig!", "Suport the developer": "Unterstützen Sie den Entwickler", "Support me": "<PERSON><PERSON> un<PERSON>", "Support the developer": "Unterstützen Sie den Entwickler", "Systems are now ready to go!": "System ist bereit!", "Telemetry": "Telemetrie", "Text": "Text", "Text file": "Textdatei", "Thank you ❤": "Vielen Dank ❤", "Thank you 😉": "Vielen Dank 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Der Rust-Paketmanager.<br>Enthält: <b>Rust-Bibliotheken und in Rust geschriebene Programme</b>.", "The backup will NOT include any binary file nor any program's saved data.": "Die Sicherung enthält keine ausführbaren Dateien oder Programmdaten.", "The backup will be performed after login.": "Die Sicherung wird nach der Anmeldung ausgeführt.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Die Sicherung enthält eine vollständige Liste der installierten Pakete und deren Installationsoptionen. Ignorierte Updates und übersprungene Versionen werden ebenfalls gesichert.", "The bundle was created successfully on {0}": "Das Bündel wurde erfolgreich gespeichert unter {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON> Bündel, das Si<PERSON> versuchen zu laden, scheint ungültig zu sein. Bitte überprüfen Sie die Datei und versuchen Si<PERSON> es erneut.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Die Prüfsumme der Installationsdatei stimmt nicht mit dem erwarteten Wert überein und die Authentizität der Installationsdatei kann nicht verifiziert werden. Wenn Sie dem Herausgeber vertrauen, {0} das Paket erneut und überspringt die Hash-Prüfung.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Der klassische Paketmanager für Windows. Dort finden Sie alles. <br>Enthält: <b>Allgemeine Software</b>", "The cloud backup completed successfully.": "Die Cloud-Sicherung wurde erfolgreich abgeschlossen.", "The cloud backup has been loaded successfully.": "Die Cloud-Sicherung wurde erfolgreich geladen.", "The current bundle has no packages. Add some packages to get started": "Das aktuelle Bündel enthält keine Pakete. Fügen Sie ein paar Pakete hinzu, um zu beginnen.", "The executable file for {0} was not found": "Die ausführbare Datei für {0} wurde nicht gefunden.", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Die folgenden Optionen werden standardmäßig angewendet, wenn ein {0}-<PERSON><PERSON> installiert, aktualisiert oder deinstalliert wird.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Die folgenden Pakete werden in eine JSON-Datei exportiert. Es werden keine Benutzerdaten oder Programmdateien gesichert.", "The following packages are going to be installed on your system.": "Die folgenden Pakete werden auf Ihrem System installiert.", "The following settings may pose a security risk, hence they are disabled by default.": "Die folgenden Einstellungen können ein Sicherheitsrisiko darstellen und sind daher standardmäßig deaktiviert.", "The following settings will be applied each time this package is installed, updated or removed.": "Die folgenden Einstellungen werden jedes Mal angewendet, wenn dieses <PERSON> installiert, aktualisiert oder entfernt wird.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Die folgenden Einstellungen werden jedes Mal angewendet, wenn ein Paket installiert, aktualisiert oder entfernt wird. Diese werden automatisch gesichert.", "The icons and screenshots are maintained by users like you!": "Die Symbole und Screenshots werden von Nutzern wie Ihnen gepflegt!", "The installation script saved to {0}": "Das Installationsskript wurde gespeichert unter {0}", "The installer authenticity could not be verified.": "Die Echtheit der Installationsdatei konnte nicht überprüft werden.", "The installer has an invalid checksum": "Die Installationsdatei hat eine ungültige Prüfsumme", "The installer hash does not match the expected value.": "Der Hash der Installationsdatei stimmt nicht mit dem erwarteten Wert überein.", "The local icon cache currently takes {0} MB": "Der lokale Symbol-Cache belegt zur Zeit {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Das Hauptziel dieses Projekts ist das Erstellen einer intuitiven Bedienoberfläche zur Verwaltung der gängigsten CLI-Paketmanager für Windows, wie WinGet und Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Das Paket „{0}“ wurde nicht im Paketmanager „{1}“ gefunden", "The package bundle could not be created due to an error.": "Das Paketbündel konnte aufgrund eines Fehlers nicht erstellt werden.", "The package bundle is not valid": "Das Paketbündel ist ungültig", "The package manager \"{0}\" is disabled": "Paketmanager „{0}“ ist deaktiviert", "The package manager \"{0}\" was not found": "Paketmanager „{0}“ wurde nicht gefunden", "The package {0} from {1} was not found.": "<PERSON> Paket {0} von {1} wurde nicht gefunden.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Die hier aufgeführten Pakete werden bei der Suche nach Updates nicht berücksichtigt. Doppelklicken Sie auf die Updates oder klicken Sie auf die Schaltfläche rechts neben ihnen, um ihre Updates nicht mehr zu ignorieren.", "The selected packages have been blacklisted": "Die ausgewählten Pakete wurden auf die Blacklist gesetzt", "The settings will list, in their descriptions, the potential security issues they may have.": "In den Beschreibungen der Einstellungen werden deren potenzielle Sicherheitsprobleme aufgeführt.", "The size of the backup is estimated to be less than 1MB.": "Die voraussichtliche Größe der Sicherung beträgt weniger als 1 MB.", "The source {source} was added to {manager} successfully": "Die Quelle {source} wurde er<PERSON><PERSON>g<PERSON><PERSON> zu {manager} hinz<PERSON>fügt", "The source {source} was removed from {manager} successfully": "Die Quelle {source} wurde er<PERSON><PERSON><PERSON><PERSON><PERSON> von {manager} entfernt", "The system tray icon must be enabled in order for notifications to work": "Das Symbol im Infobereich muss aktiviert sein, damit die Benachrichtigungen funktionieren.", "The update process has been aborted.": "Der Update-Vorgang wurde abgebrochen.", "The update process will start after closing UniGetUI": "Das Update wird nach dem Beenden von UniGetUI durchgeführt.", "The update will be installed upon closing WingetUI": "Das Update wird nach dem Schließen von UniGetUI installiert", "The update will not continue.": "Das Update wird nicht fortgesetzt.", "The user has canceled {0}, that was a requirement for {1} to be run": "Der Benutzer hat {0} a<PERSON>gebro<PERSON>, das eine Voraussetzung für die Ausführung von {1} war.", "There are no new UniGetUI versions to be installed": "Es existieren keine neueren UniGetUI-Versionen", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Laufende Vorgänge gefunden. Wenn Sie UniGetUI beenden, können diese fehlschlagen. Möchten Si<PERSON> fortfahren?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Es existieren einige tolle Videos auf YouTube, die UniGetUI und seine Funktionen vorstellen. Sie können nützliche Tricks und Tipps entdecken!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Es existieren zwei Hauptgründe, UniGetUI nicht als Administrator auszuführen: Der erste Grund ist, dass der Scoop-Paketmanager Probleme mit einigen Befehlen verursachen kann, wenn er mit Administratorrechten ausgeführt wird. Der zweite Grund ist, dass die Ausführung von UniGetUI als Administrator bedeutet, dass jede<PERSON>, das <PERSON>e herunterladen, als Administrator ausgeführt wird (und das ist nicht sicher). <PERSON><PERSON>, das<PERSON> <PERSON><PERSON>, wenn Sie ein bestimmtes Paket als Administrator installieren müssen, jederzeit mit der rechten Maustaste auf das Element klicken können -> Als Administrator installieren/aktualisieren/deinstallieren.", "There is an error with the configuration of the package manager \"{0}\"": "<PERSON>s liegt ein <PERSON> in der Konfiguration des Paketmanagers „{0}“ vor", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "<PERSON>s läuft eine Installation. Wenn Sie UniGetUI schließen, könnte die Installation fehlschlagen und unerwartetes Verhalten auftreten. Möchten Sie UniGetUI trotzdem schließen?", "They are the programs in charge of installing, updating and removing packages.": "Das sind die Programme, die für die Installation, Aktualisierung und Deinstallation von Paketen zuständig sind.", "Third-party licenses": "Drittanbieterlizenzen", "This could represent a <b>security risk</b>.": "<PERSON>s kann ein <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b> da<PERSON><PERSON><PERSON>.", "This is not recommended.": "Dies ist nicht empfohlen.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Dies liegt wahr<PERSON><PERSON> da<PERSON>, dass das Paket, das <PERSON> gesendet haben, entfernt oder in einem Paketmanager veröffentlicht wurde, den <PERSON> nicht aktiviert haben. Die empfangene ID ist {0}", "This is the <b>default choice</b>.": "Dies ist die <b>Standardauswahl</b>.", "This may help if WinGet packages are not shown": "Dies kann hilfreich sein, wenn WinGet-Pakete nicht angezeigt werden", "This may help if no packages are listed": "Dies kann hilfreich sein, wenn keine Pakete aufgelistet sind", "This may take a minute or two": "Dies kann ein oder zwei Minuten dauern.", "This operation is running interactively.": "Dieser Vorgang wird interaktiv durchgeführt.", "This operation is running with administrator privileges.": "Dieser Vorgang wird mit Administratorrechten ausgeführt.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Diese Option WIRD Probleme verursachen. <PERSON><PERSON>, der nicht in der Lage ist, sich selbst zu erhöhen, WIRD FEHLSCHLAGEN. Installieren/Aktualisieren/Deinstallieren als Administrator wird NICHT FUNKTIONIEREN.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Dieses Paketbündel enthielt Einstellungen, die potenziell riskant sind und standardmäßig ignoriert werden können.", "This package can be updated": "<PERSON><PERSON> kann aktualisiert werden", "This package can be updated to version {0}": "<PERSON><PERSON> kann auf Version {0} aktualisiert werden", "This package can be upgraded to version {0}": "<PERSON><PERSON> kann auf Version {0} aktualisiert werden", "This package cannot be installed from an elevated context.": "Das Paket kann nicht mit erh<PERSON><PERSON><PERSON> (als Administrator) installiert werden.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Dieses <PERSON>et hat keine Screenshots oder es fehlt ein Symbol? Unterstützen Sie UniGetUI, indem Sie die fehlenden Symbole und Screenshots zu unserer offenen, öffentlichen Datenbank hinzufügen.", "This package is already installed": "Dieses <PERSON> wurde bereits installiert", "This package is being processed": "<PERSON><PERSON> wird verarbeitet", "This package is not available": "<PERSON><PERSON> ist nicht verfügbar", "This package is on the queue": "Die<PERSON>et befindet sich in der Warteschlange", "This process is running with administrator privileges": "Dieser Prozess wird mit Administratorrechten ausgeführt", "This project has no connection with the official {0} project — it's completely unofficial.": "Dieses Projekt hat keine Verbindung zum offiziellen {0}-Projekt – es ist völlig inoffiziell.", "This setting is disabled": "Diese Einstellung ist deaktiviert", "This wizard will help you configure and customize WingetUI!": "Dieser Assistent hilft Ihnen bei der Konfiguration und Anpassung von UniGetUI!", "Toggle search filters pane": "Suchfilterbereich umschalten", "Translators": "<PERSON>bers<PERSON>zer", "Try to kill the processes that refuse to close when requested to": "Prozesse nach Möglichkeit zwangsweise beenden, wenn sie sich nicht ordnungsgemäß schließen lassen.", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "<PERSON>n Si<PERSON> diese Option aktivieren, können Sie die ausführbare Datei ändern, die zur Interaktion mit Paketmanagern verwendet wird. Dies ermöglicht eine feinere Anpassung Ihrer Installationsprozesse, kann jedoch sicherheitsgefährdend sein.", "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON><PERSON>, durch Leerzeichen getrennt, den Namen und die URL der Quelle ein, die hinzugefügt werden soll.", "Unable to find package": "<PERSON>et kann nicht gefunden werden", "Unable to load informarion": "Information kann nicht geladen werden", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI sammelt anonyme Nutzungsdaten, um die Nutzererfahrung zu verbessern.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI sammelt anonyme Nutzungsdaten mit dem alleinigen Ziel, das Nutzererlebnis zu verstehen und zu verbessern.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI hat eine automatisch entfernbare Desktopverknüpfung gefunden.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI hat folgende Desktopverknüpfungen gefunden, die beim nächsten Update automatisch entfernt werden können", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI hat {0} automatisch entfernbare Desktopverknüpfungen gefunden.", "UniGetUI is being updated...": "UniGetUI wird aktualisiert...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI gehört zu keinem der kompatiblen Paketmanager. UniGetUI ist ein unabhängiges Projekt.", "UniGetUI on the background and system tray": "UniGetUI im Hintergrund und Infobereich", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI oder einige seiner Komponenten fehlen oder sind beschädigt.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI benötigt {0}, es konnte aber auf Ihrem System nicht gefunden werden.", "UniGetUI startup page:": "UniGetUI-Startseite:", "UniGetUI updater": "UniGetUI-Updater", "UniGetUI version {0} is being downloaded.": "UniGetUI-Version {0} wird heruntergeladen.", "UniGetUI {0} is ready to be installed.": "UniGetUI-Version {0} ist bereit zur Installation.", "Uninstall": "Deinstallieren", "Uninstall Scoop (and its packages)": "<PERSON><PERSON> deinstall<PERSON> (inkl. aller Pakete)", "Uninstall and more": "Deinstallieren und mehr", "Uninstall and remove data": "Deinstallieren und Daten entfernen", "Uninstall as administrator": "Als Administrator deinstallieren", "Uninstall canceled by the user!": "Deinstallation vom Benutzer abgebrochen!", "Uninstall failed": "Deinstallieren fehlgeschlagen", "Uninstall options": "Deinstallationsoptionen", "Uninstall package": "<PERSON><PERSON> deinstallieren", "Uninstall package, then reinstall it": "<PERSON><PERSON> dei<PERSON>, dann neu installieren", "Uninstall package, then update it": "<PERSON><PERSON>, dann aktualisieren", "Uninstall previous versions when updated": "Frühere Versionen nach der Aktualisierung deinstallieren", "Uninstall selected packages": "Ausgewählte Pakete deinstallieren", "Uninstall selection": "Auswahl deinstallieren", "Uninstall succeeded": "Erfolgreich deinstalliert", "Uninstall the selected packages with administrator privileges": "Ausgewählte Pakete mit Administratorrechten deinstallieren", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Deinstallierbare Pakete mit dem Ursprung „{0}“ sind bei keinem Paketmanager veröffentlicht, daher sind keine Informationen über sie verfügbar, die angezeigt werden könnten.", "Unknown": "Unbekannt", "Unknown size": "Unbekannte Größe", "Unset or unknown": "Nicht festgelegt oder unbekannt", "Up to date": "Aktuell", "Update": "Aktualisieren", "Update WingetUI automatically": "UniGetUI automatisch aktualisieren", "Update all": "Alles aktualisieren", "Update and more": "Aktualisieren und mehr", "Update as administrator": "Als Administrator aktualisieren", "Update check frequency, automatically install updates, etc.": "Update-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, automatische Updates usw.", "Update checking": "<PERSON>ch Updates suchen", "Update date": "Aktualisierungsdatum", "Update failed": "Update fehlgeschlagen", "Update found!": "Updates gefunden!", "Update now": "Jetzt aktualisieren", "Update options": "Aktualisierungsoptionen", "Update package indexes on launch": "Paketindizes beim Start aktualisieren", "Update packages automatically": "Pakete automatisch aktualisieren", "Update selected packages": "Ausgewählte Pakete aktualisieren", "Update selected packages with administrator privileges": "Ausgewählte Pakete mit Administratorrechten aktualisieren", "Update selection": "Auswahl aktualisieren", "Update succeeded": "Update er<PERSON><PERSON>g<PERSON>ich", "Update to version {0}": "Update auf Version {0}", "Update to {0} available": "Update auf {0} verfügbar", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Git-Portdateien von vcpkg automatisch aktualisieren (erfordert installiertes Git)", "Updates": "Updates", "Updates available!": "Updates verfügbar!", "Updates for this package are ignored": "Updates für dieses Paket werden ignoriert", "Updates found!": "Updates gefunden!", "Updates preferences": "Aktualisierungseinstellungen", "Updating WingetUI": "UniGetUI aktualisieren", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Verwenden Sie das mitgelieferte Legacy WinGet anstelle von PowerShell Cmdlets", "Use a custom icon and screenshot database URL": "Benutzerdefinierte Datenbank-URL für Symbole und Screenshots verwenden", "Use bundled WinGet instead of PowerShell CMDlets": "Mitgeliefertes WinGet anstelle von PowerShell Cmdlets verwenden", "Use bundled WinGet instead of system WinGet": "Mitgeliefertes WinGet anstelle des systeminternen WinGet verwenden", "Use installed GSudo instead of UniGetUI Elevator": "Installiertes GSudo anstelle des UniGetUI-Elevators verwenden", "Use installed GSudo instead of the bundled one": "Installiertes GSudo anstelle des mitgelieferten verwenden (Neustart erforderlich)", "Use system Chocolatey": "Systeminternes Chocolatey verwenden", "Use system Chocolatey (Needs a restart)": "Systeminternes Chocolatey verwenden (Neustart notwendig)", "Use system Winget (Needs a restart)": "Systeminternes WinGet verwenden (Neustart notwendig)", "Use system Winget (System language must be set to english)": "Systeminternes WinGet verwenden (Systemsprache muss auf Englisch festgelegt sein)", "Use the WinGet COM API to fetch packages": "Verwenden Sie die WinGet COM API zum Abrufen von Pak<PERSON>n", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Verwende das WinGet PowerShell Modul anstelle der WinGet COM API", "Useful links": "Nützliche Links", "User": "<PERSON><PERSON><PERSON>", "User interface preferences": "Bedienoberfläche", "User | Local": "Benutzer | Lokal", "Username": "<PERSON><PERSON><PERSON><PERSON>", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Die Verwendung von UniGetUI impliziert die Zustimmung zur GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Die Verwendung von UniGetUI impliziert die Zustimmung zur MIT-Lizenz", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Das Wurzelverzeichnis von Vcpkg wurde nicht gefunden. Bitte den Wert als Umgebungsvariable %VCPKG_ROOT% oder in den UniGetUI-Einstellungen setzen", "Vcpkg was not found on your system.": "Vcpkg wurde nicht auf dem System gefunden.", "Verbose": "Verbose", "Version": "Version", "Version to install:": "Zu installierende Version:", "Version:": "Version:", "View GitHub Profile": "GitHub-<PERSON><PERSON>", "View WingetUI on GitHub": "UniGetUI auf GitHub öffnen", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "<PERSON><PERSON> sich den Quellcode von UniGetUI an. Melden Sie hier Fehler oder schlagen Sie Funktionen vor, oder tragen Sie direkt zum UniGetUI-Projekt bei.", "View mode:": "Ansicht:", "View on UniGetUI": "Auf UniGetUI ansehen", "View page on browser": "Seite im Browser öffnen", "View {0} logs": "{0}-<PERSON><PERSON><PERSON> anzeigen", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Mit der Ausführung von Aufgaben, die eine Internetverbindung erfordern, warten, bis das Gerät mit dem Internet verbunden ist.", "Waiting for other installations to finish...": "Warte auf die Fertigstellung anderer Installationen...", "Waiting for {0} to complete...": "Auf die Fertigstellung von {0} warten...", "Warning": "<PERSON><PERSON><PERSON>", "Warning!": "Warnung!", "We are checking for updates.": "<PERSON>s wird nach Updates gesucht.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Es konnten keine detaillierten Informationen über dieses Paket geladen werden, da es in keiner Ihrer Paketquellen gefunden wurde.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Es konnten keine detaillierten Informationen über dieses Paket geladen werden, da es nicht von einem verfügbaren Paketmanager installiert wurde.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Es konnte {package} nicht {action}. Bitte versuchen Sie es später erneut. Klicken Sie auf „{showDetails}“, um die Protokolle der Installation zu erhalten.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Es konnte {package} nicht {action}. Bitte versuchen Sie es später erneut. Klicken Sie auf „{showDetails}“, um die Protokolle der Deinstallation zu erhalten.", "We couldn't find any package": "<PERSON>s konnten keine Pakete gefunden werden", "Welcome to WingetUI": "Willkommen bei UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Bei der Batch-Installation von Paketen aus einem Bündel auch Pakete installieren, die bereits installiert sind.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Wenn neue Verknüpfungen erkannt werden, diese automatisch löschen, anstatt diesen Dialog anzuzeigen.", "Which backup do you want to open?": "Welche Sicherung möchten Si<PERSON> öffnen?", "Which package managers do you want to use?": "Welcher Paketmanager soll verwendet werden?", "Which source do you want to add?": "Welche Quelle möchten Sie hinzufügen?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Während WinGet innerhalb von UniGetUI verwendet werden kann, kann UniGetUI auch mit anderen Paketmanagern verwendet werden, was verwi<PERSON>nd sein kann. In der Vergangenheit war UniGetUI darauf ausgerichtet, nur mit WinGet zu funktionieren, aber das ist nicht länger der Fall, sodass der Name „UniGetUI“ nicht mehr das repräsentiert, was dieses Projekt anstrebt.", "WinGet could not be repaired": "WinGet konnte nicht repariert werden", "WinGet malfunction detected": "WinGet-<PERSON><PERSON> er<PERSON>", "WinGet was repaired successfully": "WinGet wurde erfolgreich repariert", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI – Alles ist auf dem neuesten Stand", "WingetUI - {0} updates are available": "UniGetUI – {0} Updates sind verfügbar", "WingetUI - {0} {1}": "UniGetUI – {0} {1}", "WingetUI Homepage": "UniGetUI-Homepage", "WingetUI Homepage - Share this link!": "UniGetUI-Homepage – Teilen Sie diesen Link!", "WingetUI License": "UniGetUI-Lizenz", "WingetUI Log": "UniGetUI-Protokoll", "WingetUI Repository": "UniGetUI-Repository", "WingetUI Settings": "UniGetUI-Einstellungen", "WingetUI Settings File": "UniGetUI-Einstellungsdatei", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI verwendet die folgenden Bibliotheken, ohne die UniGetUI nicht realisierbar gewesen wäre.", "WingetUI Version {0}": "UniGetUI-Version {0}", "WingetUI autostart behaviour, application launch settings": "Einstellungen für den Anwendungsstart und Autostart", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI kann nach Updates für Ihre Programme suchen und diese, falls gewünscht, automatisch installieren", "WingetUI display language:": "UniGetUI-Anzeigesprache:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI wurde als Administrator ausgeführt, was nicht empfohlen wird. Wenn UniGetUI als Administrator ausgeführt wird, wird JED<PERSON> Vorgang in UniGetUI mit Administratorrechten ausgeführt. Sie können das Programm trotzdem verwenden, aber wir empfehlen dringend, UniGetUI nicht mit Administratorrechten auszuführen.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI wurde dank freiwilliger Übersetzer in mehr als 40 Sprachen übersetzt. Vielen Dank 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI wurde nicht maschinell übersetzt. Die folgenden Nutzer waren an den Übersetzungen beteiligt:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI ist eine Anwendung, die das Verwalten Ihrer Software vereinfacht, indem sie eine vollumfängliche Oberfläche für Ihre Kommandozeilen-Paketmanager bereitstellt.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI wird umbenannt, um eine bessere Differenzierung zwischen UniGetUI (die Oberfläche, die Sie gerade verwenden) und WinGet (ein von Microsoft entwickelter Paketmanager, mit dem ich nicht in Verbindung stehe) zu erreichen", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI wird aktualisiert. Nach Abschluss wird UniGetUI automatisch neu gestartet.", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI ist kostenlos und wird für immer kostenlos bleiben. Keine Werbung, keine Kreditkarten und keine Premiumversion. 100% kostenlos, für immer.", "WingetUI log": "UniGetUI-Protokoll", "WingetUI tray application preferences": "UniGetUI-Taskleistensymbol-Einstellungen", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI verwendet die folgenden Bibliotheken, ohne die UniGetUI nicht realisierbar gewesen wäre.", "WingetUI version {0} is being downloaded.": "UniGetUI-Version {0} wird heruntergeladen.", "WingetUI will become {newname} soon!": "WingetUI wird bald zu {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI wird nicht mehr regelmäßig nach Updates suchen. Beim Start wird immer noch danach gesucht, aber Sie werden nicht mehr gewarnt.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI wird jedes Mal eine Benutzerkontensteuerung-Eingabeaufforderung anzeigen, wenn für die Installation eines Pakets eine höhere Berechtigung erforderlich ist.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI wird bald in {newname} umbenannt. Dies wird keinen Einfluss auf die Funktionsweise der Anwendung haben. Ich (der Entwickler) werde die Entwicklung dieses Projekts wie bisher fortsetzen, nur unter einem anderen Namen.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI wäre ohne die Hilfe unserer lieben Mitwirkenden nicht möglich gewesen. Schauen Si<PERSON> sich auch deren GitHub-Profile an, UniGetUI wäre ohne diese Leute nicht möglich!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI wäre ohne die Hilfe der Mitwirkenden nicht möglich gewesen. Vielen Dank an alle 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI-Version {0} ist bereit zur Installation.", "Write here the process names here, separated by commas (,)": "<PERSON><PERSON><PERSON> Sie hier die Prozessnamen ein, getrennt durch Kommas (,)", "Yes": "<PERSON>a", "You are logged in as {0} (@{1})": "Sie sind angemeldet als {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "<PERSON><PERSON> können dieses Verhalten in den UniGetUI-Sicherheitseinstellungen ändern.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Sie können die Befehle festlegen, die vor oder nach der Installation, Aktualisierung oder Deinstallation dieses Pakets ausgeführt werden. Sie werden in einer Eingabeaufforderung ausgeführt, sodass CMD-Skripte hier funktionieren.", "You have currently version {0} installed": "Sie haben aktuell die Version {0} installiert", "You have installed WingetUI Version {0}": "Sie haben die UniGetUI-Version {0} installiert", "You may lose unsaved data": "<PERSON>e können nicht gespeicherte Daten verlieren", "You may need to install {pm} in order to use it with WingetUI.": "Sie müssen möglicherweise {pm} installieren, um es mit UniGetUI verwenden zu können.", "You may restart your computer later if you wish": "<PERSON>e können Ihren Computer bei Bedarf später neu starten", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Sie werden nur ein<PERSON> gef<PERSON>, und die Administratorrechte werden denjenigen Paketen gewährt, die sie anfordern.", "You will be prompted only once, and every future installation will be elevated automatically.": "Sie werden nur ein<PERSON> gef<PERSON>, und jede weitere Installation wird automatisch mit höherer Berechtigung ausgeführt.", "You will likely need to interact with the installer.": "<PERSON><PERSON> werden wahrscheinlich mit der Installation interagieren müssen.", "[RAN AS ADMINISTRATOR]": "ALS ADMINISTRATOR AUSGEFÜHRT", "buy me a coffee": "spendieren Si<PERSON> mir einen <PERSON>e", "extracted": "extrahiert", "feature": "Feature", "formerly WingetUI": "fr<PERSON><PERSON>", "homepage": "Website", "install": "installieren", "installation": "Installation", "installed": "installiert", "installing": "installiere", "library": "Bibliothek", "mandatory": "obligatorisch", "option": "Option", "optional": "optional", "uninstall": "deinstallieren", "uninstallation": "Deinstallation", "uninstalled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstalling": "deinstallieren", "update(noun)": "Update", "update(verb)": "aktualisieren", "updated": "aktual<PERSON><PERSON>", "updating": "aktualisiere", "version {0}": "Version {0}", "{0} Install options are currently locked because {0} follows the default install options.": "Die Installationsoptionen von {0} sind derzeit gesperrt, da {0} den Standardinstallationsoptionen folgt.", "{0} Uninstallation": "{0} Deinstallation", "{0} aborted": "{0} abgebrochen", "{0} can be updated": "{0} kann aktualisiert werden", "{0} can be updated to version {1}": "{0} kann auf Version {1} aktualisiert werden", "{0} days": "{0} <PERSON><PERSON>", "{0} desktop shortcuts created": "{0} Desktopverknüpfungen angelegt", "{0} failed": "{0} fehlgeschlagen", "{0} has been installed successfully.": "{0} wurde erfolgreich installiert.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} wurde erfolgreich installiert. Es wird empfohlen, UniGetUI neu zu starten, um die Installation abzuschließen.", "{0} has failed, that was a requirement for {1} to be run": "{0} ist fehlgeschlagen. Es war eine Voraussetzung für die Ausführung von {1}", "{0} homepage": "{0} Homepage", "{0} hours": "{0} St<PERSON><PERSON>", "{0} installation": "{0} Installation", "{0} installation options": "Installationsoptionen für {0}", "{0} installer is being downloaded": "{0}-Installations<PERSON>i wird heruntergeladen", "{0} is being installed": "{0} wird installiert", "{0} is being uninstalled": "{0} wird dei<PERSON><PERSON><PERSON>t", "{0} is being updated": "{0} wird aktual<PERSON>ert", "{0} is being updated to version {1}": "{0} wird auf Version {1} aktualisiert", "{0} is disabled": "{0} ist deaktiviert", "{0} minutes": "{0} Minuten", "{0} months": "{0} <PERSON><PERSON>", "{0} packages are being updated": "{0} Pakete werden aktualisiert", "{0} packages can be updated": "{0} Pakete können aktualisiert werden", "{0} packages found": "{0} Pakete gefunden", "{0} packages were found": "{0} Pakete wurden gefunden", "{0} packages were found, {1} of which match the specified filters.": "<PERSON><PERSON> wurden {0} <PERSON><PERSON> gef<PERSON>, von denen {1} den festgelegten Filtern entsprechen.", "{0} selected": "{0} ausgewählt", "{0} settings": "{0}-Einstellungen", "{0} status": "{0}-Status", "{0} succeeded": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} update": "{0} aktualisieren", "{0} updates are available": "{0} Updates verfügbar", "{0} was {1} successfully!": "{0} wurde erfolgreich {1}!", "{0} weeks": "{0} <PERSON><PERSON><PERSON>", "{0} years": "{0} J<PERSON>re", "{0} {1} failed": "{0} {1} ist fehlgeschlagen", "{package} Installation": "{package} Installation", "{package} Uninstall": "{package} Deinstallation", "{package} Update": "{package} Update", "{package} could not be installed": "{package} konnte nicht installiert werden", "{package} could not be uninstalled": "{package} konnte nicht deinstalliert werden", "{package} could not be updated": "{package} konnte nicht aktualisiert werden", "{package} installation failed": "Installation von {package} fehlgeschlagen", "{package} installer could not be downloaded": "{package}-Installationsdatei konnte nicht heruntergeladen werden", "{package} installer download": "{package}-Installations<PERSON>i wird heruntergeladen", "{package} installer was downloaded successfully": "{package}-Installationsdatei wurde erfolgreich heruntergeladen", "{package} uninstall failed": "Deinstallation von {package} fehlgeschlagen", "{package} update failed": "Update von {package} fehlgeschlagen", "{package} update failed. Click here for more details.": "Update von {package} fehlgeschlagen. Klicken Sie hier für weitere Informationen.", "{package} was installed successfully": "{package} wurde erfolgreich installiert", "{package} was uninstalled successfully": "{package} wurde erfolgreich deinstalliert", "{package} was updated successfully": "{package} wurde erfolgreich aktualisiert", "{pcName} installed packages": "{pcName} installierte Pakete", "{pm} could not be found": "{pm} konnte nicht gefunden werden", "{pm} found: {state}": "{pm} gefunden: {state}", "{pm} is disabled": "{pm} ist deaktiviert", "{pm} is enabled and ready to go": "{pm} ist aktiviert und bereit", "{pm} package manager specific preferences": "{pm} Paketmanager-spezifische Einstellungen", "{pm} preferences": "{pm}-Einstellungen", "{pm} version:": "{pm}-Version:", "{pm} was not found!": "{pm} wurde nicht gefunden"}