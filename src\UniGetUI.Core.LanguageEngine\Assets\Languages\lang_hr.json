{"\"{0}\" is a local package and can't be shared": "\"{0}\" je lokalni paket i ne može se dijeliti", "\"{0}\" is a local package and does not have available details": "\"{0}\" je lokalni paket i nema dostupnih detalja", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" je lokalni paket i nije kompatibilan s ovom značajkom", "(Last checked: {0})": "(Posljednja provjera: {0})", "(Number {0} in the queue)": "(<PERSON><PERSON><PERSON> {0} u redu čekanja)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON><PERSON><PERSON>, @AndrejFeher", "0 packages found": "0 pronađenih paketa", "0 updates found": "0 ažuriranja pronađeno", "1 - Errors": "1 - G<PERSON>ške", "1 day": "1 dan", "1 hour": "1 sat", "1 month": "1 mjesec", "1 package was found": "1 paket pronađen", "1 update is available": "Dostupno je 1 ažuriranje", "1 week": "1 tjedan", "1 year": "1 godina", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON> na stranicu \"{0}\" ili \"{1}\".", "2 - Warnings": "2 - Upozorenja", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Pronađite paket(e) koje želite dodati u skup paketa i označite njihov potvrdni okvir krajnje lijevo.", "3 - Information (less)": "3 - Informaci<PERSON> (manje)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. Kada su paketi koje želite dodati u skup paketa odabrani, pronađite i kliknite opciju \"{0}\" na alatnoj traci.", "4 - Information (more)": "4 - Inform<PERSON><PERSON><PERSON> (više)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON><PERSON><PERSON> paketi su dodani u skup paketa. Možete nastaviti dodavati pakete ili izvesti skup paketa.", "5 - information (debug)": "5 - Informacije (debugiranje)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Popularni upravitelj biblioteka za C/C++. Puni C/C++ biblioteka i drugih alata povezanih s C/C++.\nSadrži: C/C++ biblioteke i povezane alate.", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Repozitorij pun alata i izvršnih programa dizajniran sa Microsoft .NET ekosustavom u ideji. <br><PERSON><PERSON><PERSON><PERSON>: <b>.NET povezane alate i skripte</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Repozitorij pun alata dizajniranih sa Microsoft .NET ekosustavom u ideji.<br><PERSON><PERSON><PERSON><PERSON>: <b>.NET alate</b>", "A restart is required": "Potrebno je ponovno pokretanje", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "O aplikaciji", "About Qt6": "O Qt6", "About WingetUI": "O UniGetUI", "About WingetUI version {0}": "O UniGetUI verziji {0}", "About the dev": "O developeru", "Accept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "Radnja pri dvostrukom kliku na pakete, skrivanje uspješnih instalacija", "Add": "<PERSON><PERSON><PERSON>", "Add a source to {0}": "<PERSON><PERSON>j iz<PERSON> za {0}", "Add a timestamp to the backup file names": "Dodajte vremensku oznaku nazivima datoteka sigurnosnih kopija", "Add a timestamp to the backup files": "Dodajte vremensku oznaku datotekama sigurnosnih kopija", "Add packages or open an existing bundle": "Dodajte pakete ili otvorite postojeći skup paketa", "Add packages or open an existing package bundle": "Dodajte pakete ili otvorite postojeći skup paketa", "Add packages to bundle": "Dodaj pakete u skup paketa", "Add packages to start": "Dodajte pakete za početak", "Add selection to bundle": "<PERSON><PERSON><PERSON> odabir u skup paketa", "Add source": "<PERSON><PERSON>j <PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Dodajte ažuriranja koja ne uspiju s porukom 'nije pronađeno primjenjivo ažuriranje' na popis zanemarenih ažuriranja.", "Adding source {source}": "Dodavanje izvora {source}", "Adding source {source} to {manager}": "Dodavanje izvora {source} u {manager}", "Addition succeeded": "Dodavanje <PERSON>", "Administrator privileges": "Administratorske ovlasti", "Administrator privileges preferences": "Postavke administratorskih prava", "Administrator rights": "Administratorska prava", "Administrator rights and other dangerous settings": null, "Advanced options": "Napredne opcije", "All files": "<PERSON><PERSON> dato<PERSON>ke", "All versions": "Sve verzije", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Omogući paralelno izvođenje operacija paketa", "Allow parallel installs (NOT RECOMMENDED)": "Dopusti paralelne instalacije (NE PREPORUČUJE SE)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Omogući paralelno izvođenje {pm} operacija", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativno, {0} možete instalirati i pokretanjem sljedeće naredbe u Windows PowerShell:", "Always elevate {pm} installations by default": "Uvije<PERSON> povisi {pm} instalacije prema zadanim postavkama", "Always run {pm} operations with administrator rights": "Uvijek pokreni {pm} operacije s administratorskim pravima", "An error occurred": "<PERSON><PERSON><PERSON> je do pogreške", "An error occurred when adding the source: ": "Došlo je do pogreške prilikom dodavanja izvora: ", "An error occurred when attempting to show the package with Id {0}": "<PERSON><PERSON><PERSON> je do pogreške prilikom pokušaja prikaza paketa s ID-om {0}", "An error occurred when checking for updates: ": "Došlo je do pogreške prilikom provjere ažuriranja: ", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "<PERSON><PERSON><PERSON> je do pogreške prilikom obrade ovog paketa", "An error occurred:": "<PERSON><PERSON><PERSON> je do pogreške:", "An interal error occurred. Please view the log for further details.": "Došlo je do interne pogreške. Za više informacija pogledajte zapisnik.", "An unexpected error occurred:": "Doš<PERSON> je do neočekivane pogreške:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Doš<PERSON> je do neočekivanog problema prilikom pokušaja popravka WinGet-a. Pokušajte ponovno kasnije.", "An update was found!": "Pronađeno je ažuriranje!", "Android Subsystem": "Android podsustav", "Another source": "Drugi izvor", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Svi novi prečaci stvoreni tijekom instalacije ili ažuriranja bit će automatski izbrisani, bez prikazivanja upita za potvrdu pri prvom otkrivanju.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Svi prečaci kreirani ili izmijenjeni izvan UniGetUI-ja bit će ignorirani. <PERSON><PERSON>i ćete ih dodati putem gumba {0}.", "Any unsaved changes will be lost": "Sve nespremljene promjene bit će izgubljene", "App Name": "Naziv aplikacije", "Appearance": "<PERSON><PERSON><PERSON>", "Application theme, startup page, package icons, clear successful installs automatically": "Tema aplikacije, p<PERSON><PERSON><PERSON>na stranica, ikone paketa, automatsko brisanje uspješnih instalacija", "Application theme:": "Tema aplikacije:", "Apply": null, "Architecture to install:": "Arhitektura za instalaciju:", "Are these screenshots wron or blurry?": "Jesu li ove snimke zaslona pogrešne ili mutne?", "Are you really sure you want to enable this feature?": "Jeste li zaista sigurni da želite omogućiti ovu značajku?", "Are you sure you want to create a new package bundle? ": "Jeste li sigurni da želite stvoriti novi skup paketa?", "Are you sure you want to delete all shortcuts?": "Jeste li sigurni da želite izbrisati sve prečace?", "Are you sure?": "Jeste li sigurni?", "Ascendant": "Uzlazno", "Ask for administrator privileges once for each batch of operations": "Zatražite administratorske ovlasti jednom za svaku grupu operacija", "Ask for administrator rights when required": "Zatražite administratorska prava kada je potrebno", "Ask once or always for administrator rights, elevate installations by default": "Pitaj jednom ili uvijek za administratorska prava, povisite instalacije prema zadanim postavkama", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Pitaj samo jednom za administratorske privilegije (ne preporučuje se)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Pitaj za brisanje prečaca na radnoj površini stvorenih tijekom instalacije ili nadogradnje.", "Attention required": "Potrebna je pažnja", "Authenticate to the proxy with an user and a password": "Autentificirajte se na proxy korisničkim imenom i lozinkom.", "Author": "Autor", "Automatic desktop shortcut remover": null, "Automatically save a list of all your installed packages to easily restore them.": null, "Automatically save a list of your installed packages on your computer.": null, "Autostart WingetUI in the notifications area": "Automatski pokrenite WingetUI u području obavijesti", "Available Updates": null, "Available updates: {0}": "Dostupna ažuriranja: {0}", "Available updates: {0}, not finished yet...": "<PERSON><PERSON><PERSON><PERSON>: {0}, jo<PERSON>...", "Backing up packages to GitHub Gist...": null, "Backup": null, "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": null, "Backup location": null, "Become a contributor": null, "Become a translator": null, "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Beta značajke i druge opcije koje se ne smiju dirati", "Both": null, "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Ali evo drugih stvari koje možete učiniti kako biste saznali još više o WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": null, "Cache administrator rights and elevate installers by default": "Predmemoriraj administratorska prava i povisite programe za instalaciju prema zadanim postavkama", "Cache administrator rights, but elevate installers only when required": "Predmemoriraj administrators<PERSON> prava, ali povisite programe za instalaciju samo kada je to potrebno", "Cache was reset successfully!": "Predmemorija je uspješno poništena!", "Can't {0} {1}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} {1}", "Cancel": "Odustani", "Cancel all operations": null, "Change backup output directory": null, "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": null, "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": null, "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Povremeno prov<PERSON> ažuri<PERSON> paketa", "Check for updates": null, "Check for updates every:": "Provjerite ima li ažuriranja svakih:", "Check for updates periodically": "Povremeno provjeri ima li ažuriranja", "Check for updates regularly, and ask me what to do when updates are found.": "Redovito provjeravaj ažuriranja i pitaj me što učiniti kada se ažuriranja pronađu.", "Check for updates regularly, and automatically install available ones.": "Redovito provjeravajte ažuriranja i automatski instalirajte dostupna.", "Check out my {0} and my {1}!": "Pogledajte moj {0} i moj {1}!", "Check out some WingetUI overviews": "Pogledajte neke recenzije UniGetUI-ja", "Checking for other running instances...": "Provjera ostalih pokrenutih instanci...", "Checking for updates...": "Provjera ažuriranja...", "Checking found instace(s)...": "Provjera pronađenih instanci...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "<PERSON><PERSON><PERSON><PERSON>", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": null, "Clearing Scoop cache...": "<PERSON><PERSON><PERSON><PERSON> pred<PERSON>...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": null, "Close": "Zatvori", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Zatvorite WingetUI u području obavijesti", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": null, "Command-line to run:": null, "Compare query against": null, "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Informacije o komponenti", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": null, "Contribute to the icon and screenshot repository": "Doprinesite spremištu ikona i snimaka zaslona", "Contributors": "Surad<PERSON><PERSON>", "Copy": null, "Copy to clipboard": "Kopirati u međuspremnik", "Could not add source": null, "Could not add source {source} to {manager}": null, "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": null, "Could not load announcements - HTTP status code is $CODE": null, "Could not remove source": null, "Could not remove source {source} from {manager}": null, "Could not remove {source} from {manager}": null, "Credentials": null, "Current Version": "Trenutna verzija", "Current status: Not logged in": null, "Current user": "Trenutni korisnik", "Custom arguments:": null, "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Prilagođeni argumenti naredbenog retka:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Prilagodite WingetUI - samo za hakere i napredne korisnike", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ODRICANJE ODGOVORNOSTI: NE SNOSIMO ODGOVORNOST ZA PREUZETE PAKETE. <PERSON><PERSON><PERSON><PERSON> VAS INSTALIRAJTE SAMO POUZDANI SOFTVER.", "Dark": "Tamno", "Decline": null, "Default": "Zadano", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Zadane postavke - pogodne za obične korisnike", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "Opis:", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Programiranje je teško, ali ova je aplikacija besplatna. Ako vam se svidjela aplikacija, uvijek me možete <b>častiti kavom</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Izravna instalacija dvostrukim klikom na stavku na kartici \"{discoveryTab}\" (umjesto prikazivanja informacija o paketu)", "Disable new share API (port 7058)": "Onemogući API za novo dijeljenje (port 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": null, "Discover Packages": "Otk<PERSON>j<PERSON> pakete", "Discover packages": null, "Distinguish between\nuppercase and lowercase": null, "Distinguish between uppercase and lowercase": null, "Do NOT check for updates": "NEMOJ provjeravati ažuriranja", "Do an interactive install for the selected packages": "Izvrši interaktivnu instalaciju za odabrane pakete", "Do an interactive uninstall for the selected packages": "Izvrši interaktivnu deinstalaciju za odabrane pakete", "Do an interactive update for the selected packages": "Izvrši interaktivno ažuriranje za odabrane pakete", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Nemojte automatski preuzimati prijevode novih aplikacija s GitHuba", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": null, "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "Nemojte ažurirati indekse paketa pri pokretanju", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": null, "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Smatrate li WingetUI korisnim? Želite li podržati programera? Ako je tako, možete {0}, puno pomaže!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": null, "Do you really want to uninstall {0} packages?": "Želite li stvarno deinstalirati {0} pakete?", "Do you really want to uninstall {0}?": "Ž<PERSON>te li stvarno deinstalirati {0}?", "Do you want to restart your computer now?": "Želite li sada ponovno pokrenuti računalo?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Želite li prevesti WingetUI na svoj jezik? Pogledajte kako doprinijeti <a style=\"color:{0}\" href=\"{1}\"a>OVDJE!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": null, "Donate": "<PERSON><PERSON><PERSON><PERSON>", "Done!": null, "Download failed": null, "Download installer": null, "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": null, "Download updated language files from GitHub automatically": null, "Downloading": null, "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "Preuzimanje metapodataka paketa...", "Enable Scoop cleanup on launch": "Omogući Scoop čišćenje pri pokretanju", "Enable WingetUI notifications": "Omogući WingetUI obavijesti", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": null, "Enable it to install packages from {pm}.": null, "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "<PERSON><PERSON><PERSON><PERSON><PERSON> {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "Greška", "Everything is up to date": null, "Exact match": null, "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": null, "Experimental settings and developer options": "Eksperimentalne postavke i mogućnosti za programere", "Export": "Izvoz", "Export log as a file": "Izvoz dnevnika u datoteku", "Export packages": "<PERSON>zvoz paketa", "Export selected packages to a file": "Izvoz odabranih paketa u datoteku", "Export settings to a local file": "Izvezi postavke u lokalnu datoteku", "Export to a file": "Izvoz u datoteku", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": null, "Filters": null, "Finish": "Zav<PERSON>š<PERSON>", "Follow system color scheme": "<PERSON><PERSON><PERSON><PERSON><PERSON> shemu boja sustava", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": null, "Formerly known as WingetUI": null, "Found": "<PERSON><PERSON><PERSON><PERSON>", "Found packages: ": null, "Found packages: {0}": "<PERSON>nađ<PERSON> paketi: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON><PERSON> paketi: {0}, jo<PERSON> gotovi...", "General preferences": "<PERSON><PERSON><PERSON>", "GitHub profile": "GitHub profil", "Global": "Globalno", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "S<PERSON>jno skladište nepoznatih, ali korisnih uslužnih programa i drugih zanimljivih paketa.<br>Sad<PERSON><PERSON>i: <b>Us<PERSON>žne programe, programe naredbenog retka, op<PERSON><PERSON> softver (potrebno je dodatno spremište)</b>", "Great! You are on the latest version.": null, "Grid": null, "Help": null, "Help and documentation": null, "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, moje ime je Martí i ja sam <i>programer</i> WingetUI-ja. WingetUI je u potpunosti napravljen u moje slobodno vrijeme!", "Hide details": "<PERSON><PERSON><PERSON>", "Homepage": "Početna stranica", "Hooray! No updates were found.": "Hura! <PERSON>ema <PERSON>!", "How should installations that require administrator privileges be treated?": "<PERSON><PERSON> treba postupati s instalacijama koje zahtijevaju administratorske ovlasti?", "How to add packages to a bundle": null, "I understand": null, "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Ignorirajte buduća ažuriranja za ovaj paket", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "Ignorirajte odabrane pakete", "Ignore special characters": null, "Ignore updates for the selected packages": "<PERSON><PERSON><PERSON>ran<PERSON> za odabrane pakete", "Ignore updates for this package": "Ignorirajte ažuriranja za ovaj paket", "Ignored updates": "Zanemarena <PERSON>", "Ignored version": "Zanemarena ve<PERSON>", "Import": "Uvoz", "Import packages": "<PERSON><PERSON><PERSON> pakete", "Import packages from a file": "Uvoz paketa iz datoteke", "Import settings from a local file": "Uvezi postavke iz lokalne datoteke", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "Inicijalizacija WingetUI...", "Install": "Instalirati", "Install Scoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install and more": null, "Install and update preferences": null, "Install as administrator": "Instalirajte kao administrator", "Install available updates automatically": null, "Install location can't be changed for {0} packages": null, "Install location:": null, "Install options": null, "Install packages from a file": "Instalirajte pakete iz datoteke", "Install prerelease versions of UniGetUI": null, "Install selected packages": "Instalirajte odabrane pakete", "Install selected packages with administrator privileges": "Instaliraj odabrane pakete s <PERSON>kim ovlastima", "Install selection": null, "Install the latest prerelease version": null, "Install updates automatically": "Automatski instaliraj ažuriranja", "Install {0}": null, "Installation canceled by the user!": "Korisnik je otkazao instalaciju!", "Installation failed": null, "Installation options": "Mogućnosti instalacije", "Installation scope:": "Opseg instalacije:", "Installation succeeded": null, "Installed Packages": "<PERSON><PERSON><PERSON><PERSON> paketi", "Installed Version": "Instalirana verzija", "Installed packages": null, "Installer SHA256": "Instalater SHA256", "Installer SHA512": "Instalater SHA512", "Installer Type": "Vrsta instalatera", "Installer URL": "URL instalatera", "Installer not available": null, "Instance {0} responded, quitting...": "Instanca {0} je od<PERSON><PERSON>, odustajanje...", "Instant search": "Instant pretraga", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "Interaktivna instalacija", "Interactive operation": null, "Interactive uninstall": "Interaktivna deinstalacija", "Interactive update": "Interaktivno ažuriranje", "Internet connection settings": null, "Is this package missing the icon?": "Nedostaje li ovom paketu ikona?", "Is your language missing or incomplete?": null, "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Čini se da ste pokrenuli WingetUI kao administrator, što se ne preporučuje. I dalje možete koristiti program, ali toplo preporučujemo da ne pokrećete WingetUI s administratorskim ovlastima. Kliknite na \"{showDetails}\" da vidite zašto.", "Language": null, "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON>, tema i druge razne postavke", "Last updated:": "Zadnje ažuriranje:", "Latest": "Najnovije", "Latest Version": "Najnovija verzija", "Latest Version:": "Najnovija verzija:", "Latest details...": "<PERSON><PERSON><PERSON><PERSON>...", "Launching subprocess...": null, "Leave empty for default": null, "License": "Licenca", "Licenses": "Licence", "Light": "<PERSON><PERSON><PERSON><PERSON>", "List": null, "Live command-line output": "Izlaz naredbenog retka uživo", "Live output": null, "Loading UI components...": "Učitavanje komponenti korisničkog sučelja...", "Loading WingetUI...": "Učitavanje WingetUI...", "Loading packages": null, "Loading packages, please wait...": null, "Loading...": "Učitavanje...", "Local": "Lokalno", "Local PC": "Lokalno računalo", "Local backup advanced options": null, "Local machine": "Lokalni stroj", "Local package backup": null, "Locating {pm}...": "<PERSON><PERSON><PERSON><PERSON> {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": null, "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Traženje paketa...", "Machine | Global": null, "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": null, "Manage ignored packages": "Upravljanje z<PERSON>marenim paketima", "Manage ignored updates": "Upravljanje ignoriranim ažuriranjima", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": null, "Manifest": "Manifest", "Manifests": "<PERSON><PERSON><PERSON><PERSON>", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoftov službeni upravitelj paketa. Pun dobro poznatih i provjerenih paketa<br><PERSON><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON> soft<PERSON>, aplikacije Microsoft Storea</b>", "Missing dependency": null, "More": null, "More details": null, "More details about the shared data and how it will be processed": null, "More info": null, "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "<PERSON><PERSON>", "New": null, "New Version": "Nova verzija", "New bundle": null, "New version": "Nova verzija", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Ne", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "<PERSON><PERSON> pro<PERSON> p<PERSON>ta", "No packages found matching the input criteria": "<PERSON><PERSON> pronađen nijedan paket koji odgovara kriterijima unosa", "No packages have been added yet": null, "No packages selected": "<PERSON><PERSON> o<PERSON> paketa", "No packages were found": null, "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": null, "No sources found": null, "No sources were found": null, "No updates are available": "<PERSON><PERSON>", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS upravitelj paketa. Pun biblioteka i drugih uslužnih programa koji kruže svijetom Javascripta<br><PERSON><PERSON><PERSON><PERSON>: <b>Javascript biblioteke i druge srodne uslužne programe</b>", "Not available": "<PERSON><PERSON>", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "<PERSON><PERSON>", "Not right now": null, "Notes:": "Bilješke:", "Notification preferences": null, "Notification tray options": "Opcije područja obavijesti", "Notification types": null, "NuPkg (zipped manifest)": null, "OK": "U redu", "Ok": "U redu", "Open": "Otvori", "Open GitHub": "<PERSON><PERSON><PERSON><PERSON>", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": null, "Open backup location": null, "Open existing bundle": null, "Open install location": null, "Open the welcome wizard": "Otvori čarobnjaka dobrodošlice", "Operation canceled by user": null, "Operation cancelled": null, "Operation history": "Povijest operacija", "Operation in progress": null, "Operation on queue (position {0})...": null, "Operation profile:": null, "Options saved": null, "Order by:": null, "Other": null, "Other settings": null, "Package": null, "Package Bundles": null, "Package ID": "ID paketa", "Package Manager": null, "Package Manager logs": "Dnevnici upravitelja paketa", "Package Managers": null, "Package Name": "<PERSON><PERSON> paketa", "Package backup": null, "Package backup settings": null, "Package bundle": null, "Package details": "<PERSON><PERSON><PERSON> p<PERSON>", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "Postavke upravitelja paketa", "Package managers": null, "Package not found": null, "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "Paketi", "Packages found: {0}": null, "Partially": null, "Password": null, "Paste a valid URL to the database": null, "Pause updates for": null, "Perform a backup now": null, "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": null, "Periodically perform a backup of the installed packages": null, "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": null, "Please enter at least 3 characters": null, "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Imajte na umu da se određeni paketi možda neće moći instalirati zbog upravitelja paketa koji su omogućeni na ovom računalu.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Imajte na umu da se paketi iz određenih izvora možda neće moći izvoziti. Zasivljeni su i neće se izvoziti.", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": null, "Please select how you want to configure WingetUI": "Odaberite kako želite konfigurirati WingetUI", "Please try again later": null, "Please type at least two characters": "Unesite najmanje dva znaka", "Please wait": null, "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "Molimo pričekajte...", "Portable": null, "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": null, "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": null, "Preparing packages, please wait...": null, "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "Da<PERSON> objave:", "Publisher": "<PERSON><PERSON><PERSON>č", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python upravitelj paketa. Pun python biblioteka i drugih uslužnih programa povezanih s Pythonom<br><PERSON><PERSON><PERSON><PERSON>: <b>Python biblioteke i srodnih uslužnih programa</b>", "Quit": "Zatvori", "Quit WingetUI": null, "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": null, "Related settings": null, "Release notes": null, "Release notes URL": null, "Release notes URL:": "URL napomena o izdanju:", "Release notes:": "Napomene o izdanju:", "Reload": "Ponovno učitati", "Reload log": "Ponovno učitaj dnevnik", "Removal failed": null, "Removal succeeded": null, "Remove from list": null, "Remove permanent data": "Ukloni trajne podatke", "Remove selection from bundle": null, "Remove successful installs/uninstalls/updates from the installation list": "Uklonite uspješne instalacije/deinstalacije/a<PERSON><PERSON>ran<PERSON> s popisa instalacija", "Removing source {source}": null, "Removing source {source} from {manager}": null, "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": null, "Repository": "<PERSON>oz<PERSON><PERSON><PERSON>", "Reset": "<PERSON><PERSON><PERSON><PERSON>", "Reset Scoop's global app cache": "Resetirajte globalnu predmemoriju aplikacije <PERSON>", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "Poništi Winget izvore (moglo bi pomoći ako nijedan paket nije naveden)", "Reset WingetUI": "<PERSON><PERSON><PERSON><PERSON>", "Reset WingetUI and its preferences": "Resetirajte WingetUI i njegove postavke", "Reset WingetUI icon and screenshot cache": "Resetirajte ikonu WingetUI i predmemoriju snimaka zaslona", "Reset list": null, "Resetting Winget sources - WingetUI": null, "Restart": null, "Restart UniGetUI": null, "Restart WingetUI": "Ponovno pokrenite WingetUI", "Restart WingetUI to fully apply changes": null, "Restart later": "Ponovno pokretanje kasnije", "Restart now": "Ponovno pokreni sada", "Restart required": "Potrebno ponovno pokretanje", "Restart your PC to finish installation": "Ponovno pokrenite računalo kako biste dovršili instalaciju", "Restart your computer to finish the installation": "Ponovno pokrenite računalo kako biste dovršili instalaciju", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Pokušaj ponovo", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": null, "Return to top": "Povratak na vrh", "Run": null, "Run as admin": "Pokreni kao administrator", "Run cleanup and clear cache": null, "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "Pokretanje instalacijskog programa...", "Running the uninstaller...": "Pokretanje programa za deinstalaciju...", "Running the updater...": "Pokretanje programa za ažuriranje...", "Save": null, "Save File": "<PERSON><PERSON><PERSON><PERSON> datoteku", "Save and close": null, "Save as": null, "Save bundle as": null, "Save now": null, "Saving packages, please wait...": null, "Scoop Installer - WingetUI": null, "Scoop Uninstaller - WingetUI": null, "Scoop package": "<PERSON><PERSON> paket", "Search": "Pretraga", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Traži softver za stolna računala, upozori me kada su dostupna ažuriranja i nemoj raditi štreberske stvari. Ne želim da WingetUI prekomplicira, samo želi<PERSON> j<PERSON>nu <b>trgovinu softvera</b>", "Search for packages": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON>", "Search for packages to start": "Za početak, potražite pakete", "Search mode": null, "Search on available updates": "Pretražite dostupna ažuriranja", "Search on your software": "Pretražite svoj softver", "Searching for installed packages...": "Traženje instaliranih paketa...", "Searching for packages...": "Traženje paketa...", "Searching for updates...": "Traženje ažuriranja...", "Select": null, "Select \"{item}\" to add your custom bucket": "Odaberite \"{item}\" da dodate svoj prilagođeni spremnik", "Select a folder": null, "Select all": "Odaberi sve", "Select all packages": "Odaberite sve pakete", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "Odaberite samo <b>ako znate što radite</b>.", "Select package file": "Odaberite datoteku paketa", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": null, "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Odaberite koje <b>upravitelje paketa</b> k<PERSON><PERSON><PERSON> ({0}), konfigurirajte kako se paketi instaliraju, upravljajte načinom na koji se rukuje administratorskim pravima itd.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Poslano rukovanje. Čeka se na odgovor slušatelja... ({0}%)", "Set a custom backup file name": null, "Set custom backup file name": null, "Settings": null, "Share": null, "Share WingetUI": null, "Share anonymous usage data": null, "Share this package": "Podijelite ovaj paket", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": null, "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "Prikaži WingetUI", "Show a notification when an installation fails": "Prikaži obavijest kada instalacija ne uspije", "Show a notification when an installation finishes successfully": "Prikaži obavijest kada instalacija uspješno završi", "Show a notification when an operation fails": null, "Show a notification when an operation finishes successfully": null, "Show a notification when there are available updates": "Prikaži obavijest kada postoje dostupna ažuriranja", "Show a silent notification when an operation is running": null, "Show details": "Pokaži detalje", "Show in explorer": null, "Show info about the package on the Updates tab": "Prikaži informacije o paketu na kartici Ažuriranja", "Show missing translation strings": "Prikaži nedostajuće nizove prijevoda", "Show notifications on different events": null, "Show package details": "P<PERSON><PERSON><PERSON> paketa", "Show package icons on package lists": null, "Show similar packages": null, "Show the live output": "Prikažite dnevnik uživo", "Size": null, "Skip": "Preskoči", "Skip hash check": "Preskoči provjeru hasha", "Skip hash checks": null, "Skip integrity checks": null, "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "Preskoči provjeru hasha prilikom instaliranja odabranih paketa", "Skip the hash check when updating the selected packages": "Preskočite hash provjeru kada až<PERSON>rate odabrane pakete", "Skip this version": "Preskoči ovu verziju", "Software Updates": "Ažuriranja soft<PERSON>a", "Something went wrong": null, "Something went wrong while launching the updater.": null, "Source": "<PERSON><PERSON><PERSON>", "Source URL:": null, "Source added successfully": null, "Source addition failed": null, "Source name:": null, "Source removal failed": null, "Source removed successfully": null, "Source:": "Izvor:", "Sources": null, "Start": "<PERSON><PERSON><PERSON>", "Starting daemons...": "Pokretanje servisa...", "Starting operation...": null, "Startup options": "Mogućnosti pokretanja", "Status": "Status", "Stuck here? Skip initialization": "Zapeli ste ovdje? Preskoči inicijalizaciju", "Suport the developer": "Podržite programera", "Support me": null, "Support the developer": null, "Systems are now ready to go!": "Su<PERSON>vi su sada spremni za rad!", "Telemetry": null, "Text": null, "Text file": "Tekstna datoteka", "Thank you ❤": null, "Thank you 😉": null, "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": null, "The backup will be performed after login.": null, "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Kontrolni zbroj instalacijskog programa ne podudara se s očekivanom vrijednošću, a autentičnost instalacijskog programa ne može se provjeriti. Ako vjerujete izdavaču, {0} paket ponovno zaobilazeći provjeru hasha.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Klasični upravitelj paketa za Windows. Sve ćete tamo pronaći. <br><PERSON><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON> softver</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Sljedeći paketi će se izvesti u JSON datoteku. Nikakvi korisnički podaci ili binarne datoteke neće biti spremljene.", "The following packages are going to be installed on your system.": "Sljedeći paketi bit će instalirani na vašem sustavu.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": null, "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": null, "The icons and screenshots are maintained by users like you!": "Ikone i snimke zaslona održavaju korisnici poput vas!", "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "Instalacijski program ima nevažeći kontrolni zbroj", "The installer hash does not match the expected value.": null, "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Glavni cilj ovog projekta je stvoriti intuitivno korisničko sučelje za najčešće CLI upravitelje paketa za Windows, kao što su Winget i Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": null, "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Ov<PERSON><PERSON> navedeni paketi neće biti uzeti u obzir prilikom provjere ažuriranja. Dvaput kliknite na njih ili kliknite gumb s njihove desne strane da prestanete ignorirati njihova ažuriranja.", "The selected packages have been blacklisted": null, "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": null, "The source {source} was added to {manager} successfully": null, "The source {source} was removed from {manager} successfully": null, "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": null, "The update will not continue.": null, "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": null, "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Na YouTubeu ima nekoliko izvrsnih videozapisa koji prikazuju WingetUI i njegove mogućnosti. Mogli biste naučiti korisne trikove i savjete!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Postoje dva glavna razloga zašto ne pokretati WingetUI kao administrator: <PERSON><PERSON><PERSON> je taj što Scoop upravitelj paketa može uzrokovati probleme s nekim naredbama kada se pokreće s administratorskim pravima. Drugi je da pokretanje WingetUI-a kao administratora znači da će svaki paket koji preuzmete biti pokrenut kao administrator (a to nije sigurno). Imajte na umu da ako trebate instalirati određeni paket kao administrator, uvijek možete desnom tipkom miša kliknuti stavku -> Instaliraj/Ažuriraj/Deinstaliraj kao administrator.", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "U tijeku je instalacija. Ako zatvorite WingetUI, instalacija može biti neuspješna i imati neočekivane rezultate. Još uvijek želite napustiti WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "Oni su programi zaduženi za instaliranje, ažuriranje i uklanjanje paketa.", "Third-party licenses": null, "This could represent a <b>security risk</b>.": "To bi moglo predstavljati <b>sigurnosni rizik</b>.", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "To je vjerojatno zbog činjenice da je paket koji vam je poslan uklonjen ili objavljen na upravitelju paketa koji niste omogućili. Primljeni ID je {0}", "This is the <b>default choice</b>.": "<PERSON><PERSON> je <b>z<PERSON><PERSON></b>.", "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": null, "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": null, "This package can be updated to version {0}": null, "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": null, "This package is already installed": null, "This package is being processed": null, "This package is not available": null, "This package is on the queue": null, "This process is running with administrator privileges": "Ovaj proces se izvodi s administratorskim ovlastima", "This project has no connection with the official {0} project — it's completely unofficial.": null, "This setting is disabled": null, "This wizard will help you configure and customize WingetUI!": "Ovaj čarobnjak će vam pomoći konfigurirati i prilagoditi WingetUI!", "Toggle search filters pane": null, "Translators": "Prevoditelji", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": null, "Unable to find package": "<PERSON><PERSON> mogu<PERSON>e pronaći paket", "Unable to load informarion": "Nije mogu<PERSON>e učitati informacije", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": null, "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (i njegove pakete)", "Uninstall and more": null, "Uninstall and remove data": null, "Uninstall as administrator": "<PERSON><PERSON><PERSON><PERSON><PERSON> kao administrator", "Uninstall canceled by the user!": "Deinstalaciju je otkazao korisnik!", "Uninstall failed": null, "Uninstall options": null, "Uninstall package": "<PERSON><PERSON><PERSON><PERSON><PERSON> paket", "Uninstall package, then reinstall it": null, "Uninstall package, then update it": null, "Uninstall previous versions when updated": null, "Uninstall selected packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> oda<PERSON> pakete", "Uninstall selection": null, "Uninstall succeeded": null, "Uninstall the selected packages with administrator privileges": "Deinstalirajte odabrane pakete s <PERSON>kim ovlastima", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Paketi koji se mogu deinstalirati s izvorom navedenim kao \"{0}\" nisu objavljeni ni na jednom upravitelju paketa, tako da nema dostupnih informacija za prikaz o njima.", "Unknown": "Nepoznato", "Unknown size": null, "Unset or unknown": null, "Up to date": null, "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Ažurirajte WingetUI automatski", "Update all": "Ažuriraj sve", "Update and more": null, "Update as administrator": "Ažurirajte kao administrator", "Update check frequency, automatically install updates, etc.": null, "Update date": "<PERSON><PERSON>", "Update failed": null, "Update found!": "Ažuriranje pronađeno!", "Update now": null, "Update options": null, "Update package indexes on launch": null, "Update packages automatically": "Automatski ažurirajte pakete", "Update selected packages": "Ažurirajte odabrane pakete", "Update selected packages with administrator privileges": "Ažurirajte odabrane pakete s administratorskim ovlastima", "Update selection": null, "Update succeeded": null, "Update to version {0}": null, "Update to {0} available": "Dostupno ažuriranje na {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "Ažuriranja", "Updates available!": null, "Updates for this package are ignored": null, "Updates found!": "Pronađena ažuriranja!", "Updates preferences": null, "Updating WingetUI": "Ažuriranje WingetUI", "Url": null, "Use Legacy bundled WinGet instead of PowerShell CMDLets": null, "Use a custom icon and screenshot database URL": null, "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Koristite instalirani GSudo umjesto onog iz programa (zahtijeva ponovno pokretanje aplikacije)", "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": "<PERSON><PERSON><PERSON> (potrebno je ponovno pokretanje)", "Use system Winget (Needs a restart)": "<PERSON><PERSON><PERSON> (potrebno je ponovno pokretanje)", "Use system Winget (System language must be set to english)": null, "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": null, "User": "<PERSON><PERSON><PERSON>", "User interface preferences": "Postavke korisničkog sučelja", "User | Local": null, "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": null, "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": "Verzija", "Version to install:": "Verzija za instaliranje:", "Version:": null, "View GitHub Profile": null, "View WingetUI on GitHub": "Pogledajte WingetUI na GitHubu", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Pogledajte WingetUI izvorni kod. Tamo možete prijaviti bugove ili predložiti značajke ili događaje koji izravno doprinose projektu WingetUI", "View mode:": null, "View on UniGetUI": null, "View page on browser": null, "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "Čeka se završetak ostalih instalacija...", "Waiting for {0} to complete...": null, "Warning": "Upozorenje", "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": "Nismo mogli učitati detaljne informacije o ovom paketu jer nije pronađen ni u jednom od vaših izvora paketa.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Nemoguće učitati detaljne informacije o ovom paketu jer nije instaliran iz dostupnog upravitelja paketa.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Nemoguće {action} {package}. Molimo pokušajte ponovo kasnije. Kliknite na \"{showDetails}\" da biste dobili zapise od instalatera.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Nemoguće {action} {package}. Molimo pokušajte ponovo kasnije. Kliknite na \"{showDetails}\" da biste dobili zapise iz programa za deinstalaciju.", "We couldn't find any package": null, "Welcome to WingetUI": "Dobrodošli u WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Koje upravitelje paketa želite koristiti?", "Which source do you want to add?": null, "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": null, "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": null, "WingetUI - Everything is up to date": "WingetUI - S<PERSON> je <PERSON>", "WingetUI - {0} updates are available": "WingetUI - dostupna su ažuriranja za {0}", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": null, "WingetUI Log": null, "WingetUI Repository": null, "WingetUI Settings": "WingetUI postavke", "WingetUI Settings File": "WingetUI datoteka postavki", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": null, "WingetUI autostart behaviour, application launch settings": "Ponašanje automatskog pokretanja WingetUI, postavke pokretanja aplikacije", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI može provjeriti ima li vaš softver dostupna ažuriranja i automatski ih instalirati ako želite", "WingetUI display language:": "WingetUI jezik prikaza:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": null, "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": null, "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI nije strojno preveden! Za prijevode su bili zaduženi sljedeći korisnici:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI se ažurira. <PERSON><PERSON>, WingetUI će se sam ponovno pokrenuti", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": null, "WingetUI log": "Dnevnik WingetUI", "WingetUI tray application preferences": "WingetUI postavke područja obavijesti", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": null, "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI neće povremeno provjeravati ažuriranja. I dalje će se provjeravati pri pokretanju, ali nećete biti upozoreni na njih.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI će prikazati upit UAC-a svaki put kada paket zahtijeva podizanje razine za instaliranje.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI ne bi bio moguć bez pomoći naših dragih suradnika. Provjerite njihove GitHub profile, WingetUI ne bi bio moguć bez njih!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": null, "WingetUI {0} is ready to be installed.": null, "Write here the process names here, separated by commas (,)": null, "Yes": "Da", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": null, "You have installed WingetUI Version {0}": null, "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": "Možete kasnije ponovno pokrenuti računalo ako želite", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Bit ćete upitani samo jednom, a administratorska prava bit će dodijeljena paketima koji ih zatraže.", "You will be prompted only once, and every future installation will be elevated automatically.": "Od vas će se to zatražiti samo jednom, a svaka buduća instalacija bit će automatski podignuta.", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": "plati mi kavu", "extracted": null, "feature": null, "formerly WingetUI": null, "homepage": "web stranica", "install": "instalirati", "installation": "instalacija", "installed": "instalirano", "installing": "instaliranje", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "deinstalirati", "uninstallation": "deinstalacija", "uninstalled": "deinstalirano", "uninstalling": "deinstaliranje", "update(noun)": "ažuriranje", "update(verb)": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON>", "updating": "ažuriranje", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} aborted": "{0} prekinuto", "{0} can be updated": "{0} se može a<PERSON>i", "{0} can be updated to version {1}": null, "{0} days": "{0} dana", "{0} desktop shortcuts created": null, "{0} failed": "{0} nije <PERSON>", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} sati", "{0} installation": "{0} instalac<PERSON>", "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} se <PERSON><PERSON><PERSON><PERSON>", "{0} is being updated to version {1}": null, "{0} is disabled": "{0} je <PERSON><PERSON><PERSON>", "{0} minutes": "{0} minuta", "{0} months": null, "{0} packages are being updated": "{0} pake<PERSON> se a<PERSON>", "{0} packages can be updated": "{0} paketa se može ažurirati", "{0} packages found": "{0} pro<PERSON><PERSON><PERSON><PERSON> p<PERSON>ta", "{0} packages were found": "{0} pro<PERSON><PERSON><PERSON><PERSON> p<PERSON>ta", "{0} packages were found, {1} of which match the specified filters.": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} update": "{0} ažuriranje", "{0} updates are available": "{0} ažuri<PERSON><PERSON> je <PERSON>", "{0} was {1} successfully!": "{0} je {1} us<PERSON><PERSON><PERSON><PERSON>!", "{0} weeks": null, "{0} years": null, "{0} {1} failed": "{0} {1} nije <PERSON><PERSON>", "{package} Installation": "{package} Instalacija", "{package} Uninstall": "{package} Deinstaliranje", "{package} Update": "{package} Ažuriranje", "{package} could not be installed": "{package} nije mogu<PERSON>e instalirati", "{package} could not be uninstalled": "{package} nije moguće deinstalirati", "{package} could not be updated": "{package} nije mogu<PERSON> ažuri<PERSON>", "{package} installation failed": "{package} neuspješna instalacija", "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": "{package} deisntaliranje <PERSON>", "{package} update failed": "{package} a<PERSON>uri<PERSON><PERSON>", "{package} update failed. Click here for more details.": "{package} ažuriranje neuspješno. Kliknite ovdje za više detalja.", "{package} was installed successfully": "{package} je usp<PERSON><PERSON><PERSON> instaliran", "{package} was uninstalled successfully": "{package} je uspje<PERSON>no deinstaliran", "{package} was updated successfully": "{package} je us<PERSON><PERSON><PERSON><PERSON> ", "{pcName} installed packages": "{pcName} instal<PERSON><PERSON> paketi", "{pm} could not be found": "{pm} nije mogu<PERSON>e pronaći", "{pm} found: {state}": "{pm} pronađeno: {state}", "{pm} is disabled": "{pm} je <PERSON><PERSON><PERSON>", "{pm} is enabled and ready to go": "{pm} je omogu<PERSON>en i spreman za ići", "{pm} package manager specific preferences": "{pm} specifične postavke upravitelja paketa", "{pm} preferences": "{pm} postavke", "{pm} version:": "{pm} verzija:", "{pm} was not found!": "{pm} nije pronađen!"}