{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(Sidst tjekket: {0})", "(Number {0} in the queue)": "(Nummer {0} i køen)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@mik<PERSON><PERSON><PERSON>, @yrjarv, @AAUCrisp, @siewers, @bstordrup", "0 packages found": "0 pakker fundet", "0 updates found": "0 opdateringer fundet", "1 - Errors": "1 fejl", "1 day": "1 dag", "1 hour": "1 time", "1 month": "1 måned", "1 package was found": "1 pakke fundet", "1 update is available": "1 opdatering tilgængelig", "1 week": "1 uge", "1 year": "1 år", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON> til \"{0}\" eller \"{1}\" siden.", "2 - Warnings": "2 - <PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON> <PERSON>(n) p<PERSON><PERSON>(r), du <PERSON><PERSON><PERSON> at tilføje til samlingen og vælg checkboksen længst til venstre.", "3 - Information (less)": "3 - Information (mindre)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON><PERSON>, du <PERSON><PERSON><PERSON> at tilføje til samlingen, er valgt, finder du indstillingen \"{0}\" på værktøjslinjen og klikker på den.", "4 - Information (more)": "4 - Information (mere)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON> pakker vil være tilføjet til samlingen. Du kan fortsætte med at tilføje pakker eller eksportere samlingen.", "5 - information (debug)": "5 - Information (fejlsøgning)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "En populær C/C++ biblioteksmanager. Fuld af C/C++ biblioteker og andre C/C++ relaterede værktøjer<br>Indeholder: <b>C/C++ biblioteker og relaterede værktøjer</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "En samling af værktøjer og programmer til brug i Microsoft's .NET økosystem.<br>Indeholder <b>.NET relaterede værktøjer og scripts</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "En samling af værktøjer til brug i Microsoft's .NET økosystem.<br>Indeholder <b>.NET relaterede værktøjer</b>", "A restart is required": "Genstart af WingetUI påkrævet", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "Om", "About Qt6": "Om Qt6", "About WingetUI": "Om UniGetUI", "About WingetUI version {0}": "Om UniGetUI version {0}", "About the dev": "<PERSON><PERSON>", "Accept": "Accepter", "Action when double-clicking packages, hide successful installations": "Skjul gennemførte installationer ved dobbeltklik", "Add": "Tilføj", "Add a source to {0}": "<PERSON><PERSON><PERSON><PERSON><PERSON> kilde til {0}", "Add a timestamp to the backup file names": "<PERSON><PERSON><PERSON><PERSON><PERSON> tidsstempel til backup filnavne", "Add a timestamp to the backup files": "<PERSON><PERSON><PERSON><PERSON><PERSON> tidsstempel til backup filerne", "Add packages or open an existing bundle": "Til<PERSON>øj pakker eller åben en eksisterende samling", "Add packages or open an existing package bundle": "Tilføj pakker eller åben en eksisterende pakke samling", "Add packages to bundle": "Tilføj pakker til samling", "Add packages to start": "<PERSON><PERSON><PERSON><PERSON><PERSON> pakker til start", "Add selection to bundle": "Tilføj til samling", "Add source": "<PERSON><PERSON><PERSON><PERSON><PERSON> kilde", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "<PERSON><PERSON><PERSON><PERSON><PERSON> opdate<PERSON><PERSON>, som mislykkes med en 'ingen relevante opdateriinger fundet' til listen over ignore<PERSON>e opdateringer", "Adding source {source}": "<PERSON>il<PERSON><PERSON><PERSON> kilden {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON><PERSON><PERSON> kilde {source} til {manager}", "Addition succeeded": "Tilføjelse gennemført", "Administrator privileges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Administrator privileges preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Administrator rights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "Alle filer", "All versions": "Alle versioner", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "<PERSON><PERSON> pakke handlinger at blive gjort parallelt", "Allow parallel installs (NOT RECOMMENDED)": "Tillad installation af flere på samme tid (ANBEFALES IKKE)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Tillad {pm} operationer på samme tid", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternative kan du også installere {0} ved at afvikle følgende kommando i en Windows PowerShell prompt:", "Always elevate {pm} installations by default": "<PERSON><PERSON> kør {pm} installationer med <PERSON><PERSON><PERSON><PERSON><PERSON>r", "Always run {pm} operations with administrator rights": "<PERSON><PERSON> kør {pm} operationer med administrator rettigh<PERSON>r", "An error occurred": "Der skete en fejl", "An error occurred when adding the source: ": "Der skete en fejl under tilføjelsen af kilden:", "An error occurred when attempting to show the package with Id {0}": "Der opstod en fejl under forsøget på at vise pakken med Id {0}", "An error occurred when checking for updates: ": "Der skete en fejl under sø<PERSON><PERSON> efter opdateringer:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "Der skete en fejl ved kørsel af denne pakke", "An error occurred:": "Der opstod en fejl:", "An interal error occurred. Please view the log for further details.": "En intern fejl opstod. Se i loggen for flere detaljer.", "An unexpected error occurred:": "En unventet fejl opstod:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Et uventet problem opstod under forsøget på at reparere WinGet. Prøv igen senere.", "An update was found!": "Fundet en opdatering.", "Android Subsystem": "Android Subsystem", "Another source": "<PERSON><PERSON> kilde", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Nye genveje oprettet under en installations- eller opdateringsoperation vil blive slettet automatisk i stedet for at bede om bekræftelse første gang, de bliver detekteret.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Genveje oprettet eller ændret uden for UniGetUI vil blive ignoreret. Du kan tilføje dem via {0} knappen.", "Any unsaved changes will be lost": "Alle ikke-gemte ændringer vil blive mistet.", "App Name": "App Navn", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": "Applikations<PERSON>a, <PERSON><PERSON>, pak<PERSON><PERSON><PERSON>, fjern <PERSON>de installationer automatisk", "Application theme:": "Program udseende:", "Apply": null, "Architecture to install:": "Installation af arkitektur:", "Are these screenshots wron or blurry?": "Er disse skærmbilleder forkerte eller utydelige?", "Are you really sure you want to enable this feature?": "<PERSON>r du virkelig sikker på, du ø<PERSON><PERSON> at aktivere denne funktion?", "Are you sure you want to create a new package bundle? ": "Er du sikker på, at du vil oprette en ny pakkesamling?", "Are you sure you want to delete all shortcuts?": "Er du sikker på, du vil slette alle genveje?", "Are you sure?": "<PERSON>r du sikker?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "Anmod om administrator retti<PERSON><PERSON><PERSON> gang, for hver samling af operationer", "Ask for administrator rights when required": "Anmod om administrator <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Ask once or always for administrator rights, elevate installations by default": "<PERSON><PERSON><PERSON><PERSON> altid eller én gang om administrator rettigheder til installationer.", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "<PERSON><PERSON><PERSON><PERSON> kun om administrator rettigh<PERSON>r <PERSON>n gang (ikke anbefalet)", "Ask to delete desktop shortcuts created during an install or upgrade.": "<PERSON><PERSON><PERSON><PERSON> om at slette skrivebordsgenveje oprettet under en installation el<PERSON> opgradering.", "Attention required": "Opmærksomhed kræves", "Authenticate to the proxy with an user and a password": null, "Author": "<PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "Automatisk skrivebordsgenvej-fjerner", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Gem automatisk en liste af dine installerede pakker til genoprettelse.", "Automatically save a list of your installed packages on your computer.": "Gem automatisk en liste af dine installerede pakker på din computer.", "Autostart WingetUI in the notifications area": "Start WingetUI automatisk i notifikations området", "Available Updates": "Tilgængelige opdateringer", "Available updates: {0}": "Tilgængelige opdateringer: {0}", "Available updates: {0}, not finished yet...": "Tilgængelige opdateringer: {0}, endnu ikke færdiggjorte...", "Backing up packages to GitHub Gist...": null, "Backup": "Backup", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Lav backup af installerede pakker", "Backup location": null, "Become a contributor": "Bidrag selv til udviklingen", "Become a translator": "Bidrag selv til oversættelse", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Beta funktionaliteter og andre indstillinger der ikke burde røres", "Both": "<PERSON><PERSON><PERSON>", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Her er der flere ting du kan gøre for at lære endnu mere om WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Ved at fravælge en pakkemanager vil du ikke længere kunne se eller opdatere dens pakker.", "Cache administrator rights and elevate installers by default": "Gem administrator rettighede<PERSON> i cache, og giv forhøjede rettigheder automatisk", "Cache administrator rights, but elevate installers only when required": "Gem administrator rettigh<PERSON><PERSON> <PERSON> cache, men giv kun forhøjede rettigheder når nødvendigt", "Cache was reset successfully!": "Tømning af cache gennemført", "Can't {0} {1}": "Kan ikke {0} {1}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "<PERSON><PERSON><PERSON> alle operationer", "Change backup output directory": "<PERSON><PERSON><PERSON> mappe for backup filer", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "Bestem hvordan UniGetUI søger efter og installerer tilgængelige opdateringer for dine pakker", "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": "Bestem hvordan UniGetUI installerer pakker og søger efter og installerer tilgængelige opdateringer", "Change how operations request administrator rights": null, "Change install location": "Ændre installations placering", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "<PERSON>øg efter pakke opdateringer periodisk", "Check for updates": "<PERSON><PERSON><PERSON> efter op<PERSON>", "Check for updates every:": "<PERSON><PERSON><PERSON> efter opdateringer hver:", "Check for updates periodically": "<PERSON><PERSON><PERSON> efter opdateringer periodisk", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON>øg efter opdateringer automatisk, og spørg før installation.", "Check for updates regularly, and automatically install available ones.": "Søg efter og installer opdateringer automatisk.", "Check out my {0} and my {1}!": "Se min {0} og {1}!", "Check out some WingetUI overviews": "Se nogle WingetUI oversigter", "Checking for other running instances...": "<PERSON>er efter andre kørende instanser...", "Checking for updates...": "<PERSON><PERSON><PERSON> efter op<PERSON>ringer...", "Checking found instace(s)...": "Undersøger fundne instans(er)...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON> hvor mange operationer der skal udføres parallelt", "Clear cache": "Tøm cache", "Clear finished operations": null, "Clear selection": "<PERSON><PERSON>", "Clear successful operations": "<PERSON><PERSON><PERSON> succesfulde operationer", "Clear successful operations from the operation list after a 5 second delay": "Fjern succesfulde operationer fra operationslisten med 5 sekunders forsinkelse", "Clear the local icon cache": "Tøm den lokale ikon-cache", "Clearing Scoop cache - WingetUI": "Fjerner Scoop cache - WingetUI", "Clearing Scoop cache...": "Fjerner Scoop cache...", "Click here for more details": "Klik her for yd<PERSON><PERSON><PERSON><PERSON>", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Klik på Installer for at starte installationsprocessen. Hvis du springer installationen over, vil UniGetUI muligvis ikke virke som forventet.", "Close": "Luk", "Close UniGetUI to the system tray": "Luk UniGetUI til systembakken", "Close WingetUI to the notification area": "Luk WingetUI til notifikationsområdet", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "Command-line Output", "Command-line to run:": null, "Compare query against": "Sammenlign forespørgsel med", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Komponent Information", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": "Fortsæt", "Contribute to the icon and screenshot repository": "B<PERSON>rag til ikon og screenshot samlingen", "Contributors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Ko<PERSON>r til udklipsholder", "Could not add source": "<PERSON><PERSON> ikke tilføje kilde", "Could not add source {source} to {manager}": "<PERSON><PERSON> ikke til<PERSON>ø<PERSON> kilde {source} til {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "Kunne ikke oprette samlingen", "Could not load announcements - ": "<PERSON>nne ikke hente beskeder - ", "Could not load announcements - HTTP status code is $CODE": "Kunne ikke hente beskeder - HTTP status kode er $CODE", "Could not remove source": "<PERSON><PERSON> ikke fjerne kilde", "Could not remove source {source} from {manager}": "<PERSON><PERSON> ikke fjerne kilde {source} fra {manager}", "Could not remove {source} from {manager}": "<PERSON><PERSON> ikke fjerne {source} fra {manager}", "Create .ps1 script": null, "Credentials": null, "Current Version": "Nuværende version", "Current status: Not logged in": null, "Current user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bruger", "Custom arguments:": "Brugerd<PERSON>inerede argumenter:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "B<PERSON>erd<PERSON>inerede command-line argumenter:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Personliggør WingetUI - kun for advancerede bruger", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ADVARSEL: Vi er ikke ansvarlige for hentede pakker. Installer venligst kun software der er tillid til!", "Dark": "<PERSON><PERSON><PERSON>", "Decline": "<PERSON><PERSON><PERSON>", "Default": "Standard", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Standard indstillinger - <PERSON><PERSON><PERSON><PERSON> til almindelige brugere", "Default vcpkg triplet": "Standard vcpkg triplet", "Delete?": "Slet?", "Dependencies:": null, "Descendant": null, "Description:": "Beskrivelse:", "Desktop shortcut created": "Skrivebordsgenvej oprettet", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Software udvikling er hårdt, og det her program er gratis. H<PERSON> du kan lide programmet, kan du altid <b>købe mig en kaffe</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installer ved dobbeltklik på en pakke i \"{discoveryTab}\" (istedet for at vise pakke-info)", "Disable new share API (port 7058)": "Deaktiver nyt share API (port 7058)", "Disable the 1-minute timeout for package-related operations": "Deaktiver 1-minuts timeout for pakkerelaterede operationer", "Disclaimer": "Ansvarsfraskrivelse", "Discover Packages": "<PERSON><PERSON><PERSON> efter pakker", "Discover packages": "<PERSON><PERSON><PERSON> efter pakker", "Distinguish between\nuppercase and lowercase": "Forskel på store- & små bogstaver", "Distinguish between uppercase and lowercase": "Forskel på store- & små bogstaver", "Do NOT check for updates": "Søg IKKE efter opdateringer", "Do an interactive install for the selected packages": "Foretag en interaktiv installation af de valgte pakker", "Do an interactive uninstall for the selected packages": "Foretag en interaktiv afinstallation af de valgte pakker", "Do an interactive update for the selected packages": "Foretag en interaktiv opdatering af de valgte pakker", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Download ikke nye applicaktionsoversættelser fra GitHub automatisk", "Do not ignore updates for this package anymore": "Ignorer ikke opdateringer for denne pakke mere", "Do not remove successful operations from the list automatically": "Fjern ikke succesfulde operationer fra listen automatisk", "Do not show this dialog again for {0}": "Vis ikke denne dialog igen for {0}", "Do not update package indexes on launch": "Opdater ikke pakkeindex ved programstart", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Accepterer du, at UniGetUI indsamler og sender anonyme brugsstatistikker med det ene formål at forstå og forbedre brugeroplevelsen?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "<PERSON><PERSON><PERSON> du, at UniGetUI er brugbar? <PERSON><PERSON> du gør, vil du måske støtte mit arbejde, så jeg kan fortsætte med at gøre UniGetUI til den ultimative pakkemanager brugergrænseflade.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "<PERSON><PERSON><PERSON> du, at UniGetUI er brugbar? Har du lyst til at støtte udvikleren? <PERSON><PERSON> du gør, kan du {0}. Det hjælper meget!", "Do you really want to reset this list? This action cannot be reverted.": "Ønsker du virkelig at nulstille denne liste? Handlingen kan ikke føres tilbage.", "Do you really want to uninstall the following {0} packages?": "Ønsker du virkelig at afinstallere følgende {0} pakker?", "Do you really want to uninstall {0} packages?": "Ønsker du virkelig at afinstallere {0} pakker?", "Do you really want to uninstall {0}?": "Ønsker du virkelig at afinstallere {0}?", "Do you want to restart your computer now?": "Ønsker du at genstarte computeren nu?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Har du lyst til at hjælpe med at oversætte WingetUI til dit sprog? Se hvordag du kan bidrage <a style=\"color:{0}\" href=\"{1}\"a>her!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Ikke lyst til at donere? <PERSON><PERSON> rolig, du kan altid dele UniGetUI med dine venner. Fortæl om UniGetUI.", "Donate": "Giv et tilskud", "Done!": null, "Download failed": "Download mislykkedes", "Download installer": "Download installationsprogram", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Download lykkedes", "Download updated language files from GitHub automatically": "Download opdaterede sprogfiler fra GitHub automatisk", "Downloading": "Downloader", "Downloading backup...": null, "Downloading installer for {package}": "Download installationsprogram til {package}", "Downloading package metadata...": "Downloader pakke metadata...", "Enable Scoop cleanup on launch": "Aktiver Scoop-oprydning ved programstart", "Enable WingetUI notifications": "Aktiver UniGetUI notifikationer", "Enable an [experimental] improved WinGet troubleshooter": "Aktiver en [eksperimentel] forbedret WinGet problemløser", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "Aktiver baggrunds CPU-brug optimeringer (se Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktiver baggrunds-API (Widgets til UniGetUI og Deling, port 7058)", "Enable it to install packages from {pm}.": "Aktiver det for at installere pakker fra {pm}.", "Enable the automatic WinGet troubleshooter": "Aktiver den automatiske WinGet problemløser", "Enable the new UniGetUI-Branded UAC Elevator": "Aktiver den nye UniGetUI-brandede UAC prompt", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Aktiver {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "<PERSON><PERSON><PERSON>", "Everything is up to date": "Alt er opdateret", "Exact match": "Nøjagtigt match", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Eksisterende genveje på dit skrivebord vil blive scannet, og du vil være nødt til at vælge hvilke, der skal beholdes og hvilke, der skal fjernes.", "Expand version": "Udvid version", "Experimental settings and developer options": "Eksperimentelle indstillinger samt udviklerindstillinger", "Export": "Eksporter", "Export log as a file": "Eksportér log som en fil", "Export packages": "Eksporter pakker", "Export selected packages to a file": "Eksporter valgte pakker til en fil", "Export settings to a local file": "Eksporter indstillinger til en fil", "Export to a file": "Eksporter til fil", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "He<PERSON> seneste besked, vent venligst...", "Filters": "Filtre", "Finish": "A<PERSON>lut", "Follow system color scheme": "Følg styresystemets farvetema", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Tving ARM kompileret version af Winget (KUN TIL ARM64 SYSTEMER)", "Formerly known as WingetUI": "Tidligere kendt som WingetUI", "Found": "Fundet", "Found packages: ": "<PERSON><PERSON>", "Found packages: {0}": "<PERSON><PERSON> pakker: {0}", "Found packages: {0}, not finished yet...": "<PERSON>ne pakker: {0}, endnu ikke færdig...", "General preferences": "<PERSON><PERSON><PERSON>", "GitHub profile": "GitHub-profil", "Global": "Global", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Fantastisk lager af ukendte, men brugbare værktøjer og andre interessante pakker.<br>Indeholder: <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Kommandolinje programmer. Gemerel software (ekstra samling påkrævet)</b>", "Great! You are on the latest version.": "Fantastisk! Du har den seneste version.", "Grid": null, "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help and documentation": "Hjælp og dokumentation", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Her kan du ændre UniGetUI's op<PERSON><PERSON><PERSON><PERSON> vedrørende følgende genveje. Markering af en genvej vil få UniGetUI til at slette den, hvis den oprettes under en fremtidig opgradering. Afmarkering af den vil beholde genvejen.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, mit navn er Mart<PERSON> og jeg er <i>udvikleren</i> af WingetUI. WingetUI er udelukkende blevet bygget i min fritid!", "Hide details": "<PERSON><PERSON><PERSON><PERSON>", "Homepage": "Startside", "Hooray! No updates were found.": "Ingen opdateringer fundet.", "How should installations that require administrator privileges be treated?": "<PERSON><PERSON><PERSON> behandles installationer, som kræver administratorrettigheder?", "How to add packages to a bundle": "<PERSON><PERSON><PERSON> tilfø<PERSON>s pakker til en samling", "I understand": "Accepter", "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Ignorer fremtidige opdateringer til denne pakke", "Ignore packages from {pm} when showing a notification about updates": "Ignorer pakker fra {pm} når der vises en notifikation om opdateringer", "Ignore selected packages": "<PERSON><PERSON><PERSON> valgte pakker", "Ignore special characters": "Ignorer særlige bogstaver/tegn", "Ignore updates for the selected packages": "Ignorer opdateringer for de valgte pakker", "Ignore updates for this package": "Ignorer opdateringer for denne pakke", "Ignored updates": "Ignorerede opdateringer", "Ignored version": "Ignoreret version", "Import": "Importer", "Import packages": "Importer pakker", "Import packages from a file": "Importer pakker fra fil", "Import settings from a local file": "Importer indstillinger fra fil", "In order to add packages to a bundle, you will need to: ": "For at føje pakker til en samling, er du nødt til:", "Initializing WingetUI...": "Klargøre WingetUI", "Install": "Installer", "Install Scoop": "Installer Scoop", "Install and more": null, "Install and update preferences": "Installations- og opdateringspræferencer", "Install as administrator": "Installér som administrator", "Install available updates automatically": "Installer tilgængelige opdateringer automatisk", "Install location can't be changed for {0} packages": null, "Install location:": "Installations placering", "Install options": null, "Install packages from a file": "Installer pakker fra fil", "Install prerelease versions of UniGetUI": "Installer præ-release versioner af UniGetUI", "Install script": null, "Install selected packages": "Installer valgte pakker", "Install selected packages with administrator privileges": "Installer valgte pakker med administrator re<PERSON><PERSON><PERSON>r", "Install selection": "Installer valgte", "Install the latest prerelease version": "Installer den seneste test version", "Install updates automatically": "Installer opdateringer automatisk", "Install {0}": "Installer {0}", "Installation canceled by the user!": "Installation afbrudt af bruger", "Installation failed": "Installation fejlede", "Installation options": "Installations muligheder", "Installation scope:": "Installationsomfang:", "Installation succeeded": "Installation gennemført", "Installed Packages": "Installerede <PERSON>", "Installed Version": "Installeret Version", "Installed packages": "Installerede pakker", "Installer SHA256": "Installer SHA256", "Installer SHA512": "Installer SHA512", "Installer Type": "Installer Type", "Installer URL": "Installer URL", "Installer not available": "Installationsprogram er ikke tilgængeligt", "Instance {0} responded, quitting...": "Instans {0} svar<PERSON>, afslutter...", "Instant search": "Øjeblikkelig søgning", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Integritetstjek sprunget over", "Integrity checks will not be performed during this operation": "Integritetstjek vil ikke blive udført under denne operation", "Interactive installation": "Interaktiv installation", "Interactive operation": "Interaktiv operation", "Interactive uninstall": "Interaktiv afinstallation", "Interactive update": "Interaktiv opdatering", "Internet connection settings": null, "Is this package missing the icon?": "Mangler denne pakke sit ikon?", "Is your language missing or incomplete?": "Mangler dit sprog (he<PERSON> el<PERSON>)?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": "<PERSON> anbefales at genstarte UniGetUI efter WinGet er blevet repareret", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Det ser ud til, at WinGet ikke fungerer ordentligt. Ønsker du at forsøge at reparere WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Det ser ud til, at du startede UniGetUI som administrator, hvilket ikke anbefales. Du kan stadig brpge programmet, men vi anbefaler kraftigt ikke at køre UniGetUI med administratorrettigheder. Klik på \"{showDetails}\" for at se hvorfor.", "Language": null, "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON>, tema og forskellige andre inds<PERSON>", "Last updated:": "Senest opdateret:", "Latest": "Senest:", "Latest Version": "Seneste Version", "Latest Version:": "Seneste Version:", "Latest details...": "Seneste de<PERSON>jer...", "Launching subprocess...": "Starter underprocess...", "Leave empty for default": "<PERSON><PERSON><PERSON> blankt for standardindstilling", "License": "Licens", "Licenses": "Licenser", "Light": "Lys", "List": null, "Live command-line output": "Live-output fra kommandolinje", "Live output": "Live-output", "Loading UI components...": "Indlæser brugerfladekomponenter...", "Loading WingetUI...": "Indlæser WingetUI...", "Loading packages": "Indlæser pakker", "Loading packages, please wait...": "<PERSON><PERSON><PERSON><PERSON> pakker, vent venligst...", "Loading...": "Indlæser...", "Local": "<PERSON><PERSON>", "Local PC": "Lokal PC", "Local backup advanced options": null, "Local machine": "Lokale maskine", "Local package backup": null, "Locating {pm}...": "Lokalisere {pm}", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "Log-niveau:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "<PERSON><PERSON> efter pakker...", "Machine | Global": "Maskine | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "Administrer", "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "Administrer UniGetUI automatisk start-opførsel fra Indstillinger appen", "Manage ignored packages": "Administrer <PERSON><PERSON><PERSON> p<PERSON>ker", "Manage ignored updates": "Administrer ignore<PERSON><PERSON>", "Manage shortcuts": "Administrer genveje", "Manage telemetry settings": "Administrer telemetriindstillinger", "Manage {0} sources": "Administrer {0} kilder", "Manifest": "Manifest", "Manifests": "Manifests", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsofts officielle pakkemanager. Fuld af velkendte og verificerede pakker<br>Indeholder: <b>Generel software, Microsoft Store apps</b>", "Missing dependency": "Manglende afhængighed", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON><PERSON>", "More details about the shared data and how it will be processed": "Flere detaljer om delte data og hvordan det bliver behandled", "More info": "Mere info", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "BEMÆRK: <PERSON><PERSON> kan deaktiveres fra UniGetUI Indstillinger i WinGet sektionen", "Name": "Navn", "New": null, "New Version": "Ny Version", "New bundle": "<PERSON><PERSON> sam<PERSON>", "New version": "Ny version", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": "Der blev ikke fundet noget relevant installationsprogram til pakken {0}", "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "Ingen pakker fundet", "No packages found matching the input criteria": "Ingen pakker fundet der opfylder valgte kriterier", "No packages have been added yet": "Ingen pakker tilføjet endnu", "No packages selected": "Ingen pakker valgt", "No packages were found": "Ingen pakker blev fundet", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Ingen personlig information bliver indsamlet eller sendt, og den indsamlede data bliver anonymiseret, så det ikke kan spores tilbage til dig.", "No results were found matching the input criteria": "Ingen resultater fundet der opfylder valgte kriterier", "No sources found": "Ingen kilder fundet", "No sources were found": "Ingen kilder blev fundet", "No updates are available": "Ingen opdateringer er tilgængelige", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS's pakkemanager. Fuld af biblioteker og andre væ<PERSON>øjer, som omkranser Javascript verdenen<br>Indeholder: <b>Node Javascript biblioteker og andre relaterede værktøjer</b>", "Not available": "<PERSON><PERSON><PERSON>", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Ikke fundet", "Not right now": "Ikke lige nu", "Notes:": "Noter", "Notification preferences": "Notifikationspræferencer", "Notification tray options": "<PERSON><PERSON><PERSON><PERSON> for notifikationsområde", "Notification types": null, "NuPkg (zipped manifest)": "NuPkg (zipped manifest)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON>", "Open UniGetUI": "Åbn UniGetUI", "Open UniGetUI security settings": null, "Open WingetUI": "<PERSON>ben WingetUI", "Open backup location": "Åben backup lokation", "Open existing bundle": "<PERSON><PERSON> eksisterende samling", "Open install location": "Åbn installationslokation", "Open the welcome wizard": "<PERSON><PERSON> velkomst wizard'en", "Operation canceled by user": "Handlingen blev annulleret af bruger", "Operation cancelled": "Handling annulleret", "Operation history": "Handlingshistorik", "Operation in progress": "Handling i gang", "Operation on queue (position {0})...": "Handling er i kø (position {0})...", "Operation profile:": null, "Options saved": "Indstillinger gemt", "Order by:": null, "Other": "<PERSON>", "Other settings": null, "Package": null, "Package Bundles": "<PERSON><PERSON>", "Package ID": "Pakke ID", "Package Manager": "Pakkemanager", "Package Manager logs": "Pakkemanager logs", "Package Managers": "Pakkemanagere", "Package Name": "<PERSON><PERSON> navn", "Package backup": "Pak<PERSON><PERSON><PERSON>", "Package backup settings": null, "Package bundle": "<PERSON><PERSON> sa<PERSON>", "Package details": "<PERSON><PERSON>", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "Pakkemanager inds<PERSON>linger", "Package managers": "Pakkemanagere", "Package not found": "Pakke ikke fundet", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": "<PERSON><PERSON> {name} fra {manager}", "Package's default": null, "Packages": "<PERSON><PERSON>", "Packages found: {0}": "Pakker fundet: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "Indsæt en gyldig URL til databasen", "Pause updates for": null, "Perform a backup now": "Lav en backup", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "Laver backup, vent venligst...", "Periodically perform a backup of the installed packages": "Lav backup af installerede pakker periodisk", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "Kontroller installationsindstillingerne for denne pakke og prøv igen", "Please click on \"Continue\" to continue": "Klik på \"Fortsæt\" for at fortsætte", "Please enter at least 3 characters": "Indtast venligst mindst 3 tegn", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON><PERSON>, at visse pakker muligvis ikke kan istalleres på grund af de pakkemanagere, der er aktiveret på denne maskine.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Bemærk venligst at pakker fra bestemte kilder kan være ikke-eksporterbare. De er nedtonet og vil ikke blive eksporteret.", "Please run UniGetUI as a regular user and try again.": "Kør UniGetUI som almindelig bruger og prøv igen.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Se Kommandolinje Output eller i Handlingshistorikken for yderligere information om dette problem.", "Please select how you want to configure WingetUI": "<PERSON><PERSON><PERSON>g venligst hvordan du vil opsætte WingetUI", "Please try again later": "<PERSON><PERSON><PERSON><PERSON> igen senere", "Please type at least two characters": "Indtast venligst mindst 2 tegn", "Please wait": "<PERSON><PERSON> venligst", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Vent mens {0} bliver installeret. Et sort (el<PERSON> blåt) vindue kan dukke up. Vent venligst på at det lukker.", "Please wait...": "Vent venligst...", "Portable": "Portabel", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell's pakkemanager. Find biblioteker og scripts til at udvide mulighederne i PowerShell<br>Indeholder: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "Præ-release", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vent venligst...", "Proceed at your own risk.": "Fortsæt på egen risiko.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "<PERSON>dgi<PERSON><PERSON> dato", "Publisher": "Udgiver", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python's biblioteksmanager. Fuld af python biblioteker og andre pythonrelaterede værktøjer<br>Indeholder: <b>Python biblioteker og andre relaterede værktøjer</b>", "Quit": "A<PERSON>lut", "Quit WingetUI": "Luk WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "<PERSON><PERSON><PERSON><PERSON>", "Reinstall package": "Geninstaller pakke", "Related settings": null, "Release notes": "Releasenotes", "Release notes URL": "Releasenotes URL", "Release notes URL:": "Releasenotes URL:", "Release notes:": "Udgivelsesnoter", "Reload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reload log": "Gen<PERSON>læ<PERSON> log", "Removal failed": "Fjernelse fejlede", "Removal succeeded": "Fjernelse gennemført", "Remove from list": "<PERSON><PERSON>n fra liste", "Remove permanent data": "Fjern permanente data", "Remove selection from bundle": "<PERSON><PERSON><PERSON> valgte fra samling", "Remove successful installs/uninstalls/updates from the installation list": "<PERSON><PERSON>n gennemførte installeringer/afinstalleringer/opdateringer fra installationslisten", "Removing source {source}": "<PERSON><PERSON><PERSON> kilde {source}", "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON> kilde {source} fra {manager}", "Repair UniGetUI": null, "Repair WinGet": "Reparer Win<PERSON>et", "Report an issue or submit a feature request": "Rapporter et issue eller send et ønske om en ny feature", "Repository": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reset": "Nulstil", "Reset Scoop's global app cache": "Nulstil Scoop's globale app cache", "Reset UniGetUI": "Nulstil UniGetUI", "Reset WinGet": "Nulstil WinGet", "Reset Winget sources (might help if no packages are listed)": "<PERSON><PERSON><PERSON><PERSON>'s kilder (hjæ<PERSON>per måske hvis ingen pakker bliver vist)", "Reset WingetUI": "Nulstil WingetUI", "Reset WingetUI and its preferences": "Nulstil WingetUI og det's indstillinger", "Reset WingetUI icon and screenshot cache": "Nulstil WingetUI ikon og screenshot cache", "Reset list": "Nulstil liste", "Resetting Winget sources - WingetUI": "Nulstiller Winget kilder - WingetUI", "Restart": "Genstart", "Restart UniGetUI": "Genstart UniGetUI", "Restart WingetUI": "Genstart WingetUI", "Restart WingetUI to fully apply changes": "Genstart WingetUI for at gennemføre ændringer", "Restart later": "Genstart senere", "Restart now": "Genstart nu", "Restart required": "Genstart påkrævet", "Restart your PC to finish installation": "Genstart din computer for at færdiggøre installation", "Restart your computer to finish the installation": "Genstart din computer for at færdiggøre installation", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "<PERSON><PERSON><PERSON><PERSON> igen", "Retry as administrator": "<PERSON><PERSON><PERSON><PERSON> igen som administrator", "Retry failed operations": "<PERSON><PERSON><PERSON><PERSON> mis<PERSON>kedes handlinger igen", "Retry interactively": "Prøv igen som interaktiv", "Retry skipping integrity checks": "Prøv oversprungne integritetstjek igen", "Retrying, please wait...": "<PERSON><PERSON><PERSON><PERSON> igen, vent venligst...", "Return to top": "Til toppen", "Run": "<PERSON><PERSON><PERSON>", "Run as admin": "<PERSON><PERSON><PERSON> administrator", "Run cleanup and clear cache": "Kør oprydning og ryd cache", "Run last": "<PERSON><PERSON><PERSON>", "Run next": "<PERSON><PERSON><PERSON>", "Run now": "<PERSON><PERSON><PERSON> nu", "Running the installer...": "<PERSON><PERSON><PERSON> installationsprogram...", "Running the uninstaller...": "<PERSON><PERSON><PERSON> afinstallationsprogram...", "Running the updater...": "<PERSON><PERSON><PERSON> op<PERSON>ring...", "Save": null, "Save File": "Gem <PERSON>l", "Save and close": "Gem og luk", "Save as": null, "Save bundle as": "Gem samling som", "Save now": "Gem", "Saving packages, please wait...": "<PERSON><PERSON><PERSON>, vent venligst...", "Scoop Installer - WingetUI": "<PERSON><PERSON>lere - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop <PERSON>lere - WingetUI", "Scoop package": "<PERSON><PERSON> pakke", "Search": "<PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "<PERSON>øg efter desktop-software, fortæl mig når der er opdateringer tilgængelig, og lad være med at gøre nørdede ting. Jeg ø<PERSON><PERSON> ikke at UniGetUI skal overkomplicere, jeg ø<PERSON><PERSON> bare en enkel <b>software-butik</b>", "Search for packages": "<PERSON><PERSON><PERSON> efter pakker", "Search for packages to start": "<PERSON><PERSON><PERSON> efter pakker for at komme i gang", "Search mode": "S<PERSON><PERSON> tilstand", "Search on available updates": "<PERSON><PERSON><PERSON> blandt tilgængelige opdateringer", "Search on your software": "Søg i din software", "Searching for installed packages...": "<PERSON><PERSON><PERSON> efter installerede pakker...", "Searching for packages...": "<PERSON><PERSON><PERSON> efter pakker...", "Searching for updates...": "<PERSON><PERSON><PERSON> efter op<PERSON>ringer...", "Select": "<PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Vælg \"{item}\" til at tilføje til samling", "Select a folder": "Vælg en folder", "Select all": "<PERSON><PERSON><PERSON><PERSON> alle", "Select all packages": "<PERSON><PERSON><PERSON><PERSON> alle pakker", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON> kun <b>hvis du ved hvad du laver</b>.", "Select package file": "<PERSON><PERSON><PERSON><PERSON> pakke-fil", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "<PERSON><PERSON><PERSON>g kilden du vil tilføje:", "Select upgradable packages by default": "Vælg opgraderbare pakker som standard", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> <b>pakkemanager<PERSON></b>, der skal anvendes ({0}), konfigurer hvordan pakker installeres, administrer hvordan administrator-rettigheder håndteres osv.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Afsendt håndtryk. Venter på svar... ({0}%)", "Set a custom backup file name": "<PERSON>æ<PERSON>g <PERSON> filnavn", "Set custom backup file name": "<PERSON>æ<PERSON>g <PERSON> filnavn", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Share": "Del", "Share WingetUI": "<PERSON>", "Share anonymous usage data": "Del anonym brugsdata", "Share this package": "<PERSON> p<PERSON>ke", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Vis UniGetUI i system-tray", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "<PERSON><PERSON>", "Show a notification when an installation fails": "Vis notifikation når en installation fejler", "Show a notification when an installation finishes successfully": "Vis notifikation når en installation gennemføres", "Show a notification when an operation fails": "Vis en notifikation når en handling mislykkes", "Show a notification when an operation finishes successfully": "Vis en notifikation når en handling gennemføres med success", "Show a notification when there are available updates": "Vis notifikation når der er tilgængelige opdateringer", "Show a silent notification when an operation is running": "Vis en lydløs notifikation når en handling kører", "Show details": "<PERSON><PERSON>", "Show in explorer": "Vis i stifinder", "Show info about the package on the Updates tab": "Vis information om pakken på Opdateringer fanen", "Show missing translation strings": "<PERSON><PERSON> felter der mangler oversættelse", "Show notifications on different events": "Vis notifikationer ved forskellige hænde<PERSON>er", "Show package details": "<PERSON><PERSON><PERSON>", "Show package icons on package lists": "<PERSON>is pakke<PERSON>ner på pakkelister", "Show similar packages": "<PERSON>is lignende pakker", "Show the live output": "Vis Live-output", "Size": "<PERSON><PERSON><PERSON><PERSON>", "Skip": "Spring over", "Skip hash check": "Spring over hash check", "Skip hash checks": "Spring hash-tjeks over", "Skip integrity checks": "Spring integritetstjeks over", "Skip minor updates for this package": "Spring mindre opdateringer over for denne pakke", "Skip the hash check when installing the selected packages": "Spring hash-tjekket over ved installation af de valgte pakker", "Skip the hash check when updating the selected packages": "Spring hash-tjekket onver ved opdatering af de valgte pakker", "Skip this version": "Spring denne version over", "Software Updates": "<PERSON><PERSON>", "Something went wrong": "Noget gik galt", "Something went wrong while launching the updater.": "Noget gik galt ved afviklingen af opdateringen.", "Source": "<PERSON><PERSON>", "Source URL:": "Kilde URL:", "Source added successfully": "Kilde blev tilføjet med success", "Source addition failed": "<PERSON><PERSON> tilføjelse fejlede", "Source name:": "Kilde navn:", "Source removal failed": "Fjernelse af kilde fejlede", "Source removed successfully": "Kilden blev fjernet med success", "Source:": "<PERSON><PERSON>", "Sources": "<PERSON><PERSON>", "Start": "Start", "Starting daemons...": "Starter baggrun<PERSON><PERSON>plik<PERSON>er...", "Starting operation...": "Starter handling...", "Startup options": "Opstartsindstillinger", "Status": "Status", "Stuck here? Skip initialization": "Sidder den fast? Spring over opsætningen", "Success!": null, "Suport the developer": "<PERSON><PERSON><PERSON>", "Support me": "<PERSON><PERSON>t mig", "Support the developer": "<PERSON><PERSON><PERSON>", "Systems are now ready to go!": "Systemet er nu klar.", "Telemetry": null, "Text": "Tekst", "Text file": "Tekstfil", "Thank you ❤": "Tak ❤", "Thank you 😉": "Tak  😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust pakkemanageren.<br>Indeholder: <b>Rust biblioteker og programmer skrevet i Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Sikkerhedskopien vil IKKE indeholde nogle binære filer eller nogle programmers gemte data.", "The backup will be performed after login.": "<PERSON><PERSON> vil blive udført efter login.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Sikkerhedskopien vil indeholde den fulde liste over de installerede pakker og deres installationsindstillinger. Ignorerede opdateringer og oversprungne versioner vil også blive gemt.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON><PERSON><PERSON>, du prøver at indlæse, ser ud til at være ugyldig. Tjek filen og prøv igen.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Kontrolsummmen for installationsprogrammet stemmer ikke med den forventede værdi, og ægtheden af installationsprogrammet kan ikke verificeres. Hvis du stoler på udgiveren, {0} pakken igen med overspring af hash-tjek.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Windows' e<PERSON>, hvor du kan finde det meste.<br>Indeholder:<b>Generel software</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Den nuværende samling har ikke nogle pakker. Tilføj nogle pakker for at komme i gang", "The executable file for {0} was not found": "Den eksekverbare fil for {0} blev ikke fundet", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Disse pakker vil blive eksporteret til en JSON-fil. Ingen brugerdata eller binære filer bliver gemt.", "The following packages are going to be installed on your system.": "Disse pakker vil blive installeret på dit system.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "Disse indstillinger vil blive anvendt hver gang denne pakke installeres, opdateres eller fjernes.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Disse indstillinger vil blive anvendt hver gang denne pakke installeres, opdateres eller fjernes. De vil blive gemt automatisk.", "The icons and screenshots are maintained by users like you!": "Ikonerne og skærmbillederne er vedligeholdt af brugere som dig!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "Installationsprogrammets ægthed kunne ikke verificeres.", "The installer has an invalid checksum": "Installationsprogrammet har en ugyldig kontrolsum", "The installer hash does not match the expected value.": "Installationsprogrammets hash passer ikke med den forventede værdi.", "The local icon cache currently takes {0} MB": "Den lokale ikon-cache fylder lige nu {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Hovedformålet med dette projekt er, at skabe en intuitiv brugergrænseflade til at håndtere de mest almindelige kommandolinje-pakkemanagerprogrammer til Windows, så som Winget og Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Pak<PERSON> \"{0}\" blev ikke fundet via pakkemanager \"{1}\"", "The package bundle could not be created due to an error.": "Pakkesamlingen kunne ikke oprettes på grund af en fejl.", "The package bundle is not valid": "Pakkesamlingen er ikke gyldig", "The package manager \"{0}\" is disabled": "Pakkemanageren \"{0}\" er deaktiveret", "The package manager \"{0}\" was not found": "Pakkemanageren \"{0}\" blev ikke fundet", "The package {0} from {1} was not found.": "Pakken {0} fra {1} blev ikke fundet.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "<PERSON><PERSON><PERSON> listet her vil ikke blive taget i betragtning ved søgning efter opdateringer. Dobbeltklik på dem eller klik på knappen til højre for dem for at stoppe med at ignorere deres opdateringer.", "The selected packages have been blacklisted": "De valgte pakker er blevet sortlistet", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "<PERSON><PERSON><PERSON><PERSON> af sikkerhedskopien estimeres til at være mindre end 1MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON> {source} blev <PERSON><PERSON><PERSON><PERSON> til {manager} med success", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON> {source} blev fjernet fra {manager} med success", "The system tray icon must be enabled in order for notifications to work": "Systemtray ikonen skal aktiveres for at notifikationer fungerer", "The update process has been aborted.": "Opdateringsprocessen er blevet afbrudt.", "The update process will start after closing UniGetUI": "Opdateringsprocessen vil starte efter UniGetUI bliver lukket", "The update will be installed upon closing WingetUI": "Opdateringen vil blive installeret, når UniGetUI bliver lukket", "The update will not continue.": "Opdateringen vil ikke fortsætte.", "The user has canceled {0}, that was a requirement for {1} to be run": "<PERSON><PERSON><PERSON>n har annulleret {0}, det var et krav for, at {1} kunne afvikles", "There are no new UniGetUI versions to be installed": "Der er ikke nogen nye UniGetUI versioner til installation.", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Der er igangværende handlinger. Afslutning af UniGetUI kan betyde, at de mislykkes. Ønsker du at fortsætte?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Der er nogle gode videoer på YouTube, som demonstrerer UniGetUI og det's muligheder. Du kan lære nogle brugbare tips og tricks!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Der er to hovedårsager til ikke at køre UniGetUI som administrator:\n <PERSON>, at Scoop pakkemanageren kan forårsage problemer med nogle kommandoer, når den afvikles med administratorrettigheder.\n Den anden er, at køres UniGetUI som administrator, bet<PERSON><PERSON> det, at enhver pakke, du downloader, vil blive afviklet som administrator (hvilket ikke er sikkerhedsmæssigt fornuftigt).\n <PERSON>sk, at hvis du har behov for at installere en specifik pakke som administrator, kan du altid højre-klikke på pakken -> Installer/Opdater/Afinstaller som administrator.", "There is an error with the configuration of the package manager \"{0}\"": "Der er en fejl i konfigurationen af pakkemanageren \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Der er en installation i gang. Hvis du lukker UniGetUI, kan installationen muligvis mislykkes og have uventedede resultater. Ønsker du stadig at afslutte UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "De er programmerne, som er ansvarlige for at installere, opdatere og fjerne pakker.", "Third-party licenses": "Tredjepartslicenser", "This could represent a <b>security risk</b>.": "<PERSON><PERSON> kunne udgøre en <b>si<PERSON><PERSON><PERSON><PERSON><PERSON></b>.", "This is not recommended.": "<PERSON>te er ikke anbefalet.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Dette er sandsynligvis på grund af, at den pakke, du modtog, er blevet fjernet eller offentliggjort via en pakkemanager, du endnu ikke har aktiveret. Det modtagne ID er {0}", "This is the <b>default choice</b>.": "Dette er <b>standardvalget</b>.", "This may help if WinGet packages are not shown": "<PERSON> kan hj<PERSON><PERSON><PERSON>, h<PERSON>et pakker ikke bliver vist", "This may help if no packages are listed": "Det kan hjæ<PERSON><PERSON>, hvis der ikke listes nogle pakker", "This may take a minute or two": "Det kan tage et minut eller to", "This operation is running interactively.": "Handlingen kører interaktivt.", "This operation is running with administrator privileges.": "<PERSON>lingen kører med administratorrettigheder.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Pakken kan opdateres", "This package can be updated to version {0}": "<PERSON>ne pakke kan opdateres til version {0}", "This package can be upgraded to version {0}": "<PERSON>ne pakke kan opgraderes til version {0}", "This package cannot be installed from an elevated context.": "Denne pakke kan ikke installeres fra en kontekst med forhøjede rettigheder.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Denne pakke har ingen skærmbilleder eller mangler ikonet? Bidrag til UniGetUI ved at tilføje manglende ikoner og skærmbilleder til vores åbne, offentlige database.", "This package is already installed": "Denne pakke er allerede installeret", "This package is being processed": "<PERSON><PERSON> pakke bliver behandlet", "This package is not available": "Denne pakke er ikke tilgængelig", "This package is on the queue": "Denne pakke er ikke i køen", "This process is running with administrator privileges": "Denne process kører med administratorrettigheder", "This project has no connection with the official {0} project — it's completely unofficial.": "Dette projekt har ingen forbindelse med det officielle {0} projekt — det er komplet uofficielt.", "This setting is disabled": "<PERSON><PERSON> indstilling er deaktiveret", "This wizard will help you configure and customize WingetUI!": "Denne guide vil hjæ<PERSON><PERSON> dig med at konfigurere og tilpasse UniGetUI!", "Toggle search filters pane": "Skift søgefiltervinduet", "Translators": "Oversættere", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Her skriver du navnet og URL på den kilde, du ønsker at tilføje, adskilt af et mellemrum.", "Unable to find package": "<PERSON>nne ikke finde pakken", "Unable to load informarion": "Kunne ikke indlæse information", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI indsamler anonyme brugsdata for at forbedre brugeroplevelsen.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI indsamler anonyme brugsdata med det ene formåle at forstå og forbedre brugeroplevelsen.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI har opdaget en ny skrivebordsgenvej, som kan slettes automatisk.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI har opdaget følgende skrivebordsgenveje, som kan fjernes automatisk ved fremtidige opgraderinger", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI har opdaget {0} nye skrivebordsgenveje, som kan fjernes automatisk.", "UniGetUI is being updated...": "UniGetUI bliver opdateret...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI er ikke relateret til nogle af de kompatible pakkemanagere. UniGetUI er it uafhængigt projekt.", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI kræver {0} for at fungere, men det blev ikke fundet på dit system.", "UniGetUI startup page:": "UniGetUI startside:", "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": "UniGetUI version {0} bliver downloaded.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} er klar til at blive installeret.", "Uninstall": "A<PERSON>stallér", "Uninstall Scoop (and its packages)": "Afinstaller Scoop (og dets pakker)", "Uninstall and more": null, "Uninstall and remove data": "Afinstaller og fjern data", "Uninstall as administrator": "Afinstallér som administrator", "Uninstall canceled by the user!": "Afinstallering afbrudt af brugeren!", "Uninstall failed": "Afinstallering fejlede", "Uninstall options": null, "Uninstall package": "Afinstaller pakke", "Uninstall package, then reinstall it": "Afinstaller valgte pakke, og så geninstaller den", "Uninstall package, then update it": "Afinstaller valgte pakke, og så opdater den (hvordan fungere det?)", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Afinstaller valgte pakker", "Uninstall selection": null, "Uninstall succeeded": "Afinstallation gennemført", "Uninstall the selected packages with administrator privileges": "Afinstaller de valgte pakker med administratorrettigheder", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "<PERSON>kke installerbare pakker med oprindelse listet som \"{0}\" er ikke offentliggjort på nogle pakkemanagere, så der er ingen information tilgængelig at vise om dem.", "Unknown": "Ukendt", "Unknown size": "Ukendt størrelse", "Unset or unknown": "Ikke angivet eller uk<PERSON>t", "Up to date": "<PERSON><PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Opdatér WingetUI automatisk", "Update all": "Opdatér alle", "Update and more": null, "Update as administrator": "Opdatér som administrator", "Update check frequency, automatically install updates, etc.": null, "Update checking": null, "Update date": "Sidste opdatering", "Update failed": "<PERSON><PERSON><PERSON> fejlet", "Update found!": "Opdatering fundet!", "Update now": "Opdater nu", "Update options": null, "Update package indexes on launch": "Opdater pakkeindeks ved start", "Update packages automatically": "Opdater pakker automatisk", "Update selected packages": "<PERSON><PERSON><PERSON> valgte pakker", "Update selected packages with administrator privileges": "Opdater valgte pakker med administrator rettigh<PERSON>r", "Update selection": null, "Update succeeded": "Opdatering gennemført", "Update to version {0}": "Opdater til version {0}", "Update to {0} available": "Opdatering til {0} tilgængelig", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Opdater vcpkg's Git portfiles automatisk (kræver Git installeret)", "Updates": "<PERSON><PERSON><PERSON><PERSON>", "Updates available!": "Opdateringer tilgængelig!", "Updates for this package are ignored": "Opdateringer til denne pakker bliver ignoreret", "Updates found!": "Opdateringer fundet!", "Updates preferences": "Opdateringspræferencer", "Updating WingetUI": "Opdatere WingetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Brug Legacy medleverede WinGet i stedet for PowerShell kommandoer", "Use a custom icon and screenshot database URL": "Brug brugerdefineret ikon og screenshot database URL", "Use bundled WinGet instead of PowerShell CMDlets": "Brug den medleverede WinGet i stedet for PowerShell kommandoer", "Use bundled WinGet instead of system WinGet": "Brug den medleverede WinGet i stedet for systemets WinGet", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Brug den installerede GSudo i stedet for den medleverede", "Use system Chocolatey": "Brug systemets Chocolatey", "Use system Chocolatey (Needs a restart)": "Brug systemets Chocolatey (Kræver genstart)", "Use system Winget (Needs a restart)": "Brug systemets Winget (Kræver genstart)", "Use system Winget (System language must be set to english)": "Brug systemets Winget (Systemsprog skal muligvis være sat til engelsk)", "Use the WinGet COM API to fetch packages": "Brug WinGet COM API for at hente pakker", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Brug WinGet PowerShell-modulet i stedet for WinGet COM API", "Useful links": "Nyttige links", "User": "<PERSON><PERSON><PERSON>", "User interface preferences": "Brugerflade indstillinger", "User | Local": "Bruger | Lokal", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Brug af UniGetUI betyder accept af GNU Lesser General Public License v2.1 licensen", "Using WingetUI implies the acceptation of the MIT License": "Brug af UniGetUI betyder accept af MIT licensen", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg roden blev ikke fundet. Definer %VCPKG_ROOT% miljøvariablen eller definer den fra UniGetUI indstillinger", "Vcpkg was not found on your system.": "Vcpkg blev ikke fundet på dit system.", "Verbose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Version": "Version", "Version to install:": "Version til installation", "Version:": null, "View GitHub Profile": "Se GitHub Profil", "View WingetUI on GitHub": "Se WingetUI på GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Se kildekoden til UniGetUI. Derfra kan du rapportere fejl, foreslå funktioner eller endda bidrage direkte til UniGetUI projektet", "View mode:": null, "View on UniGetUI": "Se på UniGetUI", "View page on browser": "Vis side i browser", "View {0} logs": "Vis {0} logfiler", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "<PERSON><PERSON> på, at enheden er forbundet til internettet, før der forsøges på at gøre noget, som kræver internetforbindelse.", "Waiting for other installations to finish...": "Venter på at andre installationer bliver færdige...", "Waiting for {0} to complete...": "Venter på, at {0} bliver færdig...", "Warning": "<PERSON><PERSON><PERSON>", "Warning!": "<PERSON><PERSON><PERSON>!", "We are checking for updates.": "<PERSON><PERSON> søger efter op<PERSON>.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Vi kunne ikke indlæse detaljerede informationer om denne pakke, fordi den ikke blev fundet i nogle af dine pakkekilder.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Vi kunne ikke indlæse detaljeret information om denne pakke fordi, den ikke blev installeret fra en tilgængelig pakkemanager.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON>i kunne ikke {action} {package}. Prøv igen senere. Klik på \"{showDetails}\" for at få logfiler fra installationsprogrammet.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON>i kunne ikke {action} {package}. Prøv igen senere. Klik på \"{showDetails}\" for at få ligfiler fra afinstallationsprogrammet.", "We couldn't find any package": "Vi kunne ikke finde nogen pakke", "Welcome to WingetUI": "Velkommen til UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Hvilke pakkemanagere ønsker du at anvende?", "Which source do you want to add?": "Hvilken kilde ønsker du at tilføje?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Mens WinGet kan bruges i UniGetUI, kan UniGetUI også bruges med andre pakkemanagere, hvilket kan være forvirrende. Tidligere var UniGetUI designet til kun at anvende WinGet, men dette gælder ikke længere, og derfor repræsenterer UniGetUI ikke hvad dette projekt sigter efter at blive.", "WinGet could not be repaired": "WinGet kunne ikke repareres", "WinGet malfunction detected": "WinGet-fejl fundet", "WinGet was repaired successfully": "WinGet blev repareret med success", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Alt er opdateret", "WingetUI - {0} updates are available": "UniGetUI - {0} opdateringer er tilgængelige", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI Startside", "WingetUI Homepage - Share this link!": "WingetUI Startside - <PERSON> link!", "WingetUI License": "WingetUI Licens", "WingetUI Log": "WingetUI Log", "WingetUI Repository": "WingetUI Biblioteket", "WingetUI Settings": "WingetUI Indstillinger", "WingetUI Settings File": "WingetUI Indstillings Fil", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI bruger følgende biblioteker. Uden dem ville WingetUI ikke have været mulig.", "WingetUI Version {0}": "WingetUI Version {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI autostart-opførsel, programstart-indstillinger", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI kan kontrollere om din software har tilgængelige opdateringer og installere dem automatisk, hvis du ønsker det", "WingetUI display language:": "WingetUI Sprog", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI har været kørt som administrator, hvilket ikke anbefales. Når man kører UniGetUI som administrator vil ALLE handlinger startet fra UniGetUI have administratorrettigheder. Du kan stadig bruge programmet, men vi anbefaler kraftigt ikke at køre UniGetUI med administratorrettigheder.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI er oversat til mere end 40 sprog takket være de frivillige oversættere. Mange tak 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI er ikke blevet automatisk oversat! Disse mennesker har stået for oversættelsen:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI er en applikation, som gør adminstrationen af din software lettere ved at levere et alt-i-et grafisk brugergrænseflade til dine kommandolinje pakkemanagere.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI ændrer navn for at tydeliggøre forskellen mellem UniGetUI (brugergrænsefladen, du bruger lige nu) og WinGet (en pakkemanager udviklet af Microsoft, som jeg ikke har relation med)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI er ved at blive opdateret. Når den er færdig, vil WingetUI genstarte sig selv", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI er gratis, og vil altid være gratis. Ingen reklamer, kreditkort, premium version. 100% gratis for altid.", "WingetUI log": "WingetUI log", "WingetUI tray application preferences": "WingetUI indstillinger for notifikationsområdet", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI bruger følgende biblioteker. Uden dem ville WingetUI ikke have været mulig.", "WingetUI version {0} is being downloaded.": "WingetUI version {0} er ved at blive hentet.", "WingetUI will become {newname} soon!": "WingetUI bliver snart til {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI vil ikke undersøge for opdateringer med mellemrum. De vil stadig blive undersøgt ved programstart, men du vil ikke blive varslet om dem.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI vil vise en UAC-prompt hver gang in pakke kræver forhøjede rettigheder for at blive installeret.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI vil snart blive navngivet {newname}. Dette vil ikke betyde nogle ændringer i programmet. Jeg (udvikleren) vil fortsætte udviklingen af dette projekt, men det vil være under et andet navn.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI ville ikke have været mulig uden støtten fra vores hjælpere. Kig forbi deres GitHub profiler, og støt dem.", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI ville ikke have været mulig uden støtten fra vores hjælpere. Tak til jer alle 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} er klar til at blive installeret.", "Write here the process names here, separated by commas (,)": null, "Yes": "<PERSON>a", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "Nuværende installerede version: {0}", "You have installed WingetUI Version {0}": "Installeret WingetUI version er {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "Installation af {pm} er nødvendig, for at kunne bruge det med WingetUI.", "You may restart your computer later if you wish": "Senere genstart af computer er fint", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Du vil kun blive spurgt en gang, og administrator<PERSON>tigheder vil blive givet til pakker, som kræver dem.", "You will be prompted only once, and every future installation will be elevated automatically.": "Du vil kun blive spurgt en gang, og enhver fremtidig installation vil få forhøjede rettigheder automatisk.", "You will likely need to interact with the installer.": "Du vil sandsynligvis være nødt til at interagere med installationsprogrammet.", "[RAN AS ADMINISTRATOR]": "KØRTE SOM ADMINISTRATOR", "buy me a coffee": "giv en kop kaffe", "extracted": "pakket ud", "feature": "funktion", "formerly WingetUI": "tidligere WingetUI", "homepage": "startside", "install": "installer", "installation": "installation", "installed": "installeret", "installing": "installere", "library": "bibliotek", "mandatory": null, "option": "indstilling", "optional": null, "uninstall": "a<PERSON><PERSON><PERSON><PERSON>", "uninstallation": "afinstallering", "uninstalled": "afinstalleret", "uninstalling": "afin<PERSON>lerer", "update(noun)": "opdatering", "update(verb)": "opda<PERSON><PERSON>", "updated": "opdateret", "updating": "opdaterer", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} A<PERSON><PERSON>ler", "{0} aborted": "{0} afb<PERSON>t", "{0} can be updated": "{0} kan opdateres", "{0} can be updated to version {1}": "{0} kan opdateres til version {1}", "{0} days": "{0} dage", "{0} desktop shortcuts created": "{0} skrivebordsgenveje oprettet", "{0} failed": "{0} fej<PERSON>e", "{0} has been installed successfully.": "{0} er blevet installeret med success.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} er blevet installeret med success. Det anbefales at genstarte UniGetUI for at færdiggøre installation", "{0} has failed, that was a requirement for {1} to be run": "{0} mis<PERSON><PERSON><PERSON><PERSON>, det var et krav for, at {1} kunne afvikles", "{0} homepage": "{0} startside", "{0} hours": "{0} timer", "{0} installation": "{0} installering", "{0} installation options": "{0} installerings muligheder", "{0} installer is being downloaded": "{0} installationsprogram bliver downloaded", "{0} is being installed": "{0} bliver installeret", "{0} is being uninstalled": "{0} bliver afinstalleret", "{0} is being updated": "{0} bliver opdateret", "{0} is being updated to version {1}": "{0} bliver opdateret til version {1}", "{0} is disabled": "{0} er slået fra", "{0} minutes": "{0} minutter", "{0} months": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} packages are being updated": "{0} pak<PERSON> der bliver opdateret", "{0} packages can be updated": "{0} pakker kan blive opdateret", "{0} packages found": "{0} pakker fundet", "{0} packages were found": "{0} pakker er blevet fundet", "{0} packages were found, {1} of which match the specified filters.": "{0} pakker fundet, {1} matchede det specificerede filter.", "{0} selected": null, "{0} settings": "{0} in<PERSON><PERSON><PERSON>", "{0} status": null, "{0} succeeded": "{0} l<PERSON><PERSON><PERSON>", "{0} update": "{0} opdater", "{0} updates are available": "{0} op<PERSON>ringer tilgængelige", "{0} was {1} successfully!": "{0} blev {1} vellykket!", "{0} weeks": "{0} uger", "{0} years": "{0} år", "{0} {1} failed": "{0} {1} fej<PERSON>e", "{package} Installation": "{package} installation", "{package} Uninstall": "{package} afinstallation", "{package} Update": "{package} opdatering", "{package} could not be installed": "{package} kunne ikke installeres", "{package} could not be uninstalled": "{package} kunne ikke afinstalleres", "{package} could not be updated": "{package} kunne ikke opdateres", "{package} installation failed": "{package} installation fejlede", "{package} installer could not be downloaded": "{package} installationsprogram kunne ikke downloades", "{package} installer download": "{package} installationsprogram download", "{package} installer was downloaded successfully": "{package} installationsprogram blev downloaded med success", "{package} uninstall failed": "{package} afinstallation fejlede", "{package} update failed": "{package} opdatering fejlede", "{package} update failed. Click here for more details.": "{package} opdatering fejlede. Klik her for flere detaljer.", "{package} was installed successfully": "Installation af {package} gennemført", "{package} was uninstalled successfully": "Afinstallation af {package} gennemført", "{package} was updated successfully": "{package} opdatering gennemført", "{pcName} installed packages": "{pcName} installerede pakker", "{pm} could not be found": "<PERSON><PERSON> ikke finde {pm}", "{pm} found: {state}": "{pm} fandt: {state}", "{pm} is disabled": "{pm} er deaktiveret", "{pm} is enabled and ready to go": "{pm} er aktiveret og klar til brug", "{pm} package manager specific preferences": "{pm} pakkemanager-specifik<PERSON> in<PERSON>", "{pm} preferences": "{pm} indstillinger", "{pm} version:": "{pm} version:", "{pm} was not found!": "{pm} blev ikke fundet!"}