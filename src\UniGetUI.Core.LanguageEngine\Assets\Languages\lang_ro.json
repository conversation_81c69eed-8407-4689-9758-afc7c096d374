{"\"{0}\" is a local package and can't be shared": "„{0}” este un pachet local și nu poate fi partajat", "\"{0}\" is a local package and does not have available details": "„{0}” este un pachet local și nu are detalii disponibile", "\"{0}\" is a local package and is not compatible with this feature": "„{0}” este un pachet local și nu este compatibil cu această funcție", "(Last checked: {0})": "(Ultima verificare: {0})", "(Number {0} in the queue)": "(Poziția {0} în coadă)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@lucadsign, @SilverGreen93, TZACANEL, @David735453", "0 packages found": "0 pachete găsite", "0 updates found": "0 actualizări găsite", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 zi", "1 hour": "o oră", "1 month": "o lună", "1 package was found": "1 pachet a fost găsit", "1 update is available": "1 actualizare este disponibilă", "1 week": "o săptămână", "1 year": "un an", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Navigheaz<PERSON> la pagina „{0}” sau „{1}”", "2 - Warnings": "2 - <PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Localizează pachetele pe care dorești să le adaugi în grup și bifează caseta cea mai din stânga.", "3 - Information (less)": "3 - Informații (mai puț<PERSON>)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON><PERSON> p<PERSON> pe care dorești să le adaugi la grup sunt selectate, apasă pe opțiunea „{0}” din bara de instrumente.", "4 - Information (more)": "4 - Informa<PERSON><PERSON> (mai multe)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Pachetele tale vor fi fost adăugate în grup. Poți continua să adaugi pachete sau să exporți grupul.", "5 - information (debug)": "5 - Informații (pentru depanare)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Un manager de biblioteci C/C++ popular. Plin de biblioteci C/C++ și alte utilitare conexe<br>Conține: <b>biblioteci C/C++ și utilitare conexe</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Un repository plin de unelte și executabile concepute pe baza ecosistemului Microsoft .NET.<br>Conține: <b>Unelte și scripturi legate de .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Un repository plin cu unelte proiectat cu ecosistemul Microsoft .NET în minte.<br>Conține: <b>Unelte înrudite cu .NET</b>", "A restart is required": "Repornirea este necesară", "Abort install if pre-install command fails": "Anulează instalarea dacă comanda pre-instalare eșuează.", "Abort uninstall if pre-uninstall command fails": "Anulează dezinstalarea dacă comanda pre-dezinstalare eșuează.", "Abort update if pre-update command fails": "Anulează actualizarea dacă comanda pre-actualizării eșuează.", "About": "<PERSON><PERSON><PERSON>", "About Qt6": "Despre Qt6", "About WingetUI": "Despre UniGetUI", "About WingetUI version {0}": "Despre versiunea UniGetUI {0}", "About the dev": "Des<PERSON><PERSON> dezvoltator", "Accept": "Acceptă", "Action when double-clicking packages, hide successful installations": "Acțiune la dublu-clic pe pachete, ascunde instalările cu succes", "Add": "Adaugă", "Add a source to {0}": "Adaugă o sursă la {0}", "Add a timestamp to the backup file names": "Adaugă data și ora în numele copiei de siguranță", "Add a timestamp to the backup files": "Adaugă data și ora fișierelor din copia de siguranță", "Add packages or open an existing bundle": "Adaugă pachete sau deschide un grup existent", "Add packages or open an existing package bundle": "Adaugă pachete sau deschide un set existent de pachete", "Add packages to bundle": "Adaugă pachete în grup", "Add packages to start": "Adaugă pachete pentru a începe", "Add selection to bundle": "Adaugă selecția la grup", "Add source": "<PERSON>ug<PERSON> sur<PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Adaugă actualizările care eșuează cu „nicio actualizare aplicabilă găsită” în lista de actualizări ignorate", "Adding source {source}": "Se adaugă sursa {source}", "Adding source {source} to {manager}": "Se adaugă sursa {source} la {manager}", "Addition succeeded": "Adăugarea a fost realizată", "Administrator privileges": "Privilegii administrator", "Administrator privileges preferences": "Preferințe privilegii administrator", "Administrator rights": "Drepturi administrator", "Administrator rights and other dangerous settings": "Drepturi de administrator ș<PERSON> alte setări peric<PERSON>", "Advanced options": "Opțiuni avansate", "All files": "<PERSON><PERSON>", "All versions": "Toate vers<PERSON><PERSON><PERSON>", "Allow changing the paths for package manager executables": "Permite schimbarea căilor pentru executabilele managerilor de pachete", "Allow custom command-line arguments": "Permite argumente personalizate în linia de comandă", "Allow importing custom command-line arguments when importing packages from a bundle": "Permite importarea argumentelor personalizate în linia de comandă la importarea pachetelor dintr-un grup", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Permite importarea comenzilor de pre-instalare și post-instalare la importarea unui pachet dintr-un grup", "Allow package operations to be performed in parallel": "Permite ca operațiile cu pachete să fie executate în paralel", "Allow parallel installs (NOT RECOMMENDED)": "Permite instalările paralele (NERECOMANDAT)", "Allow pre-release versions": "Permite versiuni beta", "Allow {pm} operations to be performed in parallel": "Permite operațiile {pm} să fie executate în paralel", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativ, poți de asemenea să instalezi {0} rulând următoarea comandă în Windows PowerShell:", "Always elevate {pm} installations by default": "Elevează mereu instalările {pm} în mod implicit", "Always run {pm} operations with administrator rights": "Rulează mereu operațiile {pm} cu drepturi de administrator", "An error occurred": "A apărut o eroare", "An error occurred when adding the source: ": "A apărut o eroare la adăugarea sursei:", "An error occurred when attempting to show the package with Id {0}": "A apărut o eroare la încercarea de afișare a pachetului cu ID {0}", "An error occurred when checking for updates: ": "A apărut o eroare la verificarea de actualizări:", "An error occurred while loading a backup: ": "A apărut o eroare la încărcarea copiei de siguranță:", "An error occurred while logging in: ": "A apărut o eroare la autentificare:", "An error occurred while processing this package": "A apărut o eroare în timpul procesării acestui pachet", "An error occurred:": "A apărut o eroare:", "An interal error occurred. Please view the log for further details.": "A apărut o eroare internă. Te rog să verifici jurnalul pentru detalii suplimentare.", "An unexpected error occurred:": "A apărut o eroare neașteptată:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "O problemă neașteptată a apărut în încercarea de a repara WinGet. Te rog încearcă mai târziu", "An update was found!": "A fost găsită o actualizare!", "Android Subsystem": "Subsistemul Android", "Another source": "Altă sursă", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Orice scurtături noi create în timpul unei instalări sau actualizări vor fi șterse automat, în loc de a arăta o întrebare prima dată ce sunt detectate.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Orice scurt<PERSON><PERSON>i create sau modificate în afara UniGetUI vor fi ignorate. Le vei putea adăuga cu butonul {0}.", "Any unsaved changes will be lost": "Orice modificări nesalvate vor fi pierdute", "App Name": "Nume aplicație", "Appearance": "Aspect", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON> ap<PERSON>i, pagina de pornire, pictogram<PERSON>, elimină instalările reușite automat", "Application theme:": "Temă aplicație:", "Apply": "Aplică", "Architecture to install:": "Arhitectura de instalat:", "Are these screenshots wron or blurry?": "Sunt aceste capturi de ecran greșite sau neclare?", "Are you really sure you want to enable this feature?": "Ești sigur că dorești să activezi această caracteristică?", "Are you sure you want to create a new package bundle? ": "Ești sigur că dorești să creezi un nou grup de pachete?", "Are you sure you want to delete all shortcuts?": "Ești sigur că dorești să ștergi toate scurt<PERSON><PERSON><PERSON>?", "Are you sure?": "Ești sigur?", "Ascendant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ask for administrator privileges once for each batch of operations": "Solicită drepturi de administrator o singură dată pentru fiecare serie de operații", "Ask for administrator rights when required": "Solicită drepturi de administrator când este necesar", "Ask once or always for administrator rights, elevate installations by default": "Solicită o dată sau de mai multe ori drepturi de administrator, elevează instalările în mod implicit", "Ask only once for administrator privileges": "<PERSON><PERSON> doar o singură dată drepturi de administrator", "Ask only once for administrator privileges (not recommended)": "Solicită doar o singură dată privilegii de administrator (nerecomandat)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Întreabă pentru a șterge scurtăturile din spațiul de lucru create în timpul instalării sau actualizării.", "Attention required": "Este necesară atenție", "Authenticate to the proxy with an user and a password": "Autentifică-te la acest proxy cu un nume de utilizator și o parolă", "Author": "Autor", "Automatic desktop shortcut remover": "Eliminarea automată a scurtăturilor de pe spațiul de lucru", "Automatically save a list of all your installed packages to easily restore them.": "Salvează automat o listă a tuturor pachetelor instalate pentru a putea să le restaurezi cu ușurință", "Automatically save a list of your installed packages on your computer.": "Salvează automat o listă a pachetelor instalate pe calculatorul tău.", "Autostart WingetUI in the notifications area": "Pornește automat UniGetUI în zona de notificări", "Available Updates": "Actualizări disponibile", "Available updates: {0}": "Actualizări disponibile: {0}", "Available updates: {0}, not finished yet...": "Actualiz<PERSON><PERSON> disponibile: {0}, c<PERSON><PERSON><PERSON> în curs...", "Backing up packages to GitHub Gist...": "Se face un backup al pachetelor pe GitHub Gist...", "Backup": "Backup", "Backup Failed": "Backup-ul a <PERSON>t", "Backup Successful": "Backup-ul s-a finalizat cu succes", "Backup and Restore": "Backup și restaurare", "Backup installed packages": "Fă un backup al pachetelor instalate", "Backup location": "Locația copiei de siguranță", "Become a contributor": "<PERSON><PERSON> un contribuitor", "Become a translator": "<PERSON><PERSON> un traducător", "Begin the process to select a cloud backup and review which packages to restore": "Începe procesul de a selecta un backup din cloud și revizuiește ce pachete să restaurezi", "Beta features and other options that shouldn't be touched": "Funcții beta și alte opțiuni care nu ar trebui atinse", "Both": "Amândouă", "Bundle security report": "Raport de securitate grup", "But here are other things you can do to learn about WingetUI even more:": "Dar aici sunt alte lucruri pe care le poți face pentru a învăța despre UniGetUI și multe altele:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Dezactivând un manager de pachete, nu vei mai putea să vezi sau să actualizezi pachetele sale.", "Cache administrator rights and elevate installers by default": "Ține minte drepturile de administrator și elevează instalările în mod implicit", "Cache administrator rights, but elevate installers only when required": "Ține minte drepturile de administrator, dar elevează instalările doar când e necesar", "Cache was reset successfully!": "Cache-ul a fost resetat cu succes!", "Can't {0} {1}": "Nu se poate {0} {1}", "Cancel": "Anulează", "Cancel all operations": "<PERSON><PERSON><PERSON><PERSON> toate operațiile", "Change backup output directory": "Schimbă dosarul pentru copia de siguranță", "Change default options": "Schimbă opțiunile implicite", "Change how UniGetUI checks and installs available updates for your packages": "Schimbă modul în care UniGetUI verifică și instalează actualizările disponibile pentru pachetele tale", "Change how UniGetUI handles install, update and uninstall operations.": "Schimbă modul cum abordează UniGetUI operațiile de instalare, actualizare și dezinstalare.", "Change how UniGetUI installs packages, and checks and installs available updates": "Schimbă modul cum UniGetUI instalează, verifică și actualizează pachetele", "Change how operations request administrator rights": "Schimbă modul cum operațiile cer drepturi de administrator", "Change install location": "Schimbă locația instalării", "Change this": "<PERSON><PERSON><PERSON><PERSON> asta", "Change this and unlock": "Schimbă asta și deblochează", "Check for package updates periodically": "Verifică pentru actualiză<PERSON> p<PERSON>", "Check for updates": "Verifică pentru actualizări", "Check for updates every:": "Verifică pentru actualizări la fiecare:", "Check for updates periodically": "Verifică pentru actualizări periodic", "Check for updates regularly, and ask me what to do when updates are found.": "Verifică pentru actualizări periodic și întreabă-mă ce să fac dacă se găsesc actualizări.", "Check for updates regularly, and automatically install available ones.": "Verifică pentru actualizări periodic și instalează automat actualizările disponibile.", "Check out my {0} and my {1}!": "Verific<PERSON> {0}-ul meu și {1}-ul meu!", "Check out some WingetUI overviews": "Verifică câteva review-uri ale UniGetUI", "Checking for other running instances...": "Se verifică dacă se rulează alte instanțe...", "Checking for updates...": "Se caută actualizări...", "Checking found instace(s)...": "Se verifică instanțele găsite...", "Choose how many operations shouls be performed in parallel": "Alege câte operații se vor executa în paralel", "Clear cache": "Golește cache-ul", "Clear finished operations": "Elimină operațiunile finalizate", "Clear selection": "Anulează selecția", "Clear successful operations": "Curăță operațiile care au avut succes", "Clear successful operations from the operation list after a 5 second delay": "Curăță operațiile terminate cu succes din lista de operații după 5 secunde", "Clear the local icon cache": "Golește cache-ul de pictograme local", "Clearing Scoop cache - WingetUI": "Se curăță cache-ul pentru Scoop - UniGetUI", "Clearing Scoop cache...": "Se golește cache-ul pentru Scoop...", "Click here for more details": "Clic aici pentru mai multe detalii", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Clic pe Instalează pentru a începe procesul de instalare. Dacă vei omite instalarea, UniGetUI ar putea să nu funcționeze așa cum te-ai aștepta.", "Close": "<PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Închide UniGetUI în tăvița sistemului", "Close WingetUI to the notification area": "Închide UniGetUI în zona de notificări", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Copia de siguranță în cloud folosește un GitHub Gist privat pentru a stoca o listă a pachetelor instalate", "Cloud package backup": "Backup pachete în cloud", "Command-line Output": "Ieșire din linia de comandă", "Command-line to run:": "Linia de comandă de rulat:", "Compare query against": "Compară interogarea cu", "Compatible with authentication": "Compatibil cu autentificarea", "Compatible with proxy": "Compatibil cu proxy", "Component Information": "Informații componentă", "Concurrency and execution": "Concurență și execuție", "Connect the internet using a custom proxy": "Conectează-te la internet folosind un proxy personalizat", "Continue": "Con<PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Contribuie la repository-ul pictogramei și capturii de ecran", "Contributors": "Contribuitori", "Copy": "Copiază", "Copy to clipboard": "Copiază în clipboard", "Could not add source": "Nu s-a putut adăuga sursa", "Could not add source {source} to {manager}": "Nu s-a putut adăuga sursa {source} la {manager}", "Could not back up packages to GitHub Gist: ": "Nu s-au putut copia pachetele în GitHub Gist:", "Could not create bundle": "Nu s-a putut crea grupul", "Could not load announcements - ": "Nu s-au putut încărca anunțurile - ", "Could not load announcements - HTTP status code is $CODE": "Nu s-au putut încărca anunțurile - Codul de stare HTTP este $CODE", "Could not remove source": "Nu s-a putut elimina sursa", "Could not remove source {source} from {manager}": "Nu s-a putut elimina sursa {source} din {manager}", "Could not remove {source} from {manager}": "Nu s-a putut elimina {source} din {manager}", "Credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Current Version": "<PERSON><PERSON><PERSON><PERSON>", "Current status: Not logged in": "Stare curentă: Neautentificat", "Current user": "Utilizatorul curent", "Custom arguments:": "Argumente particularizate:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Argumentele personalizate în linie de comandă pot schimba modul în care programul este instalat, actualizat sau dezinstalat într-un mod pe care UniGetUI nu îl poate controla. Folosind argumente în linie de comandă poate strica pachetele. Continuă cu atenție.", "Custom command-line arguments:": "Argumente particularizate în linie de comandă:", "Custom install arguments:": "Argumente de instalare personalizate:", "Custom uninstall arguments:": "Argumente de dezinstalare personalizate:", "Custom update arguments:": "Argumente de actualizare personalizate:", "Customize WingetUI - for hackers and advanced users only": "Particularizează UniGetUI - doar pentru utilizatori avansați", "DEBUG BUILD": "BUILD DEPANARE", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "NOTĂ DE INFORMARE: NU SUNTEM RESPONSAB<PERSON>I PENTRU PACHETELE DESCĂRCATE. TE ROG SĂ FII SIGUR CĂ INSTALEZI DOAR SOFTWARE ÎN CARE AI ÎNCREDERE.", "Dark": "Întunecat", "Decline": "Refuză", "Default": "Implicit", "Default installation options for {0} packages": "Opțiunile implicite de instalare pentru {0} pachete", "Default preferences - suitable for regular users": "Preferințe implicite - potrivite pentru utilizatori obișnuiți", "Default vcpkg triplet": "Triplet vcpkg implicit", "Delete?": "<PERSON><PERSON><PERSON>?", "Dependencies:": "Dependințe:", "Descendant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Description:": "Descriere:", "Desktop shortcut created": "Scurtătură pe spațiul de lucru creată", "Details of the report:": "Detaliile raportului:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Dezvoltarea este dificilă și această aplicație este gratuită. Dar dacă îți place aplicația, î<PERSON> po<PERSON>i <b>cump<PERSON>ra oricând o cafea</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Instalează direct când faci dublu-clic pe un element din fila „{discoveryTab}” (în loc de a afișa informațiile despre pachet)", "Disable new share API (port 7058)": "Dezactivează API-ul nou de partajare (portul 7058)", "Disable the 1-minute timeout for package-related operations": "Dezactivează întârzierea de 1 minut pentru operațiile legate de pachete", "Disclaimer": "Negarea responsabilității", "Discover Packages": "Des<PERSON><PERSON><PERSON> p<PERSON>", "Discover packages": "Des<PERSON><PERSON><PERSON> p<PERSON>", "Distinguish between\nuppercase and lowercase": "Distingeți între  \nmajuscule și minuscule", "Distinguish between uppercase and lowercase": "Distingeți între majuscule și minuscule", "Do NOT check for updates": "NU verifica pentru actualizări", "Do an interactive install for the selected packages": "Efectuază o instalare interactivă pentru pachetele selectate", "Do an interactive uninstall for the selected packages": "Efectuază o dezinstalare interactivă pentru pachetele selectate", "Do an interactive update for the selected packages": "Efectuază o actualizare interactivă pentru pachetele selectate", "Do not automatically install updates when the battery saver is on": "Nu instala actualizările automat când economizorul de baterie este activat", "Do not automatically install updates when the network connection is metered": "Nu instala actualizările automat când conexiunea la internet este monitorizată", "Do not download new app translations from GitHub automatically": "Nu descărca noi traduceri ale aplicației de pe GitHub automat", "Do not ignore updates for this package anymore": "Nu mai ignora actualizările pentru acest pachet", "Do not remove successful operations from the list automatically": "Nu elimina automat operațiile terminate cu succes din listă", "Do not show this dialog again for {0}": "Nu mai afi<PERSON>a acest dialog pentru {0}", "Do not update package indexes on launch": "Nu actualiza indexul de pachete la pornire", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Accepți ca UniGetUI să colecteze și să trimită statistici de utilizare anonime, cu unicul scop de a înțelege și îmbunătăți experiența utilizatorilor?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "UniGetUI îți este util? Dacă poți, sprijină munca mea pentru a putea continua să fac UniGetUI cea mai bună interfață grafică pentru managerele de pachete.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "UniGetUI ți se pare util? Dorești să sprijini dezvoltatorul? Dac<PERSON> da, poți {0}, ar ajuta foarte mult!", "Do you really want to reset this list? This action cannot be reverted.": "Chiar vrei să resetezi această listă? Această acțiune nu poate fi anulată.", "Do you really want to uninstall the following {0} packages?": "Si<PERSON>r dorești să dezinstalezi următoarele {0} pachete?", "Do you really want to uninstall {0} packages?": "Chiar dorești să dezinstalezi {0} pachete?", "Do you really want to uninstall {0}?": "Chiar dorești să dezinstalezi {0}?", "Do you want to restart your computer now?": "Dorești să repornești calculatorul acum?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Dorești să traduci UniGetUI în limba ta? Află cum poți contribui <a style=\"color:{0}\" href=\"{1}\"a>AICI!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Nu ai vrea să donezi încă? Nu-ți face probleme, poți măcar să partajezi UniGetUI cu prietenii. Împrăștie vorba depsre UniGetUI!", "Donate": "Donează", "Done!": "Gata!", "Download failed": "Descărcarea a eșuat", "Download installer": "Descarcă instalatorul", "Download operations are not affected by this setting": "Operațiile de descărcare nu sunt afectate de această setare", "Download selected installers": "Descarcă instalatoarele selectate", "Download succeeded": "Descărcarea a fost realizată", "Download updated language files from GitHub automatically": "Descarcă automat fișiere lingvistice actualizate de pe GitHub", "Downloading": "Se descarcă", "Downloading backup...": "Se descarcă copia de siguranță...", "Downloading installer for {package}": "Se descarcă instalatorul pentru {package}", "Downloading package metadata...": "Se descarcă metadatele pachetelor...", "Enable Scoop cleanup on launch": "Activează curățarea pentru Scoop la pornire", "Enable WingetUI notifications": "Activează notificările de la UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Activează un depanator îmbunătățit WinGet [experimental]", "Enable and disable package managers, change default install options, etc.": "Activează și dezactivează manageri de pachete, schimbă opțiunile implicite de instalare etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Activarea optimizărilor în fundal ale utilizării CPU (vezi Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Activează API în fundal (Widgets for UniGetUI and Sharing, port 7058)", "Enable it to install packages from {pm}.": "Activează-l pentru a instala pachete de la {pm}.", "Enable the automatic WinGet troubleshooter": "Activează depanatorul automat pentru WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Activează noul elevator UAC al UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Activează noul mod de manipulare a intrării procesului (închizător de stdin automat)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Activează setările de mai jos numai și numai dacă înțelegi deplin ceea ce fac și implicațiile pe care le pot avea.", "Enable {pm}": "Activează {pm}", "Enter proxy URL here": "Introdu URL proxy aici", "Entries that show in RED will be IMPORTED.": "Intrările afișate cu ROȘU vor fi IMPORTATE.", "Entries that show in YELLOW will be IGNORED.": "Intrările afișate cu GALBEN for fi IGNORATE.", "Error": "Eroare", "Everything is up to date": "Totul este la zi", "Exact match": "<PERSON><PERSON><PERSON><PERSON>", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Scurtăturile existente pe Desktop vor fi scanate și va trebui să alegi pe care le păstrezi și pe care le ștergi.", "Expand version": "<PERSON>tinde versiunea", "Experimental settings and developer options": "Setări experimentale și opțiuni dezvoltator", "Export": "Exportă", "Export log as a file": "Exportă jurnal ca fi<PERSON>ier", "Export packages": "Exportă pache<PERSON>", "Export selected packages to a file": "Exportă pachetele selectate într-un fișier", "Export settings to a local file": "Exportă setările într-un fișier local", "Export to a file": "Exportă într-un fișier", "Failed": "<PERSON><PERSON><PERSON><PERSON>", "Fetching available backups...": "Se preiau copiile de siguranță disponibile...", "Fetching latest announcements, please wait...": "Se preiau ultimele anun<PERSON>, te rog așteaptă...", "Filters": "Filtre", "Finish": "Terminare", "Follow system color scheme": "Urmează tema de culoare a sistemului", "Follow the default options when installing, upgrading or uninstalling this package": "Urmează opțiunile implicite la instalarea, actualizarea sau dezinstalarea acestui pachet.", "For security reasons, changing the executable file is disabled by default": "Din motive de securitate, schimbarea fișierului executabil este implicit dezactivată", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Din motive de securitate, argumentele personalizate din linia de comandă sunt implicit dezactivate. Mergi la setările de securitate ale UniGetUI pentru a schimba acest lucru.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Din motive de securitate, scripturile pre-operație și post-operație sunt implicit dezactivate. Mergi la setările de securitate ale UniGetUI pentru a schimba acest lucru.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Forțează versiunea winget compilată pentru ARM (DOAR PENTRU SISTEMELE ARM64)", "Formerly known as WingetUI": "Cunoscut anterior ca WingetUI", "Found": "<PERSON><PERSON><PERSON><PERSON>", "Found packages: ": "Pachete găsite:", "Found packages: {0}": "Pachete găsite: {0}", "Found packages: {0}, not finished yet...": "Pachete găsite: {0}, c<PERSON><PERSON><PERSON> în curs...", "General preferences": "Preferințe generale", "GitHub profile": "<PERSON><PERSON>", "Global": "Global", "Go to UniGetUI security settings": "Mergi la setările de securitate UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Repository excelent de utilități necunoscute, dar utile, și alte pachete interesante.<br>Conține: <b><PERSON><PERSON><PERSON><PERSON>, programe în linie de comandă, software general (găleata extra necesară)</b>", "Great! You are on the latest version.": "Minunat! Folosești cea mai nouă versiune.", "Grid": "Grilă", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Ajutor și documentație", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "De aici poți schimba comportamentul UniGetUI legat de scurtături. Bifând o scurtătură vei face ca UniGetUI să o șteargă dacă ea va fi creată la o actualizare viitoare. Debifând-o, vei păstra scurtătura intactă.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Salut! Numele meu este Martí și sunt <i>dezvoltatorul</i> programului UniGetUI. UniGetUI a fost făcut în întregime în timpul meu liber!", "Hide details": "Ascunde detaliile", "Homepage": "Pagina <PERSON>ă", "Hooray! No updates were found.": "Ura! Nu a fost găsită nicio actualizare!", "How should installations that require administrator privileges be treated?": "Cum ar trebui să fie tratate instalările care necesită privilegii de administrator?", "How to add packages to a bundle": "Cum să adaugi pachete într-un grup", "I understand": "Am înțeles", "Icons": "Pictograme", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Dacă ai activat backup în cloud, acesta va fi salvat ca un GitHub Gist în acest cont", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignoră comenzile pre-instalare și post-instalare cînd se importă pachetele dintr-un grup", "Ignore future updates for this package": "Ignoră actualizările viitoare pentru acest pachet", "Ignore packages from {pm} when showing a notification about updates": "Ignoră pachetele din {pm} când este arătată o notificare despre actualizări", "Ignore selected packages": "Ignoră pachetele selectate", "Ignore special characters": "Ignoră caracterele speciale", "Ignore updates for the selected packages": "Ignoră actualizările pentru pachetele selectate", "Ignore updates for this package": "Ignoră actualizările pentru acest pachet", "Ignored updates": "Ignoră actualizările", "Ignored version": "Versiune ignorată", "Import": "Importă", "Import packages": "Importă p<PERSON>", "Import packages from a file": "Importă pachete din fișier", "Import settings from a local file": "Importă setări dintr-un fișier local", "In order to add packages to a bundle, you will need to: ": "Pentru a putea adăuga pachete într-un grup va trebui să:", "Initializing WingetUI...": "Se inițializează UniGetUI...", "Install": "Instalează", "Install Scoop": "Instalea<PERSON><PERSON>", "Install and more": "Instalează și mai multe", "Install and update preferences": "Preferințe instalare și actualizare", "Install as administrator": "Instalează ca administrator", "Install available updates automatically": "Instalează actualizările disponibile automat", "Install location can't be changed for {0} packages": "Locația instalării nu poate fi schimbată pentru {0} pachete", "Install location:": "Locația instalării:", "Install options": "Opțiuni instalare", "Install packages from a file": "Instalează pachete din fișier", "Install prerelease versions of UniGetUI": "Instalează versiuni în fază de testare ale UniGetUI", "Install selected packages": "Instalează pachetele selectate", "Install selected packages with administrator privileges": "Instalează pachetele selectate cu privilegii de administrator", "Install selection": "Instalează selecția", "Install the latest prerelease version": "Instalează ultima versiune beta", "Install updates automatically": "Instalează actualizările automat", "Install {0}": "Instalează {0}", "Installation canceled by the user!": "Instalarea a fost anulată de utilizator!", "Installation failed": "Instalarea a eșuat", "Installation options": "Opțiuni instalare", "Installation scope:": "Domeniu instalare:", "Installation succeeded": "Instalarea s-a realizat", "Installed Packages": "Pachete instalate", "Installed Version": "Versiune instalată", "Installed packages": "Pachete instalate", "Installer SHA256": "Instalator SHA256", "Installer SHA512": "Instalator SHA512", "Installer Type": "Tip Instalator", "Installer URL": "Instalator URL", "Installer not available": "Instalatorul nu este disponibil", "Instance {0} responded, quitting...": "Instanța {0} a r<PERSON><PERSON><PERSON>, se <PERSON>nchide...", "Instant search": "Căutare instantă", "Integrity checks can be disabled from the Experimental Settings": "Verificările de integritate pot fi dezactivate din setările experimentale", "Integrity checks skipped": "Verificările de integritate au fost omise", "Integrity checks will not be performed during this operation": "Verificările de integritate nu vor fi făcute în timpul acestei operații", "Interactive installation": "Instalare interactivă", "Interactive operation": "Operație interactivă", "Interactive uninstall": "Dezinstalare interactivă", "Interactive update": "Actualizare interactivă", "Internet connection settings": "<PERSON><PERSON><PERSON> cone<PERSON> la internet", "Is this package missing the icon?": "Lipsește pictograma acestui pachet?", "Is your language missing or incomplete?": "Lipsește limba ta sau este incompletă?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Nu este garantat că acreditările oferite vor fi stocate în siguranță, deci ai putea să nu folosești datele contului tău bancar.", "It is recommended to restart UniGetUI after WinGet has been repaired": "Este recomandat să repornești UniGetUI după ce WinGet a fost reparat", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Este recomandat cu tărie să reinstalezi UniGetUI pentru a adresa această situație.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Se pare că WinGet nu funcționează corect. Dorești să încerci să repari WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Se pare că ai rulat UniGetUI ca administrator, lucru care nerecomandat. Poți folosi programul și astfel, dar este recomandat să nu rulezi UniGetUI ca administrator. Apasă pe „{showDetails}” pentru a vedea cauza.", "Language": "Limbă", "Language, theme and other miscellaneous preferences": "Limbă, temă și alte preferințe diverse", "Last updated:": "Ultima dată actualizat:", "Latest": "Ultima", "Latest Version": "Ultima versiune", "Latest Version:": "Ultima versiune:", "Latest details...": "Ultime<PERSON> detalii...", "Launching subprocess...": "Se lansează subprocesul...", "Leave empty for default": "Lasă liber pentru valoarea implicită", "License": "Licență", "Licenses": "Licențe", "Light": "Luminos", "List": "Listă", "Live command-line output": "Ieșirea liniei de comandă în timp real", "Live output": "Output în timp real", "Loading UI components...": "Se încarcă componentele de interfață...", "Loading WingetUI...": "Se încarcă UniGetUI...", "Loading packages": "Se încarcă pachetele", "Loading packages, please wait...": "Se încarcă pache<PERSON>e, te rog așteaptă...", "Loading...": "Se încarcă...", "Local": "Local", "Local PC": "PC-ul local", "Local backup advanced options": "Opțiuni avansate backup local", "Local machine": "Mașina locală", "Local package backup": "Backup pachete local", "Locating {pm}...": "Se localizează {pm}...", "Log in": "Autentifică-te", "Log in failed: ": "Autentificarea a eșuat:", "Log in to enable cloud backup": "Autentifică-te pentru a activa backup în cloud", "Log in with GitHub": "Autentifică-te cu GitHub", "Log in with GitHub to enable cloud package backup.": "Autentifică-te cu GitHub pentru a activa backup de pachete în cloud.", "Log level:": "<PERSON><PERSON> j<PERSON>:", "Log out": "Deconectează-te", "Log out failed: ": "Deconectarea a eșuat:", "Log out from GitHub": "Deconectează-te de la GitHub", "Looking for packages...": "Se caută pachete...", "Machine | Global": "Mașină | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Argumentele în linie de comandă malformate pot strica pachetele sau chiar permite unui actor mali<PERSON><PERSON> să obțină acces privilegiat. Așadar, importarea argumentelor personalizate în linie de comandă este implicit dezactivată.", "Manage": "Administrează", "Manage UniGetUI settings": "Administrează setările UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Configurează pornirea automată a UniGetUI din Setările sistemului de operare", "Manage ignored packages": "Administrează pachetele ignorate", "Manage ignored updates": "Administrează actualizările ignorate", "Manage shortcuts": "Administrează scurtăturile", "Manage telemetry settings": "Administrează setări telemetrie", "Manage {0} sources": "Administrează {0} surse", "Manifest": "Manifest", "Manifests": "Manifesturi", "Manual scan": "Scanare manuală", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Managerul de pachete oficial al Microsoft. Plin de pachete arhicunoscute și verificate.<br>Conține: <b>Software general, aplicații Microsoft Store</b>", "Missing dependency": "Dependin<PERSON><PERSON>", "More": "<PERSON> mult", "More details": "<PERSON> multe de<PERSON>ii", "More details about the shared data and how it will be processed": "Mai multe detalii despre datele partajate și cum vor fi acestea procesate", "More info": "Mai multe informații", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOTĂ: Acest depanator poate fi dezactivat din setările UniGetUI, din secțiunea WinGet", "Name": "Nume", "New": "Nou", "New Version": "Versiune nouă", "New bundle": "Grup nou", "New version": "Versiune nouă", "Nice! Backups will be uploaded to a private gist on your account": "Fain! Backup-urile se vor încărca automat într-un gist privat în contul tău", "No": "<PERSON>u", "No applicable installer was found for the package {0}": "Niciun instalator aplicabil nu a fost găsit pentru pachetul {0}", "No dependencies specified": "<PERSON>cio dependință specificată", "No new shortcuts were found during the scan.": "În timpul scanării nu au fost găsite noi scurtături.", "No packages found": "<PERSON>u s-au g<PERSON><PERSON><PERSON> pachete", "No packages found matching the input criteria": "Nu s-au găsit pachete care corespund criteriilor introduse", "No packages have been added yet": "<PERSON><PERSON>un pachet nu a fost adăugat încă", "No packages selected": "<PERSON><PERSON><PERSON> pachet selectat", "No packages were found": "Nu au fost g<PERSON><PERSON> pachete", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "<PERSON>cio informație personală nu este colectată sau trimisă, iar datele colectate sunt anonimizate, deci nu pot fi asociate cu tine.", "No results were found matching the input criteria": "Nu s-au găsit rezultate potrivite cu criteriile selectate", "No sources found": "Nu s-au găsit surse", "No sources were found": "Nu s-au găsit surse", "No updates are available": "Nu sunt disponibile actualizări", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Managerul de pachete al lui Node JS. Plin de biblioteci și alte utilitare care orbitează lumea javascript<br>Conține: <b>Biblioteci javascript Node și alte utilitare conexe</b>", "Not available": "Indisponibil", "Not finding the file you are looking for? Make sure it has been added to path.": "Nu găsești fișierul pe care îl cauți? Asigură-te că a fost adăugat în cale.", "Not found": "Nu este găsit", "Not right now": "Nu chiar acum", "Notes:": "Notițe:", "Notification preferences": "Preferințe notificări", "Notification tray options": "Opțiuni zonă notificări", "Notification types": "Tipuri de notificări", "NuPkg (zipped manifest)": "NuPkg (manifest zip)", "OK": "<PERSON>e", "Ok": "<PERSON>e", "Open": "Deschide", "Open GitHub": "Deschide GitHub", "Open UniGetUI": "Deschide UniGetUI", "Open UniGetUI security settings": "Deschide setările de securitate ale UniGetUI", "Open WingetUI": "Deschide UniGetUI", "Open backup location": "Deschide locația copiei de siguranță", "Open existing bundle": "Deschide grup existent", "Open install location": "Deschide locația instalării", "Open the welcome wizard": "Deschide asistentul pentru instalare", "Operation canceled by user": "Operația a fost anulată de utilizator", "Operation cancelled": "Opera<PERSON><PERSON>", "Operation history": "Istoric operații", "Operation in progress": "Operație în curs", "Operation on queue (position {0})...": "Operație în coadă (poziția {0})...", "Operation profile:": "Profil operațiuni:", "Options saved": "Opțiunile au fost salvate", "Order by:": "Ordonea<PERSON><PERSON> după:", "Other": "Altele", "Other settings": "<PERSON><PERSON>", "Package": "<PERSON><PERSON><PERSON>", "Package Bundles": "Grupuri aplicații", "Package ID": "<PERSON> pachet", "Package Manager": "Manager p<PERSON><PERSON>", "Package Manager logs": "Jurnale manager de p<PERSON>", "Package Managers": "<PERSON><PERSON>", "Package Name": "<PERSON><PERSON> pachet", "Package backup": "Backup pachet", "Package backup settings": "<PERSON><PERSON><PERSON> backup p<PERSON><PERSON>", "Package bundle": "Grup pachete", "Package details": "<PERSON><PERSON><PERSON> p<PERSON>t", "Package lists": "Liste de pachete", "Package management made easy": "Gestionarea pachetelor simplificată", "Package manager": "Manager <PERSON><PERSON>", "Package manager preferences": "Preferințe manager <PERSON><PERSON><PERSON>", "Package managers": "<PERSON><PERSON>", "Package not found": "Pachetul nu a fost găsit", "Package operation preferences": "Preferințe operații cu pachete", "Package update preferences": "Preferințe actualizări <PERSON>", "Package {name} from {manager}": "Pache<PERSON>l {name} din {manager}", "Package's default": "<PERSON><PERSON><PERSON><PERSON>", "Packages": "Pachete", "Packages found: {0}": "Pachete disponibile: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON>", "Password": "Pa<PERSON><PERSON>", "Paste a valid URL to the database": "Lipește un URL valid în baza de date", "Pause updates for": "Sistează actualizările pentru", "Perform a backup now": "Fă un backup acum", "Perform a cloud backup now": "Realizează un backup în cloud acum", "Perform a local backup now": "Realizează un backup local acum", "Perform integrity checks at startup": "Realizează verificări de integritate la pornire", "Performing backup, please wait...": "Se face un backup, te rog așteaptă...", "Periodically perform a backup of the installed packages": "Realizează periodic un backup al pachetelor instalate", "Periodically perform a cloud backup of the installed packages": "Realizează periodic un backup în cloud al pachetelor instalate", "Periodically perform a local backup of the installed packages": "Realizează periodic un backup local al pachetelor instalate", "Please check the installation options for this package and try again": "Te rog verifică opțiunile de instalare ale acestui pachet și încearcă din nou", "Please click on \"Continue\" to continue": "Te rog apasă pe „Continuă” pentru a continua", "Please enter at least 3 characters": "Te rog introdu cel puțin 3 caractere", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Ia aminte că anumite pachete nu pot fi instalate din cauza managerelor de pachete activate pe acest sistem.", "Please note that not all package managers may fully support this feature": "Te rog ia aminte că nu toți managerii de pachete pot suporta această funcție", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Ia aminte că pachetele din anumite surse pot să nu fie exportabile. Ele au fost marcate cu gri și nu vor fi exportate.", "Please run UniGetUI as a regular user and try again.": "Te rog rulează UniGetUI ca un utilizator normal și încearcă din nou", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Te rog uită-te în output-ul liniei de comandă sau vezi istoricul operațiilor pentru mai multe informații legate de problemă.", "Please select how you want to configure WingetUI": "Te rog alege cum dorești să configurezi UniGetUI", "Please try again later": "Te rog încearcă din nou mai târziu", "Please type at least two characters": "Te rog tastează cel puțin două caractere", "Please wait": "Te rog așteaptă", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Te rog așteaptă cât timp {0} se instalează. O fereastră neagră ar putea apărea. Așteaptă până când aceasta va dispărea.", "Please wait...": "Te rog așteaptă...", "Portable": "Portabil", "Portable mode": "Mod portabil", "Post-install command:": "Comandă post-instalare:", "Post-uninstall command:": "Comandă post-dezinstalare:", "Post-update command:": "Comandă post-actualizare:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Managerul de pachete al PowerShell. Găsește biblioteci și scripturi pentru a extinde capabilitățile PowerShell<br>Conține: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Comenzile pre și post instalare pot face luc<PERSON><PERSON> rele pentru dispozitivul tău dacă sunt făcute să facă astfel. Este foarte periculos să imporți o comandă dintr-un grup dacă nu ai încredere în proveniența grupului.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Comenzile pre și post instalare vor fi rulate înainte și după ce un pachet a fost instalat, actualizat sau dezinstalat. Fii conștient că acestea pot strica lucruri dacă nu sunt utilizate cu grijă.", "Pre-install command:": "Comandă pre-instalare:", "Pre-uninstall command:": "Comandă pre-dezinstalare:", "Pre-update command:": "Comandă pre-actualizare:", "PreRelease": "Versiune preliminară", "Preparing packages, please wait...": "Se pregătesc pache<PERSON>e, te rog așteaptă...", "Proceed at your own risk.": "Continuă pe riscul tău.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Interzice orice tip de elevare via elevatorul UniGetUI sau GSudo", "Proxy URL": "URL proxy", "Proxy compatibility table": "Tabel compatibilitate proxy", "Proxy settings": "<PERSON><PERSON><PERSON> proxy", "Proxy settings, etc.": "<PERSON><PERSON><PERSON> proxy etc.", "Publication date:": "Data publicării:", "Publisher": "Editor", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Managerul bibliotecii Python. Plin de biblioteci Python și alte utilitare legate de Python<br>Conține: <b>Biblioteci Python și utilități aferente</b>", "Quit": "Ieșire", "Quit WingetUI": "Închide UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Redu întrbările UAC, elevează instalările în mod implicit, deblochează anumite funcții periculoase etc.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Fă referire la jurnalele UniGetUI pentru a obține mai multe detalii despre fișierele afectate", "Reinstall": "Reinstalează", "Reinstall package": "Reinstalează pachet", "Related settings": "<PERSON><PERSON><PERSON>", "Release notes": "Informații versiune", "Release notes URL": "URL informații versiune", "Release notes URL:": "URL informații versiune:", "Release notes:": "Informații versiune:", "Reload": "Re<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reload log": "Reîncarcă jurnalul", "Removal failed": "Eliminarea a eșuat", "Removal succeeded": "Eliminarea a avut succes", "Remove from list": "Elimină din listă", "Remove permanent data": "Elimină datele permanente", "Remove selection from bundle": "Elimină selecția din grup", "Remove successful installs/uninstalls/updates from the installation list": "Elimină instalările/dezinstalările/actualizările realizate din lista de instalare", "Removing source {source}": "Se elimină sursa {source}", "Removing source {source} from {manager}": "Se elimină sursa {source} din {manager}", "Repair UniGetUI": "Repară UniGetUI", "Repair WinGet": "Repară WinGet", "Report an issue or submit a feature request": "Raportează o problemă sau creează o cerere pentru o funcție nouă", "Repository": "Repository", "Reset": "Resetează", "Reset Scoop's global app cache": "Resetează cache-ul global de aplicații Scoop", "Reset UniGetUI": "Resetează UniGetUI", "Reset WinGet": "Resetează WinGet", "Reset Winget sources (might help if no packages are listed)": "Resetează sursele Winget (poate ajuta dacă nu sunt listate pachete)", "Reset WingetUI": "Resetează UniGetUI", "Reset WingetUI and its preferences": "Resetează UniGetUI și preferințele sale", "Reset WingetUI icon and screenshot cache": "Resetează UniGetUI și cache-ul de capturi de ecran", "Reset list": "Resetează lista", "Resetting Winget sources - WingetUI": "Se resetează sursele Winget - UniGetUI", "Restart": "Repornește", "Restart UniGetUI": "Repornește UniGetUI", "Restart WingetUI": "Repornește WingetUI", "Restart WingetUI to fully apply changes": "Repornește UniGetUI pentru a aplica schimbările în totalitate", "Restart later": "Repornește mai tâ<PERSON>", "Restart now": "Repornește acum", "Restart required": "Repornire necesară", "Restart your PC to finish installation": "Repornește PC-ul pentru a termina instalarea", "Restart your computer to finish the installation": "Repornește calculatorul pentru a termina instalarea", "Restore a backup from the cloud": "Restaurează un backup din cloud", "Restrictions on package managers": "Restricții asupra managerilor de pachete", "Restrictions on package operations": "Restricții asupra operațiilor cu pachete", "Restrictions when importing package bundles": "Restricții la importarea grupurilor de pachete", "Retry": "Reîncearcă", "Retry as administrator": "Reîncearcă ca Administrator", "Retry failed operations": "Reîncearcă operațiile eșuate", "Retry interactively": "Reîncearcă interactiv", "Retry skipping integrity checks": "Reîncearcă omiterea verificărilor de integritate", "Retrying, please wait...": "Se încearcă din nou, te rog așteaptă...", "Return to top": "Înapoi sus", "Run": "Rulează", "Run as admin": "Rulează ca administrator", "Run cleanup and clear cache": "Rulează curățarea și curăță cache-ul", "Run last": "Rulează ultima", "Run next": "Rulează următoarea", "Run now": "Rulează acum", "Running the installer...": "Se rulează instalatorul...", "Running the uninstaller...": "Se rulează dezinstalatorul...", "Running the updater...": "Se rulează actualizatorul...", "Save": "Salvează", "Save File": "Salveaz<PERSON>", "Save and close": "Salvează și închide", "Save as": "Salvează ca", "Save bundle as": "Salvează grupul ca", "Save now": "Salvează acum", "Saving packages, please wait...": "Se salvează pachetele, te rog așteaptă...", "Scoop Installer - WingetUI": "Instalator Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Dezinstalator Scoop - UniGetUI", "Scoop package": "<PERSON><PERSON><PERSON>", "Search": "Caut<PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Caut programe pentru desktop, avertizează-mă când sunt disponibile actualizări și nu face l<PERSON><PERSON><PERSON> tocil<PERSON>. Nu vreau ca UniGetUI să fie prea complicat, vreau doar un simplu <b>magazin de programe</b>", "Search for packages": "<PERSON><PERSON><PERSON> p<PERSON>", "Search for packages to start": "Caută pachete pentru a începe", "Search mode": "<PERSON><PERSON>", "Search on available updates": "Caută printre actualizările disponibile", "Search on your software": "Caută prin software-ul tău", "Searching for installed packages...": "Se caută pachete instalate...", "Searching for packages...": "Se caută pachete...", "Searching for updates...": "Se caută actualizări...", "Select": "Selectează", "Select \"{item}\" to add your custom bucket": "Selectați „{item}” pentru a adăuga ceva personalizat", "Select a folder": "Selectează un dosar", "Select all": "Select<PERSON>z<PERSON> tot", "Select all packages": "<PERSON><PERSON><PERSON><PERSON> toate pache<PERSON>e", "Select backup": "Alege backup", "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON> do<PERSON> <b>dac<PERSON> știi ce faci</b>.", "Select package file": "<PERSON><PERSON><PERSON><PERSON> fi<PERSON> p<PERSON>t", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Selectează backup-ul pe care dorești să îl deschizi. Mai târziu vei putea să revizuiești ce pachete dorești să instalezi.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Selectează procesele care ar trebui să fie închise înainte ca acest pachet să fie instalat, actualizat sau dezinstalat.", "Select the source you want to add:": "Selectează sursa pe care dorești să o adaugi:", "Select upgradable packages by default": "Selectează pachetele actualizabile în mod implicit", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Selectați ce <b>administratori de pachete</b> să utilizați ({0}), configurați modul în care sunt instalate pachetele, gestionați modul în care sunt gestionate drepturile de administrator etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Strângere de mână trimisă. Se așteaptă răspunsul ascultătorului... ({0}%)", "Set a custom backup file name": "Alege un nume de fișier personalizat al copiei de siguranță", "Set custom backup file name": "Setează nume particularizat pentru copia de siguranță", "Settings": "<PERSON><PERSON><PERSON>", "Share": "Partajează", "Share WingetUI": "Partajează UniGetUI", "Share anonymous usage data": "Partajează date de utilizare anonime", "Share this package": "Partajează acest pachet", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Dacă ar fi să modifici setările de securitate, va trebui să deschizi grupul din nou pentru ca schimbările să fie aplicate.", "Show UniGetUI on the system tray": "Afișează UniGetUI în tăvița sistemului", "Show UniGetUI's version and build number on the titlebar.": "Afișează versiunea și numărul de build al UniGetUI în bara de titlu.", "Show WingetUI": "Arată UniGetUI", "Show a notification when an installation fails": "Arată o notificare când o instalare eșuează", "Show a notification when an installation finishes successfully": "Arată o notificare când o instalare s-a realizat", "Show a notification when an operation fails": "Afișează o notificare dacă o operație eșuează", "Show a notification when an operation finishes successfully": "Afișează o notificare dacă o operație se finalizează cu succes", "Show a notification when there are available updates": "Arată o notificare când există actualizări disponibile", "Show a silent notification when an operation is running": "Afișează o notificare silențioasă când se rulează o operațiune", "Show details": "Arat<PERSON>", "Show in explorer": "Arată în Explorer", "Show info about the package on the Updates tab": "Arată informații despre pachet pe file de Actualizări", "Show missing translation strings": "Arată șirurile lips<PERSON> din traducere", "Show notifications on different events": "Arată notificări pentru diverse evenimente", "Show package details": "Arată de<PERSON>ii pachete", "Show package icons on package lists": "Afișează pictogramele pachetelor în listele de pachete", "Show similar packages": "Pachete similare", "Show the live output": "Arată datele de ieșire în timp real", "Size": "<PERSON><PERSON><PERSON><PERSON>", "Skip": "Omite", "Skip hash check": "Omite verificarea sumei de control", "Skip hash checks": "Omite verificările sumelor de control", "Skip integrity checks": "Omite verificările de integritate", "Skip minor updates for this package": "Omite actualizări minore pentru acest pachet", "Skip the hash check when installing the selected packages": "Omite verificarea sumei de control când se instalează pachetele selectate", "Skip the hash check when updating the selected packages": "Omite verificarea sumei de control când se actualizează pachetele selectate", "Skip this version": "Omite această versiune", "Software Updates": "Actualizări ale programelor", "Something went wrong": "Ceva nu a funcționat", "Something went wrong while launching the updater.": "Ceva nu a funcționat la lansarea actualizatorului.", "Source": "Sursă", "Source URL:": "Sursă URL:", "Source added successfully": "Sursa a fost adăugată cu succes", "Source addition failed": "Adăugarea sursei a eșuat", "Source name:": "Nume sursă:", "Source removal failed": "Eliminarea sursei a eșuat", "Source removed successfully": "Sursa a fost eliminată cu succes", "Source:": "Sursă:", "Sources": "Surse", "Start": "Start", "Starting daemons...": "Se pornesc demonii...", "Starting operation...": "Se începe operația...", "Startup options": "Opțiuni pornire", "Status": "Stare", "Stuck here? Skip initialization": "Blocat aici? Omite inițializarea", "Suport the developer": "Susține dezvoltatorul", "Support me": "Susține-mă", "Support the developer": "Susține dezvoltatorul", "Systems are now ready to go!": "<PERSON><PERSON><PERSON>e sunt acum pregătite!", "Telemetry": "Telemetrie", "Text": "Text", "Text file": "Fișier text", "Thank you ❤": "Mulțumesc ❤", "Thank you 😉": "Mulțumesc 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Managerul de pachete Rust.<br>Conține: <b>Biblioteci Rust și programe scrise în Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Copia de siguranță NU va conține fișiere binare sau salvări ale datelor programului.", "The backup will be performed after login.": "Copia de siguranță va fi făcută după autentificare.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Copia de siguranță va include lista completă a pachetelor instalate și opțiunile lor de instalare. Actualizările ignorate și versiunile omise vor fi salvate de asemenea.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Grupul pe care încerci să-l încarci pare să fie invalid. Te rog verifică fișierul și încearcă din nou.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Suma de control a programului de instalare nu coincide cu valoarea așteptată așa că autenticitatea nu poate fi verificată. Dacă ai încredere în acest editor, {0} pachetul va omite din nou verificarea sumei de control.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Managerul de pachete clasic pentru Windows. Vei găsi totul acolo. <br>Conține: <b>Programe generale</b>", "The cloud backup completed successfully.": "Copierea de siguranță s-a finalizat cu succes.", "The cloud backup has been loaded successfully.": "Copia de siguranță din cloud a fost încărcată cu succes.", "The current bundle has no packages. Add some packages to get started": "Setul curent nu conține pachete. Adaugă câteva pachete pentru a începe", "The executable file for {0} was not found": "Fișierul executabil pentru {0} nu a fost găsit", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Următoarele opțiuni vor fi aplicate implicit de fiecare dată când un pachet {0} este instalat, actualizat sau dezinstalat.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Următoarele pachete vor fi exportate într-un fișier JSON. Nu vor fi salvate fișere binare sau datele utilizatorului.", "The following packages are going to be installed on your system.": "Următoarele pachete vor fi instalate pe sistemul dumneavoastră.", "The following settings may pose a security risk, hence they are disabled by default.": "<PERSON>rm<PERSON><PERSON><PERSON><PERSON> setări pot reprezenta un risc de securitate, de aceea sunt implicit dezactivate.", "The following settings will be applied each time this package is installed, updated or removed.": "Următoarele setări vor fi aplicate de fiecare dată când acest pachet este instalat, actualizat sau eliminat.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Următoarele setări vor fi aplicate de fiecare dată când acest pachet este instalat, actualizat sau șters. Ele vor fi salvate automat.", "The icons and screenshots are maintained by users like you!": "Pictogramele și capturile de ecran sunt întreținute de utilizatori ca tine!", "The installer authenticity could not be verified.": "Autenticitatea instalatorului nu a putut fi verificată.", "The installer has an invalid checksum": "Programul de instalare are o sumă de control invalidă", "The installer hash does not match the expected value.": "Suma de control a instalatorului nu se potrivește cu valoarea așteptată.", "The local icon cache currently takes {0} MB": "Cache-ul de pictograme local ocupă {0}MO", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Scopul principal al acestui proiect este de a crea o interfață intuitivă pentru a gestiona cei mai simpli manageri de pachete CLI pentru Windows, cum ar fi Winget și Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Pachetul „{0}” nu a fost găsit în managerul de pachete „{1}”", "The package bundle could not be created due to an error.": "Grupul de pachete nu a putut fi creat din cauza unei erori.", "The package bundle is not valid": "Grupul de pachete nu este valid", "The package manager \"{0}\" is disabled": "Managerul de pachete „{0}” este dezactivat", "The package manager \"{0}\" was not found": "Managerul de pachete „{0}” nu a fost găsit", "The package {0} from {1} was not found.": "Pachetul {0} din {1} nu a fost găsit.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Pachetele enumerate aici nu vor fi luate în considerare la verificarea actualizărilor. Fă dublu clic pe ele sau fă clic pe butonul din dreapta lor pentru a nu mai mai ignora actualizările.", "The selected packages have been blacklisted": "Pachetele selectate au fost incluse pe lista neagră", "The settings will list, in their descriptions, the potential security issues they may have.": "<PERSON><PERSON><PERSON><PERSON> vor lista, în desc<PERSON><PERSON> lor, problemele potențiale de securitate pe care le pot introduce.", "The size of the backup is estimated to be less than 1MB.": "Dimensiunea copiei de siguranță este estimată a fi sub 1MB.", "The source {source} was added to {manager} successfully": "Sursa {source} a fost adăugată la {manager} cu succes", "The source {source} was removed from {manager} successfully": "Sursa {source} a fost eliminată din {manager} cu succes", "The system tray icon must be enabled in order for notifications to work": "Pictograma din tăvița sistemului trebuie să fie activată pentru a putea primi notificări", "The update process has been aborted.": "Procesul de actualizare a fost anulat.", "The update process will start after closing UniGetUI": "Procesul de actualizare va porni după închiderea UniGetUI", "The update will be installed upon closing WingetUI": "Actualizarea va fi instalată la închiderea UniGetUI", "The update will not continue.": "Actualizarea nu va continua.", "The user has canceled {0}, that was a requirement for {1} to be run": "Utilizatorul a anulat {0}, care era o necesitate pentru a putea fi rulată {1}", "There are no new UniGetUI versions to be installed": "Nu există versiuni noi de UniGetUI pentru a fi instalate", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Există operații în curs. Închizând UniGetUI va face ca ele să eșueze. Dorești să continui?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Există câteva videoclipuri minunate pe YouTube care îți prezintă UniGetUI și capacitățile sale. Ai putea învăța trucuri și sfaturi utile!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Există două motive principale pentru a nu rula UniGetUI ca administrator: Primul este că managerul de pachete Scoop poate cauza probleme cu unele comenzi rulate ca administrator. Al doilea este că rulând UniGetUI ca administrator înseamnă că orice pachet vei descărca va fi rulat de asemenea ca administrator (și acest lucru nu este sigur). Ia aminte că dacă ai nevoie să instalezi un anumit pachet ca administrator, poți oricând să faci click-dreapta pe acesta și să alegi Instalează/Actualizează/Dezinstalează ca administrator.", "There is an error with the configuration of the package manager \"{0}\"": "Există o eroare cu configurația managerului de pachete „{0}”", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Există o instalare în curs. Dacă închideți UniGetUI, este posibil ca instalarea să eșueze și să aibă rezultate neașteptate. Mai vrei să renunți la UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Sunt programele responsabile cu instalarea, actualizarea și eliminarea pachetelor.", "Third-party licenses": "Licențe terțe", "This could represent a <b>security risk</b>.": "Acest lucru ar putea reprezenta un <b>risc de securitate</b>.", "This is not recommended.": "Acest lucru nu este recomandat", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Probabil acest lucru se datorează faptului că pachetul care v-a fost trimis a fost eliminat sau publicat pe un manager de pachete pe care nu l-ați activat. ID-ul primit este {0}", "This is the <b>default choice</b>.": "Aceasta este <b>alegerea implicită</b>.", "This may help if WinGet packages are not shown": "Acest lucru poate ajuta dacă pachetele WinGet nu sunt afișate", "This may help if no packages are listed": "Acest lucru poate ajuta dacă nu este listat niciun pachet", "This may take a minute or two": "Acest lucru poate dura 1-2 minut(e)", "This operation is running interactively.": "Această operație este rulată interactiv.", "This operation is running with administrator privileges.": "Această operație este rulată cu privilegii de administrator.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Această opțiune VA cauza probleme. Orice operație incapabilă de a se eleva singură VA EȘUA. Instalarea/actualizarea/dezinstalarea ca administrator NU VA FUNCȚIONA.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Acest grup de pachete a avut anumite setări care sunt potențial periculoase și ar putea să fie implicit ignorate.", "This package can be updated": "Acest pachet poate fi actualizat", "This package can be updated to version {0}": "Acest pachet poate fi actualizat la versiunea {0}", "This package can be upgraded to version {0}": "Acest pachet poate fi actualizat la versiunea {0}", "This package cannot be installed from an elevated context.": "Acest pachet nu poate fi instalat dintr-un context elevat.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Lipsesc capturile de ecran sau pictograma acestui pachet? Contribuie la UniGetUI adăugând pictogramele sau capturile de ecran lipsă în baza noastră de date deschisă și publică.", "This package is already installed": "Acest pachet este deja instalat", "This package is being processed": "Acest pachet este în curs de procesare", "This package is not available": "Acest pachet nu este disponibil", "This package is on the queue": "Acest pachet este pus în coadă", "This process is running with administrator privileges": "Acest proces rulează cu privilegii de administrator", "This project has no connection with the official {0} project — it's completely unofficial.": "Acest proiect nu are nicio legătură cu proiectul oficial {0} — este complet neoficial.", "This setting is disabled": "Această setare este dezactivată", "This wizard will help you configure and customize WingetUI!": "Acest asistent pentru instalare vă va ajuta să configurați și să personalizați UniGetUI!", "Toggle search filters pane": "Comutați panoul de filtre de căutare", "Translators": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Încearcă să ucizi procesul care refuză a fi închis la cerere.", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Activarea acestei opțiuni permite schimbarea fișierului executabil folosit pentru a interacționa cu managerii de pachete. Deși acest lucru permite personalizare mai detaliată a procesului de instalare, ar putea de asemenea să fie periculos.", "Type here the name and the URL of the source you want to add, separed by a space.": "Scrie aici numele și URL-ul sursei pe care o dorești adăugată, separate printr-un spațiu.", "Unable to find package": "Imposibil de gă<PERSON><PERSON>", "Unable to load informarion": "Nu se poate încărca informația", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI colectează date de utilizare anonime pentru a îmbunătăți experiența utilizatorilor.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI colectează date de utilizare anonime cu unicul scop de a înțelege și a îmbunătăți experiența utilizatorilor.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI a detectat o nouă scurtătură pe spațiul de lucru care poate fi ștearsă automat.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI a detectat următoarele scurtături pe spațiul de lucru care pot fi șterse automat la actualizările viitoare", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI a detectat {0} noi scurtături pe spațiul de lucru care pot fi șterse automat.", "UniGetUI is being updated...": "UniGetUI este în curs de actualizare...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI nu este înrudit cu niciunul dintre managerele de pachete. UniGetUI este un proiect independent.", "UniGetUI on the background and system tray": "UniGetUI în fundal și zona de notificări a sistemului", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI sau unele din componentele sale lipsesc sau sunt corupte.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI necesită {0} pentru a opera, dar nu a fost găsit pe sistemul tău.", "UniGetUI startup page:": "Pagina de pornire UniGetUI:", "UniGetUI updater": "Actualizator UniGetUI", "UniGetUI version {0} is being downloaded.": "UniGetUI versiunea {0} este în curs de descărcare.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} este gata pentru a fi instalat.", "Uninstall": "Dezinstalează", "Uninstall Scoop (and its packages)": "Dezinstaleaz<PERSON> (și pachetele sale)", "Uninstall and more": "Dezinstalează și mai multe", "Uninstall and remove data": "Dezinstalează și elimină datele", "Uninstall as administrator": "Dezinstalează ca administrator", "Uninstall canceled by the user!": "Dezinstalare anulată de utilizator!", "Uninstall failed": "Dezinstalarea a eșuat", "Uninstall options": "Opțiuni dezinstalare", "Uninstall package": "Dezinstalează pachetul", "Uninstall package, then reinstall it": "Dezinstalează pachetul, apoi reinstalează-l", "Uninstall package, then update it": "Dezinstalează pachetul, apoi actualizează-l", "Uninstall previous versions when updated": "Dezinstalează versiunile anterioare la actualizare", "Uninstall selected packages": "Dezinstalează pachetele selectate", "Uninstall selection": "Dezinstalează selecția", "Uninstall succeeded": "Dezinstalarea s-a realizat", "Uninstall the selected packages with administrator privileges": "Dezinstalează pachetele selectate cu privilegii de administrator", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Pachetele ce nu se pot dezinstala cu originea listată ca „{0}” nu sunt publicate în niciun manager de pachete, deci nu există informații despre ele ce se pot afișa.", "Unknown": "Necunoscut", "Unknown size": "Mărime necunoscută", "Unset or unknown": "Nesetat sau necunoscut", "Up to date": "La zi", "Update": "Actualizează", "Update WingetUI automatically": "Actualizează UniGetUI automat", "Update all": "Actualizează toate", "Update and more": "Actualizează și mai multe", "Update as administrator": "Actualizează ca administrator", "Update check frequency, automatically install updates, etc.": "Frecvența verificărilor de actualizări, instalări automate ș.a.m.d.", "Update date": "Actualizare date", "Update failed": "Actualizarea a eșuat", "Update found!": "Actualizare găsi<PERSON>ă!", "Update now": "Actualizează acum", "Update options": "Opțiuni actualizare", "Update package indexes on launch": "Actualizează indexul pachetelor la pornire", "Update packages automatically": "Actualizează pachetele automat", "Update selected packages": "Actualizează pachetele selectate", "Update selected packages with administrator privileges": "Actualizează pachetele selectate cu privilegii de administrator", "Update selection": "Actualizează selecția", "Update succeeded": "Actualizarea s-a realizat", "Update to version {0}": "Actualizează la versiunea {0}", "Update to {0} available": "Actualizare la {0} disponibilă", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Actualizează fișierele port Git ale vcpkg automat (necesită Git instalat)", "Updates": "Act<PERSON><PERSON><PERSON><PERSON>", "Updates available!": "Actualizări disponibile!", "Updates for this package are ignored": "Actualizările pentru acest pachet sunt ignorate", "Updates found!": "Actualizări g<PERSON>site!", "Updates preferences": "Preferințe actualizări", "Updating WingetUI": "Actualizează UniGetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Folosește WinGet inclus moștenit în loc de Comenzi PowerShell", "Use a custom icon and screenshot database URL": "Utilizați o pictogramă personalizată și o captură de ecran la URL-ul bazei de date", "Use bundled WinGet instead of PowerShell CMDlets": "Folosește WinGet inclus în loc de CMDLet-uri PowerShell", "Use bundled WinGet instead of system WinGet": "Folosește WinGet-ul inclus în loc de cel al sistemului", "Use installed GSudo instead of UniGetUI Elevator": "Folosește GSudo instalat în loc de UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Utilizați GSudo instalat în loc de cel inclus (necesită repornirea aplicației)", "Use system Chocolatey": "Folosește Chocolatey de sistem", "Use system Chocolatey (Needs a restart)": "Folosește Chocolatey sistem (Necesită repornire)", "Use system Winget (Needs a restart)": "Folosește Winget sistem (Necesită repornire)", "Use system Winget (System language must be set to english)": "Folosește WinGet de sistem (limba sistemului trebuie să fie setată la engleză)", "Use the WinGet COM API to fetch packages": "Folosește API-uri COM WinGet pentru a prelua pachete", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Folosește modulul WinGet de PowerShell în loc de API-ul COM", "Useful links": "Leg<PERSON><PERSON><PERSON> utile", "User": "Utilizator", "User interface preferences": "Preferințele interfeței utilizatorului", "User | Local": "Utilizator | Local", "Username": "Nume utilizator", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Folosind UniGetUI implică acceptarea licenței GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Folosirea UniGetUI implică acceptarea licenței MIT.", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Rădăcina vcpkg nu a fost găsită. Te rog definește variabila de mediu %VCPKG_ROOT% sau definește-o din setările UniGetUI", "Vcpkg was not found on your system.": "Vcpkg nu a fost găsit pe sistemul tău.", "Verbose": "Verbos", "Version": "Versiune", "Version to install:": "Versiune de instalat:", "Version:": "Versiune:", "View GitHub Profile": "<PERSON><PERSON><PERSON> profil <PERSON>", "View WingetUI on GitHub": "Vizualizați UniGetUI pe GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Vizualizează codul sursă al UniGetUI. De acolo poți raporta probleme, sugera funcții noi sau contribui direct la proiect.", "View mode:": "Mod vizualizare:", "View on UniGetUI": "Vezi în UniGetUI", "View page on browser": "Vezi pagina în navigatorul web", "View {0} logs": "<PERSON><PERSON><PERSON> jurn<PERSON> {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Așteaptă ca dispozitivul să fie conectat la internet înainte să rulezi sarcini care necesită o conexiune la internet.", "Waiting for other installations to finish...": "Se așteaptă finalizarea celorlalte instalări...", "Waiting for {0} to complete...": "Se așteaptă ca {0} să se termine...", "Warning": "Avertizare", "Warning!": "Atenție!", "We are checking for updates.": "Verificăm pentru actualizări.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Nu am putut încărca informații detaliate despre acest pachet, deoarece nu a fost găsit în niciuna dintre sursele pachetului dumneavoastră.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Nu am putut încărca informații detaliate despre acest pachet deoarece nu a fost instalat dintr-un manager de pachete disponibil.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Nu am putut {action} {package}. Te rog încearcă mai târziu. Apasă pe „{showDetails}” pentru a vedea jurnalele de instalare.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Nu am putut {action} {package}. Te rog încearcă mai târziu. Apasă pe „{showDetails}” pentru a vedea jurnalele de dezinstalare.", "We couldn't find any package": "Nu am putut găsit niciun pachet", "Welcome to WingetUI": "Bun venit la UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "La instalarea pachetelor în serie dintr-un grup, instalează de asemenea și pachetele care sunt deja instalate.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Când noi scurtături sunt detectate, șterge-le automat în loc de a arăta acest dialog.", "Which backup do you want to open?": "Ce backup dorești să deschizi?", "Which package managers do you want to use?": "Ce manager de pachete doriți să utilizați?", "Which source do you want to add?": "Ce sursă vrei să adaugi?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Deși WinGet poate fi folosit în UniGetUI, UniGetUI poate fi folosit și împreună cu alte managere de pachete, lucru care poate fi ușor confuz. În trecut, WingetUI a fost făcut să lucreze doar cu Winget, dar acest lucru nu mai este întrutotul adevărat. Astfel WingetUI nu mai este reprezentativ pentru ceea ce vrea să devină acest proiect.", "WinGet could not be repaired": "WinGet nu a putut fi reparat", "WinGet malfunction detected": "A fost detectată o defecțiune a WinGet", "WinGet was repaired successfully": "WinGet a fost reparat cu succes", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Totul este la zi", "WingetUI - {0} updates are available": "UniGetUI - {0} actual<PERSON><PERSON>ri sunt disponibile", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "Pagină web UniGetUI", "WingetUI Homepage - Share this link!": "Pagină web UniGetUI - Partajează această link!", "WingetUI License": "Licență UniGetUI", "WingetUI Log": "Jurnal UniGetUI", "WingetUI Repository": "Repository UniGetUI", "WingetUI Settings": "Setări UniGetUI", "WingetUI Settings File": "Fișierul de setări UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI folosește următoarele biblioteci. Fără acestea, UniGetUI nu ar fi fost existat.", "WingetUI Version {0}": "Versiunea UniGetUI {0}", "WingetUI autostart behaviour, application launch settings": "Pornire automată, setări lansare UniGetUI", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI poate verifica dacă software-ul dvs. are actualizări disponibile și le poate instala automat dacă doriți", "WingetUI display language:": "Limba de afișare pentru UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI a fost lansat cu drepturi de administrator, ceea ce nu este recomandat. Când UnigetUI este rulat ca administrator, FIECARE operație lansată din UniGetUI va avea și ea drepturi de administrator. Poți desigur să folosești programul în continuare, dar încurajăm să nu mai rulezi UniGetUI în acest mod.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI a fost tradus în mai mult de 40 de limbi mulțumită voluntarilor. Mulțumesc🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI nu a fost tradus automat. Următorii utilizatori s-au ocupat de traduceri:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI este o aplicație care face managementul software-ului instalat mai ușor aducând o interfață grafică comună tuturor managerelor de pachete în linie de comandă.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI a fost redenumit pentru a sublinia diferența dintre UniGetUI (interfața pe care o utilizezi acum) și Winget (managerul de pachete dezvoltat de Microsoft) cu care eu nu sunt afiliat", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI este în curs de actualizare. Când se termină, UniGetUI va reporni singur", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI este gratuit și va fi la fel pentru totdeauna. Fără reclame, fără card de credit, fără variantă premium. 100% gratuit, pentru totdeauna.", "WingetUI log": "Jurnal UniGetUI", "WingetUI tray application preferences": "Preferințe zonă de notificări UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI folosește următoarele biblioteci. Fără el<PERSON>, WingetUI nu ar fi fost existat.", "WingetUI version {0} is being downloaded.": "UniGetUI versiunea {0} este în curs de descărcare.", "WingetUI will become {newname} soon!": "WingetUI va deveni în curând {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI nu va verifica pentru actualizări periodic. Acestea vor fi verificate la pornire, dar nu vei fi atenționat asupra lor.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI va afișa un prompt UAC de fiecare dată când un pachet necesită instalarea unei elevații.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI va fi în curând redenumit {newname}. Acest lucru nu va reprezenta nicio schimbare în aplicație. Eu (dezvolatorul) voi continua dezvoltarea acestui proiect exact ca și până acum, dar sub un nume diferit.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI nu ar fi fost posibil fără ajutorul colaboratorilor noștrii. Vezi profilul lor de GitHub!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI nu ar fi fost posibil fără ajutorul contribuitorilor. Mulțumesc vouă, tuturor 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} este gata de a fi instalat.", "Write here the process names here, separated by commas (,)": "Scrie aici numele proceselor, separate de virgule (,)", "Yes": "Da", "You are logged in as {0} (@{1})": "Ești autentificat ca {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Poți schimba acest comportament în setările de securiate al UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "<PERSON><PERSON><PERSON> defini comenzile care vor fi rulate înainte sau după ce acest pachet este instalat, actualizat sau dezinstalat. Acestea vor fi rulate într-un prompt de comandă, deci scripturile CMD vor funcționa aici.", "You have currently version {0} installed": "Versiunea curentă instalată este {0}", "You have installed WingetUI Version {0}": "Ai instalat UniGetUI versiunea {0}", "You may lose unsaved data": "<PERSON> putea pierde datele nesalvate", "You may need to install {pm} in order to use it with WingetUI.": "Ar putea fi necesar să instalezi {pm} pentru a-l utiliza cu UniGetUI.", "You may restart your computer later if you wish": "Puteți reporni computerul mai târziu, dacă do<PERSON>ți", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Vei fi întrebat o singură dată, iar drepturile de administrator vor fi acordate pachetelor care le solicită.", "You will be prompted only once, and every future installation will be elevated automatically.": "Vei fi întrebat o singură dată și la fiecare instalare viitoare va fi aplicat automat.", "You will likely need to interact with the installer.": "S-ar putea să trebuiască să interacționezi cu instalatorul.", "[RAN AS ADMINISTRATOR]": "RULAT CA ADMINISTRATOR", "buy me a coffee": "Cumpără-mi o cafea", "extracted": "extras", "feature": "caracteristică", "formerly WingetUI": "în trecut WingetUI", "homepage": "pagina principală", "install": "instalează", "installation": "instalare", "installed": "instalat", "installing": "instalează", "library": "bibliotecă", "mandatory": "obligatoriu", "option": "opțiune", "optional": "opțional", "uninstall": "dezinstalează", "uninstallation": "dezinstalare", "uninstalled": "dezinstalat", "uninstalling": "se dezinstalează", "update(noun)": "Actualizarea", "update(verb)": "actualiza", "updated": "actualizat", "updating": "se actualizează", "version {0}": "versiunea {0}", "{0} Install options are currently locked because {0} follows the default install options.": "Opțiunile de instalare ale {0} sunt blocate acum deoarece {0} respectă opțiunile de instalare implicite.", "{0} Uninstallation": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} aborted": "{0} anulat", "{0} can be updated": "{0} pot fi actualizate", "{0} can be updated to version {1}": "{0} poate fi actualizat la versiunea {1}", "{0} days": "{0} zile", "{0} desktop shortcuts created": "{0} s<PERSON><PERSON><PERSON><PERSON><PERSON> create pe spațiul de lucru", "{0} failed": "{0} a e<PERSON>uat", "{0} has been installed successfully.": "{0} a fost instalat cu succes.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} a fost instalat cu succes. Este recomandat să repornești UniGetUI pentru a finaliza instalarea", "{0} has failed, that was a requirement for {1} to be run": "{0} a e<PERSON><PERSON>t, care era necesar pentru a putea rula {1}", "{0} homepage": "Pagina principală a {0}", "{0} hours": "{0} ore", "{0} installation": "{0} ins<PERSON><PERSON><PERSON>", "{0} installation options": "Opțiuni instalare {0}", "{0} installer is being downloaded": "Instalatorul {0} este în curs de descărcare", "{0} is being installed": "{0} este în curs de instalare", "{0} is being uninstalled": "{0} este în curs de dezinstalare", "{0} is being updated": "{0} este în curs de actualizare", "{0} is being updated to version {1}": "{0} este în curs de actualizare la {1}", "{0} is disabled": "{0} este dezactivat", "{0} minutes": "{0} minute", "{0} months": "{0} luni", "{0} packages are being updated": "{0} pachete sunt în curs de actualizare", "{0} packages can be updated": "{0} pachete pot fi actualizate", "{0} packages found": "Pachete găsite: {0}", "{0} packages were found": "Pachete găsite: {0}", "{0} packages were found, {1} of which match the specified filters.": "{0} pachete au fost găsite, dintre care {1} se potrivesc cu filtrele specificate.", "{0} settings": "<PERSON><PERSON><PERSON> {0}", "{0} status": "Stare {0}", "{0} succeeded": "{0} a reușit", "{0} update": "Actualizare {0}", "{0} updates are available": "{0} <PERSON><PERSON><PERSON><PERSON> sunt disponibile", "{0} was {1} successfully!": "{0} a fost {1} cu succes!", "{0} weeks": "{0} săptămâni", "{0} years": "{0} ani", "{0} {1} failed": "{0} {1} a e<PERSON><PERSON>t", "{package} Installation": "Instalare {package}", "{package} Uninstall": "Dezinstalare {package}", "{package} Update": "Actualizare {package}", "{package} could not be installed": "{package} nu a putut fi instalat", "{package} could not be uninstalled": "{package} nu a putut fi dezinstalat", "{package} could not be updated": "{package} nu a putut fi actualizat", "{package} installation failed": "Instalarea {package} a eșuat", "{package} installer could not be downloaded": "Instalatorul {package} nu a putut fi descărcat", "{package} installer download": "Descărcare installer {package}", "{package} installer was downloaded successfully": "Instalatorul pentru {package} a fost descărcat cu succes", "{package} uninstall failed": "Dezinstalarea {package} a eșuat", "{package} update failed": "Acualizarea {package} a eșuat", "{package} update failed. Click here for more details.": "Actualizarea {package} a eșuat. Clic aici pentru mai multe detalii.", "{package} was installed successfully": "{package} a fost instalat cu succes", "{package} was uninstalled successfully": "{package} a fost dezinstalat cu succes", "{package} was updated successfully": "{package} a fost actualizat cu succes", "{pcName} installed packages": "Pachete instalate {pcName}", "{pm} could not be found": "{pm} nu a putut fi găsit", "{pm} found: {state}": "{pm} găsit: {state}", "{pm} is disabled": "{pm} este dezactivat", "{pm} is enabled and ready to go": "{pm} este activat și gata de lucru", "{pm} package manager specific preferences": "Preferințe specifice managerului de pachete {pm}", "{pm} preferences": "Prefer<PERSON><PERSON><PERSON> {pm}", "{pm} version:": "{pm} versiunea:", "{pm} was not found!": "{pm} nu a fost găsit!"}