{"\"{0}\" is a local package and can't be shared": "\"{0}\" هي حزمة محلية ولا يمكننا تحديثها", "\"{0}\" is a local package and does not have available details": "\"{0}\" هي حزمة محلية وليس لها تفاصيل متاحة", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" هي حزمة محلية وليست متوافقة مع هذه الخاصية", "(Last checked: {0})": "(أخر تحقق: {0}) ", "(Number {0} in the queue)": "(الرقم {0} في الدور)\n", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@Abdu11ahAS, @mo9a7i, @<PERSON><PERSON><PERSON><PERSON><PERSON>, @Abdullah-Dev115, @bassuny3003, @DaRandomCube, @AbdullahAlousi, @IFrxo", "0 packages found": "لم يتم العثور على أية حزمة", "0 updates found": "لم يتم العثور على أية تحديثات", "1 - Errors": "1- <PERSON><PERSON><PERSON>اء", "1 day": "يوم 1", "1 hour": "ساعة واحدة", "1 month": "شهر واحد", "1 package was found": "تم العثور على حزمة واحدة", "1 update is available": "يوجد تحديث 1 متوفر", "1 week": "أسبوع واحد", "1 year": "سنة واحدة", "1. Navigate to the \"{0}\" or \"{1}\" page.": "تنقّل إلى صفحة \"{0}\" أو \"{1}\"", "2 - Warnings": "2- تحذي<PERSON><PERSON>ت", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. حد<PERSON> موقع الحزمة/الحزم التي تريد إضافتها إلى الباقة، وحدد مربع الاختيار أقصى اليسار", "3 - Information (less)": "3- مع<PERSON><PERSON><PERSON><PERSON><PERSON> (أقل)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. عندما تكون الحزم التي تريد إضافتها إلى الباقة مُختارة، جِد واصغط الخيار \"{0}\" في شريط الأدوات.", "4 - Information (more)": "4- مع<PERSON><PERSON><PERSON><PERSON><PERSON> (أ<PERSON><PERSON><PERSON>)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. سيتم إضافة حزمك إلى الباقة. يمكنك مواصلة إضافة الحزم، أو تصدير الباقة.", "5 - information (debug)": "5- معلوم<PERSON><PERSON> (للمعالجة)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "مدير مكتبات ++C/C شائع الاستخدام. مليء بمكتبات ++C/C وأدوات مساعدة أخرى متعلقة بـ ++C/C<br>يحتوي على: <b>مكتبات ++C/C وأدوات مساعدة ذات صلة</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "مستودع مليء بالأدوات والملفات القابلة للتنفيذ المصممة مع وضع نظام Microsoft البيئي .NET في الاعتبار.<br>يحتوي على: <b>أدوات وبرامج نصية مرتبطة بـ .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "مستودع مليء بالأدوات المصممة مع وضع نظام Microsoft البيئي .NET في الاعتبار.<br>يحتوي على: <b>أدوات ذات صلة بـ .NET</b>", "A restart is required": "إعادة التشغيل مطلوب", "Abort install if pre-install command fails": "إلغاء التثبيت إذا فشل أمر التثبيت المسبق", "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "عنا", "About Qt6": "<PERSON>ن Qt6", "About WingetUI": "<PERSON>ن <PERSON>", "About WingetUI version {0}": "عن UniGetUI إصدار {0}", "About the dev": "عن المطوّر", "Accept": "قبول", "Action when double-clicking packages, hide successful installations": "الحدث عند النقر-المزدوج على الحزم, إخفاء التثبيت الناجح", "Add": "إضافة", "Add a source to {0}": "إضافة مصدر إلى {0}", "Add a timestamp to the backup file names": "إضافة ختم زمني إلى أسماء ملفات النسخ الاحتياطي", "Add a timestamp to the backup files": "إضافة ختم زمني إلى ملفات النسخ الاحتياطي", "Add packages or open an existing bundle": "إضافة حُزم أو فتح حزمة موجودة", "Add packages or open an existing package bundle": "أض<PERSON> حزم أو افتح حزمة مسبقة", "Add packages to bundle": "أ<PERSON><PERSON> ال<PERSON>ز<PERSON> إلى الباقة", "Add packages to start": "أ<PERSON><PERSON> حزم للبدء", "Add selection to bundle": "إضافة المُحدد إلى الحزمة", "Add source": "إضافة مصدر", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "أضف التحديثات التي تفشل بـ \"لا يوجد تحديث قابل للتطبيق\" إلى قائمة التحديثات التي تم تجاهلها", "Adding source {source}": "إضافة مصدر {source}", "Adding source {source} to {manager}": "إضافة المصدر {source} <PERSON><PERSON><PERSON> {manager} ", "Addition succeeded": "نجحت الإضافة", "Administrator privileges": "صلاحيات المسؤول", "Administrator privileges preferences": "تفضيلات صلاحيات المسؤول", "Administrator rights": "صلاحيات المسؤول", "Administrator rights and other dangerous settings": null, "Advanced options": "خيارات متقدمة", "All files": "جميع الملفات", "All versions": "كل الإصدارات", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "السماح بإجراء عمليات الحزمة بالتوازي", "Allow parallel installs (NOT RECOMMENDED)": "السماح بالتثبيتات المتماثلة (غير مستحسن)", "Allow pre-release versions": "السماح بنسخ ماقبل الاصدار", "Allow {pm} operations to be performed in parallel": "السماح لعمليات {pm} بالعمل على التوازي ", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "بدلا من ذلك، يمكنك تثبيت {0} عن طريق لصق هذا اﻷمر في نافذة powershell", "Always elevate {pm} installations by default": "رفع تثبيتات {pm} دائماً افتراضياً", "Always run {pm} operations with administrator rights": "السماح دائماً بشتغيل عمليات {pm} بصلاحيات المسؤول", "An error occurred": "<PERSON><PERSON><PERSON>", "An error occurred when adding the source: ": "حد<PERSON> خطأ عند إضافة المصدر:", "An error occurred when attempting to show the package with Id {0}": "حصل خطأ عند محاولة عرض الحزمة مع المعرف {0}", "An error occurred when checking for updates: ": "حد<PERSON> خطأ عند البحث عن التحديثات:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "حدث خطاُ وقت معالجة هذه الحزمة", "An error occurred:": "حد<PERSON> خطأ:", "An interal error occurred. Please view the log for further details.": "حدث خطأ داخلي. يرجى الاطلاع على السجل لمزيد من التفاصيل.", "An unexpected error occurred:": "حدث خطأٌ غيرمُتوقع:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "حدثت مشكلة غير متوقعة أثناء محاولة إصلاح WinGet. يرجى المحاولة مرة أخرى لاحقًا", "An update was found!": "تم إيجاد تحديث!", "Android Subsystem": "نظام Android الفرعي", "Another source": "مصدرُ آخر", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "أي اختصارات جديدة تُنشأ أثناء عملية تثبيت أو تحديث سيتم حذفها تلقائياً، بدلاً من إظهار إشارة تأكيد في المرة الأولى التي يتم اكتشافها فيها.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "أيّ اختصارات تُنشأ أو تُعَدّل خارج UniGetUI سيتم تجاهلها. ستتمكن من إضافتها من خلال الزر {0}.", "Any unsaved changes will be lost": "أي حزم غير محفوظة ستُفقد", "App Name": "اسم التطبيق", "Appearance": "المظهر", "Application theme, startup page, package icons, clear successful installs automatically": "سمة التطبيق، صفحة بدء التشغيل، أيقونات الحزمة، مسح التثبيتات الناجحة تلقائيًا", "Application theme:": "مظهر التطبيق:", "Apply": "تطبيق", "Architecture to install:": "المعمارية للتثبيت:", "Are these screenshots wron or blurry?": "هل لقطات الشاشة هذه خاطئة أو غير واضحة؟", "Are you really sure you want to enable this feature?": "هل أنت متأكد من أنك تريد تفعيل هذه الخاصية؟", "Are you sure you want to create a new package bundle? ": "هل أنت متأكد من أنك تريد إنشاء حزمة جديدة؟", "Are you sure you want to delete all shortcuts?": "هل أنت متأكد من أنك تريد حذف جميع الاختصارات؟", "Are you sure?": "هل أنت متأكد؟", "Ascendant": "صعوداً", "Ask for administrator privileges once for each batch of operations": "اطلب صلاحيات المسؤول مرة واحدة لكل دفعة من العمليات", "Ask for administrator rights when required": "طلب صلاحيات المسؤول عند الحاجة", "Ask once or always for administrator rights, elevate installations by default": "طلب صلاحيات المسؤول مرة أو دائماً, رفع التثبيتات افتراضياً", "Ask only once for administrator privileges": "السؤال مرة واحد لامتيازات المسؤول", "Ask only once for administrator privileges (not recommended)": "طلب صلاحيات المسؤول مرة واحدة فقط (غير مستحسن)", "Ask to delete desktop shortcuts created during an install or upgrade.": "اطلب حذف اختصارات سطح المكتب التي تم إنشاؤها أثناء التثبيت أو الترقية.", "Attention required": "مطلوب الانتباه", "Authenticate to the proxy with an user and a password": "المصادقة على الوكيل مع مستخدم وكلمة مرور", "Author": "المؤلف", "Automatic desktop shortcut remover": "إزالة اختصار سطح المكتب تلقائيًا", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "تلقائياً قم بحفظ قائمة لكل حُزمِك المثبتة لاستعادتها بسهولة.", "Automatically save a list of your installed packages on your computer.": "تلقائياً قم بحفظ قائمة لكل حُزمِك المثبتة على جهازك.", "Autostart WingetUI in the notifications area": "تشغيل UniGetUI تلقائياً في منطقة الإشعارات", "Available Updates": "التحديثات المتاحة", "Available updates: {0}": "التحديثات المتاحة: {0}", "Available updates: {0}, not finished yet...": "التحديثات المتاحة: {0}, لم يتم الانتهاء بعد...", "Backing up packages to GitHub Gist...": null, "Backup": "النسخ الإحتياطي", "Backup Failed": "فشل النسخ الاحتياطي", "Backup Successful": "نجح النسخ الاحتياطي", "Backup and Restore": null, "Backup installed packages": "نسخ احتياطي للحزم المثبتة", "Backup location": "موقع النسخة الاحتياطية", "Become a contributor": "كن مساهاً", "Become a translator": "كن مُترجِماً", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "ميزات تجريبية و خيارات أخرى لا يجب لمسها", "Both": "كلاهما", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "لكن هنا يوجد المزيد من الأشياء التي يمكنك فعلها لتعلم المزيد عن UniGetUI أكثر فأكثر:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "من خلال إيقاف تشغيل مدير الحزم، لن تتمكن بعد الآن من رؤية حزمه أو تحديثها.", "Cache administrator rights and elevate installers by default": "جعل صلاحيات المسؤول مؤقتة و رفع التثبيتات افتراضياً", "Cache administrator rights, but elevate installers only when required": "جعل صلاحيات المسؤول مؤقتة, لكن رفع التثبيتات فقط عند الحاجة", "Cache was reset successfully!": "تمت إعادة ضبط الملفات المؤقتة بنجاح!", "Can't {0} {1}": "لا يمكن {0} {1}", "Cancel": "إلغاء", "Cancel all operations": "إلغاء جميع العمليات", "Change backup output directory": "تغيير موقع ملفات النسخ الاحتياطي", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "تغيير كيفية قيام UniGetUI بالتحقق من التحديثات المتوفرة لحزمك وتثبيتها", "Change how UniGetUI handles install, update and uninstall operations.": "تغيير كيفية تولي UniGetUI لعمليات التثبيت، التحديث، والإزالة.", "Change how UniGetUI installs packages, and checks and installs available updates": "تغيير كيفية قيام UniGetUI بالتحقق من التحديثات المتوفرة لحزمك وتثبيتها", "Change how operations request administrator rights": "تغيير كيفية طلب العمليات لحقوق المسؤول", "Change install location": "تغيير مكان التنزيل", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "البحث عن التحديثات بشكل متكرر", "Check for updates": "التحقق من التحديثات", "Check for updates every:": "البحث عن التحديثات كل:", "Check for updates periodically": "البحث عن التحديثات بشكل متكرر", "Check for updates regularly, and ask me what to do when updates are found.": "البحث عن التحديثات بانتظام, و إعلامي بماذا يجب أن يفعل عند العثور على على التحديثات", "Check for updates regularly, and automatically install available ones.": "تحقق من التحديثات بانتظام وقم بتثبيتها تلقائيًا عند توفرها.", "Check out my {0} and my {1}!": "قم بزيارة {0} و {1} الخاصَّين بي!", "Check out some WingetUI overviews": "ألق نظرة على بعض ملخصات UniGetUI", "Checking for other running instances...": "جاري التحقق من وجود حالات تشغيل أخرى...", "Checking for updates...": "جاري التحقق من وجود تحديثات...", "Checking found instace(s)...": "جاري التحقق من العملية(ات) التي وُجدت...", "Choose how many operations shouls be performed in parallel": "اختر عدد العمليات التي يتم تنفيذها بالتوازي", "Clear cache": "مسح ذاكرة التخزين المؤقت", "Clear finished operations": null, "Clear selection": "إزالة التحديد", "Clear successful operations": "مسح العمليات الناجحة", "Clear successful operations from the operation list after a 5 second delay": "مسح العمليات الناجحة من قائمة العمليات بعد 5 ثوانٍ", "Clear the local icon cache": "مسح ذاكرة التخزين المؤقت للرمز المحلي", "Clearing Scoop cache - WingetUI": "مسح ذاكرة التخزين المؤقت Scoop لـ  UniGetUI", "Clearing Scoop cache...": "تتم إزالة بيانات Scoop المؤقتة...", "Click here for more details": "اضغط هنا للمزيد من التفاصيل", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "انقر فوق \"تثبيت\" لبدء عملية التثبيت. إذا تخطيت عملية التثبيت، فقد لا يعمل UniGetUI بالشكل المتوقع.", "Close": "إغلاق", "Close UniGetUI to the system tray": "إغلاق UniGetUI إلى شريط المهام", "Close WingetUI to the notification area": "إغلاق UniGetUI إلى منطقة الإشعارات", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "نتائج سطر الأوامر", "Command-line to run:": "سطر الأوامر للتشغيل:", "Compare query against": "مقارنة الاستعلام ب", "Compatible with authentication": "متوافق مع المصادقة", "Compatible with proxy": "متوافق مع الوكيل", "Component Information": "معلومات العنصر", "Concurrency and execution": "التزامن والتنفيذ", "Connect the internet using a custom proxy": "الاتصال بشبكة الانترنت باستخدام وكيل مخصص", "Continue": "أكمل", "Contribute to the icon and screenshot repository": "شارك في مخزن الأيقونات و لقطات الشاشة", "Contributors": "المساهمون", "Copy": "نسخ", "Copy to clipboard": "نسخ إلى الحافظة", "Could not add source": "لم يتم إضافة مصدر", "Could not add source {source} to {manager}": "لم يٌتمكن من إضافة المصدر {source} إل<PERSON> {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "لم يتمكن من إنشاء الحزمة", "Could not load announcements - ": "لم يتم تحميل الإعلانات -", "Could not load announcements - HTTP status code is $CODE": "تعذر تحميل الإعلانات - رمز حالة HTTP هو $CODE", "Could not remove source": "لم يتم إزالة المصدر", "Could not remove source {source} from {manager}": "لم تتم إزالة المصدر {source} من {manager}", "Could not remove {source} from {manager}": "لم يتمكن من إزالة {source} من {manager}", "Create .ps1 script": null, "Credentials": "بيانات الاعتماد", "Current Version": "النسخة الحالية", "Current status: Not logged in": null, "Current user": "المستخدم الحالي", "Custom arguments:": "مدخ<PERSON><PERSON><PERSON> خاصة", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "وسائط مخصصة لسطور-الأوامر", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "تخصيص UniGetUI - للمخترقين و المستخدمين المتقدمين فقط", "DEBUG BUILD": "أصدار التصحيح.", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "تحذير: نحن لسنا مسؤولون عن الحزم التي يتم تحميلها. الرجاء التأكد من تحميل الحزم الموثوقة فقط.\n", "Dark": "دا<PERSON>ن", "Decline": "<PERSON><PERSON><PERSON>", "Default": "افتراضي", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "تفضيلات افتراضية - مناسبة للمستخدمين العاديين", "Default vcpkg triplet": "ثلاثية vcpkg الافتراضية", "Delete?": "حذف؟", "Dependencies:": null, "Descendant": "تنازلي", "Description:": "الوصف:", "Desktop shortcut created": "تم إنشاء اختصار سطح المكتب", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "التطوير صعب, وهذا البرنامج مجاني. لكن إن أحببت البرنامج, يمكنك دائما <b>شراء قهوة لي</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "قم يالتثبيت مباشرة عند النقر المزدوج على العنصر في علامة التبويب \"{discoveryTab}\" (بدلاً من عرض معلومات الحزمة).", "Disable new share API (port 7058)": "تعطيل واجهة برمجة تطبيقات المشاركة الجديدة (المنفذ 7058)", "Disable the 1-minute timeout for package-related operations": "تعطيل مهلة الدقيقة الواحدة للعمليات المتعلقة بالحزمة", "Disclaimer": "تنصل (للتوضيح)", "Discover Packages": "استكشف الحزم", "Discover packages": "استكشف الحزم", "Distinguish between\nuppercase and lowercase": "تمييز بين الأحرف الكبيرة والصغيرة", "Distinguish between uppercase and lowercase": "تمييز بين الأحرف الكبيرة والصغيرة", "Do NOT check for updates": "لا تبحث عن التحديثات", "Do an interactive install for the selected packages": "تثبيت تفاعلي للحزم المحددة", "Do an interactive uninstall for the selected packages": "إلغاء التثبيت تفاعلي للحزم المحددة", "Do an interactive update for the selected packages": "تحديث تفاعلي للحزم المحددة", "Do not automatically install updates when the battery saver is on": "لا تقم بتثبيت التحديثات تلقائياً عند تشغيل وضع توفير الطاقة", "Do not automatically install updates when the network connection is metered": "لا تقم بتثبيت التحديثات تلقائياً عندما يكون اتصال الشبكة محدود", "Do not download new app translations from GitHub automatically": "لا تقم بتنزيل ترجمات التطبيق الجديدة من GitHub تلقائياً", "Do not ignore updates for this package anymore": "لا تتجاهل التحديثات الخاصة بهذه الحزمة بعد الآن", "Do not remove successful operations from the list automatically": "لا تقم بحذف العمليات الناجحة من القائمة تلقائياً", "Do not show this dialog again for {0}": "لا تعرض مربع الحوار هذا مرة أخرى {0}", "Do not update package indexes on launch": "عدم تحديث القوائم عند البدء", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "هل تقبل أن يقوم UniGetUI بجمع وإرسال إحصائيات استخدام مجهولة الهوية، من أجل فهم وتحسين تجربة المتسخدم فقط؟", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "هل تجد UniGetUI مفيدًا؟ إذا كان بوسعك ذلك، فقد ترغب في دعم عملي، حتى أتمكن من الاستمرار في جعل UniGetUI واجهة إدارة الحزم المثالية.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "هل تجد UniGetUI مفيداً؟ هل ترغب بدعم المطوّر؟ إذا كنت كذلك, يمكنك أن {0}, فذلك يساعد كثيراً!", "Do you really want to reset this list? This action cannot be reverted.": "هل ترغب فعلاً بإعادة تعيين هذه القائمة؟ هذا الإجراء لا يمكن التراجع عنه.", "Do you really want to uninstall the following {0} packages?": "هل ترغب حقاً بإزالة تثبيت المصادر {0}؟  ", "Do you really want to uninstall {0} packages?": "هل تريد حقأ إلغاء تثبيت {0} حزمة؟", "Do you really want to uninstall {0}?": "هل تريد حقأ إلغاء تثبيت {0}؟", "Do you want to restart your computer now?": "هل تريد إعادة تشغيل الكمبيوتر الآن؟", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "هل تريد ترجمة UniGetUI إلى لغتك؟ لمعرفة كيفية المشاركة انظر <a style=\"color:{0}\" href=\"{1}\"a>هنا!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "لا تشعر بالرغبة في التبرع؟ لا تقلق، يمكنك دائمًا مشاركة UniGetUI مع أصدقائك. انشر كلمة عن UniGetUI.", "Donate": "تبرع", "Done!": null, "Download failed": "فشل التحميل", "Download installer": "تحميل المُنصِّب", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "تم التحميل بنجاح", "Download updated language files from GitHub automatically": "تحميل ملف اللغة المُحدثة من GitHub تلقائياً", "Downloading": "يتم التحميل", "Downloading backup...": null, "Downloading installer for {package}": "جاري تنزيل برنامج التثبيت لـ {package}", "Downloading package metadata...": "يتم تحميل البيانات الوصفية للحزم...", "Enable Scoop cleanup on launch": "تفعيل تنظي<PERSON> <PERSON>oop عند البدأ", "Enable WingetUI notifications": "تفعيل إشعارات UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "تفعيل مستكشف أخطاء WinGet [التجريبي] المحسّن", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "تفعيل تحسينات استخدام وحدة المعالجة المركزية في الخلفية (انظر الطلب #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "تمكين واجهة برمجة التطبيقات الخلفية (أدوات UniGetUI والمشاركة، المنفذ 7058)", "Enable it to install packages from {pm}.": "قم بتفعيله لتثبيت الحزم من {pm}.", "Enable the automatic WinGet troubleshooter": "تمكين مستكشف أخطاء WinGet التلقائي", "Enable the new UniGetUI-Branded UAC Elevator": "تفعيل UniGetUI-Branded UAC Elevator الجديد", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "تفعيل {pm}", "Enter proxy URL here": "أدخل رابط الخادم هنا", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "خطأ", "Everything is up to date": "كل شيءٍ مُحدّث", "Exact match": "تطابق تام\n", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "الاختصارات الموجودة على سطح المكتب سيتم مسحها، وستحتاج إلى اختيار أيٍّ منها لتبقى وأيٍّ منها لتُحذَف.", "Expand version": "الإصدار الكامل", "Experimental settings and developer options": "إعدادات تجريبية و خيارات المطور", "Export": "تصدير", "Export log as a file": "تصدير السجل كملف", "Export packages": "تصدير الحزم", "Export selected packages to a file": "تصدير الحزم المحددة إلى ملف", "Export settings to a local file": "تصدير الإعدادات إلى ملف محلي", "Export to a file": "تصدير إلى ملف", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "\nيتم جلب أحدث الإعلانات، يرجى الانتظار...", "Filters": "فرز", "Finish": "إنهاء", "Follow system color scheme": "اتباع نمط ألوان النظام", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "استخدم إصدار winget المبني  لمعمارية ARM (فقط لأنظمة ARM64)", "Formerly known as WingetUI": "المعروف سابقًا باسم WingetUI", "Found": "مو<PERSON><PERSON><PERSON>", "Found packages: ": "الحُزم المعثور عليها:", "Found packages: {0}": "الحزم الموجودة: {0}", "Found packages: {0}, not finished yet...": "الحزم الموجودة: {0}, لم يتم الانتهاء بعد...", "General preferences": "التفضيلات العامة", "GitHub profile": "الملف الشخصي على GitHub", "Global": "عالمي", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "حزمة عظيمة من الأدوات المجهولة ولكن مفيدة، بالإضافة إلى حزمات مثيرة للإهتمام.<br>تحتوي: </br>أدوات، سطور أوامر، برامج، وبرمجيات عامة (بحاجة لحاوية إضافية", "Great! You are on the latest version.": "رائع! أنت تستخدم الإصدار الأحدث.", "Grid": "شبكة", "Help": "مساعدة", "Help and documentation": "المساعدة والوثائق", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "يمكنك هنا تغيير سلوك UniGetUI فيما يتعلق بالاختصارات التالية. سيؤدي تحديد اختصار إلى جعل UniGetUI يحذفه إذا تم إنشاؤه في ترقية مستقبلية. سيؤدي إلغاء تحديده إلى إبقاء الاختصار سليمًا", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "مرحباً, اسمي <PERSON>, أنا <i>مطوّر</i> UniGetUI. تمت صناعة UniGetUI بشكل كامل في وقت فراغي!\n", "Hide details": "إخفاء التفاصيل", "Homepage": "الصفحة الرئيسية", "Hooray! No updates were found.": "يا للسعادة! لم يتم العثور على أية تحديثات!", "How should installations that require administrator privileges be treated?": "كيف يجب أن تعامل التثبيتات التي تتطلب صلاحيات المسؤول؟", "How to add packages to a bundle": "كيفية إضافة الحزم إلى الباقة", "I understand": "أنا أتفهم", "Icons": "أيقونات", "Id": "معرّف", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "تجاهل التحديثات المستقبلية لهذه الحزمة", "Ignore packages from {pm} when showing a notification about updates": "تجاهل الحزم من {pm} عند إظهار إشعار عن التحديث", "Ignore selected packages": "تجاهل الحزم المحددة", "Ignore special characters": "تجاهل الأحرف الخاصة", "Ignore updates for the selected packages": "تجاهل التحديثات للحزم المحددة", "Ignore updates for this package": "تجاهل التحديثات لهذه الحزمة", "Ignored updates": "التحديثات المتجاهلة", "Ignored version": "الإصدار المتجاهل", "Import": "إستيراد", "Import packages": "استيراد الحزم", "Import packages from a file": "استيراد الحزم من ملف", "Import settings from a local file": "إستيراد الإعدادات من ملف محلي", "In order to add packages to a bundle, you will need to: ": "من أجل إضافة حزم إلى الباقة، ستحتاج إلى:", "Initializing WingetUI...": "جارٍ تهيئة UniGetUI ...", "Install": "تثبيت", "Install Scoop": "تثبيت Scoop", "Install and more": null, "Install and update preferences": "تثبيت وتحديث التفضيلات", "Install as administrator": "تثبيت كمسؤول", "Install available updates automatically": "تثبيت التحديثات المتوفرة تلقائيًا", "Install location can't be changed for {0} packages": null, "Install location:": "مكان التثبيت:", "Install options": null, "Install packages from a file": "تثبيت الحزم من ملف", "Install prerelease versions of UniGetUI": "تثبيت الإصدارات التجريبية من UniGetUI", "Install script": null, "Install selected packages": "تثبيت الحزم المحددة", "Install selected packages with administrator privileges": "تثبيت الحزم المحددة بصلاحيات المسؤول", "Install selection": "تثبيت المُحدد", "Install the latest prerelease version": "تثبيت آخر نسخة أولية", "Install updates automatically": "تثبيت التحديثات تلقائياً", "Install {0}": "تثبيت {0}", "Installation canceled by the user!": "تم إلغاء التثبيت بواسطة المستخدم", "Installation failed": "فشل التنزيل", "Installation options": "خيارات التثبيت", "Installation scope:": "التثبيت scope:", "Installation succeeded": "نجاح التنزيل", "Installed Packages": "الحزم المثبتة", "Installed Version": "الإصدار المثبت", "Installed packages": "الحزم المثبتة", "Installer SHA256": "SHA256 للمثبت", "Installer SHA512": "SHA512 للمثبت:", "Installer Type": "نوع المثبت", "Installer URL": "رابط المثبت", "Installer not available": "المثبت غير متوفر", "Instance {0} responded, quitting...": "رد النموذج {0}, يتم الإغلاق...", "Instant search": "البحث الفوري", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "عمليات التحقق من السلامة تم تخطيها", "Integrity checks will not be performed during this operation": "عمليات التحقق من السلامة لن يتم تنفيذها أثناء هذه العملية", "Interactive installation": "تثبيت تفاعلي", "Interactive operation": "عملية تفاعلية", "Interactive uninstall": "إلغاء تثبيت تفاعلي", "Interactive update": "تحديث تفاعلي", "Internet connection settings": "إعدادات اتصال الانرنت", "Is this package missing the icon?": "هل أيقونة هذه الحزمة مفقودة؟", "Is your language missing or incomplete?": "هل لغتك مفقودة أم غير مكتملة؟", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "ليس مضموناً أن بيانات الاعتماد المقدّمة سيتم حفظها بأمان، لذلك لا تقم باستخدام بيانات حسابك المصرفي", "It is recommended to restart UniGetUI after WinGet has been repaired": "يوصى بإعادة تشغيل UniGetUI بعد إصلاح WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "يبدو أن WinGet لا يعمل بشكل صحيح. هل تريد محاولة إصلاح WinGet؟", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "يبدو أنك قمت بتشغيل UniGetUI كمسؤول, و هذا غير مستحسن. لايزال بإمكانك استعمال البرنامج, لكننا ننصحك بشدة أن لا تشغل UniGetUI كمسؤول. انقر على \"{showDetails}\" لتعرف لمذا.", "Language": "اللغة", "Language, theme and other miscellaneous preferences": "اللغة, المظهر و تفضيلات متنوعة أخرى", "Last updated:": "تم التحديث آخر مرة:", "Latest": "الأخير", "Latest Version": "الإصدار الأخير", "Latest Version:": "الإصدار الأخير:", "Latest details...": "التفاصيل الأخيرة...", "Launching subprocess...": "بدأ العملية الفرعية...", "Leave empty for default": "اتركه فارغًا للإفتراضي", "License": "الترخيص", "Licenses": "التراخيص", "Light": "فاتح", "List": "قائمة", "Live command-line output": "سطور الأوامر الناتجة بشكل مباشر", "Live output": "الإخراج المباشر", "Loading UI components...": "يتم تحميل عناصر الواجهة الرسومية...", "Loading WingetUI...": "يتم تحميل UniGetUI...", "Loading packages": "تحميل الحزم", "Loading packages, please wait...": "يتم تحميل الحُزم, يرجى الإنتظار...", "Loading...": "يتم التحميل...", "Local": "م<PERSON><PERSON>ي", "Local PC": "الكمبيوتر المحلي", "Local backup advanced options": null, "Local machine": "الجه<PERSON><PERSON> المحلي", "Local package backup": null, "Locating {pm}...": "يتم تحديد موقع {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "مستوى السجل:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "جاري البحث عن الحزم...", "Machine | Global": "Machine | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "<PERSON><PERSON><PERSON>ر", "Manage UniGetUI settings": "إدارة إعدادات UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "إدارة سلوك التشغيل التلقائي لـ UniGetUI من تطبيق الإعدادات", "Manage ignored packages": "إدارة الحزم المتجاهلة", "Manage ignored updates": "إدارة التحديثات المتجاهلة", "Manage shortcuts": "إدارة الاختصارات", "Manage telemetry settings": "إدارة إعدادات القياس عن بعد", "Manage {0} sources": "إدارة {0} من المصادر", "Manifest": "القائمة الأساسية", "Manifests": "القوائم الأساسية", "Manual scan": "الفحص اليدوي.", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "مدير الحزم الرسمي لشركة Microsoft. مليئة بالحزم المعروفة والتي تم التحقق منها <br> تحتوي على: <b> برامج عامة ، تطبيقات متجر مايكروسوفت</b>", "Missing dependency": "الاعتمادات مفقود", "More": "أكثر", "More details": "تفاصيل أكثر", "More details about the shared data and how it will be processed": "المزيد من التفاصيل عن البيانات التم يتم مشاركتها وكيف سيتم معالجتها", "More info": "معلومات أكثر", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "ملاحظة: يمكن تعطيل أداة استكشاف الأخطاء وإصلاحها هذه من إعدادات UniGetUI، في قسم WinGet", "Name": "الاسم", "New": null, "New Version": "الإصدار الجديد", "New bundle": "حزمة جديدة", "New version": "الإصدار الجديد", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "لا", "No applicable installer was found for the package {0}": "لم يتم العثور على مثبّت مناسب لهذه الحزمة {0}", "No dependencies specified": null, "No new shortcuts were found during the scan.": "فشل العثور على اختصارات جديدة أثناء الفحص.", "No packages found": "لم يتم العثور على حزم", "No packages found matching the input criteria": "لم يتم العثور على حزم مطابقة للمعايير المدخلة", "No packages have been added yet": "لم تتم إضافة الحُزم حتى الآن", "No packages selected": "لم يتم تحديد حزم", "No packages were found": "لم يتم العثور على الحُزم", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "لن يتم جمع أو إرسال أية معلومات شخصية، والبيانات التي يتم جمعها ستكون مجهولة المصدر، لذا لا يمكن تتبعها رجوعاً إليك.", "No results were found matching the input criteria": "لم يتم العثور على نتائج مطابقة لمعايير الإدخال", "No sources found": "لم يتم العثور على مصادر", "No sources were found": "لم يتم العثور على مصادر", "No updates are available": "لا يوجد تحديثات متاحة", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "مدير حزم Node JS. مليئة بالمكتبات والأدوات المساعدة الأخرى التي تدور حول عالم جافا سكريبت <br> تحتوي على: <b> مكتبات Node javascript والأدوات المساعدة الأخرى ذات الصلة </ b>", "Not available": "<PERSON>ير متاح", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "<PERSON>ير موجود", "Not right now": "ليس الان", "Notes:": "ملاحظات:", "Notification preferences": "تفضيلات الإشعارات", "Notification tray options": "خيارات قسم الإشعارات", "Notification types": "أنواع التنبيهات", "NuPkg (zipped manifest)": "NuPkg (بيان مضغوط)", "OK": "حسناً", "Ok": "حسناً", "Open": "فتح", "Open GitHub": "<PERSON><PERSON><PERSON>", "Open UniGetUI": "فتح UniGetUI", "Open UniGetUI security settings": null, "Open WingetUI": "فتح UniGetUI", "Open backup location": "فتح مكان النسخة الاحتياطية", "Open existing bundle": "فتح الحزمة الموجودة", "Open install location": "فتح موقع التثبيت", "Open the welcome wizard": "فتح صفحة الترحيب", "Operation canceled by user": "تم إلغاء العملية بواسطة المستخدم", "Operation cancelled": "تم إلغاء العملية", "Operation history": "سجل العمليات", "Operation in progress": "العملية قيد التنفيذ", "Operation on queue (position {0})...": "العملية على قائمة الانتظار (الموضع {0})...", "Operation profile:": null, "Options saved": "تم حفظ الخيارات", "Order by:": "رتب بحسب:", "Other": "أ<PERSON><PERSON><PERSON>", "Other settings": "إعدادات أخرى", "Package": null, "Package Bundles": "حزم الحزم", "Package ID": "معرف الحزمة", "Package Manager": "مدير الحُزم", "Package Manager logs": "سجلات نظام إدارة الحزم", "Package Managers": "مديرو الحزم", "Package Name": "اسم الحزمة", "Package backup": "النسخة الاحتياطية للحزمة", "Package backup settings": null, "Package bundle": "حزمة الحزمة", "Package details": "تفاصيل الحزمة", "Package lists": "قوائم الحزمة", "Package management made easy": "إدارة الحزم أصبحت سهلة", "Package manager": "مدير الحزم", "Package manager preferences": "تفضيلات مدير الحزم", "Package managers": "مدراء الحزم", "Package not found": "الحزمة غير موجودة", "Package operation preferences": "تفضيلات عملية الحزمة", "Package update preferences": "تفضيلات تحديث الحزمة", "Package {name} from {manager}": "الحزمة {name} من {manager}", "Package's default": null, "Packages": "الحزم", "Packages found: {0}": "الحزم المعثور عليها: {0}", "Partially": "جزئياً", "Password": "كلمة المرور", "Paste a valid URL to the database": "إلصق عنوان URL صالح الي قاعدةالبيانات", "Pause updates for": "إيقا<PERSON> التحديثات لـ", "Perform a backup now": "القيام بالنسخ الإحتياطي الآن", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "يتم النسخ الإحتياطي الآن, يرجى الإنتظار...", "Periodically perform a backup of the installed packages": "بشكل دوري قم بالنسخ الإحتياطي للحُزم المُثبتّة", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "يرجى التحقق من خيارات التثبيت لهذه الحزمة ثم المحاولة مرة أخرى", "Please click on \"Continue\" to continue": "الرجاء الضغط على \"متابعة\" للمتابعة", "Please enter at least 3 characters": "ير<PERSON>ى إدخال على الأقل 3 أحرف", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "من فضلك انتبه أن حزم محددة قد لا تكون قابلة للتثبيت, بسبب مدراء الحزم المفعلة على هذا الجهاز.", "Please note that not all package managers may fully support this feature": "يرجى ملاحظة أنه قد لا يمكن لجميع إدارات الحزمة ان تدعم هذه الخاصية", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "من فضلك انتبه أن حزم من مصادر محددة قد لا تكون قابلة للتصدير. لقد تم تظليلها ولن يتم تصديرها.", "Please run UniGetUI as a regular user and try again.": "يرجى تشغيل UniGetUI كمستخدم عادي ثم حاول مرة أخرى.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "يرجى الاطلاع على مخرجات سطر الأوامر أو الرجوع إلى سجل العمليات للحصول على مزيد من المعلومات حول المشكلة.", "Please select how you want to configure WingetUI": "من فضلك اختر كيف تريد إعداد UniGetUI", "Please try again later": "يرجى المحاولة مرة أخرى لاحقًا", "Please type at least two characters": "الرجاء كتابة حرفين على الأقل", "Please wait": "ير<PERSON>ى الإنتظار", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "يرجى الانتظار أثناء تثبيت {0}. قد تظهر نافذة سوداء. يرجى الانتظار حتى يتم إغلاقها.", "Please wait...": "الرجاء الانتظار...", "Portable": "محمول", "Portable mode": "الوضع المتنقل.", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "مدير حزم PowerShell. ابحث عن المكتبات والبرامج النصية لتوسيع إمكانيات PowerShell<br>يحتوي على: <b>وحدات نمطية وبرامج نصية وأدوات أوامر</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "إصدار مبدأي", "Preparing packages, please wait...": "جاري تحضير الحزم ، يرجى الانتظار...", "Proceed at your own risk.": "التباعة على مسؤوليتك الخاصة.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "عنوان URL الخاص بالوكيل", "Proxy compatibility table": "جدول توافق الوكيل", "Proxy settings": "إعدادات الوكيل", "Proxy settings, etc.": "إعدادات الوكيل، وأخرى.", "Publication date:": "تاريخ النشر:", "Publisher": "الناشر", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "مدير مكتبة بايثون. مليء بمكتبات بايثون وأدوات مساعدة أخرى متعلقة بالبايثون<br>يحتوي على: <b>مكتبات بايثون وأدوات مساعدة متعلقة</b>", "Quit": "خروج", "Quit WingetUI": "الخروج من UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "إعادة التثبيت", "Reinstall package": "إعادة تثبيت الحزمة", "Related settings": "إعدادات مرتبطة", "Release notes": "ملاحظات الإصدار", "Release notes URL": "ملاحظات الإصدار URL", "Release notes URL:": "رابط ملاحظات الإصدار:", "Release notes:": "ملاحظات الإصدار:", "Reload": "إعادة تحميل", "Reload log": "إعادة تحميل السجل", "Removal failed": "فشلت الإزالة", "Removal succeeded": "تمت الإزالة بنجاح", "Remove from list": "إزالة من القائمة", "Remove permanent data": "إزالة البيانات الدائمة", "Remove selection from bundle": "حذ<PERSON> المحد<PERSON> من الحزمة", "Remove successful installs/uninstalls/updates from the installation list": "إزالة عمليات التثبيت\\عمليات إلغاء التثبيت\\التحديثات الناجحة من القائمة التثبيت", "Removing source {source}": "إزالة المصدر {source}", "Removing source {source} from {manager}": "إزالة المصدر {source} من {manager}", "Repair UniGetUI": null, "Repair WinGet": "إصلا<PERSON>", "Report an issue or submit a feature request": "الإبلاغ عن مشكلة أو تقديم طلب ميزة", "Repository": "مخزن", "Reset": "إعادة ضبط", "Reset Scoop's global app cache": "إعادة تعيين ذاكرة التخزين المؤقت لتطبيق Scoop العالمي", "Reset UniGetUI": "إعادة تعيين UniGetUI", "Reset WinGet": "إعادة تعيين WinGet", "Reset Winget sources (might help if no packages are listed)": "إعادة ضبط مصادر Winget (من الممكن أن يساعد عند عدم ظهور الحزم في القائمة)", "Reset WingetUI": "إعادة تعيين UniGetUI", "Reset WingetUI and its preferences": "إعادة تعيين UniGetUI و تفضيلاته", "Reset WingetUI icon and screenshot cache": "إعادة ضبط الذاكرة المؤقتة لأيقونات و لقطات شاشة UniGetUI", "Reset list": "إعادة تعيين القائمة", "Resetting Winget sources - WingetUI": "إعادة تعيين مصادر UniGetUI - WinGet", "Restart": "إعادة التشغيل", "Restart UniGetUI": "أعد تشغيل UniGetUI", "Restart WingetUI": "إعادة تشغيل UniGetUI", "Restart WingetUI to fully apply changes": "أعد تشغيل UniGetUI لتطبيق التغييرات بشكل تام", "Restart later": "إعادة التشغيل لاحقاً", "Restart now": "إعادة التشغيل الآن", "Restart required": "إعادة التشغيل مطلوبة", "Restart your PC to finish installation": "إعادة التشغيل كمبيوترك لإنهاء التثبيت", "Restart your computer to finish the installation": "إعادة التشغيل كمبيوترك لإنهاء التثبيت", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "إعادة المحاولة", "Retry as administrator": "المحاولة كمسؤول", "Retry failed operations": "المحاولة للعمليات الفاشلة", "Retry interactively": "المحاولة تفاعلياً", "Retry skipping integrity checks": "إعادة محاولة عمليات التحقق من السلامة التي تم تخطيها", "Retrying, please wait...": "تتم إعادة المحاولة, يرجى الإنتظار... ", "Return to top": "عودة للأعلى", "Run": "تشغيل", "Run as admin": "تشغيل كمسؤول", "Run cleanup and clear cache": "قم بتشغيل التنظيف ومسح ذاكرة التخزين المؤقت", "Run last": "تشغيل الأخير", "Run next": "تشغيل التالي", "Run now": "تشغيل الآن", "Running the installer...": "جاري تشغيل المثبت ...", "Running the uninstaller...": "جاري تشغيل إزالة التثبيت...", "Running the updater...": "جاري تشغيل المحدث...", "Save": null, "Save File": "<PERSON><PERSON><PERSON> الملف", "Save and close": "حفظ وإغلاق", "Save as": null, "Save bundle as": "حفظ الحزمة كـ", "Save now": "<PERSON><PERSON><PERSON> الآن", "Saving packages, please wait...": "يتم حفظ الحُزم, يرجى الإنتظار...", "Scoop Installer - WingetUI": "UniGetUI - م<PERSON><PERSON><PERSON>oop", "Scoop Uninstaller - WingetUI": "UniGetUI - إلغاء تثبيت Scoop", "Scoop package": "<PERSON><PERSON><PERSON>", "Search": "ب<PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "ابحث عن برامج سطح المكتب ، وحذرني عندما تكون التحديثات متاحة ولا تفعل أشياء غير مألوفة. لا أرغب في زيادة تعقيد UniGetUI ، أريد فقط <b> متجر برامج </ b> بسيطًا", "Search for packages": "البحث عن حزم", "Search for packages to start": "ابحث عن الحزم للبدء", "Search mode": "وضع البحث", "Search on available updates": "البحث عن التحديثات المتاحة", "Search on your software": "البحث عن برنامجك", "Searching for installed packages...": "يتم البحث عن الحزم المثبتة...", "Searching for packages...": "يتم البحث عن الحزم...", "Searching for updates...": "جاري البحث ععن تحديثات...", "Select": "تحديد", "Select \"{item}\" to add your custom bucket": "حدد \"{item}\" لإضافة الحزمة المخصصة الخاصة بك", "Select a folder": "تحدي<PERSON> مجلد", "Select all": "تحدي<PERSON> الكل", "Select all packages": "إختيار جميع الحزم", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "حدد فقط <b> إذا كنت تعرف ما تفعله </ b>.", "Select package file": "تحديد ملف الحزم", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "قم تحديد المصدر المُراد إضافته:", "Select upgradable packages by default": "حد<PERSON> الحزم التي سيتم ترقيتها بشكل افتراضي", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "حدد <b> م<PERSON>ي<PERSON><PERSON> الحزم </ b> لاستخدام ({0}) ، وتهيئة كيفية تثبيت الحزم ، وإدارة كيفية التعامل مع صلاحيات المسؤول ، وما إلى ذلك.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "تم إرسال المصافحة. يتم انتظار رد مستمعة العمليات ({0}%)", "Set a custom backup file name": "تعيين اسم ملف النسخ الاحتياطي المخصص", "Set custom backup file name": "تعيين اسم لملف النسخة الإحتياطية المخصصة", "Settings": "الإعدادات", "Share": "مشاركة", "Share WingetUI": "مشاركة UniGetUI", "Share anonymous usage data": "مشاركة بيانات استخدام مجهولة المصدر", "Share this package": "مشاركة هذه الحزمة", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "إظهار UniGetUI على شريط المهام", "Show UniGetUI's version and build number on the titlebar.": "إظهار نسخة UniGetUI على شرط العنوان", "Show WingetUI": "إظهار UniGetUI", "Show a notification when an installation fails": "إظهار تنبيه عند فشل التثبيت", "Show a notification when an installation finishes successfully": "إظهار تنبيه عند انتهاء التثبيت بنجاح", "Show a notification when an operation fails": "إظهار إشعار عند فشل العملية", "Show a notification when an operation finishes successfully": "إظهار إشعار عند انتهاء العملية بنجاح", "Show a notification when there are available updates": "إظهار إشعار عند توفر تحديثات", "Show a silent notification when an operation is running": "إظهار إشعار صامت عند تشغيل عملية ما", "Show details": "إظهار التفاصيل", "Show in explorer": "الإظهار في المستكشف", "Show info about the package on the Updates tab": "إظهار معلومات حول الحزمة في تبويبة التحديثات", "Show missing translation strings": "إظهار نصوص الترجمات المفقودة", "Show notifications on different events": "إظهار الإشعارات حول الأحداث المختلفة", "Show package details": "إظهار تفاصيل الحزمة", "Show package icons on package lists": "إظهار أيقونات الحزمة في قوائم الحزمة", "Show similar packages": "إظهار الحُزم المتشابهة", "Show the live output": "إظهار الناتج بشكل مباشر", "Size": "الحجم", "Skip": "تخطي", "Skip hash check": "تخطي تحقق hash", "Skip hash checks": "تخطي عمليات التحقق من hash", "Skip integrity checks": "تخطي عمليات التحقق من السلامة", "Skip minor updates for this package": "تخطي التحديثات البسيطة لهذه الحزمة", "Skip the hash check when installing the selected packages": "تخطي فحص الهاش عند تثبيت الحزم المحددة", "Skip the hash check when updating the selected packages": "تخطي فحص الهاش عند تحديث الحزم المحددة", "Skip this version": "تخطي هذه النسخة", "Software Updates": "تحديثات البرامج", "Something went wrong": "حد<PERSON> خطأ ما", "Something went wrong while launching the updater.": "لقد حدث خطأ ما أثناء تشغيل برنامج التحديث.", "Source": "المصدر", "Source URL:": "رابط المصدر:", "Source added successfully": "تم إضافة المصدر بنجاح", "Source addition failed": "فشلت إضافة المصدر", "Source name:": "اسم المصدر:", "Source removal failed": "فشل إزالة المصدر", "Source removed successfully": "تم حذف المصدر بنجاح", "Source:": "المصدر:", "Sources": "المصادر", "Start": "بدء", "Starting daemons...": "يتم بدء الخدمات... ", "Starting operation...": "يجري التسغيل...", "Startup options": "خيارات بدأ التشغيل", "Status": "الحالة", "Stuck here? Skip initialization": "علقت هنا؟ تخطي التهيئة", "Success!": null, "Suport the developer": "قم بدعم المؤلف", "Support me": "ادعمني", "Support the developer": "ادعم المُطوِّر", "Systems are now ready to go!": "الأنظمة جاهزة الآن للانطلاق!", "Telemetry": "القياس عن بُعد", "Text": "نص", "Text file": "مل<PERSON> نصي", "Thank you ❤": "شكراً لك ❤️", "Thank you 😉": "شكراً لك 😉 ", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "مدير حزمة Rust.<br>يحتوي على: <b>مكتبات Rust والبرامج المكتوبة بلغة Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "لن تتضمن النسخة الاحتياطية أي ملف ثنائي أو أي بيانات محفوظة بواسطة برامج.", "The backup will be performed after login.": "سيتم إجراء النسخ الاحتياطي بعد تسجيل الدخول.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "لن تتضمن النسخة الاحتياطية أي ملف مصدري أو أي بيانات محفوظة بواسطة برامج.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "يبدو أن الحزمة التي تحاول تحميلها غير صالحة. يرجى التحقق من الملف والمحاولة مرة أخرى.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "لا يتطابق المجموع الاختباري للمثبت مع القيمة المتوقعة ، ولا يمكن التحقق من أصالة المثبت. إذا كنت تثق في الناشر ، {0} ستتخطى الحزمة فحص التجزئة مرة أخرى.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "نظام إدارة الحزم الكلاسيكي لويندوز. ستجد كل شيء هناك. <br> يحتوي على: <b> برامج عامة </ b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "لا تحتوي الحزمة الحالية على أي حزم. أضف بعض الحزم للبدء", "The executable file for {0} was not found": "الملف القابل للتنفيذ لـ {0} لم يتم إيجاده", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "سيتم تصدير الحزم التالية إلى ملف JSON. لن يتم حفظ بيانات المستخدم أو الثنائيات.", "The following packages are going to be installed on your system.": "سيتم تثبيت الحزم التالية على نظامك.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "سيتم تطبيق الإعدادات التالية في كل مرة يتم فيها تثبيت هذه الحزمة أو تحديثها أو إزالتها.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "سيتم تطبيق الإعدادات التالية في كل مرة يتم فيها تثبيت هذه الحزمة أو تحديثها أو إزالتها. وسيتم حفظها تلقائيًا.", "The icons and screenshots are maintained by users like you!": "تتم رعاية الأيقونات و لقطات الشاشة بواسطة مستخدمين مثلك!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "لم يتم التحقق من صحة المثبت.", "The installer has an invalid checksum": "المثبت يحتوي مجموع اختباري غير صالح", "The installer hash does not match the expected value.": "لا يتطابق hash المثبت مع القيمة المتوقعة.", "The local icon cache currently takes {0} MB": "تبلغ مساحة ذاكرة التخزين المؤقتة للرمز المحلي حاليًا {0} ميجا بايت", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "الهدف الأساسي من هذا المشروع هو إنشاء واجهة سهلة الإستخدام لإدارة مدراء حزم CLI الأكثر شيوعاً لـ Windows. مثل Winget و Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "لم يتم العثور على الحزمة \"{0}\" في مدير الحزم \"{1}\"", "The package bundle could not be created due to an error.": "لم يتم إنشاء حزمة الحزمة بسبب خطأ.", "The package bundle is not valid": "الحزمة غير صالحة", "The package manager \"{0}\" is disabled": "تم تعطيل مدير الحزمة \"{0}\"", "The package manager \"{0}\" was not found": "لم يتم العثور على مدير الحزمة \"{0}\"", "The package {0} from {1} was not found.": "لم يتم العثور على الحزمة {0} من {1}.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "لن يتم أخذ الحزم المدرجة هنا في الاعتبار عند التحقق من وجود تحديثات. انقر نقرًا مزدوجًا فوقهم أو انقر فوق الزر الموجود على يمينهم للتوقف عن تجاهل تحديثاتهم.", "The selected packages have been blacklisted": "تم إدراج الحزمة المحددة في قائمة الحظر.", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "حجم النسخ الإحتياطي مُقدّر بأن يكون أقل من 1 ميغابايت.", "The source {source} was added to {manager} successfully": "تمت إضافة المصدر {source} إ<PERSON><PERSON> {manager} بنجاح", "The source {source} was removed from {manager} successfully": "تم إزالة المصدر {source} من {manager} بنجاح", "The system tray icon must be enabled in order for notifications to work": "يجب تفعيل أيقونة شريط المهام لكي تعمل الإشعارات", "The update process has been aborted.": "لقد تم إلغاء عملية التحديث.", "The update process will start after closing UniGetUI": "ستبدأ عملية التحديث بعد إغلاق UniGetUI", "The update will be installed upon closing WingetUI": "سيتم تثبيت التحديث عند إغلاق UniGetUI", "The update will not continue.": "لن يستمر التحديث.", "The user has canceled {0}, that was a requirement for {1} to be run": "المستخدم قام بإلغاء {0}، والتي كانت مطلوبة لـ {1} لكي يعمل", "There are no new UniGetUI versions to be installed": "لا توجد إصدارات جديدة من UniGetUI ليتم تثبيتها\n", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "هناك عمليات جارية. قد يؤدي إغلاق UniGetUI إلى فشلها. هل تريد الاستمرار؟", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "هناك بعض مقاطع الفيديو الرائعة على YouTube تعرض UniGetUI وقدراتها. يمكنك تعلم الحيل والنصائح المفيدة!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "هناك سببان رئيسيان لعدم تشغيل UniGetUI كمسؤول: السب<PERSON> الأول هو أن مدير الحزم Scoop يمكن أن يسبب مشاكل مع بعض الأوامر عندما يُشغَّل كمسؤول. السبب الثاني هو أن تشغيل WingetUI كمسؤول يعني أن أي حزمة سوف تحملها ستعمل كمسؤول (وهذا غير آمن). تذكر أنك إذا أردت أن تثبت حزمة معينة كمسؤوول, يمكنك دائماً أن تنقر نقرة يمنى على العنصر -> تثبيت\\تحديث\\إلغاء تثبيت كمسؤول.\n", "There is an error with the configuration of the package manager \"{0}\"": "يو<PERSON><PERSON> خطأ في تكوين مدير الحزم \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "هناك تثبيت قيد التقدم. إذا أغلقت UniGetUI ، فقد يفشل التثبيت وتكون له نتائج غير متوقعة. هل ما زلت تريد إنهاء UniGetUI؟", "They are the programs in charge of installing, updating and removing packages.": "هم البرامج المسؤولة عن تثبيت الحزم وتحديثها وإزالتها.", "Third-party licenses": "تراخيص الطرف الثالث", "This could represent a <b>security risk</b>.": "قد يمثل هذا <b> خطرًا أمنيًا </ b>.", "This is not recommended.": "غير موصى به", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "ربما يرجع هذا إلى حقيقة أن الحزمة التي تم إرسالها قد تمت إزالتها أو هي منشورة على مدير حزم  لم تقم بتمكينه. المعرف المستلم هو {0}", "This is the <b>default choice</b>.": "هذا هو <b> الخيار الافتراضي </ b>.", "This may help if WinGet packages are not shown": "قد يكون هذا مفيدًا إذا لم يتم عرض حزم WinGet", "This may help if no packages are listed": "قد يكون هذا مفيدًا إذا لم يتم إدراج أي حزم", "This may take a minute or two": "قد يستغرق هذا دقيقة أو دقيقتين", "This operation is running interactively.": "هذه العملية تعمل بشكل تفاعلي.", "This operation is running with administrator privileges.": "هذه العملية تعمل بصلاحيات المسؤول", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "هذه الحزمة يمكن تحديثها.", "This package can be updated to version {0}": "يمكن تحديث هذه الحزمة إلى الإصدار {0}", "This package can be upgraded to version {0}": "يمكن تحديث هذه الحزمة إلى الإصدار {0}", "This package cannot be installed from an elevated context.": "لا يمكن تثبيت هذه الحزمة من سياق مرتفع.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "هل لا تحتوي هذه الحزمة على لقطات شاشة أو تفتقد الرمز؟ ساهم في UniGetUI عن طريق إضافة الأيقونات ولقطات الشاشة المفقودة إلى قاعدة البيانات العامة المفتوحة لدينا.", "This package is already installed": "هذه الحزمة مثبتة سابقا ", "This package is being processed": "تتم معالجة هذه الحزمة", "This package is not available": "هذه الحزمة غير متوفرة", "This package is on the queue": "هذه الحزمة موجودة في قائمة الانتظار", "This process is running with administrator privileges": "هذه العملية تعمل بصلاحيات المسؤول", "This project has no connection with the official {0} project — it's completely unofficial.": "لا يوجد أي اتصال بين هذا المشروع والمشروع الرسمي {0} — فهو غير رسمي تمامًا.", "This setting is disabled": "هذا الإعداد مُعطّل", "This wizard will help you configure and customize WingetUI!": "سيساعدك هذا المعالج في تكوين UniGetUI وتخصيصه!", "Toggle search filters pane": "تبديل لوحة مرشحات البحث", "Translators": "المترجمون", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "اكتب هنا اسم وعنوان URL للمصدر الذي تريد إضافته، مفصولًا بمسافة.", "Unable to find package": "<PERSON>ير قادر على وجود الحزمة", "Unable to load informarion": "<PERSON>ير قادر على تحميل المعلومات", "UniGetUI collects anonymous usage data in order to improve the user experience.": "يقوم UniGetUI بجمع بيانات الاستخدام مجهولة المصدر لتطوير تجربة المستخدم.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "يقوم UniGetUI بجمع بيانات استخدام مجهولة المصدر لغاية وحيدة وهي فهم وتطوير تجربة المستخدم.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "اكتشف UniGetUI اختصارًا جديدًا على سطح المكتب يمكن حذفه تلقائيًا.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "اكتشف UniGetUI اختصارات سطح المكتب التالية والتي يمكن إزالتها تلقائيًا في الترقيات المستقبلية", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "اكتشف UniGetUI {0} اختصارات سطح مكتب جديدة يمكن حذفها تلقائيًا.", "UniGetUI is being updated...": "جاري تحديث UniGetUI...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI ليس مرتبطًا بأي من مديري الحزم المتوافقين. UniGetUI هو مشروع مستقل.", "UniGetUI on the background and system tray": "UniGetUI في الخلفية وشريط المهام", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "يتطلب UniGetUI {0} للعمل، ولكن لم يتم العثور عليه على نظامك.", "UniGetUI startup page:": "صفحة بدء تشغيل UniGetUI:", "UniGetUI updater": "محدّث UniGetUI", "UniGetUI version {0} is being downloaded.": "جاري تنزيل إصدار UniGetUI {0}.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} جاهز للتثبيت.", "Uninstall": "إلغاء التثبيت", "Uninstall Scoop (and its packages)": "إلغاء تثبي<PERSON> (و حزمه)", "Uninstall and more": null, "Uninstall and remove data": "إلغاء التثبيت وإزالة البيانات", "Uninstall as administrator": "إلغاء التثبيت كمسؤول", "Uninstall canceled by the user!": "تم إلغاء الإزالة من قبل المستخدم!", "Uninstall failed": "فشل إلغاء التثبيت", "Uninstall options": null, "Uninstall package": "إلغاء تثبيت الحزمة", "Uninstall package, then reinstall it": "أزِل تثبيت الحزمة, ثم أعِد تثبيتها", "Uninstall package, then update it": "أزِل تثبيت الحزمة, ثم حدِثّها", "Uninstall previous versions when updated": null, "Uninstall selected packages": "إلغاء تثبيت الحزم المحددة", "Uninstall selection": null, "Uninstall succeeded": "تم إلغاء التثبيت بنجاح", "Uninstall the selected packages with administrator privileges": "إلغاء تثبيت الحزم المحددة بامتيازات المسؤول", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "الحزم غير القابلة للإلغاء التي تأتي من المصدر المذكور \"{0}\" ليست منشورة في أي مدير حزم، وبالتالي لا يتوفر أي معلومات عنها.", "Unknown": "غير معروف", "Unknown size": "حجم غير معروف", "Unset or unknown": "غير معروف أو لم يتم ضبطها", "Up to date": "محد<PERSON>ة", "Update": "تحديث", "Update WingetUI automatically": "تحديث UniGetUI تلقائياً", "Update all": "تحديث الكل", "Update and more": null, "Update as administrator": "تحديث كمسؤول", "Update check frequency, automatically install updates, etc.": "تردد فحص التحديثات، تثبيت التحديثات تلقائياً، إلخ.", "Update checking": null, "Update date": "تاريخ التحديث", "Update failed": "فشل التحديث", "Update found!": "تم العثور على تحديث!", "Update now": "تحديث الآن", "Update options": null, "Update package indexes on launch": "تحديث فهارس الحزمة عند البدأ", "Update packages automatically": "تحديث الحزم تلقائيًا", "Update selected packages": "تحديث الحزم المحددة", "Update selected packages with administrator privileges": "تحديث الحزم المحددة بامتيازات المسؤول", "Update selection": null, "Update succeeded": "تم التحديث بنجاح", "Update to version {0}": "التحديث إلى الإصدار {0}", "Update to {0} available": "التحديث إلى {0} متاح", "Update vcpkg's Git portfiles automatically (requires Git installed)": "تحديث ملفات منفذ Git الخاصة بـ vcpkg تلقائيًا (يتطلب تثبيت Git)", "Updates": "التحديثات", "Updates available!": "هنالك تحديثات متاحة!", "Updates for this package are ignored": "تجاهل التحديثات لهذه الحزمة", "Updates found!": "تم العثور على تحديثات!", "Updates preferences": "تحديث التفضيلات", "Updating WingetUI": "جارٍ تحديث UniGetUI", "Url": "الرابط", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "استخدم حزمة WinGet القديمة بدلاً من PowerShell CMDLets", "Use a custom icon and screenshot database URL": "استخدام رابط مخصص لقاعدة بيانات الأيقونات ولقطات الشاشة", "Use bundled WinGet instead of PowerShell CMDlets": "استخدم WinGet المضمّن بدلاً من PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "استخدم WinGet المجمع بدلاً من WinGet الخاص بالنظام", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "استخدم GSudo المثبت بدلاً من المُضمَّن (يتطلب إعادة تشغيل التطبيق)", "Use system Chocolatey": "استخدم نظام Chocolatey", "Use system Chocolatey (Needs a restart)": "استخدام Chocolatey الخاص بالنظام (يحتاج إعادة تشغيل)", "Use system Winget (Needs a restart)": "استخدام Winget الخاص بالنظام (يحتاج إلى إعادة تشغيل)", "Use system Winget (System language must be set to english)": "استخدم نظام Winget (يجب ضبط لغة النظام على اللغة الإنجليزية)", "Use the WinGet COM API to fetch packages": "استخدم WinGet COM API لجلب الحزم", "Use the WinGet PowerShell Module instead of the WinGet COM API": "استخدم وحدة WinGet PowerShell بدلاً من WinGet COM API", "Useful links": "روابط مفيدة", "User": "المستخدم", "User interface preferences": "تفضيلات واجهة المستخدم", "User | Local": "المستخدم | محلي", "Username": "اسم المستخدم", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "إن استخدام UniGetUI يعني قبول ترخيص GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "إن استخدام UniGetUI يعني قبول ترخيص MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "لم يتم العثور على جذر Vcpkg. يرجى تحديد متغير البيئة %VCPKG_ROOT% أو تحديده من إعدادات UniGetUI", "Vcpkg was not found on your system.": "لم يتم العثور على Vcpkg على نظامك.", "Verbose": "مطوّل", "Version": "الإصدار", "Version to install:": "الإصدار للتثبيت", "Version:": null, "View GitHub Profile": "عرض <PERSON><PERSON><PERSON> الشخصي ", "View WingetUI on GitHub": "استكشف UniGetUI على GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "استكشف مصدر UniGetUI.  يمكنك الإبلاغ عن الأخطاء أو اقتراح الميزات، أو حتى المساهمة مباشرة في المشروع.", "View mode:": "طريقة العرض:", "View on UniGetUI": "اعرض على UniGetUI", "View page on browser": "أعرض الصفحة في المتصفح", "View {0} logs": "إظهار {0} سجل", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "انتظار اتصال الجهاز بالانترنت قبل محاولة عمل المهام التي تتطلب اتصالاً بالشبكة.", "Waiting for other installations to finish...": "انتظار المثبتات الأخرى حتى تنتهي...", "Waiting for {0} to complete...": "انتظار لـ {0} للاكتمال...", "Warning": "تحذير", "Warning!": "تحذير!", "We are checking for updates.": "نحن نتحقق من وجود تحديثات.", "We could not load detailed information about this package, because it was not found in any of your package sources": "تعذر علينا تحميل معلومات مفصلة حول هذه الحزمة، لأنها لم يعثر عليها في أي من مصادر الحزم الخاصة بك.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "تعذر علينا تحميل معلومات مفصلة حول هذه الحزمة، لأنها لم تكن مثبتة من أي مدير حزم متاح.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "لم نتمكن من {action} {package}. الرجاء المحاولة مرة أخرى لاحقًا. انقر على \"{showDetails}\" للحصول على السجلات من مثبت البرامج.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "لم نتمكن من {action} {package}. الرجاء المحاولة مرة أخرى لاحقًا. انقر على \"{showDetails}\" للحصول على سجلات من برنامج إلغاء التثبيت.\n\n\n\n\n", "We couldn't find any package": "لم نتمكن من العثور على أي حزمة", "Welcome to WingetUI": "مرحبًا بك في UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "إذا اكتُشِفت اختصارات جديدة، أزلهم تلقائيا بدلا من عرض هذه النافذة", "Which backup do you want to open?": null, "Which package managers do you want to use?": "أي مدير حزم ترغب في استخدامه؟", "Which source do you want to add?": "أيُ مصدرٍ تريد إضافته؟", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "بينما يمكن استخدام Winget داخل UniGetUI، يمكن أيضًا استخدام UniGetUI مع مديري حزم آخرين، مما قد يكون مربكًا. في السابق، كان UniGetUI مصممًا للعمل فقط مع Winget، ولكن لم يعد هذا صحيحًا، ولذلك لم يعد اسم UniGetUI يعكس الهدف الذي يسعى إليه هذا المشروع مستقبلاً.", "WinGet could not be repaired": "لم يتم إصلاح WinGet", "WinGet malfunction detected": "تم اكتشاف خلل في برنامج WinGet", "WinGet was repaired successfully": "تم إصلاح WinGet بنجاح", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - كل شيء محدث", "WingetUI - {0} updates are available": "UniGetUI - {0} تحديث متاح", "WingetUI - {0} {1}": "UniGetUI - {1} {0}", "WingetUI Homepage": "الصفحة الرئيسية لـ UniGetUI", "WingetUI Homepage - Share this link!": "الصفحة الرئيسية لـ UniGetUI - شارك هذا الرابط!", "WingetUI License": "ترخيص UniGetUI", "WingetUI Log": "سجل UniGetUI", "WingetUI Repository": "مستودع UniGetUI", "WingetUI Settings": "إعدادات UniGetUI", "WingetUI Settings File": "ملف إعدادات UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "يستخدم UniGetUI المكتبات التالية. بدونها، لم يكن من الممكن إنشاء UniGetUI.", "WingetUI Version {0}": "إصدار UniGetUI  {0}", "WingetUI autostart behaviour, application launch settings": "سلوك البدء التلقائي لـ UniGetUI, إعدادات لغة البرنامج", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI يمكنه التحقق مما إذا كان لديك تحديثات متاحة لبرامجك، وتثبيتها تلقائيًا إذا كنت ترغب في ذلك.", "WingetUI display language:": "لغة عرض UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "تم تشغيل UniGetUI كمسؤول، وهو أمر غير مستحسن. عند تشغيل UniGetUI كمسؤول، فإن كل عملية يتم إطلاقها من UniGetUI سيكون لها امتيازات المسؤول. لا يزال بإمكانك استخدام البرنامج، لكننا نوصي بشدة بعدم تشغيل UniGetUI بامتيازات المسؤول.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "لقد تمت ترجمة UniGetUI إلى أكثر من 40 لغة بفضل المترجمين المتطوعين. شكرًا لكم 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "لم تتم ترجمة UniGetUI بواسطة آلة, هؤلاء المستخدمون هم المسؤولون عن الترجمات:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI هو تطبيق يجعل إدارة البرامج الخاصة بك أسهل، من خلال توفير واجهة رسومية شاملة لمديري حزم سطر الأوامر لديك.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "تم تغيير اسم UniGetUI للتأكيد على الفرق بين UniGetUI (الواجهة التي تستخدمها الآن) وWinGet (مدير الحزم الذي طورته Microsoft والذي لا تربطني به صلة قرابة)", "WingetUI is being updated. When finished, WingetUI will restart itself": "يتم تحديث WingetUI. عند الانتهاء ، سيعاد تشغيل WingetUI ", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI مجاني وسيظل مجانيًا إلى الأبد. لا إعلانات ولا بطاقات ائتمان ولا إصدار مميز. مجاني بنسبة 100% إلى الأبد.", "WingetUI log": "سجل UniGetUI", "WingetUI tray application preferences": "تفضيلات أيقونة المهام لتطبيق  UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "يستخدم UniGetUI المكتبات التالية. بدونها، لم يكن UniGetUI ممكنًا.", "WingetUI version {0} is being downloaded.": "جاري تنزيل إصدار {0} من UniGetUI.", "WingetUI will become {newname} soon!": "UniGetUI سيصبح {newname} قريبًا!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI لن يقوم بفحص التحديثات بشكل دوري. سيتم التحقق منها لاحقًا عند بدء التشغيل، ولكنك لن تتلقى تحذيرًا بشأنها.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "سيقوم UniGetUI بعرض مربع حوار UAC في كل مرة تتطلب فيها حزمة رفع الصلاحيات للتثبيت.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "سيتم تسمية WingetUI قريبًا باسم {newname}. ولن يمثل هذا أي تغيير في التطبيق. وسأستمر أنا (المطور) في تطوير هذا المشروع كما أفعل الآن، ولكن تحت اسم مختلف.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "لم يكن UniGetUI ليصبح ممكنناً بدون مساعدة أعزائنا المساهمين. فم بزيارة ملفهم الشخصي على GitHub, لم يكن UniGetUI ليصبح ممكنناً بدونهم! ", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "لم يكن من الممكن إنشاء UniGetUI بدون مساعدة المساهمين. شكرًا لكم جميعًا 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} جاهز للتثبيت.", "Write here the process names here, separated by commas (,)": null, "Yes": "نعم", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "لقد قمت حاليًا بتثبيت الإصدار {0}", "You have installed WingetUI Version {0}": "لقد قمت بتثبيت UniGetUI  الإصدار {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "قد تحتاج إلى تثبيت {pm} حتى تتمكن من استخدامه مع UniGetUI.", "You may restart your computer later if you wish": "يمكنك إعادة تشغيل جهاز الكمبيوتر الخاص بك لاحقًا إذا كنت ترغب في ذلك", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "ستتم مطالبتك مرة واحدة فقط ، وسيتم منح صلاحيات المسؤول للحزم التي تطلبها.", "You will be prompted only once, and every future installation will be elevated automatically.": "ستتم مطالبتك مرة واحدة فقط ، وستتم ترقية كل تثبيت مستقبلي تلقائيًا.", "You will likely need to interact with the installer.": "ستحتاج غالباً إلى التفاعل مع المثبت.", "[RAN AS ADMINISTRATOR]": "[RAN AS ADMINISTRATOR]", "buy me a coffee": "إشتري لي قهوة", "extracted": "تم استخراجه", "feature": "ميزة", "formerly WingetUI": "سابقًا WingetUI", "homepage": "الموقع الإلكتروني", "install": "تثبيت", "installation": "التثبيت", "installed": "تم التثبيت", "installing": "يتم التثبيت", "library": "مكتبة", "mandatory": null, "option": "خيار", "optional": null, "uninstall": "إلغاء التثبيت", "uninstallation": "إلغاء التثبيت", "uninstalled": "تم إلغاء تثبيت", "uninstalling": "يتم إلغاء تثبيت", "update(noun)": "تحديث", "update(verb)": "تحديث", "updated": "تم تحديث", "updating": "يتم تحديث", "version {0}": "الإصدار {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "إلغاء تثبيت {0}", "{0} aborted": "تم إيقاف {0}", "{0} can be updated": "يمكن تحديث {0}", "{0} can be updated to version {1}": "يمكن تحديث {0} إلى الإصدار {1}", "{0} days": "{0} أيام", "{0} desktop shortcuts created": "تم إنشاء {0} اختصارا سطح المكتب", "{0} failed": "فشل {0}", "{0} has been installed successfully.": "تم تثبيت {0} بنجاح.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "تم تثبيت {0} بنجاح. يوصى بإعادة تشغيل UniGetUI لإكمال التثبيت", "{0} has failed, that was a requirement for {1} to be run": "{0} قد فشلت، والتي كانت مطلوبة لـ {1} لكي تعمل", "{0} homepage": "{0} الصفحة الرئيسية", "{0} hours": "{0} ساعات", "{0} installation": "تثبيت {0}", "{0} installation options": "{0} خيارات التثبيت", "{0} installer is being downloaded": "مثبت {0} قيد التحميل", "{0} is being installed": "جاري تثبيت {0}", "{0} is being uninstalled": "جاري إلغاء تثبيت {0}", "{0} is being updated": "يتم تحديث {0}", "{0} is being updated to version {1}": "يتم تحديث {0} إلى الإصدار {1}", "{0} is disabled": "{0} <PERSON>ير مفعل", "{0} minutes": "{0} دقائق", "{0} months": "{0} <PERSON><PERSON>هر", "{0} packages are being updated": "يتم تحديث {0} حزمة", "{0} packages can be updated": "يمكن تحديث {0} حزم", "{0} packages found": "تم العثور على {0} حزم", "{0} packages were found": "تم العثور على {0} حزمة", "{0} packages were found, {1} of which match the specified filters.": "تم العثور على {0} حزمة، {1} منها تطابق الفلاتر المحددة.", "{0} selected": null, "{0} settings": "إعدادات {0}", "{0} status": "حالة {0}", "{0} succeeded": "نجح {0}", "{0} update": "تحديث {0}", "{0} updates are available": "التحديثات المتاحة: {0}", "{0} was {1} successfully!": " {1} {0} بنجاح!", "{0} weeks": "{0} شهور", "{0} years": "{0}سنوات", "{0} {1} failed": "فشل {1} {0}", "{package} Installation": "{package} التثبيت", "{package} Uninstall": "{package} إلغاء التثبيت", "{package} Update": "تحديث {package}", "{package} could not be installed": "لم يتم تثبيت {package}", "{package} could not be uninstalled": "لم يتم إلغاء تثبيت {package}", "{package} could not be updated": "لم يتمكن من تحديث {package}", "{package} installation failed": "فشل تثبيت {package}", "{package} installer could not be downloaded": "لم يتم تحميل مثبّت {package}", "{package} installer download": "حمّل مثبّت {package}", "{package} installer was downloaded successfully": "تم تحميل مثبّت {package} بنجاح", "{package} uninstall failed": "فشل إلغاء تثبيت {package}", "{package} update failed": "فشل تحديث {package}", "{package} update failed. Click here for more details.": "فشل تحديث {package}. انقر هنا لمزيد من التفاصيل.", "{package} was installed successfully": "تم تثبيت {package} بنجاح", "{package} was uninstalled successfully": "تم إلغاء تثبيت {package} بنجاح", "{package} was updated successfully": "تم تحديث {package} بنجاح", "{pcName} installed packages": "الحزم المثبتة بواسطة {pcName}", "{pm} could not be found": "لم يتم العثور على {pm}", "{pm} found: {state}": "تم إيجاد {pm}: {state}", "{pm} is disabled": "تم تعطيل {pm}", "{pm} is enabled and ready to go": "تم تمكين {pm} وهو جاهز للاستخدام", "{pm} package manager specific preferences": "تفضيلات مدير الحزم {pm} الخاصة", "{pm} preferences": "تفضيلات {pm}", "{pm} version:": "إصدار {pm}:", "{pm} was not found!": "لم يتم العثور على {pm}!"}