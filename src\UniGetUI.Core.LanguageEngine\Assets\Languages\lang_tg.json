{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": null, "(Number {0} in the queue)": null, "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "lasersPew, @znarfm", "0 packages found": "Walang nakitang package", "0 updates found": "Walang nakitang kailangang iupdate", "1 - Errors": null, "1 day": "isang araw", "1 hour": "isang oras", "1 month": "<PERSON>ang buwan", "1 package was found": "Isang package ang nakita", "1 update is available": null, "1 week": "isang linggo", "1 year": "isang taon", "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": null, "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": null, "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": null, "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": null, "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": null, "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": null, "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": null, "A restart is required": "Kailangan mong mag-restart", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": null, "About Qt6": "Tungkol sa Qt6", "About WingetUI": "Tungkol sa WingetUI", "About WingetUI version {0}": "Tungkol sa bersyon ng WingetUI na {0}", "About the dev": "Tungkol sa gumawa", "Accept": "Tanggapin", "Action when double-clicking packages, hide successful installations": "<PERSON><PERSON><PERSON> mangyayari kapag pinindot nang dalawang beses ang package, itinatago ang nakatapos nang pag-iinstall", "Add": "Magdag<PERSON><PERSON>", "Add a source to {0}": "Magdagdag ng source sa {0}", "Add a timestamp to the backup file names": "Idagdag ang oras sa backup ng mga pangalan ", "Add a timestamp to the backup files": "Idagdag ang oras sa mga backup", "Add packages or open an existing bundle": null, "Add packages or open an existing package bundle": null, "Add packages to bundle": null, "Add packages to start": null, "Add selection to bundle": null, "Add source": "Magdagdag ng source", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": "Idinadagdag na ang {source}", "Adding source {source} to {manager}": "Idinadagdag ang {source} sa {manager}", "Addition succeeded": null, "Administrator privileges": "Nakatataas na pribilehiyo", "Administrator privileges preferences": "Setting ukol sa paggamit ng nakatataas na pribilehiyo", "Administrator rights": "karapatan ng nakatataas", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "Lahat ng files", "All versions": "Lahat ng Bersyon", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Payagan na isagawa ang mga operasyon sa packages nang sabay-sabay.", "Allow parallel installs (NOT RECOMMENDED)": "Hayaang magdownload ng sabay-sabay (HINDI INIREREKOMENDA)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": null, "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": "Laging itaas sa {pm} ang iiinstall ", "Always run {pm} operations with administrator rights": null, "An error occurred": "Nagkaroon ng kamalian", "An error occurred when adding the source: ": "Nagkaroon ng pagkakamali sa pagdagdag ng source:", "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": "Nagkaroon ng kamalian sa paghahanap ng updates:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": null, "An error occurred:": "Nagkaroon ng kamalian: ", "An interal error occurred. Please view the log for further details.": null, "An unexpected error occurred:": "Nagkaroon ng hindi inaasahang pagkakamali:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": "Mayroong update na nahanap!", "Android Subsystem": null, "Another source": null, "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": null, "App Name": "Pangalan ng aplikasyon", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "Tema ng aplikasyon:", "Apply": null, "Architecture to install:": "Arkitektura para mainstall:", "Are these screenshots wron or blurry?": "Ang mga screenshot ba na ito ay mali o malabo?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "<PERSON><PERSON><PERSON> ka na?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": null, "Ask for administrator rights when required": "Magtanong ng nakatataas na karapatan kung nangangailangan", "Ask once or always for administrator rights, elevate installations by default": "Magtanong palagi o ng isang beses ukol sa nakatataas na karapatan, laging humingi ng permiso", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Magtanong lamang ng isang beses ukol sa nakatataas na pribilehiyo (HINDI INIREREKOMENDA)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": "Kailangan ng iyong atensyon", "Authenticate to the proxy with an user and a password": null, "Author": "Manggagawa", "Automatic desktop shortcut remover": null, "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": null, "Automatically save a list of your installed packages on your computer.": null, "Autostart WingetUI in the notifications area": "Laging buksan ang WingetUI sa lugar ng mga abiso", "Available Updates": null, "Available updates: {0}": "Mga maaaring i-update: {0}", "Available updates: {0}, not finished yet...": "<PERSON>ga maaaring i-update: {0}, hindi pa nakakatapos ang iba...", "Backing up packages to GitHub Gist...": null, "Backup": null, "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": null, "Backup location": null, "Become a contributor": null, "Become a translator": null, "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Ibang mga bagay na di dapat baguhin gaya ng beta features", "Both": null, "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Ng<PERSON>t ito ang maaari mo pang gawin upang matutunan ukol sa WingetUI", "By toggling a package manager off, you will no longer be able to see or update its packages.": null, "Cache administrator rights and elevate installers by default": "I-cache ang nakatataas na pribilehiyo at lagi itong gamitin pag i-install.", "Cache administrator rights, but elevate installers only when required": "I-cache ang nakatataas na pribilehiyo, ngunit gamitin ito kung kailan lang kailangan.", "Cache was reset successfully!": "Ang cache ay maayos na naibalik sa dati.", "Can't {0} {1}": "Hindi kayang i-{0} ang {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": null, "Change backup output directory": null, "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": null, "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": null, "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Hanapin ang mga bagong updates ng mga package kada panahon", "Check for updates": null, "Check for updates every:": "Maghanap ng mga bagong updates ng mga package tuwing:", "Check for updates periodically": "Paminsan-minsang maghanap ng mga update", "Check for updates regularly, and ask me what to do when updates are found.": "Palaging maghanap ng update at tanungin ako kung iiiinstall ba o hindi ito.", "Check for updates regularly, and automatically install available ones.": "Palaging maghanap ng updates at i-install ito nang kusa.", "Check out my {0} and my {1}!": "<PERSON><PERSON><PERSON> mo ang aking {0} at {1}", "Check out some WingetUI overviews": null, "Checking for other running instances...": "<PERSON><PERSON><PERSON> ang iba pang bukas na mga instances...", "Checking for updates...": "Maghanap ng mga updates...", "Checking found instace(s)...": "Tinitignan ang mga instances na nahanap", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "<PERSON><PERSON> ang pinili", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": null, "Clearing Scoop cache...": "Ni<PERSON>linis ang cache ng scoop", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": null, "Close": "Isara", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Ilagay ang WingetUI sa notifications", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": null, "Command-line to run:": null, "Compare query against": null, "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": null, "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": null, "Contribute to the icon and screenshot repository": "Mag-ambag sa repository ng mga icon at mga screenshot", "Contributors": "Mga nag-ambag", "Copy": null, "Copy to clipboard": "<PERSON><PERSON><PERSON><PERSON> patungo sa clipboard", "Could not add source": "Hindi maidagdag ang source", "Could not add source {source} to {manager}": "Hindi maidagdag ang {source} sa {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": null, "Could not load announcements - HTTP status code is $CODE": null, "Could not remove source": "Hindi maalis ang source", "Could not remove source {source} from {manager}": "Hindi maalis ang {source} mula sa {manager}", "Could not remove {source} from {manager}": "Hindi maalis ang {source} mula sa {manager}", "Create .ps1 script": null, "Credentials": null, "Current Version": "Kasalukuyang Bersyon", "Current status: Not logged in": null, "Current user": "Kasalukuyang User", "Custom arguments:": null, "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": null, "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": null, "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "PAUNAWA: <PERSON><PERSON><PERSON> RESPONSABLE PARA SA MGA PACKAGE NA NAINSTALL. PAKISIGURADO NA I-INSTALL LANG ANG MGA MAPAGKAKATIWALAANG APPS.", "Dark": "<PERSON><PERSON><PERSON>", "Decline": null, "Default": null, "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": null, "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": null, "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Mahirap mag-develop, at ang app na ito ay libre ngunit kung nagustuhan mo itong app, maaari mo akong <b>bilihan ng kape</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Direktang i-install kapag pinindot ng doble ang isang item sa \"{discoveryTab}\" tab imbes na ipakita ang impormasyon ukol sa package", "Disable new share API (port 7058)": null, "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": null, "Discover Packages": "Tumuklas ng mga packages", "Discover packages": null, "Distinguish between\nuppercase and lowercase": null, "Distinguish between uppercase and lowercase": null, "Do NOT check for updates": "Huwag ma<PERSON> ng updates", "Do an interactive install for the selected packages": "Mag interactive na install para sa package na ito", "Do an interactive uninstall for the selected packages": "Mag interactive na uninstall para sa package na ito", "Do an interactive update for the selected packages": "Mag interactive na update para sa package na ito", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Huwag kusang magdownload ng mga pagsasalin mula sa GitHub.", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": null, "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": null, "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": null, "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Na<PERSON><PERSON> mo ba na nakatulong sayo ang WingetUI? Gusto mo ba na suportahan ang gumawa nito? Kung oo, maaari kang {0}, nak<PERSON><PERSON>long ito ng marami!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": null, "Do you really want to uninstall {0} packages?": "<PERSON>to mo ba talaga na alisin ang {0} na packages", "Do you really want to uninstall {0}?": "<PERSON>to mo ba talaga na alisin ang {0}", "Do you want to restart your computer now?": "Gusto mo ba na i-restart ang computer mo ngayon?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "<PERSON>to mo ba na isalin ang WingetUI sa iyong wika? Tignan mo <a style=\"color:{0}\" href=\"{1}\"a>dito</a> kung paano ka makakatulong!", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": null, "Donate": null, "Done!": null, "Download failed": null, "Download installer": null, "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": null, "Download updated language files from GitHub automatically": null, "Downloading": null, "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": null, "Enable Scoop cleanup on launch": null, "Enable WingetUI notifications": null, "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": null, "Enable it to install packages from {pm}.": null, "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "<PERSON><PERSON><PERSON> ang {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": null, "Everything is up to date": null, "Exact match": null, "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": null, "Experimental settings and developer options": null, "Export": null, "Export log as a file": null, "Export packages": null, "Export selected packages to a file": null, "Export settings to a local file": null, "Export to a file": null, "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": null, "Filters": null, "Finish": null, "Follow system color scheme": null, "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": null, "Formerly known as WingetUI": null, "Found": null, "Found packages: ": null, "Found packages: {0}": null, "Found packages: {0}, not finished yet...": null, "General preferences": null, "GitHub profile": null, "Global": null, "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": null, "Great! You are on the latest version.": null, "Grid": null, "Help": null, "Help and documentation": null, "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": null, "Hide details": null, "Homepage": null, "Hooray! No updates were found.": null, "How should installations that require administrator privileges be treated?": null, "How to add packages to a bundle": null, "I understand": null, "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": null, "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": null, "Ignore special characters": null, "Ignore updates for the selected packages": null, "Ignore updates for this package": null, "Ignored updates": null, "Ignored version": null, "Import": null, "Import packages": null, "Import packages from a file": null, "Import settings from a local file": null, "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": null, "Install": null, "Install Scoop": null, "Install and more": null, "Install and update preferences": null, "Install as administrator": null, "Install available updates automatically": null, "Install location can't be changed for {0} packages": null, "Install location:": null, "Install options": null, "Install packages from a file": null, "Install prerelease versions of UniGetUI": null, "Install script": null, "Install selected packages": null, "Install selected packages with administrator privileges": null, "Install selection": null, "Install the latest prerelease version": null, "Install updates automatically": null, "Install {0}": null, "Installation canceled by the user!": null, "Installation failed": null, "Installation options": null, "Installation scope:": null, "Installation succeeded": null, "Installed Packages": null, "Installed Version": null, "Installed packages": null, "Installer SHA256": null, "Installer SHA512": null, "Installer Type": null, "Installer URL": null, "Installer not available": null, "Instance {0} responded, quitting...": null, "Instant search": null, "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": null, "Interactive operation": null, "Interactive uninstall": null, "Interactive update": null, "Internet connection settings": null, "Is this package missing the icon?": null, "Is your language missing or incomplete?": null, "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": null, "Language": null, "Language, theme and other miscellaneous preferences": null, "Last updated:": null, "Latest": null, "Latest Version": null, "Latest Version:": null, "Latest details...": null, "Launching subprocess...": null, "Leave empty for default": null, "License": null, "Licenses": null, "Light": null, "List": null, "Live command-line output": null, "Live output": null, "Loading UI components...": null, "Loading WingetUI...": null, "Loading packages": null, "Loading packages, please wait...": null, "Loading...": null, "Local": null, "Local PC": null, "Local backup advanced options": null, "Local machine": null, "Local package backup": null, "Locating {pm}...": null, "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": null, "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": null, "Machine | Global": null, "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": null, "Manage ignored packages": null, "Manage ignored updates": null, "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": null, "Manifest": null, "Manifests": null, "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": null, "Missing dependency": null, "More": null, "More details": null, "More details about the shared data and how it will be processed": null, "More info": null, "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": null, "New": null, "New Version": null, "New bundle": null, "New version": null, "Nice! Backups will be uploaded to a private gist on your account": null, "No": null, "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": null, "No packages found matching the input criteria": null, "No packages have been added yet": null, "No packages selected": null, "No packages were found": null, "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": null, "No sources found": "Walang nakitang source", "No sources were found": "Walang source na nahanap", "No updates are available": null, "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": null, "Not available": null, "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": null, "Not right now": null, "Notes:": null, "Notification preferences": null, "Notification tray options": null, "Notification types": null, "NuPkg (zipped manifest)": null, "OK": null, "Ok": null, "Open": null, "Open GitHub": null, "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": null, "Open backup location": null, "Open existing bundle": null, "Open install location": null, "Open the welcome wizard": null, "Operation canceled by user": null, "Operation cancelled": null, "Operation history": null, "Operation in progress": null, "Operation on queue (position {0})...": null, "Operation profile:": null, "Options saved": null, "Order by:": null, "Other": null, "Other settings": null, "Package": null, "Package Bundles": null, "Package ID": null, "Package Manager": null, "Package Manager logs": null, "Package Managers": null, "Package Name": null, "Package backup": null, "Package backup settings": null, "Package bundle": null, "Package details": null, "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": null, "Package managers": null, "Package not found": null, "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": null, "Packages found: {0}": null, "Partially": null, "Password": null, "Paste a valid URL to the database": null, "Pause updates for": null, "Perform a backup now": null, "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": null, "Periodically perform a backup of the installed packages": null, "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": null, "Please enter at least 3 characters": null, "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": null, "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Pakitandaan na ang mga packages mula sa iilang sources ay maaaring hindi ma-e-export. Ang mga ito ay naka-grey out at hindi ma-e-export.", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": null, "Please select how you want to configure WingetUI": null, "Please try again later": null, "Please type at least two characters": null, "Please wait": null, "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": null, "Portable": null, "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": null, "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": null, "Preparing packages, please wait...": null, "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": null, "Publisher": null, "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": null, "Quit": null, "Quit WingetUI": null, "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": null, "Related settings": null, "Release notes": null, "Release notes URL": null, "Release notes URL:": null, "Release notes:": null, "Reload": null, "Reload log": null, "Removal failed": null, "Removal succeeded": null, "Remove from list": null, "Remove permanent data": null, "Remove selection from bundle": null, "Remove successful installs/uninstalls/updates from the installation list": null, "Removing source {source}": "<PERSON><PERSON>is na ang {source}", "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON> ang {source} mula sa {manager}", "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": null, "Repository": null, "Reset": null, "Reset Scoop's global app cache": null, "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": null, "Reset WingetUI": null, "Reset WingetUI and its preferences": null, "Reset WingetUI icon and screenshot cache": null, "Reset list": null, "Resetting Winget sources - WingetUI": null, "Restart": null, "Restart UniGetUI": null, "Restart WingetUI": null, "Restart WingetUI to fully apply changes": null, "Restart later": null, "Restart now": null, "Restart required": null, "Restart your PC to finish installation": null, "Restart your computer to finish the installation": null, "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": null, "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": null, "Return to top": null, "Run": null, "Run as admin": null, "Run cleanup and clear cache": null, "Run last": null, "Run next": null, "Run now": null, "Running the installer...": null, "Running the uninstaller...": null, "Running the updater...": null, "Save": null, "Save File": null, "Save and close": null, "Save as": null, "Save bundle as": null, "Save now": null, "Saving packages, please wait...": null, "Scoop Installer - WingetUI": null, "Scoop Uninstaller - WingetUI": null, "Scoop package": null, "Search": null, "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": null, "Search for packages": null, "Search for packages to start": null, "Search mode": null, "Search on available updates": null, "Search on your software": null, "Searching for installed packages...": null, "Searching for packages...": null, "Searching for updates...": null, "Select": null, "Select \"{item}\" to add your custom bucket": null, "Select a folder": null, "Select all": null, "Select all packages": null, "Select backup": null, "Select only <b>if you know what you are doing</b>.": null, "Select package file": null, "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": null, "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": null, "Sent handshake. Waiting for instance listener's answer... ({0}%)": null, "Set a custom backup file name": null, "Set custom backup file name": null, "Settings": null, "Share": null, "Share WingetUI": null, "Share anonymous usage data": null, "Share this package": null, "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": null, "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": null, "Show a notification when an installation fails": null, "Show a notification when an installation finishes successfully": null, "Show a notification when an operation fails": null, "Show a notification when an operation finishes successfully": null, "Show a notification when there are available updates": null, "Show a silent notification when an operation is running": null, "Show details": null, "Show in explorer": null, "Show info about the package on the Updates tab": null, "Show missing translation strings": null, "Show notifications on different events": null, "Show package details": null, "Show package icons on package lists": null, "Show similar packages": null, "Show the live output": null, "Size": null, "Skip": null, "Skip hash check": null, "Skip hash checks": null, "Skip integrity checks": null, "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": null, "Skip the hash check when updating the selected packages": null, "Skip this version": null, "Software Updates": null, "Something went wrong": null, "Something went wrong while launching the updater.": null, "Source": null, "Source URL:": null, "Source added successfully": null, "Source addition failed": null, "Source name:": null, "Source removal failed": null, "Source removed successfully": null, "Source:": null, "Sources": null, "Start": null, "Starting daemons...": null, "Starting operation...": null, "Startup options": null, "Status": null, "Stuck here? Skip initialization": null, "Success!": null, "Suport the developer": null, "Support me": null, "Support the developer": null, "Systems are now ready to go!": null, "Telemetry": null, "Text": null, "Text file": null, "Thank you ❤": null, "Thank you 😉": null, "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": null, "The backup will be performed after login.": null, "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": null, "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": null, "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": null, "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": null, "The following packages are going to be installed on your system.": null, "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": null, "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": null, "The icons and screenshots are maintained by users like you!": null, "The installation script saved to {0}": null, "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": null, "The installer hash does not match the expected value.": null, "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": null, "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": null, "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": null, "The selected packages have been blacklisted": null, "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": null, "The source {source} was added to {manager} successfully": null, "The source {source} was removed from {manager} successfully": null, "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": null, "The update will not continue.": null, "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": null, "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": null, "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": null, "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": null, "They are the programs in charge of installing, updating and removing packages.": null, "Third-party licenses": null, "This could represent a <b>security risk</b>.": null, "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": null, "This is the <b>default choice</b>.": null, "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": null, "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": null, "This package can be updated to version {0}": null, "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": null, "This package is already installed": null, "This package is being processed": null, "This package is not available": null, "This package is on the queue": null, "This process is running with administrator privileges": null, "This project has no connection with the official {0} project — it's completely unofficial.": null, "This setting is disabled": null, "This wizard will help you configure and customize WingetUI!": null, "Toggle search filters pane": null, "Translators": null, "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": null, "Unable to find package": null, "Unable to load informarion": null, "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": null, "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": null, "Uninstall Scoop (and its packages)": null, "Uninstall and more": null, "Uninstall and remove data": null, "Uninstall as administrator": null, "Uninstall canceled by the user!": null, "Uninstall failed": null, "Uninstall options": null, "Uninstall package": null, "Uninstall package, then reinstall it": null, "Uninstall package, then update it": null, "Uninstall previous versions when updated": null, "Uninstall selected packages": null, "Uninstall selection": null, "Uninstall succeeded": null, "Uninstall the selected packages with administrator privileges": null, "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": null, "Unknown": null, "Unknown size": null, "Unset or unknown": null, "Up to date": null, "Update": null, "Update WingetUI automatically": null, "Update all": null, "Update and more": null, "Update as administrator": null, "Update check frequency, automatically install updates, etc.": null, "Update checking": null, "Update date": null, "Update failed": null, "Update found!": null, "Update now": null, "Update options": null, "Update package indexes on launch": null, "Update packages automatically": null, "Update selected packages": null, "Update selected packages with administrator privileges": null, "Update selection": null, "Update succeeded": null, "Update to version {0}": null, "Update to {0} available": null, "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": null, "Updates available!": null, "Updates for this package are ignored": null, "Updates found!": null, "Updates preferences": null, "Updating WingetUI": null, "Url": null, "Use Legacy bundled WinGet instead of PowerShell CMDLets": null, "Use a custom icon and screenshot database URL": null, "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": null, "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": null, "Use system Winget (Needs a restart)": null, "Use system Winget (System language must be set to english)": null, "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": null, "User": null, "User interface preferences": null, "User | Local": null, "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": null, "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": null, "Version to install:": null, "Version:": null, "View GitHub Profile": null, "View WingetUI on GitHub": null, "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Tignan ang source code ng UniGetUI. <PERSON><PERSON> roon, maaari kang mag-ulat ng mga bug o magmungkahi ng mga features, o kaya naman ay direktang mag-ambag sa proyekto ng UniGetUI. ", "View mode:": null, "View on UniGetUI": null, "View page on browser": null, "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": null, "Waiting for {0} to complete...": null, "Warning": null, "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": "Hindi namin ma-load ang detalyadong impormasyon tungkol sa package na ito, dahil hindi ito natagpuan sa alinman sa iyong mga sources.", "We could not load detailed information about this package, because it was not installed from an available package manager.": null, "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": null, "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": null, "We couldn't find any package": null, "Welcome to WingetUI": null, "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": null, "Which source do you want to add?": "Anong source ang gusto mong idagdag?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": null, "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": null, "WingetUI - Everything is up to date": null, "WingetUI - {0} updates are available": null, "WingetUI - {0} {1}": null, "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": null, "WingetUI Log": null, "WingetUI Repository": null, "WingetUI Settings": null, "WingetUI Settings File": null, "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": null, "WingetUI autostart behaviour, application launch settings": null, "WingetUI can check if your software has available updates, and install them automatically if you want to": null, "WingetUI display language:": null, "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": null, "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": null, "WingetUI has not been machine translated. The following users have been in charge of the translations:": null, "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": null, "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": null, "WingetUI log": null, "WingetUI tray application preferences": null, "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": null, "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": null, "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": null, "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": null, "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": null, "WingetUI {0} is ready to be installed.": null, "Write here the process names here, separated by commas (,)": null, "Yes": null, "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": null, "You have installed WingetUI Version {0}": null, "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": null, "You will be prompted only once, and administrator rights will be granted to packages that request them.": null, "You will be prompted only once, and every future installation will be elevated automatically.": null, "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": "Ilibre mo ako ng kape", "extracted": null, "feature": null, "formerly WingetUI": null, "homepage": null, "install": null, "installation": null, "installed": null, "installing": null, "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": null, "uninstallation": null, "uninstalled": null, "uninstalling": null, "update(noun)": null, "update(verb)": null, "updated": null, "updating": null, "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} ang a<PERSON>in", "{0} aborted": "{0} ay itinigil", "{0} can be updated": "{0} pa ang maaaring i-update", "{0} can be updated to version {1}": null, "{0} days": "{0} araw", "{0} desktop shortcuts created": null, "{0} failed": "{0} nabigo", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} oras", "{0} installation": "{0} pagkakabit", "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} ngayon ay inuupdate", "{0} is being updated to version {1}": null, "{0} is disabled": "{0} ay nakasara", "{0} minutes": "{0} minuto", "{0} months": null, "{0} packages are being updated": "{0}ng mga package ang inuupdate", "{0} packages can be updated": "{0} pa na mga package ang maaaring i-update", "{0} packages found": "{0} ang mga nakitang package", "{0} packages were found": "{0} ang mga nakitang package", "{0} packages were found, {1} of which match the specified filters.": null, "{0} selected": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} ang nagt<PERSON><PERSON>ay", "{0} update": "{0} ang iuupdate", "{0} updates are available": null, "{0} was {1} successfully!": "{0} ay {1} nang tuluyan", "{0} weeks": null, "{0} years": null, "{0} {1} failed": "Ang package na {0} ay nabigong i-{1}", "{package} Installation": null, "{package} Uninstall": null, "{package} Update": null, "{package} could not be installed": null, "{package} could not be uninstalled": null, "{package} could not be updated": null, "{package} installation failed": null, "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": null, "{package} update failed": null, "{package} update failed. Click here for more details.": null, "{package} was installed successfully": null, "{package} was uninstalled successfully": null, "{package} was updated successfully": null, "{pcName} installed packages": null, "{pm} could not be found": null, "{pm} found: {state}": null, "{pm} is disabled": null, "{pm} is enabled and ready to go": null, "{pm} package manager specific preferences": null, "{pm} preferences": null, "{pm} version:": null, "{pm} was not found!": null}