{"\"{0}\" is a local package and can't be shared": "\"{0}\" yerel bir pakettir ve paylaşılamaz", "\"{0}\" is a local package and does not have available details": "\"{0}\" yerel bir pakettir ve kullanılabilir ayrıntılara sahip değildir", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" yerel bir pakettir ve bu özellik ile uyumlu değildir", "(Last checked: {0})": "(Son kontrol: {0})", "(Number {0} in the queue)": "({0} <PERSON><PERSON><PERSON>)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@ahmetozmtn, @gokberkgs, @dogancanyr @anzeralp", "0 packages found": "<PERSON><PERSON> bulu<PERSON>adı", "0 updates found": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulu<PERSON>", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 gün", "1 hour": "1 saat", "1 month": "1 ay", "1 package was found": "1 paket bulundu", "1 update is available": "1 güncelleme mevcut", "1 week": "1 hafta", "1 year": "1 yıl", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. \"{0}\" veya \"{1}\" say<PERSON><PERSON><PERSON>na gidin.", "2 - Warnings": "2 - <PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON> grubuna eklemek istediğiniz paketi/paketleri bulun ve en soldaki onay kutusunu seçin.", "3 - Information (less)": "3 - Bilgilendirme (az)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON>et grubuna eklemek istediğiniz paketler seçiliyken araç çubuğunda \"{0}\" seçeneğini bulup tıklayın.", "4 - Information (more)": "4 - Bilgilendirme (orta)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Paketleriniz paket grubuna eklenmiş olacak. Paket eklemeye devam edebilir veya paket grubunu dışa aktarabilirsiniz.", "5 - information (debug)": "5 - Bilgilendirme (hata ayıklama)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Popüler bir C/C++ k<PERSON>t<PERSON><PERSON>ne yöneticisi. C/C++ kitaplıkları ve diğer C/C++ ile ilgili yardımcı programlarla dolu<br>İçerikler: <b>C/C++ kitaplıkları ve ilgili yardımcı programlar</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Microsoft ile tasarlanmış araçlar ve yürütülebilir dosyalarla dolu bir depo.NET ekosistemini göz önünde bulundurun.<br>İçerikleri: <b>.NET ile ilgili araçlar ve komut dosyaları</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Microsoft ile tasarlanmış araçlar ve yürütülebilir dosyalarla dolu bir depo.NET ekosistemini göz önünde bulundurun.<br>İçerik: <b>.NET ile ilgili Araçlar</b>", "A restart is required": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON> g<PERSON>", "Abort install if pre-install command fails": "Ön kurulum komutu başarısız olursa kurulumu iptal et", "Abort uninstall if pre-uninstall command fails": "Ön kaldırma komutu başarısız olursa kaldırmayı iptal et", "Abort update if pre-update command fails": "Ön güncelleme komutu başarısız olursa güncellemeyi iptal et", "About": "Hakkında", "About Qt6": "Qt6 Hakkında", "About WingetUI": "UniGetUI Hakkında", "About WingetUI version {0}": "UniGetUI {0} sürümü hakkında", "About the dev": "Geliştirici ha<PERSON>ı<PERSON>", "Accept": "Kabul", "Action when double-clicking packages, hide successful installations": "Paketlere çift tıklandığında yapılacak eylem, başarılı kurulumları gizleyin", "Add": "<PERSON><PERSON>", "Add a source to {0}": "{0} öğ<PERSON>ne bir kaynak ekle", "Add a timestamp to the backup file names": "Yedekleme dosyası adlarına bir zaman damgası ekleyin", "Add a timestamp to the backup files": "Yedekleme dosyalarına bir zaman damgası ekleyin", "Add packages or open an existing bundle": "Paketler ekleyin veya mevcut bir paket grubunu açın", "Add packages or open an existing package bundle": "Paketler ekleyin veya mevcut bir paket grubunu açın", "Add packages to bundle": "Paketleri paket grubuna ekle", "Add packages to start": "Başlamak için pake<PERSON>i ekleyin", "Add selection to bundle": "<PERSON>ç<PERSON>i paket grubuna ekle", "Add source": "<PERSON><PERSON><PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "'Uygulanabilir güncelleme bulunamadı' uyarısıyla başarısız olan güncellemeleri yoksayılan güncellemeler listesine e<PERSON>in", "Adding source {source}": "Kaynak ekleniyor {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON> {source} {manager}' a ekleniyor", "Addition succeeded": "Ekleme başarılı", "Administrator privileges": "Yönetici ayrıcalıkları", "Administrator privileges preferences": "Yönetici ayrıcalıkları tercihleri", "Administrator rights": "Yönetici <PERSON>", "Administrator rights and other dangerous settings": "Yönetici ayrıcalıkları ve diğer tehlikeli ayarlar", "Advanced options": "Gelişmiş seçenekler", "All files": "<PERSON><PERSON><PERSON>", "All versions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Paket işlemlerinin paralel olarak gerçekleştirilmesine izin ver", "Allow parallel installs (NOT RECOMMENDED)": "Paralel kurulumlara izin ver (ÖNERİLMEZ)", "Allow pre-release versions": "<PERSON><PERSON> sürümlere izin ver", "Allow {pm} operations to be performed in parallel": "{pm} işlemlerinin paralel olarak gerçekleştirilmesine izin ver", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "<PERSON><PERSON><PERSON><PERSON>, Windows PowerShell isteminde aşağıdaki komutu çalıştırarak da {0}'ı yükleyebilirsiniz:", "Always elevate {pm} installations by default": "{pm} k<PERSON><PERSON>ların<PERSON> her zaman varsayılan olarak yükselt.", "Always run {pm} operations with administrator rights": "Her zaman {pm} işlemlerini yönetici haklarıyla çalıştırın", "An error occurred": "<PERSON>ir hata o<PERSON>", "An error occurred when adding the source: ": "Kaynak eklenirken bir hata o<PERSON>ş<PERSON>:", "An error occurred when attempting to show the package with Id {0}": "{0} kimlikli paket gösterilmeye çalışılırken bir hata oluştu", "An error occurred when checking for updates: ": "Güncellemeler kontrol edilirken bir hata oluştu:", "An error occurred while loading a backup: ": "Yedekleme yüklenirken bir hata oluş<PERSON>:", "An error occurred while logging in: ": "<PERSON><PERSON><PERSON> ya<PERSON>en bir hata o<PERSON>:", "An error occurred while processing this package": "Bu paket işlenirken bir hata oluş<PERSON>", "An error occurred:": "<PERSON>ir hata o<PERSON>:", "An interal error occurred. Please view the log for further details.": "Bir hata oluştu. Daha fazla bilgi için lütfen günlüğe bakın.", "An unexpected error occurred:": "Beklenmedik bir hata <PERSON>", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "WinGet onarılmaya çalışılırken beklenmeyen bir sorun oluştu. Lütfen daha sonra tekrar deneyin", "An update was found!": "Bir güncelleme bulundu!", "Android Subsystem": "Android Alt Sistemi", "Another source": "Başka Bir Kaynak Ekle", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Bir yükleme veya güncelleme işlemi sırasında oluşturulan yeni kısayollar, ilk algılandıklarında bir onay istemi göstermek yerine otomatik olarak silinecektir.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "UniGetUI dışında oluşturulan veya değiştirilen herhangi bir kısayol göz ardı edilecektir. Bunları {0} düğme aracılığıyla ekleyebileceksiniz.", "Any unsaved changes will be lost": "Kaydedilmemiş değişiklikler kaybolacak", "App Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> ismi", "Appearance": "Dış görünüş", "Application theme, startup page, package icons, clear successful installs automatically": "Uygulama teması, b<PERSON><PERSON><PERSON><PERSON><PERSON>, paket sim<PERSON>, başarılı yüklemeleri otomatik olarak temizle", "Application theme:": "Uygulama teması:", "Apply": "<PERSON><PERSON><PERSON><PERSON>", "Architecture to install:": "Kurulacak mimari:", "Are these screenshots wron or blurry?": "Bu ekran görüntüleri yanlış veya bulanık mı?", "Are you really sure you want to enable this feature?": "Bu özelliği gerçekten etkinleştirmek istiyor musunuz?", "Are you sure you want to create a new package bundle? ": "Yeni bir paket grubu oluşturmak istediğinizden emin misiniz?", "Are you sure you want to delete all shortcuts?": "Tüm kısayolları silmek istediğinizden emin misiniz?", "Are you sure?": "Emin misiniz?", "Ascendant": "<PERSON><PERSON>", "Ask for administrator privileges once for each batch of operations": "Her bir işlem grubu için bir kez yönetici ayrıcalıkları isteyin", "Ask for administrator rights when required": "Yönetici yetkileri gerektiğinde sor", "Ask once or always for administrator rights, elevate installations by default": "Yönetici izni için bir kez veya her zaman sor, kuru<PERSON><PERSON><PERSON> varsayılan olarak yükselt.", "Ask only once for administrator privileges": "Yönetici ayrıcalıkları için yalnızca bir kez sor", "Ask only once for administrator privileges (not recommended)": "Yönetici ayrıcalıkları için yalnızca bir kez sor (önerilmez)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Yükleme veya yükseltme sırasında oluşturulan masaüstü kısayollarının silinmesini sor.", "Attention required": "<PERSON>kkat edin", "Authenticate to the proxy with an user and a password": "Proxy'ye bir kullanıcı ve parola ile kimlik doğrulama", "Author": "<PERSON><PERSON>", "Automatic desktop shortcut remover": "Otomatik masaüstü kısayolu kaldırıcı", "Automatically save a list of all your installed packages to easily restore them.": "Kolayca geri yüklemek için yüklü tüm paketlerinizin bir listesini otomatik olarak kaydedin.", "Automatically save a list of your installed packages on your computer.": "Yüklü paketlerinizin bir listesini otomatik olarak bilgisayarınıza kaydedin.", "Autostart WingetUI in the notifications area": "WingetUI'yi bildirim alanında otomatik başlat", "Available Updates": "<PERSON><PERSON><PERSON>", "Available updates: {0}": "<PERSON><PERSON><PERSON> gü<PERSON> : {0}", "Available updates: {0}, not finished yet...": "Mev<PERSON> g<PERSON> : {0}, hen<PERSON><PERSON> bitmedi...", "Backing up packages to GitHub Gist...": "Paketler GitHub Gist'e yedekleniyor...", "Backup": "<PERSON><PERSON><PERSON>", "Backup Failed": "Yedekleme Başarısız Oldu", "Backup Successful": "Yedekleme başarılı", "Backup and Restore": "Yedekleme ve <PERSON>", "Backup installed packages": "Yüklü paketleri yedekle", "Backup location": "<PERSON><PERSON><PERSON><PERSON> kon<PERSON>u", "Become a contributor": "Katkıda bulunan olun", "Become a translator": "<PERSON><PERSON><PERSON><PERSON>", "Begin the process to select a cloud backup and review which packages to restore": "Bir bulut yedeklem<PERSON> seçme sü<PERSON>cini başlatın ve hangi paketlerin geri yükleneceğini inceleyin", "Beta features and other options that shouldn't be touched": "Beta özellikleri ve diğer dokunulmaması gereken seçenekler", "Both": "Her ikisi", "Bundle security report": "<PERSON><PERSON> güvenlik raporu", "But here are other things you can do to learn about WingetUI even more:": "Ancak WingetUI hakkında daha fazla bilgi edinmek için yapabileceğiniz başka şeyler de var:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Paket yöneticisini kapattığınızda artık paketlerini göremez veya güncelleyemezsiniz.", "Cache administrator rights and elevate installers by default": "Varsayılan olarak yönetici yetkilerini önbelleğe al ve yükleyicileri yükselt", "Cache administrator rights, but elevate installers only when required": "Yönetici yetkilerini önbelleğe al, ancak yükleyicileri yalnızca gerektiğinde yükselt", "Cache was reset successfully!": "Önbellek başarıyla sıfırlandı!", "Can't {0} {1}": "{0} {1} yapılamıyor", "Cancel": "İptal", "Cancel all operations": "Tüm işlemleri iptal et", "Change backup output directory": "Yedekleme çıktı dizinini değiştir", "Change default options": "Varsayılan seçenekleri <PERSON>r", "Change how UniGetUI checks and installs available updates for your packages": "UniGetUI'nin paketleriniz için mevcut güncellemeleri kontrol etme ve yükleme şeklini değiştirin", "Change how UniGetUI handles install, update and uninstall operations.": "UniGetUI'nin <PERSON>, güncelleme ve kaldırma işlemlerini nasıl işlediğini değiştirin.", "Change how UniGetUI installs packages, and checks and installs available updates": "UniGetUI'nin paketleri nasıl yükleyeceğini ve mevcut güncellemeleri nasıl kontrol edip yükleyeceğini değiştirin", "Change how operations request administrator rights": "İşlemlerin yönetici haklarını nasıl talep edeceğini değiştirin", "Change install location": "<PERSON><PERSON><PERSON> k<PERSON>", "Change this": "<PERSON><PERSON><PERSON>", "Change this and unlock": "<PERSON><PERSON><PERSON><PERSON>r ve kilidini aç", "Check for package updates periodically": "<PERSON>et güncellemelerin düzenli olarak kontrol et", "Check for updates": "Güncellemeleri kontrol et", "Check for updates every:": "Güncellemeleri şu aralıklarla kontrol et:", "Check for updates periodically": "Düzenli olarak güncellemeleri kontrol et", "Check for updates regularly, and ask me what to do when updates are found.": "Güncellemeleri düzenli olarak kontrol et ve güncellemeler bulunduğunda ne yapacağımı sor.", "Check for updates regularly, and automatically install available ones.": "Güncellemeleri düzenli olarak kontrol edin ve mevcut olanları otomatik olarak yükleyin.", "Check out my {0} and my {1}!": "{0} ve {1}'e göz at!", "Check out some WingetUI overviews": "Bazı WingetUI incelemelerine göz at", "Checking for other running instances...": "Çalışan diğer örnekler kontrol ediliyor...\n", "Checking for updates...": "Güncellemeler kontrol ediliyor...", "Checking found instace(s)...": "Bulunan ö<PERSON>k(ler) kontrol ediliyor...", "Choose how many operations shouls be performed in parallel": "Paralel olarak kaç işlem yapılması gerektiğini seç", "Clear cache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "Clear finished operations": "Tamamlanmış işlemleri temizle", "Clear selection": "Seçimleri iptal et", "Clear successful operations": "Başarılı Operasyonları Temizle", "Clear successful operations from the operation list after a 5 second delay": "5 saniyelik bir gecikmenin ardından başarılı işlemleri işlem listesinden silin", "Clear the local icon cache": "<PERSON><PERSON> si<PERSON> temizleyin", "Clearing Scoop cache - WingetUI": "Kepçe önbelleğini temizleme - WingetUI", "Clearing Scoop cache...": "<PERSON><PERSON>bell<PERSON>ği temizleniyor...", "Click here for more details": "Daha fazla bilgi için buraya tıklayın", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Kurulum işlemine başlamak için Install'a tıklayın. Kurulumu atlarsanız UniGetUI beklendiği gibi çalışmayabilir.", "Close": "Ka<PERSON><PERSON>", "Close UniGetUI to the system tray": "UniGetUI'yi sistem tepsisine kapatın", "Close WingetUI to the notification area": "WingetUI kapandığında görev çubuğunda çalışsın", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> paketlerin listesini depolamak için özel bir GitHub Gist kullanır", "Cloud package backup": "<PERSON><PERSON><PERSON> paketi <PERSON>", "Command-line Output": "Komut satırı Çıkışı", "Command-line to run:": "Çalıştırılacak komut satırı:", "Compare query against": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "Compatible with authentication": "Kimlik doğrulama ile uyumlu", "Compatible with proxy": "Proxy ile u<PERSON>", "Component Information": "Bileşen Bilgileri\n", "Concurrency and execution": "Eşzamanlılık ve yürütme", "Connect the internet using a custom proxy": "Özel bir proxy kullanarak internete bağlanın", "Continue": "<PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Simge ve ekran görüntüsü deposuna katkıda bulunun", "Contributors": "Katkıda Bulunanlar", "Copy": "Kopyala", "Copy to clipboard": "<PERSON><PERSON> k<PERSON>", "Could not add source": "Kaynak <PERSON>", "Could not add source {source} to {manager}": "{source} kaynağı {manager} yöneticisine eklenemedi", "Could not back up packages to GitHub Gist: ": "Paketler GitHub Gist'e <PERSON>:", "Could not create bundle": "Paket grubu oluşturulamadı", "Could not load announcements - ": "<PERSON><PERSON><PERSON><PERSON> -", "Could not load announcements - HTTP status code is $CODE": "Duyurular yüklenemedi - HTTP hata kodu: $CODE", "Could not remove source": "Kaynak kaldırılamadı", "Could not remove source {source} from {manager}": "{source} ka<PERSON>ğı {manager} ka<PERSON><PERSON><PERSON><PERSON><PERSON> kaldırılamadı", "Could not remove {source} from {manager}": "{source}, {manager}'dan ka<PERSON>", "Credentials": "<PERSON><PERSON>", "Current Version": "<PERSON><PERSON><PERSON> s<PERSON>", "Current status: Not logged in": "Mevcut durum: <PERSON><PERSON><PERSON>", "Current user": "Mevcut kullanıcı", "Custom arguments:": "<PERSON><PERSON>:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Özel komut satırı değişkenleri:", "Custom install arguments:": "Özel kurulum argümanları:", "Custom uninstall arguments:": "Özel kaldırma argümanları:", "Custom update arguments:": "<PERSON><PERSON> güncelleme argümanları:", "Customize WingetUI - for hackers and advanced users only": "WingetUI'yi ö<PERSON>tir - yalnızca hackerlar ve ileri düzey kullanıcılar içindir", "DEBUG BUILD": "HATA AYIKLAMA YAPISI", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "SORUMLULUK REDDİ: İNDİRİLEN PAKETLERDEN BİZ SORUMLU DEĞİLİZ. LÜTFEN YALNIZCA GÜVENİLİR YAZILIMLARI YÜKLEDİĞİNİZDEN EMİN OLUN.", "Dark": "Karanlık", "Decline": "<PERSON><PERSON>", "Default": "Varsayılan", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Varsayılan tercihler - normal kullanıcılar için uygundur", "Default vcpkg triplet": "Varsayılan vcpkg üçlüsü", "Delete?": "Silinsin mi?", "Dependencies:": "Bağımlılıklar:", "Descendant": "<PERSON><PERSON><PERSON>", "Description:": "Açıklama:", "Desktop shortcut created": "Masaüstü kısayolu oluşturuldu", "Details of the report:": "<PERSON><PERSON><PERSON> de<PERSON>rı:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Geliştirmek zordur ve bu uygulama ücretsizdir. <PERSON><PERSON>i<PERSON>, her zaman <b>bana bir kahve ısmarlayabilirsiniz</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "\"{discoveryTab}\" sekmesindeki bir öğeye çift tıkladığınızda doğrudan yükleyin (paket bilgilerini göstermek yerine)", "Disable new share API (port 7058)": "Yeni paylaşım API'sini devre dışı bırak (port 7058)", "Disable the 1-minute timeout for package-related operations": "Paketle ilgili işlemler için 1 dakikalık zaman aşımını devre dışı bırakın", "Disclaimer": "Feragatname", "Discover Packages": "Paketleri Keşfet", "Discover packages": "Paketleri keşfedin", "Distinguish between\nuppercase and lowercase": "Büyük ve küçük harf arasında ayrım yapın", "Distinguish between uppercase and lowercase": "Büyük ve küçük\n harf arasında ayrım yapın", "Do NOT check for updates": "Güncellemeleri DENETLEME", "Do an interactive install for the selected packages": "Seçili paketler i<PERSON><PERSON> et<PERSON> kuru<PERSON> yap", "Do an interactive uninstall for the selected packages": "Seçili paketler için et<PERSON>li olarak kaldır", "Do an interactive update for the selected packages": "Seçili paketleri etkileşimli olarak güncelle", "Do not automatically install updates when the battery saver is on": "Pil tasarrufu açıkken güncellemeleri otomatik olarak yükleme", "Do not automatically install updates when the network connection is metered": "<PERSON><PERSON> bağlantısı sınırlı olduğunda güncellemeleri otomatik olarak yükleme", "Do not download new app translations from GitHub automatically": "<PERSON>ni uygulama çevirileri GitHub'dan otomatik olarak indirilmesin", "Do not ignore updates for this package anymore": "Artık bu pakete ilişkin güncellemeleri göz ardı etmeyin", "Do not remove successful operations from the list automatically": "Başarılı işlemleri listeden otomatik olarak kaldırma", "Do not show this dialog again for {0}": "{0} i<PERSON><PERSON> bu ileti<PERSON>im kutusunu bir daha gö<PERSON>me", "Do not update package indexes on launch": "Açılışta paket dizinlerini güncellenmesin", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "UniGetui'nin yalnızca kullanıcı deneyimini anlamak ve geliştirmek amacıyla anonim kullanım istatistikleri topladığını ve gönderdiğini kabul ediyor musunuz?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "WingetUI'yi faydalı buluyor musunuz? Ya<PERSON><PERSON>irs<PERSON>z, WingetUI'yi nihai paket yönetim arayüzü yapmaya devam edebilmem için çalışmamı desteklemek isteyebilirsiniz.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "WingetUI'yi faydalı buluyor musunuz? Geliştiriciyi desteklemek ister misiniz? <PERSON><PERSON><PERSON>e, {0}ya<PERSON><PERSON><PERSON>z, çok yardımcı olur!", "Do you really want to reset this list? This action cannot be reverted.": "Bu listeyi gerçekten sıfırlamak istiyor musunuz? Bu eylem geri alınamaz.", "Do you really want to uninstall the following {0} packages?": "Aşağıdaki {0} paketleri gerçekten kaldırmak istiyor musunuz?", "Do you really want to uninstall {0} packages?": "Gerçekten {0} paketi kaldırmak istiyor musunuz?", "Do you really want to uninstall {0}?": "{0} uygulamasını gerçekten kaldırmak istiyor musunuz?", "Do you want to restart your computer now?": "Bilgisayarınızı şimdi yeniden başlatmak istiyor musunuz?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "WingetUI yazılımını kendi dilinize çevirmek ister misiniz? Nasıl katkıda bulunacağınızı öğrenin <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Bağış yapmak istemiyor musunuz? <PERSON><PERSON>şelenme, WingetUI'yi her zaman arkadaşlarınla paylaşabilirsin. WingetUI'yi her<PERSON>e <PERSON>.", "Donate": "Bağış Yap", "Done!": "Tamam!", "Download failed": "<PERSON><PERSON>r başarı<PERSON>ız", "Download installer": "Yükleyiciyi indir", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "İndirme başarılı", "Download updated language files from GitHub automatically": "Güncellenen dil dosyalarını GitHub'dan otomatik olarak indirin", "Downloading": "İndiriliyor", "Downloading backup...": "Yedekleme indiriliyor...", "Downloading installer for {package}": "{package} i<PERSON><PERSON> indiriliyor", "Downloading package metadata...": "Paket açıklamaları indiriliyor...", "Enable Scoop cleanup on launch": "Başlangıçta Scoop temizlemeyi etkinleştir", "Enable WingetUI notifications": "WingetUI bildirimlerini etkinleştir", "Enable an [experimental] improved WinGet troubleshooter": "[<PERSON><PERSON><PERSON>] o<PERSON>ak gel<PERSON>ştirilmiş bir WinGet sorun gidericisini etkinleştirin", "Enable and disable package managers, change default install options, etc.": "Paket yöneticilerini etkinleştirin ve devre dışı bırakın, var<PERSON>ılan kurulum seçeneklerini değiştirin, vb.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Arka Plan CPU kullanım optimizasyonlarını etkinleştirin (Bkz. İstek #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Arka plan api'sini etkinleştir (WingetUI Widget'ları ve Paylaşımı, bağlantı noktası 7058)", "Enable it to install packages from {pm}.": "Paketleri {pm} tarihinden itibaren yüklemek için et<PERSON>rin.", "Enable the automatic WinGet troubleshooter": "Otomatik WinGet sorun gidericisini etkinleştirin", "Enable the new UniGetUI-Branded UAC Elevator": "Yeni UniGetUI Markalı UAC Elevator etkinleştirin", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, YALNIZCA VE YALNIZCA bunların ne işe yaradığını, olası etkilerini ve tehlikelerini tam olarak anlıyorsanız etkinleştirin.", "Enable {pm}": "{pm} et<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Enter proxy URL here": "Proxy URL'sini buraya girin", "Entries that show in RED will be IMPORTED.": "KIRMIZI renkte gösterilen girdiler ÖNEMLİDİR.", "Entries that show in YELLOW will be IGNORED.": "SARI renkte gösterilen girdiler YOK SAYILACAKTIR.", "Error": "<PERSON><PERSON>", "Everything is up to date": "<PERSON> <PERSON><PERSON> güncel", "Exact match": "<PERSON>", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Masaüstünüzde bulunan kısayollar taranacak ve hangilerini tutacağınızı, hangilerini kaldıracağınızı seçmeniz gerekecektir.", "Expand version": "Sürümü genişlet", "Experimental settings and developer options": "<PERSON>eysel a<PERSON>lar ve geliştirici seçenekleri", "Export": "Dışa Aktar", "Export log as a file": "Günlüğü bir dosya olarak dışa aktar", "Export packages": "Paketleri dışa aktar", "Export selected packages to a file": "Seçili paketleri bir dosyaya aktar", "Export settings to a local file": "Ayarları yerel bir dosyaya aktar", "Export to a file": "<PERSON>ir dosyaya aktar", "Failed": null, "Fetching available backups...": "Kullanılabilir yedekler getiriliyor...", "Fetching latest announcements, please wait...": "En son du<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>...", "Filters": "<PERSON><PERSON><PERSON><PERSON>", "Finish": "Bitir", "Follow system color scheme": "Sistem temasını kullan", "Follow the default options when installing, upgrading or uninstalling this package": "<PERSON><PERSON> paketi y<PERSON>, y<PERSON><PERSON><PERSON><PERSON>rk<PERSON> veya kaldırırken varsayılan seçenekleri izleyin", "For security reasons, changing the executable file is disabled by default": "Güvenlik nedenlerinden dolayı, yürütülebilir dosyayı değiştirme varsayılan olarak devre dışıdır", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Gü<PERSON><PERSON>deniyle, özel komut satırı argümanları varsayılan olarak devre dışıdır. Bunu değiştirmek için UniGetUI güvenlik ayarlarına gidin.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Güvenlik nedeniyle, işlem öncesi ve işlem sonrası komut dosyaları varsayılan olarak devre dışıdır. Bunu değiştirmek için UniGetUI güvenlik ayarlarına gidin.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Kuvvet KOLU derlenmiş winget versiyonu (SADECE ARM64 SİSTEMLERİ İÇİN)", "Formerly known as WingetUI": "Eskiden WingetUI olarak biliniyordu", "Found": "Bulundu", "Found packages: ": "Bulunan paketler:", "Found packages: {0}": "Bulunan paketler : {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON>nan paketler : {0}, he<PERSON><PERSON><PERSON> bitmedi...", "General preferences": "<PERSON><PERSON>", "GitHub profile": "GitHub profili", "Global": "<PERSON><PERSON><PERSON><PERSON>", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Bilinmeyen ama kullanışlı yardımcı programlar ve diğer ilginç paketlerden oluşan harika bir depo.<br>İçerik: <b>Yardımcı Programlar, <PERSON><PERSON><PERSON> Satırı <PERSON>ları, <PERSON><PERSON> (Ekstra olarak Bucket gerekir)</b>", "Great! You are on the latest version.": "Harika! En son sürümdesiniz.", "Grid": "Izgara", "Help": "Yardım", "Help and documentation": "Yardım ve Dökümantasyon", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Burada UniGetUI'nin aşağıdaki kısayollara ilişkin davranışını değiştirebilirsiniz. Bir kısayolun kontrol edilmesi, eğer gelecekteki bir yükseltmede oluşturulursa UniGetUI'nin onu silmesini sağlayacaktır. İşaretini kaldırmak kısayolu olduğu gibi koruyacaktır", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON><PERSON> benim adım Marti ve ben WingetUI'ın <i>geliştircisiyim</i>,  WingetUI tamamen boş zamanlarımda yapıldı!", "Hide details": "Ayrıntıları gizle", "Homepage": "<PERSON>", "Hooray! No updates were found.": "Yaşasın! Her şey güncel!", "How should installations that require administrator privileges be treated?": "Yönetici ayrıcalıkları gerektiren kurulumlar nasıl ele alınsın?", "How to add packages to a bundle": "Bir paket grubuna paketler nasıl eklenir", "I understand": "<PERSON><PERSON><PERSON><PERSON>", "Icons": "<PERSON><PERSON><PERSON><PERSON>", "Id": "İd", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "<PERSON>u paket i<PERSON>in gelecekteki güncellemeleri yoksay", "Ignore packages from {pm} when showing a notification about updates": "G<PERSON><PERSON><PERSON>meler hakkında bir bildirim gösteren {pm} paketleri yoksay", "Ignore selected packages": "Seçilen paketleri yoksay", "Ignore special characters": "<PERSON>zel ka<PERSON>terleri yoksay", "Ignore updates for the selected packages": "Seçili paketler i<PERSON><PERSON> g<PERSON> yok say ", "Ignore updates for this package": "<PERSON>u paket i<PERSON><PERSON> g<PERSON> yok say", "Ignored updates": "<PERSON><PERSON>", "Ignored version": "<PERSON><PERSON>", "Import": "İçe aktar", "Import packages": "Paketleri içe aktar", "Import packages from a file": "Paketleri bir dosyadan içe aktarın\n", "Import settings from a local file": "Ayarları yerel bir dosyadan içe aktar", "In order to add packages to a bundle, you will need to: ": "Bir paket grubuna paket eklemek için şunları yapmanız gerekir:", "Initializing WingetUI...": "WingetUI başlatılıyor...", "Install": "<PERSON><PERSON><PERSON>", "Install Scoop": "<PERSON><PERSON>'u yükle", "Install and more": null, "Install and update preferences": "Tercih<PERSON>i yükleme ve güncelleme", "Install as administrator": "Yönetici olarak <PERSON>", "Install available updates automatically": "Mevcut g<PERSON>llemeleri otomatik olarak yükle", "Install location can't be changed for {0} packages": null, "Install location:": "<PERSON><PERSON><PERSON> yeri:", "Install options": null, "Install packages from a file": "Paketleri bir <PERSON>", "Install prerelease versions of UniGetUI": "UniGetUI'nin yayın öncesi sürümlerini yükleyin", "Install selected packages": "Seçili paketleri yükle", "Install selected packages with administrator privileges": "Seçili paketleri yönetici ayrıcalıklarıyla yükle", "Install selection": "<PERSON><PERSON><PERSON><PERSON>", "Install the latest prerelease version": "En son s<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Install updates automatically": "Güncellemeleri otomatik olarak <PERSON>", "Install {0}": "{0}'<PERSON> <PERSON><PERSON>", "Installation canceled by the user!": "<PERSON><PERSON><PERSON> kullanıcı tarafından iptal edildi!", "Installation failed": "Yükleme başarısız", "Installation options": "<PERSON><PERSON><PERSON><PERSON>", "Installation scope:": "<PERSON>ope yüklemesi:", "Installation succeeded": "<PERSON><PERSON><PERSON> başarılı", "Installed Packages": "Yüklü Paketler", "Installed Version": "Yüklü Sürüm", "Installed packages": "Yüklü Paketler", "Installer SHA256": "SHA256 Yü<PERSON>ici", "Installer SHA512": "SHA512 <PERSON>ici", "Installer Type": "Yükleyici Türü", "Installer URL": "Yükleyici URL'si", "Installer not available": "Yükleyici kullanılamıyor", "Instance {0} responded, quitting...": "Örnek {0} ya<PERSON><PERSON><PERSON> verdi, çıkılıyor...", "Instant search": "Yazarken ara", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Bütünlük kontrolleri atlandı", "Integrity checks will not be performed during this operation": "Bu işlem sırasında bütünlük kontrolleri yapılmayacak", "Interactive installation": "İnterak<PERSON>f <PERSON>", "Interactive operation": "Etkileşimli <PERSON>", "Interactive uninstall": "<PERSON>nterak<PERSON>f kaldı<PERSON>", "Interactive update": "<PERSON>nterak<PERSON><PERSON>", "Internet connection settings": "İnternet bağlantı ayarları", "Is this package missing the icon?": "Bu paketin simgesi mi eksik?", "Is your language missing or incomplete?": "Diliniz eksik mi yoksa tam değil mi?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Sağlanan kimlik bilgilerinin güvenli bir şekilde saklanacağı garanti edilmez, bu nedenle banka hesabınızın kimlik bilgilerini kullanmamanız daha iyi olur", "It is recommended to restart UniGetUI after WinGet has been repaired": "WinGet onarıldıktan sonra UniGetUI'nin yeniden başlatılması önerilir", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Görünüşe göre WinGet düzgün çalışmıyor. WinGet'i onarmayı denemek istiyor musunuz?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Görünüşe göre WingetUI'yi yönetici olarak çalıştırmışsınız, bu önerilmez. Programı yine de kullanabilirsiniz, ancak WingetUI'yi yönetici ayrıcalıklarıyla çalıştırmamanızı önemle tavsiye ederiz. Nedenini görmek için \"{showDetails}\" seçeneğine tıklayın.", "Language": "Dil", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON>, tema ve diğer çeşitli tercihler", "Last updated:": "<PERSON> günce<PERSON><PERSON>:", "Latest": "En son", "Latest Version": "En son s<PERSON><PERSON><PERSON><PERSON>", "Latest Version:": "En son s<PERSON><PERSON><PERSON><PERSON>:", "Latest details...": "Son ayrıntılar...", "Launching subprocess...": "Alt süreç başlatılıyor...", "Leave empty for default": "Varsayılan olarak boş bırakın", "License": "Lisa<PERSON>", "Licenses": "Lisanslar", "Light": "Aydınlık", "List": "Liste", "Live command-line output": "Canlı komut satırı çıktısı", "Live output": "Canlı çıkış", "Loading UI components...": "Kullanıcı arayüzü bileşenleri yükleniyor...", "Loading WingetUI...": "WingetUI yükleniyor", "Loading packages": "Paketler yükleniyor", "Loading packages, please wait...": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "Loading...": "Yükleniyor...", "Local": "<PERSON><PERSON>", "Local PC": "<PERSON><PERSON>", "Local backup advanced options": null, "Local machine": "<PERSON><PERSON> ma<PERSON>e", "Local package backup": null, "Locating {pm}...": "{pm} konumu belirleniyor...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "Günlük düzeyi:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Paketler aranıyor...", "Machine | Global": "Makine | Küresel", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "<PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "UniGetUI ayarlarını yönetin", "Manage WingetUI autostart behaviour from the Settings app": "WingetUI otomatik başlatma davranışını Ayarlar uygulamasından yönetin", "Manage ignored packages": "Yok sayılan paketleri yönet", "Manage ignored updates": "<PERSON>k sayılan güncellemeleri yönet", "Manage shortcuts": "Kısayolları yönet", "Manage telemetry settings": "Telemetri ayarlarını yönet", "Manage {0} sources": "{0} kaynaklarını yönet", "Manifest": "Manifesto", "Manifests": "Manifestolar", "Manual scan": "<PERSON>", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoft'un resmi paket yöneticisi. Tanınmış ve doğrulanmış paketlerle dolu<br>İçerik: <b><PERSON><PERSON>, Microsoft Mağazası uygulamaları</b>", "Missing dependency": "Eksik bağımlılık", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON> fazla detay", "More details about the shared data and how it will be processed": "Paylaşılan veriler ve nasıl işleneceği hakkında daha fazla ayrıntı", "More info": "<PERSON><PERSON> fazla bilgi", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOT: <PERSON><PERSON>, WinGet bölümündeki UniGetUI Ayarları'ndan devre dışı bırakılabilir", "Name": "İsim", "New": null, "New Version": "<PERSON><PERSON>", "New bundle": "<PERSON><PERSON> paket grubu", "New version": "<PERSON><PERSON>", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Hay<PERSON><PERSON>", "No applicable installer was found for the package {0}": "<PERSON><PERSON> için geçerli bir yükleyici bulunamadı ({0})", "No dependencies specified": null, "No new shortcuts were found during the scan.": "Tarama sırasında yeni kısayol bulunmadı.", "No packages found": "<PERSON><PERSON> bulu<PERSON>adı", "No packages found matching the input criteria": "G<PERSON>len kriterlere uygun paket bulunamadı", "No packages have been added yet": "Hen<PERSON>z hiçbir paket eklenmedi", "No packages selected": "Hiçbir paket seçilmedi", "No packages were found": "<PERSON><PERSON> bulu<PERSON>adı", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Kişisel bilgi toplanmaz veya gönderilmez ve toplanan veriler anonimize edilir, bu ne<PERSON>le bulunamazsınız.", "No results were found matching the input criteria": "<PERSON><PERSON><PERSON> kriterlerine uygun sonuç bulunamadı", "No sources found": "Kaynak Bulunamadı", "No sources were found": "Kaynak Bulunamadı", "No updates are available": "<PERSON><PERSON><PERSON><PERSON><PERSON> yok", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "\"Node JS'in paket yöneticisi. Javascript dünyasının yörüngesinde dönen kütüphaneler ve diğer yardımcı programlarla dolu.<br>İçerik: <b>Node JavaScript kütüphaneleri ve diğer ilgili yardımcı programlar</b>\"", "Not available": "<PERSON><PERSON><PERSON>", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Bulunamadı", "Not right now": "<PERSON><PERSON><PERSON>", "Notes:": "Notlar:", "Notification preferences": "<PERSON><PERSON><PERSON><PERSON>", "Notification tray options": "<PERSON><PERSON><PERSON><PERSON>", "Notification types": "<PERSON><PERSON><PERSON><PERSON>", "NuPkg (zipped manifest)": "NuPkg (sıkıştırılmış manifest)", "OK": "TAMAM", "Ok": "<PERSON><PERSON>", "Open": "Aç", "Open GitHub": "GitHub'ı aç", "Open UniGetUI": "UniGetUI'yi aç", "Open UniGetUI security settings": null, "Open WingetUI": "WingetUI'yi aç", "Open backup location": "Yedekleme konumunu aç", "Open existing bundle": "Mevcut paket grubunu aç", "Open install location": "<PERSON><PERSON><PERSON> konumunu aç", "Open the welcome wizard": "Karşılama sihirbazını aç", "Operation canceled by user": "İşlem kullanıcı tarafından iptal edildi", "Operation cancelled": "İşlem iptal edildi", "Operation history": "İşlem geçmişi", "Operation in progress": "İşlem devam ediyor.", "Operation on queue (position {0})...": "Kuyr<PERSON><PERSON> i<PERSON> (konum {0})...", "Operation profile:": null, "Options saved": "Seçenekler ka<PERSON>ildi", "Order by:": "Sıralama:", "Other": "<PERSON><PERSON><PERSON>", "Other settings": "<PERSON><PERSON><PERSON>", "Package": null, "Package Bundles": "Paketleme Grupları", "Package ID": "<PERSON><PERSON> (ID)", "Package Manager": "<PERSON><PERSON>", "Package Manager logs": "Paket Yöneticisi gü<PERSON>", "Package Managers": "<PERSON><PERSON>", "Package Name": "<PERSON><PERSON> ismi", "Package backup": "<PERSON><PERSON>", "Package backup settings": null, "Package bundle": "Paketleme g<PERSON>bu", "Package details": "Paket ayrıntıları", "Package lists": "<PERSON><PERSON>", "Package management made easy": "Paket yönetimi kolaylaştırıldı", "Package manager": "<PERSON><PERSON>", "Package manager preferences": "Paket yöneticisi tercihleri", "Package managers": "<PERSON><PERSON>", "Package not found": "<PERSON><PERSON> bulu<PERSON>adı", "Package operation preferences": "Paket işlem tercihleri", "Package update preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package {name} from {manager}": "{manager}'dan {name} paketi", "Package's default": null, "Packages": "<PERSON><PERSON><PERSON>", "Packages found: {0}": "Bulunan paketler: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON>", "Password": "Şifre", "Paste a valid URL to the database": "Veritabanına geçerli bir URL yapıştırın", "Pause updates for": "<PERSON><PERSON><PERSON> i<PERSON><PERSON>", "Perform a backup now": "Şimdi bir ye<PERSON><PERSON><PERSON> ya<PERSON>n", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>, l<PERSON><PERSON><PERSON> be<PERSON>...", "Periodically perform a backup of the installed packages": "Yüklü paketlerin periyodik olarak yedeğini alın", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "Lütfen bu paketin yü<PERSON><PERSON> seçeneklerini kontrol edip tekrar deneyin.", "Please click on \"Continue\" to continue": "Devam etmek için l<PERSON> \"Devam\"a tıklayın", "Please enter at least 3 characters": "Lütfen en az 3 karakter girin.", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> makinede etkin olduğu için yüklenemeyebileceğini lütfen unutmayın.", "Please note that not all package managers may fully support this feature": "Lütfen tüm paket yöneticilerinin bu özelliği tam olarak desteklemeyebileceğini unutmayın", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Belirli kaynaklardan gelen paketlerin dışa aktarılamayabileceğini lütfen unutmayın. Bunlar gri renktedir ve dışa aktarılmayacaktır.", "Please run UniGetUI as a regular user and try again.": "Lütfen UniGetUI'yi normal kullanıcı olarak çalıştırın ve tekrar deneyin.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "<PERSON><PERSON> hakkında daha fazla bilgi için lütfen Komut satırı Çıktısına veya İşlem Geçmişine bakın.", "Please select how you want to configure WingetUI": "Lütfen WingetUI'yi nasıl yapılandırmak istediğinizi seçin", "Please try again later": "<PERSON><PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "Please type at least two characters": "Lütfen en az iki karakter yazın", "Please wait": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "{0} yüklenirken lütfen bekleyin. Siyah bir pencere görünebilir. Lütfen kapanana kadar bekleyin.", "Please wait...": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>in...", "Portable": "Taşinabi̇li̇nir", "Portable mode": "Taşınabilir Mod", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell'in paket yöneticisi.  PowerShell özelliklerini genişletmek için kitaplıkları ve komut dosyalarını bulun<br><PERSON><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, C<PERSON><PERSON><PERSON>'ler</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "Önsürüm", "Preparing packages, please wait...": "<PERSON><PERSON>ler hazırl<PERSON>ıyor, <PERSON><PERSON><PERSON><PERSON> be<PERSON>...", "Proceed at your own risk.": "<PERSON><PERSON> etmek kendi sorumluluğunuzda.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "Proxy URL", "Proxy compatibility table": "Proxy uyumluluk tablosu", "Proxy settings": "Proxy a<PERSON>ları", "Proxy settings, etc.": "Proxy ayarları vb.", "Publication date:": "Ya<PERSON>ı<PERSON>lanma tarihi:", "Publisher": "Yayımcı", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python'un kütüphane yöneticisi. Python kitaplıkları ve diğer python ile ilgili yardımcı programlarla dolu<br>İçerik: <b>Python kitaplıkları ve ilgili yardımcı programlar</b>", "Quit": "Çıkış", "Quit WingetUI": "WingetUI'den çık", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "<PERSON><PERSON><PERSON>", "Reinstall package": "<PERSON><PERSON> ye<PERSON>", "Related settings": "<PERSON><PERSON><PERSON><PERSON>", "Release notes": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı", "Release notes URL": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı", "Release notes URL:": "Sürüm notları URL: ", "Release notes:": "S<PERSON>rüm notları: ", "Reload": "<PERSON><PERSON><PERSON>", "Reload log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ye<PERSON>", "Removal failed": "Ka<PERSON><PERSON>rma başarısız", "Removal succeeded": "Ka<PERSON><PERSON>rma başarılı", "Remove from list": "<PERSON><PERSON> sil", "Remove permanent data": "Kalıcı verileri kaldır", "Remove selection from bundle": "Seçimi paket grubundan kaldır", "Remove successful installs/uninstalls/updates from the installation list": "Başarılı yüklemeleri, kaldırmaları ve güncellemeleri yükleme listesinden kaldır", "Removing source {source}": "{source} kaynağından kaldırılıyor", "Removing source {source} from {manager}": "{source} ka<PERSON>ğ<PERSON> {manager} ka<PERSON><PERSON><PERSON><PERSON><PERSON> kaldırılıyor", "Repair UniGetUI": null, "Repair WinGet": "WinG<PERSON>'i onar", "Report an issue or submit a feature request": "Bir sorunu bildirin veya bir özellik isteği gönderin", "Repository": "<PERSON><PERSON>", "Reset": "Sıfırla", "Reset Scoop's global app cache": "Scoop'un global uygulama önbelleğini sıfırla", "Reset UniGetUI": "UniGetUI'yi sıfırla", "Reset WinGet": "WinGet'i sıfırla", "Reset Winget sources (might help if no packages are listed)": "Winget kaynaklarını sıfırla (hiçbir paket listelenmemişse yardımcı olabilir)", "Reset WingetUI": "WingetUI'yi <PERSON>", "Reset WingetUI and its preferences": "WingetUI ve tercihleri sıfırla", "Reset WingetUI icon and screenshot cache": "WingetUI simge ve ekran görüntüsü önbelleğini sıfırla", "Reset list": "<PERSON><PERSON>i sı<PERSON>ı<PERSON>a", "Resetting Winget sources - WingetUI": "Winget kaynaklarını sıfırlama - WingetUI", "Restart": "<PERSON><PERSON><PERSON> b<PERSON>", "Restart UniGetUI": "UniGetUI'yi ye<PERSON> ba<PERSON>lat", "Restart WingetUI": "WingetUI'ı yeniden başlat", "Restart WingetUI to fully apply changes": "Değişiklikleri tamamıyla uygulamak için WingetUi'yi yeniden başlatın", "Restart later": "<PERSON><PERSON> sonra yeniden başlatacağım", "Restart now": "<PERSON><PERSON><PERSON> ba<PERSON>", "Restart required": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON> g<PERSON>", "Restart your PC to finish installation": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON> için PC'yi yeniden başlatın", "Restart your computer to finish the installation": "<PERSON><PERSON><PERSON>u tamamlamak için bilgisayarınızı yeniden başlatın", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "<PERSON><PERSON><PERSON> dene", "Retry as administrator": "Yönetici olarak yeniden den<PERSON>in", "Retry failed operations": "Başarısız operasyonları yeniden deneyin", "Retry interactively": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> ye<PERSON> den<PERSON>", "Retry skipping integrity checks": "Bütünlük kontrollerini atlayarak yeniden dene", "Retrying, please wait...": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "Return to top": "<PERSON><PERSON><PERSON>", "Run": "Çalıştır", "Run as admin": "Yönetici olarak çalıştır", "Run cleanup and clear cache": "Temizlemeyi çalıştır ve önbelleği temizle", "Run last": "Son <PERSON>", "Run next": "Sonra çalış", "Run now": "<PERSON><PERSON><PERSON> b<PERSON>", "Running the installer...": "Yükleyici çalıştırılıyor...", "Running the uninstaller...": "Kaldırıcı çalıştırılıyor...", "Running the updater...": "Güncelleyici çalıştırılıyor...", "Save": null, "Save File": "Dosyayı kaydet", "Save and close": "<PERSON><PERSON>", "Save as": null, "Save bundle as": "Paket grubunu farklı kaydet", "Save now": "<PERSON><PERSON><PERSON> ka<PERSON>", "Saving packages, please wait...": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "Scoop Installer - WingetUI": "<PERSON><PERSON> - WingetUI", "Scoop Uninstaller - WingetUI": "<PERSON><PERSON> - WingetUI", "Scoop package": "<PERSON><PERSON> paketi", "Search": "Ara", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Masaüstü yazılımı ara, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> geldiğinde beni uyar ve karmaşık şeyler yapma. WingetUI'nin aşırı karmaşık olmasını istemiyorum, sadece basit bir <b>yazılım mağazası</b> istiyorum", "Search for packages": "<PERSON><PERSON> ara", "Search for packages to start": "Başlamak için paket ara", "Search mode": "<PERSON><PERSON> modu", "Search on available updates": "Mevcut güncellemeleri kontrol et", "Search on your software": "Ku<PERSON>lu yazılım ara", "Searching for installed packages...": "Kurulu paketler aranıyor...", "Searching for packages...": "Paketler aranıyor...", "Searching for updates...": "Searching for updates...", "Select": "Seç", "Select \"{item}\" to add your custom bucket": "Özel paketinizi eklemek için \"{item}\" öğesini seçin.", "Select a folder": "<PERSON><PERSON> k<PERSON>ö<PERSON>", "Select all": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç", "Select all packages": "<PERSON><PERSON><PERSON> paketleri seç", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "Ya<PERSON><PERSON>z<PERSON> <b>ne yaptığınızı biliyorsanız</b> se<PERSON><PERSON>.", "Select package file": "paket dosyasını seçin", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "Eklemek istediğiniz kaynağı seçin:", "Select upgradable packages by default": "Yükseltilebilir paketleri varsayılan olarak seç", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON>i <b>paket yöneticilerinin</b> kullanılacağını seçin ({0}), paketlerin nasıl yükleneceğini yapılandırın, yönetici ayrıcalıklarının nasıl ele alınacağını yönetin, vb.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake gö<PERSON>ildi. Örnek dinleyicinin yanıtı bekleniyor... ({0}%)", "Set a custom backup file name": "Özel bir yedek<PERSON>e dosyası adı belirleyin", "Set custom backup file name": "<PERSON>zel yedekleme dos<PERSON>ı adı ayarla", "Settings": "<PERSON><PERSON><PERSON>", "Share": "Paylaş", "Share WingetUI": "WingetUI'yi <PERSON>", "Share anonymous usage data": "<PERSON><PERSON><PERSON> kullanım verilerini <PERSON>", "Share this package": "<PERSON><PERSON> paketi <PERSON>", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "UniGetUI'yi sistem tepsisinde göster", "Show UniGetUI's version and build number on the titlebar.": "UniGetUI'nin sü<PERSON>ü<PERSON>ü<PERSON>ü başlık çubuğunda göster", "Show WingetUI": "WingetUI'ı göster\n", "Show a notification when an installation fails": "B<PERSON> yükleme başarısız olduğunda bildirim göster", "Show a notification when an installation finishes successfully": "<PERSON><PERSON> yükleme başarıyla tamamlandığında bildirim göster", "Show a notification when an operation fails": "Bir işlem başarısız olduğunda bildirim göster", "Show a notification when an operation finishes successfully": "Bir işlem başarıyla tamamlandığında bildirim göster", "Show a notification when there are available updates": "Mevcut güncellemeler olduğunda bildirim göster", "Show a silent notification when an operation is running": "Bir işlem çalışırken sessiz bildirim göster", "Show details": "Ayrıntıları göster", "Show in explorer": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> g<PERSON>", "Show info about the package on the Updates tab": "Güncellemeler sekmesinde paketle ilgili bilgileri göster", "Show missing translation strings": "Çeviri hatalarını göster", "Show notifications on different events": "Farklı etkinliklere ilişkin bildirimleri göster", "Show package details": "Paket ayrıntılarını göster", "Show package icons on package lists": "<PERSON><PERSON> listelerinde paket simgelerini göster", "Show similar packages": "Benzer paketleri göster", "Show the live output": "canlı çıktıyı göster", "Size": "<PERSON><PERSON>", "Skip": "Atla", "Skip hash check": "Hash kontrolünü atla", "Skip hash checks": "<PERSON><PERSON> kontrollerini atla", "Skip integrity checks": "Bütünlük kontrollerini atla", "Skip minor updates for this package": "Bu paket için küçük güncellemeleri atla", "Skip the hash check when installing the selected packages": "Seçili paketleri yüklerken hash kontrolünü atla", "Skip the hash check when updating the selected packages": "Seçili paketleri güncellerken hash kontrolünü atla", "Skip this version": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> atla", "Software Updates": "Ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "Something went wrong": "<PERSON><PERSON> so<PERSON> o<PERSON>", "Something went wrong while launching the updater.": "Güncelleyici başlatılırken bir şeyler ters gitti.", "Source": "<PERSON><PERSON><PERSON>", "Source URL:": "Kaynak URL'si:", "Source added successfully": "Kaynak başarıyla eklendi", "Source addition failed": "Kaynak ekleme başarısız oldu", "Source name:": "Kaynak adı:", "Source removal failed": "Kaynak kaldırılamadı", "Source removed successfully": "Kaynak başarıyla kaldırıldı", "Source:": "Kaynak:", "Sources": "<PERSON><PERSON><PERSON><PERSON>", "Start": "Başla", "Starting daemons...": "Arka plan uygulamaları başlatılıyor...", "Starting operation...": "Çalışmaya başlıyor...", "Startup options": "Başlangıç seçenekleri", "Status": "Durum", "Stuck here? Skip initialization": "Takıldı mı? Başlatmayı atla", "Suport the developer": "G<PERSON>ş<PERSON><PERSON><PERSON><PERSON>", "Support me": "<PERSON><PERSON>", "Support the developer": "Geliş<PERSON><PERSON><PERSON><PERSON>", "Systems are now ready to go!": "Sistemler artık kullanıma hazır!", "Telemetry": "Telemetri", "Text": "<PERSON><PERSON>", "Text file": "<PERSON><PERSON>", "Thank you ❤": "Teşekkürler ❤", "Thank you 😉": "Teşekkürler 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust paket yöneticisi.<br>İçerir: <b>Rust kitaplıkları ve Rust'ta yazılmış programlar</b>", "The backup will NOT include any binary file nor any program's saved data.": "<PERSON><PERSON><PERSON><PERSON>, herhangi bir ikili dosya veya herhangi bir programın kaydedilmiş verilerini içermeyecektir.", "The backup will be performed after login.": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON> sonra gerçekleştirilecektir.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> paketlerin tam listesini ve kurulum seçeneklerini içerecektir. <PERSON><PERSON> <PERSON>ı<PERSON> güncellemeler ve atlanan sürümler de kaydedilecektir.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Yüklemeye çalıştığınız paket grubu geçersiz görünüyor. Lütfen dosyayı kontrol edip tekrar deneyin.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Yazılımın Hash değeri orijinal değerle uyuşmuyor ve yükleyicinin güvenilirliği doğrulanamıyor. Yayıncıya güveniyorsanız, Hash kontrolünü atlayarak paketi yeniden {0}.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Windows için klasik paket yöneticisi. Orada her şeyi bulacaksınız. <br>İçerir: <b><PERSON><PERSON> Yazılım</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Mevcut paket grubunda paket yok. Başlamak için birkaç paket ekleyin.", "The executable file for {0} was not found": "Yürütülebilir dosya {0} i<PERSON>in bulunamadı", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Aşağıdaki paketler bir JSON dosyasına aktarılacak. Kullanıcı verileri veya ikili dosyalar kaydedilmeyecek.", "The following packages are going to be installed on your system.": "Aşağıdaki paketler sisteminize yüklenecek.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "<PERSON>u paket her yü<PERSON><PERSON><PERSON><PERSON><PERSON>, güncellendiğinde veya kaldırıldığında aşağıdaki ayarlar uygulanacaktır.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "<PERSON>u paket her y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, güncellendiğinde veya kaldırıldığında aşağıdaki ayarlar uygulanacaktır. Bunlar otomatik olarak kaydedilecektir.", "The icons and screenshots are maintained by users like you!": "Simgeler ve ekran görüntüleri sizin gibi kullanıcılar tarafından sağlanmaktadır!", "The installer authenticity could not be verified.": "Yükleyicinin orijinalliği doğrulanamadı.", "The installer has an invalid checksum": "Yazılım geçersiz Hash değerine sahip", "The installer hash does not match the expected value.": "Yükleyici karması beklenen değerle eşleşmiyor.", "The local icon cache currently takes {0} MB": "<PERSON><PERSON> simge önbelleği şu anda {0} MB yer kaplıyor", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "<PERSON>u projenin ana amacı, Winget ve Scoop gibi Windows için en yaygın CLI paket yöneticilerini yönetmek için sezgisel bir kullanıcı arayüzü oluşturmaktır.", "The package \"{0}\" was not found on the package manager \"{1}\"": "\"{0}\" paketi \"{1}\" paket yöneticisinde bulunamadı", "The package bundle could not be created due to an error.": "Bir hata nedeniyle paket grubu oluşturulamadı.", "The package bundle is not valid": "<PERSON>et grubu ge<PERSON><PERSON><PERSON>", "The package manager \"{0}\" is disabled": "\"{0}\" paket yöneticisi devre dışı", "The package manager \"{0}\" was not found": "\"{0}\" paket yöneticisi bulunamadı", "The package {0} from {1} was not found.": "{1} kaynağından {0} paketi bulunamadı.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Burada listelenen paketler güncellemeler kontrol edilirken dikkate alınmayacaktır. Güncellemelerini yok saymayı durdurmak için bunlara çift tıklayın veya sağlarındaki düğmeye tıklayın.", "The selected packages have been blacklisted": "Seçilen paketler kara listeye alındı", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Yedekleme boyutunun 1 MB'den küçük olduğu tahmin edilmektedir.", "The source {source} was added to {manager} successfully": "{source} ka<PERSON>ğı {manager} yöneticisine başarıyla eklendi", "The source {source} was removed from {manager} successfully": "{source} kaynağı {manager} ta<PERSON><PERSON>ı<PERSON>n başarıyla kaldırıldı", "The system tray icon must be enabled in order for notifications to work": "Bildirimlerin çalışması için sistem tepsisi simgesinin etkinleştirilmesi gerekir", "The update process has been aborted.": "<PERSON><PERSON><PERSON><PERSON><PERSON> işlemi iptal edildi.", "The update process will start after closing UniGetUI": "UniGetUI kapatıldıktan sonra güncelleme işlemi başlayacak", "The update will be installed upon closing WingetUI": "<PERSON><PERSON><PERSON><PERSON><PERSON>, WingetUI kapatıldıktan sonra yüklenecektir", "The update will not continue.": "<PERSON><PERSON><PERSON><PERSON><PERSON> de<PERSON>.", "The user has canceled {0}, that was a requirement for {1} to be run": "Kullanıcı iptal etti {0}, {1} çalıştırılması için bir gereklilikti.", "There are no new UniGetUI versions to be installed": "Yüklenecek yeni UniGetUI sürümü yok", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Devam eden i<PERSON>lemler var. WingetUI'den çıkmak başarısız olmalarına neden olabilir. Devam etmek ister misiniz?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "YouTube'da WingetUI'yi ve yeteneklerini sergileyen harika videolar var. Faydalı ipuçları ve püf noktaları öğrenebilirsiniz!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "WingetUI'yi yönetici olarak çalıştırmamanın iki ana nedeni vardır: <PERSON><PERSON><PERSON><PERSON>, Scoop paket yöneticisinin yönetici haklarıyla çalıştırıldığında bazı komutlarda sorunlara neden olabilmesidir. <PERSON><PERSON><PERSON><PERSON>, WingetUI'yi yönetici olarak çalıştırmak, indirdiğiniz herhangi bir paketin yönetici olarak çalıştırılacağı anlamına gelir (ve bu güvenli değildir). Belirli bir paketi yönetici olarak yüklemeniz gerekiyorsa, öğeyi her zaman sağ tıklatabileceğinizi unutmayın -> Yönetici olarak Yükle/Güncelle/Kaldır.", "There is an error with the configuration of the package manager \"{0}\"": "\"{0}\" paket yöneticisinin yapılandırmasında bir hata var", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Devam eden bir kurulum var. WingetUI'ı kapatırsanız, yü<PERSON><PERSON> başarısız olabilir ve beklenmeyen sonuçlara yol açabilir. Hala WingetUI'den çıkmak istiyor musunuz?", "They are the programs in charge of installing, updating and removing packages.": "Onlar Paketleri yüklemek, güncellemek ve kaldırmakla görevli programlardır.", "Third-party licenses": "Üçüncü taraf l<PERSON>ı", "This could represent a <b>security risk</b>.": "<PERSON>u bir <b>g<PERSON><PERSON><PERSON> <PERSON>i</b> teşkil edebilir.", "This is not recommended.": "Bu önerilmez.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Bunun nedeni muhtemelen size gönderilen paketin kaldırılmış olması veya etkinleştirmediğiniz bir paket yöneticisinde yayınlanmış olmasıdır. Alınan kimlik {0}", "This is the <b>default choice</b>.": "<PERSON>u, <b><PERSON><PERSON><PERSON><PERSON></b>.", "This may help if WinGet packages are not shown": "WinGet paketleri gösterilmiyorsa bu yardımcı olabilir", "This may help if no packages are listed": "Hiçbir paket listelenmemişse bu yardımcı olabilir", "This may take a minute or two": "Bu işlem bir veya iki dakika sürebilir", "This operation is running interactively.": "Bu işlem etkileşimli olarak çalışmaktadır.", "This operation is running with administrator privileges.": "Bu işlem yönetici ayrıcalıklarıyla çalışıyor.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "<PERSON>u paket güncellenebilir", "This package can be updated to version {0}": "<PERSON>u paket {0} s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> güncellenebilir", "This package can be upgraded to version {0}": "Bu paket {0} sürümüne yükseltilebilir", "This package cannot be installed from an elevated context.": "<PERSON>u paket yükseltilmiş bir bağlamdan yüklenemez.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Bu paketin ekran görüntüsü yok veya simgesi eksik mi? Eksik simgeleri ve ekran görüntülerini herkese açık veritabanımıza ekleyerek WingetUI'ye katkıda bulunun.", "This package is already installed": "<PERSON><PERSON> zaten kuru<PERSON>", "This package is being processed": "<PERSON>u paket işleniyor", "This package is not available": "<PERSON>u paket mevcut değil", "This package is on the queue": "<PERSON>u paket sırada", "This process is running with administrator privileges": "Bu işlem yönetici ayrıcalıklarıyla çalışıyor", "This project has no connection with the official {0} project — it's completely unofficial.": "Bu projenin resmi {0} proje<PERSON><PERSON> bağlantısı yoktur — tamamen gayri resmi.", "This setting is disabled": "Bu ayar devre dışı", "This wizard will help you configure and customize WingetUI!": "<PERSON><PERSON>, WingetUI'yi yapılandırmanıza ve özelleştirmenize yardımcı olacak!", "Toggle search filters pane": "Arama filtreleri bölmesini aç/kapat", "Translators": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Buraya eklemek istediğiniz kaynağın adını ve URL'sini bir boşlukla ayırarak yazın.", "Unable to find package": "<PERSON><PERSON> bulu<PERSON>adı", "Unable to load informarion": "<PERSON><PERSON><PERSON>", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI, kullanıcı deneyimini geliştirmek için anonim kullanım verileri toplar.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI, yalnızca kullanıcı deneyimini anlamak ve geliştirmek amacıyla anonim kullanım verileri toplar.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI, otomatik olarak silinebilecek yeni bir masaüstü kısayolu algıladı.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI, gelecekteki yükseltmelerde otomatik olarak kaldırılabilecek aşağıdaki masaüstü kısayollarını algıladı", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI, otomatik olarak silinebilecek {0} yeni ma<PERSON>ü<PERSON>ü kısayolu algıladı.", "UniGetUI is being updated...": "UniGetUI güncelleniyor...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI uyumlu paket yöneticilerinin hiçbiriyle ilişkili değildir. UniGetUI bağımsız bir projedir.", "UniGetUI on the background and system tray": "Arka planda ve sistem tepsisinde UniGetUI", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI'nin <PERSON> için {0} gerekiyor ancak sisteminizde bulunamadı.", "UniGetUI startup page:": "UniGetUI başlangıç ​​sayfası:", "UniGetUI updater": "UniGetUI güncelleyici", "UniGetUI version {0} is being downloaded.": "UniGetUI {0} sürümü indiriliyor.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} kurulmaya hazır.", "Uninstall": "Kaldır", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON> (ve pake<PERSON>) kaldırın", "Uninstall and more": null, "Uninstall and remove data": "Verileri kaldır", "Uninstall as administrator": "Yönetici olarak kaldır", "Uninstall canceled by the user!": "Ka<PERSON>ırma işlemi kullanıcı tarafından iptal edildi!\n", "Uninstall failed": "Ka<PERSON><PERSON>rma başarısız", "Uninstall options": null, "Uninstall package": "Paketi kaldır", "Uninstall package, then reinstall it": "<PERSON><PERSON> kaldı<PERSON>, ardı<PERSON><PERSON> yeni<PERSON> yü<PERSON>", "Uninstall package, then update it": "<PERSON><PERSON> kaldı<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Seçili paketleri kaldır\n", "Uninstall selection": null, "Uninstall succeeded": "Ka<PERSON><PERSON>rma başarılı", "Uninstall the selected packages with administrator privileges": "Seçili paketleri yönetici ayrıcalıklarıyla kaldır", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Kaynağı \"{0}\" olarak listelenen kaldırılabilir paketler herhangi bir paket yöneticisinde yayınlanmaz, bu nedenle bunlar hakkında gösterilebilecek hiçbir bilgi yoktur.", "Unknown": "Bilinmeyen", "Unknown size": "Bilinmeyen boyut", "Unset or unknown": "Ayarlanmamış veya bilinmiyor", "Up to date": "<PERSON><PERSON><PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "WingetUI'ı otomatik olarak güncelle", "Update all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update and more": null, "Update as administrator": "Yönetici olarak <PERSON>", "Update check frequency, automatically install updates, etc.": "<PERSON><PERSON><PERSON><PERSON>me kontrol s<PERSON>ı, g<PERSON><PERSON>llemelerin otomatik olarak yüklenmesi vb.", "Update date": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "Update failed": "G<PERSON>ncelleme işlemi başarısız", "Update found!": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulundu!", "Update now": "<PERSON><PERSON><PERSON>", "Update options": null, "Update package indexes on launch": "Girişte paket dizinlerini güncelle", "Update packages automatically": "Paketleri otomatik olarak gü<PERSON>lle", "Update selected packages": "Seçili paketleri güncelle", "Update selected packages with administrator privileges": "Seçili paketleri yönetici ayrıcalıklarıyla güncelle", "Update selection": null, "Update succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarılı", "Update to version {0}": "{0} s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update to {0} available": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON> mevcut", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Vcpkg'ın Git portfiles dosyalarını otomatik olarak güncelleyin (Git'in kurulu olmasını gerektirir)", "Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Updates available!": "<PERSON><PERSON><PERSON><PERSON>meler mevcut!", "Updates for this package are ignored": "<PERSON>u paketin gü<PERSON><PERSON>eri yoksayıldı", "Updates found!": "G<PERSON><PERSON>llemeler bulundu!", "Updates preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updating WingetUI": "WingetUI'ı güncelle", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "PowerShell CMDlet'ler yerine eski paketlenmiş WinGet'i kullanın", "Use a custom icon and screenshot database URL": "Özel bir simge ve ekran görüntüsü veritabanı URL'si kullanın", "Use bundled WinGet instead of PowerShell CMDlets": "PowerShell CMDlet'leri yerine paketlenmiş WinGet'i kullanın", "Use bundled WinGet instead of system WinGet": "Sistem WinGet yerine paketlenmiş WinGet'i kullanın", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Paketlenmiş olan yerine yüklü GSudo'yu kull<PERSON>n", "Use system Chocolatey": "<PERSON><PERSON> siste<PERSON> k<PERSON>n", "Use system Chocolatey (Needs a restart)": "Sistem Chocolatey'ini <PERSON> (Yeniden başlatma gerektirir)", "Use system Winget (Needs a restart)": "Sistem Winget'ini kull<PERSON> (Yeniden başlatma gerektirir)", "Use system Winget (System language must be set to english)": "Winget sistemini kullanın (Sistem dili İngilizce olarak ayarlanmalıdır)", "Use the WinGet COM API to fetch packages": "Paketleri getirmek için WinGet COM API'sini kullanın", "Use the WinGet PowerShell Module instead of the WinGet COM API": "WinGet COM API yerine WinGet PowerShell Modülünü kullanın", "Useful links": "Faydalı bağlantılar", "User": "Kullanıcı", "User interface preferences": "Kullanıcı arayüzü tercihleri", "User | Local": "User | Local", "Username": "Kullanıcı adı", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "WingetUI kullanmak, GNU Kısıtlı Genel Kamu Lisansı v2.1 Lisansının kabul edildiği anlamına gelir", "Using WingetUI implies the acceptation of the MIT License": "WingetUI'yi kullanmak MIT Lisansının kabul edildiği anlamına gelir", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg kökü bulunamadı. Lütfen %VCPKG_ROOT% ortam değişkenini tanımlayın veya bunu UniGetUI Ayarlarından tanımlayın", "Vcpkg was not found on your system.": "Vcpkg sisteminizde bulunamadı.", "Verbose": "Ayrıntılı", "Version": "S<PERSON>r<PERSON><PERSON>", "Version to install:": "Yüklenecek sürüm:", "Version:": null, "View GitHub Profile": "GitHub Profili", "View WingetUI on GitHub": "WingetUI'yi G<PERSON>'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "WingetUI'nin kaynak kodunu görüntüle. Buradan, hataları bildirebilir veya özellikler önerebilir, hatta WingetUI Projesine doğrudan katkıda bulunabilirsiniz", "View mode:": "G<PERSON>rünt<PERSON>leme modu:", "View on UniGetUI": "UniGetUI'de göster", "View page on browser": "Sayfayı tarayıcıda gö<PERSON>ü<PERSON>üle", "View {0} logs": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "İnternet bağlantısı gerektiren görevleri yapmaya başlamadan önce cihazın internete bağlanmasını bekleyin.", "Waiting for other installations to finish...": "<PERSON><PERSON><PERSON> yü<PERSON><PERSON><PERSON>n tamamlanması bekleniyor...", "Waiting for {0} to complete...": "{0} <PERSON><PERSON><PERSON><PERSON> i<PERSON> be<PERSON>.", "Warning": "Uyarı", "Warning!": "Uyarı!", "We are checking for updates.": "Güncellemeleri kontrol ediyoruz.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Paket kaynaklarınızda bulunmadığı için bu paket hakkında ayrıntılı bilgi yükleyemedik.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Mevcut bir paket yöneticisinden yüklenmediği için bu paket hakkında ayrıntılı bilgi yükleyemedik.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "{action} işlemi {package} i<PERSON>in gerçekleştirilemedi. Lütfen daha sonra tekrar deneyin. Yükleyiciden günlükleri almak için \"{showDetails}\" seçeneğine tıklayın.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "{action} işlemi {package} i<PERSON>in gerçekleştirilemedi. Lütfen daha sonra tekrar deneyin. Kaldırıcı günlüklerini almak için \"{showDetails}\" seçeneğine tıklayın.", "We couldn't find any package": "<PERSON><PERSON><PERSON> bir paket bula<PERSON>k", "Welcome to WingetUI": "WingetUI'a hoş geldiniz", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON>ni kısayollar algılandığında, bu iletişim kutusunu göstermek yerine bunları otomatik olarak silin.", "Which backup do you want to open?": null, "Which package managers do you want to use?": "Hangi paket yöneticilerini kullanmak istiyorsunuz?", "Which source do you want to add?": "Hangi kaynağı eklemek istiyorsunuz?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Winget WingetUI içinde kullanılabilirken, WingetUI diğer paket yöneticileriyle birlikte kullanılabilir, bu da kafa karıştırıcı olabilir. Geçmişte, WingetUI yalnızca Winget ile çalışmak üzere tasarlandı, ancak bu artık doğru değil ve bu nedenle WingetUI bu projenin olmayı hedeflediği şeyi temsil etmiyor.", "WinGet could not be repaired": "WinGet onarılamadı", "WinGet malfunction detected": "WinGet arızası tespit edildi", "WinGet was repaired successfully": "WinGet başarıyla onarıldı", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - Her şey g<PERSON>l", "WingetUI - {0} updates are available": "WingetUI - {0} gü<PERSON>lleme mevcut", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI Ana Sayfası", "WingetUI Homepage - Share this link!": "WingetUI Ana Sayfası - Bu bağlantıyı paylaşın!", "WingetUI License": "WingetUI Lisansı", "WingetUI Log": "WingetUI Günlüğü", "WingetUI Repository": "WingetUI Deposu", "WingetUI Settings": "WingetUI Ayarları", "WingetUI Settings File": "WingetUI Ayarlar Dosyası", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI Aşağıdaki kütüphaneleri kullanır. <PERSON><PERSON>, WingetUI mümkün olmazdı.", "WingetUI Version {0}": "WingetUI Sürüm {0}", "WingetUI autostart behaviour, application launch settings": "WingetUI otomatik başlatma seçenekleri ve uygulama başlatma ayarları", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI, yazılımınızda kullanılabilir güncellemeler olup olmadığını kontrol edebilir ve isterseniz bunları otomatik olarak yükleyebilir", "WingetUI display language:": "WingetUI arayüz dili:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI yönetici olarak çalıştırıldı, bu önerilmez. WingetUI'yi yönetici olarak çalıştırırken, wi̇ngetui'den başlatilan HER işlem yönetici ayrıcalıklarına sahip olacaktır. Programı yine de kullanabilirsiniz, ancak WingetUI'yi yönetici ayrıcalıklarıyla çalıştırmamanızı şiddetle tavsiye ederiz.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI, gönüllü çevirmenler sayesinde 40 'tan fazla dile çevrildi. Teşekkürler 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI makine tarafından çevrilmemiştir. Çevirilerden aşağıdaki kullanıcılar sorumludur:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI, komut satırı paket yöneticileriniz için hepsi bir arada grafik arayüzü sağlayarak yazılımınızı yönetmeyi kolaylaştıran bir uygulamadır.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI (şu anda kullandığınız arayüz) ve Winget (Microsoft tarafından geliştirilen ve benim ilişkim olmayan bir paket yöneticisi) arasındaki farkı vurgulamak için WingetUI yeniden adlandırılıyor", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI güncelleniyor. <PERSON><PERSON><PERSON>, WingetUI kendini yeniden başlatacak", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI ücretsizdir ve sonsuza kadar ücretsiz olacaktır. Reklam yok, kredi kartı yok, premium sürüm yok. Sonsuza kadar % 100 ücretsiz.", "WingetUI log": "WingetUI günlüğü", "WingetUI tray application preferences": "WingetUI bildirim çubuğu tercihleri", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI Aşağıdaki kütüphaneleri kullanır. <PERSON><PERSON>, WingetUI mümkün olmazdı.", "WingetUI version {0} is being downloaded.": "WingetUI {0} sürümü indiriliyor.", "WingetUI will become {newname} soon!": "WingetUI yakında {newname} olacak!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI güncellemeleri periyodik olarak kontrol etmeyecek. Uygulama açılışında kontrol edilecek, ancak onlar hakkında uyarılmayacaksınız.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI, her<PERSON>i bir paketin yüklenmesi için UAC gerektirdiği du<PERSON>, her seferinde bir UAC istemi gösterecektir.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI yakında {newname} olarak adlandırılacak. Bu, uygulamada herhangi bir değişikliği temsil etmeyecektir. Ben (geliştirici) şu anda yaptığım gibi bu projenin geliştirilmesine farklı bir isimle devam edeceğim.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI, değerli katılımcılarımızın yardımıyla geliştirildi. GitHub profillerine göz atın, WingetUI onlarsız mümkün olmazdı!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "Katkıda bulunanların yardımı olmadan WingetUI mümkün olmazdı. Hepinize teşekkür ederim 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} yüklenmeye hazır.", "Write here the process names here, separated by commas (,)": null, "Yes": "<PERSON><PERSON>", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "<PERSON><PERSON> anda {0} s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "You have installed WingetUI Version {0}": "WingetUI {0} Sürümünü yü<PERSON>z", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "WingetUI ile kullanmak için {pm} yüklemeniz gerekebilir.", "You may restart your computer later if you wish": "İsterseniz bilgisayarınızı daha sonra da yeniden başlatabilirsiniz", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Yalnızca bir kez uyarılacaksınız ve yönetici hakları talep eden tüm paketlere yönetici ayrıcalıkları verilecek.", "You will be prompted only once, and every future installation will be elevated automatically.": "Yalnızca bir kez uyarılacaksınız ve gelecekteki tüm yüklemeler yönetici ayrıcalıklarıyla gerçekleştirilecek.", "You will likely need to interact with the installer.": "Muhtemelen yükleyiciyle etkileşime girmeniz gerekecektir.", "[RAN AS ADMINISTRATOR]": "YÖNETİCİ OLARAK ÇALIŞTIR", "buy me a coffee": "<PERSON><PERSON> kahve <PERSON>", "extracted": "Çıkarıldı", "feature": "özellik", "formerly WingetUI": "eski adıyla WingetUI", "homepage": "İnternet sitesi", "install": "<PERSON><PERSON><PERSON>", "installation": "<PERSON><PERSON><PERSON>", "installed": "yüklü", "installing": "Yükleniyor", "library": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mandatory": null, "option": "seçenek", "optional": null, "uninstall": "kaldır", "uninstallation": "kaldı<PERSON> i<PERSON>lemi", "uninstalled": "kaldırıldı", "uninstalling": "kaldırılıyor", "update(noun)": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update(verb)": "<PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updating": "Güncelleniyor", "version {0}": "s<PERSON><PERSON><PERSON><PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} Ka<PERSON><PERSON>rma <PERSON>i", "{0} aborted": "{0} iptal edildi", "{0} can be updated": "{0} güncelleneb<PERSON>r", "{0} can be updated to version {1}": "{0}, {1} s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> güncellenebilir", "{0} days": "{0} gün", "{0} desktop shortcuts created": "Masaüstünde {0} kısayol oluşturuldu", "{0} failed": "{0} hatalı", "{0} has been installed successfully.": "{0} b<PERSON><PERSON><PERSON><PERSON><PERSON> kuruldu.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} başarıyla kuruldu. Kurulumu tamamlamak için UniGetUI'nin yeniden başlatılması önerilir.", "{0} has failed, that was a requirement for {1} to be run": "{0} başarısız oldu ve {1} çalıştırılmak için bir gereklilikti", "{0} homepage": "{0} ana sayfa", "{0} hours": "{0} saat", "{0} installation": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} installation options": "{0} <PERSON><PERSON><PERSON>", "{0} installer is being downloaded": "{0}  y<PERSON>kleyici indiriliyor", "{0} is being installed": "{0} kuruluyor", "{0} is being uninstalled": "{0} kaldırılıyor", "{0} is being updated": "{0} güncelleniyor...", "{0} is being updated to version {1}": "{0}, {1} s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> güncelleniyor", "{0} is disabled": "{0} devre dışı", "{0} minutes": "{0} da<PERSON><PERSON>", "{0} months": "{0} ay", "{0} packages are being updated": "{0} paket g<PERSON>", "{0} packages can be updated": "{0} paket g<PERSON>ebili<PERSON>", "{0} packages found": "{0} paket bulundu", "{0} packages were found": "{0} paket bulundu", "{0} packages were found, {1} of which match the specified filters.": "{1} tanesi belirtilen filtrelerle eşleşen {0} paket bulundu.", "{0} settings": "{0} a<PERSON><PERSON>", "{0} status": "{0} durum", "{0} succeeded": "{0} başarılı", "{0} update": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} updates are available": "{0} g<PERSON><PERSON><PERSON><PERSON><PERSON> mevcut", "{0} was {1} successfully!": "{0} pakette {1} başarılı!", "{0} weeks": "{0} hafta", "{0} years": "{0} yıl", "{0} {1} failed": "{0} {1} başarısız", "{package} Installation": "{package} Yükleniyor", "{package} Uninstall": "{package} Kaldır", "{package} Update": "{package} Güncelleştir", "{package} could not be installed": "{package} yüklenemedi", "{package} could not be uninstalled": "{package} kaldırılamadı", "{package} could not be updated": "{package} güncellenemedi", "{package} installation failed": "{package} kuru<PERSON>u başarısız oldu", "{package} installer could not be downloaded": "{package} y<PERSON>kleyici indirilemedi", "{package} installer download": "{package} yükleyi̇ci̇ i̇ndi̇r", "{package} installer was downloaded successfully": "{package} y<PERSON><PERSON>ici başarıyla indirildi", "{package} uninstall failed": "{package} kaldırılamadı", "{package} update failed": "{package} güncellemesi başarısız oldu", "{package} update failed. Click here for more details.": "{package} güncellemesi başarısız oldu. Daha fazla bilgi için buraya tıklayın.", "{package} was installed successfully": "{package} başarıyla yü<PERSON>ndi", "{package} was uninstalled successfully": "{package} başarıyla kaldırıldı", "{package} was updated successfully": "{package} başar<PERSON><PERSON>", "{pcName} installed packages": "{pcName} y<PERSON><PERSON><PERSON><PERSON> paketler", "{pm} could not be found": "{pm} bulunamadı", "{pm} found: {state}": "{pm} şurada bulundu: {state}", "{pm} is disabled": "{pm} devre dışı", "{pm} is enabled and ready to go": "{pm} etkinleş<PERSON>rildi ve kullanıma hazır", "{pm} package manager specific preferences": "{pm} paket yöneticisine özel tercihler", "{pm} preferences": "{pm} terc<PERSON><PERSON>i", "{pm} version:": "{pm} sür<PERSON><PERSON>ü:", "{pm} was not found!": "{pm} bulunamadı!"}