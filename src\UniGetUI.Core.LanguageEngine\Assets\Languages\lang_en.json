{"\"{0}\" is a local package and can't be shared": "\"{0}\" is a local package and can't be shared", "\"{0}\" is a local package and does not have available details": "\"{0}\" is a local package and does not have available details", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" is a local package and is not compatible with this feature", "(Last checked: {0})": "(Last checked: {0})", "(Number {0} in the queue)": "(Number {0} in the queue)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@marticliment, @ppvnf, @lucadsign", "0 packages found": "0 packages found", "0 updates found": "0 updates found", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "a day", "1 hour": "an hour", "1 month": "a month", "1 package was found": "1 package was found", "1 update is available": "1 update is available", "1 week": "1 week", "1 year": "1 year", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Navigate to the \"{0}\" or \"{1}\" page.", "2 - Warnings": "2 - Warnings", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.", "3 - Information (less)": "3 - Information (less)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.", "4 - Information (more)": "4 - Information (more)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.", "5 - information (debug)": "5 - Information (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>", "A restart is required": "A restart is required", "Abort install if pre-install command fails": "Abort install if pre-install command fails", "Abort uninstall if pre-uninstall command fails": "Abort uninstall if pre-uninstall command fails", "Abort update if pre-update command fails": "Abort update if pre-update command fails", "About": "About", "About Qt6": "About Qt6", "About WingetUI": "About UniGetUI", "About WingetUI version {0}": "About UniGetUI version {0}", "About the dev": "About the dev", "Accept": "Accept", "Action when double-clicking packages, hide successful installations": "Action when double-clicking packages, hide successful installations", "Add": "Add", "Add a source to {0}": "Add a source to {0}", "Add a timestamp to the backup file names": "Add a timestamp to the backup file names", "Add a timestamp to the backup files": "Add a timestamp to the backup files", "Add packages or open an existing bundle": "Add packages or open an existing bundle", "Add packages or open an existing package bundle": "Add packages or open an existing package bundle", "Add packages to bundle": "Add packages to bundle", "Add packages to start": "Add packages to start", "Add selection to bundle": "Add selection to bundle", "Add source": "Add source", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Add updates that fail with 'no applicable update found' to the ignored updates list.", "Adding source {source}": "Adding source {source}", "Adding source {source} to {manager}": "Adding source {source} to {manager}", "Addition succeeded": "Addition succeeded", "Administrator privileges": "Administrator privileges", "Administrator privileges preferences": "Administrator privileges preferences", "Administrator rights": "Administrator rights", "Administrator rights and other dangerous settings": "Administrator rights and other dangerous settings", "Advanced options": "Advanced options", "All files": "All files", "All versions": "All versions", "Allow changing the paths for package manager executables": "Allow changing the paths for package manager executables", "Allow custom command-line arguments": "Allow custom command-line arguments", "Allow importing custom command-line arguments when importing packages from a bundle": "Allow importing custom command-line arguments when importing packages from a bundle", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Allow importing custom pre-install and post-install commands when importing packages from a bundle", "Allow package operations to be performed in parallel": "Allow package operations to be performed in parallel", "Allow parallel installs (NOT RECOMMENDED)": "Allow parallel installs (NOT RECOMMENDED)", "Allow pre-release versions": "Allow pre-release versions", "Allow {pm} operations to be performed in parallel": "Allow {pm} operations to be performed in parallel", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:", "Always elevate {pm} installations by default": "Always elevate {pm} installations by default", "Always run {pm} operations with administrator rights": "Always run {pm} operations with administrator rights", "An error occurred": "An error occurred", "An error occurred when adding the source: ": "An error occurred when adding the source: ", "An error occurred when attempting to show the package with Id {0}": "An error occurred when attempting to show the package with Id {0}", "An error occurred when checking for updates: ": "An error occurred when checking for updates: ", "An error occurred while attempting to create an installation script:": "An error occurred while attempting to create an installation script:", "An error occurred while loading a backup: ": "An error occurred while loading a backup: ", "An error occurred while logging in: ": "An error occurred while logging in: ", "An error occurred while processing this package": "An error occurred while processing this package", "An error occurred:": "An error occurred:", "An interal error occurred. Please view the log for further details.": "An internal error occurred. Please view the log for further details.", "An unexpected error occurred:": "An unexpected error occurred:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "An unexpected issue occurred while attempting to repair WinGet. Please try again later", "An update was found!": "An update was found!", "Android Subsystem": "Android Subsystem", "Another source": "Another source", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Any new shortcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.", "Any unsaved changes will be lost": "Any unsaved changes will be lost", "App Name": "App Name", "Appearance": "Appearance", "Application theme, startup page, package icons, clear successful installs automatically": "Application theme, startup page, package icons, clear successful installs automatically", "Application theme:": "Application theme:", "Apply": "Apply", "Architecture to install:": "Architecture to install:", "Are these screenshots wron or blurry?": "Are these screenshots wrong or blurry?", "Are you really sure you want to enable this feature?": "Are you really sure you want to enable this feature?", "Are you sure you want to create a new package bundle? ": "Are you sure you want to create a new package bundle? ", "Are you sure you want to delete all shortcuts?": "Are you sure you want to delete all shortcuts?", "Are you sure?": "Are you sure?", "Ascendant": "Ascendant", "Ask for administrator privileges once for each batch of operations": "Ask for administrator privileges once for each batch of operations", "Ask for administrator rights when required": "Ask for administrator rights when required", "Ask once or always for administrator rights, elevate installations by default": "Ask once or always for administrator rights, elevate installations by default", "Ask only once for administrator privileges": "Ask only once for administrator privileges", "Ask only once for administrator privileges (not recommended)": "Ask only once for administrator privileges (not recommended)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Ask to delete desktop shortcuts created during an install or upgrade.", "Attention required": "Attention required", "Authenticate to the proxy with an user and a password": "Authenticate to the proxy with an user and a password", "Author": "Author", "Automatic desktop shortcut remover": "Automatic desktop shortcut remover", "Automatic updates": "Automatic updates", "Automatically save a list of all your installed packages to easily restore them.": "Automatically save a list of all your installed packages to easily restore them.", "Automatically save a list of your installed packages on your computer.": "Automatically save a list of your installed packages on your computer.", "Autostart WingetUI in the notifications area": "Autostart UniGetUI in the notifications area", "Available Updates": "Available Updates", "Available updates: {0}": "Available updates: {0}", "Available updates: {0}, not finished yet...": "Available updates: {0}, not finished yet...", "Backing up packages to GitHub Gist...": "Backing up packages to GitHub Gist...", "Backup": "Backup", "Backup Failed": "Backup Failed", "Backup Successful": "Backup successful", "Backup and Restore": "Backup and Restore", "Backup installed packages": "Backup installed packages", "Backup location": "Backup location", "Become a contributor": "Become a contributor", "Become a translator": "Become a translator", "Begin the process to select a cloud backup and review which packages to restore": "Begin the process to select a cloud backup and review which packages to restore", "Beta features and other options that shouldn't be touched": "Beta features and other options that shouldn't be touched", "Both": "Both", "Bundle security report": "Bundle security report", "But here are other things you can do to learn about WingetUI even more:": "But here are other things you can do to learn about UniGetUI even more:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "By toggling a package manager off, you will no longer be able to see or update its packages.", "Cache administrator rights and elevate installers by default": "Cache administrator rights and elevate installers by default", "Cache administrator rights, but elevate installers only when required": "Cache administrator rights, but elevate installers only when required", "Cache was reset successfully!": "C<PERSON> was reset successfully!", "Can't {0} {1}": "Can't {0} {1}", "Cancel": "Cancel", "Cancel all operations": "Cancel all operations", "Change backup output directory": "Change backup output directory", "Change default options": "Change default options", "Change how UniGetUI checks and installs available updates for your packages": "Change how UniGetUI checks and installs available updates for your packages", "Change how UniGetUI handles install, update and uninstall operations.": "Change how UniGetUI handles install, update and uninstall operations.", "Change how UniGetUI installs packages, and checks and installs available updates": "Change how UniGetUI installs packages, and checks and installs available updates", "Change how operations request administrator rights": "Change how operations request administrator rights", "Change install location": "Change install location", "Change this": "Change this", "Change this and unlock": "Change this and unlock", "Check for package updates periodically": "Check for package updates periodically", "Check for updates": "Check for updates", "Check for updates every:": "Check for updates every:", "Check for updates periodically": "Check for updates periodically.", "Check for updates regularly, and ask me what to do when updates are found.": "Check for updates regularly, and ask me what to do when updates are found.", "Check for updates regularly, and automatically install available ones.": "Check for updates regularly, and automatically install available ones.", "Check out my {0} and my {1}!": "Check out my {0} and my {1}!", "Check out some WingetUI overviews": "Check out some UniGetUI reviews", "Checking for other running instances...": "Checking for other running instances...", "Checking for updates...": "Checking for updates...", "Checking found instace(s)...": "Checking found instance(s)...", "Choose how many operations shouls be performed in parallel": "Choose how many operations should be performed in parallel", "Clear cache": "Clear cache", "Clear finished operations": "Clear finished operations", "Clear selection": "Clear selection", "Clear successful operations": "Clear successful operations", "Clear successful operations from the operation list after a 5 second delay": "Clear successful operations from the operation list after a 5 second delay", "Clear the local icon cache": "Clear the local icon cache", "Clearing Scoop cache - WingetUI": "Clearing Scoop cache - UniGetUI", "Clearing Scoop cache...": "Clearing Scoop cache...", "Click here for more details": "Click here for more details", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.", "Close": "Close", "Close UniGetUI to the system tray": "Close UniGetUI to the system tray", "Close WingetUI to the notification area": "Close UniGetUI to the notification area", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Cloud backup uses a private GitHub Gist to store a list of installed packages", "Cloud package backup": "Cloud package backup", "Command-line Output": "Command-line Output", "Command-line to run:": "Command-line to run:", "Compare query against": "Compare query against", "Compatible with authentication": "Compatible with authentication", "Compatible with proxy": "Compatible with proxy", "Component Information": "Component Information", "Concurrency and execution": "Concurrency and execution", "Connect the internet using a custom proxy": "Connect the internet using a custom proxy", "Continue": "Continue", "Contribute to the icon and screenshot repository": "Contribute to the icon and screenshot repository", "Contributors": "Contributors", "Copy": "Copy", "Copy to clipboard": "Copy to clipboard", "Could not add source": "Could not add source", "Could not add source {source} to {manager}": "Could not add source {source} to {manager}", "Could not back up packages to GitHub Gist: ": "Could not back up packages to GitHub Gist: ", "Could not create bundle": "Could not create bundle", "Could not load announcements - ": "Could not load announcements - ", "Could not load announcements - HTTP status code is $CODE": "Could not load announcements - HTTP status code is $CODE", "Could not remove source": "Could not remove source", "Could not remove source {source} from {manager}": "Could not remove source {source} from {manager}", "Could not remove {source} from {manager}": "Could not remove {source} from {manager}", "Create .ps1 script": "Create .ps1 script", "Credentials": "Credentials", "Current Version": "Current Version", "Current status: Not logged in": "Current status: Not logged in", "Current user": "Current user", "Custom arguments:": "Custom arguments:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.", "Custom command-line arguments:": "Custom command-line arguments:", "Custom install arguments:": "Custom install arguments:", "Custom uninstall arguments:": "Custom uninstall arguments:", "Custom update arguments:": "Custom update arguments:", "Customize WingetUI - for hackers and advanced users only": "Customize UniGetUI - for hackers and advanced users only", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "DISCLAIMER: WE ARE NOT R<PERSON><PERSON><PERSON><PERSON><PERSON> FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.", "Dark": "Dark", "Decline": "Decline", "Default": "<PERSON><PERSON><PERSON>", "Default installation options for {0} packages": "Default installation options for {0} packages", "Default preferences - suitable for regular users": "Default preferences - suitable for regular users", "Default vcpkg triplet": "Default vcpkg triplet", "Delete?": "Delete?", "Dependencies:": "Dependencies:", "Descendant": "Descendant", "Description:": "Description:", "Desktop shortcut created": "Desktop shortcut created", "Details of the report:": "Details of the report:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Developing is hard, and this application is free. However, if you found the application helpful, you can always <b>buy me a coffee</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)", "Disable new share API (port 7058)": "Disable new share API (port 7058)", "Disable the 1-minute timeout for package-related operations": "Disable the 1-minute timeout for package-related operations", "Disclaimer": "Disclaimer", "Discover Packages": "Discover Packages", "Discover packages": "Discover packages", "Distinguish between\nuppercase and lowercase": "Distinguish between \nuppercase and lowercase", "Distinguish between uppercase and lowercase": "Distinguish between uppercase and lowercase", "Do NOT check for updates": "Do NOT check for updates", "Do an interactive install for the selected packages": "Do an interactive install for the selected packages", "Do an interactive uninstall for the selected packages": "Do an interactive uninstall for the selected packages", "Do an interactive update for the selected packages": "Do an interactive update for the selected packages", "Do not automatically install updates when the battery saver is on": "Do not automatically install updates when the battery saver is on", "Do not automatically install updates when the network connection is metered": "Do not automatically install updates when the network connection is metered", "Do not download new app translations from GitHub automatically": "Do not download new app translations from GitHub automatically", "Do not ignore updates for this package anymore": "Do not ignore updates for this package anymore", "Do not remove successful operations from the list automatically": "Do not remove successful operations from the list automatically", "Do not show this dialog again for {0}": "Do not show this dialog again for {0}", "Do not update package indexes on launch": "Do not update package indexes on launch", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Do you find UniGetUI useful? If you can, you may want to support my work, so I can continue making UniGetUI the ultimate package managing interface.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Do you find UniGetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!", "Do you really want to reset this list? This action cannot be reverted.": "Do you really want to reset this list? This action cannot be reverted.", "Do you really want to uninstall the following {0} packages?": "Do you really want to uninstall the following {0} packages?", "Do you really want to uninstall {0} packages?": "Do you really want to uninstall {0} packages?", "Do you really want to uninstall {0}?": "Do you really want to uninstall {0}?", "Do you want to restart your computer now?": "Do you want to restart your computer now?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Do you want to translate UniGetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Don't feel like donating? Don't worry, you can always share UniGetUI with your friends. Spread the word about UniGetUI.", "Donate": "Donate", "Done!": "Done!", "Download failed": "Download failed", "Download installer": "Download installer", "Download operations are not affected by this setting": "Download operations are not affected by this setting", "Download selected installers": "Download selected installers", "Download succeeded": "Download succeeded", "Download updated language files from GitHub automatically": "Download updated language files from GitHub automatically", "Downloading": "Downloading", "Downloading backup...": "Downloading backup...", "Downloading installer for {package}": "Downloading installer for {package}", "Downloading package metadata...": "Downloading package metadata...", "Enable Scoop cleanup on launch": "Enable Scoop cleanup on launch", "Enable WingetUI notifications": "Enable UniGetUI notifications", "Enable an [experimental] improved WinGet troubleshooter": "Enable an [experimental] improved WinGet troubleshooter", "Enable and disable package managers, change default install options, etc.": "Enable and disable package managers, change default install options, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Enable background CPU usage optimizations (see Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Enable background API (Widgets for UniGetUI and Sharing, port 7058)", "Enable it to install packages from {pm}.": "Enable it to install packages from {pm}.", "Enable the automatic WinGet troubleshooter": "Enable the automatic WinGet troubleshooter", "Enable the new UniGetUI-Branded UAC Elevator": "Enable the new UniGetUI-Branded UAC Elevator", "Enable the new process input handler (StdIn automated closer)": "Enable the new process input handler (StdIn automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Enable the settings below IF AND ONLY IF you fully understand what they do, and the implications and dangers they may involve.", "Enable {pm}": "Enable {pm}", "Enter proxy URL here": "Enter proxy URL here", "Entries that show in RED will be IMPORTED.": "Entries that show in RED will be IMPORTED.", "Entries that show in YELLOW will be IGNORED.": "Entries that show in YELLOW will be IGNORED.", "Error": "Error", "Everything is up to date": "Everything is up to date", "Exact match": "Exact match", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.", "Expand version": "Expand version", "Experimental settings and developer options": "Experimental settings and developer options", "Export": "Export", "Export log as a file": "Export log as a file", "Export packages": "Export packages", "Export selected packages to a file": "Export selected packages to a file", "Export settings to a local file": "Export settings to a local file", "Export to a file": "Export to a file", "Failed": "Failed", "Fetching available backups...": "Fetching available backups...", "Fetching latest announcements, please wait...": "Fetching latest announcements, please wait...", "Filters": "Filters", "Finish": "Finish", "Follow system color scheme": "Follow system color scheme", "Follow the default options when installing, upgrading or uninstalling this package": "Follow the default options when installing, upgrading or uninstalling this package", "For security reasons, changing the executable file is disabled by default": "For security reasons, changing the executable file is disabled by default", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)", "Formerly known as WingetUI": "Formerly known as WingetUI", "Found": "Found", "Found packages: ": "Found packages: ", "Found packages: {0}": "Found packages: {0}", "Found packages: {0}, not finished yet...": "Found packages: {0}, not finished yet...", "General preferences": "General preferences", "GitHub profile": "GitHub profile", "Global": "Global", "Go to UniGetUI security settings": "Go to UniGetUI security settings", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>", "Great! You are on the latest version.": "Great! You are on the latest version.", "Grid": "Grid", "Help": "Help", "Help and documentation": "Help and documentation", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if gets created on a future upgrade. Unchecking it will keep the shortcut intact", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Hi, my name is <PERSON><PERSON>, and i am the <i>developer</i> of UniGetUI. UniGetUI has been entirely made on my free time!", "Hide details": "Hide details", "Homepage": "Homepage", "Hooray! No updates were found.": "Hooray! No updates were found.", "How should installations that require administrator privileges be treated?": "How should installations that require administrator privileges be treated?", "How to add packages to a bundle": "How to add packages to a bundle", "I understand": "I understand", "Icons": "Icons", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignore custom pre-install and post-install commands when importing packages from a bundle", "Ignore future updates for this package": "Ignore future updates for this package", "Ignore packages from {pm} when showing a notification about updates": "Ignore packages from {pm} when showing a notification about updates", "Ignore selected packages": "Ignore selected packages", "Ignore special characters": "Ignore special characters", "Ignore updates for the selected packages": "Ignore updates for the selected packages", "Ignore updates for this package": "Ignore updates for this package", "Ignored updates": "Ignored updates", "Ignored version": "Ignored version", "Import": "Import", "Import packages": "Import packages", "Import packages from a file": "Import packages from a file", "Import settings from a local file": "Import settings from a local file", "In order to add packages to a bundle, you will need to: ": "In order to add packages to a bundle, you will need to: ", "Initializing WingetUI...": "Initializing UniGetUI...", "Install": "Install", "Install Scoop": "Install Scoop", "Install and more": "Install and more", "Install and update preferences": "Install and update preferences", "Install as administrator": "<PERSON><PERSON><PERSON> as administrator", "Install available updates automatically": "Install available updates automatically", "Install location can't be changed for {0} packages": "Install location can't be changed for {0} packages", "Install location:": "Install location:", "Install options": "Install options", "Install packages from a file": "Install packages from a file", "Install prerelease versions of UniGetUI": "Install prerelease versions of UniGetUI", "Install script": "Install script", "Install selected packages": "Install selected packages", "Install selected packages with administrator privileges": "Install selected packages with administrator privileges", "Install selection": "Install selection", "Install the latest prerelease version": "Install the latest prerelease version", "Install updates automatically": "Install updates automatically", "Install {0}": "Install {0}", "Installation canceled by the user!": "Installation canceled by the user!", "Installation failed": "Installation failed", "Installation options": "Installation options", "Installation scope:": "Installation scope:", "Installation succeeded": "Installation succeeded", "Installed Packages": "Installed Packages", "Installed Version": "Installed Version", "Installed packages": "Installed packages", "Installer SHA256": "Installer SHA256", "Installer SHA512": "Installer SHA512", "Installer Type": "Installer Type", "Installer URL": "Installer URL", "Installer not available": "Installer not available", "Instance {0} responded, quitting...": "Instance {0} responded, quitting...", "Instant search": "Instant search", "Integrity checks can be disabled from the Experimental Settings": "Integrity checks can be disabled from the Experimental Settings", "Integrity checks skipped": "Integrity checks skipped", "Integrity checks will not be performed during this operation": "Integrity checks will not be performed during this operation", "Interactive installation": "Interactive installation", "Interactive operation": "Interactive operation", "Interactive uninstall": "Interactive uninstall", "Interactive update": "Interactive update", "Internet connection settings": "Internet connection settings", "Is this package missing the icon?": "Is this package missing the icon?", "Is your language missing or incomplete?": "Is your language missing or incomplete?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account", "It is recommended to restart UniGetUI after WinGet has been repaired": "It is recommended to restart UniGetUI after WinGet has been repaired", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "It is strongly recommended to reinstall UniGetUI to adress the situation.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "It looks like you ran UniGetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running UniGetUI with administrator privileges. <PERSON>lick on \"{showDetails}\" to see why.", "Language": "Language", "Language, theme and other miscellaneous preferences": "Language, theme and other miscellaneous preferences", "Last updated:": "Last updated:", "Latest": "Latest", "Latest Version": "Latest Version", "Latest Version:": "Latest Version:", "Latest details...": "Latest details...", "Launching subprocess...": "Launching subprocess...", "Leave empty for default": "Leave empty for default", "License": "License", "Licenses": "Licenses", "Light": "Light", "List": "List", "Live command-line output": "Live command-line output", "Live output": "Live output", "Loading UI components...": "Loading UI components...", "Loading WingetUI...": "Loading UniGetUI...", "Loading packages": "Loading packages", "Loading packages, please wait...": "Loading packages, please wait...", "Loading...": "Loading...", "Local": "Local", "Local PC": "Local PC", "Local backup advanced options": "Local backup advanced options", "Local machine": "Local machine", "Local package backup": "Local package backup", "Locating {pm}...": "Locating {pm}...", "Log in": "Log in", "Log in failed: ": "Log in failed: ", "Log in to enable cloud backup": "Log in to enable cloud backup", "Log in with GitHub": "Log in with GitHub", "Log in with GitHub to enable cloud package backup.": "Log in with GitHub to enable cloud package backup.", "Log level:": "Log level:", "Log out": "Log out", "Log out failed: ": "Log out failed: ", "Log out from GitHub": "Log out from GitHub", "Looking for packages...": "Looking for packages...", "Machine | Global": "Machine | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default", "Manage": "Manage", "Manage UniGetUI settings": "Manage UniGetUI settings", "Manage WingetUI autostart behaviour from the Settings app": "Manage UniGetUI autostart behaviour from the Settings app", "Manage ignored packages": "Manage ignored packages", "Manage ignored updates": "Manage ignored updates", "Manage shortcuts": "Manage shortcuts", "Manage telemetry settings": "Manage telemetry settings", "Manage {0} sources": "Manage {0} sources", "Manifest": "Manifest", "Manifests": "Manifests", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>", "Missing dependency": "Missing dependency", "More": "More", "More details": "More details", "More details about the shared data and how it will be processed": "More details about the shared data and how it will be processed", "More info": "More info", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section", "Name": "Name", "New": "New", "New Version": "New Version", "New bundle": "New bundle", "New version": "New version", "Nice! Backups will be uploaded to a private gist on your account": "Nice! Backups will be uploaded to a private gist on your account", "No": "No", "No applicable installer was found for the package {0}": "No applicable installer was found for the package {0}", "No dependencies specified": "No dependencies specified", "No new shortcuts were found during the scan.": null, "No packages found": "No packages found", "No packages found matching the input criteria": "No packages found matching the input criteria", "No packages have been added yet": "No packages have been added yet", "No packages selected": "No packages selected", "No packages were found": "No packages were found", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.", "No results were found matching the input criteria": "No results were found matching the input criteria", "No sources found": "No sources found", "No sources were found": "No sources were found", "No updates are available": "No updates are available", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>", "Not available": "Not available", "Not finding the file you are looking for? Make sure it has been added to path.": "Not finding the file you are looking for? Make sure it has been added to path.", "Not found": "Not found", "Not right now": "Not right now", "Notes:": "Notes:", "Notification preferences": "Notification preferences", "Notification tray options": "Notification tray options", "Notification types": "Notification types", "NuPkg (zipped manifest)": "NuPkg (zipped manifest)", "OK": "OK", "Ok": "Ok", "Open": "Open", "Open GitHub": "Open GitHub", "Open UniGetUI": "Open UniGetUI", "Open UniGetUI security settings": "Open UniGetUI security settings", "Open WingetUI": "Open UniGetUI", "Open backup location": "Open backup location", "Open existing bundle": "Open existing bundle", "Open install location": "Open install location", "Open the welcome wizard": "Open the welcome wizard", "Operation canceled by user": "Operation canceled by user", "Operation cancelled": "Operation cancelled", "Operation history": "Operation history", "Operation in progress": "Operation in progress", "Operation on queue (position {0})...": "Operation on queue (position {0})...", "Operation profile:": "Operation profile:", "Options saved": "Options saved", "Order by:": "Order by:", "Other": "Other", "Other settings": "Other settings", "Package": "Package", "Package Bundles": "Package Bundles", "Package ID": "Package ID", "Package Manager": "Package Manager", "Package Manager logs": "Package Manager logs", "Package Managers": "Package Managers", "Package Name": "Package Name", "Package backup": "Package backup", "Package backup settings": "Package backup settings", "Package bundle": "Package bundle", "Package details": "Package details", "Package lists": "Package lists", "Package management made easy": "Package management made easy", "Package manager": "Package manager", "Package manager preferences": "Package managers preferences", "Package managers": "Package managers", "Package not found": "Package not found", "Package operation preferences": "Package operation preferences", "Package update preferences": "Package update preferences", "Package {name} from {manager}": "Package {name} from {manager}", "Package's default": "Package's default", "Packages": "Packages", "Packages found: {0}": "Packages found: {0}", "Partially": "Partially", "Password": "Password", "Paste a valid URL to the database": "Paste a valid URL to the database", "Pause updates for": "Pause updates for", "Perform a backup now": "Perform a backup now", "Perform a cloud backup now": "Perform a cloud backup now", "Perform a local backup now": "Perform a local backup now", "Perform integrity checks at startup": "Perform integrity checks at startup", "Performing backup, please wait...": "Performing backup, please wait...", "Periodically perform a backup of the installed packages": "Periodically perform a backup of the installed packages", "Periodically perform a cloud backup of the installed packages": "Periodically perform a cloud backup of the installed packages", "Periodically perform a local backup of the installed packages": "Periodically perform a local backup of the installed packages", "Please check the installation options for this package and try again": "Please check the installation options for this package and try again", "Please click on \"Continue\" to continue": "Please click on \"Continue\" to continue", "Please enter at least 3 characters": "Please enter at least 3 characters", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.", "Please note that not all package managers may fully support this feature": "Please note that not all package managers may fully support this feature", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.", "Please run UniGetUI as a regular user and try again.": "Please run UniGetUI as a regular user and try again.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Please see the Command-line Output or refer to the Operation History for further information about the issue.", "Please select how you want to configure WingetUI": "Please select how you want to configure UniGetUI", "Please try again later": "Please try again later", "Please type at least two characters": "Please type at least two characters", "Please wait": "Please wait", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Please wait while {0} is being installed. A black (or blue) window may show up. Please wait until it closes.", "Please wait...": "Please wait...", "Portable": "Portable", "Portable mode": null, "Post-install command:": "Post-install command:", "Post-uninstall command:": "Post-uninstall command:", "Post-update command:": "Post-update command:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b><PERSON><PERSON><PERSON>, Scripts, Cmdlets</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully", "Pre-install command:": "Pre-install command:", "Pre-uninstall command:": "Pre-uninstall command:", "Pre-update command:": "Pre-update command:", "PreRelease": "PreRelease", "Preparing packages, please wait...": "Preparing packages, please wait...", "Proceed at your own risk.": "Proceed at your own risk.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo", "Proxy URL": "Proxy URL", "Proxy compatibility table": "Proxy compatibility table", "Proxy settings": "Proxy settings", "Proxy settings, etc.": "Proxy settings, etc.", "Publication date:": "Publication date:", "Publisher": "Publisher", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>", "Quit": "Quit", "Quit WingetUI": "Quit UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Refer to the UniGetUI Logs to get more details regarding the affected file(s)", "Reinstall": "Reinstall", "Reinstall package": "Reinstall package", "Related settings": "Related settings", "Release notes": "Release notes", "Release notes URL": "Release notes URL", "Release notes URL:": "Release notes URL:", "Release notes:": "Release notes:", "Reload": "Reload", "Reload log": "Reload log", "Removal failed": "Removal failed", "Removal succeeded": "<PERSON><PERSON><PERSON> succeeded", "Remove from list": "Remove from list", "Remove permanent data": "Remove permanent data", "Remove selection from bundle": "Remove selection from bundle", "Remove successful installs/uninstalls/updates from the installation list": "Remove successful installs/uninstalls/updates from the installation list", "Removing source {source}": "Removing source {source}", "Removing source {source} from {manager}": "Removing source {source} from {manager}", "Repair UniGetUI": "Repair UniGetUI", "Repair WinGet": "Repair WinGet", "Report an issue or submit a feature request": "Report an issue or submit a feature request", "Repository": "Repository", "Reset": "Reset", "Reset Scoop's global app cache": "Reset Scoop's global app cache", "Reset UniGetUI": "Reset UniGetUI", "Reset WinGet": "Reset WinGet", "Reset Winget sources (might help if no packages are listed)": "Reset WinGet sources (might help if no packages are listed)", "Reset WingetUI": "Reset UniGetUI", "Reset WingetUI and its preferences": "Reset UniGetUI and its preferences", "Reset WingetUI icon and screenshot cache": "Reset UniGetUI icon and screenshot cache", "Reset list": "Reset list", "Resetting Winget sources - WingetUI": "Resetting WinGet sources - UniGetUI", "Restart": "<PERSON><PERSON>", "Restart UniGetUI": "Restart UniGetUI", "Restart WingetUI": "Restart UniGetUI", "Restart WingetUI to fully apply changes": "Restart UniGetUI to fully apply changes", "Restart later": "<PERSON><PERSON> later", "Restart now": "Restart now", "Restart required": "Restart required", "Restart your PC to finish installation": "Restart your PC to finish installation", "Restart your computer to finish the installation": "Restart your computer to finish the installation", "Restore a backup from the cloud": "Restore a backup from the cloud", "Restrictions on package managers": "Restrictions on package managers", "Restrictions on package operations": "Restrictions on package operations", "Restrictions when importing package bundles": "Restrictions when importing package bundles", "Retry": "Retry", "Retry as administrator": "<PERSON><PERSON> as administrator", "Retry failed operations": "Retry failed operations", "Retry interactively": "Retry interactively", "Retry skipping integrity checks": "Retry skipping integrity checks", "Retrying, please wait...": "Retrying, please wait...", "Return to top": "Return to top", "Run": "Run", "Run as admin": "Run as admin", "Run cleanup and clear cache": "Run cleanup and clear cache", "Run last": "Run last", "Run next": "Run next", "Run now": "Run now", "Running the installer...": "Running the installer...", "Running the uninstaller...": "Running the uninstaller...", "Running the updater...": "Running the updater...", "Save": "Save", "Save File": "Save File", "Save and close": "Save and close", "Save as": "Save as", "Save bundle as": "Save bundle as", "Save now": "Save now", "Saving packages, please wait...": "Saving packages, please wait...", "Scoop Installer - WingetUI": "Scoop Installer - UniGetUI", "Scoop Uninstaller - WingetUI": "Scoop Uninstaller - UniGetUI", "Scoop package": "Scoop package", "Search": "Search", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want UniGetUI to overcomplicate, I just want a simple <b>software store</b>", "Search for packages": "Search for packages", "Search for packages to start": "Search for packages to start", "Search mode": "Search mode", "Search on available updates": "Search on available updates", "Search on your software": "Search on your software", "Searching for installed packages...": "Searching for installed packages...", "Searching for packages...": "Searching for packages...", "Searching for updates...": "Searching for updates...", "Select": "Select", "Select \"{item}\" to add your custom bucket": "Select \"{item}\" to add your custom bucket", "Select a folder": "Select a folder", "Select all": "Select all", "Select all packages": "Select all packages", "Select backup": "Select backup", "Select only <b>if you know what you are doing</b>.": "Select only <b>if you know what you are doing</b>.", "Select package file": "Select package file", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Select the backup you want to open. Later, you will be able to review which packages/programs you want to restore.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Select the processes that should be closed before this package is installed, updated or uninstalled.", "Select the source you want to add:": "Select the source you want to add:", "Select upgradable packages by default": "Select upgradable packages by default", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Sent handshake. Waiting for instance listener's answer... ({0}%)", "Set a custom backup file name": "Set a custom backup file name", "Set custom backup file name": "Set custom backup file name", "Settings": "Settings", "Share": "Share", "Share WingetUI": "Share UniGetUI", "Share anonymous usage data": "Share anonymous usage data", "Share this package": "Share this package", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.", "Show UniGetUI on the system tray": "Show UniGetUI on the system tray", "Show UniGetUI's version and build number on the titlebar.": "Show UniGetUI's version on the titlebar", "Show WingetUI": "Show UniGetUI", "Show a notification when an installation fails": "Show a notification when an installation fails", "Show a notification when an installation finishes successfully": "Show a notification when an installation finishes successfully", "Show a notification when an operation fails": "Show a notification when an operation fails", "Show a notification when an operation finishes successfully": "Show a notification when an operation finishes successfully", "Show a notification when there are available updates": "Show a notification when there are available updates", "Show a silent notification when an operation is running": "Show a silent notification when an operation is running", "Show details": "Show details", "Show in explorer": "Show in explorer", "Show info about the package on the Updates tab": "Show info about the package on the Updates tab", "Show missing translation strings": "Show missing translation strings", "Show notifications on different events": "Show notifications on different events", "Show package details": "Show package details", "Show package icons on package lists": "Show package icons on package lists", "Show similar packages": "Show similar packages", "Show the live output": "Show the live output", "Size": "Size", "Skip": "<PERSON><PERSON>", "Skip hash check": "Skip hash check", "Skip hash checks": "Skip hash checks", "Skip integrity checks": "Skip integrity checks", "Skip minor updates for this package": "Skip minor updates for this package", "Skip the hash check when installing the selected packages": "Skip the hash check when installing the selected packages", "Skip the hash check when updating the selected packages": "Skip the hash check when updating the selected packages", "Skip this version": "Skip this version", "Software Updates": "Software Updates", "Something went wrong": "Something went wrong", "Something went wrong while launching the updater.": "Something went wrong while launching the updater.", "Source": "Source", "Source URL:": "Source URL:", "Source added successfully": "Source added successfully", "Source addition failed": "Source addition failed", "Source name:": "Source name:", "Source removal failed": "Source removal failed", "Source removed successfully": "Source removed successfully", "Source:": "Source:", "Sources": "Sources", "Start": "Start", "Starting daemons...": "Starting daemons...", "Starting operation...": "Starting operation...", "Startup options": "Startup options", "Status": "Status", "Stuck here? Skip initialization": "Stuck here? Skip initialization", "Success!": "Success!", "Suport the developer": "Support the developer", "Support me": "Support me", "Support the developer": "Support the developer", "Systems are now ready to go!": "Systems are now ready to go!", "Telemetry": "Telemetry", "Text": "Text", "Text file": "Text file", "Thank you ❤": "Thank you ❤", "Thank you 😉": "Thank you 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "The backup will NOT include any binary file nor any program's saved data.", "The backup will be performed after login.": "The backup will be performed after login.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.", "The bundle was created successfully on {0}": "The bundle was created successfully on {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "The bundle you are trying to load appears to be invalid. Please check the file and try again.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "The classic package manager for Windows. You'll find everything there. <br>Contains: <b>General Software</b>", "The cloud backup completed successfully.": "The cloud backup completed successfully.", "The cloud backup has been loaded successfully.": "The cloud backup has been loaded successfully.", "The current bundle has no packages. Add some packages to get started": "The current bundle has no packages. Add some packages to get started", "The executable file for {0} was not found": "The executable file for {0} was not found", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.", "The following packages are going to be installed on your system.": "The following packages are going to be installed on your system.", "The following settings may pose a security risk, hence they are disabled by default.": "The following settings may pose a security risk, hence they are disabled by default.", "The following settings will be applied each time this package is installed, updated or removed.": "The following settings will be applied each time this package is installed, updated or removed.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.", "The icons and screenshots are maintained by users like you!": "The icons and screenshots are maintained by users like you!", "The installation script saved to {0}": "The installation script saved to {0}", "The installer authenticity could not be verified.": "The installer authenticity could not be verified.", "The installer has an invalid checksum": "The installer has an invalid checksum", "The installer hash does not match the expected value.": "The installer hash does not match the expected value.", "The local icon cache currently takes {0} MB": "The local icon cache currently takes {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "The main goal of this project is to create an intuitive UI for the most common CLI package managers for Windows, such as Winget and Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "The package \"{0}\" was not found on the package manager \"{1}\"", "The package bundle could not be created due to an error.": "The package bundle could not be created due to an error.", "The package bundle is not valid": "The package bundle is not valid", "The package manager \"{0}\" is disabled": "The package manager \"{0}\" is disabled", "The package manager \"{0}\" was not found": "The package manager \"{0}\" was not found", "The package {0} from {1} was not found.": "The package {0} from {1} was not found.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.", "The selected packages have been blacklisted": "The selected packages have been blacklisted", "The settings will list, in their descriptions, the potential security issues they may have.": "The settings will list, in their descriptions, the potential security issues they may have.", "The size of the backup is estimated to be less than 1MB.": "The size of the backup is estimated to be less than 1MB.", "The source {source} was added to {manager} successfully": "The source {source} was added to {manager} successfully", "The source {source} was removed from {manager} successfully": "The source {source} was removed from {manager} successfully", "The system tray icon must be enabled in order for notifications to work": "The system tray icon must be enabled in order for notifications to work", "The update process has been aborted.": "The update process has been aborted.", "The update process will start after closing UniGetUI": "The update process will start after closing UniGetUI", "The update will be installed upon closing WingetUI": "The update will be installed upon closing UniGetUI", "The update will not continue.": "The update will not continue.", "The user has canceled {0}, that was a requirement for {1} to be run": "The user has canceled {0}, that was a requirement for {1} to be run", "There are no new UniGetUI versions to be installed": "There are no new UniGetUI versions to be installed", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "There are ongoing operations. Quitting UniGetUI may cause them to fail. Do you want to continue?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "There are some great videos on YouTube that showcase UniGetUI and its capabilities. You could learn useful tricks and tips!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "There are two main reasons to not run UniGetUI as administrator:\nThe first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\nThe second one is that running UniGetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\nRemember that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.", "There is an error with the configuration of the package manager \"{0}\"": "There is an error with the configuration of the package manager \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "There is an installation in progress. If you close UniGetUI, the installation may fail and have unexpected results. Do you still want to quit UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "They are the programs in charge of installing, updating and removing packages.", "Third-party licenses": "Third-party licenses", "This could represent a <b>security risk</b>.": "This could represent a <b>security risk</b>.", "This is not recommended.": "This is not recommended.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}", "This is the <b>default choice</b>.": "This is the <b>default choice</b>.", "This may help if WinGet packages are not shown": "This may help if WinGet packages are not shown", "This may help if no packages are listed": "This may help if no packages are listed", "This may take a minute or two": "This may take a minute or two", "This operation is running interactively.": "This operation is running interactively.", "This operation is running with administrator privileges.": "This operation is running with administrator privileges.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "This package bundle had some settings that are potentially dangerous, and may be ignored by default.", "This package can be updated": "This package can be updated", "This package can be updated to version {0}": "This package can be updated to version {0}", "This package can be upgraded to version {0}": "This package can be upgraded to version {0}", "This package cannot be installed from an elevated context.": "This package cannot be installed from an elevated context.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "This package has no screenshots or is missing the icon? Contribute to UniGetUI by adding the missing icons and screenshots to our open, public database.", "This package is already installed": "This package is already installed", "This package is being processed": "This package is being processed", "This package is not available": "This package is not available", "This package is on the queue": "This package is on the queue", "This process is running with administrator privileges": "This process is running with administrator privileges", "This project has no connection with the official {0} project — it's completely unofficial.": "This project has no connection with the official {0} project — it's completely unofficial.", "This setting is disabled": "This setting is disabled", "This wizard will help you configure and customize WingetUI!": "This wizard will help you configure and customize UniGetUI!", "Toggle search filters pane": "Toggle search filters pane", "Translators": "Translators", "Try to kill the processes that refuse to close when requested to": "Try to kill the processes that refuse to close when requested to", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous", "Type here the name and the URL of the source you want to add, separed by a space.": "Type here the name and the URL of the source you want to add, separated by a space.", "Unable to find package": "Unable to find the package", "Unable to load informarion": "Unable to load information", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI collects anonymous usage data in order to improve the user experience.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI has detected a new desktop shortcut that can be deleted automatically.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.", "UniGetUI is being updated...": "UniGetUI is being updated...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.", "UniGetUI on the background and system tray": "UniGetUI on the background and system tray", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI or some of its components are missing or corrupt.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI requires {0} to operate, but it was not found on your system.", "UniGetUI startup page:": "UniGetUI startup page:", "UniGetUI updater": "UniGetUI updater", "UniGetUI version {0} is being downloaded.": "UniGetUI version {0} is being downloaded.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} is ready to be installed.", "Uninstall": "Uninstall", "Uninstall Scoop (and its packages)": "Uninstall Scoop (and its packages)", "Uninstall and more": "Uninstall and more", "Uninstall and remove data": "Uninstall and remove data", "Uninstall as administrator": "<PERSON><PERSON><PERSON><PERSON> as administrator", "Uninstall canceled by the user!": "Uninstall canceled by the user!", "Uninstall failed": "Uninstall failed", "Uninstall options": "Uninstall options", "Uninstall package": "Uninstall package", "Uninstall package, then reinstall it": "Uninstall package, then reinstall it", "Uninstall package, then update it": "Uninstall package, then update it", "Uninstall previous versions when updated": "Uninstall previous versions when updated", "Uninstall selected packages": "Uninstall selected packages", "Uninstall selection": "Uninstall selection", "Uninstall succeeded": "<PERSON><PERSON><PERSON><PERSON> succeeded", "Uninstall the selected packages with administrator privileges": "Uninstall the selected packages with administrator privileges", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.", "Unknown": "Unknown", "Unknown size": "Unknown size", "Unset or unknown": "Unset or unknown", "Up to date": "Up to date", "Update": "Update", "Update WingetUI automatically": "Update UniGetUI automatically", "Update all": "Update all", "Update and more": "Update and more", "Update as administrator": "Update as administrator", "Update check frequency, automatically install updates, etc.": "Update check frequency, automatically install updates, etc.", "Update checking": "Update checking", "Update date": "Update date", "Update failed": "Update failed", "Update found!": "Update found!", "Update now": "Update now", "Update options": "Update options", "Update package indexes on launch": "Update package indexes on launch", "Update packages automatically": "Update packages automatically", "Update selected packages": "Update selected packages", "Update selected packages with administrator privileges": "Update selected packages with administrator privileges", "Update selection": "Update selection", "Update succeeded": "Update succeeded", "Update to version {0}": "Update to version {0}", "Update to {0} available": "Update to {0} available", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Update vcpkg's Git portfiles automatically (requires Git installed)", "Updates": "Updates", "Updates available!": "Updates available!", "Updates for this package are ignored": "Updates for this package are ignored", "Updates found!": "Updates found!", "Updates preferences": "Updates preferences", "Updating WingetUI": "Updating UniGetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Use Legacy bundled WinGet instead of PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Use a custom icon and screenshot database URL", "Use bundled WinGet instead of PowerShell CMDlets": "Use bundled WinGet instead of PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "Use bundled WinGet instead of system WinGet", "Use installed GSudo instead of UniGetUI Elevator": "Use installed GSudo instead of UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Use installed GSudo instead of the bundled one", "Use system Chocolatey": "Use system Chocolatey", "Use system Chocolatey (Needs a restart)": "Use system Chocolatey (Needs a restart)", "Use system Winget (Needs a restart)": "Use system Winget (Needs a restart)", "Use system Winget (System language must be set to english)": "Use WinGet (System language must be set to English)", "Use the WinGet COM API to fetch packages": "Use the WinGet COM API to fetch packages", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Use the WinGet PowerShell Module instead of the WinGet COM API", "Useful links": "Useful links", "User": "User", "User interface preferences": "User interface preferences", "User | Local": "User | Local", "Username": "Username", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Using UniGetUI implies the acceptance of the GNU Lesser General Public License v2.1 License", "Using WingetUI implies the acceptation of the MIT License": "Using UniGetUI implies the acceptance of the MIT License", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings", "Vcpkg was not found on your system.": "Vcpkg was not found on your system.", "Verbose": "Verbose", "Version": "Version", "Version to install:": "Version to install:", "Version:": "Version:", "View GitHub Profile": "View GitHub Profile", "View WingetUI on GitHub": "View UniGetUI on GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "View UniGetUI's source code. From there, you can report bugs or suggest features, or even contribute directly to the UniGetUI project", "View mode:": "View mode:", "View on UniGetUI": "View on UniGetUI", "View page on browser": "View page on browser", "View {0} logs": "View {0} logs", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.", "Waiting for other installations to finish...": "Waiting for other installations to finish...", "Waiting for {0} to complete...": "Waiting for {0} to complete...", "Warning": "Warning", "Warning!": "Warning!", "We are checking for updates.": "We are checking for updates.", "We could not load detailed information about this package, because it was not found in any of your package sources": "We could not load detailed information about this package, because it was not found in any of your package sources.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "We could not load detailed information about this package, because it was not installed from an available package manager.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.", "We couldn't find any package": "We couldn't find any package", "Welcome to WingetUI": "Welcome to UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "When batch installing packages from a bundle, install also packages that are already installed", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "When new shortcuts are detected, delete them automatically instead of showing this dialog.", "Which backup do you want to open?": "Which backup do you want to open?", "Which package managers do you want to use?": "Which package managers do you want to use?", "Which source do you want to add?": "Which source do you want to add?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "While WinGet can be used within UniGetUI, UniGetUI can be used with other package managers, which can be confusing. In the past, UniGetUI was designed to work only with Winget, but this is not true anymore, and therefore UniGetUI does not represent what this project aims to become.", "WinGet could not be repaired": "WinGet could not be repaired", "WinGet malfunction detected": "WinGet malfunction detected", "WinGet was repaired successfully": "WinGet was repaired successfully", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Everything is up to date", "WingetUI - {0} updates are available": "UniGetUI - {0} updates are available", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "UniGetUI Homepage", "WingetUI Homepage - Share this link!": "UniGetUI Homepage - Share this link!", "WingetUI License": "UniGetUI License", "WingetUI Log": "UniGetUI Log", "WingetUI Repository": "UniGetUI Repository", "WingetUI Settings": "UniGetUI Settings", "WingetUI Settings File": "UniGetUI Settings File", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI uses the following libraries. Without them, UniGetUI wouldn't have been possible.", "WingetUI Version {0}": "UniGetUI Version {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI autostart behaviour, application launch settings", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI can check if your software has available updates, and install them automatically if you want", "WingetUI display language:": "UniGetUI display language:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI has been ran as administrator, which is not recommended. When running UniGetUI as administrator, EVERY operation launched from UniGetUI will have administrator privileges. You can still use the program, but we highly recommend not running UniGetUI with administrator privileges.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI has not been machine translated! The following users have been in charge of the translations:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI is being renamed in order to emphasize the difference between UniGetUI (the interface you are using right now) and WinGet (a package manager developed by Microsoft with which I am not related)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI is being updated. When finished, UniGetUI will restart itself", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.", "WingetUI log": "UniGetUI log", "WingetUI tray application preferences": "UniGetUI tray application preferences", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI uses the following libraries. Without them, UniGetUI wouldn't have been possible.", "WingetUI version {0} is being downloaded.": "UniGetUI version {0} is being downloaded.", "WingetUI will become {newname} soon!": "WingetUI will become {newname} soon!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI will show a UAC prompt every time a package requires elevation to be installed.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI wouldn't have been possible without the help of our dear contributors. Check out their GitHub profiles, UniGetUI wouldn't be possible without them!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} is ready to be installed.", "Write here the process names here, separated by commas (,)": "Write here the process names here, separated by commas (,)", "Yes": "Yes", "You are logged in as {0} (@{1})": "You are logged in as {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "You can change this behavior on UniGetUI security settings.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.", "You have currently version {0} installed": "You have currently version {0} installed", "You have installed WingetUI Version {0}": "You have installed UniGetUI Version {0}", "You may lose unsaved data": "You may lose unsaved data", "You may need to install {pm} in order to use it with WingetUI.": "You may need to install {pm} in order to use it with UniGetUI.", "You may restart your computer later if you wish": "You may restart your computer later if you wish", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "You will be prompted only once, and administrator rights will be granted to packages that request them.", "You will be prompted only once, and every future installation will be elevated automatically.": "You will be prompted only once, and every future installation will be elevated automatically.", "You will likely need to interact with the installer.": "You will likely need to interact with the installer.", "[RAN AS ADMINISTRATOR]": "RAN AS ADMINISTRATOR", "buy me a coffee": "buy me a coffee", "extracted": "extracted", "feature": "feature", "formerly WingetUI": "formerly WingetUI", "homepage": "website", "install": "install", "installation": "installation", "installed": "installed", "installing": "installing", "library": "library", "mandatory": "mandatory", "option": "option", "optional": "optional", "uninstall": "uninstall", "uninstallation": "uninstallation", "uninstalled": "uninstalled", "uninstalling": "uninstalling", "update(noun)": "update", "update(verb)": "update", "updated": "updated", "updating": "updating", "version {0}": "version {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} Install options are currently locked because {0} follows the default install options.", "{0} Uninstallation": "{0} Uninstallation", "{0} aborted": "{0} aborted", "{0} can be updated": "{0} can be updated", "{0} can be updated to version {1}": "{0} can be updated to version {1}", "{0} days": "{0} days", "{0} desktop shortcuts created": "{0} desktop shortcuts created", "{0} failed": "{0} failed", "{0} has been installed successfully.": "{0} has been installed successfully.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation", "{0} has failed, that was a requirement for {1} to be run": "{0} has failed, that was a requirement for {1} to be run", "{0} homepage": "{0} homepage", "{0} hours": "{0} hours", "{0} installation": "{0} installation", "{0} installation options": "{0} installation options", "{0} installer is being downloaded": "{0} installer is being downloaded", "{0} is being installed": "{0} is being installed", "{0} is being uninstalled": "{0} is being uninstalled", "{0} is being updated": "{0} is being updated", "{0} is being updated to version {1}": "{0} is being updated to version {1}", "{0} is disabled": "{0} is disabled", "{0} minutes": "{0} minutes", "{0} months": "{0} months", "{0} packages are being updated": "{0} packages are being updated", "{0} packages can be updated": "{0} packages can be updated", "{0} packages found": "{0} packages found", "{0} packages were found": "{0} packages were found", "{0} packages were found, {1} of which match the specified filters.": "{0} packages were found, {1} of which match the specified filters.", "{0} selected": "{0} selected", "{0} settings": "{0} settings", "{0} status": "{0} status", "{0} succeeded": "{0} succeeded", "{0} update": "{0} update", "{0} updates are available": "{0} updates are available", "{0} was {1} successfully!": "{0} was {1} successfully!", "{0} weeks": "{0} weeks", "{0} years": "{0} years", "{0} {1} failed": "{0} {1} failed", "{package} Installation": "{package} Installation", "{package} Uninstall": "{package} Uninstall", "{package} Update": "{package} Update", "{package} could not be installed": "{package} could not be installed", "{package} could not be uninstalled": "{package} could not be uninstalled", "{package} could not be updated": "{package} could not be updated", "{package} installation failed": "{package} installation failed", "{package} installer could not be downloaded": "{package} installer could not be downloaded", "{package} installer download": "{package} installer download", "{package} installer was downloaded successfully": "{package} installer was downloaded successfully", "{package} uninstall failed": "{package} uninstall failed", "{package} update failed": "{package} update failed", "{package} update failed. Click here for more details.": "{package} update failed. Click here for more details.", "{package} was installed successfully": "{package} was installed successfully", "{package} was uninstalled successfully": "{package} was uninstalled successfully", "{package} was updated successfully": "{package} was updated successfully", "{pcName} installed packages": "{pcName} installed packages", "{pm} could not be found": "{pm} could not be found", "{pm} found: {state}": "{pm} found: {state}", "{pm} is disabled": "{pm} is disabled", "{pm} is enabled and ready to go": "{pm} is enabled and ready to go", "{pm} package manager specific preferences": "{pm} package manager specific preferences", "{pm} preferences": "{pm} preferences", "{pm} version:": "{pm} version:", "{pm} was not found!": "{pm} was not found!"}