{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(अंतिम बार जांचा गया: {0})", "(Number {0} in the queue)": "(पंक्ति में संख्या {0})", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@satanarious, @atharva_xoxo, @Ashu-r", "0 packages found": "\n0 पैकेज मिले", "0 updates found": "0 अपडेट मिले", "1 - Errors": "1 - त्रुटि", "1 day": "1 दिन", "1 hour": "1 घंटा", "1 month": "1 महीना", "1 package was found": "1 पैकेज मिला", "1 update is available": "1 अपडेट उपलब्ध है", "1 week": "1 सप्ताह", "1 year": "1 साल", "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": "2 - चेतावनियाँ", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": "3 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (क<PERSON><PERSON>र)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": "4 - ज<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (अधिक)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": "5 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (डिबग)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": null, "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": null, "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": null, "A restart is required": "पुनरारंभ करना आवश्यक है", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "के बारे में", "About Qt6": "\nQt6 के बारे में", "About WingetUI": "WingetUI  के बारे में", "About WingetUI version {0}": "WingetUI  संस्करण {0} के बारे में", "About the dev": "डेवलपर के बारे में", "Accept": "स्वीकार", "Action when double-clicking packages, hide successful installations": "संकुल पर डबल-क्लिक करने पर क्रिया, सफल स्थापनाओं को छुपाएं", "Add": "जोड़ो", "Add a source to {0}": "{0} में एक स्रोत जोड़ें", "Add a timestamp to the backup file names": null, "Add a timestamp to the backup files": null, "Add packages or open an existing bundle": null, "Add packages or open an existing package bundle": null, "Add packages to bundle": null, "Add packages to start": null, "Add selection to bundle": null, "Add source": "स्रोत जोड़ें", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": null, "Addition succeeded": null, "Administrator privileges": "\nप्रशासक के विशेषाधिकार", "Administrator privileges preferences": "व्यवस्थापक विशेषाधिकार प्राथमिकताएँ", "Administrator rights": "व्यवस्थापक अधिकार", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": null, "All versions": "सभी संस्करण", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": null, "Allow parallel installs (NOT RECOMMENDED)": "समानांतर इंस्टॉल की अनुमति दें (अनुशंसित नहीं)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": null, "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": "डिफ़ॉल्ट रूप से हमेशा {pm} इंस्टॉलेशन को उच्च वर्तनी दें", "Always run {pm} operations with administrator rights": null, "An error occurred": null, "An error occurred when adding the source: ": null, "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": null, "An error occurred:": null, "An interal error occurred. Please view the log for further details.": null, "An unexpected error occurred:": null, "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": null, "Android Subsystem": "एंड्रॉइड सबसिस्टम", "Another source": null, "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": null, "App Name": "ऐप का नाम", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "एप्लीकेशन थीम:", "Apply": null, "Architecture to install:": "स्थापित करने के लिए वास्तुकला:", "Are these screenshots wron or blurry?": "क्या ये स्क्रीनशॉट गलत हैं या धुंधले हैं?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "क्या आप निश्चित हैं?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": null, "Ask for administrator rights when required": "आवश्यकता पड़ने पर व्यवस्थापक अधिकारों के लिए पूछें", "Ask once or always for administrator rights, elevate installations by default": "व्यवस्थापक अधिकारों के लिए एक बार या हमेशा पूछें, डिफ़ॉल्ट रूप से इंस्टॉलेशन को उच्च वर्तनी दें", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "व्यवस्थापक विशेषाधिकारों के लिए केवल एक बार पूछें (अनुशंसित नहीं)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": null, "Authenticate to the proxy with an user and a password": null, "Author": "\nलेखक", "Automatic desktop shortcut remover": null, "Automatically save a list of all your installed packages to easily restore them.": null, "Automatically save a list of your installed packages on your computer.": null, "Autostart WingetUI in the notifications area": "सूचना क्षेत्र में WingetUI  ऑटोस्टार्ट करें", "Available Updates": null, "Available updates: {0}": "उपलब्ध अपडेट: {0}", "Available updates: {0}, not finished yet...": "\nउपलब्ध अपडेट: {0}, अभी तक समाप्त नहीं हुआ...", "Backing up packages to GitHub Gist...": null, "Backup": null, "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": null, "Backup location": null, "Become a contributor": null, "Become a translator": null, "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "बीटा सुविधाएँ और अन्य विकल्प जिन्हें छुआ नहीं जाना चाहिए", "Both": null, "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "लेकिन WingetUI  के बारे में और जानने के लिए आप यहां कुछ और चीजें कर सकते हैं:", "By toggling a package manager off, you will no longer be able to see or update its packages.": null, "Cache administrator rights and elevate installers by default": "व्यवस्थापक अधिकारों को कैश करें और इंस्टॉलर को डिफ़ॉल्ट रूप से उच्च वर्तनी दें।", "Cache administrator rights, but elevate installers only when required": "व्यवस्थापक अधिकारों को कैश करें, लेकिन इंस्टॉलर को केवल जब आवश्यक हो तभी उच्च वर्तनी दें।", "Cache was reset successfully!": "कैश को सफलतापूर्वक रीसेट कर दिया गया!", "Can't {0} {1}": "{1} {0}  नहीं कर सके", "Cancel": "रद्<PERSON> करें", "Cancel all operations": null, "Change backup output directory": null, "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": null, "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": null, "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "समय-समय पर पैकेज अपडेट की जांच करें", "Check for updates": null, "Check for updates every:": "अपडेट की जांच करें हर:", "Check for updates periodically": "अपडेटों के लिए समय-समय पर जाँच करें", "Check for updates regularly, and ask me what to do when updates are found.": "अपडेटों के लिए नियमित रूप से जाँच करें, और मुझसे पूछें कि अपडेट पाए जाने पर मुझे क्या करना चाहिए।", "Check for updates regularly, and automatically install available ones.": null, "Check out my {0} and my {1}!": "मेरा {0} और मेरा {1} देखें!", "Check out some WingetUI overviews": "कुछ WingetUI  अवलोकन देखें", "Checking for other running instances...": "अन्य चल रहे इंस्टेंस के लिए जाँच की जा रही है...", "Checking for updates...": "अपडेट्स के लिए जांच की जा रही है...", "Checking found instace(s)...": "पाए गए इंस्टेंस की जांच की जा रही है...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "चयन साफ़ करें", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": null, "Clearing Scoop cache...": "\nस्कूप कैश साफ़ किया जा रहा है...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": null, "Close": "ब<PERSON><PERSON> करें", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "\nअधिसूचना क्षेत्र में WingetUI  बंद करें", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": null, "Command-line to run:": null, "Compare query against": null, "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "कॉम्पोनेन्ट की जानकारी", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": null, "Contribute to the icon and screenshot repository": "आइकन और स्क्रीनशॉट रिपॉजिटरी में योगदान करें", "Contributors": "\nयोगदानकर्ता", "Copy": null, "Copy to clipboard": null, "Could not add source": null, "Could not add source {source} to {manager}": null, "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": null, "Could not load announcements - HTTP status code is $CODE": null, "Could not remove source": null, "Could not remove source {source} from {manager}": null, "Could not remove {source} from {manager}": null, "Credentials": null, "Current Version": "वर्तमान संस्करण", "Current status: Not logged in": null, "Current user": "तात्कालिक प्रयोगकर्ता", "Custom arguments:": null, "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "कस्टम आदेश-पंक्ति तर्क:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "WingetUI  को अनुकूलित करें - केवल हैकर्स और उन्नत उपयोगकर्ताओं के लिए", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "अस्वीकरण: हम डाउनलोड किए गए पैकेजों के लिए ज़िम्मेदार नहीं हैं। कृपया केवल विश्वसनीय सॉफ़्टवेयर इंस्टॉल करना सुनिश्चित करें।", "Dark": "डार्क", "Decline": null, "Default": " पूर्व निर्धारित मूल्य", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "डिफ़ॉल्ट प्राथमिकताएं - नियमित उपयोगकर्ताओं के लिए उपयुक्त", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "\nविवरण:", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "विकसित करना कठिन है, और यह एप्लिकेशन निःशुल्क है। लेकिन अगर आपको एप्लिकेशन पसंद आया, तो आप हमेशा <b>मुझे एक कॉफी खरीद सकते हैं</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": null, "Disable new share API (port 7058)": "\nनया शेयर एपीआई अक्षम करें (पोर्ट 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": null, "Discover Packages": "पैकेज खोजें", "Discover packages": null, "Distinguish between\nuppercase and lowercase": null, "Distinguish between uppercase and lowercase": null, "Do NOT check for updates": "अपडेट की जाँच न करें", "Do an interactive install for the selected packages": "चयनित पैकेजों के लिए एक इंटरैक्टिव स्थापना करें", "Do an interactive uninstall for the selected packages": "चयनित पैकेजों के लिए एक इंटरैक्टिव अनइंस्टॉल करें", "Do an interactive update for the selected packages": "चयनित पैकेजों के लिए एक इंटरैक्टिव अपडेट करें", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "<PERSON><PERSON><PERSON> से नए ऐप अनुवाद को स्वचालित रूप से डाउनलोड न करें", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": null, "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "लॉन्च होने पर पैकेज इंडेक्स अपडेट न करें", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": null, "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "क्या आपको WingetUI  उपयोगी लगता है? आप डेवलपर का समर्थन करना चाहेंगे? अगर ऐसा है, तो आप {0} कर सकते हैं, इससे बहुत मदद मिलती है!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": null, "Do you really want to uninstall {0} packages?": "क्या आप वास्तव में {0} पैकेजों की स्थापना रद्द करना चाहते हैं?", "Do you really want to uninstall {0}?": "क्या आप वाकई {0} की स्थापना रद्द करना चाहते हैं?", "Do you want to restart your computer now?": "क्या आप अपने कंप्यूटर को अभी पुनरारंभ करना चाहते हैं?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "क्या आप अपनी भाषा में WingetUI  का अनुवाद करना चाहते हैं? देखें कि कैसे योगदान दिया जाए <a style=\"color:{0}\" href=\"{1}\"a>यहां!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": null, "Donate": "दान दें", "Done!": null, "Download failed": null, "Download installer": null, "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": null, "Download updated language files from GitHub automatically": null, "Downloading": null, "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "पैकेज मेटाडेटा डाउनलोड हो रहा है...", "Enable Scoop cleanup on launch": "लॉन्च होने पर स्कूप क्लीनअप सक्षम करें", "Enable WingetUI notifications": "WingetUI  सूचनाएं सक्षम करें", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": null, "Enable it to install packages from {pm}.": null, "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "{pm} सक्षम करें", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "एरर", "Everything is up to date": null, "Exact match": null, "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": null, "Experimental settings and developer options": "प्रायोगिक सेटिंग्स और डेवलपर विकल्प", "Export": "एक्सपोर्ट", "Export log as a file": "\nफ़ाइल के रूप में लॉग निर्यात करें", "Export packages": "पैकेज एक्सपोर्ट करें", "Export selected packages to a file": "फ़ाइल में चुने पैकेज निर्यात करें", "Export settings to a local file": null, "Export to a file": null, "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": null, "Filters": null, "Finish": "खत्म करें", "Follow system color scheme": "सिस्टम कलर स्कीम का पालन करें", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": null, "Formerly known as WingetUI": null, "Found": "\nमिले", "Found packages: ": null, "Found packages: {0}": "पैकेज मिले: {0}", "Found packages: {0}, not finished yet...": "\nपैकेज मिले: {0}, अभी पूरा नहीं हुआ...", "General preferences": "सामान्य प्राथमिकताएं", "GitHub profile": "<PERSON><PERSON><PERSON> प्रोफाइल", "Global": "वैश्विक", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": null, "Great! You are on the latest version.": null, "Grid": null, "Help": null, "Help and documentation": null, "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "नमस्ते, मेरा नाम मार्टी है, और मैं WingetUI  का <i>डेवलपर</i> हूं। विनगेट यू आई पूरी तरह से मेरे खाली समय पर बनाया गया है!", "Hide details": "विवरण छुपायें", "Homepage": "\nहोमपेज", "Hooray! No updates were found.": "\nवाह! कोई अपडेट नहीं मिला!", "How should installations that require administrator privileges be treated?": "स्थापनाओं को कैसे व्यवहार करना चाहिए जिनके लिए व्यवस्थापकीय विशेषाधिकारों की आवश्यकता होती है?", "How to add packages to a bundle": null, "I understand": null, "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "इस पैकेज के लिए भविष्य के अपडेट पर ध्यान न दें", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "चयनित पैकेजों पर ध्यान न दें", "Ignore special characters": null, "Ignore updates for the selected packages": "चयनित पैकेजों के लिए अपडेट पर ध्यान न दें", "Ignore updates for this package": "इस पैकेज के अपडेट को नज़रअंदाज़ करें", "Ignored updates": "अपडेट पर ध्यान नहीं दिया", "Ignored version": "उपेक्षित संस्करण", "Import": null, "Import packages": "संकुल इम्पोर्ट करें", "Import packages from a file": "\nफ़ाइल से पैकेज आयात करें", "Import settings from a local file": null, "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "WingetUI  प्रारंभ कर रहा है...", "Install": "स्थापित करें", "Install Scoop": "स्कूप स्थापित करें", "Install and more": null, "Install and update preferences": null, "Install as administrator": "प्रशासक के रूप में स्थापना करें", "Install available updates automatically": null, "Install location can't be changed for {0} packages": null, "Install location:": null, "Install options": null, "Install packages from a file": "फ़ाइल से संकुल संस्थापित करें", "Install prerelease versions of UniGetUI": null, "Install selected packages": "चयनित संकुल स्थापित करें", "Install selected packages with administrator privileges": "व्यवस्थापक विशेषाधिकारों के साथ चयनित पैकेजों को स्थापित करें\n", "Install selection": null, "Install the latest prerelease version": null, "Install updates automatically": "अपडेट को स्वचालित रूप से स्थापित करें", "Install {0}": null, "Installation canceled by the user!": "उपयोगकर्ता द्वारा स्थापना रद्द कर दी गई!", "Installation failed": null, "Installation options": "स्थापना विकल्प", "Installation scope:": "स्थापना गुंजाइश:", "Installation succeeded": null, "Installed Packages": "स्थापित पैकेज", "Installed Version": "स्थापित संस्करण", "Installed packages": null, "Installer SHA256": "इंस्टॉलर SHA256", "Installer SHA512": "इंस्टॉलर SHA512", "Installer Type": "इंस्टॉलर प्रकार", "Installer URL": "इंस्टॉलर यू आर एल", "Installer not available": null, "Instance {0} responded, quitting...": "उदाहरण {0} ने जवाब दिया, छोड़ रहा हूँ...", "Instant search": "त्वरित खोज", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "इंटरएक्टिव स्थापना", "Interactive operation": null, "Interactive uninstall": "इंटरएक्टिव स्थापना रद्द", "Interactive update": "इंटरएक्टिव अपडेट", "Internet connection settings": null, "Is this package missing the icon?": "\nक्या इस पैकेज में आइकन नहीं है?", "Is your language missing or incomplete?": null, "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "ऐसा लगता है कि आपने WingetUI को व्यवस्थापक के रूप में चलाया, जिसकी अनुशंसा नहीं की जाती है। आप अभी भी प्रोग्राम का उपयोग कर सकते हैं, लेकिन हम अत्यधिक अनुशंसा करते हैं कि व्यवस्थापकीय विशेषाधिकारों के साथ WingetUI न चलाएँ। क्यों देखने के लिए \"{showDetails}\" पर क्लिक करें।", "Language": null, "Language, theme and other miscellaneous preferences": "भाषा, थीम और अन्य विविध प्राथमिकताएं", "Last updated:": "आखरी अपडेट:", "Latest": "नवीनतम", "Latest Version": "\nनवीनतम संस्करण", "Latest Version:": "\nनवीनतम संस्करण:", "Latest details...": "\nनवीनतम विवरण...", "Launching subprocess...": null, "Leave empty for default": null, "License": "लाइसेंस", "Licenses": "लाइसेंस", "Light": "लाइट", "List": null, "Live command-line output": "लाइव कमांड-लाइन आउटपुट", "Live output": null, "Loading UI components...": "\nयू आई कॉम्पोनेन्ट लोड हो रहे हैं...", "Loading WingetUI...": "WingetUI  लोड हो रहा है...", "Loading packages": null, "Loading packages, please wait...": null, "Loading...": "\nलोड हो रहा है...", "Local": "स्थानीय", "Local PC": "स्थानीय पी.सी", "Local backup advanced options": null, "Local machine": "स्थानीय मशीन", "Local package backup": null, "Locating {pm}...": "{pm} का पता लगाया जा रहा है...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": null, "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": null, "Machine | Global": null, "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": null, "Manage ignored packages": "उपेक्षित पैकेज प्रबंधित करें", "Manage ignored updates": "उपेक्षित अपडेट प्रबंधित करें", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": null, "Manifest": "मैनिफेस्ट", "Manifests": "प्रकट होता है", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": null, "Missing dependency": null, "More": null, "More details": null, "More details about the shared data and how it will be processed": null, "More info": null, "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "नाम", "New": null, "New Version": "नया संस्करण", "New bundle": null, "New version": "नया संस्करण", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "नहीं", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "कोई पैकेज नहीं मिला", "No packages found matching the input criteria": "\nइनपुट मानदंड से मेल खाने वाला कोई पैकेज नहीं मिला", "No packages have been added yet": null, "No packages selected": "कोई पैकेज नहीं चुना गया", "No packages were found": null, "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": null, "No sources found": null, "No sources were found": null, "No updates are available": "कोई अपडेट उपलब्ध नहीं है", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": null, "Not available": "उपलब्ध नहीं है", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "नहीं मिला", "Not right now": null, "Notes:": "टिप्पणियाँ:", "Notification preferences": null, "Notification tray options": "अधिसूचना ट्रे विकल्प", "Notification types": null, "NuPkg (zipped manifest)": null, "OK": "ठीक", "Ok": "ठीक", "Open": "खोलें", "Open GitHub": "<PERSON><PERSON><PERSON> खोलें", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": null, "Open backup location": null, "Open existing bundle": null, "Open install location": null, "Open the welcome wizard": "वेलकम विज़ार्ड खोलें", "Operation canceled by user": null, "Operation cancelled": null, "Operation history": null, "Operation in progress": null, "Operation on queue (position {0})...": null, "Operation profile:": null, "Options saved": null, "Order by:": null, "Other": null, "Other settings": null, "Package": null, "Package Bundles": null, "Package ID": "पैकेज आईडी", "Package Manager": null, "Package Manager logs": null, "Package Managers": null, "Package Name": "पैकेज का नाम", "Package backup": null, "Package backup settings": null, "Package bundle": null, "Package details": "पैकेज के ब्यौरे", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "पैकेज प्रबंधक वरीयताएँ", "Package managers": null, "Package not found": null, "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "पैकेज", "Packages found: {0}": null, "Partially": null, "Password": null, "Paste a valid URL to the database": null, "Pause updates for": null, "Perform a backup now": null, "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": null, "Periodically perform a backup of the installed packages": null, "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": null, "Please enter at least 3 characters": null, "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "कृपया ध्यान दें कि इस मशीन पर सक्षम पैकेज मेनेजर के कारण कुछ पैकेज इंस्टॉल करने योग्य नहीं हो सकते हैं।", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "कृपया ध्यान दें कि कुछ स्रोतों से पैकेज एक्सपोर्ट योग्य नहीं हो सकते हैं। उन्हें धूसर कर दिया गया है और एक्सपोर्ट नहीं किया जाएगा।", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": null, "Please select how you want to configure WingetUI": "कृपया चयन करें कि आप WingetUI  को कैसे कॉन्फ़िगर करना चाहते हैं", "Please try again later": null, "Please type at least two characters": null, "Please wait": null, "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "कृपया प्रतीक्षा करें...", "Portable": null, "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": null, "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": null, "Preparing packages, please wait...": null, "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "प्रकाशन तिथि:", "Publisher": "प्रचारक", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": null, "Quit": "ब<PERSON><PERSON> करें", "Quit WingetUI": null, "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": null, "Related settings": null, "Release notes": null, "Release notes URL": null, "Release notes URL:": "रिलीज़ नोट यूआरएल:", "Release notes:": "रिलीज नोट्स:", "Reload": "पुनः लोड करें", "Reload log": "लॉग पुनः लोड करें", "Removal failed": null, "Removal succeeded": null, "Remove from list": null, "Remove permanent data": "स्थायी डेटा हटाएं", "Remove selection from bundle": null, "Remove successful installs/uninstalls/updates from the installation list": "स्थापना सूची से सफल स्थापना/स्थापना रद्द/अद्यतन निकालें", "Removing source {source}": null, "Removing source {source} from {manager}": null, "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": null, "Repository": "कोष", "Reset": "रीसेट", "Reset Scoop's global app cache": null, "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "विंगेट स्रोत रीसेट करें (कोई पैकेज सूचीबद्ध नहीं होने पर मदद मिल सकती है)", "Reset WingetUI": null, "Reset WingetUI and its preferences": "WingetUI  और इसकी प्राथमिकताएं रीसेट करें", "Reset WingetUI icon and screenshot cache": "WingetUI  आइकन और स्क्रीनशॉट कैश को रीसेट करें", "Reset list": null, "Resetting Winget sources - WingetUI": null, "Restart": null, "Restart UniGetUI": null, "Restart WingetUI": "WingetUI  को पुनरारंभ करें", "Restart WingetUI to fully apply changes": null, "Restart later": "बाद में पुनः आरंभ करें", "Restart now": "अब पुनःचालू करें", "Restart required": "पुनरारंभ करना आवश्यक है", "Restart your PC to finish installation": "स्थापना समाप्त करने के लिए अपने कंप्यूटर को पुनरारंभ करें", "Restart your computer to finish the installation": "स्थापना समाप्त करने के लिए अपने कंप्यूटर को पुनरारंभ करें", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "पुन: प्रयास करें", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": null, "Return to top": "ऊपर लौटें", "Run": null, "Run as admin": "\nप्रशासक के रूप में चलाएँ", "Run cleanup and clear cache": null, "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "इंस्टॉलर चल रहा है...", "Running the uninstaller...": "अनइंस्टॉलर चल रहा है...", "Running the updater...": "अपडेटर चल रहा है...", "Save": null, "Save File": "फाइल सुरक्षित करें", "Save and close": null, "Save as": null, "Save bundle as": null, "Save now": null, "Saving packages, please wait...": null, "Scoop Installer - WingetUI": null, "Scoop Uninstaller - WingetUI": null, "Scoop package": "स्कूप पैकेज", "Search": "खोज", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "डेस्कटॉप सॉफ़्टवेयर के लिए खोजें, अपडेट उपलब्ध होने पर मुझे चेतावनी दें और नीरस चीज़ें न करें। मैं नहीं चाहता कि WingetUI ई जटिल हो जाए, मुझे बस एक साधारण <b>सॉफ़्टवेयर स्टोर</b> चाहिए", "Search for packages": "पैकेज खोजें", "Search for packages to start": null, "Search mode": null, "Search on available updates": "उपलब्ध अपडेट पर खोजें", "Search on your software": "अपने सॉफ़्टवेयर पर खोजें", "Searching for installed packages...": "इंस्टॉल किए गए पैकेज खोजे जा रहे हैं...", "Searching for packages...": "\nपैकेज खोजे जा रहे हैं...", "Searching for updates...": "अपडेट खोजे जा रहे हैं...", "Select": null, "Select \"{item}\" to add your custom bucket": "अपनी कस्टम बकेट जोड़ने के लिए \"{item}\" चुनें", "Select a folder": null, "Select all": "सभी चुने", "Select all packages": "सभी पैकेजों का चयन करें", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "केवल <b>यदि आप जानते हैं कि आप क्या कर रहे हैं</b> चुनें।", "Select package file": "पैकेज फ़ाइल का चयन करें", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": null, "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "चुनें कि कौन से <b>पैकेज मेनेजर</b> का उपयोग करना है ({0}), कॉन्फ़िगर करें कि पैकेज कैसे स्थापित किए जाते हैं, प्रबंधित करें कि व्यवस्थापक अधिकार कैसे प्रबंधित किए जाते हैं, आदि।", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "\nहैंडशेका भेजा। इंस्टेंस के लिए लिसनर के उत्तर की प्रतीक्षा की जा रही है... ({0}%)", "Set a custom backup file name": null, "Set custom backup file name": null, "Settings": null, "Share": null, "Share WingetUI": null, "Share anonymous usage data": null, "Share this package": "इस पैकेज को शेयर करें", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": null, "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "WingetUI  दिखाएं", "Show a notification when an installation fails": "स्थापना के विफल होने पर सूचना दिखाएं", "Show a notification when an installation finishes successfully": "स्थापना सफलतापूर्वक पूर्ण होने पर सूचना दिखाएं", "Show a notification when an operation fails": null, "Show a notification when an operation finishes successfully": null, "Show a notification when there are available updates": "अपडेट उपलब्ध होने पर सूचना दिखाएं", "Show a silent notification when an operation is running": null, "Show details": "\nविवरण दिखाएं", "Show in explorer": null, "Show info about the package on the Updates tab": "अपडेट टैब पर पैकेज के बारे में जानकारी दिखाएं", "Show missing translation strings": "अनुपलब्ध अनुवाद स्ट्रिंग दिखाएं", "Show notifications on different events": null, "Show package details": "पैकेज विवरण दिखाएं", "Show package icons on package lists": null, "Show similar packages": null, "Show the live output": "लाइव आउटपुट दिखाएं", "Size": null, "Skip": "छोड़ें", "Skip hash check": "हैश चैक छोड़ दें", "Skip hash checks": null, "Skip integrity checks": null, "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "चयनित पैकेजों को स्थापित करते समय हैश चेक छोड़ें", "Skip the hash check when updating the selected packages": "चयनित पैकेजों को अपडेट करते समय हैश चेक छोड़ें", "Skip this version": "इस संस्करण को छोड़ दें", "Software Updates": "\nसॉफ्टवेयर अपडेट", "Something went wrong": null, "Something went wrong while launching the updater.": null, "Source": "स्रोत", "Source URL:": null, "Source added successfully": null, "Source addition failed": null, "Source name:": null, "Source removal failed": null, "Source removed successfully": null, "Source:": "स्रोत:", "Sources": null, "Start": "शुरू करें", "Starting daemons...": "डेमॉन शुरू करे जा रहे हैं...", "Starting operation...": null, "Startup options": "स्टार्टअप विकल्प", "Status": "स्थति", "Stuck here? Skip initialization": "यहाँ फँस गया? आरंभीकरण छोड़ें", "Suport the developer": "डेवलपर का समर्थन करें", "Support me": null, "Support the developer": null, "Systems are now ready to go!": "सिस्टम अब चलने के लिए तैयार हैं!", "Telemetry": null, "Text": null, "Text file": "पाठ फ़ाइल", "Thank you ❤": null, "Thank you 😉": null, "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": null, "The backup will be performed after login.": null, "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "इंस्टॉलर का चेकसम अपेक्षित मान से मेल नहीं खाता है, और इंस्टॉलर की प्रामाणिकता को सत्यापित नहीं किया जा सकता है। यदि आप प्रकाशक पर भरोसा करते हैं, तो {0} पैकेज फिर से हैश जांच छोड़ देता है।", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": null, "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "निम्नलिखित पैकेजों को JSON फ़ाइल में निर्यात किया जा रहा है। कोई उपयोगकर्ता डेटा या बायनेरिज़ सहेजा नहीं जा रहा है।", "The following packages are going to be installed on your system.": "आपके सिस्टम पर निम्नलिखित पैकेज स्थापित होने जा रहे हैं।", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": null, "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": null, "The icons and screenshots are maintained by users like you!": "आप जैसे उपयोगकर्ताओं द्वारा आइकन और स्क्रीनशॉट का रखरखाव किया जाता है!", "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "इंस्टॉलर के पास अमान्य चेकसम है", "The installer hash does not match the expected value.": null, "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "\nइस परियोजना का मुख्य लक्ष्य विंडोज के लिए सबसे आम सीएलआई पैकेज मेनेजर जैसे विनगेट और स्कूप को प्रबंधित करने के लिए एक सहज यूआई बनाना है।", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": null, "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "अद्यतनों के लिए जाँच करते समय यहाँ सूचीबद्ध पैकेजों को ध्यान में नहीं रखा जाएगा। उनके अद्यतनों को नज़रअंदाज़ करना बंद करने के लिए उन पर डबल-क्लिक करें या उनकी दाईं ओर स्थित बटन क्लिक करें।", "The selected packages have been blacklisted": null, "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": null, "The source {source} was added to {manager} successfully": null, "The source {source} was removed from {manager} successfully": null, "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": null, "The update will not continue.": null, "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": null, "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "YouTube पर कुछ बेहतरीन वीडियो हैं जो WingetUI और इसकी क्षमताओं को प्रदर्शित करते हैं। आप उपयोगी ट्रिक्स और टिप्स सीख सकते हैं!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "WingetUI  को प्रशासक के रूप में नहीं चलाने के दो मुख्य कारण हैं: पहला यह है कि स्कूप पैकेज मेनेजर प्रशासक अधिकारों के साथ चलने पर कुछ कमांड के साथ समस्या पैदा कर सकता है। दूसरा यह है कि WingetUI  को प्रशासक के रूप में चलाने का अर्थ है कि आपके द्वारा डाउनलोड किया जाने वाला कोई भी पैकेज प्रशासक के रूप में चलाया जाएगा (और यह सुरक्षित नहीं है)। याद रखें कि यदि आपको प्रशासक के रूप में एक विशिष्ट पैकेज स्थापित करने की आवश्यकता है, तो आप हमेशा आइटम पर राइट-क्लिक कर सकते हैं -> व्यवस्थापक के रूप में स्थापित/अपडेट/स्थापना रद्द करें।", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "एक स्थापना प्रगति पर है। यदि आप WingetUI  को बंद करते हैं, तो स्थापना विफल हो सकती है और अनपेक्षित परिणाम हो सकते हैं। क्या आप अभी भी विंगेटयूआई छोड़ना चाहते हैं?", "They are the programs in charge of installing, updating and removing packages.": "वे पैकेज को स्थापित करने, अपडेट करने और हटाने के प्रभारी कार्यक्रम हैं।", "Third-party licenses": null, "This could represent a <b>security risk</b>.": "यह <b>सुरक्षा जोखिम</b> का प्रतिनिधित्व हो सकता है।", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "यह शायद इस तथ्य के कारण है कि आपके द्वारा भेजा गया पैकेज हटा दिया गया था, या पैकेज मैनेजर पर प्रकाशित किया गया था जिसे आपने सक्षम नहीं किया है। प्राप्त आईडी {0} है", "This is the <b>default choice</b>.": "यह <b>डिफ़ॉल्ट विकल्प</b> है।", "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": null, "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": null, "This package can be updated to version {0}": null, "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": null, "This package is already installed": null, "This package is being processed": null, "This package is not available": null, "This package is on the queue": null, "This process is running with administrator privileges": "यह प्रक्रिया व्यवस्थापक विशेषाधिकारों के साथ चल रही है", "This project has no connection with the official {0} project — it's completely unofficial.": null, "This setting is disabled": null, "This wizard will help you configure and customize WingetUI!": "यह विज़ार्ड आपको WingetUI को कॉन्फ़िगर और कस्टमाइज़ करने में मदद करेगा!", "Toggle search filters pane": null, "Translators": "\nअनुवादक", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": null, "Unable to find package": "पैकेज नहीं मिल सका", "Unable to load informarion": "सूचना लोड करने में असमर्थ", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": null, "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "स्थापना रद्द करें", "Uninstall Scoop (and its packages)": "स्कूप (और उसके पैकेज) की स्थापना रद्द करें", "Uninstall and more": null, "Uninstall and remove data": null, "Uninstall as administrator": "प्रशासक के रूप में स्थापना रद्द करें", "Uninstall canceled by the user!": "उपयोगकर्ता द्वारा स्थापना रद्द रोकी गयी!", "Uninstall failed": null, "Uninstall options": null, "Uninstall package": "\nपैकेज की स्थापना रद्द करें", "Uninstall package, then reinstall it": null, "Uninstall package, then update it": null, "Uninstall previous versions when updated": null, "Uninstall selected packages": "चुने पैकेज की स्थापना रद्द करें", "Uninstall selection": null, "Uninstall succeeded": null, "Uninstall the selected packages with administrator privileges": "चयनित पैकेजों को व्यवस्थापक विशेषाधिकारों के साथ अनइंस्टॉल करें", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "\"{0}\" के रूप में सूचीबद्ध मूल वाले अनइंस्टॉल करने योग्य पैकेज किसी भी पैकेज प्रबंधक पर प्रकाशित नहीं होते हैं, इसलिए उनके बारे में दिखाने के लिए कोई जानकारी उपलब्ध नहीं है।", "Unknown": "अज्ञात", "Unknown size": null, "Unset or unknown": null, "Up to date": null, "Update": "अपडेट", "Update WingetUI automatically": "WingetUI  को स्वचालित रूप से अपडेट करें", "Update all": "सभी अपडेट करें", "Update and more": null, "Update as administrator": "प्रशासक के रूप में अपडेट करें", "Update check frequency, automatically install updates, etc.": null, "Update date": "डेट अपडेट करें", "Update failed": null, "Update found!": "अपडेट मिला!", "Update now": null, "Update options": null, "Update package indexes on launch": null, "Update packages automatically": "संकुल स्वचालित रूप से अद्यतन करें", "Update selected packages": "Update selected packages", "Update selected packages with administrator privileges": "चयनित पैकेजों को व्यवस्थापिक विशेषाधिकारों के साथ अद्यतन करें", "Update selection": null, "Update succeeded": null, "Update to version {0}": null, "Update to {0} available": "{0} में अपडेट उपलब्ध है", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "अपडेट", "Updates available!": null, "Updates for this package are ignored": null, "Updates found!": "अपडेट मिले!", "Updates preferences": null, "Updating WingetUI": "WingetUI  को अपडेट कर रहा है", "Url": null, "Use Legacy bundled WinGet instead of PowerShell CMDLets": null, "Use a custom icon and screenshot database URL": null, "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "बंडल किए गए जीसूडो के बजाय इंस्टॉल किए गए जीसूडो का उपयोग करें (ऐप पुनरारंभ करने की आवश्यकता है)", "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": "सिस्टम चॉकलेट का प्रयोग करें (पुनः आरंभ करने की आवश्यकता है)", "Use system Winget (Needs a restart)": "सिस्टम विंगेट का उपयोग करें (पुनरारंभ करने की आवश्यकता है)", "Use system Winget (System language must be set to english)": null, "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": null, "User": "उपयोगकर्ता", "User interface preferences": "उपयोगकर्ता इंटरफ़ेस प्राथमिकताएँ", "User | Local": null, "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": null, "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": "संस्करण", "Version to install:": "संस्करण स्थापित करने के लिए:", "Version:": null, "View GitHub Profile": null, "View WingetUI on GitHub": "GitHub पर WingetUI देखें", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "WingetUI का सोर्स कोड देखें। वहां से, आप बग की रिपोर्ट कर सकते हैं या सुविधाओं का सुझाव दे सकते हैं, या यहां तक कि WingetUI प्रोजेक्ट में सीधे योगदान कर सकते हैं", "View mode:": null, "View on UniGetUI": null, "View page on browser": null, "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "अन्य स्थापनाओं के समाप्त होने की प्रतीक्षा की जा रही है...", "Waiting for {0} to complete...": null, "Warning": "चेतावनी", "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": "हम इस पैकेज के बारे में विस्तृत जानकारी लोड नहीं कर सके, क्योंकि यह आपके किसी भी पैकेज स्रोत में नहीं मिला था", "We could not load detailed information about this package, because it was not installed from an available package manager.": "हम इस पैकेज के बारे में विस्तृत जानकारी लोड नहीं कर सके, क्योंकि इसे किसी उपलब्ध पैकेज मैनेजर से स्थापित नहीं किया गया था।", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "हम {action} {package} नहीं कर सके। कृपया बाद में पुन: प्रयास करें। इंस्टॉलर से लॉग प्राप्त करने के लिए \"{showDetails}\" पर क्लिक करें।", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "हम {action} {package} नहीं कर सके। कृपया बाद में पुन: प्रयास करें। अनइंस्टॉलर से लॉग प्राप्त करने के लिए \"{showDetails}\" पर क्लिक करें।", "We couldn't find any package": null, "Welcome to WingetUI": "WingetUI  में आपका स्वागत है", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "आप किस पैकेज म,मेनेजर का उपयोग करना चाहते हैं?", "Which source do you want to add?": null, "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": null, "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": null, "WingetUI - Everything is up to date": "WingetUI  - सब कुछ अप टू डेट है", "WingetUI - {0} updates are available": "WingetUI  - {0} अपडेट उपलब्ध हैं", "WingetUI - {0} {1}": "WingetUI  - {0} {1}", "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": null, "WingetUI Log": null, "WingetUI Repository": null, "WingetUI Settings": "WingetUI  की सेटिंग", "WingetUI Settings File": null, "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": null, "WingetUI autostart behaviour, application launch settings": "WingetUI  ऑटोस्टार्ट व्यवहार, एप्लिकेशन लॉन्च सेटिंग्स", "WingetUI can check if your software has available updates, and install them automatically if you want to": null, "WingetUI display language:": "WingetUI  की प्रदर्शन भाषा", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": null, "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": null, "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI  का मशीनी अनुवाद नहीं किया गया है। निम्नलिखित उपयोगकर्ता अनुवादों के प्रभारी रहे हैं:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI  को अपडेट किया जा रहा है। समाप्त होने पर, विंगेटयूआई स्वयं को पुनः आरंभ करेगा", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": null, "WingetUI log": "WingetUI  की लॉग", "WingetUI tray application preferences": "\nWingetUI  ट्रे एप्लिकेशन प्राथमिकताएं", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": null, "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI  समय-समय पर अपडेट की जांच नहीं करेगा। लॉन्च के समय भी उनकी जांच की जाएगी, लेकिन आपको उनके बारे में चेतावनी नहीं दी जाएगी।", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI  हर बार एक UAC संकेत दिखाएगा जब किसी पैकेज को स्थापित करने के लिए उन्नयन की आवश्यकता होगी।", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI हमारे प्रिय योगदानकर्ताओं की मदद के बिना संभव नहीं हो पाता। उनकी Github प्रोफ़ाइल देखें, WingetUI उनके बिना संभव नहीं होताा!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": null, "WingetUI {0} is ready to be installed.": null, "Write here the process names here, separated by commas (,)": null, "Yes": "हाँ", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": null, "You have installed WingetUI Version {0}": null, "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": "आप चाहें तो अपने कंप्यूटर को बाद में रीस्टार्ट कर सकते हैं", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "आपको केवल एक बार संकेत दिया जाएगा, और व्यवस्थापक अधिकार उन पैकेजों को दिए जाएंगे जो उनका अनुरोध करते हैं।", "You will be prompted only once, and every future installation will be elevated automatically.": "आपको केवल एक बार संकेत दिया जाएगा, और भविष्य की प्रत्येक स्थापना स्वचालित रूप से उन्नत हो जाएगी।", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": "मेरे लिए एक कॉफी खरीदो", "extracted": null, "feature": null, "formerly WingetUI": null, "homepage": "\nहोमपेज", "install": "स्थापित", "installation": "स्थापना", "installed": "स्थापित", "installing": "स्थापना", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "स्थापना रद्द", "uninstallation": "स्थापना रद्द", "uninstalled": "स्थापना रद्द हुई", "uninstalling": "स्थापना रद्द हो रही है", "update(noun)": "अपडेट(संज्ञा)", "update(verb)": "अद्यतन", "updated": "अपडेट किया गया", "updating": "अपडेट किया जा रहा है", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} स्थापना रद्द", "{0} aborted": "{0} रद्द किया गया", "{0} can be updated": "{0} अद्यतन किया जा सकता है", "{0} can be updated to version {1}": null, "{0} days": "{0} दिन", "{0} desktop shortcuts created": null, "{0} failed": "{0} असफल", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} घंटे", "{0} installation": "\n{0} स्थापना", "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} अपडेट किया जा रहा है", "{0} is being updated to version {1}": null, "{0} is disabled": "{0} अक्षम है", "{0} minutes": "{0} मिनट", "{0} months": null, "{0} packages are being updated": "{0} पैकेज अपडेट किए जा रहे हैं", "{0} packages can be updated": "{0} संकुल अद्यतन किया जा सकता है", "{0} packages found": "\n{0} पैकेज मिले", "{0} packages were found": "{0} पैकेज मिले", "{0} packages were found, {1} of which match the specified filters.": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} सफल हुआ", "{0} update": "{0} अपडेट करें", "{0} updates are available": null, "{0} was {1} successfully!": "\n{0} सफलतापूर्वक {1} हुआ!", "{0} weeks": null, "{0} years": null, "{0} {1} failed": "{0} {1} असफल रहा", "{package} Installation": null, "{package} Uninstall": null, "{package} Update": null, "{package} could not be installed": null, "{package} could not be uninstalled": null, "{package} could not be updated": null, "{package} installation failed": null, "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": null, "{package} update failed": null, "{package} update failed. Click here for more details.": null, "{package} was installed successfully": null, "{package} was uninstalled successfully": null, "{package} was updated successfully": null, "{pcName} installed packages": null, "{pm} could not be found": null, "{pm} found: {state}": "{pm} मिले: {state}", "{pm} is disabled": null, "{pm} is enabled and ready to go": null, "{pm} package manager specific preferences": "{pm} पैकेज प्रबंधक विशिष्ट प्राथमिकताएँ", "{pm} preferences": "{pm} वरीयताएँ", "{pm} version:": null, "{pm} was not found!": null}