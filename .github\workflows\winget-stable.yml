name: Publish stable releases to WinGet
on:
  release:
    types: [released]
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: vedantmgoyal9/winget-releaser@19e706d4c9121098010096f9c495a70a7518b30f
        with:
          identifier: MartiCliment.UniGetUI
          installers-regex: 'UniGetUI\.Installer\.exe$'
          version: ${{ github.event.release.tag_name }}
          token: ${{ secrets.WINGET_TOKEN }}
