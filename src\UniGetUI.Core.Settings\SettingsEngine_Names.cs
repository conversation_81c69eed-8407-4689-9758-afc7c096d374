namespace UniGetUI.Core.SettingsEngine;

public static partial class Settings
{
    public enum K
    {
        EnableProxy,
        EnablePro<PERSON>Auth,
        DisableWaitForInternetConnection,
        IconDataBaseURL,
        DisableLangAutoUpdater,
        DisableDMWThreadOptimizations,
        DisableTelemetry,
        DisableTimeoutOnPackageListingTasks,
        RemoveAllDesktopShortcuts,
        EnableScoopCleanup,
        IgnoreUpdatesNotApplicable,
        ForceLegacyBundledWinGet,
        DisableNewWinGetTroubleshooter,
        DisableUpdateVcpkgGitPorts,
        ChocolateySymbolicLinkCreated,
        UseSystemChocolatey,
        DisableSelectingUpdatesByDefault,
        DisableAutoCheckforUpdates,
        AskToDeleteNewDesktopShortcuts,
        DoCacheAdminRights,
        DoCacheAdminRightsForBatches,
        DisableAutoUpdateWingetUI,
        EnableUniGetUIBeta,
        ShowVersionNumberOnTitlebar,
        TransferredOldSettings,
        DisableApi,
        DisableSystemTray,
        MaintainSuccessfulInstalls,
        AutomaticallyUpdatePackages,
        DisableAUPOnMeteredConnections,
        DisableAUPOnBatterySaver,
        CustomVcpkgRoot,
        AlreadyWarnedAboutAdmin,
        ShownTelemetryBanner,
        CollapseNavMenuOnWideScreen,
        EnablePackageBackup_LOCAL,
        EnablePackageBackup_CLOUD,
        ChangeBackupOutputDirectory,
        DisableWinGetMalfunctionDetector,
        EnableBackupTimestamping,
        DisableIconsOnPackageLists,
        ProxyURL,
        ProxyUsername,
        PreferredLanguage,
        DefaultVcpkgTriplet,
        UpdatesCheckInterval,
        ParallelOperationCount,
        TelemetryClientToken,
        PreferredTheme,
        OperationHistory,
        StartupPage,
        WindowGeometry,
        ChangeBackupFileName,
        CurrentSessionToken,
        IgnoredPackageUpdates,
        DisabledManagers,
        DeletableDesktopShortcuts,
        WinGetAlreadyUpgradedPackages,
        DependencyManagement,
        DisabledPackageManagerNotifications,
        PackageListViewMode,
        DisableInstantSearch,
        SidepanelWidths,
        HideToggleFilters,
        FreshBoolSetting,
        FreshValue,
        DisableNotifications,
        DisableUpdatesNotifications,
        DisableErrorNotifications,
        DisableSuccessNotifications,
        DisableProgressNotifications,
        KillProcessesThatRefuseToDie,
        ManagerPaths,
        GitHubUserLogin,
        DisableNewProcessLineHandler,
        InstallInstalledPackagesBundlesPage,
        ProhibitElevation,
        DisableIntegrityChecks,

        Test1,
        Test2,
        Test3,
        Test4,
        Test5,
        Test6,
        Test7_Legacy,
        Unset
    };

    public static string ResolveKey(K key)
    {
        return key switch
        {
            K.EnableProxy => "EnableProxy",
            K.EnableProxyAuth => "EnableProxyAuth",
            K.DisableWaitForInternetConnection => "DisableWaitForInternetConnection",
            K.IconDataBaseURL => "IconDataBaseURL",
            K.DisableLangAutoUpdater => "DisableLangAutoUpdater",
            K.DisableDMWThreadOptimizations => "DisableDMWThreadOptimizations",
            K.DisableTelemetry => "DisableTelemetry",
            K.DisableTimeoutOnPackageListingTasks => "DisableTimeoutOnPackageListingTasks",
            K.RemoveAllDesktopShortcuts => "RemoveAllDesktopShortcuts",
            K.EnableScoopCleanup => "EnableScoopCleanup",
            K.IgnoreUpdatesNotApplicable => "IgnoreUpdatesNotApplicable",
            K.ForceLegacyBundledWinGet => "ForceLegacyBundledWinGet",
            K.DisableNewWinGetTroubleshooter => "DisableNewWinGetTroubleshooter",
            K.DisableUpdateVcpkgGitPorts => "DisableUpdateVcpkgGitPorts",
            K.ChocolateySymbolicLinkCreated => "ChocolateySymbolicLinkCreated",
            K.UseSystemChocolatey => "UseSystemChocolatey",
            K.DisableSelectingUpdatesByDefault => "DisableSelectingUpdatesByDefault",
            K.DisableAutoCheckforUpdates => "DisableAutoCheckforUpdates",
            K.AskToDeleteNewDesktopShortcuts => "AskToDeleteNewDesktopShortcuts",
            K.DoCacheAdminRights => "DoCacheAdminRights",
            K.DoCacheAdminRightsForBatches => "DoCacheAdminRightsForBatches",
            K.DisableAutoUpdateWingetUI => "DisableAutoUpdateWingetUI",
            K.EnableUniGetUIBeta => "EnableUniGetUIBeta",
            K.ShowVersionNumberOnTitlebar => "ShowVersionNumberOnTitlebar",
            K.TransferredOldSettings => "TransferredOldSettings",
            K.DisableApi => "DisableApi",
            K.DisableSystemTray => "DisableSystemTray",
            K.MaintainSuccessfulInstalls => "MaintainSuccessfulInstalls",
            K.AutomaticallyUpdatePackages => "AutomaticallyUpdatePackages",
            K.DisableAUPOnMeteredConnections => "DisableAUPOnMeteredConnections",
            K.DisableAUPOnBatterySaver => "DisableAUPOnBatterySaver",
            K.CustomVcpkgRoot => "CustomVcpkgRoot",
            K.AlreadyWarnedAboutAdmin => "AlreadyWarnedAboutAdmin",
            K.ShownTelemetryBanner => "ShownTelemetryBanner",
            K.CollapseNavMenuOnWideScreen => "CollapseNavMenuOnWideScreen",
            K.EnablePackageBackup_LOCAL => "EnablePackageBackup",
            K.EnablePackageBackup_CLOUD => "EnablePackageBackup_CLOUD",
            K.ChangeBackupOutputDirectory => "ChangeBackupOutputDirectory",
            K.DisableWinGetMalfunctionDetector => "DisableWinGetMalfunctionDetector",
            K.EnableBackupTimestamping => "EnableBackupTimestamping",
            K.DisableIconsOnPackageLists => "DisableIconsOnPackageLists",
            K.ProxyURL => "ProxyURL",
            K.ProxyUsername => "ProxyUsername",
            K.PreferredLanguage => "PreferredLanguage",
            K.DefaultVcpkgTriplet => "DefaultVcpkgTriplet",
            K.UpdatesCheckInterval => "UpdatesCheckInterval",
            K.ParallelOperationCount => "ParallelOperationCount",
            K.TelemetryClientToken => "TelemetryClientToken",
            K.PreferredTheme => "PreferredTheme",
            K.OperationHistory => "OperationHistory",
            K.StartupPage => "StartupPage",
            K.WindowGeometry => "WindowGeometry",
            K.ChangeBackupFileName => "ChangeBackupFileName",
            K.CurrentSessionToken => "CurrentSessionToken",
            K.IgnoredPackageUpdates => "IgnoredPackageUpdates",
            K.DisabledManagers => "DisabledManagers",
            K.DeletableDesktopShortcuts => "DeletableDesktopShortcuts",
            K.WinGetAlreadyUpgradedPackages => "WinGetAlreadyUpgradedPackages",
            K.DependencyManagement => "DependencyManagement",
            K.DisabledPackageManagerNotifications => "DisabledPackageManagerNotifications",
            K.PackageListViewMode => "PackageListViewMode",
            K.DisableInstantSearch => "DisableInstantSearch",
            K.SidepanelWidths => "SidepanelWidths",
            K.HideToggleFilters => "HideToggleFilters",
            K.FreshBoolSetting => "FreshBoolSetting",
            K.FreshValue => "FreshValue",
            K.DisableNotifications => "DisableNotifications",
            K.DisableUpdatesNotifications => "DisableUpdatesNotifications",
            K.DisableErrorNotifications => "DisableErrorNotifications",
            K.DisableSuccessNotifications => "DisableSuccessNotifications",
            K.DisableProgressNotifications => "DisableProgressNotifications",
            K.KillProcessesThatRefuseToDie => "KillProcessesThatRefuseToDie",
            K.ManagerPaths => "ManagerPaths",
            K.GitHubUserLogin => "GitHubUserLogin",
            K.DisableNewProcessLineHandler => "DisableNewProcessLineHandler",
            K.InstallInstalledPackagesBundlesPage => "InstallInstalledPackagesBundlesPage",
            K.ProhibitElevation => "ProhibitElevation",
            K.DisableIntegrityChecks => "DisableIntegrityChecks",

            K.Test1 => "TestSetting1",
            K.Test2 => "TestSetting2",
            K.Test3 => "Test.Settings_with",
            K.Test4 => "TestSettingEntry With A  Space",
            K.Test5 => "ª",
            K.Test6 => "VeryVeryLongTestSettingEntrySoTheClassCanReallyBeStressedOut",
            K.Test7_Legacy => "LegacyBoolSetting",
            K.Unset => throw new InvalidDataException("Setting key was unset!"),
            _ => throw new KeyNotFoundException($"The settings key {key} was not found on the ResolveKey map")
        };
    }
}
