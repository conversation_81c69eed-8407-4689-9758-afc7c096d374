{"\"{0}\" is a local package and can't be shared": "\"{0}\" adalah paket lokal dan tidak dapat dibagikan", "\"{0}\" is a local package and does not have available details": "\"{0}\" adalah paket lokal dan tidak memiliki detail yang tersedia", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" adalah paket lokal dan tidak kompatibel dengan fitur ini", "(Last checked: {0})": "(<PERSON><PERSON><PERSON>eri<PERSON>: {0})", "(Number {0} in the queue)": "(Nomor {0} da<PERSON> antrean)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@agrinfauzi, @arthackrc, @joenior, @nrarfn", "0 packages found": "0 paket di<PERSON>n", "0 updates found": "0 pembaruan ditemukan", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 hari", "1 hour": "1 jam", "1 month": "1 bulan", "1 package was found": "1 paket di<PERSON><PERSON>n", "1 update is available": "1 pembaruan tersedia", "1 week": "1 minggu", "1 year": "1 tahun", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON> \"{0}\" atau \"{1}\".", "2 - Warnings": "2 - <PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON> paket yang ingin Anda tamba<PERSON>kan ke bundel, lalu centang kotak paling kiri.", "3 - Information (less)": "3 - Informasi (sedikit)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON><PERSON> paket yang ingin ditambahkan ke bundel dipilih, klik opsi \"{0}\" di toolbar.", "4 - Information (more)": "4 - Informasi (lebih banyak)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON>a telah ditambahkan ke bundel. <PERSON>a dapat menambahkan lebih banyak paket atau mengekspor bundel.", "5 - information (debug)": "5 - <PERSON><PERSON><PERSON> (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Manajer pustaka C/C++ yang populer. Berisi banyak pustaka C/C++ dan utilitas terkait lainnya<br><PERSON><PERSON><PERSON>: <b>pustaka dan utilitas C/C++</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Repositori penuh dengan alat dan file eksekusi yang dirancang untuk ekosistem Microsoft .NET.<br><PERSON><PERSON><PERSON>: <b>alat dan skrip terkait .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Repositori yang penuh dengan alat yang dirancang untuk ekosistem Microsoft .NET.<br><PERSON><PERSON>i: <b>Alat terkait .NET</b>", "A restart is required": "<PERSON><PERSON><PERSON><PERSON> restart", "Abort install if pre-install command fails": "Batalkan instalasi jika perintah pra-instalasi gagal", "Abort uninstall if pre-uninstall command fails": "Batalkan penghapusan jika perintah pra-penghapusan gagal", "Abort update if pre-update command fails": "Batalkan pembaruan jika perintah pra-pembaruan gagal", "About": "Tentang", "About Qt6": "Tentang Qt6", "About WingetUI": "Tentang UniGetUI", "About WingetUI version {0}": "Tentang UniGetUI versi {0}", "About the dev": "Tentang pengembang", "Accept": "Terima", "Action when double-clicking packages, hide successful installations": "Tindakan saat paket diklik dua kali, sembunyikan instalasi yang ber<PERSON>il", "Add": "Tambah", "Add a source to {0}": "Tambahkan sumber ke {0}", "Add a timestamp to the backup file names": "Tambahkan penanda waktu pada nama file cadangan", "Add a timestamp to the backup files": "Tambahkan penanda waktu pada file cadangan", "Add packages or open an existing bundle": "Tambahkan paket atau buka bundel yang sudah ada", "Add packages or open an existing package bundle": "Tambahkan paket atau buka bundel paket yang sudah ada", "Add packages to bundle": "Tambahkan paket ke bundel", "Add packages to start": "Tambahkan paket ke awal", "Add selection to bundle": "Tambahkan pilihan ke bundel", "Add source": "Tambah sumber", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Tambahkan pembaruan yang gagal dengan pesan 'tidak ditemukan pembaruan yang sesuai' ke daftar yang diabaikan", "Adding source {source}": "Menambahkan sumber {source}", "Adding source {source} to {manager}": "Menambahkan sumber {source} ke {manager}", "Addition succeeded": "<PERSON><PERSON><PERSON><PERSON>", "Administrator privileges": "Hak administrator", "Administrator privileges preferences": "Preferensi hak administrator", "Administrator rights": "Hak administrator", "Administrator rights and other dangerous settings": "Hak Administrator dan pen<PERSON>n lan<PERSON>", "Advanced options": "Opsi lanjutan", "All files": "Semua file", "All versions": "<PERSON><PERSON><PERSON> versi", "Allow changing the paths for package manager executables": "Izinkan mengubah jalur eksekusi untuk manajer paket", "Allow custom command-line arguments": "Mengizinkan argumen perintah khusus", "Allow importing custom command-line arguments when importing packages from a bundle": "Izinkan mengimpor argumen perintah khusus saat mengimpor paket dari bundel", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Izinkan mengimpor perintah pra-instal dan pasca-instal khusus saat mengimpor paket dari bundel", "Allow package operations to be performed in parallel": "Izinkan operasi paket dija<PERSON>an secara paralel", "Allow parallel installs (NOT RECOMMENDED)": "Izinkan instalasi paralel (TIDAK DISARANKAN)", "Allow pre-release versions": "Izinkan versi pra-rilis", "Allow {pm} operations to be performed in parallel": "Izinkan <PERSON>i {pm} di<PERSON>ukan secara paralel", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Sebagai alternatif, <PERSON><PERSON> juga dapat menginstal {0} dengan menja<PERSON>an perintah berikut di jendela PowerShell Windows:", "Always elevate {pm} installations by default": "<PERSON><PERSON><PERSON> ting<PERSON>kan instalasi {pm} secara default", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON> j<PERSON> {pm} dengan hak administrator", "An error occurred": "<PERSON><PERSON><PERSON><PERSON>", "An error occurred when adding the source: ": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menambahkan sumber: ", "An error occurred when attempting to show the package with Id {0}": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mencoba menampilkan paket dengan ID {0}", "An error occurred when checking for updates: ": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat memeriksa pembaruan: ", "An error occurred while attempting to create an installation script:": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mencoba membuat skrip instalasi:", "An error occurred while loading a backup: ": "<PERSON><PERSON><PERSON><PERSON> kesalahan saat memuat cadangan:", "An error occurred while logging in: ": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat masuk:", "An error occurred while processing this package": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat memproses paket ini", "An error occurred:": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON>:", "An interal error occurred. Please view the log for further details.": "Te<PERSON><PERSON><PERSON> k<PERSON>alahan internal. Silakan lihat log untuk detail lebih lanjut.", "An unexpected error occurred:": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han yang tidak terduga:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Masalah tak terduga terjadi saat mencoba memperbaiki WinGet. <PERSON>lakan coba lagi nanti", "An update was found!": "Pembaruan ditemukan!", "Android Subsystem": "Subsistem Android", "Another source": "Sumber lain", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Setiap pintasan baru yang dibuat selama instalasi atau pembaruan akan dihapus secara otomatis, alih-alih menampilkan konfirmasi saat pertama kali terdeteksi.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Setiap pintasan yang dibuat atau diubah di luar UniGetUI akan diabaikan. Anda dapat menambahkannya melalui tombol {0}.", "Any unsaved changes will be lost": "<PERSON>bahan yang belum disimpan akan hilang", "App Name": "<PERSON><PERSON>", "Appearance": "Tampilan", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> a<PERSON>, <PERSON><PERSON> p<PERSON>, be<PERSON><PERSON><PERSON> instalasi yang ber<PERSON>il secara otomatis", "Application theme:": "<PERSON><PERSON>:", "Apply": "Terapkan", "Architecture to install:": "Arsitektur yang akan diinstal:", "Are these screenshots wron or blurry?": "A<PERSON><PERSON>h tangkapan layar ini salah atau buram?", "Are you really sure you want to enable this feature?": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin mengaktifkan fitur ini?", "Are you sure you want to create a new package bundle? ": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin membuat bundel paket baru? ", "Are you sure you want to delete all shortcuts?": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus semua pintasan?", "Are you sure?": "<PERSON><PERSON><PERSON><PERSON> Anda yakin?", "Ascendant": "<PERSON><PERSON>", "Ask for administrator privileges once for each batch of operations": "Minta hak administrator satu kali untuk setiap kelompok operasi", "Ask for administrator rights when required": "Minta hak administrator saat di<PERSON><PERSON><PERSON>", "Ask once or always for administrator rights, elevate installations by default": "Minta hak administrator se<PERSON><PERSON> se<PERSON>, tingkatkan instalasi secara default", "Ask only once for administrator privileges": "Tanyakan hanya sekali untuk mendapatkan hak istimewa administrator", "Ask only once for administrator privileges (not recommended)": "Minta hak administrator hanya se<PERSON> (tidak disarankan)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Tanyakan untuk menghapus pintasan desktop yang dibuat selama instalasi atau pembaruan.", "Attention required": "<PERSON><PERSON>", "Authenticate to the proxy with an user and a password": "Otentikasi ke proxy dengan pengguna dan kata sandi", "Author": "<PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "Penghapus pintasan desktop otomatis", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Simpan daftar semua paket terinstal secara otomatis untuk memudahkan pemulihan.", "Automatically save a list of your installed packages on your computer.": "Simpan daftar paket terinstal Anda secara otomatis di komputer.", "Autostart WingetUI in the notifications area": "<PERSON><PERSON> otomatis UniGetUI di area notifikasi", "Available Updates": "Pembaruan Tersedia", "Available updates: {0}": "Pembaruan tersedia: {0}", "Available updates: {0}, not finished yet...": "Pembaruan tersedia: {0}, belum selesai...", "Backing up packages to GitHub Gist...": "Mencadangkan paket ke GitHub Gist...", "Backup": "Cadangan", "Backup Failed": "Pencadangan Gagal", "Backup Successful": "Pencadangan Berhasil", "Backup and Restore": "Pencadangan dan <PERSON>", "Backup installed packages": "Cadangkan paket yang terinstal", "Backup location": "Lokasi cadangan", "Become a contributor": "<PERSON><PERSON><PERSON> kontributor", "Become a translator": "<PERSON><PERSON><PERSON>", "Begin the process to select a cloud backup and review which packages to restore": "<PERSON><PERSON> proses untuk memilih cadangan cloud dan tinjau paket mana yang akan dipulihkan", "Beta features and other options that shouldn't be touched": "Fitur beta dan opsi lainnya yang sebaiknya tidak diubah", "Both": "Keduanya", "Bundle security report": "<PERSON><PERSON><PERSON> keamanan bundel", "But here are other things you can do to learn about WingetUI even more:": "<PERSON><PERSON>, berikut hal lain yang dapat Anda lakukan untuk mempelajari lebih lanjut tentang UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "<PERSON>gan menonakt<PERSON><PERSON> pen<PERSON> paket, <PERSON><PERSON> tidak akan dapat melihat atau memperbarui paketnya.", "Cache administrator rights and elevate installers by default": "Simpan hak administrator dan ting<PERSON> installer secara default", "Cache administrator rights, but elevate installers only when required": "Simpan hak administrator, tetapi hanya tingkatkan installer sa<PERSON> di<PERSON><PERSON><PERSON>", "Cache was reset successfully!": "Cache berhasil diatur ulang!", "Can't {0} {1}": "Tidak dapat {0} {1}", "Cancel": "<PERSON><PERSON>", "Cancel all operations": "Batalkan semua <PERSON>i", "Change backup output directory": "Ubah direktori keluaran cadangan", "Change default options": "Ubah opsi bawaan", "Change how UniGetUI checks and installs available updates for your packages": "Ubah cara UniGetUI memeriksa dan menginstal pembaruan yang tersedia untuk paket Anda", "Change how UniGetUI handles install, update and uninstall operations.": "Ubah cara UniGetUI menangani operasi instalasi, pem<PERSON><PERSON>, dan pen<PERSON>.", "Change how UniGetUI installs packages, and checks and installs available updates": "Ubah cara UniGetUI menginstal paket, serta memeriksa dan menginstal pembaruan", "Change how operations request administrator rights": "Ubah bagaimana operasi meminta hak administrator", "Change install location": "Ubah lokasi instalasi", "Change this": "Ubah ini", "Change this and unlock": "Ubah ini dan buka kunci", "Check for package updates periodically": "Periksa pembaruan paket secara berkala", "Check for updates": "<PERSON><PERSON><PERSON> pem<PERSON>", "Check for updates every:": "Periksa pembaruan setiap:", "Check for updates periodically": "Periksa pembaruan secara berkala.", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON><PERSON><PERSON> pembaruan secara rutin, dan tanyakan kepada saya apa yang harus dilakukan saat pembaruan ditemukan.", "Check for updates regularly, and automatically install available ones.": "<PERSON><PERSON><PERSON> pembaruan secara rutin, dan instal pembaruan yang tersedia secara otomatis.", "Check out my {0} and my {1}!": "Lihat {0} dan {1} saya!", "Check out some WingetUI overviews": "<PERSON><PERSON> beberapa ulasan UniGetUI", "Checking for other running instances...": "Memeriksa instance lain yang sedang berjalan...", "Checking for updates...": "Memeriksa pembaruan...", "Checking found instace(s)...": "Memeriksa instance yang di<PERSON>n...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON> berapa banyak <PERSON>i yang dilakukan secara paralel", "Clear cache": "Bersih<PERSON> cache", "Clear finished operations": "<PERSON><PERSON><PERSON><PERSON>i yang telah se<PERSON>ai", "Clear selection": "<PERSON><PERSON> p<PERSON>", "Clear successful operations": "<PERSON><PERSON><PERSON><PERSON> yang ber<PERSON>il", "Clear successful operations from the operation list after a 5 second delay": "Hapus operasi yang berhasil dari daftar setelah jeda 5 detik", "Clear the local icon cache": "Bersihkan cache ikon lokal", "Clearing Scoop cache - WingetUI": "Membersihkan cache Scoop - UniGetUI", "Clearing Scoop cache...": "Sedang membersihkan cache Scoop...", "Click here for more details": "Klik di sini untuk detail lebih lanjut", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "<PERSON>lik Instal untuk memulai proses instalasi. <PERSON><PERSON>, UniGetUI mungkin tidak berfungsi sebagaimana mestinya.", "Close": "<PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Tutup UniGetUI ke tray sistem", "Close WingetUI to the notification area": "Tutup UniGetUI ke area notifikasi", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Pencadangan cloud menggunakan GitHub Gist pribadi untuk menyimpan daftar paket yang diinstal", "Cloud package backup": "Pencadangan paket cloud", "Command-line Output": "<PERSON><PERSON><PERSON> baris perintah", "Command-line to run:": "<PERSON><PERSON><PERSON> yang akan di<PERSON>:", "Compare query against": "<PERSON>ing<PERSON> kueri dengan", "Compatible with authentication": "Kompatibel dengan autentikasi", "Compatible with proxy": "Kompatibel dengan proxy", "Component Information": "Informasi Komponen", "Concurrency and execution": "Konkuren dan eks<PERSON><PERSON>i", "Connect the internet using a custom proxy": "Hubungkan internet menggunakan proxy kustom", "Continue": "Lanjutkan", "Contribute to the icon and screenshot repository": "Berkontribusi ke repositori ikon dan tangkapan layar", "Contributors": "Kontributor", "Copy": "<PERSON><PERSON>", "Copy to clipboard": "<PERSON>in ke papan klip", "Could not add source": "Tidak dapat menambahkan sumber", "Could not add source {source} to {manager}": "Tidak dapat menambahkan sumber {source} ke {manager}", "Could not back up packages to GitHub Gist: ": "Tidak dapat mencadangkan paket ke GitHub Gist:", "Could not create bundle": "Tidak dapat membuat bundel", "Could not load announcements - ": "Tidak dapat memuat pen<PERSON> - ", "Could not load announcements - HTTP status code is $CODE": "Tidak dapat memuat pengumuman - Kode status HTTP adalah $CODE", "Could not remove source": "Tidak dapat menghapus sumber", "Could not remove source {source} from {manager}": "Tidak dapat menghapus sumber {source} dari {manager}", "Could not remove {source} from {manager}": "Tidak dapat menghapus {source} dari {manager}", "Create .ps1 script": "Buat skrip .ps1", "Credentials": "Kredensial", "Current Version": "Versi Saat Ini", "Current status: Not logged in": "Status saat ini: <PERSON><PERSON> masuk", "Current user": "Pengguna saat ini", "Custom arguments:": "Argumen kustom:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Argumen perintah khusus dapat mengubah cara program diinstal, diupgrade, atau dihapus, dengan cara yang tidak dapat dikontrol oleh UniGetUI. Menggunakan perintah khusus dapat merusak paket. Lanjutkan dengan hati-hati.", "Custom command-line arguments:": "Argumen baris perintah kustom:", "Custom install arguments:": "Argumen pen<PERSON> khus<PERSON>:", "Custom uninstall arguments:": "Argumen hapus instalan khusus:", "Custom update arguments:": "Argumen pembaruan khusus:", "Customize WingetUI - for hackers and advanced users only": "Kustomisasi UniGetUI - hanya untuk pengguna lanjutan dan pengembang", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "DISKLAIMER: <PERSON>AMI TIDAK BERTANGGUNG JAWAB ATAS PAKET YANG DIUNDUH. PASTIKAN HANYA MENGINSTAL PERANGKAT LUNAK YANG TERPERCAYA.", "Dark": "<PERSON><PERSON><PERSON>", "Decline": "<PERSON><PERSON>", "Default": "<PERSON><PERSON><PERSON>", "Default installation options for {0} packages": "Opsi penginstalan default untuk paket {0}", "Default preferences - suitable for regular users": "Preferensi bawaan - cocok untuk pengguna umum", "Default vcpkg triplet": "Triplet vcpkg default", "Delete?": "Hapus?", "Dependencies:": "Dependensi:", "Descendant": "<PERSON><PERSON><PERSON>", "Description:": "Deskripsi:", "Desktop shortcut created": "Pintasan desktop dibuat", "Details of the report:": "Detail laporan:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Pengembanga<PERSON> itu sulit, dan aplikasi ini gratis. <PERSON><PERSON> jika kamu suka aplik<PERSON>, kamu bisa <b>trak<PERSON><PERSON> kopi</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Langsung instal saat klik ganda pada item di tab \"{discoveryTab}\" (alih-alih menampilkan info paket)", "Disable new share API (port 7058)": "Nonaktifkan API berbagi baru (port 7058)", "Disable the 1-minute timeout for package-related operations": "Nonaktifkan batas waktu 1 menit untuk operasi terkait paket", "Disclaimer": "<PERSON><PERSON><PERSON>", "Discover Packages": "<PERSON><PERSON><PERSON>", "Discover packages": "Te<PERSON><PERSON> paket", "Distinguish between\nuppercase and lowercase": "Bedakan antara huruf besar dan kecil", "Distinguish between uppercase and lowercase": "Bedakan antara huruf besar dan kecil", "Do NOT check for updates": "JANGAN periksa pembaruan", "Do an interactive install for the selected packages": "Lakukan instalasi interaktif untuk paket yang dipilih", "Do an interactive uninstall for the selected packages": "Lakukan pencopotan interaktif untuk paket yang dipilih", "Do an interactive update for the selected packages": "Lakukan pembaruan interaktif untuk paket yang dipilih", "Do not automatically install updates when the battery saver is on": "Jangan instal pembaruan secara otomatis saat penghemat baterai aktif", "Do not automatically install updates when the network connection is metered": "<PERSON><PERSON> instal pembaruan otomatis saat koneksi internet terbatas", "Do not download new app translations from GitHub automatically": "<PERSON><PERSON> unduh terjemahan aplikasi baru dari <PERSON><PERSON><PERSON><PERSON> secara otomatis", "Do not ignore updates for this package anymore": "<PERSON>an a<PERSON>ikan pembaruan untuk paket ini lagi", "Do not remove successful operations from the list automatically": "<PERSON><PERSON> ha<PERSON>i yang berhasil dari daftar secara otomatis", "Do not show this dialog again for {0}": "<PERSON>an tampilkan dialog ini lagi untuk {0}", "Do not update package indexes on launch": "<PERSON><PERSON> indeks paket saat di<PERSON>an", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Apakah Anda menyetujui bahwa UniGetUI mengumpulkan dan mengirim statistik penggunaan anonim untuk memahami dan meningkatkan pengalaman pengguna?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Apakah Anda merasa UniGetUI bermanfaat? <PERSON><PERSON>ya, Anda bisa mendukung pengembang agar terus mengembangkan UniGetUI sebagai antarmuka manajemen paket terbaik.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Apakah UniGetUI bermanfaat? Ingin mendukung pengembangnya? <PERSON><PERSON> ya, <PERSON>a bisa {0}, itu sangat membantu!", "Do you really want to reset this list? This action cannot be reverted.": "Yakin ingin mengatur ulang daftar ini? Tindakan ini tidak dapat dibatalkan.", "Do you really want to uninstall the following {0} packages?": "Yakin ingin mencopot {0} paket berikut?", "Do you really want to uninstall {0} packages?": "Yakin ingin mencopot {0} paket?", "Do you really want to uninstall {0}?": "Yakin ingin mencopot {0}?", "Do you want to restart your computer now?": "Ingin memulai ulang komputer sekarang?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Ingin menerjemahkan UniGetUI ke bahasa Anda? <PERSON><PERSON> caranya <a style=\"color:{0}\" href=\"{1}\"a>DI SINI!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Tidak ingin berdon<PERSON>? <PERSON><PERSON><PERSON>, <PERSON>a bisa berbagi UniGetUI ke teman-teman Anda. Sebarkan tentang UniGetUI.", "Donate": "<PERSON><PERSON>", "Done!": "Selesai!", "Download failed": "<PERSON><PERSON><PERSON> gagal", "Download installer": "<PERSON><PERSON><PERSON> pema<PERSON>", "Download operations are not affected by this setting": "Operasi pengunduhan tidak terpengaruh oleh pengaturan ini", "Download selected installers": "<PERSON><PERSON><PERSON> pen<PERSON>tal terpilih", "Download succeeded": "<PERSON><PERSON><PERSON>", "Download updated language files from GitHub automatically": "Unduh file bahasa terbaru dari Git<PERSON>ub secara otomatis", "Downloading": "<PERSON><PERSON><PERSON><PERSON>", "Downloading backup...": "Mengunduh cadangan...", "Downloading installer for {package}": "<PERSON><PERSON><PERSON><PERSON> pemasang untuk {package}", "Downloading package metadata...": "Mengunduh metadata paket...", "Enable Scoop cleanup on launch": "Aktifkan pembersihan Scoop saat dija<PERSON>an", "Enable WingetUI notifications": "Aktifkan notifikasi UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Aktifkan pemecah masalah <PERSON> yang [eksperimental] dan lebih baik", "Enable and disable package managers, change default install options, etc.": "Mengaktifkan dan menonaktif<PERSON> manajer paket, mengubah opsi penginstalan default, dll.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Aktifkan optimasi penggunaan CPU di latar belakang (lihat Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktifkan API latar belakang (Widget dan Berbagi UniGetUI, port 7058)", "Enable it to install packages from {pm}.": "Aktifkan untuk menginstal paket dari {pm}.", "Enable the automatic WinGet troubleshooter": "Aktifkan pemecah masalah WinGet otomatis", "Enable the new UniGetUI-Branded UAC Elevator": "Aktifkan UAC Elevator dengan merek UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Aktif<PERSON> penangan input proses yang baru (StdIn otomatis)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Aktifkan pengaturan di bawah ini JIKA DAN HANYA JIKA Anda memahami sepenuhnya apa yang di<PERSON>ukannya, dan imp<PERSON><PERSON><PERSON>.", "Enable {pm}": "Aktifkan {pm}", "Enter proxy URL here": "Masukkan URL proxy di sini", "Entries that show in RED will be IMPORTED.": "<PERSON>tri yang berwarna MERAH akan DIIMPOR.", "Entries that show in YELLOW will be IGNORED.": "<PERSON>tri yang ditampilkan dalam warna KUNING akan DIABAIKAN.", "Error": "<PERSON><PERSON><PERSON>", "Everything is up to date": "<PERSON><PERSON><PERSON> sudah diperbarui", "Exact match": "Pencocokan tepat", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Shortcut yang ada di desktop <PERSON><PERSON> akan <PERSON>, dan <PERSON>a harus memilih mana yang akan disimpan dan mana yang akan dihapus.", "Expand version": "<PERSON><PERSON><PERSON> versi", "Experimental settings and developer options": "Pengaturan eksperimental dan opsi pengembang", "Export": "Ekspor", "Export log as a file": "Ekspor log sebagai berkas", "Export packages": "Ekspor paket", "Export selected packages to a file": "Ekspor paket yang dipilih ke berkas", "Export settings to a local file": "Ekspor pengaturan ke berkas lokal", "Export to a file": "Ekspor ke berkas", "Failed": "Gaga<PERSON>", "Fetching available backups...": "Mengambil cadangan yang tersedia...", "Fetching latest announcements, please wait...": "<PERSON><PERSON><PERSON> pen<PERSON> terbaru, mohon tunggu...", "Filters": "Filter", "Finish": "Se<PERSON><PERSON>", "Follow system color scheme": "<PERSON><PERSON><PERSON> skema warna sistem", "Follow the default options when installing, upgrading or uninstalling this package": "Ikuti opsi default saat instalasi, pembaruan, atau menghapus paket ini", "For security reasons, changing the executable file is disabled by default": "Untuk alasan k<PERSON>, perubahan pada file yang dapat dieksekusi dinonaktifkan secara default", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Untuk alasan keamanan, argumen baris perintah khusus dinonaktifkan secara default. Buka pengaturan keamanan UniGetUI untuk mengubahnya.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Untuk alasan keamanan, skrip pra-operasi dan pasca-operasi dinonaktifkan secara default. Buka pengaturan keamanan UniGetUI untuk mengubahnya.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Paksa versi winget ARM (HANYA UNTUK SISTEM ARM64)", "Formerly known as WingetUI": "Sebelumnya dikenal sebagai WingetUI", "Found": "<PERSON><PERSON><PERSON><PERSON>", "Found packages: ": "<PERSON><PERSON>: ", "Found packages: {0}": "<PERSON><PERSON>: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON> di<PERSON>: {0}, belum selesai...", "General preferences": "Preferensi umum", "GitHub profile": "<PERSON><PERSON>", "Global": "Global", "Go to UniGetUI security settings": "Buka pengaturan keamanan UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Repositori hebat berisi utilitas yang tidak dikenal namun berguna dan paket menarik lainnya.<br>Berisi: <b><PERSON><PERSON><PERSON>, <PERSON> baris perintah, Perangkat lunak umum (memerlukan extras bucket)</b>", "Great! You are on the latest version.": "He<PERSON>! Anda sudah menggunakan versi terbaru.", "Grid": "Grid", "Help": "Bantuan", "Help and documentation": "<PERSON><PERSON><PERSON> dan dokumentasi", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Di sini Anda dapat mengubah perilaku UniGetUI terkait pintasan berikut. Mencentang pintasan akan membuat UniGetUI menghapusnya jika dibuat pada peningkatan di masa mendatang. Menghapus centang akan membuat pintasan tetap utuh", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON>, nama saya <PERSON>, dan saya ad<PERSON> <i>pengembang</i> dari UniGetUI. UniGetUI sepenuhnya dibuat di waktu luang saya!", "Hide details": "Sembunyikan detail", "Homepage": "Be<PERSON><PERSON>", "Hooray! No updates were found.": "Hore! Tidak ada pembaruan yang ditemukan.", "How should installations that require administrator privileges be treated?": "Bagaimana sebaiknya pemasangan yang memerlukan hak administrator di<PERSON><PERSON><PERSON>?", "How to add packages to a bundle": "Cara menambahkan paket ke bundel", "I understand": "<PERSON><PERSON>", "Icons": "<PERSON><PERSON>", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Jika Anda mengaktifkan pencadangan cloud, pencadangan akan disimpan sebagai GitHub Gist di akun ini", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "<PERSON><PERSON><PERSON><PERSON> perintah pra-instalasi dan pasca-instalasi khusus saat mengimpor paket dari bundel", "Ignore future updates for this package": "Abaikan pembaruan mendatang untuk paket ini", "Ignore packages from {pm} when showing a notification about updates": "<PERSON><PERSON><PERSON><PERSON> paket dari {pm} saat menampilkan notifikasi pembaruan", "Ignore selected packages": "<PERSON><PERSON><PERSON><PERSON> paket yang dipilih", "Ignore special characters": "<PERSON><PERSON><PERSON><PERSON> karakter k<PERSON>us", "Ignore updates for the selected packages": "Abaikan pembaruan untuk paket yang dipilih", "Ignore updates for this package": "Abaikan pembaruan untuk paket ini", "Ignored updates": "<PERSON><PERSON><PERSON><PERSON> yang di<PERSON>ikan", "Ignored version": "<PERSON><PERSON><PERSON> yang di<PERSON>ikan", "Import": "Impor", "Import packages": "<PERSON><PERSON><PERSON> paket", "Import packages from a file": "<PERSON><PERSON><PERSON> paket dari berkas", "Import settings from a local file": "Impor pengaturan dari berkas lokal", "In order to add packages to a bundle, you will need to: ": "Untuk menambahkan paket ke bundel, <PERSON><PERSON>:", "Initializing WingetUI...": "Menginisialisasi UniGetUI...", "Install": "<PERSON><PERSON>", "Install Scoop": "<PERSON><PERSON>", "Install and more": "Instal dan lainnya", "Install and update preferences": "Preferensi pemasangan dan pembaruan", "Install as administrator": "<PERSON><PERSON> se<PERSON>ai administrator", "Install available updates automatically": "Pasang pembaruan yang tersedia secara otomatis", "Install location can't be changed for {0} packages": "Lokasi penginstalan tidak dapat diubah untuk paket {0}", "Install location:": "Lok<PERSON> pemasangan:", "Install options": "<PERSON><PERSON>", "Install packages from a file": "<PERSON><PERSON> paket dari berkas", "Install prerelease versions of UniGetUI": "Pasang versi pra-rilis UniGetUI", "Install script": "Skrip instalasi", "Install selected packages": "<PERSON><PERSON> paket yang dipilih", "Install selected packages with administrator privileges": "<PERSON><PERSON> paket yang dipilih dengan hak administrator", "Install selection": "<PERSON><PERSON> pilihan", "Install the latest prerelease version": "Pasang versi pra-rilis terbaru", "Install updates automatically": "Pasang pembaruan secara otomatis", "Install {0}": "<PERSON><PERSON> {0}", "Installation canceled by the user!": "Pemasangan di<PERSON>alkan oleh pengguna!", "Installation failed": "<PERSON><PERSON><PERSON><PERSON> gagal", "Installation options": "<PERSON><PERSON>", "Installation scope:": "<PERSON><PERSON><PERSON> pema<PERSON>an:", "Installation succeeded": "<PERSON><PERSON><PERSON><PERSON>", "Installed Packages": "<PERSON><PERSON>", "Installed Version": "<PERSON><PERSON><PERSON>", "Installed packages": "<PERSON><PERSON> yang terpasang", "Installer SHA256": "SHA256 Pemasang", "Installer SHA512": "SHA512 Pemasang", "Installer Type": "<PERSON><PERSON><PERSON>", "Installer URL": "URL Pemasang", "Installer not available": "Pemasang tidak tersedia", "Instance {0} responded, quitting...": "Instan<PERSON> {0} merespons, keluar...", "Instant search": "Pencarian instan", "Integrity checks can be disabled from the Experimental Settings": "Pemeriksaan integritas dapat dinonaktifkan dari Pengaturan Eksperimental", "Integrity checks skipped": "Pemeriksaan integritas di<PERSON>ati", "Integrity checks will not be performed during this operation": "Pemeriksaan integritas tidak akan dilakukan selama operasi ini", "Interactive installation": "<PERSON><PERSON><PERSON><PERSON> interaktif", "Interactive operation": "Operasi interaktif", "Interactive uninstall": "<PERSON><PERSON> pema<PERSON>an interaktif", "Interactive update": "Pembaruan interaktif", "Internet connection settings": "Pengaturan koneksi internet", "Is this package missing the icon?": "<PERSON><PERSON><PERSON>h paket ini tidak memiliki ikon?", "Is your language missing or incomplete?": "<PERSON><PERSON><PERSON><PERSON> bahasa Anda tidak tersedia atau belum lengkap?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Tidak dijamin bahwa kredensial yang diberikan akan disimpan dengan aman, jadi sebaiknya jangan gunakan kredensial akun bank Anda", "It is recommended to restart UniGetUI after WinGet has been repaired": "Disarankan untuk memulai ulang UniGetUI setelah WinGet diperbaiki", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Sangat disarankan untuk menginstal ulang UniGetUI untuk mengatasi situasi ini.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Sepertinya WinGet tidak berfungsi dengan baik. <PERSON><PERSON><PERSON><PERSON> Anda ingin mencoba memperbaikinya?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Sepertinya Anda menjalankan UniGetUI sebagai administrator, yang tidak disarankan. <PERSON>a masih dapat menggunakan program ini, tetapi sangat disarankan untuk tidak menjalankan UniGetUI dengan hak administrator. Klik \"{showDetails}\" untuk melihat alasannya.", "Language": "Bahasa", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON>, te<PERSON>, dan <PERSON><PERSON>i la<PERSON>", "Last updated:": "<PERSON><PERSON><PERSON>:", "Latest": "Terbaru", "Latest Version": "<PERSON><PERSON><PERSON>", "Latest Version:": "V<PERSON>i Terbaru:", "Latest details...": "Detail terbaru...", "Launching subprocess...": "Menjalankan subproses...", "Leave empty for default": "Biarkan kosong untuk bawaan", "License": "<PERSON><PERSON><PERSON>", "Licenses": "<PERSON><PERSON><PERSON>", "Light": "Terang", "List": "<PERSON><PERSON><PERSON>", "Live command-line output": "Output baris perintah langsung", "Live output": "Output langsung", "Loading UI components...": "Memuat komponen antarmuka...", "Loading WingetUI...": "Memuat UniGetUI...", "Loading packages": "<PERSON><PERSON><PERSON> paket", "Loading packages, please wait...": "<PERSON><PERSON><PERSON> paket, harap tunggu...", "Loading...": "Memuat...", "Local": "<PERSON><PERSON>", "Local PC": "PC Lokal", "Local backup advanced options": "Opsi lanjutan pencadangan lokal", "Local machine": "Mesin lokal", "Local package backup": "Pencadangan paket lokal", "Locating {pm}...": "<PERSON><PERSON><PERSON> {pm}...", "Log in": "<PERSON><PERSON><PERSON>", "Log in failed: ": "<PERSON>l masuk:", "Log in to enable cloud backup": "Masuk untuk mengaktifkan pencadangan cloud", "Log in with GitHub": "<PERSON><PERSON><PERSON>", "Log in with GitHub to enable cloud package backup.": "Masuk dengan GitHub untuk mengaktifkan pencadangan paket cloud.", "Log level:": "Tingkat log:", "Log out": "<PERSON><PERSON><PERSON>", "Log out failed: ": "Logout gagal:", "Log out from GitHub": "<PERSON><PERSON><PERSON>", "Looking for packages...": "<PERSON><PERSON><PERSON> paket...", "Machine | Global": "Mesin | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Argumen perintah yang salah dapat merusak paket, atau bahkan memungkinkan aktor jahat untuk mendapatkan hak eksekusi istimewa. <PERSON><PERSON> ka<PERSON> itu, mengimpor argumen perintah khusus dinonaktifkan secara default", "Manage": "<PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "Kelola pengaturan UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Ke<PERSON>la perilaku mulai otomatis UniGetUI dari aplikasi Pengaturan", "Manage ignored packages": "<PERSON><PERSON><PERSON> paket yang diabaikan", "Manage ignored updates": "<PERSON><PERSON><PERSON> pembaruan yang di<PERSON>ikan", "Manage shortcuts": "<PERSON><PERSON><PERSON>", "Manage telemetry settings": "<PERSON><PERSON>la pengaturan telemetri", "Manage {0} sources": "<PERSON><PERSON><PERSON> sumber {0}", "Manifest": "Manifest", "Manifests": "Manifest", "Manual scan": "Pemindaian manual", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "<PERSON><PERSON><PERSON> paket resmi Microsoft. Berisi banyak paket populer dan telah diverifikasi<br><PERSON><PERSON><PERSON>: <b>Perang<PERSON>, Aplikasi Microsoft Store</b>", "Missing dependency": "Ketergantungan hilang", "More": "<PERSON><PERSON><PERSON>", "More details": "Detail lainnya", "More details about the shared data and how it will be processed": "Informasi lebih lanjut tentang data yang dibagikan dan bagaimana data tersebut diproses", "More info": "Info lebih lanjut", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "CATATAN: <PERSON><PERSON><PERSON><PERSON> masalah ini dapat dinonaktifkan dari Pengaturan UniGetUI, di bagian WinGet", "Name": "<PERSON><PERSON>", "New": "<PERSON><PERSON>", "New Version": "<PERSON><PERSON><PERSON>", "New bundle": "Bundel baru", "New version": "Versi baru", "Nice! Backups will be uploaded to a private gist on your account": "Bagus! Cadangan akan diunggah ke Gist pribadi di akun Anda", "No": "Tidak", "No applicable installer was found for the package {0}": "Tidak ditemukan penginstal yang sesuai untuk paket {0}", "No dependencies specified": "Tidak ada dependensi yang ditentukan", "No new shortcuts were found during the scan.": "Tidak ada pintasan baru yang ditemukan selama pem<PERSON>ian.", "No packages found": "Tidak ada paket di<PERSON>ukan", "No packages found matching the input criteria": "Tidak ada paket yang cocok dengan kriteria input", "No packages have been added yet": "Belum ada paket yang ditambahkan", "No packages selected": "Tidak ada paket yang dipilih", "No packages were found": "Tidak di<PERSON>n paket", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Tidak ada informasi pribadi yang dikumpulkan atau dikirim, dan data yang dikumpulkan dianonimkan sehingga tidak dapat ditelusuri kembali kepada Anda.", "No results were found matching the input criteria": "Tidak di<PERSON>ukan hasil yang sesuai dengan kriteria", "No sources found": "Tidak ditemukan sumber", "No sources were found": "Tidak ditemukan sumber", "No updates are available": "Tidak ada pembaruan tersedia", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "<PERSON><PERSON><PERSON> paket Node JS. Berisi berbagai pustaka dan utilitas di ekosistem JavaScript<br><PERSON><PERSON><PERSON>: <b>Pustaka JavaScript Node dan utilitas terkait lainnya</b>", "Not available": "Tidak tersedia", "Not finding the file you are looking for? Make sure it has been added to path.": "Tidak menemukan file yang Anda cari? Pastikan file tersebut telah ditambahkan ke PATH.", "Not found": "Tidak ditem<PERSON>n", "Not right now": "Tidak se<PERSON>", "Notes:": "Catatan:", "Notification preferences": "Preferensi notifikasi", "Notification tray options": "<PERSON><PERSON> baki notifikasi", "Notification types": "<PERSON><PERSON>", "NuPkg (zipped manifest)": "NuPkg (manifest terkompresi)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON>", "Open UniGetUI": "Buka UniGetUI", "Open UniGetUI security settings": "Buka pengaturan keamanan UniGetUI", "Open WingetUI": "Buka UniGetUI", "Open backup location": "Buka lokasi cadangan", "Open existing bundle": "<PERSON>uka bundel yang ada", "Open install location": "<PERSON>uka lokasi instalasi", "Open the welcome wizard": "Buka panduan selamat datang", "Operation canceled by user": "Operasi dibatalkan o<PERSON>h pen<PERSON>una", "Operation cancelled": "Operasi di<PERSON>", "Operation history": "Riwayat operasi", "Operation in progress": "Operasi sedang berlangsung", "Operation on queue (position {0})...": "Operasi dalam antrean (posisi {0})...", "Operation profile:": "Profil operasi:", "Options saved": "Opsi disimpan", "Order by:": "Urutkan berdasarkan:", "Other": "<PERSON><PERSON><PERSON>", "Other settings": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "Package": "<PERSON><PERSON>", "Package Bundles": "Bundel Paket", "Package ID": "ID Paket", "Package Manager": "<PERSON><PERSON><PERSON>", "Package Manager logs": "<PERSON><PERSON>", "Package Managers": "<PERSON><PERSON><PERSON>", "Package Name": "<PERSON><PERSON>", "Package backup": "Cadangan paket", "Package backup settings": "Pengaturan pencadangan paket", "Package bundle": "Bundel paket", "Package details": "Detail paket", "Package lists": "<PERSON><PERSON><PERSON> paket", "Package management made easy": "<PERSON><PERSON><PERSON><PERSON> paket menjadi mudah", "Package manager": "<PERSON><PERSON><PERSON> paket", "Package manager preferences": "Preferensi manajer paket", "Package managers": "<PERSON><PERSON><PERSON> paket", "Package not found": "<PERSON><PERSON> tidak di<PERSON>n", "Package operation preferences": "Preferensi operasi paket", "Package update preferences": "Preferensi pembaruan paket", "Package {name} from {manager}": "<PERSON><PERSON> {name} dari {manager}", "Package's default": "<PERSON><PERSON> bawaan", "Packages": "<PERSON><PERSON>", "Packages found: {0}": "<PERSON><PERSON>: {0}", "Partially": "Sebagian", "Password": "<PERSON>a sandi", "Paste a valid URL to the database": "Tempel URL valid ke basis data", "Pause updates for": "Jeda pembaruan untuk", "Perform a backup now": "Lakukan cadangan sekarang", "Perform a cloud backup now": "Lakukan pencadangan cloud sekarang", "Perform a local backup now": "Lakukan pencadangan lokal sekarang", "Perform integrity checks at startup": "Melakukan pemeriksaan integritas saat memulai", "Performing backup, please wait...": "Sedang melakukan cadangan, harap tunggu...", "Periodically perform a backup of the installed packages": "Lakukan cadangan berkala untuk paket yang telah terinstal", "Periodically perform a cloud backup of the installed packages": "Lakukan pencadangan cloud secara berkala dari paket yang diinstal", "Periodically perform a local backup of the installed packages": "Lakukan pencadangan lokal secara berkala dari paket yang terinstal", "Please check the installation options for this package and try again": "Periksa opsi instalasi untuk paket ini dan coba lagi", "Please click on \"Continue\" to continue": "<PERSON>lakan klik \"Lanjutkan\" untuk melanjutkan", "Please enter at least 3 characters": "<PERSON><PERSON><PERSON> ma<PERSON> minimal 3 karakter", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Harap dicatat bahwa beberapa paket mungkin tidak dapat diinstal, tergantung pada manajer paket yang diaktifkan di mesin ini.", "Please note that not all package managers may fully support this feature": "Harap dicatat bahwa tidak semua manajer paket mendukung fitur ini sepenuhnya", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Harap dicatat bahwa paket dari beberapa sumber mungkin tidak dapat diekspor. Paket tersebut akan dinonaktifkan dan tidak akan diekspor.", "Please run UniGetUI as a regular user and try again.": "Jalankan UniGetUI sebagai pengguna biasa dan coba lagi.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Lihat Output Baris Per<PERSON>ah atau Riwayat Operasi untuk informasi lebih lanjut tentang masalah ini.", "Please select how you want to configure WingetUI": "<PERSON><PERSON><PERSON> bagai<PERSON> Anda ingin mengatur UniGetUI", "Please try again later": "<PERSON><PERSON>an coba lagi nanti", "Please type at least two characters": "<PERSON><PERSON><PERSON> ketik minimal dua karakter", "Please wait": "<PERSON><PERSON> tunggu", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "<PERSON>p tunggu saat {0} sedang diinstal. Mungkin akan muncul jendela hitam (atau biru). Tunggu hingga jendela tersebut tertutup.", "Please wait...": "<PERSON>p tunggu...", "Portable": "Portabel", "Portable mode": "Mode portabel", "Post-install command:": "<PERSON><PERSON><PERSON> pasca-instalasi:", "Post-uninstall command:": "<PERSON><PERSON>ah pasca-penghapusan instalasi:", "Post-update command:": "Perintah pasca-pembaruan:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "<PERSON><PERSON><PERSON> paket PowerShell. Temukan pustaka dan skrip untuk memperluas kemampuan PowerShell<br><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Perintah pra dan pasca instalasi dapat melakukan hal-hal yang sangat buruk pada perangkat Anda, jika memang dirancang untuk itu. Akan sangat berbahaya untuk mengimpor perintah dari sebuah paket, kecuali jika Anda mempercayai sumber paket tersebut", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Perintah pra dan pasca instalasi akan dijalankan sebelum dan sesudah sebuah paket diinstal, diupgrade, atau dihapus. Ketahuilah bahwa perintah-perintah tersebut dapat merusak kecuali jika digunakan dengan hati-hati", "Pre-install command:": "<PERSON><PERSON><PERSON> pra-instalasi:", "Pre-uninstall command:": "<PERSON><PERSON><PERSON> pra-penghapusan instalasi:", "Pre-update command:": "<PERSON><PERSON><PERSON> pra-pembaruan:", "PreRelease": "PraRilis", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON> paket, harap tunggu...", "Proceed at your own risk.": "Lanjutkan dengan risiko Anda sendiri.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "<PERSON><PERSON><PERSON> segala jenis <PERSON> melalui UniGetUI Elevator atau GSudo", "Proxy URL": "URL Proxy", "Proxy compatibility table": "Tabel kompatibilitas proxy", "Proxy settings": "Pengaturan proxy", "Proxy settings, etc.": "Pengaturan proxy, dan la<PERSON>ya", "Publication date:": "Tanggal publikasi:", "Publisher": "Penerbit", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Pengelola pustaka Python. Penuh dengan pustaka Python dan utilitas terkait Python lainnya<br><PERSON><PERSON><PERSON>: <b>Pustaka Python dan utilitas terkait</b>", "Quit": "<PERSON><PERSON><PERSON>", "Quit WingetUI": "Keluar UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Kurangi permintaan UAC, tingkatkan penginstalan secara default, buka kunci fitur lan<PERSON> te<PERSON>, dll.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Lihat Log UniGetUI untuk mendapatkan rincian lebih lanjut mengenai file yang terpengaruh", "Reinstall": "<PERSON><PERSON>", "Reinstall package": "<PERSON><PERSON> ulang paket", "Related settings": "Pengaturan terkait", "Release notes": "Catatan rilis", "Release notes URL": "URL catatan rilis", "Release notes URL:": "URL catatan rilis:", "Release notes:": "Catatan rilis:", "Reload": "<PERSON><PERSON> ul<PERSON>", "Reload log": "<PERSON>at ulang log", "Removal failed": "Penghapusan gagal", "Removal succeeded": "Penghapusan berhasil", "Remove from list": "<PERSON><PERSON> dari daftar", "Remove permanent data": "Hapus data permanen", "Remove selection from bundle": "<PERSON><PERSON> pemilihan dari bundle", "Remove successful installs/uninstalls/updates from the installation list": "<PERSON><PERSON> p<PERSON>/penghapusan/ pembaruan yang berhasil dari daftar instalasi", "Removing source {source}": "Menghapus sumber {source}", "Removing source {source} from {manager}": "Menghapus sumber {source} dari {manager}", "Repair UniGetUI": "Perbaiki UniGetUI", "Repair WinGet": "Perbaiki WinGet", "Report an issue or submit a feature request": "Laporkan masalah atau ajukan permintaan fitur", "Repository": "Repositori", "Reset": "<PERSON><PERSON>", "Reset Scoop's global app cache": "Atur ulang cache aplikasi global Scoop", "Reset UniGetUI": "Atur ulang UniGetUI", "Reset WinGet": "<PERSON><PERSON> ulang WinG<PERSON>", "Reset Winget sources (might help if no packages are listed)": "Atur ulang sumber Winget (mungkin membantu jika tidak ada paket yang terdaftar)", "Reset WingetUI": "Atur ulang UniGetUI", "Reset WingetUI and its preferences": "Atur ulang UniGetUI dan preferensinya", "Reset WingetUI icon and screenshot cache": "Atur ulang cache ikon dan tangkapan layar UniGetUI", "Reset list": "<PERSON><PERSON> ulang daftar", "Resetting Winget sources - WingetUI": "Mengatur ulang sumber WinGet - UniGetUI", "Restart": "<PERSON><PERSON>", "Restart UniGetUI": "<PERSON><PERSON> UniGetUI", "Restart WingetUI": "<PERSON><PERSON> UniGetUI", "Restart WingetUI to fully apply changes": "<PERSON><PERSON> ulang UniGetUI untuk menerapkan perubahan sepenuhnya", "Restart later": "<PERSON><PERSON> nanti", "Restart now": "<PERSON><PERSON> se<PERSON>ng", "Restart required": "<PERSON><PERSON>an", "Restart your PC to finish installation": "<PERSON><PERSON>a untuk menyelesaikan instalasi", "Restart your computer to finish the installation": "<PERSON><PERSON> ulang komputer <PERSON><PERSON> untuk menyelesaikan instalasi", "Restore a backup from the cloud": "Memulihkan cadangan dari cloud", "Restrictions on package managers": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada manajer paket", "Restrictions on package operations": "Pembatasan <PERSON>i paket", "Restrictions when importing package bundles": "Pembatasan saat mengimpor bundel paket", "Retry": "Coba lagi", "Retry as administrator": "Coba lagi sebagai administrator", "Retry failed operations": "Coba lagi operasi yang gagal", "Retry interactively": "Coba lagi secara interaktif", "Retry skipping integrity checks": "Coba lagi dengan melewati pemeriksaan integritas", "Retrying, please wait...": "<PERSON><PERSON><PERSON> lagi, harap tunggu...", "Return to top": "Ke<PERSON><PERSON> ke atas", "Run": "Jalankan", "Run as admin": "Jalankan sebagai admin", "Run cleanup and clear cache": "Jalankan pembersihan dan bersih<PERSON> cache", "Run last": "Jalankan terakhir", "Run next": "Jalankan berikutnya", "Run now": "Jalankan se<PERSON>ng", "Running the installer...": "<PERSON><PERSON><PERSON><PERSON>...", "Running the uninstaller...": "<PERSON><PERSON><PERSON><PERSON> penghapus instalan...", "Running the updater...": "<PERSON><PERSON><PERSON><PERSON> pembaru...", "Save": "Simpan", "Save File": "Simpan File", "Save and close": "<PERSON>mpan dan tutup", "Save as": "Simpan sebagai", "Save bundle as": "Simpan bundle sebagai", "Save now": "<PERSON><PERSON><PERSON>", "Saving packages, please wait...": "<PERSON><PERSON><PERSON><PERSON> paket, harap tunggu...", "Scoop Installer - WingetUI": "Instalasi Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Penghapus Instalasi Scoop - UniGetUI", "Scoop package": "<PERSON><PERSON>", "Search": "<PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Cari perangkat lunak desktop, beri tahu saya jika ada pembaruan dan jangan melakukan hal-hal rumit. Saya tidak ingin UniGetUI membingungkan, saya hanya ingin <b>toko perangkat lunak</b> yang se<PERSON>hana", "Search for packages": "<PERSON><PERSON> paket", "Search for packages to start": "<PERSON>i paket untuk memulai", "Search mode": "Mode pencarian", "Search on available updates": "<PERSON>i pembaruan yang tersedia", "Search on your software": "Cari perangkat lunak Anda", "Searching for installed packages...": "<PERSON><PERSON><PERSON> paket yang terinstal...", "Searching for packages...": "<PERSON><PERSON><PERSON> paket...", "Searching for updates...": "<PERSON><PERSON>i pembaruan...", "Select": "<PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Pilih \"{item}\" untuk menambahkan ember kustom Anda", "Select a folder": "<PERSON><PERSON><PERSON> folder", "Select all": "<PERSON><PERSON><PERSON> se<PERSON>a", "Select all packages": "<PERSON><PERSON><PERSON> semua paket", "Select backup": "<PERSON><PERSON><PERSON> cadangan", "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON> hanya <b>jika <PERSON>a tahu apa yang Anda lakukan</b>.", "Select package file": "Pilih file paket", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "<PERSON><PERSON><PERSON> cadangan yang ingin Anda buka. <PERSON><PERSON><PERSON>, <PERSON><PERSON> akan dapat meninjau paket/program mana yang ingin Anda puli<PERSON>kan.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "<PERSON><PERSON><PERSON> proses yang harus ditutup sebelum paket ini diinstal, diperbar<PERSON>, atau dihapus.", "Select the source you want to add:": "<PERSON><PERSON>h sumber yang ingin Anda tamba<PERSON>kan:", "Select upgradable packages by default": "<PERSON><PERSON><PERSON> paket yang dapat diperbarui secara default", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON> <b>pen<PERSON><PERSON> paket</b> yang ingin diguna<PERSON> ({0}), atur cara pema<PERSON>an paket, kelola bagaimana hak administrator di<PERSON><PERSON><PERSON>, dll.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Mengirimkan jabat tangan. Menunggu jawaban pendengar instance... ({0}%)", "Set a custom backup file name": "Atur nama file cadangan kustom", "Set custom backup file name": "Atur nama file cadangan kustom", "Settings": "<PERSON><PERSON><PERSON><PERSON>", "Share": "Bagikan", "Share WingetUI": "Bagikan UniGetUI", "Share anonymous usage data": "Bagikan data penggunaan anonim", "Share this package": "Bagikan paket ini", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "<PERSON><PERSON> <PERSON>a men<PERSON> pengat<PERSON>n kea<PERSON>, <PERSON><PERSON> harus membuka bundel lagi agar perubahan dapat diterapkan.", "Show UniGetUI on the system tray": "Tampilkan UniGetUI di baki sistem", "Show UniGetUI's version and build number on the titlebar.": "Tampilkan versi UniGetUI pada judul", "Show WingetUI": "Tampilkan UniGetUI", "Show a notification when an installation fails": "Tampilkan pemberitahuan saat instalasi gagal", "Show a notification when an installation finishes successfully": "Tampilkan pemberitahuan saat instalasi selesai dengan sukses", "Show a notification when an operation fails": "Tam<PERSON>lkan pemberitahuan saat operasi gagal", "Show a notification when an operation finishes successfully": "Tam<PERSON>lkan pemberitahuan saat operasi selesai dengan sukses", "Show a notification when there are available updates": "Tam<PERSON>lkan pemberitahuan saat ada pembaruan yang tersedia", "Show a silent notification when an operation is running": "<PERSON><PERSON><PERSON><PERSON> pemberitahuan diam saat operasi sedang berjalan", "Show details": "Tam<PERSON>lkan detail", "Show in explorer": "<PERSON><PERSON><PERSON><PERSON>", "Show info about the package on the Updates tab": "Tampilkan informasi tentang paket di tab Pembaruan", "Show missing translation strings": "Tampilkan string terjemahan yang hilang", "Show notifications on different events": "<PERSON><PERSON><PERSON>an pemberitahuan pada berbagai acara", "Show package details": "Tampilkan detail paket", "Show package icons on package lists": "<PERSON><PERSON><PERSON><PERSON> ikon paket di daftar paket", "Show similar packages": "<PERSON><PERSON><PERSON><PERSON> paket serupa", "Show the live output": "Tampilkan output langsung", "Size": "Ukuran", "Skip": "<PERSON><PERSON>", "Skip hash check": "<PERSON><PERSON> p<PERSON> hash", "Skip hash checks": "<PERSON><PERSON> p<PERSON> hash", "Skip integrity checks": "<PERSON><PERSON> p<PERSON> integritas", "Skip minor updates for this package": "Le<PERSON> pembaruan minor untuk paket ini", "Skip the hash check when installing the selected packages": "<PERSON><PERSON> pemeri<PERSON>aan hash saat menginstal paket yang dipilih", "Skip the hash check when updating the selected packages": "<PERSON><PERSON> pemeri<PERSON>aan hash saat memperbarui paket yang dipilih", "Skip this version": "<PERSON><PERSON> versi ini", "Software Updates": "Pembaruan perangkat lunak", "Something went wrong": "<PERSON><PERSON><PERSON><PERSON>", "Something went wrong while launching the updater.": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat meluncurkan pembaru.", "Source": "Sumber", "Source URL:": "URL sumber:", "Source added successfully": "Sumber ber<PERSON><PERSON> di<PERSON>", "Source addition failed": "<PERSON><PERSON><PERSON><PERSON> sumber gagal", "Source name:": "Nama sumber:", "Source removal failed": "Penghapusan sumber gagal", "Source removed successfully": "Sumber ber<PERSON><PERSON>", "Source:": "Sumber:", "Sources": "Sumber", "Start": "<PERSON><PERSON>", "Starting daemons...": "<PERSON><PERSON><PERSON> daemon...", "Starting operation...": "<PERSON><PERSON><PERSON>...", "Startup options": "Opsi startup", "Status": "Status", "Stuck here? Skip initialization": "Terjebak di sini? <PERSON><PERSON> inisialisasi", "Success!": "Sukses!", "Suport the developer": "Dukung pengembang", "Support me": "<PERSON><PERSON><PERSON> saya", "Support the developer": "Dukung pengembang", "Systems are now ready to go!": "Sistem sekarang siap untuk digunakan!", "Telemetry": "Telemetri", "Text": "Teks", "Text file": "File teks", "Thank you ❤": "<PERSON><PERSON> kasih ❤", "Thank you 😉": "<PERSON><PERSON> kasih 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "<PERSON><PERSON><PERSON> paket Rust.<br><PERSON><PERSON><PERSON>: <b>Pustaka Rust dan program yang ditulis dalam Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Cadangan TIDAK akan mencakup file biner atau data yang disimpan oleh program.", "The backup will be performed after login.": "<PERSON>adangan akan dilakukan setelah login.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Cadangan akan mencakup daftar lengkap paket yang terinstal dan opsi instalasinya. Pembaruan yang diabaikan dan versi yang dilewati juga akan disimpan.", "The bundle was created successfully on {0}": "<PERSON>et telah dibuat dengan sukses pada {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON>et yang Anda coba muat tampaknya tidak valid. Harap periksa file dan coba lagi.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Checksum dari penginstal tidak sesuai dengan nilai yang dihara<PERSON>, dan keaslian penginstal tidak dapat diverifikasi. Jika Anda mempercayai penerbitnya, {0} paket lagi dengan melewati pemeriksaan hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "<PERSON><PERSON><PERSON> paket klasik untuk Windows. Anda akan menemukan segalanya di sana. <br><PERSON><PERSON><PERSON>: <b>Perang<PERSON></b>", "The cloud backup completed successfully.": "Pencadangan cloud berhasil diselesaikan.", "The cloud backup has been loaded successfully.": "Cadangan cloud telah berhasil dimuat.", "The current bundle has no packages. Add some packages to get started": "<PERSON>et saat ini tidak memiliki paket. Tambahkan beberapa paket untuk memulai", "The executable file for {0} was not found": "File eksekusi untuk {0} tidak ditemukan", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Opsi berikut ini akan diterapkan secara default setiap kali paket {0} diinstal, diperbarui, atau dihapus.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Paket-paket berikut akan diekspor ke file JSON. Tidak ada data pengguna atau biner yang akan disimpan.", "The following packages are going to be installed on your system.": "Paket-paket berikut akan diinstal di sistem Anda.", "The following settings may pose a security risk, hence they are disabled by default.": "Pengaturan berikut ini dapat menimbulkan risiko k<PERSON>n, oleh karena itu pengaturan ini dinonaktifkan secara default.", "The following settings will be applied each time this package is installed, updated or removed.": "Pengaturan berikut akan diterapkan setiap kali paket ini diinstal, diperbarui, atau dihapus.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Pengaturan berikut akan diterapkan setiap kali paket ini diinstal, diperbarui, atau dihapus. Pengaturan ini akan disimpan secara otomatis.", "The icons and screenshots are maintained by users like you!": "Ikon dan tangkapan layar dikelola oleh pengguna seperti Anda!", "The installation script saved to {0}": "Skrip instalasi disimpan ke {0}", "The installer authenticity could not be verified.": "<PERSON><PERSON><PERSON> penginstal tidak dapat diverifikasi.", "The installer has an invalid checksum": "Penginstal memiliki checksum yang tidak valid", "The installer hash does not match the expected value.": "<PERSON>h penginstal tidak sesuai dengan nilai yang diharapkan.", "The local icon cache currently takes {0} MB": "Cache ikon lokal saat ini memakan {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "<PERSON><PERSON><PERSON> utama proyek ini adalah untuk membuat UI intuitif untuk mengelola manajer paket CLI yang paling umum untuk Windows, seperti Winget dan <PERSON>.", "The package \"{0}\" was not found on the package manager \"{1}\"": "<PERSON><PERSON> \"{0}\" tidak ditemukan di manajer paket \"{1}\"", "The package bundle could not be created due to an error.": "Paket bundle tidak dapat dibuat karena kes<PERSON>han.", "The package bundle is not valid": "Paket bundle tidak valid", "The package manager \"{0}\" is disabled": "<PERSON><PERSON><PERSON> paket \"{0}\" dinonaktifkan", "The package manager \"{0}\" was not found": "<PERSON><PERSON><PERSON> paket \"{0}\" tidak ditemukan", "The package {0} from {1} was not found.": "<PERSON><PERSON> {0} dari {1} tidak ditem<PERSON>n.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Paket-paket yang terdaftar di sini tidak akan diperhitungkan saat memeriksa pembaruan. Klik dua kali pada mereka atau klik tombol di sebelah kanan mereka untuk berhenti mengabaikan pembaruan mereka.", "The selected packages have been blacklisted": "<PERSON>et yang dipilih telah masuk daftar hitam", "The settings will list, in their descriptions, the potential security issues they may have.": "<PERSON><PERSON><PERSON><PERSON> akan <PERSON>, da<PERSON>, pot<PERSON>i masalah keamanan yang mungkin terjadi.", "The size of the backup is estimated to be less than 1MB.": "Ukuran cadangan diperkirakan kurang dari 1MB.", "The source {source} was added to {manager} successfully": "Sumber {source} ber<PERSON><PERSON> ditam<PERSON> ke {manager}", "The source {source} was removed from {manager} successfully": "Sumber {source} ber<PERSON><PERSON> <PERSON><PERSON> dari {manager}", "The system tray icon must be enabled in order for notifications to work": "<PERSON><PERSON> baki sistem harus diaktifkan agar pemberitahuan dapat bekerja", "The update process has been aborted.": "Proses pembaruan telah di<PERSON>.", "The update process will start after closing UniGetUI": "Proses pembaruan akan dimulai setelah menutup UniGetUI", "The update will be installed upon closing WingetUI": "Pembaruan akan diinstal setelah menutup UniGetUI", "The update will not continue.": "Pembaruan tidak akan dilanjutkan.", "The user has canceled {0}, that was a requirement for {1} to be run": "<PERSON>gg<PERSON> telah membatalkan {0}, yang merupakan syarat agar {1} dapat di<PERSON>an", "There are no new UniGetUI versions to be installed": "Tidak ada versi UniGetUI baru yang akan diinstal", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Ada operasi yang sedang berjalan. Menutup UniGetUI dapat menyebabkan mereka gagal. Apakah Anda ingin melanjutkan?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "<PERSON> beberapa video menarik di YouTube yang menampilkan UniGetUI dan kemampuannya. Anda bisa belajar trik dan tips yang berguna!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Ada dua alasan utama untuk tidak menjalankan UniGetUI sebagai administrator:\n <PERSON> pertama adalah manajer paket Scoop mungkin menyebabkan masalah dengan beberapa perintah ketika dijalankan dengan hak administrator.\n <PERSON> kedua adalah menjalankan UniGetUI sebagai administrator berarti paket yang Anda unduh akan dijalankan sebagai administrator (dan ini tidak aman).\n Ingat, jika Anda perlu menginstal paket tertentu sebagai administrator, <PERSON><PERSON> selalu dapat mengklik kanan item -> Instal/Pembaruan/Hapus sebagai administrator.", "There is an error with the configuration of the package manager \"{0}\"": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han dengan konfigurasi manajer paket \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Sedang ada instalasi yang berlangsung. Jika Anda menutup UniGetUI, instalasi mungkin gagal dan mengh<PERSON><PERSON>an hasil yang tidak terduga. Apakah Anda tetap ingin menutup UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Mereka adalah program yang bertanggung jawab untuk men<PERSON>, me<PERSON><PERSON><PERSON>, dan mengh<PERSON><PERSON> paket.", "Third-party licenses": "Lisensi pihak ketiga", "This could represent a <b>security risk</b>.": "Ini bisa mewakili <b>r<PERSON><PERSON> k<PERSON></b>.", "This is not recommended.": "Ini tidak disarankan.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "<PERSON>i kemungkinan disebabkan oleh fakta bahwa paket yang Anda terima telah dihapus, atau diterbitkan di manajer paket yang tidak Anda aktifkan. ID yang diterima adalah {0}", "This is the <b>default choice</b>.": "Ini adalah <b>pilihan default</b>.", "This may help if WinGet packages are not shown": "Ini dapat membantu jika paket WinGet tidak ditampilkan", "This may help if no packages are listed": "Ini mungkin membantu jika tidak ada paket yang terdaftar", "This may take a minute or two": "Ini mungkin memerlukan waktu satu atau dua menit", "This operation is running interactively.": "Operasi ini sedang berjalan secara interaktif.", "This operation is running with administrator privileges.": "Operasi ini berjalan dengan hak istimewa administrator.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Opsi ini AKAN menyebabkan masalah. Operasi apa pun yang tidak mampu elevasi dirinya sendiri AKAN GAGAL. Instal/perbarui/hapus sebagai administrator TIDAK AKAN BERFUNGSI.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "<PERSON>et bundel ini memiliki beberapa pengaturan yang berpotensi berbahaya, dan dapat diabaikan secara default.", "This package can be updated": "Paket ini dapat diperbarui", "This package can be updated to version {0}": "Paket ini dapat diperbarui ke versi {0}", "This package can be upgraded to version {0}": "Paket ini dapat ditingkatkan ke versi {0}", "This package cannot be installed from an elevated context.": "Paket ini tidak dapat diinstal dari konteks yang ditinggikan.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Paket ini tidak memiliki tangkapan layar atau ikon yang hilang? Berkontribusilah ke UniGetUI dengan menambahkan ikon dan tangkapan layar yang hilang ke database publik terbuka kami.", "This package is already installed": "Paket ini sudah terinstal", "This package is being processed": "Paket ini sedang diproses", "This package is not available": "Paket ini tidak tersedia", "This package is on the queue": "<PERSON>et ini ada dalam antrean", "This process is running with administrator privileges": "Proses ini ber<PERSON>lan dengan hak istimewa administrator", "This project has no connection with the official {0} project — it's completely unofficial.": "Proyek ini tidak memiliki kaitan dengan proyek {0} resmi — ini sepenuhnya tidak resmi.", "This setting is disabled": "Pengaturan ini dinonaktifkan", "This wizard will help you configure and customize WingetUI!": "Panduan ini akan membantu Anda mengonfigurasi dan menyesuaikan UniGetUI!", "Toggle search filters pane": "Alihkan panel filter pencarian", "Translators": "<PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Cobalah untuk mematikan proses yang menolak untuk menutup ketika diminta", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "<PERSON><PERSON> men<PERSON>, <PERSON><PERSON> dapat mengubah file yang dapat dieksekusi yang digunakan untuk berinteraksi dengan manajer paket. Meskipun hal ini memungkinkan penyesuaian yang lebih baik pada proses instalasi <PERSON>, hal ini juga dapat berbahaya", "Type here the name and the URL of the source you want to add, separed by a space.": "Ketik nama dan URL sumber yang ingin <PERSON>a tamba<PERSON>, dipisa<PERSON><PERSON> oleh spasi.", "Unable to find package": "Tidak dapat menemukan paket", "Unable to load informarion": "Tidak dapat memuat informasi", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI mengumpulkan data penggunaan anonim untuk meningkatkan pengalaman pengguna.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI mengumpulkan data penggunaan anonim dengan tujuan tunggal untuk memahami dan meningkatkan pengalaman pengguna.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI telah mendeteksi pintasan desktop baru yang dapat dihapus secara otomatis.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI telah mendeteksi pintasan desktop berikut yang dapat dihapus secara otomatis pada pembaruan mendatang", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI telah mendeteksi {0} pintasan desktop baru yang dapat dihapus secara otomatis.", "UniGetUI is being updated...": "UniGetUI sedang diperbarui...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI tidak terkait dengan manajer paket yang kompatibel manapun. UniGetUI adalah proyek independen.", "UniGetUI on the background and system tray": "UniGetUI di latar belakang dan area sistem", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI atau beberapa komponennya hilang atau rusak.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI membutuhkan {0} untuk beroperasi, tetapi tidak ditemukan di sistem Anda.", "UniGetUI startup page:": "Halaman awal UniGetUI:", "UniGetUI updater": "Pembaruan UniGetUI", "UniGetUI version {0} is being downloaded.": "Versi UniGetUI {0} sedang diunduh.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} siap untuk dipasang.", "Uninstall": "<PERSON><PERSON> instalasi", "Uninstall Scoop (and its packages)": "<PERSON><PERSON> instal<PERSON> (dan paket-paket<PERSON>)", "Uninstall and more": "<PERSON><PERSON> instalan dan la<PERSON>ya", "Uninstall and remove data": "Hapus instalasi dan hapus data", "Uninstall as administrator": "<PERSON><PERSON> instalasi sebagai administrator", "Uninstall canceled by the user!": "Hapus instalasi dibatalkan oleh pen<PERSON>una!", "Uninstall failed": "<PERSON><PERSON> instalasi gagal", "Uninstall options": "Opsi penghapusan instalan", "Uninstall package": "<PERSON><PERSON> instalasi paket", "Uninstall package, then reinstall it": "<PERSON><PERSON> instalasi paket, lalu pasang ulang", "Uninstall package, then update it": "<PERSON><PERSON> instal<PERSON> paket, la<PERSON> per<PERSON>ui", "Uninstall previous versions when updated": "<PERSON><PERSON> instalan versi sebelumnya saat diperbarui", "Uninstall selected packages": "<PERSON><PERSON> instalasi paket yang dipilih", "Uninstall selection": "<PERSON><PERSON><PERSON> p<PERSON>", "Uninstall succeeded": "<PERSON><PERSON> instalasi ber<PERSON>", "Uninstall the selected packages with administrator privileges": "<PERSON><PERSON> instalasi paket yang dipilih dengan hak istimewa administrator", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "<PERSON>et yang dapat dihapus instalasinya dengan asal yang tercantum sebagai \"{0}\" tidak diterbitkan di manajer paket manapun, se<PERSON>ga tidak ada informasi yang dapat ditampilkan tentangnya.", "Unknown": "Tidak diketahui", "Unknown size": "Ukuran tidak diketahui", "Unset or unknown": "Tidak disetel atau tidak diketahui", "Up to date": "Sudah terbaru", "Update": "<PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Perbarui UniGetUI secara otomatis", "Update all": "<PERSON><PERSON><PERSON> se<PERSON>a", "Update and more": "<PERSON><PERSON><PERSON> dan la<PERSON>ya", "Update as administrator": "<PERSON><PERSON><PERSON> se<PERSON>ai administrator", "Update check frequency, automatically install updates, etc.": "Frekuensi pemeri<PERSON><PERSON> pembaruan, instal pembaruan secara otomatis, dll.", "Update checking": null, "Update date": "Tanggal pembaruan", "Update failed": "<PERSON><PERSON><PERSON><PERSON> gagal", "Update found!": "Pembaruan ditemukan!", "Update now": "<PERSON><PERSON><PERSON>", "Update options": "Opsi pembaruan", "Update package indexes on launch": "<PERSON><PERSON><PERSON> indeks paket saat diluncurkan", "Update packages automatically": "<PERSON><PERSON><PERSON> paket secara otomatis", "Update selected packages": "<PERSON><PERSON><PERSON> paket yang dipilih", "Update selected packages with administrator privileges": "<PERSON><PERSON><PERSON> paket yang dipilih dengan hak istimewa administrator", "Update selection": "<PERSON><PERSON><PERSON> pembaruan", "Update succeeded": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>il", "Update to version {0}": "<PERSON><PERSON><PERSON> ke versi {0}", "Update to {0} available": "Pembaruan ke {0} tersedia", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Perbarui file port Git vcpkg secara otomatis (memerlukan Git terinstal)", "Updates": "Pembaruan", "Updates available!": "Pembaruan tersedia!", "Updates for this package are ignored": "Pembaruan untuk paket ini diabaikan", "Updates found!": "Pembaruan ditemukan!", "Updates preferences": "Preferensi pembaruan", "Updating WingetUI": "Memperbarui UniGetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Gunakan WinGet bawaan Legacy daripada PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Gunakan URL database ikon dan tangkapan layar kustom", "Use bundled WinGet instead of PowerShell CMDlets": "Gunakan WinGet bawaan daripada PowerShell CMDLets", "Use bundled WinGet instead of system WinGet": "<PERSON><PERSON><PERSON> bawaan da<PERSON>ada WinGet sistem", "Use installed GSudo instead of UniGetUI Elevator": "Gunakan GSudo yang terinstal sebagai pengganti UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Gunakan GSudo yang diinstal daripada yang dibundel", "Use system Chocolatey": "Gunakan Chocolatey sistem", "Use system Chocolatey (Needs a restart)": "<PERSON><PERSON><PERSON>y sistem (Perlu restart)", "Use system Winget (Needs a restart)": "<PERSON><PERSON><PERSON> sistem (Perlu restart)", "Use system Winget (System language must be set to english)": "<PERSON><PERSON><PERSON> Winget sistem (Bahasa sistem harus diatur ke bahasa Inggris)", "Use the WinGet COM API to fetch packages": "Gunakan API COM WinGet untuk mengambil paket", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Gunakan Modul PowerShell WinGet daripada API COM WinGet", "Useful links": "Tautan berguna", "User": "Pengguna", "User interface preferences": "Preferensi antarmuka pengguna", "User | Local": "Pengguna | Lokal", "Username": "<PERSON><PERSON>", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Menggunakan UniGetUI berarti menerima Lisensi GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Menggunakan UniGetUI berarti menerima Lisensi MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Root vcpkg tidak ditemukan. Harap tentukan variabel lingkungan %VCPKG_ROOT% atau tentukan dari Pengaturan UniGetUI", "Vcpkg was not found on your system.": "Vcpkg tidak ditemukan di sistem Anda.", "Verbose": "Verbose", "Version": "<PERSON><PERSON><PERSON>", "Version to install:": "Versi untuk dipasang:", "Version:": "Versi:", "View GitHub Profile": "<PERSON><PERSON> Profil <PERSON>", "View WingetUI on GitHub": "Lihat UniGetUI di GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Lihat kode sumber UniGetUI. <PERSON><PERSON>, <PERSON><PERSON> dapat melaporkan bug atau menyarankan fitur, atau bahkan berkontribusi langsung pada Proyek UniGetUI", "View mode:": "Mode tampilan:", "View on UniGetUI": "Lihat di UniGetUI", "View page on browser": "<PERSON><PERSON> di <PERSON>", "View {0} logs": "Lihat {0} log", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Tunggu perangkat terhubung ke internet sebelum mencoba melakukan tugas yang memerlukan koneksi internet.", "Waiting for other installations to finish...": "<PERSON><PERSON><PERSON> instalasi lainnya se<PERSON>...", "Waiting for {0} to complete...": "<PERSON>ung<PERSON> {0} selesai...", "Warning": "Peringatan", "Warning!": "Peringatan!", "We are checking for updates.": "<PERSON>mi <PERSON>g memeriksa pembaruan.", "We could not load detailed information about this package, because it was not found in any of your package sources": "<PERSON>mi tidak dapat memuat informasi rinci tentang paket ini, karena tidak ditemukan di sumber paket Anda.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "<PERSON>mi tidak dapat memuat informasi rinci tentang paket ini, karena tidak diinstal dari pengelola paket yang tersedia.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON><PERSON> tidak dapat {action} {package}. Harap coba lagi nanti. Klik \"{showDetails}\" untuk melihat log dari penginstal.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON><PERSON> tidak dapat {action} {package}. Harap coba lagi nanti. Klik \"{showDetails}\" untuk melihat log dari penghapus instalasi.", "We couldn't find any package": "Kami tidak dapat menemukan paket apapun", "Welcome to WingetUI": "Selamat datang di UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "<PERSON>at menginstal paket dari bundel, instal juga paket yang sudah terinstal", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Saat pintasan baru terdeteksi, hapus pintasan secara otomatis alih-alih menampilkan dialog ini.", "Which backup do you want to open?": "Cadangan mana yang ingin Anda buka?", "Which package managers do you want to use?": "<PERSON><PERSON><PERSON> paket mana yang ingin Anda gunakan?", "Which source do you want to add?": "Sumber mana yang ingin Anda tamba<PERSON>kan?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Sementara Winget dapat digunakan dalam UniGetUI, UniGetUI dapat digunakan dengan pengelola paket lain, yang bisa membingungkan. <PERSON><PERSON>, UniGetUI dirancang hanya untuk bekerja dengan Winget, namun sekarang ini tidak lagi demikian, sehingga UniGetUI tidak lagi mewakili tujuan proyek ini.", "WinGet could not be repaired": "WinGet tidak dapat diperbaiki", "WinGet malfunction detected": "Kerusakan WinGet terdeteksi", "WinGet was repaired successfully": "WinGet berhasil diperbaiki", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - <PERSON><PERSON><PERSON> sudah diperbarui", "WingetUI - {0} updates are available": "UniGetUI - {0} pembaruan tersedia", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "Halaman Utama UniGetUI", "WingetUI Homepage - Share this link!": "Halaman Utama UniGetUI - Bagikan tautan ini!", "WingetUI License": "Lisensi UniGetUI", "WingetUI Log": "Log UniGetUI", "WingetUI Repository": "Repositori UniGetUI", "WingetUI Settings": "Pengaturan UniGetUI", "WingetUI Settings File": "File Pengaturan UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI menggunakan pustaka berikut. <PERSON><PERSON>, UniGetUI tidak akan mungkin terwujud.", "WingetUI Version {0}": "UniGetUI Versi {0}", "WingetUI autostart behaviour, application launch settings": "Perilaku autostart UniGetUI, pengaturan peluncuran aplikasi", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI dapat memeriksa apakah perangkat lunak Anda memiliki pembaruan yang tersedia, dan men<PERSON><PERSON>nya secara otomatis jika <PERSON>a inginkan", "WingetUI display language:": "Bahasa tampilan UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI telah dijalankan sebagai administrator, yang tidak disarankan. Ketika menjalankan UniGetUI sebagai administrator, SETIAP operasi yang diluncurkan dari UniGetUI akan memiliki hak istimewa administrator. Anda masih bisa menggunakan program ini, tetapi kami sangat menyarankan untuk tidak menjalankan UniGetUI dengan hak istimewa administrator.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI telah diterjemahkan ke lebih dari 40 bahasa berkat para penerjemah sukarelawan. <PERSON><PERSON> kasih 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI tidak diterjemahkan mesin. Pengguna berikut yang bertanggung jawab atas terjemahan:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI adalah aplikasi yang memudahkan pengelolaan perangkat lunak Anda, dengan menyediakan antarmuka grafis serba ada untuk pengelola paket baris perintah Anda.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI sedang diganti namanya untuk menekankan perbedaan antara UniGetUI (antarmuka yang Anda gunakan sekarang) dan WinGet (pengelola paket yang dikembangkan oleh Microsoft yang tidak ada kaitannya dengan saya)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI sedang diperbarui. <PERSON><PERSON><PERSON>, UniGetUI akan memulai ulang dirinya sendiri", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI gratis, dan akan tetap gratis selamanya. Tanpa iklan, tanpa kartu kredit, tanpa versi premium. 100% gratis, selamanya.", "WingetUI log": "Log UniGetUI", "WingetUI tray application preferences": "Preferensi aplikasi tray UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI menggunakan pustaka berikut. Tanpa pustaka ini, UniGetUI tidak akan mungkin ada.", "WingetUI version {0} is being downloaded.": "Versi UniGetUI {0} sedang diunduh.", "WingetUI will become {newname} soon!": "UniGetUI akan segera menjadi {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI tidak akan memeriksa pembaruan secara berkala. Mereka tetap akan diperiksa saat peluncuran, tetapi Anda tidak akan diberi peringatan tentang hal itu.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI akan menampilkan prompt UAC setiap kali paket memerlukan peningkatan hak akses untuk diinstal.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "UniGetUI akan segera dinamakan {newname}. Ini tidak akan mengubah aplikasi. <PERSON><PERSON> (pengembang) akan terus mengembangkan proyek ini seperti yang saya lakukan sekarang, tetapi dengan nama yang berbeda.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI tidak akan mungkin ada tanpa bantuan kontributor kami yang terhormat. Periksa profil G<PERSON><PERSON> mere<PERSON>, UniGetUI tidak akan ada tanpa mereka!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI tidak akan mungkin ada tanpa bantuan kontributor. <PERSON><PERSON> kasih semua 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} siap untuk diinstal.", "Write here the process names here, separated by commas (,)": "Tuliskan nama proses di sini, dip<PERSON><PERSON><PERSON> dengan koma (,)", "Yes": "Ya", "You are logged in as {0} (@{1})": "<PERSON><PERSON> masuk sebagai {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "<PERSON>a dapat mengubah perilaku ini pada pengaturan keamanan UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Anda dapat menentukan perintah yang akan dijalankan sebelum atau sesudah paket ini diinstal, diperbarui, atau dihapus. Perintah-perintah tersebut akan dijalankan pada command prompt, jadi skrip <PERSON> akan bekerja di sini.", "You have currently version {0} installed": "Anda saat ini memiliki versi {0} yang terinstal", "You have installed WingetUI Version {0}": "<PERSON><PERSON> telah menginstal UniGetUI Versi {0}", "You may lose unsaved data": "<PERSON><PERSON> mungkin akan kehilangan data yang belum disimpan", "You may need to install {pm} in order to use it with WingetUI.": "<PERSON><PERSON> mungkin perlu menginstal {pm} untuk menggunakannya dengan UniGetUI.", "You may restart your computer later if you wish": "Anda dapat me-restart komputer <PERSON><PERSON> nanti jika Anda mau", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "<PERSON><PERSON> hanya akan diminta se<PERSON>, dan hak administrator akan di<PERSON>ikan kepada paket yang memintanya.", "You will be prompted only once, and every future installation will be elevated automatically.": "<PERSON>a hanya akan diminta sekali, dan setiap instalasi di masa depan akan ditingkatkan secara otomatis.", "You will likely need to interact with the installer.": "<PERSON>a mungkin perlu berinteraksi dengan pen<PERSON>tal.", "[RAN AS ADMINISTRATOR]": "[<PERSON><PERSON><PERSON><PERSON> SEBAGAI ADMINISTRATOR]", "buy me a coffee": "beli saya secangkir kopi", "extracted": "diekstrak", "feature": "fitur", "formerly WingetUI": "<PERSON>lu <PERSON>", "homepage": "situs web", "install": "instal", "installation": "instalasi", "installed": "terinstal", "installing": "<PERSON>g men<PERSON>", "library": "pustaka", "mandatory": "wajib", "option": "pilihan", "optional": "opsional", "uninstall": "hapus instalasi", "uninstallation": "penghapusan instalasi", "uninstalled": "<PERSON><PERSON><PERSON><PERSON> instalasi", "uninstalling": "sedang menghapus instalasi", "update(noun)": "pembaruan", "update(verb)": "<PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON>", "updating": "<PERSON><PERSON><PERSON><PERSON>", "version {0}": "versi {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} Opsi penginstalan saat ini terkunci karena {0} mengikuti opsi penginstalan default.", "{0} Uninstallation": "{0} Penghapusan Instalasi", "{0} aborted": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} can be updated": "{0} dapat diperbarui", "{0} can be updated to version {1}": "{0} dapat diperbarui ke versi {1}", "{0} days": "{0} hari", "{0} desktop shortcuts created": "{0} pintasan desktop dibuat", "{0} failed": "{0} gagal", "{0} has been installed successfully.": "{0} telah terinstal dengan sukses.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} telah terinstal dengan sukses. Disarankan untuk me-restart UniGetUI untuk menyelesaikan instalasi", "{0} has failed, that was a requirement for {1} to be run": "{0} gagal, itu adalah persyaratan agar {1} dapat di<PERSON>an", "{0} homepage": "{0} ha<PERSON>an utama", "{0} hours": "{0} jam", "{0} installation": "{0} instalasi", "{0} installation options": "{0} opsi instalasi", "{0} installer is being downloaded": "<PERSON>ginstal {0} sedang diunduh", "{0} is being installed": "{0} sedang diinstal", "{0} is being uninstalled": "{0} sedang dihapus instalasi", "{0} is being updated": "{0} <PERSON>g diperbarui", "{0} is being updated to version {1}": "{0} sedang diperbarui ke versi {1}", "{0} is disabled": "{0} dinonaktif<PERSON>", "{0} minutes": "{0} menit", "{0} months": "{0} bulan", "{0} packages are being updated": "{0} paket sedang diperbarui", "{0} packages can be updated": "{0} paket dapat diperbarui", "{0} packages found": "{0} paket di<PERSON>n", "{0} packages were found": "{0} paket di<PERSON>n", "{0} packages were found, {1} of which match the specified filters.": "{0} pake<PERSON> di<PERSON>, {1} di antaranya cocok dengan filter yang ditentukan.", "{0} selected": "{0} dipilih", "{0} settings": "{0} pengaturan", "{0} status": "{0} status", "{0} succeeded": "{0} ber<PERSON>il", "{0} update": "{0} pembaruan", "{0} updates are available": "{0} pembaruan tersedia", "{0} was {1} successfully!": "{0} telah {1} dengan sukses!", "{0} weeks": "{0} minggu", "{0} years": "{0} tahun", "{0} {1} failed": "{0} {1} gagal", "{package} Installation": "Instalasi {package}", "{package} Uninstall": "Hapus Instalasi {package}", "{package} Update": "{package} Pembaruan", "{package} could not be installed": "{package} tidak dapat diinstal", "{package} could not be uninstalled": "{package} tidak dapat dihapus", "{package} could not be updated": "{package} tidak dapat diperbarui", "{package} installation failed": "<PERSON><PERSON><PERSON><PERSON> {package} gagal", "{package} installer could not be downloaded": "Penginstal {package} tidak dapat diunduh", "{package} installer download": "<PERSON><PERSON><PERSON> pen<PERSON>tal {package}", "{package} installer was downloaded successfully": "Penginstal {package} ber<PERSON>il diunduh", "{package} uninstall failed": "Penghapusan {package} gagal", "{package} update failed": "Pembaruan {package} gagal", "{package} update failed. Click here for more details.": "Pembaruan {package} gagal. Klik di sini untuk detail lebih lanjut.", "{package} was installed successfully": "{package} berhasil dipasang", "{package} was uninstalled successfully": "{package} ber<PERSON>il dihapus", "{package} was updated successfully": "{package} ber<PERSON>il diperbarui", "{pcName} installed packages": "<PERSON>et yang terpasang di {pcName}", "{pm} could not be found": "{pm} tidak dapat di<PERSON>n", "{pm} found: {state}": "{pm} ditemukan: {state}", "{pm} is disabled": "{pm} dinonaktifkan", "{pm} is enabled and ready to go": "{pm} diaktifkan dan siap digunakan", "{pm} package manager specific preferences": "Prefer<PERSON><PERSON> khusus pengelola paket {pm}", "{pm} preferences": "Preferensi {pm}", "{pm} version:": "Versi {pm}:", "{pm} was not found!": "{pm} tidak di<PERSON>ukan!"}