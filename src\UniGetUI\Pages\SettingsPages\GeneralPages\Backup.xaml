<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.GeneralPages.Backup"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Toolkit="using:CommunityToolkit.WinUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages.GeneralPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:services="using:UniGetUI.Services"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    mc:Ignorable="d">

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel Orientation="Vertical">


            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Package backup" />

            <Toolkit:SettingsCard CornerRadius="8">
                <Toolkit:SettingsCard.Description>
                    <StackPanel Orientation="Vertical">
                        <widgets:TranslatedTextBlock Prefix=" ● " Text="The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved." />
                        <widgets:TranslatedTextBlock Prefix=" ● " Text="The backup will NOT include any binary file nor any program's saved data." />
                        <widgets:TranslatedTextBlock Prefix=" ● " Text="The size of the backup is estimated to be less than 1MB." />
                        <widgets:TranslatedTextBlock Prefix=" ● " Text="The backup will be performed after login." />
                    </StackPanel>
                </Toolkit:SettingsCard.Description>
            </Toolkit:SettingsCard>


            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontSize="16"
                FontWeight="SemiBold"
                Text="Cloud package backup" />

            <Toolkit:SettingsCard
                x:Name="GithubIdentityCard"
                ContentAlignment="Left"
                CornerRadius="8,8,0,0">

                <Grid ColumnSpacing="8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="48" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" MaxWidth="70"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <PersonPicture
                        x:Name="GitHubImage"
                        Grid.RowSpan="2"
                        Width="48" />
                    <TextBlock
                        x:Name="GitHubUserTitle"
                        Grid.Column="1"
                        VerticalAlignment="Bottom"
                        Text="Loading..."
                        TextWrapping="WrapWholeWords" />
                    <TextBlock
                        x:Name="GitHubUserSubtitle"
                        Grid.Row="1"
                        Grid.Column="1"
                        VerticalAlignment="Top"
                        FontSize="12"
                        FontWeight="SemiBold"
                        Opacity="0.8"
                        Text="Please wait..."
                        TextWrapping="WrapWholeWords" />
                    <Button
                        x:Name="LogInButton"
                        Grid.RowSpan="2"
                        Grid.Column="2"
                        VerticalAlignment="Stretch"
                        Click="LoginWithGitHubButton_Click"
                        Style="{ThemeResource AccentButtonStyle}"
                        Visibility="Collapsed">
                        <widgets:TranslatedTextBlock Text="Log in with GitHub" WrappingMode="WrapWholeWords" />
                    </Button>
                    <Button
                        x:Name="LogOutButton"
                        Grid.RowSpan="2"
                        Grid.Column="2"
                        VerticalAlignment="Stretch"
                        Click="LogoutGitHubButton_Click"
                        Visibility="Collapsed">
                        <widgets:TranslatedTextBlock Text="Log out from GitHub" WrappingMode="WrapWholeWords" />
                    </Button>
                    <HyperlinkButton
                        x:Name="MoreInfoBtn"
                        Grid.RowSpan="2"
                        Grid.Column="3"
                        Padding="0"
                        VerticalAlignment="Stretch"
                        Click="MoreInfoBtn_OnClick">
                        <widgets:TranslatedTextBlock Text="More details" WrappingMode="WrapWholeWords" />
                    </HyperlinkButton>
                </Grid>
            </Toolkit:SettingsCard>

            <widgets:CheckboxCard
                x:Name="EnablePackageBackupCheckBox_CLOUD"
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,8,8"
                SettingName="EnablePackageBackup_CLOUD"
                Text="Periodically perform a cloud backup of the installed packages">
                <widgets:CheckboxCard.Description>
                    <widgets:TranslatedTextBlock Text="Cloud backup uses a private GitHub Gist to store a list of installed packages" />
                </widgets:CheckboxCard.Description>
            </widgets:CheckboxCard>

            <UserControl Height="16" />

            <widgets:ButtonCard
                x:Name="BackupNowButton_Cloud"
                ButtonText="Backup"
                Click="BackupToGitHubButton_Click"
                CornerRadius="8,8,0,0"
                Text="Perform a cloud backup now" />

            <widgets:ButtonCard
                x:Name="RestorePackagesFromGitHubButton"
                BorderThickness="1,0,1,1"
                ButtonText="Select backup"
                Click="RestoreFromGitHubButton_Click"
                CornerRadius="0,0,8,8"
                Text="Restore a backup from the cloud">
                <widgets:ButtonCard.Description>
                    <widgets:TranslatedTextBlock Text="Begin the process to select a cloud backup and review which packages to restore" />
                </widgets:ButtonCard.Description>
            </widgets:ButtonCard>


            <!--  END CLOUD, BEGIN LOCAL  -->


            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontSize="16"
                FontWeight="SemiBold"
                Text="Local package backup" />

            <widgets:CheckboxCard
                x:Name="EnablePackageBackupCheckBox_LOCAL"
                BorderThickness="1,1,1,0"
                CornerRadius="8,8,0,0"
                SettingName="EnablePackageBackup_LOCAL"
                StateChanged="ShowRestartBanner"
                Text="Periodically perform a local backup of the installed packages" />

            <widgets:ButtonCard
                x:Name="BackupNowButton_LOCAL"
                ButtonText="Backup"
                Click="DoBackup_LOCAL_Click"
                CornerRadius="0,0,8,8"
                IsEnabled="{x:Bind EnablePackageBackupCheckBox_LOCAL._checkbox.IsOn, Mode=OneWay}"
                Text="Perform a local backup now" />

            <UserControl Height="16" />


            <widgets:ButtonCard
                x:Name="ChangeBackupDirectory"
                ButtonText="Select"
                Click="ChangeBackupDirectory_Click"
                CornerRadius="8"
                IsEnabled="{x:Bind EnablePackageBackupCheckBox_LOCAL._checkbox.IsOn, Mode=OneWay}"
                Text="Change backup output directory">
                <Toolkit:SettingsCard.Description>
                    <StackPanel Orientation="Horizontal" Spacing="5">
                        <TextBlock Name="BackupDirectoryLabel" VerticalAlignment="Center" />
                        <HyperlinkButton Name="ResetBackupDirectory" Click="ResetBackupPath_Click" />
                        <HyperlinkButton Name="OpenBackupDirectory" Click="OpenBackupPath_Click" />
                    </StackPanel>
                </Toolkit:SettingsCard.Description>
            </widgets:ButtonCard>

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Local backup advanced options" />

            <widgets:TextboxCard
                x:Name="ChangeBackupFileNameTextBox"
                CornerRadius="8,8,0,0"
                IsEnabled="{x:Bind EnablePackageBackupCheckBox_LOCAL._checkbox.IsOn, Mode=OneWay}"
                Placeholder="Leave empty for default"
                SettingName="ChangeBackupFileName"
                Text="Set a custom backup file name" />

            <widgets:CheckboxCard
                x:Name="EnableBackupTimestampingCheckBox"
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,8,8"
                IsEnabled="{x:Bind EnablePackageBackupCheckBox_LOCAL._checkbox.IsOn, Mode=OneWay}"
                SettingName="EnableBackupTimestamping"
                Text="Add a timestamp to the backup file names" />
        </StackPanel>
    </ScrollViewer>
</Page>
