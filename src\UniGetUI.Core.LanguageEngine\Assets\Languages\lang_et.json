{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(<PERSON><PERSON><PERSON><PERSON> kontrollitud: {0})", "(Number {0} in the queue)": "(Positsioon {0} j<PERSON><PERSON><PERSON>kor<PERSON>)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@artjom3729", "0 packages found": "Pole pakette leitud", "0 updates found": "<PERSON> uuendusi leitud", "1 - Errors": "1 - <PERSON><PERSON>", "1 day": "Üks päev", "1 hour": "Üks tund", "1 month": "Üks kuu", "1 package was found": "Üks pakett on leitud", "1 update is available": "Üks uuendus on kättesaadav", "1 week": "Üks nädal", "1 year": "Üks aasta", "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": "2 - <PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": "3 - Teave (vähem)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": "4 - <PERSON><PERSON> (rohkem)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": "5 - teave (silumine)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": null, "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Repositoorium, mis on täis Microsofti .NET ökosüsteemi silmas pidades loodud tööriistu ja käivitatavaid faile.<br>Sisaldab: <b>.NETiga seotud tööriistu ja skripte</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Repositoorium, mis on täis Microsofti .NET ökosüsteemi silmas pidades loodud tööriistu.<br>Sisaldab: <b>.NETiga seotud tööriistu</b>", "A restart is required": "Taaskäivitamine on vajalik", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "Rakendusest", "About Qt6": "Qt6-st", "About WingetUI": "WingetUI`ist", "About WingetUI version {0}": "WingetUI versioonist {0}", "About the dev": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Accept": null, "Action when double-clicking packages, hide successful installations": "Toiming pakette <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, peida edukad p<PERSON>", "Add": "<PERSON>", "Add a source to {0}": "<PERSON> {0}", "Add a timestamp to the backup file names": "<PERSON> a<PERSON>", "Add a timestamp to the backup files": "<PERSON> a<PERSON>", "Add packages or open an existing bundle": "<PERSON> paketid või ava olem<PERSON><PERSON>v kimp", "Add packages or open an existing package bundle": "<PERSON> paketid või ava olemasolev paketikomplekt", "Add packages to bundle": null, "Add packages to start": "<PERSON> paketid al<PERSON>", "Add selection to bundle": "<PERSON> valik kimpu", "Add source": "<PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": "<PERSON><PERSON> {source} lisamine {manager}`isse", "Addition succeeded": "<PERSON><PERSON>", "Administrator privileges": "Administraatori õigused", "Administrator privileges preferences": "Administraatori õiguste eeli<PERSON>used", "Administrator rights": "Administraatori õigused", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "<PERSON><PERSON><PERSON> failid", "All versions": "Kõik versioonid", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Luba paketitoimingute paralleelne sooritamine", "Allow parallel installs (NOT RECOMMENDED)": "Luba paralleelsed allalaadimised (POLE SOOVITATAV)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Luba {pm} toimingute paralleelne sooritamine", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": "Alati tõsta {pm} allalaadimiste õigused vaikimisi", "Always run {pm} operations with administrator rights": "Alati käivitada {pm} toimingud administraatori õigustega", "An error occurred": "Tekkis viga:", "An error occurred when adding the source: ": "Allika lisamisel tekkis viga:", "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": "Uuenduste kontrollimisel tekkis viga:", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "<PERSON><PERSON> paketi töötlemisel tekkis viga", "An error occurred:": "Tekkis viga:", "An interal error occurred. Please view the log for further details.": "<PERSON><PERSON><PERSON>. Lisa<PERSON>abe sa<PERSON>seks vaadake logi.", "An unexpected error occurred:": "Tekkis ootamatu viga:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": "Uuendus on leitud!", "Android Subsystem": "<PERSON><PERSON>", "Another source": "<PERSON><PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": "<PERSON><PERSON><PERSON> salvestamata muudatused l<PERSON><PERSON><PERSON><PERSON> kaduma", "App Name": "<PERSON><PERSON><PERSON><PERSON> nimi", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "Rakenduse teema:", "Apply": null, "Architecture to install:": "Paigaldamisarhitektuur:", "Are these screenshots wron or blurry?": "Kas need ku<PERSON><PERSON><PERSON><PERSON> on valed või udused?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "Olete kindel?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "Küsi administraatori õigused üks kord iga toimingupartii kohta", "Ask for administrator rights when required": "<PERSON><PERSON><PERSON> administraatori <PERSON>, kui see on vajalik", "Ask once or always for administrator rights, elevate installations by default": "Küsi korra või alati administraatori õigused, tõsta allalaadimiste õigused vaikimisi", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Küsi administraatori õigused ainult üks kord (ei ole soovitatav)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authenticate to the proxy with an user and a password": null, "Author": "Looja", "Automatic desktop shortcut remover": null, "Automatically save a list of all your installed packages to easily restore them.": "Salvesta automaatselt kõigi installitud pakettide loend, et neid hõlpsasti taastada.", "Automatically save a list of your installed packages on your computer.": "Salvesta automaatselt arvutis installitud pakettide loend.", "Autostart WingetUI in the notifications area": "Käivita WingetUI automaatselt teavituste alas", "Available Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uuendus<PERSON>", "Available updates: {0}": "Kättesaadavad uuendused: {0}", "Available updates: {0}, not finished yet...": "<PERSON><PERSON><PERSON> u<PERSON> : {0}, ve<PERSON> l<PERSON>...", "Backing up packages to GitHub Gist...": null, "Backup": "Loo varukoopia", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Loo allalaaditud pakettide varuk<PERSON>ia", "Backup location": null, "Become a contributor": "Saa panustaja<PERSON>", "Become a translator": "Saage tõlk<PERSON>ks", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Beetaversiooni funktsioonid ja muud seadused, mida ei tohiks puudutada", "Both": "Mõlemad", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "<PERSON>ga siin on veel, mida saab teha, et õppida WingetUI kohta veelgi:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Lülitades paketihalduri v<PERSON>, ei ole Te<PERSON> enam võimalik selle pakette näha ega uuendada.", "Cache administrator rights and elevate installers by default": "Jäta administraatori õigused meelde ja tõsta paigaldajate õigused vaikimisi", "Cache administrator rights, but elevate installers only when required": "Jäta administraatori õigused meelde, aga tõsta paigalda<PERSON>te õigused vaja<PERSON>el", "Cache was reset successfully!": "Vahemälu lähtestati edukalt!", "Can't {0} {1}": "Ei saa {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": null, "Change backup output directory": "<PERSON><PERSON> var<PERSON><PERSON><PERSON><PERSON><PERSON> kataloog", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "<PERSON><PERSON>, kuidas UniGetUI kontrollib ja paigaldab pakettide saadaolevaid uuendusi", "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": "<PERSON><PERSON> p<PERSON>", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Kont<PERSON>i pakettide uuendusi perioodiliselt", "Check for updates": "<PERSON><PERSON><PERSON><PERSON>i", "Check for updates every:": "Kontrolli uuendusi iga:", "Check for updates periodically": "Ko<PERSON><PERSON>i uuendusi perioodiliselt", "Check for updates regularly, and ask me what to do when updates are found.": "Kont<PERSON>i regulaars<PERSON>t uuendusi ja küsi, mida teha, kui uuendused on leitud.", "Check for updates regularly, and automatically install available ones.": "<PERSON><PERSON><PERSON>i uuendusi regulaarselt ja paigalda saadaolevad automaatselt.", "Check out my {0} and my {1}!": "Külastage minu {0} ja {1}!", "Check out some WingetUI overviews": "Tutvu mõne WingetUI ülevaatega", "Checking for other running instances...": "<PERSON><PERSON><PERSON> jooksvate eksemplaride kontrollimine...", "Checking for updates...": "Uuenduste kontrollimine", "Checking found instace(s)...": "<PERSON><PERSON><PERSON>(de) kontrollimine...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": "Tühjenda vahemälu", "Clear finished operations": null, "Clear selection": "<PERSON><PERSON><PERSON>", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": "Scoop'i vahemälu kustutamine - WingetUI", "Clearing Scoop cache...": "Scoop'i vahemälu kustutamine...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Paigaldamisprotsessi alustamiseks klõpsake nuppu \"Paigalda\". Kui jätate paigalduse vahele, ei pruugi UniGetUI töötada ootuspäraselt.", "Close": "Sule", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Sule WingetUI teavitamispiirkonda", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "Käsurea väljund", "Command-line to run:": null, "Compare query against": "<PERSON><PERSON><PERSON><PERSON> pä<PERSON>ut vastu", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Komponentide informatsioon", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": "Jätka", "Contribute to the icon and screenshot repository": "Panusta i<PERSON> ja piltide repositooriumisse", "Contributors": "<PERSON><PERSON><PERSON><PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "<PERSON><PERSON><PERSON>", "Could not add source": null, "Could not add source {source} to {manager}": "<PERSON><PERSON> {source} {manager}'isse lisamine nurjus", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "Kimbu loomine nurjus", "Could not load announcements - ": "Teadete laadimine nurjus -", "Could not load announcements - HTTP status code is $CODE": "Teadete laadimine nurjus - HTTP olekukood on $CODE", "Could not remove source": null, "Could not remove source {source} from {manager}": "<PERSON><PERSON> {source} {manager}'ist eemaldamine nurjus", "Could not remove {source} from {manager}": "{source} {manager}'ist eemaldamine nurjus", "Credentials": null, "Current Version": "Praegune versioon", "Current status: Not logged in": null, "Current user": "<PERSON><PERSON><PERSON><PERSON>", "Custom arguments:": "Ko<PERSON><PERSON>ud argumendid:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Kohandatud käsurea argumendid:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "WingetUI kohandamine - ainult häkkeritele ja edasij<PERSON><PERSON><PERSON><PERSON> ka<PERSON>e", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "LAHTIÜTLUS: ME EI VASTUTA ALLALAETUD PAKETTIDE EEST. PALUN VEENDUGE, ET PAIGALDATE  AINULT USALDUSVÄÄRSET TARKVARA.", "Dark": "<PERSON><PERSON>", "Decline": null, "Default": "<PERSON><PERSON><PERSON><PERSON>", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Vaikimisi eelistused - sobib ta<PERSON>e", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "Kirjeldus:", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": null, "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": null, "Disable new share API (port 7058)": null, "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pake<PERSON>d", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "<PERSON><PERSON><PERSON>ur- ja v<PERSON><PERSON>e", "Distinguish between uppercase and lowercase": "<PERSON><PERSON><PERSON>ur- ja v<PERSON><PERSON>e", "Do NOT check for updates": "ÄRA kontrolli uuendusi", "Do an interactive install for the selected packages": "Tee valitud pakettide jaoks interaktiivne paigaldamine", "Do an interactive uninstall for the selected packages": "Tee valitud pakettide jaoks interaktiivne kustutamine", "Do an interactive update for the selected packages": "Tee valitud pakettide jaoks interaktiivne uuendus", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": null, "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": "Ära kustuta edukad toim<PERSON>ud ni<PERSON>r<PERSON>t automaatselt", "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": null, "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": null, "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "<PERSON><PERSON> <PERSON>, et WingetUI on kasulik? <PERSON><PERSON> ta<PERSON>site toetada loojat? <PERSON><PERSON> jah, siis saate {0}, see aitab väga!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": "Kas te tõesti soovite desinstallida {0, plural, one {järgmine pakett} few {järgmised {0} paketid} other {järgmised {0} paketid}}?", "Do you really want to uninstall {0} packages?": "Kas te tõesti soovite eemaldada {0, plural, one {üks pakett} few {{0} paketti} other {{0} paketti}}?", "Do you really want to uninstall {0}?": null, "Do you want to restart your computer now?": "Kas Te soovite taaskäivitada arvuti praegu?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "<PERSON><PERSON> soovite t<PERSON><PERSON><PERSON> WingetUI-d oma keelde? Vaatage, kuidas panustada <a style=\"color:{0}\" href=\"{1}\"a>SIIN!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": null, "Donate": null, "Done!": null, "Download failed": null, "Download installer": null, "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": null, "Download updated language files from GitHub automatically": "Laadi uuendatud keelefailid GitHubist automaatselt alla", "Downloading": null, "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": null, "Enable Scoop cleanup on launch": "Luba Scoopi pu<PERSON>tamine käivitamisel", "Enable WingetUI notifications": null, "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Lülita tausta API sisse (WingetUI vidinad ja jagamine, port 7058)", "Enable it to install packages from {pm}.": null, "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": null, "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": null, "Everything is up to date": "<PERSON><PERSON><PERSON> on ajakohane", "Exact match": "<PERSON><PERSON><PERSON><PERSON> vaste", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": null, "Experimental settings and developer options": "Eksperimentaalsed seaded ja tarkvaraarendaja säted", "Export": null, "Export log as a file": "Ekspordi logid failina", "Export packages": null, "Export selected packages to a file": "Ekspordi valitud paketid failisse", "Export settings to a local file": "Ekspordi seaded faili", "Export to a file": null, "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "<PERSON>iimas<PERSON> teadete hank<PERSON>, palun oodake...", "Filters": "Filtrid", "Finish": null, "Follow system color scheme": null, "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "<PERSON><PERSON><PERSON> ARMi kompileeritud winget versioon (AINULT ARM64 SÜSTEEMIDE JAOKS)", "Formerly known as WingetUI": "<PERSON><PERSON><PERSON> tuntud kui <PERSON>", "Found": null, "Found packages: ": null, "Found packages: {0}": null, "Found packages: {0}, not finished yet...": null, "General preferences": "Üldised seaded", "GitHub profile": null, "Global": null, "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": null, "Great! You are on the latest version.": null, "Grid": null, "Help": "<PERSON><PERSON>", "Help and documentation": "Abi ja dokumentatsioon", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, minu nimi on Martí ja mina olen WingetUI <i>arendaja</i>. WingetUI on täielikult tehtud minu vabal ajal!", "Hide details": "<PERSON><PERSON><PERSON>", "Homepage": null, "Hooray! No updates were found.": "Hurraa! Uuendusi pole leitud.", "How should installations that require administrator privileges be treated?": null, "How to add packages to a bundle": null, "I understand": null, "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Eira selle paketi tulevasi uuendusi", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "<PERSON><PERSON> valituid pakette", "Ignore special characters": "<PERSON><PERSON>", "Ignore updates for the selected packages": "<PERSON><PERSON> valitud pakettide u<PERSON>i", "Ignore updates for this package": "<PERSON>ira selle paketi u<PERSON>i", "Ignored updates": "Eira<PERSON><PERSON><PERSON> uuendus<PERSON>", "Ignored version": "Eiratav versioon", "Import": null, "Import packages": null, "Import packages from a file": null, "Import settings from a local file": "<PERSON><PERSON><PERSON> seaded failist", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": null, "Install": null, "Install Scoop": "<PERSON><PERSON><PERSON>", "Install and more": null, "Install and update preferences": null, "Install as administrator": null, "Install available updates automatically": "Paigalda saadaolevad uuendused automaatselt", "Install location can't be changed for {0} packages": null, "Install location:": "Paigaldamiskoht:", "Install options": null, "Install packages from a file": null, "Install prerelease versions of UniGetUI": null, "Install selected packages": "Paigalda valitud paketid", "Install selected packages with administrator privileges": "Paigalda valitud paketid administraatori õigustega", "Install selection": "Paigalda valik", "Install the latest prerelease version": "Paigalda viimane väljalaske-eelne versioon", "Install updates automatically": "Paigalda uuendusi automaatselt", "Install {0}": null, "Installation canceled by the user!": "Paigaldamine tühistatud kasutaja poolt!", "Installation failed": "Paigaldamine e<PERSON>", "Installation options": "Paigaldamise seaded", "Installation scope:": "<PERSON><PERSON><PERSON><PERSON> ulatus:", "Installation succeeded": null, "Installed Packages": "Paigaldatud paketid", "Installed Version": "Paigaldatud versioon:", "Installed packages": null, "Installer SHA256": null, "Installer SHA512": null, "Installer Type": null, "Installer URL": null, "Installer not available": null, "Instance {0} responded, quitting...": "{0} <PERSON><PERSON>, väljun...", "Instant search": "<PERSON><PERSON><PERSON><PERSON>", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "Interaktiivne p<PERSON>dus", "Interactive operation": null, "Interactive uninstall": "Interaktiivne kustutamine", "Interactive update": "Interaktiivne uuendus", "Internet connection settings": null, "Is this package missing the icon?": null, "Is your language missing or incomplete?": "<PERSON><PERSON> teie keel on puudu või puudulik?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": null, "Language": null, "Language, theme and other miscellaneous preferences": "<PERSON><PERSON>, teema ja muud eelist<PERSON>", "Last updated:": "Viimati uuendatud:", "Latest": "<PERSON><PERSON><PERSON><PERSON>", "Latest Version": "<PERSON><PERSON><PERSON><PERSON> versio<PERSON>", "Latest Version:": "V<PERSON><PERSON>e versioon:", "Latest details...": "Viimased detailid...", "Launching subprocess...": null, "Leave empty for default": null, "License": null, "Licenses": null, "Light": null, "List": null, "Live command-line output": null, "Live output": null, "Loading UI components...": null, "Loading WingetUI...": null, "Loading packages": null, "Loading packages, please wait...": null, "Loading...": null, "Local": null, "Local PC": null, "Local backup advanced options": null, "Local machine": "<PERSON><PERSON><PERSON> jao<PERSON>", "Local package backup": null, "Locating {pm}...": null, "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "<PERSON><PERSON><PERSON> tase:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": null, "Machine | Global": "Seade | Globaalne", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "Halda WingetUI automaatilise käivitamise käitumine rakendusest \"Seaded\"", "Manage ignored packages": "<PERSON><PERSON> pake<PERSON>d", "Manage ignored updates": "<PERSON><PERSON> e<PERSON> uuendused", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": "<PERSON><PERSON> {0} allikat", "Manifest": null, "Manifests": null, "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": null, "Missing dependency": null, "More": "Rohkem", "More details": "Rohkem detaile", "More details about the shared data and how it will be processed": null, "More info": "Lisainfo", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": null, "New": null, "New Version": "<PERSON><PERSON> versioon", "New bundle": "<PERSON><PERSON> kimp", "New version": "<PERSON><PERSON> versioon", "Nice! Backups will be uploaded to a private gist on your account": null, "No": null, "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "<PERSON><PERSON> ei leitud", "No packages found matching the input criteria": null, "No packages have been added yet": null, "No packages selected": "Pakette pole valitud", "No packages were found": "<PERSON>ette pole leitud", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": null, "No sources found": "Allikaid ei leitud", "No sources were found": null, "No updates are available": "<PERSON> k<PERSON><PERSON><PERSON>", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": null, "Not available": null, "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": null, "Not right now": null, "Notes:": "Märkmed:", "Notification preferences": "Teavi<PERSON><PERSON> eelist<PERSON>", "Notification tray options": "Teavituste riba seaded", "Notification types": null, "NuPkg (zipped manifest)": null, "OK": null, "Ok": null, "Open": "Ava", "Open GitHub": "<PERSON>", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": "Ava WingetUI", "Open backup location": "Ava varukoopiate asukoht", "Open existing bundle": "<PERSON> o<PERSON><PERSON>v kimp", "Open install location": null, "Open the welcome wizard": null, "Operation canceled by user": "Toiming t<PERSON><PERSON><PERSON><PERSON> ka<PERSON>t ", "Operation cancelled": "To<PERSON><PERSON>", "Operation history": "<PERSON><PERSON>inguajalug<PERSON>", "Operation in progress": "Toiming prots<PERSON>is", "Operation on queue (position {0})...": "Toiming <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (positsioon {0})...", "Operation profile:": null, "Options saved": "Seaded salvestatud", "Order by:": null, "Other": null, "Other settings": null, "Package": null, "Package Bundles": "Pakettide kimbud", "Package ID": "Paketi ID", "Package Manager": null, "Package Manager logs": "Paketihal<PERSON><PERSON> logid", "Package Managers": null, "Package Name": "<PERSON><PERSON> nimetus", "Package backup": null, "Package backup settings": null, "Package bundle": "<PERSON><PERSON><PERSON> kimp", "Package details": "Paketi info", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "Package managers": null, "Package not found": null, "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": null, "Packages found: {0}": null, "Partially": null, "Password": null, "Paste a valid URL to the database": null, "Pause updates for": null, "Perform a backup now": "<PERSON><PERSON> varuk<PERSON>ia praegu", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "<PERSON><PERSON><PERSON> varunda<PERSON>, palun oodake...", "Periodically perform a backup of the installed packages": "Tee perioodiliselt paigaldatud pakettide varukoopia", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": null, "Please enter at least 3 characters": null, "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON> t<PERSON>, et teatud paketid ei pruugi olla selles arvutis lubatud paketihaldurite tõttu paigaldatavad.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": null, "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "<PERSON><PERSON><PERSON> vaadake käsurea väljundit või vaadake probleemi kohta lisateavet toiminguajaloost.", "Please select how you want to configure WingetUI": null, "Please try again later": null, "Please type at least two characters": null, "Please wait": null, "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": null, "Portable": null, "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": null, "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": null, "Preparing packages, please wait...": null, "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": null, "Publisher": null, "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": null, "Quit": "Välju", "Quit WingetUI": "Välju WingetUI-ist", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": "Paigalda pakett uuesti", "Related settings": null, "Release notes": "<PERSON><PERSON><PERSON><PERSON><PERSON> märk<PERSON>", "Release notes URL": "Väljalase märkmete URL", "Release notes URL:": "Väljalase märkmete URL:", "Release notes:": "<PERSON><PERSON><PERSON><PERSON><PERSON> märkmed:", "Reload": null, "Reload log": "<PERSON>adi logid u<PERSON>i", "Removal failed": "Eemalda<PERSON> e<PERSON>", "Removal succeeded": null, "Remove from list": null, "Remove permanent data": null, "Remove selection from bundle": "Kustuta valik kimbust", "Remove successful installs/uninstalls/updates from the installation list": null, "Removing source {source}": null, "Removing source {source} from {manager}": null, "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": null, "Repository": null, "Reset": "Lähtesta", "Reset Scoop's global app cache": "Lähtesta Scoopi rakenduse süsteemis vahemälu", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "Lähtesta Winget allikad (v<PERSON><PERSON>, kui pakette pole loetletud)", "Reset WingetUI": "Lähtesta WingetUI", "Reset WingetUI and its preferences": "Lähtesta WingetUI ja selle seaded", "Reset WingetUI icon and screenshot cache": "Lähtesta WingetUI-i ikooni ja kuvatõmmise vähemälu", "Reset list": null, "Resetting Winget sources - WingetUI": "Winget allikate lähtestamine - WingetUI", "Restart": null, "Restart UniGetUI": "Taaskäivita UniGetUI", "Restart WingetUI": "Taaskäivita WingetUI", "Restart WingetUI to fully apply changes": "Taaskäivita WingetUI muudatuste täielikuks rakendamiseks", "Restart later": "Taaskäiv<PERSON>", "Restart now": "Taaskäivita praegu", "Restart required": "Taaskäivitamine on vajalik", "Restart your PC to finish installation": null, "Restart your computer to finish the installation": null, "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": null, "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": null, "Return to top": null, "Run": null, "Run as admin": "Käivita administraatori õigustega", "Run cleanup and clear cache": "<PERSON><PERSON><PERSON><PERSON> pu<PERSON> ja puhasta vah<PERSON>", "Run last": null, "Run next": null, "Run now": null, "Running the installer...": null, "Running the uninstaller...": null, "Running the updater...": "Uuendaja käivitamine", "Save": null, "Save File": "Salvesta fail", "Save and close": "Salvesta ja sule", "Save as": null, "Save bundle as": "<PERSON><PERSON><PERSON> kimp kui", "Save now": "Salvesta praegu", "Saving packages, please wait...": null, "Scoop Installer - WingetUI": "<PERSON><PERSON><PERSON> - WingetUI", "Scoop Uninstaller - WingetUI": "Scoopi des<PERSON>tal<PERSON> - WingetUI", "Scoop package": "<PERSON><PERSON><PERSON> pakett", "Search": "<PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": null, "Search for packages": "<PERSON><PERSON><PERSON> p<PERSON>", "Search for packages to start": null, "Search mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Search on available updates": "<PERSON><PERSON><PERSON> uuendused", "Search on your software": null, "Searching for installed packages...": "Paigaldatud pakettide otsimine...", "Searching for packages...": "Pakettide otsimine...", "Searching for updates...": "Uuenduste otsimine", "Select": "Vali", "Select \"{item}\" to add your custom bucket": null, "Select a folder": "<PERSON><PERSON> ka<PERSON>", "Select all": "Vali kõik", "Select all packages": "Vali kõik paketid", "Select backup": null, "Select only <b>if you know what you are doing</b>.": null, "Select package file": "<PERSON>i paketi fail", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "<PERSON><PERSON> allika<PERSON>, mida soovite lisada", "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": null, "Sent handshake. Waiting for instance listener's answer... ({0}%)": "<PERSON><PERSON>in k<PERSON>epigistuse. Ootan kuulaja vastust... ({0}%)", "Set a custom backup file name": "<PERSON><PERSON><PERSON><PERSON> kohanda<PERSON>d var<PERSON> nimi", "Set custom backup file name": "<PERSON><PERSON><PERSON><PERSON> kohanda<PERSON>d var<PERSON> nimi", "Settings": "Seaded", "Share": null, "Share WingetUI": null, "Share anonymous usage data": null, "Share this package": null, "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Näita UniGetUI tegumiribal", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": null, "Show a notification when an installation fails": "<PERSON><PERSON><PERSON>, kui pai<PERSON><PERSON><PERSON> e<PERSON>", "Show a notification when an installation finishes successfully": null, "Show a notification when an operation fails": "<PERSON><PERSON><PERSON>, kui toim<PERSON> e<PERSON>", "Show a notification when an operation finishes successfully": "<PERSON><PERSON><PERSON>, kui toiming l<PERSON><PERSON><PERSON> edukalt", "Show a notification when there are available updates": null, "Show a silent notification when an operation is running": "Näita vaikiv teavitus toimingu käivitamisel", "Show details": "<PERSON><PERSON><PERSON>", "Show in explorer": null, "Show info about the package on the Updates tab": null, "Show missing translation strings": null, "Show notifications on different events": null, "Show package details": "Näita paketi info", "Show package icons on package lists": null, "Show similar packages": "<PERSON><PERSON><PERSON> sarna<PERSON> paketid", "Show the live output": null, "Size": null, "Skip": "<PERSON><PERSON><PERSON> v<PERSON>", "Skip hash check": "Jäta räsikontroll vahele", "Skip hash checks": null, "Skip integrity checks": "Jäta terviklikkuse kontroll vahele", "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "Jäta räsikontroll valitud pakettide paigaldamisel vahele", "Skip the hash check when updating the selected packages": "Jäta räsikontroll valitud pakettide uuendamisel vahele", "Skip this version": "<PERSON><PERSON>a versioon vahele", "Software Updates": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>", "Something went wrong": null, "Something went wrong while launching the updater.": null, "Source": "<PERSON><PERSON><PERSON>", "Source URL:": "Allika URL:", "Source added successfully": null, "Source addition failed": "<PERSON>ika lisamine e<PERSON>", "Source name:": "<PERSON><PERSON> ni<PERSON>:", "Source removal failed": "Allika kustutamine e<PERSON>", "Source removed successfully": null, "Source:": "Allikas:", "Sources": "Allikad:", "Start": null, "Starting daemons...": null, "Starting operation...": null, "Startup options": null, "Status": null, "Stuck here? Skip initialization": "<PERSON>te siia kinni j<PERSON>änud? Jäta initsialiseerimine vahele", "Suport the developer": null, "Support me": null, "Support the developer": null, "Systems are now ready to go!": null, "Telemetry": null, "Text": null, "Text file": null, "Thank you ❤": null, "Thank you 😉": null, "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": "Varukoopia EI sisalda ühtegi binaarfaili ega programmi salvestatud andmeid.", "The backup will be performed after login.": "Varukoopia tehakse pärast sisselogimist.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Varukoopia sisaldab paigaldatud pakettide täielikku loendit ja nende paigaldamisvõimalusi. Salvestatakse ka ignoreeritud värskendusi ja vahele jäetud versioone.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Paigaldaja kontrollsumma ei lange kokku eeldatava väärtusega ja paigaldaja autentsust ei saa kontrollida. Kui usaldate väljaandjat, {0} pakett uuesti räsikontrolli vahele j<PERSON>.\n", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": null, "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Järgmised paketid eksporditakse JSON-faili. <PERSON><PERSON><PERSON><PERSON><PERSON> and<PERSON>d ega ka<PERSON>ile ei salvestata.", "The following packages are going to be installed on your system.": null, "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "<PERSON><PERSON><PERSON><PERSON><PERSON> seadeid rakendatakse iga kord, kui see pakett on paigaldatud, uuendatud või eemaldatud.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "<PERSON><PERSON><PERSON><PERSON><PERSON> seadeid rakendatakse iga kord, kui see pakett on paigaldatud, uuendatud või eemaldatud. Need salvestatakse automaatselt.", "The icons and screenshots are maintained by users like you!": "<PERSON><PERSON><PERSON>d ja pildid on hooldatud kasutajate nagu sina poolt!", "The installer authenticity could not be verified.": "Paigaldaja autentsuse tõendamine nurjus", "The installer has an invalid checksum": null, "The installer hash does not match the expected value.": "Paigaldaja räsi ei vasta eeldatavale väärtusele.", "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Selle projekti peamine e<PERSON>rk on luua intuitiivne kasu<PERSON>ides, et hallata Windowsi levinumaid CLI-paketi-haldureid nagu Winget ja <PERSON>.", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": "<PERSON><PERSON>de kimbu loomine nurjus vea tõttu", "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": null, "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "<PERSON>in loet<PERSON>ud pakette ei võeta arvesse värskenduste kontrollimisel. Topeltklõpsake neid või klõpsake nende paremal olevat nuppu, et lõpetada nende värskenduste ignoreerimine.", "The selected packages have been blacklisted": null, "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Varukoopia suurus on hinnanguliselt alla 1MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON> {source} oli lisatud {manager}isse edukalt", "The source {source} was removed from {manager} successfully": null, "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": null, "The update will not continue.": "Uuendamine ei j<PERSON>.", "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "<PERSON><PERSON><PERSON>s on toimingud. WingetUI-ist väljumine võib põhjustada nende ebaõnnestumist. Kas Te tahate jätkata?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": null, "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "On kaks peamist p<PERSON><PERSON><PERSON><PERSON>, miks mitte käivitada WingetUI administraatorina: <PERSON><PERSON><PERSON><PERSON> on see, et Scoopi paketihaldur võib administraatori õigustega käivitamisel põhjustada probleeme mõnede käskudega. <PERSON><PERSON> on see, et WingetUI administraatorina käivitamine tähendab, et mis tahes pakett, mille alla laadite, käivitatakse administraatorina (ja see ei ole ohutu). Mäletage, et kui Teil on vaja installida konkreetne pakett administraatorina, saate alati paremklõpsata asja -> Paigalda/Uuenda/Desinstalli administraatorina.\n\n", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Käimas on paigaldamine. Kui Te väljute WingetUI-ist, paigaldus võib ebaõnnestuda ja tekitada ootamatu tulemusi. Kas Te ikkagi tahate väljuda WingetUI-ist?", "They are the programs in charge of installing, updating and removing packages.": null, "Third-party licenses": null, "This could represent a <b>security risk</b>.": null, "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": null, "This is the <b>default choice</b>.": null, "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": null, "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": null, "This package can be updated to version {0}": "<PERSON>da paketti saab uuendada versiooniks {0}", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "<PERSON>s sellel paketil pole ekraanipilte või puudub ikoon? <PERSON><PERSON>-le, lisades puuduolevad ikoonid ja pildid meie avatud avalikku and<PERSON><PERSON><PERSON>.", "This package is already installed": "See pakett on juba paigaldatud", "This package is being processed": null, "This package is not available": null, "This package is on the queue": null, "This process is running with administrator privileges": null, "This project has no connection with the official {0} project — it's completely unofficial.": null, "This setting is disabled": null, "This wizard will help you configure and customize WingetUI!": null, "Toggle search filters pane": "<PERSON><PERSON><PERSON>a o<PERSON>ingufiltrite paneel", "Translators": null, "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON><PERSON><PERSON> siia lisatava allika nimi ja URL, mis on eraldatud tü<PERSON>ga.", "Unable to find package": null, "Unable to load informarion": null, "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI ei ole seotud ühegi ühilduva paketihalduriga. UniGetUI on iseseisev projekt.", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": null, "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON> (ja selle paketid)", "Uninstall and more": null, "Uninstall and remove data": null, "Uninstall as administrator": null, "Uninstall canceled by the user!": "Desinstallimine tühistatud kasutaja poolt!", "Uninstall failed": "Desinstallimine e<PERSON>", "Uninstall options": null, "Uninstall package": null, "Uninstall package, then reinstall it": "<PERSON><PERSON><PERSON> pakett, siis paigalda see uuesti", "Uninstall package, then update it": null, "Uninstall previous versions when updated": null, "Uninstall selected packages": "Kustuta valitud paketid", "Uninstall selection": null, "Uninstall succeeded": null, "Uninstall the selected packages with administrator privileges": "Kustuta valitud paketid administraatori õigustega", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": null, "Unknown": null, "Unknown size": null, "Unset or unknown": null, "Up to date": null, "Update": "<PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Uuenda WingetUI automaatselt", "Update all": "Uuenda kõik", "Update and more": null, "Update as administrator": null, "Update check frequency, automatically install updates, etc.": null, "Update date": null, "Update failed": "<PERSON><PERSON><PERSON><PERSON>", "Update found!": "Uuendus leitud!", "Update now": "Uuenda praegu", "Update options": null, "Update package indexes on launch": null, "Update packages automatically": "Uuenda paketid automaatselt", "Update selected packages": "<PERSON>uenda valitud paketid", "Update selected packages with administrator privileges": null, "Update selection": null, "Update succeeded": "<PERSON><PERSON><PERSON><PERSON>", "Update to version {0}": null, "Update to {0} available": "{0} uuendus leitud", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "<PERSON><PERSON><PERSON><PERSON>", "Updates available!": "Uuendused kättes<PERSON>val!", "Updates for this package are ignored": "<PERSON><PERSON> p<PERSON> on eiratud", "Updates found!": "Uuendused leitud!", "Updates preferences": "Uuenduste eelistused", "Updating WingetUI": null, "Url": null, "Use Legacy bundled WinGet instead of PowerShell CMDLets": null, "Use a custom icon and screenshot database URL": null, "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": null, "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": null, "Use system Winget (Needs a restart)": null, "Use system Winget (System language must be set to english)": "<PERSON><PERSON><PERSON> (Süsteemi keel peab olema inglise)", "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": null, "User": "<PERSON><PERSON><PERSON><PERSON>", "User interface preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eeli<PERSON>", "User | Local": "Kasutaja | Lokaalne", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "WingetUI kasutamine tähendab GNU Lesser General Public License v2.1 litsentsi aktsepteerimist", "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": "Versioon", "Version to install:": "Versioon paigaldamiseks:", "Version:": null, "View GitHub Profile": null, "View WingetUI on GitHub": null, "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Vaata WingetUI lähtekoodi. Sealt saate teatada vigadest või soovitada funktsioone või isegi panustada otse WingetUI projektile", "View mode:": null, "View on UniGetUI": null, "View page on browser": "<PERSON><PERSON><PERSON><PERSON>", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": null, "Waiting for {0} to complete...": null, "Warning": null, "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": null, "We could not load detailed information about this package, because it was not installed from an available package manager.": null, "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Me ei saanud {action} {package}. Palun proovige hiljem uuesti. Paigaldaja logide saamiseks klõpsa \"{showDetails}\".", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Me ei saanud {action} {package}. Palun proovige hiljem uuesti. Desinstallija logide saamiseks klõpsa \"{showDetails}\".", "We couldn't find any package": null, "Welcome to WingetUI": null, "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": null, "Which source do you want to add?": null, "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "<PERSON><PERSON>t saab kasutada WingetUI-s, saab WingetUI-d kasutada koos teiste paketihalduritega, mis võib olla segane. Varem oli WingetUI loodud töötama ainult koos Wingetiga, kuid see ei ole enam tõsi ja seega ei esinda WingetUI seda, milleks see projekt on mõeldud.", "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": null, "WingetUI - Everything is up to date": "WingetUI - kõik on ajakohane", "WingetUI - {0} updates are available": null, "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": null, "WingetUI Log": "WingetUI logid", "WingetUI Repository": null, "WingetUI Settings": "WingetUI seaded", "WingetUI Settings File": "WingetUI seadmete fail", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": "WingetUI versioon {0}", "WingetUI autostart behaviour, application launch settings": null, "WingetUI can check if your software has available updates, and install them automatically if you want to": null, "WingetUI display language:": "WingetUI kuvamiskeel:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI on administraatori õigustega tööle käivitatud, mis ei ole soovitatav. WingetUI administraatorina käivitamisel on IGAL WingetUI-st käivitatud operatsioonil administraatori õigused. Saate programmi veel kasutada, kuid soovitame tungivalt mitte käivitada WingetUI administraatori õigustega.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "<PERSON><PERSON><PERSON> vabatahtlikele tõlkijatele on WingetUI tõlgitud rohkem kui 40 keelde. Tänan teid 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI ei ole ma<PERSON>tõlgitud. Tõlkeid on juh<PERSON>ud järgmised kasuta<PERSON>d:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": null, "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI on vaba ning tasuta ja jääbki vaba ning tasuta igavesti. Ei mingeid reklaame, krediitkaarti ega premium versiooni. 100% tasuta, igavesti.", "WingetUI log": "WingetUI logid", "WingetUI tray application preferences": "WingetUI tegumiriba rakenduse eelistused", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": "Toimub WingetUI versiooni {0} allalaadimine.", "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": null, "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": null, "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI poleks olnud võimalik meie kallite panustajate abita. Vaata nende GitHub profiili, WingetUI ei oleks võimalik ilma nendeta!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI poleks olnud võimalik ilma panustajate abita. Tänan teid kõiki 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} on valmis paigaldamiseks", "Write here the process names here, separated by commas (,)": null, "Yes": null, "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "Praegune rakenduse versioon on {0}", "You have installed WingetUI Version {0}": "Te olete paigaldanud WingetUI versiooni {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": null, "You will be prompted only once, and administrator rights will be granted to packages that request them.": null, "You will be prompted only once, and every future installation will be elevated automatically.": null, "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": null, "extracted": null, "feature": null, "formerly WingetUI": "varem <PERSON>et<PERSON>", "homepage": null, "install": null, "installation": null, "installed": "paigaldatud", "installing": null, "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": null, "uninstallation": null, "uninstalled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstalling": null, "update(noun)": "uuendus", "update(verb)": "<PERSON><PERSON>da", "updated": "uuenda<PERSON><PERSON>", "updating": null, "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": null, "{0} aborted": null, "{0} can be updated": "{0} sa<PERSON> <PERSON><PERSON><PERSON><PERSON>", "{0} can be updated to version {1}": "{0} saab uuendada versioonini {1}", "{0} days": "{0} {0, plural,\n=0 {p<PERSON><PERSON>}\none {päev}\nother {päeva}\n}", "{0} desktop shortcuts created": null, "{0} failed": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} has been installed successfully.": "{0} oli eduka<PERSON> p<PERSON>d", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} {0, plural,\n=0 {tundi}\none {tund}\nother {tundi}\n}", "{0} installation": null, "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": "{0} paigaldatakse", "{0} is being uninstalled": "{0} desinstallitakse", "{0} is being updated": "{0} u<PERSON><PERSON><PERSON>e", "{0} is being updated to version {1}": "{0} uuendatakse versioonini {1}", "{0} is disabled": null, "{0} minutes": "{0} {0, plural,\n=0 {minutit}\none {minut}\nother {minutit}\n}", "{0} months": null, "{0} packages are being updated": null, "{0} packages can be updated": null, "{0} packages found": "{count, plural,\n=0 {{0} paketti on leitud} one {{0} pakett on leitud} other {{0} paketti on leitud}}", "{0} packages were found": "{0, plural,\n=0 {{0} paketti on leitud} one {{0} pakett on leitud} other {{0} paketti on leitud}}", "{0} packages were found, {1} of which match the specified filters.": "{0} {0, plural,\n=0 {paketti on leitud}\none {pakett on leitud}\nother {paketti on leitud}}, millest {1} {1, plural,\n=0 {vastavad}\none {vastab}\nother {vastavad}} määratud filtrile", "{0} settings": null, "{0} status": null, "{0} succeeded": null, "{0} update": null, "{0} updates are available": null, "{0} was {1} successfully!": null, "{0} weeks": null, "{0} years": null, "{0} {1} failed": "{0} {1} <PERSON><PERSON><PERSON><PERSON><PERSON>", "{package} Installation": null, "{package} Uninstall": null, "{package} Update": null, "{package} could not be installed": "{package} pai<PERSON><PERSON>ine nurjus", "{package} could not be uninstalled": "{package} desinstallimine nurjus", "{package} could not be updated": null, "{package} installation failed": "{package} pai<PERSON><PERSON><PERSON> e<PERSON>", "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": "{package} desinstallimine ebaõnnestus", "{package} update failed": "{package} u<PERSON><PERSON><PERSON> e<PERSON>", "{package} update failed. Click here for more details.": "{package} uuendamine e<PERSON>nnestus. Klõpsake siin, et saada rohkem informatsiooni.", "{package} was installed successfully": "{package} oli edukalt pai<PERSON>d", "{package} was uninstalled successfully": "{package} oli edukalt desinstallitud", "{package} was updated successfully": null, "{pcName} installed packages": null, "{pm} could not be found": "Ei saanud leida {pm}", "{pm} found: {state}": null, "{pm} is disabled": null, "{pm} is enabled and ready to go": null, "{pm} package manager specific preferences": null, "{pm} preferences": "{pm} eelistused", "{pm} version:": "{pm} versioon:", "{pm} was not found!": null}