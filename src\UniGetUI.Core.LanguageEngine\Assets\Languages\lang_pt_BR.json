{"\"{0}\" is a local package and can't be shared": "\"{0}\" é um pacote local e não pode ser compartilhado", "\"{0}\" is a local package and does not have available details": "\"{0}\" é um pacote local e não possui detalhes suficientes", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" é um pacote local e não é compatível com este recurso", "(Last checked: {0})": "(Última verificação: {0})", "(Number {0} in the queue)": "(Posição {0} na fila)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@thia<PERSON><PERSON><PERSON><PERSON>, @ppvnf, @wanderleihuttel, @maison<PERSON><PERSON><PERSON>, @<PERSON><PERSON><PERSON><PERSON><PERSON>", "0 packages found": "0 pacotes encontrados", "0 updates found": "0 atualizações encontradas", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 dia", "1 hour": "1 hora", "1 month": "1 mês", "1 package was found": "1 pacote foi encontrado", "1 update is available": "1 atualização está disponível", "1 week": "1 semana", "1 year": "1 ano", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON> até a página \"{0}\" ou \"{1}\".", "2 - Warnings": "2 - Avisos", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Encontre os pacotes que deseja adicionar ao conjunto e selecione a caixa de seleção mais à esquerda.", "3 - Information (less)": "3 - Informações (resumidas)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON>uan<PERSON> os pacotes que deseja adicionar ao conjunto estiverem selecionados, localize e clique na opção \"{0}\" na barra de ferramentas.", "4 - Information (more)": "4 - Informações (detalhadas)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON> seus pacotes serão adicionados ao conjunto. Você pode continuar adicionando pacotes ou exportar o conjunto.", "5 - information (debug)": "5 - Informações (depuração)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Um gerenciador de bibliotecas C/C++ bastante popular. Repleto de bibliotecas C/C++ e outras ferramentas relacionadas a C/C++<br>Contém: <b>Bibliotecas C/C++ e ferramentas relacionadas</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Um repositório repleto de ferramentas e executáveis projetados para o ecossistema .NET da Microsoft.<br>Contém: <b>ferramentas e scripts relacionados ao .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Um repositório repleto de ferramentas projetadas para o ecossistema .NET da Microsoft.<br>Contém: <b>Ferramentas relacionadas ao .NET</b>", "A restart is required": "É necessária uma reinicialização", "Abort install if pre-install command fails": "Cancelar instalação se o comando de pré-instalação falhar", "Abort uninstall if pre-uninstall command fails": "Cancelar desinstalação se o comando de pré-deinstalação falhar", "Abort update if pre-update command fails": "Cancelar atualização se o comando de pré-atualização falhar", "About": "Sobre", "About Qt6": "Sobre o Qt6", "About WingetUI": "Sobre o UniGetUI", "About WingetUI version {0}": "Sobre o UniGetUI versão {0}", "About the dev": "Sobre o desenvolvedor", "Accept": "Aceitar", "Action when double-clicking packages, hide successful installations": "Ação ao clicar duas vezes nos pacotes, ocultar instalações concluídas", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "Adicionar uma fonte a {0}", "Add a timestamp to the backup file names": "Adicionar um registro de data e hora aos nomes dos arquivos de backup", "Add a timestamp to the backup files": "Adicionar um registro de data e hora aos arquivos de backup", "Add packages or open an existing bundle": "Adicionar paco<PERSON> ou abrir uma coleção existente", "Add packages or open an existing package bundle": "Adicione paco<PERSON> ou abra uma coleção existente", "Add packages to bundle": "<PERSON><PERSON><PERSON><PERSON> pacotes ao conjunto", "Add packages to start": "Adicione pacotes para começar", "Add selection to bundle": "Adicionar seleção à coleção", "Add source": "<PERSON><PERSON><PERSON><PERSON> fonte", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Adicionar atualizações com falha 'nenhuma atualização aplicável encontrada' à lista de atualizações ignoradas", "Adding source {source}": "Adicionando fonte {source}", "Adding source {source} to {manager}": "Adicionando a fonte {source} ao {manager}", "Addition succeeded": "<PERSON><PERSON><PERSON> bem-sucedida", "Administrator privileges": "Privilégios de administrador", "Administrator privileges preferences": "Preferências de privilégios de administrador", "Administrator rights": "Privilégios de administrador", "Administrator rights and other dangerous settings": "Direitos de administrador e outras configurações perigosas", "Advanced options": "Opções avançadas", "All files": "Todos os arquivos", "All versions": "<PERSON><PERSON> as vers<PERSON><PERSON>", "Allow changing the paths for package manager executables": "Permitir alterar caminhos para executáveis de gerenciadores de pacotes", "Allow custom command-line arguments": "Permitir argumentos de linha de comando personalizados", "Allow importing custom command-line arguments when importing packages from a bundle": "Permitir a importação de argumentos de linha de comando personalizados ao importar pacotes", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Permitir a importação de comandos personalizados de pré-instalação e pós-instalação ao importar pacotes", "Allow package operations to be performed in parallel": "Permitir que operações de pacotes sejam realizadas em paralelo", "Allow parallel installs (NOT RECOMMENDED)": "Permitir instalaçõ<PERSON> paralelas (NÃO RECOMENDADO)", "Allow pre-release versions": "Permitir ve<PERSON> de pré-lançamento", "Allow {pm} operations to be performed in parallel": "Permitir que operações do {pm} sejam executadas em paralelo", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Como alternativa, você também pode instalar {0} executando o seguinte comando no prompt do Windows PowerShell:", "Always elevate {pm} installations by default": "Sempre elevar as instalações do {pm} por padrão", "Always run {pm} operations with administrator rights": "Sempre executar operações do {pm} com privilégios de administrador", "An error occurred": "Ocorreu um erro\n", "An error occurred when adding the source: ": "Ocorreu um erro ao adicionar a fonte:", "An error occurred when attempting to show the package with Id {0}": "Ocorreu um erro ao exibir o pacote com ID {0}", "An error occurred when checking for updates: ": "Ocorreu um erro ao verificar se há atualizações:", "An error occurred while attempting to create an installation script:": "Ocorreu um erro ao tentar criar o script de instalação:", "An error occurred while loading a backup: ": "Ocorreu um erro ao carregar o backup:", "An error occurred while logging in: ": "Ocorreu um erro ao fazer login:", "An error occurred while processing this package": "Ocorreu um erro ao processar este pacote", "An error occurred:": "Ocorreu um erro:", "An interal error occurred. Please view the log for further details.": "Foi identificado um erro interno. Verifique o log para obter mais informações.", "An unexpected error occurred:": "Ocorreu um erro inesperado:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Ocorreu um problema inesperado ao tentar reparar o WinGet. Por favor, tente novamente mais tarde", "An update was found!": "Uma atualização foi encontrada!", "Android Subsystem": "Subsistema Android", "Another source": "Outra fonte", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Quaisquer atalhos criados durante a instalação ou atualização serão excluídos automaticamente, em vez de mostrar um prompt de confirmação na primeira vez que forem detectados.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Quaisquer atalhos criados ou modificados fora do UniGetUI serão ignorados. Você poderá adicioná-los com o botão {0}.", "Any unsaved changes will be lost": "Quaisquer alterações não salvas serão perdidas", "App Name": "Nome do Aplicativo", "Appearance": "Aparência", "Application theme, startup page, package icons, clear successful installs automatically": "Tema do aplicativo, p<PERSON><PERSON>a inicial, ícones dos pacotes, remover instalações concluídas automaticamente", "Application theme:": "Tema do aplicativo:", "Apply": "Aplicar", "Architecture to install:": "Arquitetura para instalar:", "Are these screenshots wron or blurry?": "Estas capturas de tela estão erradas ou borradas?", "Are you really sure you want to enable this feature?": "Você deseja realmente ativar este recurso?", "Are you sure you want to create a new package bundle? ": "Tem certeza de que deseja criar uma nova coleção de pacotes? ", "Are you sure you want to delete all shortcuts?": "Você deseja realmente excluir todos os atalhos?", "Are you sure?": "Você tem certeza?", "Ascendant": "Ascendente", "Ask for administrator privileges once for each batch of operations": "Solicitar privilégios de administrador uma vez para cada lote de operações", "Ask for administrator rights when required": "Solicitar privilégios de administrador quando necessário", "Ask once or always for administrator rights, elevate installations by default": "Perguntar uma vez ou sempre por direitos de administrador, elevar instalações por padrão", "Ask only once for administrator privileges": "Perguntar uma única vez por direitos de administrador", "Ask only once for administrator privileges (not recommended)": "Perguntar apenas uma vez por privilégios de administrador (não recomendado)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Perguntar se deseja excluir atalhos criados na área de trabalho durante a instalação ou atualização.", "Attention required": "<PERSON><PERSON> ate<PERSON>", "Authenticate to the proxy with an user and a password": "Autenticar no proxy com um usuário e senha", "Author": "Autor", "Automatic desktop shortcut remover": "Removedor automático de atalhos na área de trabalho", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Salvar automaticamente uma lista de todos os seus pacotes instalados para restaurá-los facilmente.", "Automatically save a list of your installed packages on your computer.": "Salvar automaticamente no seu computador uma lista de seus pacotes instalados.", "Autostart WingetUI in the notifications area": "Iniciar o UniGetUI automaticamente na área de notificações", "Available Updates": "Atualizações Disponíveis", "Available updates: {0}": "Atualizações disponíveis: {0} ", "Available updates: {0}, not finished yet...": "Atualizações disponíveis: {0}, ainda não concluído...", "Backing up packages to GitHub Gist...": "Fazendo backup dos pacotes para o Github Gist...", "Backup": "Backup", "Backup Failed": "<PERSON><PERSON><PERSON> no backup", "Backup Successful": "Backup bem sucedido", "Backup and Restore": "Backup e Restauração", "Backup installed packages": "Fazer backup dos pacotes instalados", "Backup location": "Local do backup", "Become a contributor": "Torne-se um colaborador", "Become a translator": "Torne-se um tradutor", "Begin the process to select a cloud backup and review which packages to restore": "Inicie o processo para selecionar um backup na nuvem e revise quais pacotes restaurar", "Beta features and other options that shouldn't be touched": "Recursos beta e outras opções que não devem ser alteradas", "Both": "Ambos", "Bundle security report": "Relatório de segurança do pacote", "But here are other things you can do to learn about WingetUI even more:": "Mas aqui estão outras coisas que você pode fazer para aprender ainda mais sobre o UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Ao desativar um gerenciador de pacotes, você não poderá mais ver ou atualizar seus pacotes.", "Cache administrator rights and elevate installers by default": "Manter privilégios de administrador em cache e elevar instaladores por padrão", "Cache administrator rights, but elevate installers only when required": "Manter privilégios de administrador em cache, mas elevar instaladores apenas quando necessário", "Cache was reset successfully!": "Cache redefinido com sucesso!", "Can't {0} {1}": "Não foi possível {0} {1}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "Change backup output directory": "Alterar diretório de saída do backup", "Change default options": "Alterar opções padrão", "Change how UniGetUI checks and installs available updates for your packages": "Alterar como o UniGetUI verifica e instala atualizações disponíveis para seus pacotes", "Change how UniGetUI handles install, update and uninstall operations.": "Altere como o UniGetUI lida com as operações de instalação, atualização e desinstalação.", "Change how UniGetUI installs packages, and checks and installs available updates": "Altere como o UniGetUI instala pacotes, e verifica e instala atualizações disponíveis", "Change how operations request administrator rights": "<PERSON><PERSON><PERSON> como as operações solicitam direitos de administrador", "Change install location": "Alterar local de instalação", "Change this": "<PERSON><PERSON>r isto", "Change this and unlock": "Alterar isto e desblo<PERSON>ar", "Check for package updates periodically": "Verificar atualizações de pacotes periodicamente", "Check for updates": "Verificar atualizações", "Check for updates every:": "Verificar atualizações a cada:", "Check for updates periodically": "Verificar atualizações periodicamente", "Check for updates regularly, and ask me what to do when updates are found.": "Verificar atualizações regularmente e perguntar o que fazer quando elas forem encontradas.", "Check for updates regularly, and automatically install available ones.": "Verificar atualizações regularmente e instalar automaticamente as disponíveis.", "Check out my {0} and my {1}!": "Con<PERSON>ra meu {0} e minha {1}!", "Check out some WingetUI overviews": "Saiba mais sobre o UniGetUI", "Checking for other running instances...": "Verificando outras instâncias em execução.", "Checking for updates...": "Verificando atualizações...", "Checking found instace(s)...": "Verificando instância(s) encontrada(s).", "Choose how many operations shouls be performed in parallel": "Escolha quantas operações devem ser executadas em paralelo", "Clear cache": "Limpar cache", "Clear finished operations": "Limpar operações concluídas", "Clear selection": "<PERSON><PERSON>", "Clear successful operations": "Limpar operações bem sucedidas", "Clear successful operations from the operation list after a 5 second delay": "<PERSON><PERSON> as operaç<PERSON><PERSON> bem-sucedidas da lista após um atraso de 5 segundos", "Clear the local icon cache": "Limpar o cache de ícones local", "Clearing Scoop cache - WingetUI": "Limpando cache do Scoop - UniGetUI", "Clearing Scoop cache...": "Limpando cache do Scoop.", "Click here for more details": "Clique aqui para mais de<PERSON>hes", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Clique em Instalar para iniciar a instalação. Se você pular esta etapa, o UniGetUI pode não funcionar corretamente.", "Close": "<PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Fechar o UniGetUi para a área de notificação", "Close WingetUI to the notification area": "Fechar o UniGetUI para a área de notificação", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "O backup em nuvem usa um GitHub Gist privado para armazenar uma lista de pacotes instalados", "Cloud package backup": "Backup de pacote em nuvem", "Command-line Output": "<PERSON><PERSON><PERSON>o", "Command-line to run:": "Linha de comando para executar:", "Compare query against": "Comparar consulta com", "Compatible with authentication": "Compatível com autenticação", "Compatible with proxy": "Compatível com proxy", "Component Information": "Informações do Componente", "Concurrency and execution": "Concorrência e execução", "Connect the internet using a custom proxy": "Conectar na Internet usando proxy personalizado", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Contribua para o repositório de ícones e capturas de tela", "Contributors": "Colaboradores", "Copy": "Copiar", "Copy to clipboard": "Copiar para a área de transferência", "Could not add source": "Não foi possível adicionar a fonte", "Could not add source {source} to {manager}": "Não foi possível adicionar a fonte {source} ao {manager}", "Could not back up packages to GitHub Gist: ": "Não foi possível fazer backup dos pacotes para o GitHub Gist:", "Could not create bundle": "Não foi possível criar a coleção de pacotes", "Could not load announcements - ": "Não foi possível carregar os anúncios - ", "Could not load announcements - HTTP status code is $CODE": "Não foi possível carregar os anúncios - O código de status HTTP é $CODE", "Could not remove source": "Não foi possível remover a fonte", "Could not remove source {source} from {manager}": "Não foi possível remover a fonte {source} do {manager}", "Could not remove {source} from {manager}": "Não foi possível remover {source} de {manager}", "Create .ps1 script": "Criar script .ps1", "Credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Current Version": "<PERSON><PERSON><PERSON>", "Current status: Not logged in": "Status atual: Deslogado", "Current user": "<PERSON><PERSON><PERSON><PERSON> atual", "Custom arguments:": "Argumentos personalizados:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Argumentos de linha de comando personalizados podem alterar a maneira como os programas são instalados, atualizados ou desinstalados, de uma forma que o UniGetUI não pode controlar. O uso de linhas de comando personalizadas pode corromper pacotes. Prossiga com cautela.", "Custom command-line arguments:": "Argumentos personalizados da linha de comando:", "Custom install arguments:": "Argumentos de instalação personalizados:", "Custom uninstall arguments:": "Argumentos de desinstalação personalizados:", "Custom update arguments:": "Argumentos de atualização personalizados:", "Customize WingetUI - for hackers and advanced users only": "Personalizar o UniGetUI - apenas para hackers e usuários avançados", "DEBUG BUILD": "COMPILAÇÃO DE DEPURAÇÃO", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "AVISO LEGAL: NÃO NOS RESPONSABILIZAMOS PELOS PACOTES BAIXADOS. CERTIFIQUE-SE DE INSTALAR APENAS SOFTWARES CONFIÁVEL.", "Dark": "Escuro", "Decline": "Recusar", "Default": "Padrão", "Default installation options for {0} packages": "Opções de instalação padrão para {0} pacotes", "Default preferences - suitable for regular users": "Preferências padrão - adequadas para usuários comuns", "Default vcpkg triplet": "Triplet padrão do vcpkg", "Delete?": "Excluir?", "Dependencies:": "Dependências:", "Descendant": "Descendente", "Description:": "Descrição:", "Desktop shortcut created": "Atalho na área de trabalho criado", "Details of the report:": "Detalhes do relatório:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Desenvolver é difícil e este aplicativo é gratuito. Mas se você gostou do aplicativo, você sempre pode <b>me pagar um café</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Instalar diretamente ao clicar duas vezes em um item na guia \\\"{discoveryTab}\\\" (em vez de mostrar as informações do pacote)", "Disable new share API (port 7058)": "Desativar nova API de compartilhamento (porta 7058)", "Disable the 1-minute timeout for package-related operations": "Desativar o tempo limite de 1 minuto para operações relacionadas a pacotes", "Disclaimer": "Aviso Legal", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON>", "Discover packages": "<PERSON><PERSON><PERSON><PERSON> paco<PERSON>", "Distinguish between\nuppercase and lowercase": "Diferenciar maiús<PERSON>s de minúsculas", "Distinguish between uppercase and lowercase": "Diferenciar maiús<PERSON>s de minúsculas", "Do NOT check for updates": "NÃO verificar atualizações", "Do an interactive install for the selected packages": "Fazer uma instalação interativa para os pacotes selecionados", "Do an interactive uninstall for the selected packages": "Fazer uma desinstalação interativa para os pacotes selecionados", "Do an interactive update for the selected packages": "Fazer uma atualização interativa para os pacotes selecionados", "Do not automatically install updates when the battery saver is on": "Não instalar atualizações automaticamente com a economia de bateria ativada", "Do not automatically install updates when the network connection is metered": "Não instalar atualizações automaticamente quando a conexão de rede for limitada", "Do not download new app translations from GitHub automatically": "Não baixar automaticamente novas traduções do aplicativo do GitHub", "Do not ignore updates for this package anymore": "Não ignorar mais atualizações para este pacote", "Do not remove successful operations from the list automatically": "Não remover operações concluídas da lista automaticamente", "Do not show this dialog again for {0}": "Não mostrar esta caixa de diálogo novamente para {0}", "Do not update package indexes on launch": "Não atualizar índices de pacotes na inicialização", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Você permite que o UniGetUI colete e envie estatísticas de uso anônimas, com o único objetivo de entender e melhorar a experiência do usuário?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Você acha o UniGetUI útil? Se puder, apoie meu trabalho para que eu possa continuar tornando o UniGetUI a interface definitiva para gerenciamento de pacotes.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Você acha o UniGetUI útil? Gostaria de apoiar o desenvolvedor? Se sim, você pode {0}, ajuda muito!", "Do you really want to reset this list? This action cannot be reverted.": "Você deseja realmente redefinir esta lista? Esta ação não pode ser desfeita.", "Do you really want to uninstall the following {0} packages?": "Você tem certeza de que deseja desinstalar os {0} paco<PERSON> se<PERSON>?", "Do you really want to uninstall {0} packages?": "Você tem certeza de que deseja desinstalar {0} paco<PERSON>?", "Do you really want to uninstall {0}?": "Você tem certeza de que deseja desinstalar {0}?", "Do you want to restart your computer now?": "Você deseja reiniciar o computador agora?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Quer traduzir o UniGetUI para o seu idioma? Veja como contribuir <a style=\"color:{0}\" href=\"{1}\"a>AQUI!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Não está em condições de fazer uma doação? Sem problemas, você sempre pode compartilhar o UniGetUI com seus amigos. Ajude divulgando o UniGetUI.", "Donate": "<PERSON><PERSON>", "Done!": "Concluído!", "Download failed": "Falha no download", "Download installer": "Baixar instalador", "Download operations are not affected by this setting": "Operações de download não são afetadas por esta configuração", "Download selected installers": "Baixar instaladores selecionados", "Download succeeded": "Download concluído", "Download updated language files from GitHub automatically": "Baixar arquivos de idioma atualizados do GitHub automaticamente", "Downloading": "<PERSON><PERSON><PERSON>", "Downloading backup...": "<PERSON><PERSON><PERSON> o backup...", "Downloading installer for {package}": "Baixando o instalador para {package}", "Downloading package metadata...": "Baixando metadados do pacote.", "Enable Scoop cleanup on launch": "Ativar limpeza do Scoop na inicialização", "Enable WingetUI notifications": "Ativar notificações do UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Ativar solucionador de problemas do WinGet aprimorado [experimental]", "Enable and disable package managers, change default install options, etc.": "Habilite e desabilite gerenciadores de pacotes, altere opções de instalação padrão, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Ativar otimizações de uso de CPU em segundo plano (veja Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Habilitar API em segundo plano (Widgets e Compartilhamento do UniGetUI, porta 7058)", "Enable it to install packages from {pm}.": "Habilitar a instalação de pacotes do {pm}.", "Enable the automatic WinGet troubleshooter": "Habilitar a solução de problemas automática do WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Habilitar o novo elevador de privilégios personalizado do UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Habilitar o novo manipulador de entrada de processo (fechador automatizado StdIn)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Ative as configurações abaixo SE E SOMENTE SE você entender completamente o que elas fazem e as implicações e perigos que podem envolver.", "Enable {pm}": "Habilitar {pm}", "Enter proxy URL here": "Insira o URL do proxy aqui", "Entries that show in RED will be IMPORTED.": "As entradas que aparecerem em VERMELHO serão IMPORTADAS.", "Entries that show in YELLOW will be IGNORED.": "As entradas que aparecerem em AMARELO serão IGNORADAS.", "Error": "Erro", "Everything is up to date": "Tudo está atualizado", "Exact match": "Correspondência exata", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Atalhos existentes na sua área de trabalho serão verificados, e você precisará escolher quais manter e quais remover.", "Expand version": "Expandir", "Experimental settings and developer options": "Configurações experimentais e opções de desenvolvedor", "Export": "Exportar", "Export log as a file": "Exportar log como um arquivo", "Export packages": "Exportar pacotes", "Export selected packages to a file": "Exportar pacotes selecionados para um arquivo", "Export settings to a local file": "Exportar configurações para um arquivo local", "Export to a file": "Exportar para um arquivo", "Failed": "Fal<PERSON>", "Fetching available backups...": "Buscando backups disponíveis...", "Fetching latest announcements, please wait...": "Buscando os últimos <PERSON>, aguarde...", "Filters": "<PERSON><PERSON><PERSON>", "Finish": "Concluir", "Follow system color scheme": "Seguir o esquema de cores do sistema", "Follow the default options when installing, upgrading or uninstalling this package": "Se<PERSON>ir opções padrão ao instalar, atualizar ou desinstalar este pacote", "For security reasons, changing the executable file is disabled by default": "Por razões de segurança, a alteração do arquivo executável é desabilitada por padrão", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Por motivos de segurança, os argumentos de linha de comando personalizados são desabilitados por padrão. Acesse as configurações de segurança do UniGetUI para alterar isso.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Por motivos de segurança, os scripts de pré e pós-operação são desabilitados por padrão. Acesse as configurações de segurança do UniGetUI para alterar isso.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Forçar versão ARM compilada do winget (SOMENTE PARA SISTEMAS ARM64)", "Formerly known as WingetUI": "Anteriormente conhecido como WingetUI", "Found": "Encontrado", "Found packages: ": "Pacotes encontrados:", "Found packages: {0}": "Pacotes encontrados: {0}", "Found packages: {0}, not finished yet...": "Pacotes encontrados: {0}, ainda não concluído.", "General preferences": "Preferências gerais", "GitHub profile": "Perfil do GitHub", "Global": "Global", "Go to UniGetUI security settings": "Acessar configurações de segurança do UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Uma vasta coleção de utilitários úteis, ainda que pouco conhecidos, e pacotes variados.<br>Contém: <b><PERSON><PERSON>it<PERSON><PERSON><PERSON>, Programas para linha de comando, Software geral (é necessário habilitar o bucket extras)</b>", "Great! You are on the latest version.": "Ótimo! Você está na versão mais recente.", "Grid": "Grade", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Ajuda e documentação", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Aqui você pode alterar o comportamento do UniGetUI em relação aos atalhos abaixo. Marcar um atalho fará com que o UniGetUI o exclua caso ele seja criado em uma futura atualização. Desmarcar manterá o atalho intacto", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON>, meu nome é Martí e sou o <i>desenvolvedor</i> do UniGetUI. O UniGetUI foi feito inteiramente no meu tempo livre!", "Hide details": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "Homepage": "Página Inicial", "Hooray! No updates were found.": "Uhu! Nenhuma atualização foi encontrada.", "How should installations that require administrator privileges be treated?": "Como as instalações que exigem privilégios de administrador devem ser tratadas?", "How to add packages to a bundle": "Como adicionar pacotes no conjunto", "I understand": "<PERSON><PERSON><PERSON>", "Icons": "Ícones", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Se você tiver o backup em nuvem habilitado, ele será salvo como um GitHub Gist nesta conta", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignorar comandos personalizados de pré-instalação e pós-instalação ao importar pacotes", "Ignore future updates for this package": "Ignorar futuras atualizações para este pacote", "Ignore packages from {pm} when showing a notification about updates": "Ignorar pacotes do {pm} ao exibir notificação sobre atualização", "Ignore selected packages": "Ignorar pacotes selecionados", "Ignore special characters": "Ignorar caracteres especiais", "Ignore updates for the selected packages": "Ignorar atualizações para os pacotes selecionados", "Ignore updates for this package": "Ignorar atualizações para este pacote", "Ignored updates": "Atualizações ignoradas", "Ignored version": "<PERSON><PERSON><PERSON>", "Import": "Importar", "Import packages": "Importar pacotes", "Import packages from a file": "Importar pacotes de um arquivo", "Import settings from a local file": "Importar configurações de um arquivo local", "In order to add packages to a bundle, you will need to: ": "Para poder adicionar pacotes no conjunto, você precisará:", "Initializing WingetUI...": "Inicializando o UniGetUI...", "Install": "Instalar", "Install Scoop": "<PERSON><PERSON><PERSON>", "Install and more": "Instalação e mais", "Install and update preferences": "Preferências de instalação e atualização", "Install as administrator": "Instalar como administrador", "Install available updates automatically": "Instalar atualizações disponíveis automaticamente", "Install location can't be changed for {0} packages": "O local de instalação não pode ser alterado para {0} pacotes", "Install location:": "Local de instalação:", "Install options": "Opções de instalação", "Install packages from a file": "Instalar pacotes de um arquivo", "Install prerelease versions of UniGetUI": "Instalar versões de pré-lançamento do UniGetUI", "Install script": "Instalar script", "Install selected packages": "Instalar pacotes selecionados", "Install selected packages with administrator privileges": "Instalar pacotes selecionados com privilégios de administrador", "Install selection": "Instalar se<PERSON>ção", "Install the latest prerelease version": "Instalar a versão de pré-lançamento mais recente", "Install updates automatically": "Instalar atualizações automaticamente", "Install {0}": "Instalar {0}", "Installation canceled by the user!": "Instalação cancelada pelo usuário!", "Installation failed": "A instalação falhou!", "Installation options": "Opções de instalação", "Installation scope:": "Escopo da instalação:", "Installation succeeded": "Instalação concluída com sucesso!", "Installed Packages": "Pacotes Instalados", "Installed Version": "Versão Instalada", "Installed packages": "Pacotes instalados", "Installer SHA256": "SHA256 do Instalador", "Installer SHA512": "SHA512 do instalador", "Installer Type": "Tipo de Instalador", "Installer URL": "URL do Instalador ", "Installer not available": "Instalador não disponível", "Instance {0} responded, quitting...": "Instância {0} respondeu, saindo...", "Instant search": "Pesquisa <PERSON>ânea", "Integrity checks can be disabled from the Experimental Settings": "As verificações de integridade podem ser desabilitadas nas Configurações Experimentais", "Integrity checks skipped": "Verificações de integridade ignoradas", "Integrity checks will not be performed during this operation": "As verificações de integridade não serão executadas durante esta operação", "Interactive installation": "Instalação interativa", "Interactive operation": "Operação interativa", "Interactive uninstall": "Desinstalação interativa", "Interactive update": "Atualização interativa", "Internet connection settings": "Configurações de conexão com a Internet", "Is this package missing the icon?": "Este pacote está sem ícone?", "Is your language missing or incomplete?": "Seu idioma está faltando ou incompleto?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Não há garantia de que as credenciais fornecidas serão armazenadas com segurança, portanto, você também não pode usar as credenciais da sua conta bancária.", "It is recommended to restart UniGetUI after WinGet has been repaired": "É recomendado reiniciar o UniGetUI após o WinGet ser reparado", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "É altamente recomendável reinstalar o UniGetUI para resolver a situação.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Parece que o WinGet não está funcionando corretamente. Deseja tentar repará-lo?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Parece que você executou o UniGetUI como administrador, o que não é recomendado. Você ainda pode usar o programa, mas é altamente não executar o UniGetUI com privilégios de administrador. Clique em \\\"{showDetails}\\\" para saber mais.", "Language": "Idioma", "Language, theme and other miscellaneous preferences": "Idioma, tema e outras preferências diversas", "Last updated:": "Última atualização:", "Latest": "<PERSON><PERSON>e", "Latest Version": "<PERSON><PERSON><PERSON> mais recente", "Latest Version:": "Vers<PERSON> mais recente:", "Latest details...": "Últimos de<PERSON>...", "Launching subprocess...": "Iniciando subprocesso...", "Leave empty for default": "Deixe em branco para padrão", "License": "Licença", "Licenses": "Licenças", "Light": "<PERSON><PERSON><PERSON>", "List": "Lista", "Live command-line output": "<PERSON><PERSON><PERSON> da linha de comando em tempo real", "Live output": "Saída em tempo real", "Loading UI components...": "Carregando componentes da interface...", "Loading WingetUI...": "Carregando UniGetUI...", "Loading packages": "<PERSON><PERSON><PERSON> paco<PERSON>", "Loading packages, please wait...": "<PERSON><PERSON><PERSON> pacotes, por favor aguarde...", "Loading...": "Carregando...", "Local": "Local", "Local PC": "PC Local", "Local backup advanced options": "Opções avançadas de backup local", "Local machine": "Máquina Local", "Local package backup": "Backup de pacote local", "Locating {pm}...": "Localizando {pm}...", "Log in": "<PERSON><PERSON>", "Log in failed: ": "Falha no login:", "Log in to enable cloud backup": "Faça login para ativar o backup na nuvem", "Log in with GitHub": "Fazer login com o GitHub", "Log in with GitHub to enable cloud package backup.": "Faça login com o GitHub para ativar o backup de pacotes na nuvem.", "Log level:": "Nível de log:", "Log out": "<PERSON><PERSON>", "Log out failed: ": "Falha ao sair:", "Log out from GitHub": "Sair do GitHub", "Looking for packages...": "<PERSON><PERSON><PERSON><PERSON> pacotes...", "Machine | Global": "Máquina | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Argumentos de linha de comando malformados podem corromper pacotes ou até mesmo permitir que um agente malicioso obtenha execução privilegiada. Portanto, a importação de argumentos de linha de comando personalizados está desabilitada por padrão.", "Manage": "Gerenciar", "Manage UniGetUI settings": "Gerenciar configurações do UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Gerenciar o comportamento de inicialização automática do UniGetUI no aplicativo Configurações", "Manage ignored packages": "Gerenciar pacotes ignorados", "Manage ignored updates": "Gerenciar atualizações ignoradas", "Manage shortcuts": "Gerenciar atalhos", "Manage telemetry settings": "Gerenciar configurações de telemetria", "Manage {0} sources": "Gerenciar {0} fontes", "Manifest": "Manifesto", "Manifests": "Manifestos", "Manual scan": "Verificação manual", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Gerenciador de pacotes oficial da Microsoft. Repleto de pacotes conhecidos e verificados<br>Contém: <b>Software Geral, aplicativos da Microsoft Store</b>", "Missing dependency": "Dependência ausente", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON>", "More details about the shared data and how it will be processed": "Mais detalhes sobre os dados compartilhados e como eles serão processados", "More info": "Mais informaç<PERSON>", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "OBSERVAÇÃO: esta solução de problemas pode ser desabilitada nas Configurações do UniGetUI, na seção WinGet", "Name": "Nome", "New": "Novo", "New Version": "Nova Versão", "New bundle": "Nova coleção de pacotes", "New version": "Nova versão", "Nice! Backups will be uploaded to a private gist on your account": "Ótimo! Os backups serão enviados para um gist privado na sua conta.", "No": "Não", "No applicable installer was found for the package {0}": "Nenhum instalador encontrado para o pacote {0}", "No dependencies specified": "Nenhuma dependência especificada", "No new shortcuts were found during the scan.": "Nenhum novo atalho foi encontrado durante a verificação.", "No packages found": "Nenhum pacote encontrado", "No packages found matching the input criteria": "Nenhum pacote encontrado que corresponda aos critérios informados", "No packages have been added yet": "Nenhum pacote foi adicionado ainda", "No packages selected": "Nenhum pacote sele<PERSON>ado", "No packages were found": "Nenhum pacote foi encontrado", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Nenhuma informação pessoal é coletada e enviada. Os dados coletados não anônimos, assim não podem ser rastreados.", "No results were found matching the input criteria": "Nenhum resultado foi encontrado que corresponda aos critérios informados", "No sources found": "Nenhuma fonte encontrada", "No sources were found": "Nenhuma fonte foi encontrada", "No updates are available": "Nenhuma atualização está disponível", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Gerenciador de pacotes do Node JS. Repleto de bibliotecas e outros utilitários que orbitam o mundo JavaScript<br>Contém: <b>Bibliotecas JavaScript do Node e outros utilitários relacionados</b>", "Not available": "Não disponível", "Not finding the file you are looking for? Make sure it has been added to path.": "Não encontrou o arquivo que procurava? Certifique-se de que ele foi adicionado ao caminho.", "Not found": "Não encontrado", "Not right now": "<PERSON><PERSON><PERSON> n<PERSON>", "Notes:": "Observações:", "Notification preferences": "Preferências de notificação", "Notification tray options": "Opções da bandeja de notificação", "Notification types": "Tipos de notificação", "NuPkg (zipped manifest)": "NuPkg (manifesto compactado)", "OK": "OK", "Ok": "Ok", "Open": "Abrir", "Open GitHub": "<PERSON><PERSON><PERSON>", "Open UniGetUI": "Abrir UniGetUI", "Open UniGetUI security settings": "<PERSON><PERSON>r configurações de segurança do UniGetUI", "Open WingetUI": "Abrir UniGetUI", "Open backup location": "Abrir local do backup", "Open existing bundle": "Abrir coleção de pacotes existente", "Open install location": "Abrir local de instalação", "Open the welcome wizard": "<PERSON><PERSON>r o assistente de boas-vindas", "Operation canceled by user": "Operação cancelada pelo usuário", "Operation cancelled": "Operação cancelada", "Operation history": "Histórico de operações", "Operation in progress": "Operação em andamento", "Operation on queue (position {0})...": "Operação na fila (posição {0})...", "Operation profile:": "Perfil de operação:", "Options saved": "Opções salvas!", "Order by:": "Classificar por:", "Other": "Outros", "Other settings": "Outras configurações", "Package": "<PERSON><PERSON>", "Package Bundles": "Coleção de Pacotes", "Package ID": "ID do Pacote", "Package Manager": "Gerenciador de pacotes", "Package Manager logs": "Logs do Gerenciador de Pacotes", "Package Managers": "Gerenciadores de pacotes", "Package Name": "Nome do Pacote", "Package backup": "Backup do pacote", "Package backup settings": "Configurações de backup do pacote", "Package bundle": "Coleção de pacotes", "Package details": "Detalhes do pacote", "Package lists": "Listas de pacotes", "Package management made easy": "Gestão de pacotes facilitada", "Package manager": "Gerenciador de pacotes", "Package manager preferences": "Preferências do gerenciador de pacotes", "Package managers": "Gerenciadores de pacotes", "Package not found": "Pacote não encontrado", "Package operation preferences": "Preferências das operações de pacotes", "Package update preferences": "Preferências da atualização de pacotes", "Package {name} from {manager}": "<PERSON><PERSON> {name} de {manager}", "Package's default": "Padrão do pacote", "Packages": "<PERSON><PERSON>", "Packages found: {0}": "Pacotes encontrados: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON>", "Password": "<PERSON><PERSON>", "Paste a valid URL to the database": "Cole uma URL válida no banco de dados", "Pause updates for": "Pausar atualizações por", "Perform a backup now": "Fazer backup agora", "Perform a cloud backup now": "Executar backup na nuvem agora", "Perform a local backup now": "Executar backup local agora", "Perform integrity checks at startup": "Executar verificações de integridade na inicialização", "Performing backup, please wait...": "Fazendo backup, aguarde...", "Periodically perform a backup of the installed packages": "Fazer backup dos pacotes instalados periodicamente", "Periodically perform a cloud backup of the installed packages": "Executar periodicamente um backup na nuvem dos pacotes instalados", "Periodically perform a local backup of the installed packages": "Executar periodicamente um backup local dos pacotes instalados", "Please check the installation options for this package and try again": "Verifique as opções de instalação deste pacote e tente novamente", "Please click on \"Continue\" to continue": "Clique em \"Continuar\" para prosseguir", "Please enter at least 3 characters": "Insira pelo menos 3 caracteres", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Observe que certos pacotes podem não ser instaláveis devido aos gerenciadores de pacotes ativados nesta máquina.", "Please note that not all package managers may fully support this feature": "Observe que nem todos os gerenciadores de pacotes podem suportar totalmente este recurso", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Observe que pacotes de certas fontes podem não ser exportáveis. Eles foram desativados e não serão exportados.", "Please run UniGetUI as a regular user and try again.": "Execute o UniGetUI como um usuário comum e tente novamente.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Consulte a saída da linha de comando ou o histórico de operações para mais informações sobre o problema.", "Please select how you want to configure WingetUI": "Selecione como você deseja configurar o UniGetUI", "Please try again later": "Por favor, tente novamente mais tarde", "Please type at least two characters": "Insira pelo menos dois caracteres", "Please wait": "Por favor, aguarde", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Aguarde enquanto {0} está sendo instalado. Uma janela preta poderá aparecer. Aguarde até que ela seja fechada.", "Please wait...": "Por favor, aguarde.", "Portable": "<PERSON><PERSON><PERSON>", "Portable mode": "<PERSON><PERSON>", "Post-install command:": "Comando de pós-instalação:", "Post-uninstall command:": "Comando de pós-desinstalação:", "Post-update command:": "Comando de pós-atualização:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Gerenciador de pacotes do PowerShell. Encontre bibliotecas e scripts para expandir as capacidades do PowerShell<br>Contém: <b><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Comandos de pré e pós-instalação podem causar danos graves ao seu dispositivo, se projetados para isso. Pode ser muito perigoso importar os comandos de um pacote, a menos que você confie na fonte desse pacote.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Os comandos de pré e pós-instalação serão executados antes e depois da instalação, atualização ou desinstalação de um pacote. Esteja ciente de que eles podem causar problemas, a menos que sejam usados com cuidado.", "Pre-install command:": "Comando de pré-instalação:", "Pre-uninstall command:": "Comando de pré-desinstalação:", "Pre-update command:": "Comando de pré-atualização:", "PreRelease": "Pré-lançamento", "Preparing packages, please wait...": "<PERSON>parando pacotes, por favor aguarde.", "Proceed at your own risk.": "Prossiga por sua conta e risco.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Proibir qualquer tipo de elevação via UniGetUI Elevator ou GSudo", "Proxy URL": "URL do proxy", "Proxy compatibility table": "Tabela de compatibilidade do proxy", "Proxy settings": "Configurações do proxy", "Proxy settings, etc.": "Configurações do proxy, etc.", "Publication date:": "Data de publicação:", "Publisher": "<PERSON><PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Gerenciador de bibliotecas do Python. Repleto de bibliotecas Python e outros utilitários relacionados ao Python<br>Contém: <b>Bibliotecas Python e utilitários relacionados</b>", "Quit": "<PERSON><PERSON>", "Quit WingetUI": "Sair do UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Reduza os prompts do UAC, eleve as instalações por padrão, desbloqueie certos recursos perigosos, etc.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Consulte os registros do UniGetUI para obter mais detalhes sobre os arquivos afetados", "Reinstall": "Reins<PERSON>ar", "Reinstall package": "Reinstalar pacote", "Related settings": "Configurações relacionadas", "Release notes": "Notas de lançamento", "Release notes URL": "URL das notas de lançamento", "Release notes URL:": "URL das notas de lançamento:", "Release notes:": "Notas de lançamento:", "Reload": "<PERSON><PERSON><PERSON><PERSON>", "Reload log": "Recarregar log", "Removal failed": "Remoção falhou", "Removal succeeded": "Remoção concluída com sucesso", "Remove from list": "Remover da lista", "Remove permanent data": "Remover dados permanentes", "Remove selection from bundle": "Remover seleção da coleção", "Remove successful installs/uninstalls/updates from the installation list": "Remover instalações/desinstalações/atualizações concluídas da lista de instalação", "Removing source {source}": "Removendo fonte {source}", "Removing source {source} from {manager}": "Removendo fonte {source} do {manager}", "Repair UniGetUI": "Reparar o UniGetUI", "Repair WinGet": "<PERSON><PERSON><PERSON>", "Report an issue or submit a feature request": "Reportar um problema ou solicitar um recurso", "Repository": "Repositório", "Reset": "Redefinir", "Reset Scoop's global app cache": "Redefinir cache global de aplicativos do Scoop", "Reset UniGetUI": "Redefinir UniGetUI", "Reset WinGet": "Redefinir WinGet", "Reset Winget sources (might help if no packages are listed)": "Redefinir fontes do Winget (pode ajudar se nenhum pacote estiver listado)", "Reset WingetUI": "Redefinir UniGetUI", "Reset WingetUI and its preferences": "Redefinir o UniGetUI e as suas preferências", "Reset WingetUI icon and screenshot cache": "Redefinir o cache de ícones e de capturas de tela do WingetU", "Reset list": "Redefinir lista", "Resetting Winget sources - WingetUI": "Redefinindo fontes do Winget - UniGetUI", "Restart": "Reiniciar", "Restart UniGetUI": "Reiniciar UniGetUI", "Restart WingetUI": "Reiniciar UniGetUI", "Restart WingetUI to fully apply changes": "Reinicie o UniGetUI para aplicar todas as alterações", "Restart later": "Reiniciar mais tarde", "Restart now": "Reiniciar agora", "Restart required": "Reinicialização necessária", "Restart your PC to finish installation": "Reinicie seu PC para concluir a instalação", "Restart your computer to finish the installation": "Reinicie o computador para concluir a instalação", "Restore a backup from the cloud": "Restaurar backup da nuvem", "Restrictions on package managers": "Restrições em gerenciadores de pacotes", "Restrictions on package operations": "Restrições nas operações de pacotes", "Restrictions when importing package bundles": "Restrições ao importar pacotes", "Retry": "Tentar novamente", "Retry as administrator": "Repetir como administrador", "Retry failed operations": "Repetir operações com falha", "Retry interactively": "Repetir interativamente", "Retry skipping integrity checks": "Repetir verificações de integridade ignoradas", "Retrying, please wait...": "<PERSON><PERSON>do novamente, por favor aguarde.", "Return to top": "Voltar ao topo", "Run": "Executar", "Run as admin": "Executar como administrador", "Run cleanup and clear cache": "Executar limpeza e limpar cache", "Run last": "Executar último", "Run next": "Executar próximo", "Run now": "Executar agora", "Running the installer...": "Executando o instalador...", "Running the uninstaller...": "Executando o desinstalador...", "Running the updater...": "Executando o atualizador...", "Save": "<PERSON><PERSON>", "Save File": "<PERSON><PERSON>qui<PERSON>", "Save and close": "<PERSON>var e fechar", "Save as": "<PERSON><PERSON> como", "Save bundle as": "<PERSON>var coleção como...", "Save now": "<PERSON><PERSON> agora", "Saving packages, please wait...": "<PERSON><PERSON><PERSON> pacotes, por favor aguarde.", "Scoop Installer - WingetUI": "Instalador do Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Desinstalador do Scoop - UniGetUI", "Scoop package": "Pacote do Scoop", "Search": "<PERSON><PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Pesquisar por softwares de desktop, me avisar quando houver atualizações disponíveis e não fazer coisas nerds. Não quero que o UniGetUI seja complicado, só quero uma <b>loja de softwares</b> simples", "Search for packages": "<PERSON><PERSON>rar por pacotes", "Search for packages to start": "Procure por pacotes para começar", "Search mode": "<PERSON>do de pesquisa", "Search on available updates": "Pesquisar por atualizações disponíveis", "Search on your software": "Buscar programa", "Searching for installed packages...": "Pesquisando por pacotes instalados...", "Searching for packages...": "Pesquisando por pacotes...", "Searching for updates...": "Pesquisando por atualizações...", "Select": "Selecionar", "Select \"{item}\" to add your custom bucket": "Selecione \"{item}\" para adicionar seu bucket personalizado", "Select a folder": "Selecionar uma pasta", "Select all": "Selecionar tudo", "Select all packages": "Selecionar todos os pacotes", "Select backup": "Selecionar backup", "Select only <b>if you know what you are doing</b>.": "Se<PERSON><PERSON>e somente <b>se você souber o que está fazendo</b>.", "Select package file": "Selecionar arquivo de pacote", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Selecione o backup que deseja abrir. Posteriormente, você poderá verificar quais pacotes/programas deseja restaurar.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Selecione os processos que devem ser fechados antes que este pacote seja instalado, atualizado ou desinstalado.", "Select the source you want to add:": "Selecione a fonte que você deseja adicionar:", "Select upgradable packages by default": "Selecionar pacotes que podem ser atualizados por padrão", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Sele<PERSON>e quais <b>gerenciadores de pacotes</b> usar ({0}), configure como os pacotes são instalados, gerencie como os privilégios de administrador são manipulados, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake enviado. Aguardando resposta do listener da instância... ({0}%)", "Set a custom backup file name": "Defina um nome para seu arquivo de backup", "Set custom backup file name": "Defina o nome para seu arquivo de backup", "Settings": "Configurações", "Share": "Compartilhar", "Share WingetUI": "Compartilhar UniGetUI", "Share anonymous usage data": "Compartilhar dados de uso anônimos", "Share this package": "Compartilhar este pacote", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Caso você modifique as configurações de segurança, será necessário abrir o pacote novamente para que as alterações entrem em vigor.", "Show UniGetUI on the system tray": "Mostrar UniGetUI na bandeja do sistema", "Show UniGetUI's version and build number on the titlebar.": "Mostrar versão do UniGetUI na barra de título", "Show WingetUI": "Mostrar UniGetUI", "Show a notification when an installation fails": "Mostrar uma notificação quando uma instalação falhar", "Show a notification when an installation finishes successfully": "Mostrar uma notificação quando uma instalação for concluída com sucesso", "Show a notification when an operation fails": "Exibir uma notificação quando uma operação falhar", "Show a notification when an operation finishes successfully": "Exibir uma notificação quando uma operação for concluída com sucesso", "Show a notification when there are available updates": "Mostrar uma notificação quando houver atualizações disponíveis", "Show a silent notification when an operation is running": "Exibir uma notificação silenciosa enquanto uma operação está em andamento", "Show details": "<PERSON><PERSON> de<PERSON>", "Show in explorer": "Exibir no <PERSON>", "Show info about the package on the Updates tab": "Mostrar informações sobre o pacote na guia Atualizações", "Show missing translation strings": "Mostrar strings de tradução ausentes", "Show notifications on different events": "Exibir notificações em diferentes eventos", "Show package details": "Mostrar detalhes do pacote", "Show package icons on package lists": "<PERSON><PERSON><PERSON> dos pacotes nas listas de pacotes", "Show similar packages": "Mostrar pacotes semelhantes", "Show the live output": "Mostrar a saída em tempo real", "Size": "<PERSON><PERSON><PERSON>", "Skip": "<PERSON><PERSON>", "Skip hash check": "Ignorar verificação de hash", "Skip hash checks": "Ignorar verificações de hash", "Skip integrity checks": "Ignorar verificações de integridade", "Skip minor updates for this package": "Ignoras atualizações menores deste pacote", "Skip the hash check when installing the selected packages": "Ignorar a verificação de hash ao instalar os pacotes selecionados", "Skip the hash check when updating the selected packages": "Ignorar a verificação de hash ao atualizar os pacotes selecionados", "Skip this version": "Pular esta versão", "Software Updates": "Atualizações de Software", "Something went wrong": "<PERSON>go deu errado", "Something went wrong while launching the updater.": "Algo deu errado ao iniciar o atualizador.", "Source": "Origem", "Source URL:": "URL da fonte:", "Source added successfully": "Fonte adicionada com sucesso", "Source addition failed": "Falha na adição da fonte", "Source name:": "<PERSON>me da fonte:", "Source removal failed": "Falha na remoção da fonte", "Source removed successfully": "Fonte removida com sucesso", "Source:": "Origem:", "Sources": "<PERSON><PERSON><PERSON>", "Start": "Iniciar", "Starting daemons...": "Iniciando daemons...", "Starting operation...": "Iniciando operação...", "Startup options": "Opções de inicialização", "Status": "Status", "Stuck here? Skip initialization": "Preso aqui? Pular inicialização", "Success!": "Sucesso!", "Suport the developer": "<PERSON><PERSON><PERSON> o desenvolvedor", "Support me": "A<PERSON><PERSON>-me", "Support the developer": "<PERSON><PERSON><PERSON> o desenvolvedor", "Systems are now ready to go!": "Os sistemas estão prontos para começar!", "Telemetry": "Telemetria", "Text": "Texto", "Text file": "Arquivo de texto", "Thank you ❤": "<PERSON><PERSON><PERSON> ❤", "Thank you 😉": "<PERSON><PERSON><PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "O gerenciador de pacotes Rust.<br>Contém: <b>Bibliotecas Rust e programas escritos em Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "O backup NÃO incluirá nenhum arquivo binário nem dados salvos de nenhum programa.", "The backup will be performed after login.": "O backup será realizado após o login.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "O backup incluirá a lista completa dos pacotes instalados e suas opções de instalação. Atualizações ignoradas e versões puladas também serão salvas.", "The bundle was created successfully on {0}": "O pacote foi criado com sucesso em {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "A coleção de pacotes que você está tentando carregar parece ser inválida. Verifique o arquivo e tente novamente.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "A soma de verificação do instalador não coincide com o valor esperado e a autenticidade do instalador não pode ser verificada. Se você confia no editor, {0} o pacote novamente ignorando a verificação de hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "O gerenciador de pacotes clássico para Windows. Você encontrará tudo lá. <br>Contém: <b>Software Geral</b>", "The cloud backup completed successfully.": "O backup na nuvem foi concluído com sucesso.", "The cloud backup has been loaded successfully.": "O backup na nuvem foi carregado com sucesso.", "The current bundle has no packages. Add some packages to get started": "A coleção de pacotes atual não contém pacotes. Adicione alguns para começar!", "The executable file for {0} was not found": "O arquivo executável do {0} não foi encontrado", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "As seguintes opções serão aplicadas por padrão sempre que um pacote {0} for instalado, atualizado ou desinstalado.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "<PERSON>s seguintes pacotes serão exportados para um arquivo JSON. Nenhum dado do usuário ou binários serão salvos.", "The following packages are going to be installed on your system.": "<PERSON><PERSON> se<PERSON><PERSON> pacotes serão instalados em seu sistema.", "The following settings may pose a security risk, hence they are disabled by default.": "As seguintes configurações podem representar um risco à segurança, por isso estão desabilitadas por padrão.", "The following settings will be applied each time this package is installed, updated or removed.": "As seguintes configurações serão aplicadas sempre que este pacote for instalado, atualizado ou removido.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "As seguintes configurações serão aplicadas sempre que este pacote for instalado, atualizado ou removido. Elas serão salvas automaticamente.", "The icons and screenshots are maintained by users like you!": "Os ícones e capturas de tela são mantidos por usuários como você!", "The installation script saved to {0}": "O script de instalação foi salvo em {0}", "The installer authenticity could not be verified.": "A autenticidade do instalador não pôde ser verificada.", "The installer has an invalid checksum": "O instalador possui uma soma de verificação inválida", "The installer hash does not match the expected value.": "O hash do instalador não corresponde ao valor esperado.", "The local icon cache currently takes {0} MB": "O cache de ícones local atualmente ocupa {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "O principal objetivo deste projeto é criar uma interface de usuário intuitiva para gerenciar os gerenciadores de pacotes de linha de comando mais comuns para Windows, como Winget e Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "O pacote \"{0}\" não foi encontrado no gerenciador de pacotes \"{1}\"", "The package bundle could not be created due to an error.": "Não foi possível criar a coleção de pacotes devido a um erro.", "The package bundle is not valid": "A coleção de pacotes não é válida", "The package manager \"{0}\" is disabled": "O gerenciador de pacotes \"{0}\" está desativado", "The package manager \"{0}\" was not found": "O gerenciador de pacotes \"{0}\" não foi encontrado", "The package {0} from {1} was not found.": "O pacote {0} de {1} não foi encontrado.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "<PERSON>s pacotes listados aqui não serão levados em conta ao verificar atualizações. Clique duas vezes neles ou clique no botão à direita para parar de ignorar suas atualizações.", "The selected packages have been blacklisted": "Os pacotes selecionados foram colocados na lista negra", "The settings will list, in their descriptions, the potential security issues they may have.": "As configurações listarão, em suas descrições, os potenciais problemas de segurança que podem ter.", "The size of the backup is estimated to be less than 1MB.": "O tamanho do backup é estimado em menos de 1 MB.", "The source {source} was added to {manager} successfully": "A fonte {source} foi adicionada ao {manager} com sucesso", "The source {source} was removed from {manager} successfully": "A fonte {source} foi removida do {manager} com sucesso", "The system tray icon must be enabled in order for notifications to work": "O ícone da área de notificação precisa estar ativado para que as notificações funcionem", "The update process has been aborted.": "O processo de atualização foi interrompido.", "The update process will start after closing UniGetUI": "O processo de atualização será iniciado após encerrar o UniGetUI", "The update will be installed upon closing WingetUI": "A atualização será instalada ao fechar o UniGetUI", "The update will not continue.": "A atualização não continuará.", "The user has canceled {0}, that was a requirement for {1} to be run": "O usuário cancelou {0}, um requerimento para {1} ser executado", "There are no new UniGetUI versions to be installed": "Não há novas versões do UniGetUI para serem instaladas", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Há operações em andamento. Sair do UniGetUI pode fazer com que elas falhem. Deseja continuar?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Existem ótimos vídeos no YouTube que mostram o UniGetUI e seus recursos. Você pode aprender truques e dicas úteis!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Existem dois motivos principais para não executar o UniGetUI como administrador:\\n O primeiro é que o gerenciador de pacotes Scoop pode causar problemas com alguns comandos quando executado com direitos de administrador.\\n O segundo é que executar o UniGetUI como administrador significa que qualquer pacote que você baixar será executado como administrador (e isso não é seguro).\\n Lembre-se de que, se precisar instalar um pacote específico como administrador, você sempre pode clicar com o botão direito do mouse no item -> Instalar/Atualizar/Desinstalar como administrador.", "There is an error with the configuration of the package manager \"{0}\"": "Há um erro na configuração do gerenciador de pacotes \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Há uma instalação em andamento. Se você fechar o UniGetUI, a instalação pode falhar e ter resultados inesperados. Deseja sair do UniGetUI mesmo assim?", "They are the programs in charge of installing, updating and removing packages.": "São os programas responsáveis por instalar, atualizar e remover pacotes.", "Third-party licenses": "Licenças de terceiros", "This could represent a <b>security risk</b>.": "<PERSON><PERSON> pode representar um <b>risco de segurança</b>.", "This is not recommended.": "Isto não é recomendado", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "<PERSON><PERSON> provavelmente se deve ao fato de o pacote que você recebeu ter sido removido ou publicado em um gerenciador de pacotes que você não ativou. O ID recebido é {0}", "This is the <b>default choice</b>.": "Esta é a <b>es<PERSON><PERSON><PERSON> pad<PERSON></b>.", "This may help if WinGet packages are not shown": "<PERSON><PERSON> pode ajudar caso os pacotes do WinGet não sejam exibidos", "This may help if no packages are listed": "<PERSON><PERSON> pode ajudar se nenhum pacote estiver listado", "This may take a minute or two": "<PERSON><PERSON> pode levar um ou dois minutos", "This operation is running interactively.": "Esta operação está sendo executada de forma interativa.", "This operation is running with administrator privileges.": "Esta operação está sendo executada com privilégios de administrador.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Esta opção CAUSARÁ problemas. Qualquer operação que não consiga se elevar FALHARÁ. Instalar/atualizar/desinstalar como administrador NÃO FUNCIONARÁ.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Este pacote possui algumas configurações que são potencialmente perigosas e podem ser ignoradas por padrão.", "This package can be updated": "Este pacote pode ser atualizado", "This package can be updated to version {0}": "Este pacote pode ser atualizado para a versão {0}", "This package can be upgraded to version {0}": "Este pacote pode ser atualizado para a versão {0}", "This package cannot be installed from an elevated context.": "Este pacote não pode ser instalado em um contexto com privilégios elevados.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Este pacote não tem capturas de tela ou está faltando o ícone? Contribua para o UniGetUI adicionando os ícones e capturas de tela ausentes ao nosso banco de dados público e aberto.", "This package is already installed": "Este pacote já está instalado", "This package is being processed": "Este pacote está sendo processado", "This package is not available": "Este pacote não está disponível", "This package is on the queue": "Este pacote está na fila", "This process is running with administrator privileges": "Este processo está sendo executado com privilégios de administrador", "This project has no connection with the official {0} project — it's completely unofficial.": "Este projeto não tem conexão com o projeto oficial {0} - é completamente não oficial.", "This setting is disabled": "Esta configuração está desativada", "This wizard will help you configure and customize WingetUI!": "Este assistente ajudará você a configurar e personalizar o UniGetUI!", "Toggle search filters pane": "Alternar painel de filtros de pesquisa", "Translators": "<PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Tentar encerrar os processos que se recusam a fechar quando solicitados", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Ativar esta opção permite alterar o arquivo executável usado para interagir com os gerenciadores de pacotes. Embora isso permita uma personalização mais precisa dos seus processos de instalação, também pode ser perigoso.", "Type here the name and the URL of the source you want to add, separed by a space.": "Digite aqui o nome e a URL da fonte que você deseja adicionar, separados por um espaço.", "Unable to find package": "Não foi possível encontrar o pacote", "Unable to load informarion": "Não foi possí<PERSON> carre<PERSON> as informações", "UniGetUI collects anonymous usage data in order to improve the user experience.": "O UniGetUI coleta dados de uso anônimas para melhorar a experiência do usuário.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "O UniGetUI coleta dados de uso anônimos com o único propósito de entender e melhorar a experiência do usuário.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "O UniGetUI detectou um novo atalho na área de trabalho que pode ser excluído automaticamente.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "O UniGetUI detectou os seguintes atalhos na área de trabalho, que poderão ser removidos automaticamente em futuras atualizações", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "O UniGetUI detectou {0} novos atalhos na área de trabalho que podem ser excluídos automaticamente.", "UniGetUI is being updated...": "O UniGetUI está sendo atualizado...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "O UniGetUI não está relacionado a nenhum dos gerenciadores de pacotes compatíveis. O UniGetUI é um projeto independente.", "UniGetUI on the background and system tray": "UniGetUi em segundo plano e área de notificação", "UniGetUI or some of its components are missing or corrupt.": "O UniGetUI ou alguns de seus componentes estão ausentes ou corrompidos.", "UniGetUI requires {0} to operate, but it was not found on your system.": "O UniGetUI requer {0} para funcionar, mas ele não foi encontrado em seu sistema.", "UniGetUI startup page:": "Página inicial do UniGetUI:", "UniGetUI updater": "Atualizador do UniGetUI", "UniGetUI version {0} is being downloaded.": "A versão {0} do UniGetUI está sendo baixada.", "UniGetUI {0} is ready to be installed.": "O UniGetUI {0} está pronto para ser instalado.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Desinstalar o Scoop (e os seus pacotes)\n", "Uninstall and more": "Desinstação e mais", "Uninstall and remove data": "Desinstalar e remover dados", "Uninstall as administrator": "Desinstalar como administrador", "Uninstall canceled by the user!": "Desinstalação cancelada pelo usuário!", "Uninstall failed": "Desinstalação falhou", "Uninstall options": "Opções de desinstalação", "Uninstall package": "Desinstalar pacote", "Uninstall package, then reinstall it": "Desinstalar pacote e reinstalá-lo", "Uninstall package, then update it": "Desinstalar o pacote e atualizá-lo", "Uninstall previous versions when updated": "Desinstalar versões anteriores ao atualizar", "Uninstall selected packages": "Desinstalar pacotes selecionados", "Uninstall selection": "Desinstalar <PERSON>", "Uninstall succeeded": "Desinstalação bem-sucedida", "Uninstall the selected packages with administrator privileges": "Desinstalar os pacotes selecionados com privilégios de administrador", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Pacotes desinstaláveis com a origem listada como \"{0}\" não são publicados em nenhum gerenciador de pacotes, portanto, não há informações disponíveis para mostrar sobre eles.", "Unknown": "Desconhecido", "Unknown size": "<PERSON><PERSON><PERSON>", "Unset or unknown": "<PERSON><PERSON> definido ou desconhecido", "Up to date": "Atualizado", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Atualizar o UniGetUI automaticamente", "Update all": "<PERSON><PERSON><PERSON><PERSON> tudo", "Update and more": "Atualização e mais", "Update as administrator": "Atualizar como administrador", "Update check frequency, automatically install updates, etc.": "Frequência de verificação de atualização, instalação automática de atualizações, etc.", "Update checking": null, "Update date": "Data da atualização", "Update failed": "Falha na atualização", "Update found!": "Atualização encontrada!", "Update now": "Atual<PERSON>r agora", "Update options": "Opções de atualização", "Update package indexes on launch": "Atualizar índices de pacotes na inicialização", "Update packages automatically": "At<PERSON><PERSON>r pacotes automaticamente", "Update selected packages": "Atualizar pacotes selecionados", "Update selected packages with administrator privileges": "Atualizar os pacotes selecionados com privilégios de administrador", "Update selection": "At<PERSON>izar <PERSON>", "Update succeeded": "Atualização bem-sucedida", "Update to version {0}": "Atualizar para a versão {0}", "Update to {0} available": "Atualização para {0} disponível", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Atualizar automaticamente os portfiles Git do vcpkg (exige o Git instalado)", "Updates": "Atualizações", "Updates available!": "Atualizações disponíveis!", "Updates for this package are ignored": "As atualizações para este pacote estão sendo ignoradas", "Updates found!": "Atualizações encontradas!", "Updates preferences": "Preferências de atualizações", "Updating WingetUI": "Atualizando UniGetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Usar versão legada do WinGet integrado em vez dos cmdlets do PowerShell", "Use a custom icon and screenshot database URL": "Usar uma URL de banco de dados personalizada para ícones e capturas de tela", "Use bundled WinGet instead of PowerShell CMDlets": "Usar o WinGet integrado em vez dos cmdlets do PowerShell", "Use bundled WinGet instead of system WinGet": "Usar WinGet integrado em vez do WinGet do sistema", "Use installed GSudo instead of UniGetUI Elevator": "Usar o GSudo instalado em vez do UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Usar o GSudo instalado em vez do integrado", "Use system Chocolatey": "Usar Chocolatey do sistema", "Use system Chocolatey (Needs a restart)": "Usar Chocolatey do sistema (Requer reinicialização)", "Use system Winget (Needs a restart)": "Usar Winget do sistema (Requer reinicialização)", "Use system Winget (System language must be set to english)": "Usar Winget do sistema (O idioma do sistema deve ser definido para inglês)", "Use the WinGet COM API to fetch packages": "Usar a API COM do WinGet para obter pacotes", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Usar o módulo WinGet do PowerShell em vez da API COM do WinGet", "Useful links": "<PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON>", "User interface preferences": "Preferências da interface do usuário", "User | Local": "Usuário | Local", "Username": "Nome de usuário", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "O uso do UniGetUI implica na aceitação da Licença Pública Geral Menor do GNU v2.1", "Using WingetUI implies the acceptation of the MIT License": "O uso do UniGetUI implica na aceitação da Licença MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Não foi encontrado o diretório raiz do vcpkg. Defina a variável de ambiente %VCPKG_ROOT% ou defina-a nas Configurações do UniGetUI", "Vcpkg was not found on your system.": "O vcpkg não foi encontrado em seu sistema.", "Verbose": "<PERSON><PERSON><PERSON><PERSON>", "Version": "Vers<PERSON>", "Version to install:": "Versão para instalar:", "Version:": "Versão:", "View GitHub Profile": "Ver perfil do GitHub", "View WingetUI on GitHub": "Ver UniGetUI no GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Ver o código fonte do UniGetUI. A partir daí, você pode relatar bugs ou sugerir recursos, ou até mesmo contribuir diretamente para o Projeto", "View mode:": "Modo de visualização:", "View on UniGetUI": "Ver no UniGetUI", "View page on browser": "Ver página no navegador", "View {0} logs": "Visualizar registros do {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Aguarde até que o dispositivo esteja conectado à internet antes de tentar realizar tarefas que exijam conectividade à internet.", "Waiting for other installations to finish...": "Aguardando outras instalações terminarem.", "Waiting for {0} to complete...": "Aguardando {0} ser concluído...", "Warning": "Aviso", "Warning!": "Aviso!", "We are checking for updates.": "Estamos verificando por atualizações.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Não foi possível carregar informações detalhadas sobre este pacote, pois ele não foi encontrado em nenhuma de suas fontes de pacotes", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Não foi possível carregar informações detalhadas sobre este pacote, pois ele não foi instalado de um gerenciador de pacotes disponível.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Não foi possível {action} {package}. Tente novamente mais tarde. Clique em \"{showDetails}\" para obter detalhes do instalador.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Não foi possível {action} {package}. Tente novamente mais tarde. Clique em \"{showDetails}\" para obter detalhes do desinstalador.", "We couldn't find any package": "Não foi possível encontrar nenhum pacote", "Welcome to WingetUI": "Bem-vindo ao UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Na instalação em lote, instalar também pacotes que já estão instalados", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Quando novos atalhos forem detectados, exclua-os automaticamente em vez de mostrar esta caixa de diálogo.", "Which backup do you want to open?": "Qual backup você deseja abrir?", "Which package managers do you want to use?": "Quais gerenciadores de pacotes você deseja usar?", "Which source do you want to add?": "Qual fonte você deseja adicionar?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Embora o Winget possa ser usado dentro do WingetUI, o WingetUI pode ser usado com outros gerenciadores de pacotes, o que pode ser confuso. No passado, o WingetUI foi projetado para funcionar apenas com o Winget, mas isso não é mais verdade e, portanto, o WingetUI não representa o que este projeto pretende se tornar.", "WinGet could not be repaired": "O WinGet não pôde ser reparado", "WinGet malfunction detected": "Mau funcionamento do WinGet detectado", "WinGet was repaired successfully": "O WinGet foi reparado com sucesso", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "UniGetUI - Tudo está atualizado", "WingetUI - {0} updates are available": "UniGetUI - {0} atualizações estão disponíveis", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "Página inicial do UniGetUI", "WingetUI Homepage - Share this link!": "Página inicial do UniGetUI - Compartilhe este link!", "WingetUI License": "Licença do UniGetUI", "WingetUI Log": "Log do UniGetUI", "WingetUI Repository": "Repositório do UniGetUI", "WingetUI Settings": "Configurações do UniGetUI", "WingetUI Settings File": "Arquivo de Configurações do UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "O UniGetUI utiliza as seguintes bibliotecas. Sem elas, o UniGetUI não seria possível.", "WingetUI Version {0}": "UniGetUI Versão {0}", "WingetUI autostart behaviour, application launch settings": "Comportamento de inicialização automática do UniGetUI, configurações de inicialização do aplicativo", "WingetUI can check if your software has available updates, and install them automatically if you want to": "O UniGetUI pode verificar se seu software tem atualizações disponíveis e instalá-las automaticamente, se você desejar", "WingetUI display language:": "Idioma de exibição do UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "O UniGetUI foi executado como administrador, o que não é recomendado. Quando executado como administrador, TODAS as operações iniciadas pelo UniGetUI terão privilégios de administrador. Você ainda pode usar o programa, mas é altamente recomendável não executar o UniGetUI com privilégios de administrador.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "O UniGetUI foi traduzido para mais de 40 idiomas graças aos tradutores voluntários. Obrigado 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "O UniGetUI não foi traduzido automaticamente. Os seguintes usuários foram responsáveis pelas traduções:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "O UniGetUI é um aplicativo que facilita o gerenciamento de seu software, fornecendo uma interface gráfica completa para seus gerenciadores de pacotes de linha de comando.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "O WingetUI está sendo renomeado para enfatizar a diferença entre o WingetUI (a interface que você está usando agora) e o Winget (um gerenciador de pacotes desenvolvido pela Microsoft com o qual não tenho relação)", "WingetUI is being updated. When finished, WingetUI will restart itself": "O UniGetUI está sendo atualizado. Quando terminar, o UniGetUI será reiniciado automaticamente.", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "O UniGetUI é gratuito e será gratuito para sempre. Sem an<PERSON>, sem cartão de crédito, sem versão premium. 100% gratuito, para sempre.", "WingetUI log": "Log do UniGetUI", "WingetUI tray application preferences": "Preferências do aplicativo UniGetUI na bandeja do sistema", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "O UniGetUI usa as seguintes bibliotecas. Sem elas, o UniGetUI não seria possível.", "WingetUI version {0} is being downloaded.": "A versão {0} do UniGetUI está sendo baixada.", "WingetUI will become {newname} soon!": "O WingetUI em breve será {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "O UniGetUI não verificará as atualizações periodicamente. Elas ainda serão verificadas na inicialização, mas você não será avisado sobre elas.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "O UniGetUI mostrará um prompt do UAC sempre que um pacote precisar de elevação para ser instalado.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "O WingetUI em breve será chamado de {newname}. Isso não representará nenhuma alteração no aplicativo. Eu (o desenvolvedor) continuarei o desenvolvimento deste projeto como estou fazendo agora, mas sob um nome diferente.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "O UniGetUI não seria possível sem a ajuda de nossos queridos colaboradores. Confira o perfil deles no GitHub, o WingetUI não seria possível sem eles!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "O UniGetUI não seria possível sem a ajuda dos colaboradores. Obrigado a todos 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} está pronto para ser instalado.", "Write here the process names here, separated by commas (,)": "Escreva aqui os nomes dos processos, separados por vírgulas (,)", "Yes": "<PERSON>m", "You are logged in as {0} (@{1})": "Você está logado como {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Você pode alterar esse comportamento nas configurações de segurança do UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Você pode definir os comandos que serão executados antes ou depois da instalação, atualização ou desinstalação deste pacote. Eles serão executados em um prompt de comando, portanto, scripts CMD funcionarão aqui.", "You have currently version {0} installed": "Você tem a versão {0} instalada atualmente", "You have installed WingetUI Version {0}": "Você instalou a versão {0} do UniGetUI", "You may lose unsaved data": "Você pode perder dados não salvos", "You may need to install {pm} in order to use it with WingetUI.": "Você pode precisar instalar {pm} para usá-lo com o UniGetUI.", "You may restart your computer later if you wish": "Você pode reiniciar o computador mais tarde, se desejar", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Você será solicitado apenas uma vez e os direitos de administrador serão concedidos aos pacotes que os solicitarem.", "You will be prompted only once, and every future installation will be elevated automatically.": "Você será solicitado apenas uma vez e todas as instalações futuras serão elevadas automaticamente.", "You will likely need to interact with the installer.": "Provavelmente você precisará interagir com o instalador.", "[RAN AS ADMINISTRATOR]": "EXECUTADO COMO ADMINISTRADOR", "buy me a coffee": "me pague um café", "extracted": "<PERSON><PERSON><PERSON>", "feature": "recurso", "formerly WingetUI": "antigo WingetUI", "homepage": "página inicial", "install": "instalar", "installation": "instalação", "installed": "instalado", "installing": "instalando", "library": "biblioteca", "mandatory": "obrigatória", "option": "opção", "optional": "opcional", "uninstall": "desinstalar", "uninstallation": "desinstalação", "uninstalled": "desinstalado", "uninstalling": "desinstalando", "update(noun)": "atualização", "update(verb)": "atual<PERSON>r", "updated": "atualizado", "updating": "atualizando", "version {0}": "vers<PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} opções de instalação estão bloqueadas no momento porque {0} seguem as opções de instalação padrão.", "{0} Uninstallation": "Desinstalação de {0}", "{0} aborted": "{0} abor<PERSON>o", "{0} can be updated": "{0} pode ser atualizado", "{0} can be updated to version {1}": "{0} pode ser atualizado para a versão {1}", "{0} days": "{0} dias", "{0} desktop shortcuts created": "{0} atalhos na área de trabalho criados", "{0} failed": "{0} falhou", "{0} has been installed successfully.": "{0} foi instalado com sucesso.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} foi instalado com sucesso. Recomenda-se reiniciar o UniGetUI para concluir a instalação", "{0} has failed, that was a requirement for {1} to be run": "{0} f<PERSON><PERSON>, um requerimento para {1} ser executado", "{0} homepage": "página inicial de {0}", "{0} hours": "{0} horas", "{0} installation": "instalação de {0}", "{0} installation options": "opções de instalação de {0}", "{0} installer is being downloaded": "O instalador do {0} está sendo baixado", "{0} is being installed": "{0} está sendo instalado", "{0} is being uninstalled": "{0} est<PERSON> sendo desinstalado", "{0} is being updated": "{0} está sendo atualizado", "{0} is being updated to version {1}": "{0} está sendo atualizado para a versão {1}", "{0} is disabled": "O {0} está desativado", "{0} minutes": "{0} minutos", "{0} months": "{0} meses", "{0} packages are being updated": "{0} pacotes estão sendo atualizados", "{0} packages can be updated": "{0} paco<PERSON> podem ser atualizados", "{0} packages found": "{0} pacotes encontrados", "{0} packages were found": "{0} pacotes foram encontrados", "{0} packages were found, {1} of which match the specified filters.": "{0} paco<PERSON> foram encontrados, dos quais {1} correspondem aos filtros especificados.", "{0} selected": "{0} se<PERSON><PERSON><PERSON>(s)", "{0} settings": "Configurações do {0}", "{0} status": "Status de {0}", "{0} succeeded": "{0} bem-sucedido", "{0} update": "atualizando {0}", "{0} updates are available": "{0} atualizações disponíveis", "{0} was {1} successfully!": "{0} foi {1} com sucesso!", "{0} weeks": "{0} semanas", "{0} years": "{0} anos", "{0} {1} failed": "{0} {1} falhou", "{package} Installation": "Instalação de {package}", "{package} Uninstall": "Desinstalação de {package}", "{package} Update": "Atualizando {package}...", "{package} could not be installed": "{package} não pôde ser instalado", "{package} could not be uninstalled": "{package} não pôde ser desinstalado", "{package} could not be updated": "{package} não pôde ser atualizado", "{package} installation failed": "A instalação de {package} falhou", "{package} installer could not be downloaded": "O instalador do {package} não pôde ser baixado", "{package} installer download": "Download do instalador do {package}", "{package} installer was downloaded successfully": "Instalador do {package} baixado com sucesso", "{package} uninstall failed": "A desinstalação de {package} falhou", "{package} update failed": "Falha ao atualizar {package}", "{package} update failed. Click here for more details.": "Falha ao atualizar {package}. Clique aqui para mais detalhes.", "{package} was installed successfully": "{package} foi instalado com sucesso", "{package} was uninstalled successfully": "{package} foi desinstalado com sucesso", "{package} was updated successfully": "{package} foi atualizado com sucesso", "{pcName} installed packages": "Pacotes instalados em {pcName}", "{pm} could not be found": "{pm} não foi encontrado", "{pm} found: {state}": "{pm} encontrado: {state}", "{pm} is disabled": "{pm} está desativado", "{pm} is enabled and ready to go": "{pm} está ativado e pronto para ser usado", "{pm} package manager specific preferences": "Preferências específicas do gerenciador de pacotes {pm}", "{pm} preferences": "Preferências de {pm}", "{pm} version:": "Versão do {pm}:", "{pm} was not found!": "{pm} não foi encontrado!"}