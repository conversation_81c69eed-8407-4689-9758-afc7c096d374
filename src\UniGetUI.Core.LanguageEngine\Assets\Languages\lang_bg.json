{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": null, "(Number {0} in the queue)": "(Номер {0} в опашката)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON><PERSON><PERSON>", "0 packages found": "Няма намерени пакети", "0 updates found": "Няма намерени актуализации", "1 - Errors": null, "1 day": "1 ден", "1 hour": "1 час", "1 month": null, "1 package was found": "Намерен е 1 пакет", "1 update is available": null, "1 week": "1 седмица", "1 year": null, "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": null, "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": null, "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": null, "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": null, "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": null, "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": null, "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Хранилище на инструменти, предназначени за екосистемата на .NET oт Microsoft.<br>Съдържа: <b>Инструменти, свъразни с .NET</b>", "A restart is required": "Изисква се рестарт", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": null, "About Qt6": " За Qt6", "About WingetUI": "За WingetUI", "About WingetUI version {0}": "За WingetUI версия {0}", "About the dev": "За автора", "Accept": null, "Action when double-clicking packages, hide successful installations": "Поведение при двойно кликване върху пакети, скриване на успешните инсталации", "Add": null, "Add a source to {0}": "Добавяне на източник към {0}", "Add a timestamp to the backup file names": null, "Add a timestamp to the backup files": null, "Add packages or open an existing bundle": null, "Add packages or open an existing package bundle": null, "Add packages to bundle": null, "Add packages to start": null, "Add selection to bundle": null, "Add source": "Добави източник ", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": null, "Addition succeeded": null, "Administrator privileges": "Администраторски привилегии", "Administrator privileges preferences": "Предпочитания за администраторските привилегии", "Administrator rights": "Администраторски права", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "Всички файлове", "All versions": "Всички версии", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": null, "Allow parallel installs (NOT RECOMMENDED)": "Разрешаване на паралелни инсталации (НЕ СЕ ПРЕПОРЪЧВА)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": null, "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": "Винаги повишавай правата, с които се стартират инсталациите на {pm}, по подразбиране", "Always run {pm} operations with administrator rights": null, "An error occurred": null, "An error occurred when adding the source: ": null, "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "Грешка при обработката на този пакет", "An error occurred:": null, "An interal error occurred. Please view the log for further details.": null, "An unexpected error occurred:": null, "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": null, "Android Subsystem": "Подсистема на Android", "Another source": "Друг източник", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": null, "App Name": "Име на приложението", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "Тема на приложението:", "Apply": null, "Architecture to install:": "Архитектура за инсталиране:", "Are these screenshots wron or blurry?": "Грешни или размазани са тези снимки?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "Сигурни ли сте?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": null, "Ask for administrator rights when required": "Питай за администраторски права, когато е необходимо", "Ask once or always for administrator rights, elevate installations by default": "Питай за администраторски права само веднъж или всеки път винаги, издигай инсталациите по подразбиране", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Питай само веднъж за администраторски привилегии (не е препоръчително)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": "Обърнете внимание", "Authenticate to the proxy with an user and a password": null, "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": null, "Automatically save a list of all your installed packages to easily restore them.": null, "Automatically save a list of your installed packages on your computer.": "Автоматично запазване на списъка на Вашите инсталирани пакети на Вашия компютър.", "Autostart WingetUI in the notifications area": "Автоматично стартиране на WingetUI в зоната за известия", "Available Updates": null, "Available updates: {0}": "Налични актуализации: {0}", "Available updates: {0}, not finished yet...": "Налични актуализации: {0}, още не сме готови...", "Backing up packages to GitHub Gist...": null, "Backup": null, "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Архивиране на инсталираните пакети", "Backup location": null, "Become a contributor": null, "Become a translator": null, "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Бета функции и други опции, които не трябва да се пипат", "Both": "И двата", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Ето какво още бихте могли да направите, за да научите повече за WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": null, "Cache administrator rights and elevate installers by default": "Кеширай администраторските права и издигай инсталаторите по подразбиране", "Cache administrator rights, but elevate installers only when required": null, "Cache was reset successfully!": "Кеша беше нулиран успешно!", "Can't {0} {1}": "Неуспех при {0} на {1}", "Cancel": "Отмяна", "Cancel all operations": null, "Change backup output directory": "Смяна на директорията на архивното копие", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": null, "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": "Смяна на мястото на инсталация", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Периодично проверяване за актуализации на пакети", "Check for updates": null, "Check for updates every:": "Проверяване за актуализации на всеки:", "Check for updates periodically": "Периодично проверявай за актуализации.", "Check for updates regularly, and ask me what to do when updates are found.": "Редовно проверявай за актуализации и ако такива са налице, питай за последващите действия.", "Check for updates regularly, and automatically install available ones.": "Редовно проверявай за актуализации и ако такива са налице, премини към инсталирането им автоматично.", "Check out my {0} and my {1}!": "Вижте моите {0} и моите {1}!", "Check out some WingetUI overviews": "Вижте няколко рецензии на WingetUI", "Checking for other running instances...": "Проверява се за други работещи копия...", "Checking for updates...": "Проверка за актуализации...", "Checking found instace(s)...": "Проверяват се намерените инстанции...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "Изчисти избора", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": null, "Clearing Scoop cache...": "Чистене на Scoop кеш...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": null, "Close": "Затвори", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Прибери WingetUI в зоната за известия", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": null, "Command-line to run:": null, "Compare query against": "Сравни заявката срещу", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Информация за компонентите", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": null, "Contribute to the icon and screenshot repository": "Допринасяне към хранилището за икони и снимки", "Contributors": "Сътрудници", "Copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "Копирай в клипбоарда", "Could not add source": null, "Could not add source {source} to {manager}": null, "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": null, "Could not load announcements - HTTP status code is $CODE": null, "Could not remove source": null, "Could not remove source {source} from {manager}": null, "Could not remove {source} from {manager}": null, "Credentials": null, "Current Version": "Текуща версия", "Current status: Not logged in": null, "Current user": "Текущ потребител", "Custom arguments:": null, "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Персонализирани аргументи на командния ред:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Персонали<PERSON><PERSON>р<PERSON>й WingetUI - само за хакери и напреднали потребители", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ОТКАЗ ОТ ОТГОВОРНОСТ: НЕ НОСИМ ОТГОВОРНОСТ ЗА ИЗТЕГЛЕНИТЕ ПАКЕТИ. МОЛЯ, УВЕРЕТЕ СЕ, ЧЕ ИНСТАЛИРАТЕ САМО ДОВЕРЕН СОФТУЕР.", "Dark": "Тъмна", "Decline": null, "Default": "По подразбиране", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Предпочитания по подразбиране - подходящо за обикновенни потребители", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "Описание:", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Разработването е трудно и тази програма е безплатна. Но ако Ви е харесала, винаги можете <b>да ми купите кафе</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Директно започни инсталацията, когато артикул от раздел \"{discoveryTab}\"  бъде двукратно щракнат (вместо да се показва пълната информация за пакета)", "Disable new share API (port 7058)": "Изключи API за ново споделяне (порт 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": null, "Discover Packages": "Откриване на пакети", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "Разграничавай големи от малки букви", "Distinguish between uppercase and lowercase": null, "Do NOT check for updates": "НЕ проверявай за актуализации", "Do an interactive install for the selected packages": "Интерактивна инсталация на избраните пакети", "Do an interactive uninstall for the selected packages": "Интерактивна деинсталация на избраните пакети", "Do an interactive update for the selected packages": "Интерактивна актуализация на избраните пакети", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Не изтегляй автоматично нови преводи на приложението от GitHub", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": null, "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "Не актуализи<PERSON><PERSON>й пакетните индекси при стартиране", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": null, "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Смятате ли WingetUI за полезен? Искате ли да подкрепите автора? Ако е така, можете да ми {0}, много помага!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": null, "Do you really want to uninstall {0} packages?": "Сигурни ли сте, че искате да деинсталирате {0} пакети?", "Do you really want to uninstall {0}?": "Наистина ли искате да деинсталирате {0}?", "Do you want to restart your computer now?": "Искате ли да рестартирате компютъра сега?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Искате ли да преведете WingetUI на вашия език? Вижте как да допринесете <a style=\"color:{0}\" href=\"{1}\"a>ТУК!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": null, "Donate": "Направи дарение", "Done!": null, "Download failed": null, "Download installer": null, "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": null, "Download updated language files from GitHub automatically": null, "Downloading": null, "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "Изтегляне на метаданни за пакета...", "Enable Scoop cleanup on launch": "Активиране на Scoop cleanup при стартиране", "Enable WingetUI notifications": "Включи WingetUI известията", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": null, "Enable it to install packages from {pm}.": null, "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Включи {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "Грешка", "Everything is up to date": null, "Exact match": null, "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": null, "Experimental settings and developer options": "Експериментални настройки и опции за разработчици", "Export": "Експортир<PERSON>й", "Export log as a file": "Експортирай лога като файл", "Export packages": "Експортирай пакетите", "Export selected packages to a file": "Експортирай избраните пакети във файл", "Export settings to a local file": "Експортирай избраните пакети в локален файл", "Export to a file": "Експортирай във файл", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": null, "Filters": "Филтри", "Finish": "Завър<PERSON>и", "Follow system color scheme": "Следване на цветовата схема на системата", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Форсирай winget версия компилирана на ARM (САМО ЗА ARM64 СИСТЕМИ)", "Formerly known as WingetUI": null, "Found": "Намерени", "Found packages: ": null, "Found packages: {0}": "Намерени пакети: {0} ", "Found packages: {0}, not finished yet...": "Намерени пакети: {0}, търсенето не е приключило...", "General preferences": "Общи настройки", "GitHub profile": "профил в GitHub", "Global": "Гло<PERSON><PERSON><PERSON><PERSON>н", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Чудесно хранилище на малко известни, но полезни инструменти и други интересни пакети.<br>Съдръжание: <b>Инструменти, програми за командния ред, общ софтуер (изисква кофата \"extras\")</b>", "Great! You are on the latest version.": null, "Grid": null, "Help": "Помощ", "Help and documentation": "Помощ и документация", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Здравейте, казвам се Мартѝ и съм <i>автор</i> на WingetUI. WingetUI е създаден изцяло в свободното ми време!", "Hide details": "Скриване на подробности", "Homepage": "Уебс<PERSON><PERSON>т", "Hooray! No updates were found.": "Ура! Няма намерени актуализации!", "How should installations that require administrator privileges be treated?": "Как да се третират инсталации, които се нуждаят от администраторски права?", "How to add packages to a bundle": null, "I understand": null, "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Игнориране на бъдещи актуализации за този пакет", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "Игнориране на избраните пакети", "Ignore special characters": "Игнориране на специални символи", "Ignore updates for the selected packages": "Игнориране на актуализации за избраните пакети", "Ignore updates for this package": "Игнориране на актуализации за този пакет", "Ignored updates": "Игнорирани актуализации", "Ignored version": "Игнорирана версия", "Import": "Импортиране", "Import packages": "Импортиране на пакети", "Import packages from a file": "Импортиране на пакети от файл", "Import settings from a local file": "Импортиране на настройки от локален файл", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "Инициализиране на WingetUI...", "Install": "Инсталиране", "Install Scoop": "Инсталиране на Scoop", "Install and more": null, "Install and update preferences": null, "Install as administrator": "Инсталиране като администратор", "Install available updates automatically": null, "Install location can't be changed for {0} packages": null, "Install location:": null, "Install options": null, "Install packages from a file": "Инсталиране на пакети от файл", "Install prerelease versions of UniGetUI": null, "Install selected packages": "Инсталиране на избраните пакети", "Install selected packages with administrator privileges": "Започни инсталирането на избраните пакети с администраторски права", "Install selection": null, "Install the latest prerelease version": null, "Install updates automatically": "Автоматично инсталиране на актуализации", "Install {0}": null, "Installation canceled by the user!": "Инсталацията е прекратена от потребителят!", "Installation failed": null, "Installation options": "Инсталационни настройки", "Installation scope:": "Обхват на инсталацията:", "Installation succeeded": null, "Installed Packages": "Инсталирани пакети", "Installed Version": "Инсталир<PERSON>на версия", "Installed packages": null, "Installer SHA256": "SHA256 инсталатор", "Installer SHA512": "SHA512 инсталатор", "Installer Type": "Тип инсталатор", "Installer URL": "Лин<PERSON> към инсталатора", "Installer not available": null, "Instance {0} responded, quitting...": "Отговориха {0} инстанции, затваряне...", "Instant search": "Незабавно търсене", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "Интерактивна инсталация", "Interactive operation": null, "Interactive uninstall": "Интерактивна деинсталация", "Interactive update": "Интерактивна актуализация", "Internet connection settings": null, "Is this package missing the icon?": "Липсва ли иконата на този пакет?", "Is your language missing or incomplete?": null, "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": null, "Language": null, "Language, theme and other miscellaneous preferences": "Език, тема и други предпочитания", "Last updated:": "Последна актуализация:", "Latest": "Последна", "Latest Version": "Последна Версия", "Latest Version:": "Последна версия:", "Latest details...": "Последни детайли...", "Launching subprocess...": null, "Leave empty for default": null, "License": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Licenses": "Лицензи", "Light": "Светла", "List": null, "Live command-line output": "Резултат в конзолата", "Live output": null, "Loading UI components...": "Зареждане на компонентите от потребителския интерфейс...", "Loading WingetUI...": "Зареждане на WingetUI...", "Loading packages": null, "Loading packages, please wait...": null, "Loading...": "Зареждане...", "Local": "Локален", "Local PC": "Локален ПК", "Local backup advanced options": null, "Local machine": "Локална машина", "Local package backup": null, "Locating {pm}...": "Локализир<PERSON>н<PERSON> {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": null, "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Търсят се пакети...", "Machine | Global": null, "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": null, "Manage ignored packages": "Управление на игнорираните пакети", "Manage ignored updates": "Управление на игнорираните актуализации", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": "Управление на {0} източника", "Manifest": "Манифест", "Manifests": "Манифести", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Официалният мениждър за пакети на Microsoft. Съдържа добре известни и проверени пакети<br>Съдържа: <b>общ софтуер, пакети от Microsoft Store</b>", "Missing dependency": null, "More": null, "More details": "Още подробности", "More details about the shared data and how it will be processed": null, "More info": null, "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "Име", "New": null, "New Version": "Нова Версия", "New bundle": null, "New version": "Нова версия", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Не", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "Няма намерени пакети", "No packages found matching the input criteria": "Няма намерени пакети, отговарящи на въведените критерии", "No packages have been added yet": null, "No packages selected": "Не са избрани пакети", "No packages were found": null, "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": null, "No sources found": null, "No sources were found": "Не бяха намерени източници", "No updates are available": "Няма налични актуализации", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": null, "Not available": "Не е наличен", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Не е намерен", "Not right now": null, "Notes:": "Бележки:", "Notification preferences": null, "Notification tray options": "Настройки на таблата за известия", "Notification types": null, "NuPkg (zipped manifest)": null, "OK": "ОК", "Ok": "Ок", "Open": "Отвори", "Open GitHub": "Отв<PERSON><PERSON><PERSON>", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": null, "Open backup location": "Отваряне на мястото на архивното копие", "Open existing bundle": null, "Open install location": null, "Open the welcome wizard": "Отваряне на съветника за добре дошли", "Operation canceled by user": null, "Operation cancelled": null, "Operation history": "История на операциите", "Operation in progress": "Изпълнява се операция", "Operation on queue (position {0})...": null, "Operation profile:": null, "Options saved": "Настройките са запазени", "Order by:": null, "Other": null, "Other settings": null, "Package": null, "Package Bundles": null, "Package ID": "ID на пакета", "Package Manager": null, "Package Manager logs": "Дневник на мениджърът на пакети", "Package Managers": null, "Package Name": "Име на пакета", "Package backup": null, "Package backup settings": null, "Package bundle": null, "Package details": "Подробности за пакета", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "Настройки на мениджърът на пакети", "Package managers": null, "Package not found": null, "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "Пакети", "Packages found: {0}": "Намерени пакети: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "Поставете валиден адрес (URL) на базата данни", "Pause updates for": null, "Perform a backup now": null, "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": null, "Periodically perform a backup of the installed packages": null, "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": null, "Please enter at least 3 characters": null, "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": null, "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": null, "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": null, "Please select how you want to configure WingetUI": "Моля, изберете как искате да конфигурирате WingetUI", "Please try again later": null, "Please type at least two characters": "Моля, въведете поне 2 знака", "Please wait": null, "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "Моля изчакайте...", "Portable": "Преносим", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": null, "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": null, "Preparing packages, please wait...": null, "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "Дата на публикация:", "Publisher": "Издател", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Мениджър на библиотеки за Python. Съдържа библиотеки за Python и други инструменти, свързани с Python.<br>Съдържа: <b> библиотеки за Python и свързану инструменти</b>", "Quit": "Изход", "Quit WingetUI": null, "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": "Преинсталиране на пакет", "Related settings": null, "Release notes": "Бележки към изданието", "Release notes URL": null, "Release notes URL:": "Адрес на бележите към изданието", "Release notes:": "Бележки към версията:", "Reload": "Презареждане", "Reload log": "Презареждане на лога", "Removal failed": null, "Removal succeeded": null, "Remove from list": null, "Remove permanent data": "Премахване на постоянните данни", "Remove selection from bundle": null, "Remove successful installs/uninstalls/updates from the installation list": "Премахване на успешните инсталации/деинсталации/актуализации от списъка за инсталиране", "Removing source {source}": null, "Removing source {source} from {manager}": null, "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": null, "Repository": "Храни<PERSON><PERSON><PERSON>е", "Reset": "Нулиране", "Reset Scoop's global app cache": "Нулиране на общия кеш на Scoop за приложения", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "Нулиране на източниците на Winget (може да помогне, ако няма показани пакети)", "Reset WingetUI": "Нулиране на WingetUI", "Reset WingetUI and its preferences": "Нулиране на WingetUI и неговите настройки", "Reset WingetUI icon and screenshot cache": "Нулиране на кеша на WingetUI за иконки и екранни снимки", "Reset list": null, "Resetting Winget sources - WingetUI": null, "Restart": null, "Restart UniGetUI": null, "Restart WingetUI": "Рестартиране на WingetUI", "Restart WingetUI to fully apply changes": null, "Restart later": "Да се рестартира по-късно", "Restart now": "Да се рестартира сега", "Restart required": "Нужно е рестрартиране", "Restart your PC to finish installation": "Моля, рестарартирайте компютър си, за завършите инсталацията", "Restart your computer to finish the installation": "Моля, рестарартирайте компютър си, за завършите инсталацията", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Нов опит", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": null, "Return to top": "Връщане горе", "Run": null, "Run as admin": "Стартиране като администратор", "Run cleanup and clear cache": null, "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "Стартиране на инсталатора...", "Running the uninstaller...": "Стартиране на деинсталатора...", "Running the updater...": "Стартиране на актуализатора...", "Save": null, "Save File": "Запазване на файла", "Save and close": null, "Save as": null, "Save bundle as": null, "Save now": "Запазване сега", "Saving packages, please wait...": null, "Scoop Installer - WingetUI": null, "Scoop Uninstaller - WingetUI": null, "Scoop package": "Пакет на Scoop", "Search": "Търсене", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": null, "Search for packages": "Търсене на пакети", "Search for packages to start": "За да започнете, потърсете пакети", "Search mode": null, "Search on available updates": "Търсене в намерените актуализации", "Search on your software": "Търсене във вашия софтуер", "Searching for installed packages...": "Търсене на инсталирани пакети...", "Searching for packages...": "Търсене на пакети...", "Searching for updates...": "Търсене на актуализации...", "Select": "Избор", "Select \"{item}\" to add your custom bucket": null, "Select a folder": "Избиране на папка", "Select all": "Маркиране на всички", "Select all packages": "Избор на всички пакети", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "Изберете, <b<само ако знаете какво правите</b>.", "Select package file": "Избиране на пакетен файл", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": null, "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Изберете кои <b>мениджъри на пакети</b> да се използват ({0}), конфигурирайте как да се инсталират пакетите, управлявайте използването на администраторските права и т.н.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Изпратен е handshake сигнал. Изчаква се отговор... ({0}%)", "Set a custom backup file name": null, "Set custom backup file name": "Персонализирано име на архивния файл", "Settings": null, "Share": null, "Share WingetUI": null, "Share anonymous usage data": null, "Share this package": "Споделяне на този пакет", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": null, "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "Показване на WingetUI", "Show a notification when an installation fails": "Показване на съобщение при провал на инсталция", "Show a notification when an installation finishes successfully": "Показване на съобщение при успешна инсталация", "Show a notification when an operation fails": null, "Show a notification when an operation finishes successfully": null, "Show a notification when there are available updates": "Показване на известие при наличие на актуализации", "Show a silent notification when an operation is running": null, "Show details": "Показване на детайли", "Show in explorer": null, "Show info about the package on the Updates tab": "Показване на информация за пакета в раздела Актуализации", "Show missing translation strings": "Показване на липсващи преводи", "Show notifications on different events": null, "Show package details": "Показване на подробности за пакет", "Show package icons on package lists": null, "Show similar packages": null, "Show the live output": "Показване на резултата в реално време", "Size": null, "Skip": "Пропускане", "Skip hash check": "Пропускане проверката на хеша", "Skip hash checks": null, "Skip integrity checks": null, "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "Пропускане на проверката на хеша при инсталиране на избраните пакети", "Skip the hash check when updating the selected packages": "Пропускане на проверката на хеша при актуализация на избраните пакети", "Skip this version": "Пропускане на тази версия", "Software Updates": "Софтуерни актуализации", "Something went wrong": null, "Something went wrong while launching the updater.": null, "Source": "Източник", "Source URL:": null, "Source added successfully": null, "Source addition failed": null, "Source name:": null, "Source removal failed": null, "Source removed successfully": null, "Source:": "Източник:", "Sources": "Източници", "Start": "Начало", "Starting daemons...": "Стартиране на нишки...", "Starting operation...": null, "Startup options": "Настройки при стартиране", "Status": "Статус", "Stuck here? Skip initialization": null, "Suport the developer": "Подкрепете разработчика", "Support me": null, "Support the developer": null, "Systems are now ready to go!": null, "Telemetry": null, "Text": null, "Text file": "Текстов файл", "Thank you ❤": null, "Thank you 😉": null, "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": null, "The backup will be performed after login.": null, "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": null, "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Оригиналния мениджър за пакети за Windows. Има всичко. <br>Съдържа: <b>Общ софтуер</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": null, "The following packages are going to be installed on your system.": "Следните пакети ще бъдат инсталирани на Вашата система.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": null, "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Следните настройки ще се прилагат всеки път, когато този пакет бъде инсталиран, актуализиран или премахнат. Те ще бъдат запазени автоматично.", "The icons and screenshots are maintained by users like you!": "Иконите и снимките се поддържат от потребители като вас!", "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "Инсталаторът има невалидна контролна сума.", "The installer hash does not match the expected value.": null, "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Основната цел на този проект е да създаде интуитивен потребителски интерфейс за най-разпространените конзолни мениджъри на пакети за Windows, като Winget и Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": null, "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": null, "The selected packages have been blacklisted": "Избраните пакети са включени в забранения списък", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Размерът на архивното копие се оценява да е под 1MB.", "The source {source} was added to {manager} successfully": null, "The source {source} was removed from {manager} successfully": null, "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": null, "The update will not continue.": null, "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": null, "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": null, "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Има две основни причини да не стартирате WingetUI като администратор: Първата е, че мениджърът на пакети Scoop може да причини проблеми с някои команди, когато се изпълнява с администраторски права. Второто е, че стартирането на WingetUI като администратор означава, че всеки пакет, който изтеглите, ще се изпълнява като администратор (това не е безопасно). Не забравяйте, че ако трябва да инсталирате конкретен пакет като администратор, винаги можете да щракнете с десния бутон върху него -> Инсталиране/Актуализиране/Деинсталиране като администратор.", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": null, "They are the programs in charge of installing, updating and removing packages.": "Те са програми, които инсталират, актуализират и премахват пакети.", "Third-party licenses": null, "This could represent a <b>security risk</b>.": "Това може да е <b>риск за сигурността</b>.", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": null, "This is the <b>default choice</b>.": "Тов а е <b>избора по подразбиране</b>.", "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": null, "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Този пакет може да се актуализира", "This package can be updated to version {0}": "Този пакет може да бъде актуализиран до версия {0}", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": null, "This package is already installed": "Този пакет е вече инсталиран", "This package is being processed": "Този пакет се обработва", "This package is not available": null, "This package is on the queue": "Този пакет е в опашката", "This process is running with administrator privileges": "Този процес се изпълнява с администраторски права", "This project has no connection with the official {0} project — it's completely unofficial.": null, "This setting is disabled": "Тази настройка е изключена", "This wizard will help you configure and customize WingetUI!": null, "Toggle search filters pane": "Превключване на панела с филтри за търсене", "Translators": "Преводачи", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": null, "Unable to find package": null, "Unable to load informarion": "Не може да се зареди информация", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": null, "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "Деинсталиране", "Uninstall Scoop (and its packages)": "Деинсталира<PERSON><PERSON> на Scoop (и неговите пакети)", "Uninstall and more": null, "Uninstall and remove data": null, "Uninstall as administrator": "Деинсталиране като администратор", "Uninstall canceled by the user!": "Деинсталацията е отменена от потребителя!", "Uninstall failed": null, "Uninstall options": null, "Uninstall package": "Деинсталиране на пакета", "Uninstall package, then reinstall it": "Деинсталиране на пакета, и тогава инсталиране наново", "Uninstall package, then update it": "Деинсталиране на пакета, и тогава актуализиция", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Деинсталиране на избраните пакети", "Uninstall selection": null, "Uninstall succeeded": null, "Uninstall the selected packages with administrator privileges": "Деинсталиране на избраните пакети с администраторски права", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": null, "Unknown": "Неизвестна", "Unknown size": null, "Unset or unknown": null, "Up to date": null, "Update": "Актуализи<PERSON>а<PERSON>е", "Update WingetUI automatically": "Автоматично актуализиране на WingetUI", "Update all": "Актуализиране на всички", "Update and more": null, "Update as administrator": "Актуализиране като администратор", "Update check frequency, automatically install updates, etc.": null, "Update date": "От дата", "Update failed": null, "Update found!": "Намерена е актуализация!", "Update now": null, "Update options": null, "Update package indexes on launch": null, "Update packages automatically": "Автоматично актуализиране на пакети", "Update selected packages": "Актуализиране на избраните пакети", "Update selected packages with administrator privileges": null, "Update selection": null, "Update succeeded": null, "Update to version {0}": null, "Update to {0} available": "Налична е актуализация до {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "Актуализации", "Updates available!": "Има актуализации!", "Updates for this package are ignored": "Актуализациите за този пакет се игнорират", "Updates found!": "Намерени са актуализации!", "Updates preferences": null, "Updating WingetUI": "Актуализиране на WingetUI", "Url": null, "Use Legacy bundled WinGet instead of PowerShell CMDLets": null, "Use a custom icon and screenshot database URL": null, "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Използвай инсталирания GSudo вместо вградения (изисква рестартиране на приложението)", "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": null, "Use system Winget (Needs a restart)": "Използване на системния Winget (изисква рестартиране)", "Use system Winget (System language must be set to english)": null, "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": null, "User": "Потребител", "User interface preferences": "Настройки на потребителския интерфейс", "User | Local": null, "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": null, "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": "Версия", "Version to install:": "Версия за инсталиране:", "Version:": null, "View GitHub Profile": null, "View WingetUI on GitHub": "Посетете страницата на WingetUI в GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": null, "View mode:": null, "View on UniGetUI": null, "View page on browser": "Отваряне на страницата в браузър", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "Изчаква се завършването на другите инсталации...", "Waiting for {0} to complete...": null, "Warning": "Внимание", "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": null, "We could not load detailed information about this package, because it was not installed from an available package manager.": null, "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": null, "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": null, "We couldn't find any package": null, "Welcome to WingetUI": "Добре дошли в WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Кои мениджъри за пекети искате да използвате?", "Which source do you want to add?": "Кой източник искате да добавите?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": null, "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": null, "WingetUI - Everything is up to date": "WingetUI - Всичко е актуално", "WingetUI - {0} updates are available": "WingetUI - налични са {0} актуализации", "WingetUI - {0} {1}": "WingetUI - {1} на {0}", "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": null, "WingetUI Log": null, "WingetUI Repository": null, "WingetUI Settings": "WingetUI настройки", "WingetUI Settings File": "Файл с настройки на WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": null, "WingetUI autostart behaviour, application launch settings": "Поведение при автоматично стартиране на WingetUI, настройки при стартиране на приложения", "WingetUI can check if your software has available updates, and install them automatically if you want to": null, "WingetUI display language:": "WingetUI език:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": null, "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": null, "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI не е машинно преведен. Следните потребители са помогнали при превеждането:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI се актуализира. Когато това приключи, WingetUI ще се рестартира сам.", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": null, "WingetUI log": "WingetUI лог", "WingetUI tray application preferences": "WingetUI настройки на областта за уведомления", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": null, "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": null, "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": null, "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI не би бил възможен без помощта на нашите скъпи сътрудници. Разгледайте техните профили в GitHub, WingetUI не би бил възможен без тях!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": null, "WingetUI {0} is ready to be installed.": null, "Write here the process names here, separated by commas (,)": null, "Yes": "Да", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": null, "You have installed WingetUI Version {0}": null, "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": "Можете да рестартирате компютъра си и по-късно, ако желаете", "You will be prompted only once, and administrator rights will be granted to packages that request them.": null, "You will be prompted only once, and every future installation will be elevated automatically.": null, "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": "купите кафе", "extracted": null, "feature": null, "formerly WingetUI": null, "homepage": "уебс<PERSON>йт", "install": "инсталиране", "installation": "инсталация", "installed": "инсталир<PERSON>н", "installing": "инсталиране", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "деинсталиране", "uninstallation": "деинсталация", "uninstalled": "деинста<PERSON><PERSON><PERSON><PERSON>н", "uninstalling": "деинсталиране", "update(noun)": "актуализация", "update(verb)": "актуализация", "updated": "актуа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updating": "актуализира<PERSON>е", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "Деинсталиране на {0}", "{0} aborted": null, "{0} can be updated": "{0} могат да се актуализират", "{0} can be updated to version {1}": null, "{0} days": "{0} дена", "{0} desktop shortcuts created": null, "{0} failed": "Неус<PERSON><PERSON><PERSON><PERSON> {0}", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} часа", "{0} installation": "Инсталиране на {0}", "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} се актуализира", "{0} is being updated to version {1}": null, "{0} is disabled": "{0} е деактивиран", "{0} minutes": "{0} минути", "{0} months": null, "{0} packages are being updated": "{0} пакета се актуализират", "{0} packages can be updated": "{0} пакета могат да се актуализират", "{0} packages found": "Намерени са {0} пакета", "{0} packages were found": "{0} пакета са намерени", "{0} packages were found, {1} of which match the specified filters.": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "Усп<PERSON><PERSON><PERSON> {0}", "{0} update": "Актуализиране на {0}", "{0} updates are available": null, "{0} was {1} successfully!": "{0} е {1} успешно!", "{0} weeks": null, "{0} years": null, "{0} {1} failed": "Неуспешна {1} на {0}", "{package} Installation": null, "{package} Uninstall": null, "{package} Update": null, "{package} could not be installed": null, "{package} could not be uninstalled": null, "{package} could not be updated": null, "{package} installation failed": null, "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": null, "{package} update failed": null, "{package} update failed. Click here for more details.": null, "{package} was installed successfully": null, "{package} was uninstalled successfully": null, "{package} was updated successfully": null, "{pcName} installed packages": null, "{pm} could not be found": "{pm} не могат да се намерят", "{pm} found: {state}": null, "{pm} is disabled": null, "{pm} is enabled and ready to go": null, "{pm} package manager specific preferences": null, "{pm} preferences": "{pm} предпочитания", "{pm} version:": null, "{pm} was not found!": null}