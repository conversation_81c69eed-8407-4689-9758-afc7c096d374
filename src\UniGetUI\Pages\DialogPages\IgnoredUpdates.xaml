<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Interface.IgnoredUpdatesManager"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Interface"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    MaxWidth="1100"
    MaxHeight="500"
    mc:Ignorable="d">

    <Grid
        HorizontalAlignment="Stretch"
        ColumnSpacing="8"
        RowSpacing="8">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" MaxWidth="200" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" MinHeight="100" />
        </Grid.RowDefinitions>
        <widgets:TranslatedTextBlock Text="The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates." />
        <Button
            Grid.Column="2"
            MaxWidth="200"
            HorizontalAlignment="Right">
            <widgets:TranslatedTextBlock Text="Reset list" />
            <Button.Flyout>
                <widgets:BetterFlyout
                    x:Name="ConfirmResetFlyout"
                    LightDismissOverlayMode="Off"
                    Placement="Bottom">
                    <Grid
                        Width="300"
                        ColumnSpacing="8"
                        RowSpacing="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.ColumnSpan="2"
                            Margin="0,0,0,0"
                            Text="Do you really want to reset the ignored updates list? This action cannot be reverted"
                            TextWrapping="WrapWholeWords" />
                        <Button
                            Name="NoResetButton"
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Stretch"
                            Click="NoResetButton_Click"
                            Style="{ThemeResource AccentButtonStyle}">
                            <widgets:TranslatedTextBlock Text="No" />
                        </Button>
                        <Button
                            Name="YesResetButton"
                            Grid.Row="1"
                            Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            Click="YesResetButton_Click">
                            <widgets:TranslatedTextBlock Text="Yes" />
                        </Button>
                    </Grid>
                </widgets:BetterFlyout>
            </Button.Flyout>
        </Button>
        <ItemsView
            Name="IgnoredUpdatesList"
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Padding="2,4,2,4"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="{ThemeResource ApplicationPageBackgroundThemeBrush}"
            CornerRadius="6"
            SelectionMode="None">
            <ItemsView.Layout>
                <StackLayout Orientation="Vertical" Spacing="4" />
            </ItemsView.Layout>
            <ItemsView.ItemTemplate>
                <DataTemplate x:DataType="local:IgnoredPackageEntry">
                    <ItemContainer>
                        <Grid Padding="8,0" ColumnSpacing="4">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" MaxWidth="120" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" MaxWidth="170" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" MaxWidth="90" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <widgets:LocalIcon
                                Grid.Column="0"
                                Width="24"
                                Height="24"
                                Icon="package" />
                            <TextBlock
                                Grid.Column="1"
                                VerticalAlignment="Center"
                                Text="{x:Bind Name}"
                                ToolTipService.ToolTip="{x:Bind Name}" />
                            <widgets:LocalIcon
                                Grid.Column="2"
                                Width="24"
                                Height="24"
                                Icon="Id" />
                            <TextBlock
                                Grid.Column="3"
                                VerticalAlignment="Center"
                                Text="{x:Bind Id}"
                                ToolTipService.ToolTip="{x:Bind Id}" />
                            <widgets:LocalIcon
                                Grid.Column="4"
                                Width="24"
                                Height="24"
                                Icon="version" />
                            <TextBlock
                                Grid.Column="5"
                                VerticalAlignment="Center"
                                Text="{x:Bind Version}"
                                ToolTipService.ToolTip="{x:Bind Version}" />
                            <widgets:LocalIcon
                                Grid.Column="6"
                                Width="24"
                                Height="24"
                                Icon="Upgradable" />
                            <TextBlock
                                Grid.Column="7"
                                VerticalAlignment="Center"
                                Text="{x:Bind NewVersion}"
                                ToolTipService.ToolTip="{x:Bind NewVersion}" />
                            <widgets:LocalIcon
                                Grid.Column="8"
                                Width="24"
                                Height="24"
                                Icon="{x:Bind Manager.Properties.IconId}" />
                            <TextBlock
                                Grid.Column="9"
                                VerticalAlignment="Center"
                                Text="{x:Bind Manager.DisplayName}"
                                ToolTipService.ToolTip="{x:Bind Manager.DisplayName}" />
                            <Button
                                Grid.Column="10"
                                Width="32"
                                Height="32"
                                Padding="0"
                                Click="{x:Bind RemoveFromIgnoredUpdates}">
                                <FontIcon FontSize="16" Glyph="&#xE74D;" />
                            </Button>
                        </Grid>
                    </ItemContainer>
                </DataTemplate>
            </ItemsView.ItemTemplate>
        </ItemsView>
        <widgets:DialogCloseButton
            Grid.Column="1"
            Margin="0,-63,-24,0"
            Click="CloseButton_Click" />
    </Grid>
</Page>
