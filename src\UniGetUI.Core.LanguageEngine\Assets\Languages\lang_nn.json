{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(Sist sjekka: {0})", "(Number {0} in the queue)": "(Nummer {0} i køen)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@yrjarv", "0 packages found": "0 pakkar funne", "0 updates found": "0 oppdateringar funne", "1 - Errors": "1 - Feilmeldingar", "1 day": "1 dag", "1 hour": "1 time", "1 month": null, "1 package was found": "1 pakke vart funne", "1 update is available": "1 oppdatering er tilgjengeleg", "1 week": "1 veke", "1 year": null, "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": "2 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": "3 - In<PERSON><PERSON><PERSON> (mindre)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": "4 - In<PERSON><PERSON><PERSON> (meir)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": "5 - Inform<PERSON><PERSON> (feilsøking)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": null, "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Eit repository fullt av verktøy og programmar desingna for Microsoft sitt .NET-økosystem.<br>Inneheld: <b>.NET-relaterte verktøy og skript</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Eit repository fylt med verktøy, designa med Microsoft sitt .NET-økosystem i tankane.<br>Inneheld: <b>.NET-relaterte verktøy</b>", "A restart is required": "Omstart av maskina krevst", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "Om", "About Qt6": "Om Qt6", "About WingetUI": "Om WingetUI", "About WingetUI version {0}": "Om WingetUI versjon {0}", "About the dev": "Om utveklaren", "Accept": null, "Action when double-clicking packages, hide successful installations": "Handling ved dobbelk<PERSON>k på pakkar, sk<PERSON><PERSON> ferdige installasjonar", "Add": "Legg til", "Add a source to {0}": "Legg til ein kjelde til {0}", "Add a timestamp to the backup file names": "<PERSON>gg til eit tidsstempel til backup-filnamna", "Add a timestamp to the backup files": "<PERSON>gg til eit tidsstempel til backup-filane", "Add packages or open an existing bundle": "Legg til pakkar eller opne ein eksisterande bundle", "Add packages or open an existing package bundle": "Legg til pakkar eller opne ein eksisterande bundle", "Add packages to bundle": null, "Add packages to start": "<PERSON><PERSON> til pakkar for å starte", "Add selection to bundle": "Legg til utval til bundlen", "Add source": "Legg til kjelde", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": "Legg til kjelde {source} til {manager}", "Addition succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> tillegging", "Administrator privileges": "Administrator<PERSON><PERSON>", "Administrator privileges preferences": "Preferansar for adminstaratorrettar", "Administrator rights": "Administrator<PERSON><PERSON>", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "Alle filar", "All versions": "Alle versjonar", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Tillat at pakkehandlingar skjer parallelt", "Allow parallel installs (NOT RECOMMENDED)": "Tillat parallelle installasjonar (IKKJE ANBEFALT)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Tillat at {pm}-operasjonar kan køyre i parallell", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": "Alltid auk tillatingsnivået for {pm}-installasjonar", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON> kø<PERSON> {pm}-operasjonar med administratorrettar", "An error occurred": "Ein feil oppstod", "An error occurred when adding the source: ": "Ein feil oppstod då kjelden vart lagt til:", "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": "Ein feil oppstod ved sjekking for oppdateringar", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "Ein feil oppstod i behandlinga av denne pakken", "An error occurred:": "Ein feil oppstod:", "An interal error occurred. Please view the log for further details.": "Ein intern feil oppstod. Venlegst sjå loggen for fleire detaljar.", "An unexpected error occurred:": "Ein uventa feil oppstod:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": "Ein oppdatering vart funnen!", "Android Subsystem": "Android Subsystem", "Another source": "<PERSON><PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": null, "App Name": "Appnamn", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "Applikasjonstema:", "Apply": null, "Architecture to install:": "Arkitektur som blir installert:", "Are these screenshots wron or blurry?": "Er desse skjermbilda feil eller uklare?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "<PERSON>r du sikker?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "<PERSON><PERSON><PERSON><PERSON> etter administratorrettar ein gong per gruppe operasjonar", "Ask for administrator rights when required": "<PERSON><PERSON><PERSON><PERSON> om <PERSON><PERSON><PERSON> ved behov", "Ask once or always for administrator rights, elevate installations by default": "<PERSON><PERSON><PERSON>r ein gong eller alltid etter administratorrettar, auk tillatingsnivået som standard", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "<PERSON><PERSON> spør om administratortilgang ein gong (ikkje anbefalt)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": "Oppmerksemd krevst", "Authenticate to the proxy with an user and a password": null, "Author": "Forfattar", "Automatic desktop shortcut remover": null, "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Lagre ei liste over alle dine installerte pakkar automatisk for å forenkle gjenoppretting av dei.", "Automatically save a list of your installed packages on your computer.": "Lagre ei liste over dine installerte pakkar på datamaskina di automatisk", "Autostart WingetUI in the notifications area": "Start WingetUI automatisk i varslingsfeltet", "Available Updates": "Tilgjengelege oppdateringar", "Available updates: {0}": "Tilgjengelege oppdateringar: {0}", "Available updates: {0}, not finished yet...": "Tilgjengelege oppdateringar: {0}, jobbar framleis...", "Backing up packages to GitHub Gist...": null, "Backup": "Ta backup", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Sikkerheitskopier installerte pakkar", "Backup location": null, "Become a contributor": "<PERSON><PERSON> ein bidrag<PERSON>", "Become a translator": "<PERSON><PERSON> ein omsettar", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Beta-funksjonalitet og andre innstillingar som ikkje må rørast", "Both": "<PERSON><PERSON><PERSON>", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Men her er andre ting du kan gjere for å lære enda meir om WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Ved å deaktivere ein pakkehandterar kjem du ikkje lenger til å kunne sjå eller oppdatere pakkane hans", "Cache administrator rights and elevate installers by default": "Bufre administratorrettar og gjev installasjonsprogrammar administratorrettar automatisk", "Cache administrator rights, but elevate installers only when required": "<PERSON><PERSON><PERSON> administrator<PERSON><PERSON>, men berre gjev installasjonsprogrammar administrator<PERSON><PERSON> når det er naudsynt", "Cache was reset successfully!": "Buffer var vorte nullstilt!", "Can't {0} {1}": "Kan ikke {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": null, "Change backup output directory": "<PERSON><PERSON> mappa for sikkerheitskopien", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "<PERSON><PERSON> korleis UniGetUI sjekkar for og installerar tilgjengelege oppdateringar for pakkane dine", "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": "<PERSON><PERSON> for installasjon", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Sjekk etter pakkoppdateringar med jamne mello<PERSON>rom", "Check for updates": null, "Check for updates every:": "<PERSON><PERSON><PERSON> etter oppdateringar kvar:", "Check for updates periodically": "<PERSON><PERSON><PERSON> etter nye oppdateringar med jamne mellomrom", "Check for updates regularly, and ask me what to do when updates are found.": "Sjekk etter oppdateringar regelmesseg og spør meg kva jeg vil gjere når oppdateringar er funne.", "Check for updates regularly, and automatically install available ones.": "Sjekk for oppdateringar jamnleg, og installer tilgjengelege oppdateringar automatisk.", "Check out my {0} and my {1}!": "Sjekk ut min {0} og min {1}!", "Check out some WingetUI overviews": "Sjekk ut nokre WingetUI-oversikter", "Checking for other running instances...": "<PERSON><PERSON><PERSON> etter andre kjø<PERSON>e instansar...", "Checking for updates...": "<PERSON><PERSON><PERSON> etter opp<PERSON>...", "Checking found instace(s)...": "<PERSON><PERSON><PERSON><PERSON> op<PERSON> instans(ar)...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "<PERSON><PERSON><PERSON> val", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": "<PERSON><PERSON>-cachen - WingetUI", "Clearing Scoop cache...": "<PERSON><PERSON><PERSON><PERSON> sin buffer...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Trykk på \"Installer\" for å starte installasjonsprosessen. Viss du hoppar over installasjonen, kan det hende at UniGetUI ikkje fungerar som forventa.", "Close": "Lukk", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Minimer WingetUI til systemstatusfeltet", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "Kommandolinje-output", "Command-line to run:": null, "Compare query against": "Samanlikn spørring mot", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Komponentinformasjon", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": "Fortsett", "Contribute to the icon and screenshot repository": "Bidra til ikon- og skjembildearkivet", "Contributors": "Bidragsytare", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Ko<PERSON>r til utklippstavla", "Could not add source": null, "Could not add source {source} to {manager}": "<PERSON><PERSON> ikkje legge til kjelde {source} til {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": "Kunne ikkje laste inn annonseringar -", "Could not load announcements - HTTP status code is $CODE": "Kunne ikkje laste inn annonseringar - HTTP-statuskoden er $CODE", "Could not remove source": null, "Could not remove source {source} from {manager}": "<PERSON><PERSON> ikkje fjerne kje<PERSON> {source} fr<PERSON> {manager}", "Could not remove {source} from {manager}": null, "Create .ps1 script": null, "Credentials": null, "Current Version": "Nåværande versjon", "Current status: Not logged in": null, "Current user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> brukar", "Custom arguments:": "Tilpassa parameter:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Tilpassa kommandolinje-parametrar:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Tilpass WingetUI - berre for hackarar og avanserte brukarar", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ANSVARSFRÅSKRIVING: VI ER IKKJE ANSVARLEGE FOR DEI NEDLASTA PAKKANE, VENLEGST BERRE INSTALLER PROGRAMVARE DU STOLAR PÅ.", "Dark": "<PERSON><PERSON><PERSON>", "Decline": null, "Default": "Standard", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Standardpreferansar - tilpassa normale brukarar", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "Beskriving:", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Utve<PERSON> er vanskeleg  og dette programmet er gradis, men viss du liker det kan du alltids <b>spandere ein kaffi på meg</b>", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installer direkte ved å dobbeltklikke på eit element under fana \"{discoveryTab}\" (i staden for å¨vise pakkeinformasjon)", "Disable new share API (port 7058)": "Deaktiver det nye delings-APIet (port 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": "Ansvarsfråskriving", "Discover Packages": "Utforsk pakkar", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "Skill mellom store og små bokstavar", "Distinguish between uppercase and lowercase": "Skilj mellom store og små bokstavar", "Do NOT check for updates": "IKKJE søk etter oppdateringar", "Do an interactive install for the selected packages": "Utf<PERSON><PERSON> ein interaktiv installasjon for den valte pakka", "Do an interactive uninstall for the selected packages": "Utfør ein interaktiv avinstallasjon for den valte pakka", "Do an interactive update for the selected packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> ein interaktiv oppdatering for den valte pakka", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "<PERSON><PERSON><PERSON><PERSON> last ned nye oversettingar av appen automatisk frå GitHub", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": "Ikkje fjern suksessfulle operasjonar automatisk frå lista", "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "Ikkje oppdater pakkeindeksar ved oppstart", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Synest du at WingetUI er nyttig? Viss du kan, vil du kanskje støtte arbeidet mitt, så eg kan fortsetje med å gjere WingetUI til det ultimate pakkehandterargrensesnittet.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Synest du at WingetUI er nyttig? Ynskjer du å støtte utveklaren? I so fall kan du {0}, det hjelper masse!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": "Vil du verkeleg avinstallere føljande {0} pakkar?", "Do you really want to uninstall {0} packages?": "Vil du verkeleg avinstallere {0} pakkar?", "Do you really want to uninstall {0}?": "Vil du verkeleg avinstallere {0}?", "Do you want to restart your computer now?": "Vil du starte datamaskina på nytt no?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Vil du omsette WingetUI til ditt språk? Sj<PERSON> korleis du kan bidra <a style=\"color:{0}\" href=\"{1}\"a>Her!<\\a> (PS: Viss du klarar å finne feil i nynorsk-omsettinga må du gjerne også hjelpe til!)", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Har du ikkje lyst til å donere? Null stress, du kan alltid dele WingetUI med vennane dine. Sprei ordet om WingetUI.", "Donate": "<PERSON><PERSON><PERSON>", "Done!": null, "Download failed": null, "Download installer": "Last ned installasjonsprogram", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Nedlasting var suksessfull", "Download updated language files from GitHub automatically": "Last ned oppdaterte språkfilar frå GitHub automatisk", "Downloading": "<PERSON><PERSON> ned", "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "Lastar ned metadata for pakka...", "Enable Scoop cleanup on launch": "Aktiver Scoop-cleanup (nullstilling av buffer) ved oppstart", "Enable WingetUI notifications": "Aktiver varslingar frå WingetUI", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktiver bakgrunns-API-et (WingetUI Widgets and Sharing, port 7058)", "Enable it to install packages from {pm}.": "Aktiver det for å installere pakkar frå {pm}", "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Aktiver {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "<PERSON><PERSON>", "Everything is up to date": "Alt er oppdatert", "Exact match": "Eksakt match", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": "<PERSON><PERSON><PERSON>", "Experimental settings and developer options": "Eksperimentelle innstillingar og val for utveklare", "Export": "Eksporter", "Export log as a file": "Eksporter logg som ei fil", "Export packages": "Eksporter pakkar", "Export selected packages to a file": "Eksporter valte pakkar til ei fil", "Export settings to a local file": "Eksporter innstillingar til ei lokal fil", "Export to a file": "Eksporter til ei fil", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "<PERSON><PERSON><PERSON> siste an<PERSON>, venlegst vent...", "Filters": "Filtrar", "Finish": "<PERSON><PERSON><PERSON>", "Follow system color scheme": "<PERSON><PERSON><PERSON><PERSON>", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Bruk ARM-kompilert versjon av winget (BERRE FOR ARM64-SYSTEM)", "Formerly known as WingetUI": "Tidlegare kjend som WingetUI", "Found": "<PERSON><PERSON>", "Found packages: ": "<PERSON><PERSON>:", "Found packages: {0}": "<PERSON><PERSON> pakkar: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON> pakkar: {0}, jobbar framleis...", "General preferences": "<PERSON><PERSON><PERSON>illinga<PERSON>", "GitHub profile": "GitHub-profil", "Global": "Global", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Flott repository med ukjende men nyttige verktøy og andre interessante pakkar.<br>Inneheld:<b><PERSON><PERSON><PERSON><PERSON><PERSON>, kom<PERSON><PERSON><PERSON><PERSON><PERSON>ram, generell programvare (ekstra buckets krevst)</b>", "Great! You are on the latest version.": null, "Grid": null, "Help": "<PERSON><PERSON><PERSON><PERSON>", "Help and documentation": "Hjelp og dokumentasjon", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, mitt navn er Martí, og eg er <i>utveklaren</i> som står bak WingetUI. WingetUI har utelukkande vore utvekla på fritida mi!", "Hide details": "<PERSON><PERSON><PERSON><PERSON>", "Homepage": "Heimeside", "Hooray! No updates were found.": "Hurra! Ingen oppdateringar funne!", "How should installations that require administrator privileges be treated?": "Kva skal gjerast med installasjonar som krev administratorrettar?", "How to add packages to a bundle": null, "I understand": "<PERSON><PERSON>", "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Ignorer framtidige oppdateringar for denne pakka", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "Ignorer valte pakkar", "Ignore special characters": "Ignorer spesialtekn", "Ignore updates for the selected packages": "Ignorer oppdateringar for de valte pakkane", "Ignore updates for this package": "Ignorer oppdateringar til denne pakka", "Ignored updates": "Ignorerte oppdateringar", "Ignored version": "Ignorert versjon", "Import": "Importer", "Import packages": "Importer pakkar", "Import packages from a file": "Importer pakkar frå ei fil", "Import settings from a local file": "Eksporter innstillingar frå ei lokal fil", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "Initialiserar WingetUI...", "Install": "Installer", "Install Scoop": "Installer Scoop", "Install and more": null, "Install and update preferences": null, "Install as administrator": "Installer som administrator", "Install available updates automatically": "Installer tilgjengelege oppdateringar automatisk", "Install location can't be changed for {0} packages": null, "Install location:": "Installasjonsplassering:", "Install options": null, "Install packages from a file": "Installer pakkar frå ei fil", "Install prerelease versions of UniGetUI": null, "Install script": null, "Install selected packages": "Installer valte pakkar", "Install selected packages with administrator privileges": "Installer valte pakkar med administratorrettar", "Install selection": "Installer utval", "Install the latest prerelease version": "Installer siste forhandsutgivingsversjon", "Install updates automatically": "Installer oppdateringar automatisk", "Install {0}": null, "Installation canceled by the user!": "Installasjon avbroten av brukar!", "Installation failed": "Installasjon feilet", "Installation options": "Installasjonsval", "Installation scope:": "Installasjonsomfang:", "Installation succeeded": "Installasjon fullført", "Installed Packages": "Installerte pakkar:", "Installed Version": "Installer versjon", "Installed packages": null, "Installer SHA256": "Installer SHA256", "Installer SHA512": "Installer SHA512", "Installer Type": "Type installasjonsprogram", "Installer URL": "URL til installasjonsprogram", "Installer not available": "Installerar ikkje tilgjengeleg", "Instance {0} responded, quitting...": "Instans {0} ga svar, avsluttar...", "Instant search": "Hurtigsøk", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "Interaktiv installasjon", "Interactive operation": null, "Interactive uninstall": "Interaktiv avinstallering", "Interactive update": "Interaktiv oppdatering", "Internet connection settings": null, "Is this package missing the icon?": "Manglar ikonet for denne pakka?", "Is your language missing or incomplete?": "Manglar språket ditt eller er det uferdig? (viss språket ditt er bokmål eller nynorsk jobbar eg med saka)", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Det ser ut som om du kjørte WingetUI som administrator, som ikkje anbefalast. Du kan framleis bruke programmet, men vi anbefalar sterkt å ikkje kjøre WingetUI med administratorrettar. Klikk på \"{showDetails}\" for å vise grunnen til det.", "Language": null, "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tema, og diverse andre preferansar", "Last updated:": "Sist oppdatert:", "Latest": "<PERSON><PERSON>", "Latest Version": "<PERSON>ste versjon", "Latest Version:": "Siste versjon:", "Latest details...": "<PERSON>ste de<PERSON>...", "Launching subprocess...": "Startar subprosess...", "Leave empty for default": "La stå tomt for standardvalet", "License": "<PERSON><PERSON><PERSON>", "Licenses": "Lisensar", "Light": "Lys", "List": null, "Live command-line output": "Direkte output frå kommandolinja", "Live output": "Direkte output", "Loading UI components...": "Lastar UI-komponentar...", "Loading WingetUI...": "Lastar inn WingetUI...", "Loading packages": "Lastar inn pakkar", "Loading packages, please wait...": "<PERSON><PERSON> p<PERSON>, venlegst vent...", "Loading...": "Lastar...", "Local": "<PERSON><PERSON>", "Local PC": "Lokal PC", "Local backup advanced options": null, "Local machine": "Lokal maskin", "Local package backup": null, "Locating {pm}...": "Finnar {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "Loggnivå:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Ser etter pakkar...", "Machine | Global": "Maskin | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "Behandle autostartåtferd for WingetUI frå innstillingsappen", "Manage ignored packages": "<PERSON><PERSON><PERSON> pakkar", "Manage ignored updates": "Behandle ignorerte oppdateringar", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": "Behandle {0} kjelder", "Manifest": "Konfigurasjonsfil", "Manifests": "Konfigurasjonsfilar", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoft sin offisielle pakkehandterar. Full av velkjende og verifiserte pakkar<br>Inneheld: <b><PERSON><PERSON> programvare, Microsoft Store-appar</b>", "Missing dependency": "Manglar avhengigheit", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON><PERSON>", "More details about the shared data and how it will be processed": null, "More info": "<PERSON><PERSON>", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "<PERSON><PERSON>", "New": null, "New Version": "<PERSON><PERSON> versjon", "New bundle": "Ny bundle", "New version": "<PERSON><PERSON> versjon", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "Ingen pakkar funne", "No packages found matching the input criteria": "<PERSON>n ingen pakkar som samsvarar med søkekriteriane", "No packages have been added yet": "Ingen pakkar er lagt til enda", "No packages selected": "Ingen pakkar er valt", "No packages were found": "Ingen pakkar vart funne", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": "Ingen resultatar som matcha kriteria vart funne", "No sources found": "Ingen kjelder vart funne", "No sources were found": "Ingen kjelder har vorte funne", "No updates are available": "<PERSON>gen oppdate<PERSON>r <PERSON>", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS sin pakkehandterer. Full av bibliotek og andre verktøy som svevar rundt i JavaScript-universet<br>Inneheld: <b>Node.js-bibliotek og andre relaterte verktøy</b>", "Not available": "<PERSON><PERSON><PERSON><PERSON>", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "<PERSON><PERSON><PERSON><PERSON>", "Not right now": "Ikkje akkurat no", "Notes:": "Notatar:", "Notification preferences": "<PERSON><PERSON><PERSON>st<PERSON><PERSON><PERSON>", "Notification tray options": "Innstillingar for varslingsområdet:", "Notification types": null, "NuPkg (zipped manifest)": "NuPkg (zippa manifest)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON><PERSON>", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": "Opne WingetUI", "Open backup location": "<PERSON><PERSON><PERSON> backup-<PERSON><PERSON><PERSON><PERSON>", "Open existing bundle": "Opne eksisterande bundle", "Open install location": null, "Open the welcome wizard": "Åpne velkomstveiledaren", "Operation canceled by user": "Operasjon avbroten av brukar", "Operation cancelled": "Handling avbroten", "Operation history": "Handlingshistorikk", "Operation in progress": "Handlingar som utførast", "Operation on queue (position {0})...": "Handlinga er i køen (posisjon {0})...", "Operation profile:": null, "Options saved": "Inn<PERSON><PERSON><PERSON><PERSON> lagra", "Order by:": null, "Other": "<PERSON>", "Other settings": null, "Package": null, "Package Bundles": "Pakkebundlar", "Package ID": "Pakke-ID", "Package Manager": "Pak<PERSON><PERSON><PERSON><PERSON>", "Package Manager logs": "Pakkehandteringsloggar", "Package Managers": "Pakkehandterare", "Package Name": "Pakkenamn", "Package backup": null, "Package backup settings": null, "Package bundle": "Pakkebundle", "Package details": "<PERSON><PERSON><PERSON><PERSON>", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "Innstillingar for pakkehandterar", "Package managers": null, "Package not found": "Pakke vart ikkje funne", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "<PERSON><PERSON>", "Packages found: {0}": "<PERSON><PERSON> funne: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "Lim inn ein gyldig URL til databasen", "Pause updates for": null, "Perform a backup now": "Ut<PERSON>ør ein si<PERSON>its<PERSON>pi no", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, venlegst vent...", "Periodically perform a backup of the installed packages": "Ut<PERSON>ør ein sikkerheitskopi av dei installerte pakkane med jamne mellamrom", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": "Venlegst klikk på \"Fortsett\" for å fortsette", "Please enter at least 3 characters": "Venlegst skriv inn minst 3 tekn", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Venlegst vær oppm<PERSON>ks<PERSON> på at enkelte pakkar kan hende at ikkje er installerbare, på grunn av pakkehåndterarane som er aktivert på denne maskina.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Venlegst vær oppmerks<PERSON> på at pakkar frå enkelte kjelder kan være at ikkje er eksporterbare. Dei er gråa ut, og kjem ikkje til å verte eksportert.", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Venlegst sjå på kommandolinje-outputen eller handlingshistorikken for meir informasjon om problemet.", "Please select how you want to configure WingetUI": "Venlegst vel korleis du vil konfigurere WingetUI", "Please try again later": null, "Please type at least two characters": "Venlegst tast minst to tekn", "Please wait": "Venlegst vent", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "Venlegst vent...", "Portable": "Portabel", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell sin pakkehandterar. Finn bibliotek og skript for å utvide PowerShell sine evnar<br>Inneheld: <b><PERSON><PERSON><PERSON>, skript, cmdlets</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "Forhandsutgiving", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON>, venlegst vent...", "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "Publiseringsdato:", "Publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python sin pakkehandterer. Full av bibliotek og andre Python-relaterte verktøy<br>Inneheld: <b>Python-bibliotek og andre relaterte verktøy</b>", "Quit": "Lukk", "Quit WingetUI": "Avslutt WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": "Installer pakke på nytt", "Related settings": null, "Release notes": "Utgivingsnotatar", "Release notes URL": "URL for utgivingsnotatar", "Release notes URL:": "URL til utgivingsnotatar:", "Release notes:": "Utgivingsnotatar:", "Reload": "Last inn på ny", "Reload log": "Last inn logg på ny", "Removal failed": "<PERSON><PERSON><PERSON> feila", "Removal succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> fjerning", "Remove from list": "<PERSON><PERSON><PERSON> frå liste", "Remove permanent data": "Fjern permanente data", "Remove selection from bundle": "<PERSON><PERSON><PERSON> utval frå bundle", "Remove successful installs/uninstalls/updates from the installation list": "<PERSON>jern vellykka installasjonar/avinstallasjonar/oppdateringar frå installasjonslista", "Removing source {source}": null, "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON> {source} fr<PERSON> {manager}", "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": "Si frå om ein feil eller send inn forslag til nye funksjonar", "Repository": "Repository", "Reset": "Tilbakestill", "Reset Scoop's global app cache": "TIlbakestill Scoop sin globale app-buffer", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "Tilbakestil<PERSON> Winget-kjelder (kan hjelpe viss ingen pakkar visast)", "Reset WingetUI": "Tilbakestill WingetUI", "Reset WingetUI and its preferences": "Tilbakestill WingetUI med tilhørande innstillingar", "Reset WingetUI icon and screenshot cache": "Tilbakestill WingetUI-ikonet og bufra skjermbildar", "Reset list": null, "Resetting Winget sources - WingetUI": "Nullstillar Wing<PERSON>-k<PERSON>lder - WingetUI", "Restart": null, "Restart UniGetUI": "Restart UniGetUI", "Restart WingetUI": "Start WingetUI på ny", "Restart WingetUI to fully apply changes": "Start WingetUI på ny for at alle endringane skal bli brukt", "Restart later": "Start på ny seinare", "Restart now": "Start på ny no", "Restart required": "Omstart krevst", "Restart your PC to finish installation": "Start PC-en på ny for å fullføre installasjonen", "Restart your computer to finish the installation": "Start datamaskina på ny for å fullføre installasjonen", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "<PERSON><PERSON><PERSON><PERSON> ig<PERSON>n", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": "<PERSON><PERSON><PERSON><PERSON> på ny, venlegst vent...", "Return to top": "Til toppen", "Run": "<PERSON><PERSON><PERSON>", "Run as admin": "Køyr som administrator", "Run cleanup and clear cache": "Køyr opprensing og fjern buffer", "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "K<PERSON><PERSON>r installasjonsprogrammet...", "Running the uninstaller...": "Køyrar avinstallasjonsprogrammet", "Running the updater...": "K<PERSON><PERSON>r oppdateringsprogrammet..", "Save": null, "Save File": "Lagre fil", "Save and close": "Lagre og lukk", "Save as": null, "Save bundle as": "Lagre bundle som", "Save now": "Lagre no", "Saving packages, please wait...": "<PERSON><PERSON><PERSON>, venlegst vent...", "Scoop Installer - WingetUI": "Scoop-installasjonsprogram - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop-avinstallasjonsprogram - WingetUI", "Scoop package": "Skoop-pakke", "Search": "<PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "<PERSON><PERSON><PERSON> etter sk<PERSON>programvare, <PERSON><PERSON><PERSON> meg når oppdateringar er tilgjengelege og ikkje gjer nerdete ting. Eg vil ikkje at WingetUI skal overkomplisere, eg vil berre ha ein enkel <b>programvarebehandlar</b>", "Search for packages": "<PERSON><PERSON><PERSON> etter pakkar", "Search for packages to start": "<PERSON><PERSON><PERSON> etter ein eller fleire pakkar for å starte", "Search mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search on available updates": "<PERSON><PERSON><PERSON> etter tilgjengelege oppdateringar", "Search on your software": "<PERSON><PERSON><PERSON> i din programvare", "Searching for installed packages...": "<PERSON><PERSON><PERSON> etter installerte pakkar...", "Searching for packages...": "<PERSON><PERSON><PERSON> etter pakkar...", "Searching for updates...": "<PERSON><PERSON><PERSON> etter opp<PERSON>...", "Select": "<PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Velj \"{item}\" for å legge til i din tilpassa bucket", "Select a folder": "Velj ei mappe", "Select all": "<PERSON><PERSON><PERSON> alle", "Select all packages": "<PERSON><PERSON><PERSON> alle pakkar", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON> velj <b>viss du veit kva du gjer</b>", "Select package file": "<PERSON><PERSON><PERSON>", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "Velj kjelden du ynskjer å legge til:", "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON> kva for <b>pakkebehandlare</b> som skal verte brukt ({0}), konfigurer korleis pakkar blir installert, behandle korleis administrator<PERSON>t handteras, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handtrykk sendt. Ventar på svar frå instanslyttaren... ({0}%)", "Set a custom backup file name": "Vel<PERSON> eit eige namn for sikkerheitskopifila", "Set custom backup file name": "Velj eit eiga namn for sikkerhetskopifila", "Settings": "Innstillingar", "Share": "Del", "Share WingetUI": "<PERSON>", "Share anonymous usage data": null, "Share this package": "<PERSON> pakka", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Vis UniGetUI i systemfeltet", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "<PERSON><PERSON>", "Show a notification when an installation fails": "Vis ei varsling når ein installasjon er mislykka", "Show a notification when an installation finishes successfully": "Vis ei varsling når ein installasjon blir fullført utan feil", "Show a notification when an operation fails": "Vis ein varsling når ein <PERSON>jon feilar", "Show a notification when an operation finishes successfully": "Vis ein varsling når ein operasjon er fullført", "Show a notification when there are available updates": "Vis ei varsling når oppdateringar er tilgjengelege", "Show a silent notification when an operation is running": "Vis ein stille varsling når ein operasjon køyrar", "Show details": "<PERSON><PERSON>", "Show in explorer": null, "Show info about the package on the Updates tab": "Vis info om pakka under <PERSON><PERSON>ar", "Show missing translation strings": "Vis manglande omsettingsstrengar", "Show notifications on different events": "<PERSON><PERSON> varslingar for ulike hendingar", "Show package details": "<PERSON><PERSON>", "Show package icons on package lists": null, "Show similar packages": "<PERSON>is lignande pakkar", "Show the live output": "Vis utdata i sanntid", "Size": "Storleik", "Skip": "<PERSON><PERSON> over", "Skip hash check": "<PERSON><PERSON> over sjekk av hash", "Skip hash checks": null, "Skip integrity checks": "<PERSON><PERSON> over integritetssjekka<PERSON>", "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "<PERSON><PERSON> over sjek<PERSON> av hash når valte pakkar blir installert", "Skip the hash check when updating the selected packages": "<PERSON><PERSON> over sjek<PERSON> av hash når valte pakkar blir oppdatert", "Skip this version": "<PERSON><PERSON> over denne vers<PERSON>en", "Software Updates": "Programvareoppdateringar", "Something went wrong": "<PERSON>ko gikk galt", "Something went wrong while launching the updater.": null, "Source": "<PERSON><PERSON><PERSON>", "Source URL:": "Kilde-URL:", "Source added successfully": null, "Source addition failed": "Kunne ikkje legge til kjelde", "Source name:": "Kjeldenamn:", "Source removal failed": "Fjerning av kjelde feila", "Source removed successfully": null, "Source:": "Kjelde:", "Sources": "<PERSON><PERSON><PERSON>", "Start": "Start", "Starting daemons...": "Startar bakgrunnsprosessar...", "Starting operation...": null, "Startup options": "Innstillingar for oppstart", "Status": "Status", "Stuck here? Skip initialization": "<PERSON>jem du ikkje vidare? <PERSON><PERSON> over initialiseringa", "Success!": null, "Suport the developer": "St<PERSON>tt utveklaren", "Support me": "<PERSON><PERSON><PERSON> meg", "Support the developer": "St<PERSON>tt utveklaren", "Systems are now ready to go!": "Systema er no klare for bruk!", "Telemetry": null, "Text": "Tekst", "Text file": "Tekstfil", "Thank you ❤": "Takk ❤", "Thank you 😉": "Takk 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": "Sikkerheitskopien kjem ikkje til å innehelde nokre binære filar eller programmar sine lagrede data", "The backup will be performed after login.": "Sikkerheitskopien kjem til å bli utført etter innlogging", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Sikkerheitskopien kjem til å innehelde ein fullstendig liste over dei installerte pakkane og deira installasjonsval. Ignorerte oppdateringar og versjonar som har vorte hoppa over kjem også til å bli lagra.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Sjekksummen til installasjonsprogrammet stemmar ikkje overens med den forventa verdien, og autentisiteten til installasjonsprogrammet kan ikkje bekreftas. Viss du stolar på utgjevaren, {0} pakken igjen og hopp over hash-sjekken.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Den klassiske pakkehandteraren for Windows. Du kan finne alt der. <br><PERSON><PERSON><PERSON>: <b>Generell programvare</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Nåverande bundle har ingen pakkar. Legg til pakkar for å setje i gong", "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Føljande pakkar vil bli eksportert til ein JSON-fil. Ingen brukerdata eller binære filar kjem til å bli lagra.", "The following packages are going to be installed on your system.": "Føljande pakkar kjem til å bli installert på ditt system.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "Føljande innstillingar kjem til å bli brukt kvar gong denne pakken blir installert, opp<PERSON><PERSON>, eller fjerna.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Føljande innstillingar kjem til å bli påførd kvar gang denne pakken innstalleras, opp<PERSON>ras, eller fjernas. Dei kjem til å bli lagra automatisk.", "The icons and screenshots are maintained by users like you!": "Ikonar og skjermbilde vedlikehaldast av brukare som deg!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "Installasjonsprogrammets sjekksum er ugyldig", "The installer hash does not match the expected value.": "Installasjonsprogrammet hash matchar ikkje den forventa verdien.", "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Hovudmålet med dette prosjektet er å lage eit intuitivt grensesnitt for å administrere dei mest vanlege CLI-pakkehandterarane for Windows, til dømes Winget og Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": "Pak<PERSON> {0} fr<PERSON> {1} vart ikkje funne.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Pakkane i denne lista kjem ikkje til å bli tatt hensyn til ved sjekking etter oppdateringar. Dobbeltklikk på dei eller klikk på knappen til høgre for dei for å slutte å ignorere oppdateringa deira.", "The selected packages have been blacklisted": "<PERSON><PERSON> pakkar har blitt svartel<PERSON>", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Storleiken på sikkerheitskopien er estimert til å vere mindre enn 1MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> {source} vart suksessfullt lagt til hos {manager}", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> {source} vart suksessfullt fjerna frå {manager}", "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": "Oppdateringa kjem til å bli installert når du lukkar WingetUI", "The update will not continue.": "Oppdateringa kjem ikkje til å fortsetje.", "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Det er mange pågåande operasjonar. Å avslutte WingetUI kan føre til at dei feilar. Vil du fortsetje?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Det finst nokre flotte videoar på YouTube som visar fram WingetUI og dets funksjonalitet. Du kan lære nyttige triks og tips!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Det er to hovudgrunnar til å ikkje køyre WingetUI som administrator: <PERSON> fyrste er at Scoop-pakkebehandlaren kan forårsake problemar med nokre kommandoer når han bli kjørt med administratorrettar. Den andre grunne er at å køyre WingetUI som administrator betyr at alle pakkane du lastar ned kjem til å bli køyrd som administrator (og dette er ikkje trygt). Husk at viss du treng å installere ein spesifikk pakke som administrator, kan du alltid høgreklikke på elementet -> Installer/Oppdater/Avinstaller som administrator.", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Ein installasjon utførast. Viss du lukkar WingetUI, kan installasjonen feile og gje uventa resultatar. Vil du framleis avslutte WIngetUI?", "They are the programs in charge of installing, updating and removing packages.": "Dei er programmar som er ansvarlege for å installere, oppdatere, og fjerne pakkar.", "Third-party licenses": "Tredjepartslisensar", "This could represent a <b>security risk</b>.": "<PERSON>te kan medføre ein <b>si<PERSON><PERSON><PERSON><PERSON><PERSON></b>.", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Dette er meist sannsynleg fordi pakken du fikk tilsendt er fjerna, eller er publisert på ein pakkehandterar du ikkje har aktivert. Mottatt ID er {0}", "This is the <b>default choice</b>.": "Dette er <b>standardvalet</b>.", "This may help if WinGet packages are not shown": "<PERSON>te kan hje<PERSON>pe viss WinGet-pakkar ikkje visast", "This may help if no packages are listed": null, "This may take a minute or two": "<PERSON>te kan ta nokre få minutt", "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "<PERSON>ne pakken kan bli oppdatert", "This package can be updated to version {0}": "<PERSON>ne pakken kan bli oppdatert til versjon {0}", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Denne pakka har ingen skjermb<PERSON> eller manglar eit ikon? Bidra til WingetUI ved å leggje til dei manglande ikona og skjermbilda til vår opne, offentlege database.", "This package is already installed": "Denne pakken er allereie installert", "This package is being processed": "<PERSON>ne pakken behandlas", "This package is not available": null, "This package is on the queue": "Denne pakken er i køen", "This process is running with administrator privileges": "<PERSON><PERSON> kø<PERSON>r med <PERSON>r", "This project has no connection with the official {0} project — it's completely unofficial.": "Dette prosjektet har ingen kopling til det offisielle {0}-prosjektet - det er fullstendig uoffisielt.", "This setting is disabled": "<PERSON>ne innstillinga er deaktivert", "This wizard will help you configure and customize WingetUI!": "Denne vegvisaren vil hjelpe deg med å konfigurere og tilpasse WingetUI!", "Toggle search filters pane": "Skru av/på panelet for søkefilter", "Translators": "Omsettare", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Skriv inn namn og URL til kjelden du vil legge til her, skilt av mellomrom.", "Unable to find package": "Kan ikkje finne pakka", "Unable to load informarion": "Kan ikkje laste informasjon", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGet UI er ikkje knytta til nokon av dei kompatible pakkehandterarne. UniGetUI er eit uavhengig prosjekt.", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "<PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Avinstaller Scoop (og alle tilhørande pakkar)", "Uninstall and more": null, "Uninstall and remove data": "Avinstaller og fjern data", "Uninstall as administrator": "Avinstaller som administrator", "Uninstall canceled by the user!": "Avinstallasjon avbroten av brukaren!", "Uninstall failed": "Avinstallering feila", "Uninstall options": null, "Uninstall package": "Avinstaller pakka", "Uninstall package, then reinstall it": "Avinstaller pakka, og så reinstaller han", "Uninstall package, then update it": "<PERSON><PERSON><PERSON><PERSON> pakka, og so oppdater ho", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Avinstaller valte pakkar", "Uninstall selection": null, "Uninstall succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> avin<PERSON>lering", "Uninstall the selected packages with administrator privileges": "Avinstaller dei valte pakkane med administratorrettar", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Avinstallerbare pakkar med opprinninga oppført som \"{0}\" er ikkje publisert på nokon pakkebehandlar, så det finnest ingen informasjon tilgjengeleg for visning.", "Unknown": "Ukjend", "Unknown size": "<PERSON><PERSON><PERSON><PERSON>", "Unset or unknown": null, "Up to date": null, "Update": "Uppdater", "Update WingetUI automatically": "Oppdater WingetUI automatisk", "Update all": "<PERSON><PERSON><PERSON><PERSON> alle", "Update and more": null, "Update as administrator": "Oppdater som administrator", "Update check frequency, automatically install updates, etc.": null, "Update checking": null, "Update date": "Oppdateringsdato", "Update failed": "<PERSON><PERSON><PERSON><PERSON> feila", "Update found!": "Oppdatering funne!", "Update now": "Oppdater no", "Update options": null, "Update package indexes on launch": "Oppdater pakkeindeksar ved oppstart", "Update packages automatically": "Oppdater pakkar automatisk", "Update selected packages": "<PERSON><PERSON><PERSON><PERSON> valte pakkar", "Update selected packages with administrator privileges": "Opp<PERSON>r valte pakkar med administratorrettar", "Update selection": null, "Update succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> oppdatering", "Update to version {0}": null, "Update to {0} available": "Oppdatering for {0} er tilgjengeleg", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "Opp<PERSON><PERSON><PERSON>", "Updates available!": "Oppdateringar er tilgjengelege!", "Updates for this package are ignored": "<PERSON><PERSON><PERSON><PERSON><PERSON> for denne pakka er ignorert", "Updates found!": "Oppdateringar funne!", "Updates preferences": "Oppdateringsinnstillingar", "Updating WingetUI": "Oppdaterar WingetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Bruk gamle bundla WinGet i staden for PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Bruk eit eigendefinert ikon og ein eigendefinert URL til databasen", "Use bundled WinGet instead of PowerShell CMDlets": "Bruk bundla WinGet i staden for PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "Bruk bundla WinGet i staden for systemets WinGet", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Bruk systemets installerte GSudo i staden for den medføljande (krevar omstart av programmet)", "Use system Chocolatey": "Bruk systemet Chocolatey", "Use system Chocolatey (Needs a restart)": "Bruk systemets Chocolatey (Omstart krevst)", "Use system Winget (Needs a restart)": "Bruk systemets Winget (Omstart krevst)", "Use system Winget (System language must be set to english)": "Bruk systemets Winget (Systemspråket må være satt til engelsk)", "Use the WinGet COM API to fetch packages": "Bruk WinGet sitt COM-API for å hente pakkar", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Bruk WinGet-PowerShell-modulen i staden for WinGet-COM-APIet", "Useful links": "<PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON>", "User interface preferences": "Alternativar for brukargrensesnitt", "User | Local": "Brukar | Lokal", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Å bruke WingetUI medførar at du aksepterar lisensen GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Bruk av UniGetUI inneber å godta MIT-lisensen", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": "Uttømmande", "Version": "Versjon", "Version to install:": "Versjon å installere:", "Version:": null, "View GitHub Profile": "<PERSON>is GitHub-profil", "View WingetUI on GitHub": "Vis WingetUI på GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Vis WingetUI sin kjeldekode. Herfrå kan du rapportere feil eller foreslå ny funksjonalitet. Du kan også bidra direkte til WingetUI-prosjektet", "View mode:": null, "View on UniGetUI": null, "View page on browser": "Vis sida i nettlesaren", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "Venter på at andre installasjonar skal fullførast...", "Waiting for {0} to complete...": null, "Warning": "<PERSON><PERSON><PERSON><PERSON>", "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": "Vi kunne ikkje laste inn detaljert informasjon om denne pakken, ettersom han ikkje var funne i nokon av dine pakkekjelder", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Vi kunne ikkje laste inn detaljert informasjon denne pakka, fordi han ikkje var installert frå ein tilgjengeleg pakkebehandlar", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Vi kunne ikkje {action} {package}. Venlegst prøv igjen seinare. Klikk på \"{showDetails}\" for å vise loggane frå installasjonsprogrammet.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Vi kunne ikkje {action} {package}. Venlegst prøv igjen seinare. Klikk på \"{showDetails}\" for å vise loggane frå avinstallasjonsprogrammet.", "We couldn't find any package": "Vi kunne ikkje finne nokon pakkje", "Welcome to WingetUI": "Velkommen til WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Kva for pakkehandterare ynskjer du å bruke?", "Which source do you want to add?": "Kva for kjelde ynskjer du å legge til?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Mens Winget kan bli brukt i WingetUI, kan WingetUI også bli brukt med andre pak<PERSON>rar, som kan være forvirrande. <PERSON><PERSON><PERSON> var WingerUI berre laga for å fungere med Winget, men dette stemmar ikkje lenger, og difor representerar ikkje WingetUI kva dette prosjektet har som mål å bli.", "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - Alt er oppdatert", "WingetUI - {0} updates are available": "WingetUI - {0} oppdateringar er tilgjengelege", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI - Heimeside", "WingetUI Homepage - Share this link!": "WingetUI sin heimeside - <PERSON> le<PERSON>!", "WingetUI License": "WingetUI - Lisens", "WingetUI Log": "WingetUI - Logg", "WingetUI Repository": "WingetUI - Repository", "WingetUI Settings": "Innstillingar for WingetUI", "WingetUI Settings File": "Innstillingsfil for WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI brukar føljande bibliotek. Utan dei ville ikkje WingetUI vore mogleg.", "WingetUI Version {0}": "WingetUI versjon {0}", "WingetUI autostart behaviour, application launch settings": "WingetUI sin autostartoppførsel, innstillingar for oppstart", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI kan sjekke om programvaren din har tilgjengelege oppdateringar, og installere dei automatisk viss du vil", "WingetUI display language:": "WingetUI sitt visningsspråk:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI blir køyrd som administrator, noko som ikkje anbefalast. Når du køyrer WingetUI som administrator, får ALLE programmar som WingetUI startar administratortillatingar. Du kan fortfarande bruke programmet, men vi anbefalar på det sterkaste å ikke køyre WingetUI med administratortillatingar.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI har blitt omsett til meir enn 40 språk takka vere frivillige omsetjare. Takk 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI er ikkje atuomatisk omsett. Føljande brukare var vore ansvarlege for omsettingane:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI er ein applikasjon som forenklar handtering av programvare, ved å tilby eit alt-i-eitt grafisk grensesnitt for kommandolinjepakkehandterarane dine.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI byttar namn for å tydeleggjere forskjellen mellom WingetUI (grensesnittet du brukar nett no) og Winget (ein pakkehandterar utvikla av Microsoft som eg ikkje har nokon tilkobling til)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI blir oppdatert. Når oppdateringa er ferdig startar WingetUI på ny", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI er gratis, og kjem til å forbli gratis til evig tid. Ingen reklame, ingen kredittkort, ingen premiumversjon. 100% gratis, for alltid.", "WingetUI log": "<PERSON>gg for WingetUI", "WingetUI tray application preferences": "Val for UniGetUI sin bruk av systemfeltet", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI brukar føljande bibliotek. Utan dei ville ikkje WingetUI ha vore mogleg.", "WingetUI version {0} is being downloaded.": "WingetUI versjon {0} blir lasta ned.", "WingetUI will become {newname} soon!": "WingetUI kjem til å bli til {newname} snart!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI kjem ikkje til å sjekke etter oppdaterinar periodisk. Det kjem fortatt til å bli sjekka ved oppstart, men du kjem ikkje til å bli åtvart om dei.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI kjem til å vise eit UAC-prompt kvar gong ein pakke krev administratorrettar for å installerast.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI kjem snart til å bytte namn til {newname}. Dette kjem ikkje til å føre til endringar i applikasjonen. Eg (utveklaren) kjem til å fortsetje utveklinga av dette prosjektet slik som eg gjer no, men under et anna namn.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI hadde ikkje vore mogleg uten hjelp frå våre kjære bidragsytare. Sjekk ut GitHub-profilane deira, WingetUI ville ikkje vore mogleg utan dei!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI ville ikkje vore mogleg uten hjelp frå alle bidragsytarane. Takk alle saman🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} er klar for installering.", "Write here the process names here, separated by commas (,)": null, "Yes": "<PERSON>a", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "No har du versjon {0} installert", "You have installed WingetUI Version {0}": "<PERSON> har installert WingetUI versjon {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "<PERSON> må kanskje installere {pm} for å bruke det med WingetUI.", "You may restart your computer later if you wish": "Du kan starte datamaskina på ny seinare viss du ynskjer det", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Du vil berre bli bedt om å gje administratorrettar ein gong, og rettane kjem til å bli gitt til pakkar som ber om det.", "You will be prompted only once, and every future installation will be elevated automatically.": "Du vil berre bli bedd om å gje administratorrettar ein gong, og alle framtidige installasjoner kjem til å bli gitt rettar automatisk.", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": "KØYRTE SOM ADMINISTRATOR", "buy me a coffee": "spander ein kopp kaffi på meg", "extracted": "ut<PERSON><PERSON>", "feature": null, "formerly WingetUI": "tidlegare WingetUI", "homepage": "heimeside", "install": "installer", "installation": "installas<PERSON>", "installed": "installert", "installing": "installerar", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "avin<PERSON><PERSON>", "uninstallation": "avinstallasjon", "uninstalled": "a<PERSON><PERSON><PERSON><PERSON>", "uninstalling": "avinstallerar", "update(noun)": "oppdatering", "update(verb)": "oppdatere", "updated": "oppdatert", "updating": "oppdaterar", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} Avinstallasjon", "{0} aborted": "{0} a<PERSON><PERSON><PERSON>", "{0} can be updated": "{0} kan bli oppdatert", "{0} can be updated to version {1}": "{0} kan bli oppdatert til versjon {1}", "{0} days": "{0} dagar", "{0} desktop shortcuts created": null, "{0} failed": "{0} feila", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": "{0} si heimeside", "{0} hours": "{0} timar", "{0} installation": "{0}-<PERSON><PERSON><PERSON>", "{0} installation options": "{0} sine installasjon<PERSON>val", "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} blir oppdatert", "{0} is being updated to version {1}": "{0} blir oppdatert til versjon {1}", "{0} is disabled": "{0} er deaktivert", "{0} minutes": "{0} minutt", "{0} months": null, "{0} packages are being updated": "{0} p<PERSON><PERSON> blir oppdatert", "{0} packages can be updated": "{0} pakkar kan bli oppdatert", "{0} packages found": "{0} pakkar funne", "{0} packages were found": "{0} pak<PERSON> varte funne", "{0} packages were found, {1} of which match the specified filters.": "{0} pak<PERSON> var funne, av dei matcha {1} dine filter.", "{0} selected": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} lykkest", "{0} update": "{0}-oppdatering", "{0} updates are available": "{0} opp<PERSON><PERSON>ar er tilgjengelege", "{0} was {1} successfully!": "{0} blei {1} utan feil!", "{0} weeks": null, "{0} years": null, "{0} {1} failed": "{0} {1} feila", "{package} Installation": "{package} Installasjon", "{package} Uninstall": "{package} Avinstaller", "{package} Update": "{package} Oppdater", "{package} could not be installed": "{package} kunne ikkje bli <PERSON>ert", "{package} could not be uninstalled": "{package} kunne ikkje bli avin<PERSON>t", "{package} could not be updated": "{package} kunne ikkje bli oppdatert", "{package} installation failed": "Installasjon av {package} feila", "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": "Avinstallasjon av {package} feila", "{package} update failed": "Oppdatering av {package} feila", "{package} update failed. Click here for more details.": "Oppdatering av {package} feila. Klikk her for fleire de<PERSON>.", "{package} was installed successfully": "{package} vart suksessfullt installert", "{package} was uninstalled successfully": "{package} vart suksessfullt avinstallert", "{package} was updated successfully": "{package} vart suksessfullt oppdatert", "{pcName} installed packages": "{pcName}: <PERSON><PERSON><PERSON><PERSON> pakkar", "{pm} could not be found": "{pm} kunne ikkje bli funne", "{pm} found: {state}": "{pm} funne: {state}", "{pm} is disabled": "{pm} er deaktivert", "{pm} is enabled and ready to go": "{pm} er aktivert og klar for bruk", "{pm} package manager specific preferences": "Innstillingar som er spesifikke for {pm}-pakkehandteraren", "{pm} preferences": "Inn<PERSON><PERSON><PERSON><PERSON> for {pm}", "{pm} version:": "{pm} versjon:", "{pm} was not found!": "{pm} vart ikkje funne!"}