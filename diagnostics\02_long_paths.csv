"FullName","Length"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Uninstall-ChocolateyEnvironmentVariable.ps1","161"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyEnvironmentVariable.ps1","159"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyPinnedTaskBarItem.ps1","157"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyPowershellCommand.ps1","157"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyExplorerMenuItem.ps1","156"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyFileAssociation.ps1","155"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyInstallPackage.ps1","154"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\UnInstall-ChocolateyZipPackage.ps1","152"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Start-ChocolateyProcessAsAdmin.ps1","152"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyVsixPackage.ps1","151"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Write-FunctionCallLogMessage.ps1","150"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyZipPackage.ps1","150"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Uninstall-ChocolateyPackage.ps1","149"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyShortcut.ps1","148"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-ChocolateyPackage.ps1","147"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-ChocolateyConfigValue.ps1","147"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-UninstallRegistryKey.ps1","146"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Generic.NuGet\UniGetUI.PackageEngine.Managers.Generic.NuGet.csproj","145"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-OSArchitectureWidth.ps1","145"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Set-PowerShellExitCode.ps1","144"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-ChocolateyWebFile.ps1","143"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-PackageParameters.ps1","143"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\Chocolatey.PowerShell.dll-help.xml","142"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-ChocolateyUnzip.ps1","141"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell7\UniGetUI.PackageEngine.Managers.PowerShell7.csproj","141"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-VirusCheckValid.ps1","141"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-ChocolateyPath.ps1","140"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell\UniGetUI.PackageEngine.Managers.PowerShell.csproj","139"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-ToolsLocation.ps1","139"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-CheckSumValid.ps1","139"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\Microsoft.Management.Configuration.dll","139"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\UniGetUI.PackageEngine.Managers.Chocolatey.csproj","139"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Uninstall-BinFile.ps1","139"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\WindowsPackageManager\WindowsPackageManagerStandardFactory.cs","138"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\WindowsPackageManager\WindowsPackageManagerElevatedFactory.cs","138"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-WebFileName.ps1","137"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Format-FileSize.ps1","137"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-BinFile.ps1","137"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable.Tests\UniGetUI.PackageEngine.Serializable.Tests.csproj","137"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\Classes\DesktopShortcutsDatabase.cs","136"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-UACEnabled.ps1","136"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-WebHeaders.ps1","136"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\Classes\IgnoredUpdatesDatabase.cs","134"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\chocolateyScriptRunner.ps1","134"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\ChocolateyTabExpansion.ps1","134"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Install-Vsix.ps1","134"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-WebFile.ps1","133"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\functions\Get-FtpFile.ps1","133"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\Helpers\BasePkgOperationHelper.cs","133"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\Chocolatey.PowerShell.dll","133"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\Classes\InstallOptionsFactory.cs","133"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\AppInstallerBackgroundTasks.dll","132"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\chocolateyInstaller.psm1","132"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\WindowsPackageManagerServer.exe","132"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\Microsoft.Web.WebView2.Core.dll","132"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Dotnet\UniGetUI.PackageEngine.Managers.Dotnet.csproj","131"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell7\Helpers\PowerShell7PkgOperationHelper.cs","131"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\Helpers\BasePkgDetailsHelper.cs","131"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\ExternalLibraries.WindowsPackageManager.Interop.csproj","131"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\UniGetUI.PackageEngine.Managers.WinGet.csproj","131"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\helpers\chocolateyProfile.psm1","130"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\WindowsPackageManager\WindowsPackageManagerFactory.cs","130"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Cargo\UniGetUI.PackageEngine.Managers.Cargo.csproj","129"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell\Helpers\PowerShellPkgOperationHelper.cs","129"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Vcpkg\UniGetUI.PackageEngine.Managers.Vcpkg.csproj","129"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\UniGetUI.PackageEngine.Classes.csproj","129"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\Classes\NullPackageManager.cs","129"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Scoop\UniGetUI.PackageEngine.Managers.Scoop.csproj","129"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\Helpers\ChocolateyPkgOperationHelper.cs","129"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageLoader\UniGetUI.PackageEngine.PackageLoaders.csproj","128"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable.Tests\TestSerializableIncompatiblePackage.cs","127"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\Helpers\BaseSourceHelper.cs","127"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\checksum.license.txt","126"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\redirects\choco.exe.ignore","126"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\WindowsPackageManager.dll","126"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell7\Helpers\PowerShell7DetailsHelper.cs","126"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\Classes\PackageDetails.cs","126"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\InvalidImportedPackage.cs","126"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageEngine\UniGetUI.PackageEngine.PEInterface.csproj","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\Classes\PackageCacher.cs","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Npm\UniGetUI.PackageEngine.Managers.Npm.csproj","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Pip\UniGetUI.PackageEngine.Managers.Pip.csproj","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\shimgen.license.txt","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable\UniGetUI.PackageEngine.Serializable.csproj","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\checksum.exe.ignore","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\checksum.exe.config","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell7\Helpers\PowerShell7SourceHelper.cs","125"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell\Helpers\PowerShellDetailsHelper.cs","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\Classes\ManagerSource.cs","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\Helpers\ChocolateyDetailsHelper.cs","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\Classes\SourceFactory.cs","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\ClientHelpers\IWinGetManagerHelpers.cs","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\redirects\RefreshEnv.cmd","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Generic.NuGet\Internal\NuGetManifestLoader.cs","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\shimgen.exe.ignore","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\Classes\ManagerLogger.cs","124"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\vcruntime140_1_app.dll","123"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\Helpers\ChocolateySourceHelper.cs","123"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\ClientHelpers\NativePackageHandler.cs","123"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell\Helpers\PowerShellSourceHelper.cs","123"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable.Tests\TestSerializableUpdatesOptions.cs","122"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\ClientHelpers\BundledWinGetHelper.cs","122"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\7zip.license.txt","122"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\ManagerHelpers\IPackageOperationHelper.cs","122"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\7z.dll.manifest","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\7z.exe.manifest","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\ClientHelpers\NativeWinGetHelper.cs","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\UniGetUI.PackageEngine.Interfaces.csproj","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\vcruntime140_app.dll","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\UniGetUI.PackageEngine.Operations.csproj","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Dotnet\Helpers\DotNetPkgOperationHelper.cs","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\Helpers\WinGetPkgOperationHelper.cs","121"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\libsmartscreenn.dll","120"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\ManagerHelpers\IPackageDetailsHelper.cs","120"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\vccorlib140_app.dll","120"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\ClientHelpers\WinGetIconsHelper.cs","120"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Cargo\Helpers\CargoPkgOperationHelper.cs","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\msvcp140_2_app.dll","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\7z.exe.ignore","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\msvcp140_1_app.dll","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\ImportedPackage.cs","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\Exceptions\WinGetConfigurationException.cs","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\redirects\choco.exe","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Interface.BackgroundApi\UniGetUI.Interface.BackgroundApi.csproj","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Scoop\Helpers\ScoopPkgOperationHelper.cs","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\WindowsPackageManager\ClassesDefinition.cs","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\Helpers\WinGetPkgDetailsHelper.cs","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Vcpkg\Helpers\VcpkgPkgOperationHelper.cs","119"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\bin\RefreshEnv.cmd","118"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\choco.exe.manifest","118"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\bin\_processed.txt","118"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\checksum.exe","118"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\concrt140_app.dll","118"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Generic.NuGet\BaseNuGetDetailsHelper.cs","118"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Language.Tests\UniGetUI.Core.LanguageEngine.Tests.csproj","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\ManagerHelpers\IMultiSourceHelper.cs","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\vcomp140_app.dll","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\vcamp140_app.dll","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\msvcp140_app.dll","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\shimgen.exe","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Cargo\Helpers\CargoPkgDetailsHelper.cs","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Vcpkg\Helpers\VcpkgPkgDetailsHelper.cs","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Scoop\Helpers\ScoopPkgDetailsHelper.cs","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable\SerializableIncompatiblePackage.cs","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Manager\PackageManager.cs","117"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\choco.exe.ignore","116"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Dotnet\Helpers\DotNetDetailsHelper.cs","116"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.IconEngine.Tests\UniGetUI.Core.IconEngine.Tests.csproj","115"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable.Tests\TestSerializablePackage.cs","115"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\Helpers\WinGetSourceHelper.cs","115"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Npm\Helpers\NpmPkgOperationHelper.cs","115"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Pip\Helpers\PipPkgOperationHelper.cs","115"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\WindowsPackageManager\ClsidContext.cs","114"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable.Tests\TestSerializableBundle.cs","114"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\resources.pri","114"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\ManagersPages\ManagersHomepage.xaml.cs","114"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Data\TranslatedPercentages.json","114"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\bin\choco.exe","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Enums\FDE_SHAREVIOLATION_RESPONSE.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Enums\UniGetUI.PackageEngine.Structs.csproj","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\ExternalLibraries.FilePickers.csproj","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Vcpkg\Helpers\VcpkgSourceHelper.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\AbstractOperation_Auxiliaries.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Scoop\Helpers\ScoopSourceHelper.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Npm\Helpers\NpmPkgDetailsHelper.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageLoader\DiscoverablePackagesLoader.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Pip\Helpers\PipPkgDetailsHelper.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\SettingsHomepage.xaml.cs","113"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\WindowsPackageManager\ClassModel.cs","112"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\7z.exe","112"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\upgrade_chocolatey.cmd","112"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable\SerializableUpdatesOptions.cs","112"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\ManagersPages\PackageManager.xaml.cs","112"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\.idea\.idea.UniGetUI\.idea\inspectionProfiles\Project_Default.xml","112"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\tools\7z.dll","112"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Interface.Telemetry\UniGetUI.Interface.Telemetry.csproj","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.SecureSettings\UniGetUI.Core.SecureSettings.csproj","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\ManagersPages\ManagersHomepage.xaml","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings.Tests\UniGetUI.Core.Settings.Tests.csproj","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\UniGetUI.Core.LanguageEngine.csproj","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Data\LanguagesReference.json","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes.Tests\SortableObservableCollectionTests.cs","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\winget-cli_x64\winget.exe","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\LICENSE.txt","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageLoader\UpgradablePackagesLoader.cs","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageManagerClasses\Packages\Package.cs","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\CREDITS.txt","111"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable.Tests\TestInstallOptions.cs","110"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageLoader\InstalledPackagesLoader.cs","110"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\SettingsHomepage.xaml","110"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Administrator.xaml.cs","110"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Notifications.xaml.cs","110"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes.Tests\UniGetUI.Core.Classes.Tests.csproj","109"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Logging.Tests\UniGetUI.Core.Logging.Tests.csproj","109"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.Clipboard\ExternalLibraries.Clipboard.csproj","109"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\ManagersPages\PackageManager.xaml","109"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Experimental.xaml.cs","109"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\choco-cli\choco.exe","109"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageLoader\AbstractPackageLoader.cs","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_zh_CN.json","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_zh_TW.json","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Structures\COMDLG_FILTERSPEC.cs","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\IFileDialogEvents.cs","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Enums\FDE_OVERWRITE_RESPONSE.cs","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_es-MX.json","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_pt_PT.json","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Interface_P.xaml.cs","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_pt_BR.json","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\AbstractProcessOperation.cs","108"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Operations.xaml.cs","107"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Administrator.xaml","107"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Enums\OverridenInstallationOptions.cs","107"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable\SerializableComponent.cs","107"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageLoader\PackageBundlesLoader.cs","107"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Notifications.xaml","107"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\IShellItemArray.cs","106"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\IFileOpenDialog.cs","106"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Experimental.xaml","106"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_fil.json","106"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_de.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_fi.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_et.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_es.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_en.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_fr.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_el.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_fa.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_da.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell7\PowerShell7.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ca.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_bn.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_bg.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_be.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ar.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_af.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\PageInterfaces\IKeyboardShortcutListener.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable\SerializablePackage.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Internet.xaml.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Interface_P.xaml","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_cs.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_gu.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_gl.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_hi.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_sk.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_sl.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_sq.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_sr.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\FileSaveDialog.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_sv.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_tg.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools.Tests\UniGetUI.Core.Tools.Tests.csproj","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_he.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_tr.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\FileOpenDialog.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ua.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ur.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_vi.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Classes\FileSaveDialogRCW.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Classes\FileOpenDialogRCW.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_th.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_si.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Generic.NuGet\BaseNuGet.cs","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ru.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_hr.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_hu.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_id.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_it.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ja.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ka.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_kn.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ko.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_sa.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_lt.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_mr.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_nb.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_nl.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_nn.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_pl.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_ro.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Languages\lang_mk.json","105"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\InstallOptions_Package.xaml.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\General.xaml.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\InstallOptions_Manager.xaml.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\DialogHelper_Infrastructure.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SoftwarePages\AbstractPackagesPage.xaml.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\Assets\Data\Translators.json","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\KillProcessOperation.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Updates.xaml.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable\SerializableBundle.cs","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Operations.xaml","104"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Interface.Enums\UniGetUI.Interface.Enums.csproj","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.PowerShell\PowerShell.cs","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\OperationFailedDialog.xaml.cs","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Chocolatey\Chocolatey.cs","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Backup.xaml.cs","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data.Tests\UniGetUI.Core.Data.Tests.csproj","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\fonts\UniGetUI-Symbols.woff","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\IModalWindow.cs","103"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Cargo\CratesIOClient.cs","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\OperationLiveLogPage.xaml.cs","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SettingsWidgets\SecureCheckboxCard.cs","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.IconStore\UniGetUI.Core.IconEngine.csproj","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SettingsWidgets\SettingsPageButton.cs","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SettingsWidgets\CheckboxButtonCard.cs","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Internet.xaml","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\fonts\UniGetUI-Symbols.ttf","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\fonts\UniGetUI-Symbols.svg","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\fonts\UniGetUI-Symbols.eot","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\IFileDialog.cs","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Structures\PROPERTYKEY.cs","102"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\OperationWidgets\OperationControl.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\General.xaml","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Updates.xaml","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SoftwarePages\AbstractPackagesPage.xaml","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\InstallOptions_Package.xaml","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\ManagerDependency.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.IconEngine.Tests\IconCacheEngineTests.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\InstallOptions_Manager.xaml","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\AbstractOperation.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\DownloadOperation.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Interfaces\IShellItem.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\PackageOperations.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\ManagerProperties.cs","101"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\PackageDetailsPage.xaml.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\PageInterfaces\IInnerNavigationPage.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\SettingsBasePage.xaml.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\GeneralPages\Backup.xaml","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\PrePostOperation.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Operations\SourceOperations.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SoftwarePages\InstalledPackagesPage.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes\SortableObservableCollection.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings\SettingsEngine_Dictionaries.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Serializable\InstallOptions.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings\SettingsEngine_ImportExport.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\OperationFailedDialog.xaml","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\DialogHelper_Operations.cs","100"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings\UniGetUI.Core.Settings.csproj","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\IPackageManager.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\OperationWidgets\OperationBadge.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\IPackageDetails.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\PageInterfaces\IEnterLeaveListener.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\ThirdPartyLicenses.xaml.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Enums\SIATTRIBFLAGS.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SoftwarePages\DiscoverSoftwarePage.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Properties\PublishProfiles\win10-x64.pubxml","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\OperationLiveLogPage.xaml","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.SecureSettings\SecureGHTokenManager.cs","99"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SoftwarePages\SoftwareUpdatesPage.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\IManagerLogger.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\IManagerSource.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.PackageEngine\PEInterface.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes.Tests\ObservableQueueTests.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\DialogHelper_Packages.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Language.Tests\LanguageEngineTests.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\ISourceFactory.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.IconEngine.Tests\IconDatabaseTests.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Enums\ManagerCapabilities.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\DesktopShortcuts.xaml.cs","98"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SoftwarePages\PackageBundlesPage.cs","97"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes\UniGetUI.Core.Classes.csproj","97"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Utilities\getfilesiginforedist.dll","97"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\DialogHelper_Generic.cs","97"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\PackageDetailsPage.xaml","97"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\SettingsBasePage.xaml","97"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\ThirdPartyLicenses.xaml","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Language.Tests\LanguageDataTests.cs","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Logger\UniGetUI.Core.Logging.csproj","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\IgnoredUpdates.xaml.cs","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Interface.BackgroundApi\BackgroundApi.cs","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\MsiCreator\MsiInstallerWrapper.vdproj","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SettingsWidgets\CheckboxCard.cs","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SettingsWidgets\ComboboxCard.cs","96"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes.Tests\TaskRecyclerTests.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.WinGet\WinGet.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\ObservablePackageCollection.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Interface.Telemetry\TelemetryHandler.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\demo-files\demo.css","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data\Assets\Data\Contributors.list","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\AboutUniGetUI.xaml.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\DesktopShortcuts.xaml","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Guids\CLSIDGuid.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Dotnet\DotNet.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SettingsWidgets\TextboxCard.cs","95"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\demo-files\demo.js","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\FileSavePicker.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Classes\Helper.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\WindowsPackageManager.Interop\NativeMethods.txt","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SettingsWidgets\ButtonCard.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings\SettingsEngine_Extras.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Utilities\UniGetUI Elevator.exe","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Guids\KFIDGuid.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.Clipboard\WindowsClipboard.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\AboutUniGetUI.xaml.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\ReleaseNotes.xaml.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\FileOpenPicker.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\PageInterfaces\ISearchBoxPage.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_turquoise_black.ico","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_turquoise_white.ico","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\LogPages\OperationHistoryPage.cs","94"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Guids\IIDGuid.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\LanguageEngine.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\ISSUE_TEMPLATE\enhancement-improvement.yml","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Enums\HRESULT.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Vcpkg\Vcpkg.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.SecureSettings\SecureSettings.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\Contributors.xaml.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Cargo\Cargo.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings\SettingsEngine_Lists.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings\SettingsEngine_Names.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\MsiCreator\MsiInstallerWrapper.sln","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Scoop\Scoop.cs","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools\UniGetUI.Core.Tools.csproj","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\IgnoredUpdates.xaml","93"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\TranslatedTextBlock.xaml.cs","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\FolderPicker.cs","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\AboutUniGetUI.xaml","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\SettingsPages\ISettingsPage.cs","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\upgradable_filled.svg","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\Translators.xaml.cs","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Utilities\uninstall_scoop.cmd","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Enums\ManagerStatus.cs","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data.Tests\ContributorsTests.cs","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PAckageEngine.Interfaces\IPackage.cs","92"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\AboutUniGetUI.xaml","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings.Tests\SettingsTest.cs","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_orange_white.ico","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Logging.Tests\LogEntryTests.cs","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_orange_black.ico","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\icomoon-project.json","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data\UniGetUI.Core.Data.csproj","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\installed_filled.svg","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Enums\SIGDN.cs","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.LanguageEngine\LanguageData.cs","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\DialogPages\ReleaseNotes.xaml","91"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Utilities\scoop_cleanup.cmd","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\Contributors.xaml","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\selection.json","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes\IIndexableListItem.cs","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\DialogCloseButton.xaml.cs","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Enums\FDAP.cs","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_green_white.ico","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Utilities\install_scoop.cmd","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_green_black.ico","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_empty_white.ico","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_empty_black.ico","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\SupportMe.xaml.cs","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\powershell_color.png","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\desktop_download.png","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Utilities\install_scoop.ps1","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools\SerializationHelpers.cs","90"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\clipboard_list.svg","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Logging.Tests\LoggerTests.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\LogPages\ManagerLogsPage.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\TranslatedTextBlock.xaml","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\update_pc_color.png","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Pip\Pip.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\Translators.xaml","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Managers.Npm\Npm.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\.idea\.idea.UniGetUI\.idea\indexLayout.xml","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\ExternalLibraries.FilePickers\Enums\FOS.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\shield_question.png","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\LogPages\UniGetUILogPage.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_blue_white.ico","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tray_blue_black.ico","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes.Tests\PersonTests.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\warning_filled.svg","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\loading_filled.svg","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.IconStore\IconCacheEngine.cs","89"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\checked_laptop.png","88"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\warning_round.svg","88"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\PackageItemContainer.cs","88"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Properties\Resources.Designer.cs","88"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\configuration.winget\develop-unigetui.winget","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\AboutPages\SupportMe.xaml","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\settings_gear.png","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\Read Me.txt","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes\ObservableQueue.cs","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\restart_color.png","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\package_color.png","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\DialogCloseButton.xaml","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\shield_reload.png","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\icon_unsquare.png","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\experimental.svg","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\console_color.png","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Settings\SettingsEngine.cs","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Services\GitHubBackupService.cs","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data.Tests\LicensesTest.cs","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\shield_yellow.png","87"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\open_folder.svg","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\winget_color.png","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Properties\launchSettings.json","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\interactive.svg","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\LogPages\LogPage.xaml.cs","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\alert_laptop.png","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SourceManager.xaml.cs","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\dotnet_color.png","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.IconStore\Serializable.cs","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.IconStore\IconDatabase.cs","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools.Tests\ToolsTests.cs","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\close_round.svg","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\shield_green.png","86"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\demo.html","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\powershell.svg","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\pin_filled.svg","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\simple_user.png","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\Font\style.css","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\vcpkg_color.png","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\choco_color.png","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\cargo_color.png","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\admin_color.png","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Services\generate-secrets.ps1","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\workstation.png","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\scoop_color.png","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\CustomNavViewItem.cs","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\upgradable.svg","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Services\GitHubAuthService.cs","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\info_round.svg","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools\IntegrityTester.cs","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools\DWMThreadHelper.cs","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools.Tests\MetaTests.cs","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\ISSUE_TEMPLATE\feature-request.yml","85"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data.Tests\CoreTests.cs","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\sandclock.svg","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\node_color.png","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.PackageEngine.Enums\Enums.cs","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\shield_red.png","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\installed.svg","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes\TaskRecycler.cs","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\.idea\.idea.UniGetUI\.idea\.gitignore","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\megaphone.svg","84"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\pin_color.png","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\backward.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\configuration.winget\unigetui-all.winget","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\infocolor.png","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\pip_color.png","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\checksum.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\undelete.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\sys_tray.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\settings.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\download.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\local_pc.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\collapse.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\purge_tolgee_inactiveusers_v2.py","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\configuration.winget\unigetui-min.winget","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\ms_store.svg","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\SourceManager.xaml","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\LogPages\LogPage.xaml","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\agreement.png","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\WebBasedData\screenshot-database-v2.json","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\ISSUE_TEMPLATE\widgets-issue.yml","83"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\version.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\warning.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\forward.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\history.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\Announcer.xaml.cs","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\workflows\translations-test.yml","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\.idea\.idea.UniGetUI\.idea\icon.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\console.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\options.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\PackageWrapper.cs","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\android.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\workflows\winget-prerelease.yml","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\save_as.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\loading.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\package.svg","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\MenuForPackage.cs","82"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\search.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\configuration.winget\configurations.md","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\youtube.png","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Properties\Resources.resx","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\launch.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\bucket.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\.idea\.idea.UniGetUI\.idea\vcs.xml","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\winget.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\add_to.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\delete.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\python.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\reload.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data\Contributors.cs","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\dotnet.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\update.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\expand.svg","81"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\uplay.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\ForceUniGetUIPortable","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\ISSUE_TEMPLATE\hard-crash.yml","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\purge_tolgee_inactiveusers.py","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\vcpkg.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\WebBasedData\screenshot-database.json","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\finish.png","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\rocket.png","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\steam.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\share.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\empty.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\hacker.png","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\cross.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\github.png","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\choco.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\buggy.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\coffee.png","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\cancel.png","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\scoop.svg","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Interface.Enums\Enums.cs","80"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\skip.svg","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\purge_unused_translations.py","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\netcorecheck_x64.exe","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\.idea\.idea.UniGetUI\.idea\.name","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\node.svg","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\CodeDependencies.iss","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\copy.svg","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\disk.svg","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\help.svg","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\ISSUE_TEMPLATE\bug-issue.yml","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\home.svg","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\MsiCreator\README.md","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\rust.svg","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Logger\LogEntry.cs","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\Announcer.xaml","79"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\save.png","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Services\UserAvatar.cs","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Classes\Person.cs","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\warn.png","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\HelpPage.xaml.cs","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\generate_json_from_excel.py","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\workflows\winget-stable.yml","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\get_untranslatd_strings.cmd","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\pin.svg","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\MainView.xaml.cs","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\workflows\update-tolgee.yml","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\workflows\update-icons.yaml","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\kofi.png","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\uac.svg","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\icon.bmp","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\icon.ico","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\icon.png","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\gog.svg","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Images\tick.png","78"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\CustomMessages.iss","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Controls\LocalIcon.cs","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Logger\Logger.cs","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\.vscode\settings.json","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\generate_integrity_tree.py","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data\Licenses.cs","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\Languages\LangReference.py","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Data\CoreData.cs","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\AppOperationHelper.cs","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Assets\Symbols\id.svg","77"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\ISSUE_TEMPLATE\config.yml","76"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\workflows\dotnet-test.yml","76"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\UniGetUI.csproj.user","76"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Package.appxmanifest","76"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\.vscode\launch.json","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\HelpPage.xaml","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Themes\Generic.xaml","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\PULL_REQUEST_TEMPLATE.md","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.Core.Tools\Tools.cs","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\download_translations.py","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Pages\MainView.xaml","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\Services\Secrets.cs","75"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\MainWindow.xaml.cs","74"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.v3.ncrunchsolution","74"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\.vscode\tasks.json","74"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\translation_commands.py","74"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\verify_translations.py","73"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\WebBasedData\invalid_urls.txt","72"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\Languages\LangData.py","72"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\INSTALLER.BMP","72"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\customcolor_icon_old.ai","72"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\diagnostics\01_repo_stats.txt","72"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\workflows\codeql.yml","71"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\CrashHandler.cs","71"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\MainWindow.xaml","71"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\UniGetUI.csproj","71"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\translation_utils.py","71"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\AutoUpdater.cs","70"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\get_contributors.py","70"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\CLIHandler.cs","69"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\EntryPoint.cs","69"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\InstallerExtras\appsdk.exe","69"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\tolgee_requests.py","69"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\WebBasedData\test_urls.py","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI Media.pptx","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\app.manifest","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\1024.png","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\SharedAssemblyInfo.cs","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\Directory.Build.props","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\2048.png","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\apply_versions.py","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\4096.png","68"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\App.xaml.cs","67"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\128.png","67"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\256.png","67"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\512.png","67"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\32.png","66"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.vscode\extensions.json","66"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\16.png","66"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\test_publish_nosign.cmd","66"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\64.png","66"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\Icon sizes\8.png","65"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\dependabot.yml","65"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\renovate.json","64"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\App.xaml","64"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI\icon.ico","64"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_10.png","64"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_2.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\socialicon.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_3.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_1.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_5.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_6.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_7.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_8.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_9.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\UniGetUI_4.png","63"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.github\FUNDING.yml","62"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\scripts\BuildNumber","62"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\CODE_OF_CONDUCT.md","61"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\icon_old.png","61"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\Solution.props","61"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\build_release.cmd","60"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\.editorconfig","60"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\banner.png","59"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\cli-arguments.md","59"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\src\UniGetUI.sln","59"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\test_publish.cmd","59"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.deepsource.toml","59"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\main.webp","58"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\CONTRIBUTING.md","58"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\store.png","58"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\deep_clean.ps1","57"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\icon.svg","57"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\icon.png","57"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.gitattributes","57"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\media\icon.ai","56"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.whitesource","55"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\UniGetUI.iss","55"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\SECURITY.md","54"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\.gitignore","53"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\README.md","52"
"C:\Users\<USER>\AppData\Local\Repos\UniGetUI\LICENSE","50"
