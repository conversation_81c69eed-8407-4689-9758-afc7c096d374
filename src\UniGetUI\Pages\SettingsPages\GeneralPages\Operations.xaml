<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.GeneralPages.Operations"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages.GeneralPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    mc:Ignorable="d">

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel Orientation="Vertical">

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Concurrency and execution" />

            <widgets:ComboboxCard
                x:Name="ParallelOperationCount"
                CornerRadius="8,8,0,0"
                SettingName="ParallelOperationCount"
                Text="Choose how many operations shouls be performed in parallel"
                ValueChanged="ParallelOperationCount_OnValueChanged" />

            <widgets:CheckboxCard
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,8,8"
                ForceInversion="True"
                SettingName="MaintainSuccessfulInstalls"
                WarningText="Download operations are not affected by this setting"
                Text="Clear successful operations from the operation list after a 5 second delay" />

            <UserControl Height="16" />

            <widgets:CheckboxCard
                x:Name="KillProcessesThatRefuseToDie"
                CornerRadius="8"
                SettingName="KillProcessesThatRefuseToDie"
                Text="Try to kill the processes that refuse to close when requested to"
                WarningOpacity="0.7"
                WarningText="You may lose unsaved data" />


            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Automatic desktop shortcut remover" />

            <widgets:CheckboxButtonCard
                ButtonText="Manage shortcuts"
                CheckboxText="Ask to delete desktop shortcuts created during an install or upgrade."
                Click="ManageDesktopShortcutsButton_Click"
                CornerRadius="8"
                SettingName="AskToDeleteNewDesktopShortcuts" />

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Related settings" />

            <controls:SettingsCard
                Click="UpdatesSettingsButton_Click"
                CornerRadius="8,8,0,0"
                IsClickEnabled="True">
                <controls:SettingsCard.Header>
                    <widgets:TranslatedTextBlock Text="Package update preferences" />
                </controls:SettingsCard.Header>
                <controls:SettingsCard.Description>
                    <widgets:TranslatedTextBlock Text="Update check frequency, automatically install updates, etc." />
                </controls:SettingsCard.Description>
                <controls:SettingsCard.HeaderIcon>
                    <widgets:LocalIcon Icon="Package" />
                </controls:SettingsCard.HeaderIcon>
            </controls:SettingsCard>

            <controls:SettingsCard
                BorderThickness="1,0,1,1"
                Click="AdminButton_Click"
                CornerRadius="0,0,8,8"
                IsClickEnabled="True">
                <controls:SettingsCard.Header>
                    <widgets:TranslatedTextBlock Text="Administrator rights and other dangerous settings" />
                </controls:SettingsCard.Header>
                <controls:SettingsCard.Description>
                    <widgets:TranslatedTextBlock Text="Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc." />
                </controls:SettingsCard.Description>
                <controls:SettingsCard.HeaderIcon>
                    <widgets:LocalIcon Icon="UAC" />
                </controls:SettingsCard.HeaderIcon>
            </controls:SettingsCard>


        </StackPanel>
    </ScrollViewer>
</Page>
