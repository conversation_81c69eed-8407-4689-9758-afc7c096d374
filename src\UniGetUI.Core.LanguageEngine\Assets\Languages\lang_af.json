{"\"{0}\" is a local package and can't be shared": "\"{0}\" is \"n plaaslike pakket en kan nie gedeel word nie", "\"{0}\" is a local package and does not have available details": "\"{0}\" is 'n plaaslike pakket en het nie beskikbare besonderhede nie", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" is \"n plaaslike pakket en is nie ve<PERSON><PERSON><PERSON><PERSON><PERSON> met hierdie funksie nie", "(Last checked: {0})": "(<PERSON>as nagegaan: {0})", "(Number {0} in the queue)": "(Nommer {0} in die tou)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": " <PERSON><PERSON><PERSON>", "0 packages found": "0 pakkette gevind", "0 updates found": "0 opdaterings gevind", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "'n dag", "1 hour": "'n uur", "1 month": "'n maand", "1 package was found": "1 pakket is gevind", "1 update is available": "1 opdatering is beskikbaar", "1 week": "1 week", "1 year": "1 jaar", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Navigeer na die bladsy '{0}' of '{1}'.", "2 - Warnings": "2 - Waarskuwings", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "Soek die pakket(te) wat jy by die bundel wil voeg, en merk die linkerste blokkie daarvan.", "3 - Information (less)": "3 - <PERSON><PERSON><PERSON><PERSON> (minder).", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. As die pakkette wat jy by die bundel wil voeg, gekies word, vind en klik op die opsie '{0}' op die werkbalk.", "4 - Information (more)": "4 - <PERSON><PERSON><PERSON><PERSON> (meer)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON> <PERSON><PERSON><PERSON> is bygevoeg tot die bundel. <PERSON><PERSON> ka<PERSON> v<PERSON> met die toevoeging van pakkette, of die uitvoer van die bundel.", "5 - information (debug)": "5 - <PERSON><PERSON><PERSON><PERSON> (ontfout)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "'n Gewilde C/C ++ Biblioteekbestuurder. Vol van C/C ++ biblioteke en ander C/C ++-Verwante hulpprogramme <br> bevat: <b> c/c ++ biblioteke en verwante hulpmiddels </b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "'n Bewaarplek vol gereedskap en uitvoerbare artikels wat ontwerp is met Microsoft se .NET -ekosisteem in gedagte. <br> Bevat: <b> .NET -verwante gereedskap en skrifte </b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "'n Bewaarplek vol gereedskap wat ontwerp is met Microsoft se .NET-ekosisteem in gedagte. <br>Bevat: <b>.NET verwante gereedskap</b>", "A restart is required": "'n her<PERSON><PERSON> nodig is ", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "Omtrent", "About Qt6": "Omtrent Qt6", "About WingetUI": "Omtrent UniGetUI", "About WingetUI version {0}": "Omtrent WingetUI weergawe {0}", "About the dev": "Omtrent die dev", "Accept": "<PERSON><PERSON><PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "<PERSON><PERSON><PERSON> wanneer jy op pakkette dubbelklik, versteek suksesvolle installasies", "Add": "Voegby", "Add a source to {0}": "Voeg 'n bron by {0}", "Add a timestamp to the backup file names": "Voeg 'n tyd stempel by die rugsteun lêer name", "Add a timestamp to the backup files": "Voeg 'n tyd stempel by die rug<PERSON><PERSON> lêer", "Add packages or open an existing bundle": "Voeg pakkette by of maak 'n bestaande bundel oop", "Add packages or open an existing package bundle": "<PERSON><PERSON>g pakkette by of open 'n bestaande pakket bundel", "Add packages to bundle": "Voeg pakkette by die bundel", "Add packages to start": "<PERSON>oeg pakkette by om te <PERSON>", "Add selection to bundle": "<PERSON><PERSON><PERSON> sele<PERSON> by bundel", "Add source": "<PERSON>oeg bron by", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Voeg opdaterings by wat misluk met 'geen toepaslike opdatering' by die lys wat geïgnoreer word nie.", "Adding source {source}": "Voeg bron by {source}", "Adding source {source} to {manager}": "Voeg bron by {source} na {manager}", "Addition succeeded": "Byvoeging geslaag", "Administrator privileges": "Administrateurs voorregte", "Administrator privileges preferences": "Voorkeure vir administrateur regte", "Administrator rights": "Administrateur regte", "Administrator rights and other dangerous settings": null, "Advanced options": "Gevorderde opsies ", "All files": "<PERSON>e lê<PERSON>", "All versions": "Alle weergawes", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Laat die pakket bewerkings parallel uitgevoer word", "Allow parallel installs (NOT RECOMMENDED)": "Laat parallelle installasies toe (NIE AANBEVEEL NIE)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Laat toe dat {pm} parallel uitgevoer word", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternatiewelik kan u ook {0} installeer deur die volgende opdrag in 'n Windows PowerShell aanvraag uit te voer:", "Always elevate {pm} installations by default": "<PERSON><PERSON> verhef {pm} installasies by verst<PERSON>", "Always run {pm} operations with administrator rights": "<PERSON>gin altyd {pm} <PERSON><PERSON><PERSON><PERSON><PERSON> met administrateur regte", "An error occurred": "'n Fout het voorgekom", "An error occurred when adding the source: ": "'n Fout het voorgekom by die toevoeging van die bron:", "An error occurred when attempting to show the package with Id {0}": "'n Fout het voorgekom tydens die poging om die pakket met Id {0} te wys", "An error occurred when checking for updates: ": "'n Fout het voorgekom toe daar vir opdaterings gekyk is:", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "'n Fout het voorgekom tydens die verwerking van hierdie pakket", "An error occurred:": "'n Fout het voorgekom:", "An interal error occurred. Please view the log for further details.": "'n Interne fout het voorgekom. Kyk asseblief na die logboek vir verdere besonderhede.", "An unexpected error occurred:": "'n Onverwagte fout het voorgekom:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "'n Onverwagte probleem het voorgekom terwyl daar probeer is om Winget te herstel. <PERSON><PERSON><PERSON> asseblief later weer", "An update was found!": "'n <PERSON><PERSON>ring is gevind!", "Android Subsystem": "Android  Substelsel", "Another source": "'n <PERSON><PERSON> bron ", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Enige nuwe kortpaaie wat tydens 'n installasie of 'n opdaterings bewerking geskep is, sal outomaties uitgevee word, in plaas daarvan om die eerste keer dat dit opgespoor word, 'n bevestigings aanporboodskap te wys.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "<PERSON>ige koertpaaie wat buite UniGetUI geskep of gewysig is, sal geïgnoreer word. <PERSON>y sal dit via die {0}-knoppie kan byvoeg.", "Any unsaved changes will be lost": "Enige ongestoorde veranderinge sal verlore gaan", "App Name": "<PERSON><PERSON>", "Appearance": "Voorkoms ", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON><PERSON><PERSON> tema, begin bladsy, pakket ikone,  sukses<PERSON>lle installasies outomaties skoon gemaak", "Application theme:": "Toepassings tema:", "Apply": null, "Architecture to install:": "Argitektuur om te installeer:", "Are these screenshots wron or blurry?": "Is hierdie skermkiekies uitgewerk of vaag?", "Are you really sure you want to enable this feature?": "Is jy regtig seker jy wil hierdie kenmerk aktiveer?", "Are you sure you want to create a new package bundle? ": "Is jy seker jy wil 'n nuwe pakket bundel skep?", "Are you sure you want to delete all shortcuts?": "Is jy seker dat jy alle kortpaaie wil uitvee?", "Are you sure?": "Is jy seker?", "Ascendant": "Opgang", "Ask for administrator privileges once for each batch of operations": "Vra een keer vir administrateur regte vir elke bondel bewerkings", "Ask for administrator rights when required": "Vra vir administrateur regte wanneer nodig", "Ask once or always for administrator rights, elevate installations by default": "Vra een keer of altyd vir administrateurregte, verhoog die installasies standaard", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "<PERSON>ra slegs een keer vir administrateur voorregte (nie aanbeveel nie)\n", "Ask to delete desktop shortcuts created during an install or upgrade.": "Vra om die kortpaaie van die gebruikskoppelvlak te verwyder wat tydens 'n installasie of opgradering geskep is.", "Attention required": "<PERSON><PERSON><PERSON> vereis ", "Authenticate to the proxy with an user and a password": "Verifieer aan die instaanbediener met 'n gebruiker en 'n wagwoord", "Author": "<PERSON>eur", "Automatic desktop shortcut remover": "Outomatiese gebruikskoppelvlak kortpad verwyderaar", "Automatically save a list of all your installed packages to easily restore them.": "Stoor outomaties 'n lys van al jou geïnstalleerde pakkette om dit maklik te herstel.", "Automatically save a list of your installed packages on your computer.": "Stoor outomaties 'n lys van u geïnstalleerde pakkette op u rekenaar.", "Autostart WingetUI in the notifications area": "Outo begin WingetUI in die kennisgewings area", "Available Updates": "Beskikbare Opdaterings", "Available updates: {0}": "Beskikbare Opdaterings: {0}", "Available updates: {0}, not finished yet...": "Beskikbare Opdaterings: {0} nog nie klaar nie ...", "Backing up packages to GitHub Gist...": null, "Backup": "<PERSON><PERSON><PERSON><PERSON>", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Rugsteun geïnstalleerde pakkette", "Backup location": "<PERSON><PERSON><PERSON><PERSON> ligging", "Become a contributor": "Word 'n bydraer", "Become a translator": "Word 'n vertaler", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Beta kenmerke en ander opsies wat nie aangeraak moet word nie", "Both": "<PERSON><PERSON>", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "<PERSON>ar hier is ander dinge wat u kan doen om nog meer oor UniGetUI te leer:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Deur 'n pakketbestuurder af te skakel, sal u nie meer die pakkette kan sien of opdateer nie.", "Cache administrator rights and elevate installers by default": "Voorlopige geheue administrateur regte en verhef installeerders by verst<PERSON>", "Cache administrator rights, but elevate installers only when required": "Voorlopige geheue  administrateur regte, maar verhoog installeerders slegs wanneer dit nodig is", "Cache was reset successfully!": "Voorlopige geheue was suksesvol herstel!", "Can't {0} {1}": "Kan nie {0} {1} ", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": "<PERSON><PERSON><PERSON><PERSON> alle bedrywig<PERSON>e", "Change backup output directory": "<PERSON><PERSON> rugsteun lewering lêergids", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "Verander hoe UniGetUI kontrol word en installeer die beskikbare updates vir jou pakkette", "Change how UniGetUI handles install, update and uninstall operations.": "<PERSON><PERSON> hoe Unigetui installeer en deïnstalleer van bewerkings hanteer.", "Change how UniGetUI installs packages, and checks and installs available updates": "<PERSON>nder hoe UniGetUI pakkette installeer en beskikbare opdaterings nagaan en installeer", "Change how operations request administrator rights": "<PERSON><PERSON> hoe bedryfs versoek administrateur regte raak\n\n", "Change install location": "Verander installeer plek ", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Kyk periodiek vir pakket opdaterings", "Check for updates": "Kyk vir opdaterings", "Check for updates every:": "Kyk vir opdaterings elke:", "Check for updates periodically": "Kyk periodiek vir opdaterings", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON><PERSON> gereeld vir opdaterings, en vra my wat om te doen wanneer opdaterings gevind word.", "Check for updates regularly, and automatically install available ones.": "K<PERSON> gereeld vir opdaterings en installeer outomaties beskikbares.", "Check out my {0} and my {1}!": "<PERSON>yk na my {0} en my {1}!", "Check out some WingetUI overviews": "Kyk na 'n paar UniGetUI revisie\n", "Checking for other running instances...": "Kontroleer vir ander lopende gevalle...", "Checking for updates...": "Kyk vir opdaterings...", "Checking found instace(s)...": "Kontroleer gevonde gevall(e)", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON> hoeveel bewerkings parallel uitgevoer moet word", "Clear cache": "Maak voorlopige geheue  skoon", "Clear finished operations": null, "Clear selection": "Maak seleksie skoon", "Clear successful operations": "Maak suksesvolle bedrywighede skoon", "Clear successful operations from the operation list after a 5 second delay": "Maak suksesvolle operasies van die operasie lys na  5 sekondes vertraging skoon", "Clear the local icon cache": "Maak dat die plaaslike ikoon voorlopige geheue skoon", "Clearing Scoop cache - WingetUI": "Skoonmaak van die Scoop se voorlopige geheue - UniGetUI", "Clearing Scoop cache...": "Skoonma<PERSON> van die Scoop se voorlopige geheue", "Click here for more details": "<PERSON><PERSON> hier vir meer besonderhede", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "<PERSON><PERSON> op Installeer om te begin met die installasie proses. As jy die installasie o<PERSON>, kan UniGetUI  nie werk soos verwag nie.", "Close": "Sluit", "Close UniGetUI to the system tray": "Maak dat Unigetui na die stelselbakkie toe gaan", "Close WingetUI to the notification area": "Maak dat UniGetUI na die kennisgewing area gaan", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Command-line to run:": "Opdrag-lyn om te loop:", "Compare query against": "Vergelyk navraag teen ", "Compatible with authentication": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON>", "Compatible with proxy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met vol<PERSON><PERSON>", "Component Information": "Komponent Inligting ", "Concurrency and execution": "Gelyktydigheid en uitvoering ", "Connect the internet using a custom proxy": "Verbind die internet met behulp van persoonlike volmag", "Continue": "Gaan voort", "Contribute to the icon and screenshot repository": "Dra by tot die ikoon en skermkiekie bewa<PERSON>plek", "Contributors": "<PERSON><PERSON><PERSON>\n", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopieer na knipbord", "Could not add source": "Kon nie bron byvoeg nie", "Could not add source {source} to {manager}": "Kon nie die bron {source} by {manager} voeg nie", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "Kon nie bondel skep nie", "Could not load announcements - ": "Kon nie aankondigings laai nie -", "Could not load announcements - HTTP status code is $CODE": "Kon nie aankondigings laai nie - HTTP -statuskode is $CODE", "Could not remove source": "Kon nie bron verwyder nie", "Could not remove source {source} from {manager}": "Kon die bron nie {source} van {manager} verwyder nie", "Could not remove {source} from {manager}": "Kon die {source} van {manager} verwyder nie", "Credentials": "Verwysings", "Current Version": "<PERSON><PERSON><PERSON> weergawe", "Current status: Not logged in": null, "Current user": "<PERSON><PERSON><PERSON> g<PERSON>r", "Custom arguments:": "Pasgemaakte argumente: ", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Pasgemaakte opdrag reël argumente", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Pas UniGetUI aan - slegs vir hackers en gevorderde gebruikers", "DEBUG BUILD": "ONTFOUT BOU", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "VRYWARING: ONS IS NIE VERANTWOORDELIK VIR DIE AFGELAAIDE PAKKETTE NIE. <PERSON>AK ASSEBLIEF SEKER DAT JY SLEGS BETROUBARE SAGTEWARE INSTALLEER.", "Dark": "<PERSON><PERSON>", "Decline": "<PERSON><PERSON>", "Default": "Standaard", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Standaard voorkeure - geskik vir gewone gebruikers", "Default vcpkg triplet": "Standaard vcpkg triplet", "Delete?": "Verwyder?", "Dependencies:": null, "Descendant": "Nasaat", "Description:": "Beskrywing:", "Desktop shortcut created": "Werkskerm kortpad geskep", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Ontwikkeling is moeilik, en hierdie toepassing is gratis. As jy egter die toepassing nuttig gevind het, kan jy altyd <b> vir my 'n koffie koop <b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installeer direk wanneer jy op \"n item op die \"{discoveryTab}\"-o<PERSON><PERSON><PERSON> dubbelklik (in plaas daarvan om die pakketinligting te wys)", "Disable new share API (port 7058)": "Deaktiveer nuwe aandeel API (poort 7058)", "Disable the 1-minute timeout for package-related operations": "Deaktiveer die 1-minuut spertydperk vir pakketverwante bewerkings", "Disclaimer": "<PERSON><PERSON><PERSON><PERSON>", "Discover Packages": "Vind Pak<PERSON>", "Discover packages": "Vind pakkette", "Distinguish between\nuppercase and lowercase": "Onderskei tussen hoofletters en kleinletters", "Distinguish between uppercase and lowercase": "Onderskei tussen hoofletters en kleinletters", "Do NOT check for updates": "MOENIE kyk vir opdaterings nie", "Do an interactive install for the selected packages": "Doen 'n interaktiewe installasie vir die geselekteerde pakkette", "Do an interactive uninstall for the selected packages": "Doen 'n interaktiewe deïnstalleer vir die geselekteerde pakkette", "Do an interactive update for the selected packages": "Doen 'n interaktiewe opdatering vir die geselekteerde pakkette", "Do not automatically install updates when the battery saver is on": "Moenie outomaties opdaterings installeer wanneer die batterybesparing aan is nie", "Do not automatically install updates when the network connection is metered": "Moenie outomaties opdaterings installeer wanneer die netwerkverbinding gemeteerd word nie", "Do not download new app translations from GitHub automatically": "<PERSON><PERSON> nuwe toep -ve<PERSON><PERSON> van <PERSON> outomaties aflaai nie", "Do not ignore updates for this package anymore": "<PERSON><PERSON> meer opdaterings vir hierdie pakket ignoreer nie", "Do not remove successful operations from the list automatically": "<PERSON>nie suksesvolle bewerkings outomaties van die lys verwyder nie", "Do not show this dialog again for {0}": "<PERSON><PERSON> hierdie dialoog weer wys vir {0} nie", "Do not update package indexes on launch": "<PERSON>nie pakketindekse opdateer by la<PERSON><PERSON> nie", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Aanvaar jy dat UniGetUI anonieme gebruiks statistieke versamel en stuur, met die uitsluitlike doel om die gebruikers ervaring te verstaan en te verbeter?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Vind jy UniGetUI nuttig? As jy kan, wil jy dalk my werk ondersteun, sodat ek kan voortgaan om UniGetUI die uiteindelike pakket bestuur koppelvlak te maak.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Vind u UniGetUI nuttig? Wil u die ontwikkelaar ondersteun? As dit so is, kan u {0}, dit help baie!", "Do you really want to reset this list? This action cannot be reverted.": "Wil jy definitief hierdie lys terugstel? Hierdie aksie kan nie teruggekeer word nie.", "Do you really want to uninstall the following {0} packages?": "Wil u die volgende {0} pakkette regtig deïnstalleer?", "Do you really want to uninstall {0} packages?": "Wil jy regtig {0} pak<PERSON><PERSON>?", "Do you really want to uninstall {0}?": "Wil jy regtig {0} deï<PERSON><PERSON>er?", "Do you want to restart your computer now?": "Wil jy jou rekenaar nou herbegin?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Wil jy UniGetUI na jou taal vertaal? K<PERSON> hoe om by te dra <a style=\"color:{0}\" href=\"{1}\"a>HIER!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Het jy nie lus om te skenk nie? <PERSON><PERSON> bekommerd wees nie, jy kan altyd WingetUI met jou vriende de<PERSON>. Versprei die woord oor WingetUI.", "Donate": "Skenk", "Done!": null, "Download failed": "A<PERSON>laai het misluk", "Download installer": "Laai installeerder af", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Aflaai het geslaag", "Download updated language files from GitHub automatically": "Laai outomaties bygewerkte taal lêers van GitHub af", "Downloading": "Aflaai", "Downloading backup...": null, "Downloading installer for {package}": "Laai installeerder af vir {package}", "Downloading package metadata...": "Laai tans pakket metadata af...", "Enable Scoop cleanup on launch": "Aktiveer Scoop se opruiming by bekendstelling", "Enable WingetUI notifications": "Aktiveer UniGetUI kennisgewings", "Enable an [experimental] improved WinGet troubleshooter": "Aktiveer 'n [eksperimentele] verbeterde Winget probleem oplosser", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "Aktiveer agtergrond -CPU  gebruiks optimalisering (see Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktiveer agtergrond API (Wingets vir UniGetUI en Deel, Port 7058)", "Enable it to install packages from {pm}.": "<PERSON><PERSON> dit in staat om pakkette vanaf {pm} te installeer.", "Enable the automatic WinGet troubleshooter": "Aktiveer die outomatiese WinGet probleem oplosser", "Enable the new UniGetUI-Branded UAC Elevator": "Aktiveer die nuwe UniGetUI gemerkte UAC verhogings diens", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Aktiveer {pm}", "Enter proxy URL here": "<PERSON><PERSON><PERSON> hier 'n volmag URL in", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "Fout", "Everything is up to date": "Alles is op datum", "Exact match": "Presiese resultaat", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Bestaande kortpaaie op jou werkskerm sal geskandeer word, en jy sal nodig het om te kies wat om te hou en wat om te ver<PERSON>der.", "Expand version": "<PERSON><PERSON><PERSON> weergawe uit", "Experimental settings and developer options": "Eksperimentele instellings en ontwikkelaar opsies", "Export": "Uit<PERSON><PERSON>", "Export log as a file": "<PERSON><PERSON>r log boek as 'n lêer uit", "Export packages": "<PERSON><PERSON><PERSON> pakket<PERSON> uit ", "Export selected packages to a file": "<PERSON><PERSON><PERSON> geselekteerde pakkette na 'n lêer uit", "Export settings to a local file": "<PERSON><PERSON><PERSON> instellings uit na 'n pla<PERSON>like lêer", "Export to a file": "<PERSON><PERSON><PERSON> uit na 'n lêer", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "Haal die nuutste aankondigings, wag asseblief..", "Filters": "<PERSON><PERSON><PERSON>", "Finish": "Voltooi", "Follow system color scheme": "Volg die stelsel kleur skema", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Dwing ARM saamgestelde winget weergawe (SLEGS VIR ARM64 STELSELS)", "Formerly known as WingetUI": "<PERSON><PERSON><PERSON><PERSON> bekend as <PERSON>etUI", "Found": "<PERSON><PERSON><PERSON>", "Found packages: ": "<PERSON><PERSON><PERSON> gevind:", "Found packages: {0}": "<PERSON><PERSON><PERSON> gevind: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON> gevind: {0}, nog nie klaar nie...", "General preferences": "Algemene v<PERSON>ke<PERSON>", "GitHub profile": "GitHub profiel", "Global": "Globale", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "<PERSON><PERSON> be<PERSON><PERSON><PERSON><PERSON> van <PERSON>, maar nuttige h<PERSON> en ander interessante pakkette. <br>Bevat: <b>Nuts programme, opdragreël programme, Algemene <PERSON>gt<PERSON>are (ekstra-houer vereis)</b>", "Great! You are on the latest version.": "<PERSON><PERSON>! Jy het die nuutste weergawe.", "Grid": "r<PERSON><PERSON>", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Hulp en dokumentasie", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Hier kan jy UniGetUI se gedrag ten opsigte van die volgende kortpaaie verander. As u 'n kortpad nagaan, sal UniGetUI dit uitvee as dit op \"n toekomstige opgradering geskep word. As jy dit ontmerk, sal die kortpad ongeskonde bly", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, my naam is <PERSON><PERSON>, en ek is die <i>ontwikkelaar</i> van UnigetUI. UniGetUI is heeltemal in my vrye tyd gemaak!", "Hide details": "Versteek besonderhede", "Homepage": "Tuisblad\n", "Hooray! No updates were found.": "Ho<PERSON>! <PERSON>n op<PERSON> is gevind nie.", "How should installations that require administrator privileges be treated?": "Hoe moet installasies wat administrateur regte vereis, hanteer word?", "How to add packages to a bundle": "Hoe om pakkette by 'n bondel te voeg", "I understand": "<PERSON><PERSON> verst<PERSON>", "Icons": "Ikone", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "<PERSON><PERSON>reer toekomstige opdaterings vir hierdie pakket\n", "Ignore packages from {pm} when showing a notification about updates": "<PERSON><PERSON><PERSON> pakkette van {pm} wanneer jy 'n kennisgewing oor opdaterings toon", "Ignore selected packages": "<PERSON><PERSON><PERSON> gese<PERSON>de pakkette", "Ignore special characters": "<PERSON><PERSON><PERSON> spesiale karakters", "Ignore updates for the selected packages": "Ignoreer opdaterings vir die geselekteerde pakkette", "Ignore updates for this package": "Ignoreer opdaterings vir hierdie pakket", "Ignored updates": "Ignoreer opdaterings", "Ignored version": "Geïgnoreerde weergawe", "Import": "<PERSON><PERSON><PERSON> in", "Import packages": "<PERSON><PERSON><PERSON> pak<PERSON> in", "Import packages from a file": "<PERSON><PERSON><PERSON> pakkette van<PERSON> l<PERSON> in", "Import settings from a local file": "<PERSON><PERSON><PERSON> instellings van 'n pla<PERSON><PERSON> lêer in", "In order to add packages to a bundle, you will need to: ": "Om pakkette by 'n bundel te voeg, moet jy:", "Initializing WingetUI...": "Initialiseer UniGetUI ...", "Install": "Installeer", "Install Scoop": "<PERSON><PERSON><PERSON><PERSON>", "Install and more": null, "Install and update preferences": "Install and update preferences", "Install as administrator": "<PERSON><PERSON><PERSON><PERSON> as administrateur", "Install available updates automatically": "Installeer beskikbare opdaterings outomaties", "Install location can't be changed for {0} packages": null, "Install location:": "Installeer plek", "Install options": null, "Install packages from a file": "In<PERSON><PERSON><PERSON> pak<PERSON><PERSON> van u<PERSON> 'n l<PERSON>er", "Install prerelease versions of UniGetUI": "Installeer voor vrystel uitgawe van UniGetUI", "Install selected packages": "Installeer geselekteerde pakkette", "Install selected packages with administrator privileges": "Installeer gese<PERSON><PERSON><PERSON><PERSON> met administrateur voor<PERSON><PERSON>e", "Install selection": "Installeer seleksie", "Install the latest prerelease version": "Installeer die nuutste voor vrystel uitgawe", "Install updates automatically": "Installeer opdaterings outomaties", "Install {0}": "Installeer  {0}", "Installation canceled by the user!": "Installasie gekanselleer deur die gebruiker!", "Installation failed": "Installasie het misluk", "Installation options": "Installasie opsies", "Installation scope:": "Installasie omvang:", "Installation succeeded": "Installasie het geslaag", "Installed Packages": "Geïnstalleerde Pakkette", "Installed Version": "Geïnstalleerde Weergawe", "Installed packages": "Geïnstalleerde pakkette", "Installer SHA256": "Installeerder SHA256", "Installer SHA512": "Installeerder SHA512", "Installer Type": "Installeerder Tipe", "Installer URL": "Installeerder URL", "Installer not available": "Installeerder nie beskik<PERSON> nie", "Instance {0} responded, quitting...": "Eks<PERSON><PERSON><PERSON><PERSON> {0} het gereageer en opgehou ...", "Instant search": "Onmiddellike soektog", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Integriteits kontroles oorgeslaan", "Integrity checks will not be performed during this operation": "Integriteits kontroles sal nie tydens hierdie werking uitgevoer word nie", "Interactive installation": "Interaktiewe installasie", "Interactive operation": "Interaktiewe werking", "Interactive uninstall": "Interaktiewe deïnstalleer", "Interactive update": "Interaktiewe opdatering", "Internet connection settings": "Internet verbindings instellings", "Is this package missing the icon?": "Ontbreek hierdie pakket die ikoon?", "Is your language missing or incomplete?": "Ont<PERSON><PERSON> jou taal of is dit onvolledig?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Dit is nie gew<PERSON><PERSON> dat die voorsiende bewyse veilig geberg sal word nie, en jy kan ook nie die bewyse van jou bankrekening gebruik nie", "It is recommended to restart UniGetUI after WinGet has been repaired": "Dit word aanbeveel om UniGetUI weer te begin nadat Winget herstel is", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Dit lyk asof Winget nie goed werk nie. Wil jy probeer om Winget te herstel?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON>t lyk asof jy UniGetUI as administrateur uitge<PERSON>er, wat nie aanbeveel word nie. <PERSON>y kan steeds die program gebruik, maar ons beveel sterk aan om nie UniGetUI met administrateur regte te laat loop nie. Klik op \"{showDetails}\" om te sien hoekom.", "Language": "Taal", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON>, tema en ander diverse voorke<PERSON>", "Last updated:": "<PERSON><PERSON> opgedateer:", "Latest": "Nuutste", "Latest Version": "Nuutste Weergawe", "Latest Version:": "Nuutste Weergawe:", "Latest details...": "Nuutste besonderhede ...", "Launching subprocess...": "Begin tans subverwerking...", "Leave empty for default": "Laat leeg vir standaard", "License": "Lisensie", "Licenses": "Lisensies", "Light": "<PERSON>g", "List": "Lys", "Live command-line output": "Regstreekse opdrag reël afvoer", "Live output": "Regstreekse afvoer", "Loading UI components...": "Laai UI komponente... ", "Loading WingetUI...": "Laai UniGetUI", "Loading packages": "Laai pakkette ", "Loading packages, please wait...": "<PERSON><PERSON> p<PERSON>, wag asseblief...", "Loading...": "Laai...", "Local": "Plaaslike", "Local PC": "Plaaslike PC", "Local backup advanced options": null, "Local machine": "<PERSON><PERSON><PERSON><PERSON> ", "Local package backup": null, "Locating {pm}...": "Vind tans {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "Log vlak: ", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Op soek na pakkette...", "Machine | Global": "Masjien | Globaal", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "<PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "Bestuur UniGetUI instellings", "Manage WingetUI autostart behaviour from the Settings app": "Bestuur UniGetUI outobegin gedrag vanaf die Instellings toep", "Manage ignored packages": "Bestuur geïgnoreerde pakkette", "Manage ignored updates": "Bestuur geïgnoreerde opdaterings", "Manage shortcuts": "<PERSON><PERSON><PERSON> k<PERSON>", "Manage telemetry settings": "Bestuur telemetrie-instellings", "Manage {0} sources": "Bestuur {0} bronne", "Manifest": "<PERSON><PERSON><PERSON><PERSON>", "Manifests": "<PERSON><PERSON><PERSON><PERSON>", "Manual scan": "Hand<PERSON><PERSON> skandering", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoft se amptelike pakketbestuurder. Vol bekende en geverifieerde pakkette<br>Bevat: <b><PERSON><PERSON><PERSON><PERSON>, Microsoft Store-toepassings</b>", "Missing dependency": "Ontbrekende afhanklikheid", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON> beson<PERSON>e", "More details about the shared data and how it will be processed": "Meer besonderhede oor die gedeelde data en hoe dit verwerk sal word", "More info": "<PERSON><PERSON> inligt<PERSON>", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "LET WEL: <PERSON><PERSON><PERSON> probleem oplosser kan gedeaktiveer word vanaf UniGetUI instellings, op die WinGet afdeling", "Name": "<PERSON><PERSON>", "New": null, "New Version": "Nuwe Weergawe", "New bundle": "<PERSON><PERSON><PERSON>", "New version": "<PERSON><PERSON>we weergawe", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": "<PERSON>n toepaslike installeerder is vir die pakket gevind nie{0}", "No dependencies specified": null, "No new shortcuts were found during the scan.": "<PERSON><PERSON> nuwe k<PERSON>e is tydens die skandering gevind nie.", "No packages found": "<PERSON>n pakkette gevind nie", "No packages found matching the input criteria": "<PERSON>n pakkette word gevind wat o<PERSON>enstem met die inset kriteria nie", "No packages have been added yet": "<PERSON><PERSON> pakkette is nog bygevoeg nie", "No packages selected": "<PERSON><PERSON> pakkette gekies nie", "No packages were found": "<PERSON><PERSON> pakket<PERSON> is gevind nie", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "<PERSON>n persoonlike inligting word ingesamel of gestuur nie, en die versamelde data word geanonimiseer, so dit kan nie na jou teruggestuur word nie.", "No results were found matching the input criteria": "Geen resultate is gevind wat o<PERSON>enstem met die inset kriteria nie", "No sources found": "Geen bronne gevind nie", "No sources were found": "<PERSON>n bronne is gevind nie", "No updates are available": "<PERSON><PERSON> op<PERSON> is be<PERSON><PERSON><PERSON><PERSON> nie", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS se pakket bestuurder. Vol biblioteke en ander hulpmiddels wat om die javascript-w<PERSON><PERSON><PERSON> wentel<br>Bevat: <b>Node javascript biblioteke en ander verwante hulpmiddels</b>", "Not available": "<PERSON>e be<PERSON> nie", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Nie gevind nie", "Not right now": "Nie nou nie", "Notes:": "Notas:", "Notification preferences": "Kennisgewing voorkeure", "Notification tray options": "Kennisgewing laai opsies", "Notification types": "Kennisgewing tipes", "NuPkg (zipped manifest)": "NuPkg (kompakteer openbaar) ", "OK": "GOED", "Ok": "Goed", "Open": "<PERSON><PERSON><PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON> oop", "Open UniGetUI": "Maak UniGetUI oop", "Open UniGetUI security settings": null, "Open WingetUI": "Maak UniGetUI oop", "Open backup location": "<PERSON><PERSON> rug<PERSON>un ligging oop", "Open existing bundle": "<PERSON><PERSON> bestaande bondel oop", "Open install location": "Maak installeer ligging oop", "Open the welcome wizard": "Maak die welkome assistent oop", "Operation canceled by user": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> deur gebruiker", "Operation cancelled": "<PERSON><PERSON><PERSON>", "Operation history": "<PERSON><PERSON><PERSON>", "Operation in progress": "Opdrag in vordering", "Operation on queue (position {0})...": "Opdrag in wagting (p<PERSON>ie {0})...", "Operation profile:": null, "Options saved": "Opsies gestoor", "Order by:": "Volgorde:", "Other": "Ander", "Other settings": "Ander instellings", "Package": null, "Package Bundles": "Pakket Bondels", "Package ID": "Pakket ID", "Package Manager": "Pak<PERSON> Bestuurder", "Package Manager logs": "Pakket Bestuurder logs", "Package Managers": "Pakket Bestuurders", "Package Name": "Pak<PERSON>", "Package backup": "<PERSON><PERSON> rug<PERSON>", "Package backup settings": null, "Package bundle": "Pakket bundel", "Package details": "Pakket besonderhede", "Package lists": "Pakket lyste", "Package management made easy": "Pakket bestuur maklik gemaak", "Package manager": "Pakket bestuurder", "Package manager preferences": "Pakket bestuurders se voorkeure", "Package managers": "Pakket bestuurders", "Package not found": "<PERSON><PERSON> nie gevind nie", "Package operation preferences": "Voorkeure vir pakket bewerking", "Package update preferences": "Voorkeure vir pakket opdatering", "Package {name} from {manager}": "Pak<PERSON> {name} van {manager}\n", "Package's default": null, "Packages": "Pakkette", "Packages found: {0}": "<PERSON><PERSON><PERSON> gevind: {0} ", "Partially": "Gedeeltelik", "Password": "Wagwoord", "Paste a valid URL to the database": "Plak 'n geldige URL in die databasis", "Pause updates for": "Pouse opdaterings vir", "Perform a backup now": "<PERSON><PERSON><PERSON> <PERSON><PERSON> uit", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "<PERSON><PERSON><PERSON> uit, wag asseblief...", "Periodically perform a backup of the installed packages": "Voer 'n periodiek rugsteun van die geïnstalleerde pakkette uit", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "Gaan asseblief die installasie opsies vir hierdie pakket na en probeer weer", "Please click on \"Continue\" to continue": "Klik asseblief op \"<PERSON>aan voort\" om voort te gaan", "Please enter at least 3 characters": "<PERSON><PERSON>r asseblief ten minste 3 karakters in", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Let asseblief daarop dat sekere pakkette moontlik nie geïnstalleer kan word nie, as g<PERSON><PERSON><PERSON> van die pakket bestuurders wat op hierdie masjien geaktiveer is.", "Please note that not all package managers may fully support this feature": "Let asseblief daarop dat nie alle pakket bestuurders hierdie funksie ten volle kan ondersteun nie", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Let daarop dat pakkette uit sekere bronne moontlik nie uitvoerba<PERSON> is nie. Hulle is grys en sal nie uitgevoer word nie.", "Please run UniGetUI as a regular user and try again.": "Voer asseblief UniGetUI as \"n gewone gebruiker uit en probeer weer.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Raadpleeg asseblief die opdrag reël uitset of verwys na die operasionele geskiedenis vir verdere inligting oor die kwessie.", "Please select how you want to configure WingetUI": "<PERSON><PERSON> asseblief hoe jy UniGetUI wil konfigureer", "Please try again later": "<PERSON><PERSON><PERSON> ass<PERSON><PERSON>f weer later", "Please type at least two characters": "<PERSON><PERSON> aseblief ten minste twee karakters in", "Please wait": "Wag asseblief", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Wag asseblief terwyl {0} geïnstalleer word. 'n Swart venster kan dalk oopmaak. Wag asseblief totdat dit sluit", "Please wait...": "Wag asseblief...", "Portable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Portable mode": "Draagbare modus", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell se pakket bestuurder. Vind biblioteke en skrifte om PowerShell vermoëns uit te brei <br> bevat: <b> modules, skrifte, Cmdlets </b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "Voorvrystelling", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON> pakket<PERSON> voor, wag asseblief...", "Proceed at your own risk.": "Gaan voort op eie risiko.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "Volmag UTL", "Proxy compatibility table": "Volmag versoen<PERSON><PERSON><PERSON><PERSON> tabel", "Proxy settings": "Volmag instellings", "Proxy settings, etc.": "Volmag instellings, ens.", "Publication date:": "Publikasie datum: ", "Publisher": "Uitgewer", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python se biblioteek bestuurder. Vol python biblioteke en ander Python verwante hulpmiddels <br> bevat: <b> Python biblioteke en verwante hulpmiddels </b>", "Quit": "Verlaat", "Quit WingetUI": "Verlaat UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reinstall package": "Herinstalleer Pakkette", "Related settings": "Verwante instellings", "Release notes": "<PERSON><PERSON><PERSON><PERSON> notas", "Release notes URL": "<PERSON><PERSON><PERSON><PERSON> notas", "Release notes URL:": "Vrystelling notas URL:", "Release notes:": "<PERSON><PERSON><PERSON><PERSON> notas:", "Reload": "<PERSON><PERSON><PERSON>", "Reload log": "<PERSON><PERSON><PERSON> log", "Removal failed": "Ver<PERSON>dering het misluk", "Removal succeeded": "Verwydering het geslaag", "Remove from list": "<PERSON><PERSON><PERSON><PERSON>", "Remove permanent data": "Verwyder permanente data", "Remove selection from bundle": "Verwyder seleksie uit bondel", "Remove successful installs/uninstalls/updates from the installation list": "Verwyder suksesvolle installeering//deïnstalleering/opdaterings van die installering lys", "Removing source {source}": "<PERSON>er<PERSON><PERSON> bron {source}", "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON><PERSON> bron {source} van {manager}", "Repair UniGetUI": null, "Repair WinGet": "<PERSON><PERSON>l WinGet", "Report an issue or submit a feature request": "Rapporteer 'n probleem of dien 'n funksie versoek in", "Repository": "Bewaarplek", "Reset": "<PERSON><PERSON><PERSON> ", "Reset Scoop's global app cache": "<PERSON><PERSON><PERSON> Sc<PERSON> se global toep vir voorlopige geheue", "Reset UniGetUI": "Herstel UniGetUI", "Reset WinGet": "<PERSON><PERSON>l WinGet", "Reset Winget sources (might help if no packages are listed)": "<PERSON><PERSON><PERSON> Win<PERSON>et bronne (kan help as geen pakkette gelys word nie)", "Reset WingetUI": "Herstel UniGetUI", "Reset WingetUI and its preferences": "Herstel UniGetUI en s<PERSON> voorkeure", "Reset WingetUI icon and screenshot cache": "Herstel  UniGetUI ikoon en skermkiekie se voorlopige geheue ", "Reset list": "<PERSON><PERSON><PERSON> lys", "Resetting Winget sources - WingetUI": "<PERSON><PERSON><PERSON> van Winget  bronne - UniGetUI", "Restart": "<PERSON><PERSON><PERSON>", "Restart UniGetUI": "Herbegin UniGetUI", "Restart WingetUI": "Herbegin UniGetUI", "Restart WingetUI to fully apply changes": "Restart UniGetUI to fully apply changes", "Restart later": "<PERSON><PERSON><PERSON> later", "Restart now": "<PERSON><PERSON><PERSON> nou", "Restart required": "<PERSON><PERSON><PERSON>", "Restart your PC to finish installation": "<PERSON><PERSON><PERSON> jou <PERSON> om die installasie te voltooi", "Restart your computer to finish the installation": "Herb<PERSON>in jou rekenaar om die installasie te voltooi", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "<PERSON><PERSON><PERSON><PERSON>", "Retry as administrator": "<PERSON><PERSON><PERSON><PERSON> as administrateur ", "Retry failed operations": "<PERSON><PERSON><PERSON><PERSON> mislukte bed<PERSON><PERSON><PERSON>e", "Retry interactively": "<PERSON><PERSON><PERSON><PERSON> interaktief ", "Retry skipping integrity checks": "Her<PERSON><PERSON>er om integriteits kontroles oor te slaan", "Retrying, please wait...": "<PERSON><PERSON><PERSON> weer, wag asseblief ...", "Return to top": "<PERSON>er terug na bo", "Run": "Laat loop", "Run as admin": "Laat loop as admin", "Run cleanup and clear cache": "Laat loop skoonmaak en maak voorlopige geheue skoon", "Run last": "Laat loop laaste", "Run next": "Laat loop volgende", "Run now": "Laat loop nou", "Running the installer...": "Laat loop die installeerder...", "Running the uninstaller...": "Loop loop deïnstalleerder...", "Running the updater...": "Laat loop opdatering...", "Save": null, "Save File": "<PERSON><PERSON>", "Save and close": "Stoor en sluit", "Save as": null, "Save bundle as": "<PERSON><PERSON> bundel as", "Save now": "Stoor nou", "Saving packages, please wait...": "<PERSON><PERSON> p<PERSON>, wag asseblief ...", "Scoop Installer - WingetUI": "Scoop se Installer - UniGetUI", "Scoop Uninstaller - WingetUI": "Scoop <PERSON>ï<PERSON>alleerder - UniGetUI", "Scoop package": "<PERSON>oop pakket ", "Search": "<PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Soek na werkskerm sagteware, waarsku my wanneer opdaterings beskikbaar is en nie snaaks dinge doen nie. Ek wil nie hê UniGetUI moet te veel komplisering hê nie, ek wil net 'n eenvoudige hê  <b>sagteware winkel</b>\n", "Search for packages": "Soek vir pakkette", "Search for packages to start": "Soek vir pakkette om te begin", "Search mode": "Soek modus", "Search on available updates": "Soek op beskikbare opdaterings", "Search on your software": "Soek op jou sagteware", "Searching for installed packages...": "Soek vir geïnstalleerde pakkette...", "Searching for packages...": "Op na pakkette ...", "Searching for updates...": "Soek na opdaterings ...", "Select": "Se<PERSON>kt<PERSON>", "Select \"{item}\" to add your custom bucket": "Selekteer \"{item}\" om in jou pasgemaakte emmer by te voeg", "Select a folder": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> vouer", "Select all": "Selekt<PERSON> alles", "Select all packages": "Selekteer alle pakkette", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON> slegs <b> as jy weet wat jy doen </b>.", "Select package file": "Selekteer pakket lêer", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "<PERSON><PERSON><PERSON><PERSON> die bron wat jy wil byvoeg:", "Select upgradable packages by default": "Selekteer standaard opgradeerbare pakkette", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON> watter <b> pakket bestuurders <b>  om te gebruik ({0}), konfigureer hoe pakkette geïnstalleer moet word, bestuur hoe administrateur regte hanteer moet word, ens.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "<PERSON><PERSON>ur handdruk. Wag vir die antwoord van die luisteraar ... ({0}%)", "Set a custom backup file name": "Stel 'n pasgemaakte rugsteun lêer naam vas", "Set custom backup file name": "<PERSON>el pasgemaakte rug steun lêer naam vas", "Settings": "Instellings", "Share": "<PERSON><PERSON>", "Share WingetUI": "Deel UniGetUI", "Share anonymous usage data": "Deel anonieme gebruiks data", "Share this package": "<PERSON>l hierdie pakket", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Wys UniGetUI op die stelsel bak", "Show UniGetUI's version and build number on the titlebar.": "Wys UniGetUI se weergawe op die titel balk", "Show WingetUI": "Wys UniGetUI", "Show a notification when an installation fails": "Wys 'n kennisgewing wanneer 'n installasie misluk", "Show a notification when an installation finishes successfully": "Wys 'n kennisgewing wanneer 'n installasie suksesvol voltooi is", "Show a notification when an operation fails": "Wys 'n kennisgewing aan wanneer 'n opdrag misluk", "Show a notification when an operation finishes successfully": "Wys 'n kennisgewing wanneer 'n opdrag suksesvol voltooi is", "Show a notification when there are available updates": "Wys 'n kennisgewing wanneer daar beskikbare opdaterings is", "Show a silent notification when an operation is running": "Wys 'n stil kennisgewing wanneer 'n opdrag loop", "Show details": "<PERSON><PERSON> be<PERSON>", "Show in explorer": "Wys in explorer", "Show info about the package on the Updates tab": "Wys inligting oor die pakket op die Opdaterings oortjie", "Show missing translation strings": "Wys ontbrekende vertaal stringe", "Show notifications on different events": "Wys kennisgewings oor verskillende gebeure", "Show package details": "Wys pakket besonderhede", "Show package icons on package lists": "Wys pakket ikone op pakket lyste", "Show similar packages": "<PERSON>ys so<PERSON> pakkette", "Show the live output": "Wys die regstreekse uitset", "Size": "Grootte", "Skip": "<PERSON><PERSON><PERSON> oor", "Skip hash check": "Slaan huts kontrole oor", "Skip hash checks": "<PERSON><PERSON><PERSON> hash kontroles oor", "Skip integrity checks": "Slaan integriteits kontroles oor", "Skip minor updates for this package": "<PERSON><PERSON><PERSON> geringe opdaterings oor vir hierdie pakket", "Skip the hash check when installing the selected packages": "<PERSON><PERSON><PERSON> die hash kontrole oor wanneer jy die geselekteerde pakkette installeer", "Skip the hash check when updating the selected packages": "<PERSON><PERSON><PERSON> die hash kontrole oor wanneer jy die geselekteerde pakkette opdateer", "Skip this version": "<PERSON><PERSON><PERSON> hierdie weergawe oor", "Software Updates": "Sagteware Opdaterings", "Something went wrong": "Iets het verkeerd geloop", "Something went wrong while launching the updater.": "Iets het verkeerd geloop tydens die bekendstelling van die opdaterer.", "Source": "<PERSON><PERSON>", "Source URL:": "Bron URL:", "Source added successfully": "Bron suksesvol bygevoeg", "Source addition failed": "<PERSON><PERSON> aanvulling het misluk", "Source name:": "<PERSON>ron naam:", "Source removal failed": "<PERSON><PERSON> verwydering het misluk", "Source removed successfully": "Bron suksesvol verwyder", "Source:": "Bron:", "Sources": "Bronne", "Start": "<PERSON><PERSON>", "Starting daemons...": "Begin Daemons ...", "Starting operation...": "Begin opdrag...", "Startup options": "Opstart opsies", "Status": "Status", "Stuck here? Skip initialization": "Vas hier? <PERSON><PERSON><PERSON> inisi<PERSON> oor", "Suport the developer": "Ondersteun die ontwikkelaar", "Support me": "Ondersteun my", "Support the developer": "Ondersteun die ontwikkelaar", "Systems are now ready to go!": "<PERSON><PERSON><PERSON> is nou gereed om te gaan!", "Telemetry": "Telemetrie", "Text": "Teks", "Text file": "<PERSON><PERSON>", "Thank you ❤": "Dan<PERSON> ❤", "Thank you 😉": "Dan<PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Die Rust pakket bestuurder. <br> Bevat: <b>\nRust biblioteke en  programme wat in Rust geskryf is <b>", "The backup will NOT include any binary file nor any program's saved data.": "Die rugsteun sal NIE enige binêre lêer of enige program se gestoorde data insluit nie.", "The backup will be performed after login.": "Die rugsteun sal uitgevoer word na aanmelding.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Die rugsteun bevat die volledige lys van die geïnstalleerde pakkette en hul installasie opsies. Ignoreerde opdaterings en oorgeslaande weergawes sal ook gestoor word.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Die bundel wat jy probeer laai blyk ongeldig te wees. Kontroleer aseblief die lêer en probeer weer.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Die kontrolesom van die installeerder val nie saam met die verwagte waarde nie, en die egtheid van die installeerder kan nie geverifieer word nie. As jy die uitgewer vertrou, {0} die pakket weer om die huts kontrole oor te slaan.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Die klassieke pakket bestuurder vir windows. <PERSON><PERSON> sal alles daar vind. <br>Bevat: <b>Algemen<PERSON> sa<PERSON><PERSON></b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Die huidige bundel het geen pakkette nie. Voeg 'n paar pakkette by om aan die gang te kom", "The executable file for {0} was not found": "Die uitvoerbare lêer vir {0} is nie gevind nie", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Die volgende pakkette gaan na \"n JSON lêer uitgevoer word. Geen gebruikers data of binêre gaan gestoor word nie", "The following packages are going to be installed on your system.": "Die volgende pakkette gaan op jou stelsel geïnstalleer word.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "Die volgende instellings word toegepas elke keer as hierdie pakket geïnstalleer, opgedateer of verwyder word.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "\nDie volgende instellings word toegepas elke keer as hierdie pakket geïnstalleer, opgedateer of verwyder word. Hulle sal outomaties gestoor word.", "The icons and screenshots are maintained by users like you!": "Die ikone en skermkiekies word deur gebruikers soos jy onderhou!", "The installer authenticity could not be verified.": "Die installeerder se egtheid kon nie geverifieer word nie.", "The installer has an invalid checksum": "Die installeerder het 'n ongeldige kontrolesom", "The installer hash does not match the expected value.": "Die installeerder huts stem nie ooreen met die verwagte waarde nie.", "The local icon cache currently takes {0} MB": "Die plaaslike ikoon voorlopige geheue neem tans {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Die hoofdoel van hierdie projek is om 'n intuïtiewe UI te skep vir die mees algemene CLI -pakket bestuurders vir windows, soos Winget en Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Die pakket \"{0}\" is nie op die pakket bestuurder gevind nie \"{1}\"", "The package bundle could not be created due to an error.": "Die pakket bundel kon nie geskep word nie weens 'n fout.", "The package bundle is not valid": "Die pakket bundel is nie geldig nie", "The package manager \"{0}\" is disabled": "Die pakketbestuurder \"{0}\" is gestremd", "The package manager \"{0}\" was not found": "Die pakketbestuurder \"{0}\" is nie gevind nie", "The package {0} from {1} was not found.": "Die pakket {0}  van {1} is nie gevind nie.\n", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Die pakkette wat hier gelys word, sal nie in ag geneem word wanneer daar vir opdaterings gekyk word nie. Dubbelklik op hulle of klik op die knoppie aan hul regterkant om op te hou om hul opdaterings te ignoreer.", "The selected packages have been blacklisted": "Die geselekteerde pakkette is op die swartlys geplaas", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Die grootte van die rugsteun word geskat op minder as 1 MB.", "The source {source} was added to {manager} successfully": "Die bron {source} is suksevol bygevoeg na die  {manager} ", "The source {source} was removed from {manager} successfully": "Die bron {source} is suks<PERSON><PERSON> verwyder na die  {manager} ", "The system tray icon must be enabled in order for notifications to work": "Die stelsel bakkie ikoon moet aangeskakel word om kennisgewings te laat werk", "The update process has been aborted.": "Die opdaterings proses is gestaak.", "The update process will start after closing UniGetUI": "Die opdatering sproses sal begin nadat UniGetUI toegemaak  is", "The update will be installed upon closing WingetUI": "Die opdatering sal geïnstalleer word wanneer Unigetui toekemaak word", "The update will not continue.": "Die opdatering sal nie voortgaan nie.", "The user has canceled {0}, that was a requirement for {1} to be run": "Die gebruiker het {0} g<PERSON><PERSON><PERSON><PERSON>, dit was 'n vereiste vir {1} om uitgevoer te word\n", "There are no new UniGetUI versions to be installed": "Da<PERSON> is geen nuwe UniGetUI weergawes wat geïnstalleer moet word nie", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "<PERSON><PERSON> is deurlopende bedrywighede. As jy UniGetUI toe<PERSON>, kan dit misluk. Wil jy voort<PERSON>?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Daar is \"n paar wonderlike video\"s op YouTube wat UniGetUI en sy vermoëns ten toon stel. Jy kan nuttige truuks en wenke leer!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "<PERSON><PERSON> is twee hoofredes om UniGetUI nie as administrateur uit te voer nie:\nDie eerste een is dat die Scoop-pakket bestuurder probleme met sommige opdragte kan veroorsaak wanneer dit met administrateur regte uitgevoer word.\n", "There is an error with the configuration of the package manager \"{0}\"": "<PERSON><PERSON> is 'n fout met die konfigurasie van die pakket bestuurder \" {0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "<PERSON><PERSON> is 'n installasie aan die gang. As jy UniGetUI toe maak, kan die installasie misluk en onverwagte resultate hê. Wil jy nog UniGetUI toemaak?", "They are the programs in charge of installing, updating and removing packages.": "Dit is die programme wat verantwoordelik is vir die installering, opdatering en verwydering van pakkett", "Third-party licenses": "Derde party lisensies", "This could represent a <b>security risk</b>.": "Dit kan 'n <b> se<PERSON><PERSON><PERSON> risiko <b> verteenwoordig.", "This is not recommended.": "Dit word nie aanbeveel nie.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Dit is waarskynlik te wyte aan die feit dat die pakket wat jy gestuur is, verwyder is, of gepubliseer is op 'n pakket bestuurder wat jy nie geaktiveer het nie. Die ontvangde ID is {0}", "This is the <b>default choice</b>.": "Dit is die <b> verst<PERSON> keuse <b>", "This may help if WinGet packages are not shown": "<PERSON><PERSON> kan help as <PERSON><PERSON>  pakkette nie vertoon word nie", "This may help if no packages are listed": "Dit kan help as geen pakkette gelys word nie", "This may take a minute or two": "Dit kan 'n minuut of twee neem", "This operation is running interactively.": "Hierdie <PERSON>ie loop interaktief.", "This operation is running with administrator privileges.": "<PERSON><PERSON>ie werking word uitge<PERSON><PERSON> met administrateur regte.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Hierdie pakket kan opgedateer word", "This package can be updated to version {0}": "Hierdie pakket kan opgedateer word na die weergawe {0}", "This package can be upgraded to version {0}": "Hierdie pakket kan opgegradeer word na die weergawe {0}", "This package cannot be installed from an elevated context.": "Hierdie pakket kan nie vanuit 'n verhoogde konteks geïnstalleer word nie.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Hierdie pakket het geen skermkiekies nie of ontbreek die ikoon? Dra by tot UniGgetUI deur die ontbrekende ikone en skermkiekies by ons oop, openbare databasis te voeg.", "This package is already installed": "Hierdie pakket is reeds geïnstalleer", "This package is being processed": "Hierdie pakket word verwerk", "This package is not available": "Hierdie pakket is nie beskik<PERSON> nie", "This package is on the queue": "<PERSON><PERSON><PERSON> pakket is op tou", "This process is running with administrator privileges": "<PERSON><PERSON><PERSON> proses word met administrateur regte uitgevoer", "This project has no connection with the official {0} project — it's completely unofficial.": "<PERSON><PERSON>ie projek het geen verband met die amptelike {0} projek nie - dit is heeltemal nie-amptelik.", "This setting is disabled": "<PERSON><PERSON><PERSON> instelling is gede<PERSON>tiveer", "This wizard will help you configure and customize WingetUI!": "<PERSON><PERSON>ie assistent sal jou help om UniGetUI op te stel en aan te pas!", "Toggle search filters pane": "Wipkring soek filtreer paneel", "Translators": "Vertalers", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Tik hier die naam en die URL van die bron wat jy wil byvoeg, geskei deur  'n spasie.", "Unable to find package": "Kan nie die pakket vind nie", "Unable to load informarion": "Kan nie inligting laai nie", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI versamel anonieme gebruiks data om die gebruikers ervaring te verbeter.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI versamel anonieme gebruiks data met die uitsluitlike doel om die gebruikers ervaring te verstaan en te verbeter.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI het 'n nuwe werkskerm kortpad opgespoor wat outomaties uitgevee kan word.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI het die volgende werkskerm kortpaaie opgespoor wat outomaties verwyder kan word by toekomstige opgraderings", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI het bespeur {0}  nuwe werkskerm kortpaaie wat outomaties uitgevee kan word.", "UniGetUI is being updated...": "UniGetUI word opgedateer...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI hou nie verband met enige van die versoenbare pakket bestuurders nie. UniGetUI is 'n onafhanklike projek.", "UniGetUI on the background and system tray": "UniGetUI op die agtergrond en stelsel balk", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI benodig {0} om te werk, maar dit is nie op jou stelsel gevind nie.\n", "UniGetUI startup page:": "UniGetUI Begin bladsy:", "UniGetUI updater": "UniGetUI opdatering", "UniGetUI version {0} is being downloaded.": "UniGetUI weergawe {0} word afgelaai.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} is gereed om geïnstalleer te word.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Deïnstalleer Scoop (en sy pakkette)", "Uninstall and more": null, "Uninstall and remove data": "Deïnstalleer en verwyder weg data", "Uninstall as administrator": "<PERSON><PERSON><PERSON><PERSON><PERSON> as administrateur", "Uninstall canceled by the user!": "Deïnstalleer deur die gebruiker gekanselleer!", "Uninstall failed": "Deïnstalleer het misluk", "Uninstall options": null, "Uninstall package": "Deïnstalleer pakket", "Uninstall package, then reinstall it": "Deïnstalleer pakket en installeer dit dan weer", "Uninstall package, then update it": "Deïnstalleer pakket en dateer dit dan op", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Deïnstalleer geselekteerde pakkette", "Uninstall selection": null, "Uninstall succeeded": "Deïnstalleer het geslaag", "Uninstall the selected packages with administrator privileges": "Deïnstalleer die gekose pakkette met administrateur regte", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Ongeïnstalleerbare pakkette met die oorsprong gelys as \"{0}\" word nie op enige pakket bestuurder gepubliseer nie, so daar is geen inligting beskikbaar om daaroor te wys nie.\n", "Unknown": "Onbekend", "Unknown size": "<PERSON><PERSON><PERSON><PERSON> grootte", "Unset or unknown": "Ongeset of onbekend", "Up to date": "Op datum", "Update": "Opdatering", "Update WingetUI automatically": "Dateer UniGetUI outomaties op", "Update all": "Dateer almal op", "Update and more": null, "Update as administrator": "Op<PERSON><PERSON> as administrateur", "Update check frequency, automatically install updates, etc.": "Dateer kontrole frekwensie op, installeer opdaterings outomaties, ens.", "Update date": "Dateer datum op", "Update failed": "Opdatering het misluk", "Update found!": "Opdatering gevind!", "Update now": "Bring op datum nou", "Update options": null, "Update package indexes on launch": "<PERSON>er pakket indekse op by bekendstelling", "Update packages automatically": "Dateer pakkette outomaties op", "Update selected packages": "Opdateer geselekteerde pakkette op", "Update selected packages with administrator privileges": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON><PERSON> pakket<PERSON> op met administrateur voorregte", "Update selection": null, "Update succeeded": "Opdatering het geslaag", "Update to version {0}": "Dateer op na weergawe {0}", "Update to {0} available": "Opdatering tot {0} be<PERSON><PERSON><PERSON><PERSON>", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Opdateer VCPKG se GIT portefiele outomaties (benodig GIT geïnstalleer)", "Updates": "Bywerkings", "Updates available!": "Opdaterings beskikbaar!", "Updates for this package are ignored": "Opdaterings vir hierdie pakket word geïgnoreer", "Updates found!": "Opdaterings gevind!", "Updates preferences": "<PERSON><PERSON> voorke<PERSON> op", "Updating WingetUI": "Opdatering van UniGetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Gebruik Legacy gebundelde WinGet in plaas van PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Gebruik 'n pasgemaakte ikoon en 'n skermkiekie databasis URL", "Use bundled WinGet instead of PowerShell CMDlets": "Gebruik gebundelde WinGet in plaas van PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "Gebruik gebundelde WinGet in plaas van stelsel WinGet", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Gebruik geïnstalleerde GSudo in plaas van die gebundelde een", "Use system Chocolatey": "Gebruik stelsel Chocolatey", "Use system Chocolatey (Needs a restart)": "Gebruik stelsel <PERSON>y (benodig 'n herbegin)", "Use system Winget (Needs a restart)": "Gebruik System Winget (moet weer begin)", "Use system Winget (System language must be set to english)": "Gebruik Winget (stelseltaal moet op Engels gestel word)", "Use the WinGet COM API to fetch packages": "Gebruik die Winget Com API om pakkette te gaan haal", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Gebruik die WinGet PowerShell Module in plaas van die WinGet COM-API", "Useful links": "Nuttige skakels", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "User interface preferences": "Voorkeure vir gebruiker koppelvlak", "User | Local": "Gebruiker | Plaaslik", "Username": "Gebruikers naam", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Die gebruik van UniGetUI impliseer die aanvaarding van die GNU Lesser General Public License v2.1-lisensie", "Using WingetUI implies the acceptation of the MIT License": "Die gebruik van UniGetUI impliseer die aanvaarding van die MIT-lisensie", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "VCPKG -wortel is nie gevind nie. Definieer die %VCPKG_ROOT% Omgewings veranderlike of definieer dit van UnigGtUI instellings", "Vcpkg was not found on your system.": "Vcpkg is nie op jou stelsel gevind nie.", "Verbose": "Omvat<PERSON>", "Version": "Weergawe", "Version to install:": "Weergawe om te installeer:", "Version:": null, "View GitHub Profile": "Kyk na GitHub profiel", "View WingetUI on GitHub": "Bekyk UniGetUI op GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Bekyk UniGetUI se bron kode. <PERSON> daar af kan jy foute rapporteer of kenmerke voorstel, of selfs direk bydra tot die UniGetUI-projek", "View mode:": "Bekyk modus:", "View on UniGetUI": "Kyk op UniGetUI", "View page on browser": "Bekyk bladsy op blaaier", "View {0} logs": "Bekyk {0} logs", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Wag totdat die toestel aan die internet gekoppel is voordat jy probeer om take te verrig wat internet verbinding benodig.", "Waiting for other installations to finish...": "Wag vir ander installasies om klaar te maak ...", "Waiting for {0} to complete...": "Wag vir {0}  Om te voltooi ...", "Warning": "Waarskuwing", "Warning!": "Waarskuwing!", "We are checking for updates.": "Ons kyk na opdaterings.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Ons kon nie gedetailleerde inligting oor hierdie pakket laai nie, want dit is nie in u pakket bronne gevind nie.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Ons kon nie gedetailleerde inligting oor hierdie pakket laai nie, omdat dit nie vanaf \"n beskikbare pakket bestuurder geïnstalleer is nie.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Ons kon nie {action} {package} nie. <PERSON>beer asseblief later weer. Klik op \"{showDetails}\" om die logs van die installeerder te kry.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Ons kon nie {action} {package}.  Probeer asseblief weer later. Klik op {showDetails} om die logs van die deïnstalleerder te kry.", "We couldn't find any package": "Ons kon geen pakkie kry nie", "Welcome to WingetUI": "Welkom by UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Wanneer nuwe kortpaaie bespeur word, skrap hulle outomaties in plaas daarvan om hierdie dialoog te vertoon.", "Which backup do you want to open?": null, "Which package managers do you want to use?": "Watter pakket bestuurders wil jy gebruik?", "Which source do you want to add?": "Watter bron wil jy byvoeg?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Terwyl WinGet binne UniGetUI gebruik kan word, kan UniGetUI saam met ander pakketbestuurders gebruik word, wat verwarrend kan wees. In die verlede is UniGetUI ontwerp om slegs met Winget te werk, maar dit is nie meer waar nie, en daarom verteenwoordig UniGetUI nie wat hierdie projek beoog om te word nie.", "WinGet could not be repaired": "WinGet kon nie herstel word nie", "WinGet malfunction detected": "WinGet wanfunksie opgespoor", "WinGet was repaired successfully": "WinGet is suksesvol herstel", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Alles is op datum", "WingetUI - {0} updates are available": "UniGetUI - {0} opdaterings is beskikbaar", "WingetUI - {0} {1}": "IniGetUI - {0} {1}", "WingetUI Homepage": "UniGetUI Tuisblad ", "WingetUI Homepage - Share this link!": "UniGetUI Tuisblad - Deel hierdie skakel!", "WingetUI License": "UniGetUI Lisensie ", "WingetUI Log": "UniGetUI Log", "WingetUI Repository": "UniGetUI bewaarplek", "WingetUI Settings": "UniGetUI Instellings", "WingetUI Settings File": "UniGetUI Instellings Lêer", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI gebruik die volgende biblioteke.  Sonder hulle sou UniGetUI nie moontlik gewees het nie.", "WingetUI Version {0}": "UniGetUI Weergawe {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI outo begingedrag, toepassings bekend stellings instellings", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI kan kyk of jou sagteware beskikbare opdaterings het, en dit outomaties installeer as jy wil", "WingetUI display language:": "UniGetUI vertoon taal:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI het as administrateur geloop, wat nie aanbeveel word nie. As u UniGetUI as administrateur gebruik, sal ELKE bewerking wat vanaf UniGetUI van stapel gestuur word, administrateur regte hê. <PERSON><PERSON> kan steeds die program gebruik, maar ons beveel sterk aan om nie UniGetUI met administrateurregte te laat loop nie.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI is in meer as 40 tale vertaal danksy die vrywillige vertalers. Dankie 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI is nie masjien vertaal nie! Die volgende gebruikers was in beheer van die vertalings:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI is 'n toepassing wat die bestuur van jou sagteware makliker maak deur 'n alles-in-een grafiese koppelvlak vir jou opdrag reël pakket bestuurders te verskaf.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI word hernoem om die verskil tussen UniGetUI (die koppelvlak wat jy tans gebruik) en WinGet ('n pakket bestuurder ontwikkel deur Microsoft waarmee ek nie verwant is nie) te beklemtoon", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI word opgedateer. <PERSON><PERSON> dit klaar is, sal UniGetUI vanself weer begin", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI is gratis, en dit sal vir ewig gratis wees. Geen advertensies, geen krediet<PERSON>, geen premium weergawe nie. 100% gratis, vir ewig", "WingetUI log": "UniGetUI log", "WingetUI tray application preferences": "UniGetUI skinkbord toepassings voorkeure", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI gebruik die volgende biblioteke. Sonder hulle sou UniGetUI nie moontlik gewees het nie.", "WingetUI version {0} is being downloaded.": "UniGetUI weergawe {0} word afgelaai.\n", "WingetUI will become {newname} soon!": "WingetUI sal binnekort {newname} word!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI sal nie periodiek vir opdaterings kyk nie. Hulle sal steeds by lanseering nagegaan word, maar jy sal nie daaroor gewaarsku word nie.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI sal 'n UAC aanpor wys elke keer as 'n pakket vereis dat verhoging geïnstalleer moet word.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI sal binnekort die naam {newname} kry.  Dit sal geen verandering in die toepassing verteenwoordig nie. E<PERSON> (die ontwikkelaar) sal voortgaan met die ontwikkeling van hierdie projek soos ek tans doen, maar onder 'n ander naam.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI sou nie moontlik gewees het sonder die hulp van ons dierbare bydraers nie. Kyk na hul GitHub profiele, UniGetUI sou nie sonder hulle moontlik wees nie!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI sou nie moontlik gewees het sonder die hulp van die bydraers nie.  Dankie almal 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} is gereed om geïnstalleer te word.", "Write here the process names here, separated by commas (,)": null, "Yes": "<PERSON>a", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "<PERSON>y het tans weergawe {0} geïnstalleer", "You have installed WingetUI Version {0}": "Jy het UniGetUI weergawe geïnstalleer {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "<PERSON><PERSON><PERSON> moet jy {pm} installeer om dit met UniGetUI te gebruik.", "You may restart your computer later if you wish": "<PERSON><PERSON> kan jou re<PERSON> later herb<PERSON>in as jy wil", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "<PERSON>y sal net een keer gevra word, en administrateur regte sal verleen word aan pakkette wat dit versoek.", "You will be prompted only once, and every future installation will be elevated automatically.": "<PERSON>y sal slegs een keer gevra word, en elke toekomstige installasie sal outomaties verhoog word", "You will likely need to interact with the installer.": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> met die installeerder moet kommunikeer.", "[RAN AS ADMINISTRATOR]": "LAAT LOOP AS ADMINISTRATEUR ", "buy me a coffee": "Koop vir my 'n koffie", "extracted": "onttrek", "feature": "kenmerk", "formerly WingetUI": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "Webwerf", "install": "installeer", "installation": "installasie", "installed": "geïnstalleer", "installing": "Installeer", "library": "biblioteek", "mandatory": null, "option": "keuse", "optional": null, "uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstallation": "O<PERSON><PERSON><PERSON><PERSON>", "uninstalled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstalling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update(noun)": "opdatering", "update(verb)": "opdatering", "updated": "Bygewerk", "updating": "opdatering", "version {0}": "weergawe {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} Deï<PERSON><PERSON>ering", "{0} aborted": "{0} onder<PERSON><PERSON>", "{0} can be updated": "{0} opgedateer kan word ", "{0} can be updated to version {1}": "{0} kan word opgedateer na weergawe {1}", "{0} days": "{0} dae", "{0} desktop shortcuts created": "{0} werkskerm kortpaaie geskep ", "{0} failed": "{0} misluk", "{0} has been installed successfully.": "{0} is suksesvol geïnstalleer.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} is suksesvol geïnstalleer. Dit word aanbeveel om UniGetUI te oorbegin om die installasie te voltooi", "{0} has failed, that was a requirement for {1} to be run": "{0} het nie, dit was'n vereiste vir {1} om te loop", "{0} homepage": "{0} tuisblad", "{0} hours": "{0} ure", "{0} installation": "{0} installasie", "{0} installation options": "{0} installasie opsies ", "{0} installer is being downloaded": "{0} installeerder is besig om afgelaai te word", "{0} is being installed": "{0} word geï<PERSON>alleer ", "{0} is being uninstalled": "{0} word g<PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} is being updated": "{0} word opgedateer ", "{0} is being updated to version {1}": "{0} word opgedateer na weergawe {1}", "{0} is disabled": "{0} is afgeskakel ", "{0} minutes": "{0} minute", "{0} months": "{0} maande", "{0} packages are being updated": "{0} pakkette is opgedateer", "{0} packages can be updated": "{0} pakkette kan word opgedateer ", "{0} packages found": "{0} pak<PERSON><PERSON> gevind ", "{0} packages were found": "{0} pakkette is gevind ", "{0} packages were found, {1} of which match the specified filters.": "{0} p<PERSON><PERSON><PERSON> is gevind, {1} van die wat ooreenstem met die gespesifiseerde filtreers.", "{0} settings": "{0} instellings", "{0} status": "{0} stand", "{0} succeeded": "{0} opgevolg", "{0} update": "{0} <PERSON><PERSON><PERSON>", "{0} updates are available": "{0} Opdaterings is be<PERSON><PERSON><PERSON><PERSON>", "{0} was {1} successfully!": "{0} was {1} suksesvol!", "{0} weeks": "{0} weke", "{0} years": "{0} jare", "{0} {1} failed": "{0} {1} het misluk", "{package} Installation": "{package} Installasie", "{package} Uninstall": "{package} <PERSON><PERSON><PERSON><PERSON><PERSON>", "{package} Update": "{package} Opdatering", "{package} could not be installed": "{package} kon nie geïnstalleer word nie", "{package} could not be uninstalled": "{package} Kon nie geïnstalleer word nie ", "{package} could not be updated": "{package} kon nie opgedateer word nie", "{package} installation failed": "{package} installasie het misluk", "{package} installer could not be downloaded": "{package} installeerder kon nie afgelaai word nie", "{package} installer download": "{package} installer word aflaai", "{package} installer was downloaded successfully": "{package} installeerder is suksesvol afgelaai", "{package} uninstall failed": "{package} deïnstalleer het misluk", "{package} update failed": "{package} opdatering het misluk", "{package} update failed. Click here for more details.": "{package} opdatering het misluk. Klik hier vir meer besonderhede.", "{package} was installed successfully": "{package} is suksesvol geïnstalleer", "{package} was uninstalled successfully": "{package} is suks<PERSON><PERSON><PERSON> gedeïnstalleer", "{package} was updated successfully": "{package} is suksesvol opgedateer", "{pcName} installed packages": "{pcName} geïnstalleerde pakkette", "{pm} could not be found": "{pm} kon nie gevind word nie", "{pm} found: {state}": "{pm} gevind: {state}", "{pm} is disabled": "{pm} is gedeaktiveer", "{pm} is enabled and ready to go": "{pm} is geaktiveer en gereed om te begin", "{pm} package manager specific preferences": "{pm} pakketbestuurder spesifieke voorkeure", "{pm} preferences": "{pm} v<PERSON><PERSON><PERSON>", "{pm} version:": "{pm} weergawe:", "{pm} was not found!": "{pm} is nie gevind nie!"}