<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Interface.AbstractPackagesPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Toolkit="using:CommunityToolkit.WinUI.Controls"
    xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
    xmlns:animations="using:CommunityToolkit.WinUI.Animations"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:pkgClasses="using:UniGetUI.PackageEngine.PackageClasses"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Name="ABSTRACT_PAGE"
    NavigationCacheMode="Required"
    SizeChanged="ABSTRACT_PAGE_SizeChanged"
    mc:Ignorable="d">

    <animations:Implicit.ShowAnimations>
        <animations:TranslationAnimation
            From="0,100,0"
            To="0"
            Duration="0:0:0.25" />
        <animations:OpacityAnimation
            From="0"
            To="1"
            Duration="0:0:0.25" />
    </animations:Implicit.ShowAnimations>


    <Page.Resources>
        <DataTemplate x:Key="PackageTemplate_List" x:DataType="pkgClasses:PackageWrapper">
            <widgets:PackageItemContainer
                AutomationProperties.Name="{x:Bind Package.AutomationName}"
                CornerRadius="4"
                DoubleTapped="{x:Bind PackageItemContainer_DoubleTapped}"
                Package="{x:Bind Package}"
                PreviewKeyDown="{x:Bind PackageItemContainer_PreviewKeyDown}"
                RightTapped="{x:Bind PackageItemContainer_RightTapped}"
                Wrapper="{x:Bind Self}">

                <Grid
                    Padding="12,3,8,3"
                    ColumnSpacing="4"
                    Opacity="{x:Bind ListedOpacity, Mode=OneWay}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="30" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="24" />
                        <ColumnDefinition Width="*" MinWidth="125" />
                        <ColumnDefinition Width="*" MinWidth="125" />
                        <ColumnDefinition Width="*" MaxWidth="150" />
                        <ColumnDefinition Width="*" MaxWidth="{x:Bind NewVersionLabelWidth}" />
                        <ColumnDefinition Width="*" MaxWidth="175" />
                    </Grid.ColumnDefinitions>
                    <CheckBox
                        Grid.Row="0"
                        Grid.Column="0"
                        VerticalAlignment="Center"
                        IsChecked="{x:Bind IsChecked, Mode=TwoWay}" />

                    <!--  Regular or package icon  -->
                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="1"
                        Width="24"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        widgets:IconBuilder.Icon="Package"
                        FontSize="24"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind ShowDefaultPackageIcon, Mode=OneWay}" />

                    <Image
                        Grid.Row="0"
                        Grid.Column="1"
                        Width="24"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Source="{x:Bind MainIconSource, Mode=OneWay}"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind ShowCustomPackageIcon, Mode=OneWay}" />

                    <!--  Package tag icon  -->
                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="1"
                        Width="20"
                        Height="20"
                        Margin="8,0,-4,-2"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Bottom"
                        widgets:IconBuilder.Icon="{x:Bind AlternateIconId, Mode=OneWay}"
                        FontSize="20"
                        FontWeight="ExtraLight"
                        Foreground="{ThemeResource AccentAAFillColorTertiaryBrush}"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind AlternateIdIconVisible, Mode=OneWay}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="1"
                        Width="20"
                        Height="20"
                        Margin="8,0,-4,-2"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Bottom"
                        widgets:IconBuilder.Icon="{x:Bind MainIconId, Mode=OneWay}"
                        FontSize="20"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind AlternateIdIconVisible, Mode=OneWay}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="28,-2,0,0"
                        VerticalAlignment="Center"
                        FontSize="13"
                        Text="{x:Bind Package.Name}"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="2"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        widgets:IconBuilder.Icon="Id"
                        FontSize="24"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind Package.Id}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="2"
                        Margin="28,-2,0,0"
                        VerticalAlignment="Center"
                        FontSize="13"
                        Text="{x:Bind Package.Id}"
                        ToolTipService.ToolTip="{x:Bind Package.Id}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="3"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        widgets:IconBuilder.Icon="version"
                        FontSize="24"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind Package.VersionString}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="3"
                        Margin="28,-2,0,0"
                        VerticalAlignment="Center"
                        FontSize="13"
                        Text="{x:Bind Package.VersionString, Mode=OneWay}"
                        ToolTipService.ToolTip="{x:Bind Package.VersionString}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="4"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        widgets:IconBuilder.Icon="download"
                        FontSize="24"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind Package.NewVersionString}"
                        Visibility="{x:Bind Package.IsUpgradable}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="4"
                        Margin="28,-2,0,0"
                        VerticalAlignment="Center"
                        FontSize="13"
                        Text="{x:Bind Package.NewVersionString}"
                        ToolTipService.ToolTip="{x:Bind Package.NewVersionString}"
                        Visibility="{x:Bind Package.IsUpgradable}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="5"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        widgets:IconBuilder.Icon="{x:Bind Package.Source.IconId}"
                        FontSize="24"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind Package.Source.AsString_DisplayName}" />

                    <TextBlock
                        Grid.Row="0"
                        Grid.Column="5"
                        Margin="28,-2,0,0"
                        VerticalAlignment="Center"
                        FontSize="13"
                        Text="{x:Bind Package.Source.AsString_DisplayName}"
                        ToolTipService.ToolTip="{x:Bind Package.Source.AsString_DisplayName}" />
                </Grid>
            </widgets:PackageItemContainer>
        </DataTemplate>

        <DataTemplate x:Key="PackageTemplate_Grid" x:DataType="pkgClasses:PackageWrapper">
            <widgets:PackageItemContainer
                AutomationProperties.Name="{x:Bind Package.AutomationName}"
                Background="{ThemeResource ControlFillColorDefaultBrush}"
                CornerRadius="4"
                DoubleTapped="{x:Bind PackageItemContainer_DoubleTapped}"
                Package="{x:Bind Package}"
                PreviewKeyDown="{x:Bind PackageItemContainer_PreviewKeyDown}"
                RightTapped="{x:Bind PackageItemContainer_RightTapped}"
                Wrapper="{x:Bind Self}">

                <Grid
                    Padding="4,4,4,4"
                    HorizontalAlignment="Stretch"
                    ColumnSpacing="4"
                    Opacity="{x:Bind ListedOpacity, Mode=OneWay}">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="48" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="48" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="22" />
                    </Grid.ColumnDefinitions>

                    <!--  Regular or package icon  -->
                    <TextBlock
                        Grid.Column="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        widgets:IconBuilder.Icon="Package"
                        FontSize="48"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind ExtendedTooltip, Mode=OneTime}"
                        Visibility="{x:Bind ShowDefaultPackageIcon, Mode=OneWay}" />

                    <Image
                        Grid.Column="0"
                        Width="44"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Source="{x:Bind MainIconSource, Mode=OneWay}"
                        ToolTipService.ToolTip="{x:Bind ExtendedTooltip, Mode=OneTime}"
                        Visibility="{x:Bind ShowCustomPackageIcon, Mode=OneWay}" />

                    <TextBlock
                        Grid.Column="0"
                        Margin="0,0,-4,-2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        widgets:IconBuilder.Icon="{x:Bind AlternateIconId, Mode=OneWay}"
                        FontSize="30"
                        FontWeight="ExtraLight"
                        Foreground="{ThemeResource AccentAAFillColorTertiaryBrush}"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind AlternateIdIconVisible, Mode=OneWay}" />

                    <TextBlock
                        Grid.Column="0"
                        Margin="0,0,-4,-2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        widgets:IconBuilder.Icon="{x:Bind MainIconId, Mode=OneWay}"
                        FontSize="30"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind AlternateIdIconVisible, Mode=OneWay}" />

                    <CheckBox
                        Grid.Column="2"
                        Margin="1,-4,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        IsChecked="{x:Bind IsChecked, Mode=TwoWay}" />

                    <Button
                        Grid.Column="2"
                        Width="22"
                        Height="22"
                        Padding="0"
                        VerticalAlignment="Bottom"
                        Background="Transparent"
                        BorderThickness="0"
                        Click="{x:Bind RightClick}"
                        Tapped="ContextMenuButton_Tapped">
                        <TextBlock widgets:IconBuilder.Glyph="&#xE712;" FontSize="18" />
                    </Button>

                    <TextBlock
                        Grid.Column="1"
                        VerticalAlignment="Top"
                        FontFamily="Segoe UI Variable Text"
                        FontSize="14"
                        FontWeight="SemiBold"
                        Text="{x:Bind Package.Name}"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}" />

                    <TextBlock
                        Grid.Column="1"
                        VerticalAlignment="Center"
                        FontFamily="Segoe UI Variable Text"
                        FontSize="11"
                        Opacity="0.8"
                        Text="{x:Bind Package.Id}"
                        ToolTipService.ToolTip="{x:Bind Package.Id}" />

                    <TextBlock
                        Grid.Column="1"
                        VerticalAlignment="Bottom"
                        FontFamily="Segoe UI Variable Text"
                        FontSize="11"
                        Opacity="0.5"
                        Text="{x:Bind VersionComboString, Mode=OneWay}"
                        ToolTipService.ToolTip="{x:Bind Package.VersionString}" />
                </Grid>
            </widgets:PackageItemContainer>
        </DataTemplate>

        <DataTemplate x:Key="PackageTemplate_Icons" x:DataType="pkgClasses:PackageWrapper">
            <widgets:PackageItemContainer
                AutomationProperties.Name="{x:Bind Package.AutomationName}"
                Background="{ThemeResource ControlFillColorDefaultBrush}"
                CornerRadius="4"
                DoubleTapped="{x:Bind PackageItemContainer_DoubleTapped}"
                Package="{x:Bind Package}"
                PreviewKeyDown="{x:Bind PackageItemContainer_PreviewKeyDown}"
                RightTapped="{x:Bind PackageItemContainer_RightTapped}"
                Wrapper="{x:Bind Self}">

                <Grid
                    Padding="4,4,4,4"
                    HorizontalAlignment="Stretch"
                    Opacity="{x:Bind ListedOpacity, Mode=OneWay}"
                    RowSpacing="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="22" />
                        <RowDefinition Height="60" />
                        <RowDefinition Height="30" />
                        <RowDefinition Height="15" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120" />
                    </Grid.ColumnDefinitions>

                    <!--  Regular or package icon  -->
                    <TextBlock
                        Grid.Row="1"
                        Height="64"
                        Margin="0,-8,0,4"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        widgets:IconBuilder.Icon="Package"
                        FontSize="74"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind ExtendedTooltip, Mode=OneTime}"
                        Visibility="{x:Bind ShowDefaultPackageIcon, Mode=OneWay}" />

                    <Image
                        Grid.Row="1"
                        Width="64"
                        Height="64"
                        Margin="0,-8,0,4"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Source="{x:Bind MainIconSource, Mode=OneWay}"
                        ToolTipService.ToolTip="{x:Bind ExtendedTooltip, Mode=OneTime}"
                        Visibility="{x:Bind ShowCustomPackageIcon, Mode=OneWay}" />

                    <TextBlock
                        Grid.Row="1"
                        Margin="0,0,20,-2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        widgets:IconBuilder.Icon="{x:Bind AlternateIconId, Mode=OneWay}"
                        FontSize="40"
                        FontWeight="ExtraLight"
                        Foreground="{ThemeResource AccentAAFillColorTertiaryBrush}"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind AlternateIdIconVisible, Mode=OneWay}" />

                    <TextBlock
                        Grid.Row="1"
                        Margin="0,0,20,-2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        widgets:IconBuilder.Icon="{x:Bind MainIconId, Mode=OneWay}"
                        FontSize="40"
                        FontWeight="ExtraLight"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}"
                        Visibility="{x:Bind AlternateIdIconVisible, Mode=OneWay}" />

                    <CheckBox
                        Grid.Row="0"
                        Margin="1,-4,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        IsChecked="{x:Bind IsChecked, Mode=TwoWay}" />
                    <Button
                        Grid.Row="0"
                        Width="22"
                        Height="22"
                        Padding="0"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Background="Transparent"
                        BorderThickness="0"
                        Click="{x:Bind RightClick}"
                        Tapped="ContextMenuButton_Tapped">
                        <TextBlock widgets:IconBuilder.Glyph="&#xE712;" FontSize="18" />
                    </Button>

                    <TextBlock
                        Grid.Row="2"
                        MaxWidth="120"
                        MaxHeight="30"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontFamily="Segoe UI Variable Text"
                        FontSize="12"
                        FontWeight="SemiBold"
                        HorizontalTextAlignment="Left"
                        Text="{x:Bind Package.Name}"
                        TextWrapping="Wrap"
                        ToolTipService.ToolTip="{x:Bind ListedNameTooltip, Mode=OneWay}" />

                    <TextBlock
                        Grid.Row="3"
                        HorizontalAlignment="Center"
                        FontFamily="Segoe UI Variable Text"
                        FontSize="11"
                        Opacity="0.5"
                        Text="{x:Bind VersionComboString, Mode=OneWay}"
                        ToolTipService.ToolTip="{x:Bind Package.VersionString}" />
                </Grid>
            </widgets:PackageItemContainer>
        </DataTemplate>

        <UniformGridLayout
            x:Key="Layout_Grid"
            ItemsStretch="Fill"
            MinColumnSpacing="8"
            MinItemHeight="56"
            MinItemWidth="275"
            MinRowSpacing="8" />

        <UniformGridLayout
            x:Key="Layout_Icons"
            ItemsJustification="Start"
            MinColumnSpacing="8"
            MinItemHeight="134"
            MinItemWidth="128"
            MinRowSpacing="8" />

        <StackLayout x:Key="Layout_List" Spacing="3" />

    </Page.Resources>

    <Grid Padding="0" BorderThickness="0">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="1000000*" MaxWidth="2000" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!--  HEADER  -->
        <Grid
            Name="MainHeader"
            Grid.Row="0"
            Grid.Column="1"
            ColumnSpacing="8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="80" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <FontIcon
                x:Name="HeaderIcon"
                Grid.Row="0"
                Grid.Column="0"
                Width="60"
                MinHeight="60"
                Margin="0,0,-8,0"
                FontSize="50"
                FontWeight="Normal" />

            <StackPanel
                Grid.Column="1"
                VerticalAlignment="Center"
                Orientation="Vertical"
                Spacing="0">
                <TextBlock
                    x:Name="MainTitle"
                    MaxHeight="70"
                    HorizontalAlignment="Left"
                    x:FieldModifier="protected"
                    FontFamily="Segoe UI Variable Display"
                    FontSize="30"
                    FontWeight="Bold"
                    TextWrapping="Wrap" />
                <TextBlock
                    x:Name="MainSubtitle"
                    MaxHeight="40"
                    HorizontalAlignment="Left"
                    x:FieldModifier="protected"
                    FontSize="11"
                    FontWeight="Normal"
                    Foreground="{ThemeResource AppBarItemDisabledForegroundThemeBrush}"
                    TextWrapping="Wrap" />
            </StackPanel>

            <Grid
                Grid.Row="0"
                Grid.Column="2"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                ColumnSpacing="8"
                RowSpacing="8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <StackPanel
                    x:Name="OrderByContainer"
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Orientation="Horizontal"
                    Spacing="4">
                    <widgets:TranslatedTextBlock VerticalAlignment="Center" Text="Order by:" />
                    <DropDownButton x:Name="OrderByButton">
                        <DropDownButton.Flyout>
                            <widgets:BetterMenu Placement="Bottom">
                                <widgets:BetterToggleMenuItem x:Name="OrderByName_Menu" Text="Name" />
                                <widgets:BetterToggleMenuItem x:Name="OrderById_Menu" Text="Id" />
                                <widgets:BetterToggleMenuItem x:Name="OrderByVer_Menu" Text="Version" />
                                <widgets:BetterToggleMenuItem
                                    x:Name="OrderByNewVer_Menu"
                                    Text="New version"
                                    Visibility="{x:Bind RoleIsUpdateLike}" />
                                <widgets:BetterToggleMenuItem x:Name="OrderBySrc_Menu" Text="Source" />
                                <MenuFlyoutSeparator />
                                <widgets:BetterToggleMenuItem x:Name="OrderAsc_Menu" Text="Ascendant" />
                                <widgets:BetterToggleMenuItem x:Name="OrderDesc_Menu" Text="Descendant" />
                            </widgets:BetterMenu>
                        </DropDownButton.Flyout>
                    </DropDownButton>
                </StackPanel>
                <StackPanel
                    x:Name="ViewModeContainer"
                    Grid.Row="1"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    VerticalAlignment="Center"
                    Orientation="Horizontal"
                    Spacing="4">
                    <widgets:TranslatedTextBlock VerticalAlignment="Center" Text="View mode:" />
                    <Toolkit:Segmented
                        x:Name="ViewModeSelector"
                        SelectionChanged="ViewModeSelector_SelectionChanged"
                        SelectionMode="Single">
                        <Toolkit:SegmentedItem x:Name="Selector_List">
                            <Toolkit:SegmentedItem.Icon><FontIcon Glyph="&#xE8FD;"/></Toolkit:SegmentedItem.Icon>
                        </Toolkit:SegmentedItem>
                        <Toolkit:SegmentedItem x:Name="Selector_Grid">
                            <Toolkit:SegmentedItem.Icon><FontIcon Glyph="&#xF168;"/></Toolkit:SegmentedItem.Icon>
                        </Toolkit:SegmentedItem>
                        <Toolkit:SegmentedItem x:Name="Selector_Icons">
                            <Toolkit:SegmentedItem.Icon><FontIcon Glyph="&#xF0E2;"/></Toolkit:SegmentedItem.Icon>
                        </Toolkit:SegmentedItem>
                    </Toolkit:Segmented>
                </StackPanel>
                <Button
                    x:Name="ReloadButton"
                    Grid.Column="1"
                    Width="32"
                    Height="32"
                    Padding="0"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Center"
                    x:FieldModifier="protected"
                    AutomationProperties.HelpText="Reload packages"
                    CornerRadius="4">
                    <FontIcon FontSize="16" Glyph="&#xE72C;" />
                </Button>
            </Grid>

            <Grid
                Grid.Row="3"
                Grid.Column="0"
                Grid.ColumnSpan="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <StackPanel
                    x:Name="ToggleFiltersButtonWidth"
                    Margin="0,0,13,0"
                    Orientation="Horizontal">
                    <ToggleButton
                        x:Name="ToggleFiltersButton"
                        Grid.Column="0"
                        Height="36"
                        Margin="1,4"
                        Padding="8,4"
                        x:FieldModifier="protected"
                        Background="Transparent"
                        BorderThickness="0"
                        Click="ToggleFiltersButton_Click"
                        CornerRadius="4"
                        Foreground="{ThemeResource TextFillColorPrimaryBrush}">
                        <ToggleButton.Resources>
                            <ResourceDictionary>
                                <StaticResource x:Key="ToggleButtonBackgroundChecked" ResourceKey="ControlFillColorDefaultBrush" />
                                <StaticResource x:Key="ToggleButtonBackgroundCheckedPointerOver" ResourceKey="ControlFillColorSecondaryBrush" />
                                <StaticResource x:Key="ToggleButtonBackgroundCheckedPressed" ResourceKey="ControlFillColorTertiaryBrush" />
                            </ResourceDictionary>
                        </ToggleButton.Resources>
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            <FontIcon
                                FontSize="20"
                                Foreground="{ThemeResource TextFillColorPrimaryBrush}"
                                Glyph="&#xE71C;" />
                            <widgets:TranslatedTextBlock
                                VerticalAlignment="Center"
                                FontSize="12"
                                FontWeight="Medium"
                                Foreground="{ThemeResource TextFillColorPrimaryBrush}"
                                Text="Filters" />
                        </StackPanel>
                    </ToggleButton>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button
                        x:Name="MainToolbarButton"
                        Height="36"
                        Margin="0,0,1,0"
                        x:FieldModifier="protected"
                        BorderThickness="0"
                        CornerRadius="4,0,0,4">
                        <StackPanel
                            VerticalAlignment="Stretch"
                            Orientation="Horizontal"
                            Spacing="4">
                            <widgets:LocalIcon
                                x:Name="MainToolbarButtonIcon"
                                VerticalAlignment="Center"
                                x:FieldModifier="protected" />
                            <TextBlock
                                x:Name="MainToolbarButtonText"
                                VerticalAlignment="Center"
                                x:FieldModifier="protected"
                                FontSize="12"
                                FontWeight="SemiBold" />
                        </StackPanel>
                    </Button>
                    <Button
                        x:Name="MainToolbarButtonDropdown"
                        Width="30"
                        Height="36"
                        Padding="4"
                        x:FieldModifier="protected"
                        BorderThickness="0"
                        CornerRadius="0,4,4,0">
                        <FontIcon FontSize="14" Glyph="&#xE70D;" />
                    </Button>
                </StackPanel>

                <CommandBar
                    Name="ToolBar"
                    Grid.Column="2"
                    HorizontalAlignment="Left"
                    x:FieldModifier="protected"
                    DefaultLabelPosition="Right" />
            </Grid>
        </Grid>


        <!--  NON-HEADER CONTENT  -->

        <!--  FILTERING PANE  -->

        <SplitView
            Name="FilteringPanel"
            Grid.Row="2"
            Grid.Column="1"
            BorderThickness="0"
            CornerRadius="0,0,0,0"
            DisplayMode="Inline"
            PaneBackground="Transparent"
            PaneClosing="FilteringPanel_PaneClosing"
            PanePlacement="Left"
            SizeChanged="FilteringPanel_SizeChanged">
            <SplitView.Pane>
                <ScrollViewer
                    Name="SidePanel"
                    Grid.Row="1"
                    Grid.Column="0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    BorderBrush="{ThemeResource AccentAAFillColorDefaultBrush}"
                    CornerRadius="0,8,8,0"
                    HorizontalScrollMode="Disabled"
                    SizeChanged="SidepanelWidth_SizeChanged">
                    <Grid Name="SidePanelGrid" RowSpacing="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Sources Expander  -->
                        <Expander
                            Grid.Row="0"
                            Grid.Column="0"
                            Padding="4,4,4,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            AutomationProperties.Name="Filter by sources"
                            Background="{ThemeResource SystemFillColorNeutralBackgroundBrush}"
                            CornerRadius="8"
                            IsExpanded="True">
                            <Expander.Header>
                                <StackPanel Orientation="Horizontal" Spacing="8">
                                    <FontIcon FontSize="20" Glyph="&#xE74C;" />
                                    <widgets:TranslatedTextBlock
                                        VerticalAlignment="Center"
                                        FontWeight="SemiBold"
                                        Text="Sources" />
                                </StackPanel>
                            </Expander.Header>
                            <Expander.Content>
                                <StackPanel Orientation="Vertical">
                                    <widgets:TranslatedTextBlock
                                        x:Name="SourcesPlaceholderText"
                                        MinHeight="60"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        HorizontalContentAlignment="Center"
                                        VerticalContentAlignment="Center"
                                        FontSize="15"
                                        FontWeight="Bold"
                                        Opacity="0.5"
                                        Text="Search for packages to start"
                                        WrappingMode="Wrap" />
                                    <Grid
                                        Name="SourcesTreeViewGrid"
                                        HorizontalAlignment="Stretch"
                                        ColumnSpacing="4"
                                        RowSpacing="4"
                                        Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <HyperlinkButton
                                            Name="SelectAllSourcesButton"
                                            Grid.Column="0"
                                            Padding="2"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Center"
                                            Click="SelectAllSourcesButton_Click">
                                            <widgets:TranslatedTextBlock
                                                FontSize="12"
                                                FontWeight="SemiBold"
                                                Text="Select all" />
                                        </HyperlinkButton>
                                        <HyperlinkButton
                                            Name="ClearSourceSelectionButton"
                                            Grid.Column="1"
                                            Padding="2"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Center"
                                            Click="ClearSourceSelectionButton_Click">
                                            <widgets:TranslatedTextBlock
                                                FontSize="12"
                                                FontWeight="SemiBold"
                                                Text="Clear selection" />
                                        </HyperlinkButton>
                                        <TreeView
                                            Name="SourcesTreeView"
                                            Grid.Row="1"
                                            Grid.Column="0"
                                            Grid.ColumnSpan="2"
                                            Padding="0"
                                            HorizontalAlignment="Stretch"
                                            SelectionChanged="SourcesTreeView_SelectionChanged"
                                            SelectionMode="Multiple" />
                                    </Grid>

                                </StackPanel>
                            </Expander.Content>
                        </Expander>
                        <!--  Filter Options Expander  -->
                        <Expander
                            Grid.Row="1"
                            Grid.Column="0"
                            Padding="16,8,16,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            AutomationProperties.Name="Filter options"
                            Background="{ThemeResource SystemFillColorNeutralBackgroundBrush}"
                            CornerRadius="8"
                            IsExpanded="False">
                            <Expander.Header>
                                <StackPanel Orientation="Horizontal" Spacing="8">
                                    <FontIcon FontSize="20" Glyph="&#xE71C;" />
                                    <widgets:TranslatedTextBlock
                                        VerticalAlignment="Center"
                                        FontWeight="SemiBold"
                                        Text="Filters" />
                                </StackPanel>
                            </Expander.Header>
                            <Expander.Content>
                                <StackPanel
                                    HorizontalAlignment="Stretch"
                                    Orientation="Vertical"
                                    Spacing="0">
                                    <CheckBox
                                        x:Name="InstantSearchCheckbox"
                                        x:FieldModifier="protected"
                                        Checked="InstantSearchValueChanged"
                                        Unchecked="InstantSearchValueChanged">
                                        <CheckBox.Content>
                                            <widgets:TranslatedTextBlock Text="Instant search" WrappingMode="Wrap" />
                                        </CheckBox.Content>
                                    </CheckBox>
                                    <CheckBox
                                        x:Name="UpperLowerCaseCheckbox"
                                        Checked="FilterOptionsChanged"
                                        Unchecked="FilterOptionsChanged">
                                        <CheckBox.Content>
                                            <widgets:TranslatedTextBlock Text="Distinguish between uppercase and lowercase" WrappingMode="Wrap" />
                                        </CheckBox.Content>
                                    </CheckBox>
                                    <CheckBox
                                        x:Name="IgnoreSpecialCharsCheckbox"
                                        Checked="FilterOptionsChanged"
                                        IsChecked="True"
                                        Unchecked="FilterOptionsChanged">
                                        <CheckBox.Content>
                                            <widgets:TranslatedTextBlock Text="Ignore special characters" WrappingMode="Wrap" />
                                        </CheckBox.Content>
                                    </CheckBox>
                                </StackPanel>
                            </Expander.Content>
                        </Expander>
                        <!--  Search mode expander  -->
                        <Expander
                            Grid.Row="2"
                            Grid.Column="0"
                            Padding="16,8,16,8"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            AutomationProperties.Name="Search mode"
                            Background="{ThemeResource SystemFillColorNeutralBackgroundBrush}"
                            CornerRadius="8"
                            IsExpanded="True">
                            <Expander.Header>
                                <StackPanel Orientation="Horizontal" Spacing="8">
                                    <FontIcon FontSize="20" Glyph="&#xE773;" />
                                    <widgets:TranslatedTextBlock
                                        VerticalAlignment="Center"
                                        FontWeight="SemiBold"
                                        Text="Search mode" />
                                </StackPanel>
                            </Expander.Header>
                            <Expander.Content>
                                <StackPanel
                                    HorizontalAlignment="Stretch"
                                    Orientation="Vertical"
                                    Spacing="0">
                                    <RadioButtons
                                        Name="QueryOptionsGroup"
                                        Margin="0,-4,0,6"
                                        x:FieldModifier="protected"
                                        CharacterSpacing="0"
                                        SelectionChanged="FilterOptionsChanged">
                                        <RadioButton
                                            x:Name="QueryNameRadio"
                                            Height="30"
                                            Margin="0,-2,0,-2"
                                            x:FieldModifier="protected">
                                            <RadioButton.Content>
                                                <widgets:TranslatedTextBlock Text="Package Name" WrappingMode="Wrap" />
                                            </RadioButton.Content>
                                        </RadioButton>
                                        <RadioButton
                                            x:Name="QueryIdRadio"
                                            Height="30"
                                            Margin="0,-2,0,-2"
                                            x:FieldModifier="protected">
                                            <RadioButton.Content>
                                                <widgets:TranslatedTextBlock Text="Package ID" WrappingMode="Wrap" />
                                            </RadioButton.Content>
                                        </RadioButton>
                                        <RadioButton
                                            x:Name="QueryBothRadio"
                                            Height="30"
                                            Margin="0,-2,0,-2"
                                            x:FieldModifier="protected"
                                            IsChecked="True">
                                            <RadioButton.Content>
                                                <widgets:TranslatedTextBlock Text="Both" WrappingMode="Wrap" />
                                            </RadioButton.Content>
                                        </RadioButton>
                                        <RadioButton
                                            x:Name="QueryExactMatch"
                                            Height="30"
                                            Margin="0,-2,0,-2"
                                            x:FieldModifier="protected">
                                            <RadioButton.Content>
                                                <widgets:TranslatedTextBlock Text="Exact match" WrappingMode="Wrap" />
                                            </RadioButton.Content>
                                        </RadioButton>
                                        <RadioButton
                                            x:Name="QuerySimilarResultsRadio"
                                            Height="30"
                                            Margin="0,-2,0,-2"
                                            x:FieldModifier="protected">
                                            <RadioButton.Content>
                                                <widgets:TranslatedTextBlock Text="Show similar packages" WrappingMode="Wrap" />
                                            </RadioButton.Content>
                                        </RadioButton>
                                    </RadioButtons>
                                </StackPanel>
                            </Expander.Content>
                        </Expander>
                    </Grid>
                </ScrollViewer>
            </SplitView.Pane>
            <Grid Name="PackagesListGrid" Margin="0">
                <ProgressBar
                    x:Name="LoadingProgressBar"
                    Margin="1,-6,1,0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Top"
                    x:FieldModifier="protected"
                    IsIndeterminate="True"
                    Visibility="Visible" />

                <!--  ACTUAL PACKAGE LIST  -->
                <Grid
                    Padding="4,6,4,6"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Background="{ThemeResource SystemFillColorNeutralBackgroundBrush}"
                    BorderBrush="{StaticResource ExpanderContentBorderBrush}"
                    BorderThickness="1"
                    CornerRadius="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="37" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid
                        Grid.Row="0"
                        Margin="4,2,4,5"
                        ColumnSpacing="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="8" />
                            <ColumnDefinition Width="32" />
                            <ColumnDefinition Width="28" />
                            <ColumnDefinition Width="*" MinWidth="100" />
                            <ColumnDefinition Width="32" />
                            <ColumnDefinition Width="*" MinWidth="100" />
                            <ColumnDefinition Width="32" />
                            <ColumnDefinition Width="*" MaxWidth="125" />
                            <ColumnDefinition Width="32" MaxWidth="{x:Bind NewVersionIconWidth}" />
                            <ColumnDefinition Width="*" MaxWidth="{x:Bind NewVersionLabelWidth}" />
                            <ColumnDefinition Width="32" />
                            <ColumnDefinition Width="*" MaxWidth="150" />
                            <ColumnDefinition Width="11" />
                        </Grid.ColumnDefinitions>
                        <Button
                            Name="CheckboxHeader"
                            Grid.Column="0"
                            Grid.ColumnSpan="3"
                            Padding="0"
                            HorizontalAlignment="Stretch"
                            BorderThickness="0"
                            CornerRadius="4,0,0,4">
                            <CheckBox
                                Name="SelectAllCheckBox"
                                Margin="12,4,4,4"
                                Checked="SelectAllCheckBox_ValueChanged"
                                Unchecked="SelectAllCheckBox_ValueChanged" />
                        </Button>
                        <Button
                            Name="NameHeader"
                            Grid.Column="3"
                            Grid.ColumnSpan="1"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            BorderThickness="0"
                            CornerRadius="0" />
                        <Button
                            Name="IdHeader"
                            Grid.Column="4"
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            BorderThickness="0"
                            CornerRadius="0" />
                        <Button
                            Name="VersionHeader"
                            Grid.Column="6"
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            BorderThickness="0"
                            CornerRadius="0" />
                        <Button
                            Name="NewVersionHeader"
                            Grid.Column="8"
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            BorderThickness="0"
                            CornerRadius="0"
                            Visibility="{x:Bind RoleIsUpdateLike}" />
                        <Button
                            Name="SourceHeader"
                            Grid.Column="10"
                            Grid.ColumnSpan="3"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            BorderThickness="0"
                            CornerRadius="0,4,4,0" />
                    </Grid>

                    <Toolkit:SwitchPresenter
                        Grid.Row="1"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        TargetType="x:Int32"
                        Value="{x:Bind ViewModeSelector.SelectedIndex, Mode=OneWay}">
                        <Toolkit:Case IsDefault="True" Value="0">
                            <ItemsView
                                x:Name="PackageList_List"
                                Padding="4,0"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                x:FieldModifier="protected"
                                CanBeScrollAnchor="False"
                                CharacterReceived="PackageList_CharacterReceived"
                                ItemTemplate="{StaticResource PackageTemplate_List}"
                                ItemsSource="{x:Bind FilteredPackages}"
                                Layout="{StaticResource Layout_List}" />
                        </Toolkit:Case>
                        <Toolkit:Case Value="1">
                            <ItemsView
                                x:Name="PackageList_Grid"
                                Padding="4,0"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                x:FieldModifier="protected"
                                CanBeScrollAnchor="False"
                                CharacterReceived="PackageList_CharacterReceived"
                                ItemTemplate="{StaticResource PackageTemplate_Grid}"
                                ItemsSource="{x:Bind FilteredPackages}"
                                Layout="{StaticResource Layout_Grid}" />
                        </Toolkit:Case>
                        <Toolkit:Case Value="2">
                            <ItemsView
                                x:Name="PackageList_Icons"
                                Padding="4,0"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                x:FieldModifier="protected"
                                CanBeScrollAnchor="False"
                                CharacterReceived="PackageList_CharacterReceived"
                                ItemTemplate="{StaticResource PackageTemplate_Icons}"
                                ItemsSource="{x:Bind FilteredPackages}"
                                Layout="{StaticResource Layout_Icons}" />
                        </Toolkit:Case>
                    </Toolkit:SwitchPresenter>


                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            x:Name="BackgroundText"
                            Grid.Row="1"
                            Grid.Column="1"
                            VerticalAlignment="Center"
                            FontFamily="Segoe UI Variable Display"
                            FontSize="30"
                            FontWeight="Bold"
                            Opacity="0.5"
                            Text="Hello World Lorem Ipsum dolor sit amet"
                            TextAlignment="Center"
                            TextWrapping="Wrap" />
                    </Grid>

                </Grid>

                <!--  Filters/Packages Splitter  -->
                <Toolkit:PropertySizer
                    x:Name="FiltersResizer"
                    Width="12"
                    Margin="-12,0,0,0"
                    Padding="0,0,0,0"
                    HorizontalAlignment="Left"
                    Binding="{x:Bind FilteringPanel.OpenPaneLength, Mode=TwoWay}"
                    CornerRadius="2"
                    Visibility="{x:Bind FilteringPanel.IsPaneOpen, Mode=OneWay}">
                    <Toolkit:PropertySizer.RenderTransform>
                        <TranslateTransform X="0" />
                    </Toolkit:PropertySizer.RenderTransform>
                </Toolkit:PropertySizer>


                <!--  MEGA QUERY BLOCK  -->
                <Grid
                    x:Name="MegaQueryBlockGrid"
                    Grid.Row="1"
                    Grid.Column="1"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    x:FieldModifier="protected"
                    Visibility="Collapsed">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="80" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="8*" MaxWidth="800" />
                        <ColumnDefinition Width="80" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <TextBox
                        x:Name="MegaQueryBlock"
                        Grid.Row="1"
                        Grid.Column="1"
                        Padding="20,11,10,11"
                        x:FieldModifier="protected"
                        CornerRadius="8,0,0,8"
                        FontSize="40"
                        FontWeight="SemiBold" />
                    <Button
                        x:Name="MegaFindButton"
                        Grid.Row="1"
                        Grid.Column="2"
                        Width="80"
                        Height="80"
                        Padding="12"
                        x:FieldModifier="protected"
                        AutomationProperties.HelpText="Search"
                        CornerRadius="0,8,8,0">
                        <AnimatedIcon>
                            <AnimatedIcon.Source>
                                <animatedvisuals:AnimatedFindVisualSource />
                            </AnimatedIcon.Source>
                            <AnimatedIcon.FallbackIconSource>
                                <SymbolIconSource Symbol="Find" />
                            </AnimatedIcon.FallbackIconSource>
                        </AnimatedIcon>
                    </Button>

                </Grid>
            </Grid>
        </SplitView>
    </Grid>
</Page>
