name: Publish prereleases to WinGet

on:
  release:
    types: [published]  # Trigger on published release (includes prereleases)
  workflow_dispatch:

jobs:
  publish:
    if: github.event.release.prerelease == true  # Ensure this only runs for prereleases
    runs-on: ubuntu-latest

    steps:
      - name: Publish to WinGet
        uses: vedantmgoyal9/winget-releaser@19e706d4c9121098010096f9c495a70a7518b30f
        with:
          identifier: MartiCliment.UniGetUI.Pre-Release
          installers-regex: 'UniGetUI\.Installer\.exe$'
          version: ${{ github.event.release.tag_name }}
          token: ${{ secrets.WINGET_TOKEN }}
