{"\"{0}\" is a local package and can't be shared": "\"{0}\" est un paquet local et ne peut pas être partagé", "\"{0}\" is a local package and does not have available details": "\"{0}\" est un paquet local et n'a pas de détails disponibles.", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" est un paquet local et n'est pas compatible avec cette fonctionnalité", "(Last checked: {0})": "(Dernière vérification : {0})", "(Number {0} in the queue)": "(Numéro {0} dans la file d'attente)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON>, <PERSON><PERSON><PERSON>, @W1L7dev, BreatFR, @PikPakPik, @Entropiness", "0 packages found": "<PERSON><PERSON><PERSON> paquet trouvé", "0 updates found": "<PERSON><PERSON>ne mise à jour trouvée", "1 - Errors": "1 - Erreurs", "1 day": "1 jour", "1 hour": "1 heure", "1 month": "1 mois", "1 package was found": "1 paquet a été trouvé", "1 update is available": "1 mise à jour est disponible", "1 week": "1 semaine", "1 year": "1 an", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON><PERSON> jusqu'à la page \"{0}\" ou \"{1}\" .", "2 - Warnings": "2 - Avertissements", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON> le(s) paquet(s) que vous souhaitez ajouter au lot et cochez leur case la plus à gauche.", "3 - Information (less)": "3 - Information (moins)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON><PERSON> les paquets que vous souhaitez ajouter au lot sont sélectionnés, recherchez et cliquez sur l'option \"{0}\" dans la barre d'outils.", "4 - Information (more)": "4 - Information (plus)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Vos paquets auront été ajoutés au lot. V<PERSON> pouvez continuer à ajouter des paquets ou exporter le lot.", "5 - information (debug)": "5 - Information (débogage)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Un gestionnaire populaire de bibliothèques C/C++. Plein de bibliothèques C/C++ et d'autres utilitaires liés à C/C++.<br>Contient : <b>Bibliothèques C/C++ et utilitaires liés</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Un dépôt plein d'outils et d'exécutables conçus pour l'écosystème Microsoft .NET.<br>Contient : <b>Outils et scripts liés à .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Un dépôt plein d'outils conçus avec l'écosystème Microsoft .NET.<br>Contient : <b>Outils liés à .NET</b>", "A restart is required": "Un redémarrage est requis", "Abort install if pre-install command fails": "Abandonner l'installation si la commande de pré-installation échoue", "Abort uninstall if pre-uninstall command fails": "Abandonner la désinstallation si la commande de pré-désinstallation échoue", "Abort update if pre-update command fails": "Abandonner la mise à jour si la commande de pré-mise à jour échoue", "About": "À propos", "About Qt6": "À propos de Qt6", "About WingetUI": "À propos de WingetUI", "About WingetUI version {0}": "À propos de WingetUI version {0}", "About the dev": "À propos du développeur", "Accept": "Accepter", "Action when double-clicking packages, hide successful installations": "Action lors du double-clic sur les paquets, cacher les installations réussies", "Add": "Ajouter", "Add a source to {0}": "Ajouter une source à {0}", "Add a timestamp to the backup file names": "Ajouter un horodatage aux noms des fichiers de sauvegarde", "Add a timestamp to the backup files": "Ajouter un horodatage aux fichiers de sauvegarde", "Add packages or open an existing bundle": "Ajouter des paquets ou ouvrir un lot existant", "Add packages or open an existing package bundle": "Ajouter des paquets ou ouvrir un lot de paquets existant", "Add packages to bundle": "Ajouter des paquets au lot", "Add packages to start": "Ajouter des paquets pour commencer", "Add selection to bundle": "Ajouter la sélection à un lot", "Add source": "Ajouter une source", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Ajouter les mises à jour qui échouent avec un message « aucune mise à jour applicable trouvée » à la liste des mises à jour ignorées.", "Adding source {source}": "Ajouter la source {source}", "Adding source {source} to {manager}": "Ajouter la source {source} à {manager}", "Addition succeeded": "Ajouté avec succès", "Administrator privileges": "Privilèges d'administrateur", "Administrator privileges preferences": "Paramètres des droits d'administrateur", "Administrator rights": "Droits d'administrateur", "Administrator rights and other dangerous settings": "Droits d'administrateur et autres paramètres dangereux", "Advanced options": "Options avancées", "All files": "To<PERSON> les fichiers", "All versions": "Toutes les versions", "Allow changing the paths for package manager executables": "Permettre de modifier les chemins d'accès aux exécutables du gestionnaire de paquets", "Allow custom command-line arguments": "Autoriser les arguments de ligne de commande personnalisés", "Allow importing custom command-line arguments when importing packages from a bundle": "Autoriser l'importation d'arguments de ligne de commande personnalisés lors de l'importation de packages à partir d'un bundle", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Permettre l'importation de commandes de pré-installation et de post-installation personnalisées lors de l'importation de paquets par lot", "Allow package operations to be performed in parallel": "Autoriser l'exécution en parallèle des opérations sur les paquets", "Allow parallel installs (NOT RECOMMENDED)": "Autoriser les installations parallèles (NON RECOMMANDÉ) ", "Allow pre-release versions": "Autoriser les pré-versions", "Allow {pm} operations to be performed in parallel": "Autoriser les opérations de {pm} à s'exécuter en parallèle", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "<PERSON><PERSON><PERSON>, vous pouvez également installer {0} en exécutant la commande suivante dans une invite Windows PowerShell :", "Always elevate {pm} installations by default": "Toujours élever les installations {pm} par défaut", "Always run {pm} operations with administrator rights": "Toujours exécuter les opérations de {pm} avec les droits d'administrateur", "An error occurred": "Une erreur s'est produite", "An error occurred when adding the source: ": "Une erreur s'est produite lors de l'ajout de la source :", "An error occurred when attempting to show the package with Id {0}": "Une erreur s'est produite lors de la tentative d'afficher le paquet avec l'ID {0}", "An error occurred when checking for updates: ": "Une erreur s'est produite lors de la vérification des mises à jour :", "An error occurred while attempting to create an installation script:": "Une erreur s'est produite lors de la création d'un script d'installation :", "An error occurred while loading a backup: ": "Une erreur s'est produite lors du chargement d'une sauvegarde :", "An error occurred while logging in: ": "Une erreur s'est produite lors de la connexion :", "An error occurred while processing this package": "Une erreur s'est produite lors du traitement du paquet", "An error occurred:": "Une erreur s'est produite :", "An interal error occurred. Please view the log for further details.": "Une erreur interne s'est produite. Veuillez consulter les journaux pour plus de détails.", "An unexpected error occurred:": "Une erreur inattendue s'est produite :", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Une problème inattendu s'est produit lors de la tentative de réparation de WinGet. Veuillez réessayer plus tard.", "An update was found!": "Une mise à jour a été trouvée !", "Android Subsystem": "Sous-système Android", "Another source": "Autre source", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Tout nouveau raccourci créé lors d'une installation ou d'une mise à jour sera automatiquement supprimé, au lieu d'afficher une demande de confirmation la première fois qu'il est détecté.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Tout raccourci créé ou modifié en dehors d'UniGetUI sera ignoré. Vous pourrez les ajouter via le bouton  {0} pour les ajouter.", "Any unsaved changes will be lost": "Toute modification non enregistrée sera perdue", "App Name": "Nom de l'application", "Appearance": "Apparence", "Application theme, startup page, package icons, clear successful installs automatically": "Thème de l'application, page de démarrage, icônes des paquets, nettoyage automatique des installations réussies", "Application theme:": "Thème de l'application :", "Apply": "Appliquer", "Architecture to install:": "Architecture à installer :", "Are these screenshots wron or blurry?": "<PERSON><PERSON> captures d'é<PERSON>ran sont-elles fausses ou floues ?", "Are you really sure you want to enable this feature?": "Êtes-vous vraiment sûr de vouloir activer cette fonction ?", "Are you sure you want to create a new package bundle? ": "Êtes-vous sûr de vouloir créer un nouveau lot de paquets ?", "Are you sure you want to delete all shortcuts?": "Êtes-vous sûr de vouloir supprimer tous les raccourcis ?", "Are you sure?": "Êtes-vous sûr ?", "Ascendant": "Ascendant", "Ask for administrator privileges once for each batch of operations": "Demander les privilèges d'administrateur une seule fois pour chaque lot d'opérations", "Ask for administrator rights when required": "De<PERSON>er les droits d'administrateur lorsque c'est nécessaire", "Ask once or always for administrator rights, elevate installations by default": "Demander une fois ou à chaque fois pour les droits d'administrateur, élever les installations par défaut", "Ask only once for administrator privileges": "Ne demander qu'une seule fois les privilèges d'administrateur", "Ask only once for administrator privileges (not recommended)": "De<PERSON><PERSON> seulement une fois pour les privilèges d'administrateur (non recommandé)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Demander la suppression des raccourcis sur le bureau créés lors d'une installation ou d'une mise à jour", "Attention required": "Attention requise", "Authenticate to the proxy with an user and a password": "S'authentifier auprès du proxy avec un utilisateur et un mot de passe", "Author": "<PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "Suppression automatique des raccourcis sur le bureau", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Enregistrer automatiquement une liste de tous les paquets installés pour les restaurer facilement", "Automatically save a list of your installed packages on your computer.": "Enregistrer automatiquement une liste de vos paquets installés sur votre ordinateur.", "Autostart WingetUI in the notifications area": "Démarrer automatiquement WingetUI dans la zone de notification", "Available Updates": "Mises à jour disponibles", "Available updates: {0}": "Mises à jour disponibles : {0}", "Available updates: {0}, not finished yet...": "Mises à jour disponibles : {0}, pas encore terminé...", "Backing up packages to GitHub Gist...": "Sauvegarde des paquets sur GitHub Gist...", "Backup": "<PERSON><PERSON><PERSON><PERSON>", "Backup Failed": "La sauvegarde a échoué", "Backup Successful": "La sauvegarde a réussi", "Backup and Restore": "Sauvegarde et restauration", "Backup installed packages": "Sauvegarder les paquets installés", "Backup location": "Emplacement de la sauvegarde", "Become a contributor": "<PERSON><PERSON><PERSON> un <PERSON>ur", "Become a translator": "<PERSON><PERSON><PERSON> un traducteur", "Begin the process to select a cloud backup and review which packages to restore": "Commencer le processus de sélection d'une sauvegarde dans le cloud et examiner les paquets à restaurer.", "Beta features and other options that shouldn't be touched": "Fonctionnalités bêta et autres options qui ne devraient pas être modifiées", "Both": "Les deux", "Bundle security report": "Rapport de sécurité du lot", "But here are other things you can do to learn about WingetUI even more:": "Mais voici d'autres choses que vous pouvez faire pour en savoir encore plus sur UniGetUI :", "By toggling a package manager off, you will no longer be able to see or update its packages.": "En désactivant un gestionnaire de paquets, vous ne pourrez plus voir ou mettre à jour ses paquets.", "Cache administrator rights and elevate installers by default": "Mettre en cache les droits d'administrateur et élever les installateurs par défaut", "Cache administrator rights, but elevate installers only when required": "Mettre en cache les droits d'administrateur, mais <PERSON><PERSON>er les installateurs seulement quand c'est nécessaire", "Cache was reset successfully!": "Le cache a été réinitialisé avec succès !", "Can't {0} {1}": "Impossible de {0} {1}", "Cancel": "Annuler", "Cancel all operations": "Annuler toutes les opérations", "Change backup output directory": "Modifier le répertoire de la sauvegarde", "Change default options": "Modifier les options par défaut", "Change how UniGetUI checks and installs available updates for your packages": "Modifier comment UniGetUI vérifie et installe les mises à jour disponibles pour vos paquets", "Change how UniGetUI handles install, update and uninstall operations.": "Modifier la façon dont UniGetUI gère les opérations d'installation, de mise à jour et de désinstallation.", "Change how UniGetUI installs packages, and checks and installs available updates": "Modifier la façon dont UniGetUI installe les paquets, vérifie et installe les mises à jour disponibles", "Change how operations request administrator rights": "Modifier la façon dont les opérations demandent des droits d'administrateur", "Change install location": "Changer l'emplacement d'installation", "Change this": "Modifier ceci", "Change this and unlock": "Modifier ceci et déverrouiller", "Check for package updates periodically": "Vérifier les mises à jour des paquets périodiquement", "Check for updates": "Vérifier les mises à jour", "Check for updates every:": "Vérifier les mises à jour toutes les :", "Check for updates periodically": "Vérifier les mises à jour périodiquement", "Check for updates regularly, and ask me what to do when updates are found.": "Vérifier les mises à jour régulièrement, et me demander quoi faire lorsque des mises à jour sont trouvées.", "Check for updates regularly, and automatically install available ones.": "Vérifier les mises à jour régulièrement, et installer automatiquement celles disponibles.", "Check out my {0} and my {1}!": "Consul<PERSON>z mon {0} et ma {1} !", "Check out some WingetUI overviews": "Découvrez quelques aperçus de WingetUI", "Checking for other running instances...": "Vérification d'autres instances en cours d'exécution...", "Checking for updates...": "Vérification des mises à jour...", "Checking found instace(s)...": "Vérification des instances trouvées...", "Choose how many operations shouls be performed in parallel": "Choisir le nombre d'opérations à effectuer en parallèle", "Clear cache": "Vider le cache", "Clear finished operations": "Effacer les opérations terminées", "Clear selection": "Effacer la sélection", "Clear successful operations": "Nettoyer les opérations réussies", "Clear successful operations from the operation list after a 5 second delay": "Effacer les opérations réussies de la liste des opérations après un délai de 5 secondes", "Clear the local icon cache": "Vider le cache des icônes", "Clearing Scoop cache - WingetUI": "Vidage du cache de Scoop - WingetUI", "Clearing Scoop cache...": "Nettoyage du cache de Scoop...", "Click here for more details": "Cliquez ici pour plus de détails", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Cliquez sur Installer pour commencer le processus d'installation. Si vous passez l'installation, UniGetUI risque de ne pas fonctionner comme prévu.", "Close": "<PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Fermer UniGetUI dans la barre d'état système", "Close WingetUI to the notification area": "Fermer WingetUI dans la zone de notification", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "La sauvegarde dans le cloud utilise une Gist GitHub privée pour stocker la liste des paquets installés.", "Cloud package backup": "Sauvegarde des paquets dans le cloud", "Command-line Output": "Sortie de la ligne de commande", "Command-line to run:": "Ligne de commande à exécuter :", "Compare query against": "Comparer la requête avec", "Compatible with authentication": "Compatible avec l'authentification", "Compatible with proxy": "Compatible avec le proxy", "Component Information": "Informations sur les composants", "Concurrency and execution": "Concurrence et exécution", "Connect the internet using a custom proxy": "Se connecter à Internet à l'aide d'un proxy personnalisé", "Continue": "<PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Contribuer au dépôt d'icônes et de captures d'écran", "Contributors": "Contributeurs", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Copier dans le presse-papiers", "Could not add source": "Impossible d'ajouter une source", "Could not add source {source} to {manager}": "Impossible d'ajouter la source {source} à {manager}", "Could not back up packages to GitHub Gist: ": "Impossible de sauvegarder les paquets sur GitHub Gist :", "Could not create bundle": "Impossible de créer un lot", "Could not load announcements - ": "Impossible de charger les annonces - ", "Could not load announcements - HTTP status code is $CODE": "Impossible de charger les annonces - Le code de statut HTTP est $CODE", "Could not remove source": "Impossible de supprimer la source", "Could not remove source {source} from {manager}": "Impossible de supprimer la source {source} de {manager}", "Could not remove {source} from {manager}": "Impossible de supprimer {source} depuis {manager}", "Create .ps1 script": "C<PERSON>er un script .ps1", "Credentials": "Informations d'identification", "Current Version": "Version actuelle", "Current status: Not logged in": "Statut actuel : Non connecté", "Current user": "Utilisateur actuel", "Custom arguments:": "Arguments personnalisés :", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Les arguments de ligne de commande personnalisés peuvent modifier la manière dont les programmes sont installés, mis à jour ou désinstallés, d'une manière qu'UniGetUI ne peut pas contrôler. Leur utilisation peut endommager les paquets. Soyez prudent.", "Custom command-line arguments:": "Arguments personnalisés de la ligne de commande :", "Custom install arguments:": "Arguments d'installation personnalisés :", "Custom uninstall arguments:": "Arguments de désinstallation personnalisés :", "Custom update arguments:": "Arguments de mise à jour personnalisés :", "Customize WingetUI - for hackers and advanced users only": "Customiser WingetUI - pour les hackers et les utilisateurs avancés seulement", "DEBUG BUILD": "VERSION DE DÉBOGAGE", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "AVERTISSEMENT : NOUS NE SOMMES PAS RESPONSABLES DES PAQUETS TÉLÉCHARGÉS. VEUILLEZ VOUS ASSURER DE N'INSTALLER QUE DES LOGICIELS FIABLES.", "Dark": "Sombre", "Decline": "Refuser", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "Default installation options for {0} packages": "Options d'installation par défaut pour {0} paquets", "Default preferences - suitable for regular users": "Paramètres par défaut - adaptés aux utilisateurs réguliers", "Default vcpkg triplet": "Triplet vcpkg par défaut", "Delete?": "Supprimer ?", "Dependencies:": "Dépendances :", "Descendant": "Descendant", "Description:": "Description :", "Desktop shortcut created": "Raccourci sur le bureau créé", "Details of the report:": "Détails du rapport :", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Développer est difficile, et cette application est gratuite. Mais si vous avez aimé l'application, vous pouvez toujours <b>m'acheter un café</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installer directement en double-cliquant sur un élement dans l'onglet \"{discoveryTab}\" (au lieu d'afficher les infos du paquet)", "Disable new share API (port 7058)": "Désactiver la nouvelle API de partage (port 7058)", "Disable the 1-minute timeout for package-related operations": "<PERSON><PERSON><PERSON><PERSON> le délai d'attente de 1 minute pour les opérations liées aux paquets", "Disclaimer": "Avertissement", "Discover Packages": "Découvrir des paquets", "Discover packages": "Découvrir les paquets", "Distinguish between\nuppercase and lowercase": "Faire la distinction entre les majuscules et les minuscules", "Distinguish between uppercase and lowercase": "Faire la distinction entre les majuscules et les minuscules", "Do NOT check for updates": "Ne PAS vérifier les mises à jour", "Do an interactive install for the selected packages": "Effectuer une installation interactive pour les paquets sélectionnés", "Do an interactive uninstall for the selected packages": "Effectuer une désinstallation interactive pour les paquets sélectionnés", "Do an interactive update for the selected packages": "Effectuer une mise à jour interactive pour les paquets sélectionnés", "Do not automatically install updates when the battery saver is on": "Ne pas installer automatiquement les mises à jour lorsque l'économiseur de batterie est activé", "Do not automatically install updates when the network connection is metered": "Ne pas installer automatiquement les mises à jour lorsque la connexion réseau est mesurée", "Do not download new app translations from GitHub automatically": "Ne pas télécharger automatiquement les nouvelles traductions de l'application depuis GitHub", "Do not ignore updates for this package anymore": "Ne plus ignorer les mises à jour de ce paquet", "Do not remove successful operations from the list automatically": "Ne pas supprimer automatiquement de la liste les opérations réussies", "Do not show this dialog again for {0}": "Ne plus afficher cette boîte de dialogue pour {0}", "Do not update package indexes on launch": "Ne pas mettre à jour les index des paquets au démarrage", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Acceptez-vous que UniGetUI recueille et envoie des statistiques d'utilisation anonymes, dans le seul but de comprendre et d'améliorer l'expérience utilisateur ?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Vous trouvez WingetUI utile ? Si vous le pouvez, vous pouvez soutenir mon travail, afin que je puisse continuer à faire de WingetUI le gestionnaire de paquets avec interface graphique ultime.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Vous trouvez WingetUI utile ? Vous voulez soutenir le développeur ? Si c'est le cas, vous pouvez {0}, ça aide beaucoup !", "Do you really want to reset this list? This action cannot be reverted.": "Voulez-vous vraiment réinitialiser cette liste ? Cette action ne peut pas être annulée.", "Do you really want to uninstall the following {0} packages?": "Voulez-vous vraiment désinstaller les {0} paquets suivants ?", "Do you really want to uninstall {0} packages?": "Voulez-vous vraiment d<PERSON><PERSON><PERSON>ler {0} paquets ?", "Do you really want to uninstall {0}?": "Voulez-vous vraiment d<PERSON><PERSON> {0} ?", "Do you want to restart your computer now?": "Voulez-vous redémarrer votre ordinateur maintenant ?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Vous souhaitez traduire WingetUI dans votre langue ? Découvrez comment contribuer <a style=\"color:{0}\" href=\"{1}\"a>ICI !</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Vous ne souhaitez pas faire de don ? <PERSON><PERSON> de soucis, vous pouvez toujours partager WingetUI avec vos amis. Faites passer le mot à propos de WingetUI.", "Donate": "Faire un don", "Done!": "Terminé !", "Download failed": "Téléchargement échoué", "Download installer": "Télécharger l'installateur", "Download operations are not affected by this setting": "Les opérations de téléchargement ne sont pas affectées par ce paramètre", "Download selected installers": "Télécharger les installeurs sélectionnés", "Download succeeded": "Téléchargement réussi", "Download updated language files from GitHub automatically": "Télécharger automatiquement les fichiers de langue mis à jour depuis GitHub", "Downloading": "Téléchargement en cours", "Downloading backup...": "Téléchargement de la sauvegarde...", "Downloading installer for {package}": "Téléchargement de l'installateur pour {package}", "Downloading package metadata...": "Téléchargement des métadonnées du paquet...", "Enable Scoop cleanup on launch": "<PERSON>r le nettoyage de Scoop au démarrage", "Enable WingetUI notifications": "Activer les notifications de WingetUI", "Enable an [experimental] improved WinGet troubleshooter": "Activation d'un dépanneur WinGet [expérimental] amélioré", "Enable and disable package managers, change default install options, etc.": "Activer et désactiver les gestionnaires de paquets, modifier les options d'installation par défaut, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Activation des optimisations de l'utilisation du CPU en arrière-plan (voir Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Activer l'API en arrière-plan (Widgets et Partage WingetUI, port 7058) ", "Enable it to install packages from {pm}.": "Activer ceci pour installer les paquets depuis {pm}.", "Enable the automatic WinGet troubleshooter": "Activer le dépannage automatique de WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Activer la nouvelle élévation de privilèges UAC d'UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Activer le nouveau gestionnaire d'entrée de processus (StdIn automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Activez les paramètres ci-dessous SI ET SEULEMENT SI vous comprenez parfaitement ce qu'ils font, ainsi que les implications et les dangers qu'ils peuvent impliquer.", "Enable {pm}": "Activer {pm}", "Enter proxy URL here": "Entrez l'URL du proxy ici", "Entries that show in RED will be IMPORTED.": "Les entrées qui apparaissent en ROUGE seront IMPORTÉES.", "Entries that show in YELLOW will be IGNORED.": "Les entrées qui apparaissent en JAUNE seront IGNORÉES.", "Error": "<PERSON><PERSON><PERSON>", "Everything is up to date": "Tout est à jour", "Exact match": "Correspondance exacte", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Les raccourcis existants sur votre bureau seront analysés et vous devrez choisir ceux qui doivent être conservés et ceux qui doivent être supprimés.", "Expand version": "Afficher la version", "Experimental settings and developer options": "Paramètres expérimentaux et options pour les développeurs", "Export": "Exporter", "Export log as a file": "Exporter les journaux dans un fichier", "Export packages": "Exporter des paquets", "Export selected packages to a file": "Exporter les paquets sélectionnés dans un fichier", "Export settings to a local file": "Exporter les paramètres dans un fichier local", "Export to a file": "Exporter dans un fichier", "Failed": "Échec", "Fetching available backups...": "Recherche des sauvegardes disponibles...", "Fetching latest announcements, please wait...": "Récupération des dernières annonces, veuillez patienter...", "Filters": "Filtres", "Finish": "<PERSON><PERSON><PERSON>", "Follow system color scheme": "Synchroniser avec le thème du système", "Follow the default options when installing, upgrading or uninstalling this package": "Suivez les options par défaut lors de l'installation, de la mise à niveau ou de la désinstallation de ce logiciel.", "For security reasons, changing the executable file is disabled by default": "Pour des raisons de sécurité, la modification du fichier exécutable est désactivée par défaut.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Pour des raisons de sécurité, les arguments de ligne de commande personnalisés sont désactivés par défaut. Allez dans les paramètres de sécurité d'UniGetUI pour changer cela.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Pour des raisons de sécurité, les scripts de pré-opération et de post-opération sont désactivés par défaut. Allez dans les paramètres de sécurité d'UniGetUI pour changer cela.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Forcer l'usage de la version compilée ARM de WinGet (SEULEMENT POUR LES SYSTÈMES ARM64)", "Formerly known as WingetUI": "Anciennement WingetUI", "Found": "<PERSON><PERSON><PERSON><PERSON>", "Found packages: ": "Paquets trouvés :", "Found packages: {0}": "Paquets trouvés : {0}", "Found packages: {0}, not finished yet...": "Paquets trouvés : {0}, pas encore terminé...", "General preferences": "Paramètres généraux", "GitHub profile": "<PERSON><PERSON>", "Global": "Global", "Go to UniGetUI security settings": "Aller dans les paramètres de sécurité d'UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Excellent dépôt d'utilitaires peu connus mais utiles et d'autres paquets intéressants.<br>Contient : <b>Utilitaires, Programmes en ligne de commande, Logic<PERSON>s généraux (bucket extras requis)</b>", "Great! You are on the latest version.": "Génial ! Vous utilisez la dernière version.", "Grid": "Grille", "Help": "Aide", "Help and documentation": "Aide et documentation", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Ici vous pouvez changer le comportement d'UniGetUI en ce qui concerne les raccourcis suivants. En cochant un raccourci, UniGetUI le supprimera s'il est créé lors d'une prochaine mise à jour. Si vous ne le cochez pas, cela conservera le raccourci intact.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Salut, mon nom est Martí, et je suis le <i>développeur</i> de WingetUI. WingetUI a été entièrement réalisé durant mon temps libre !", "Hide details": "Masquer les détails", "Homepage": "Page d'accueil", "Hooray! No updates were found.": "Félicitations ! Toutes vos applications sont à jour !", "How should installations that require administrator privileges be treated?": "Comment doivent être traitées les installations qui requièrent des privilèges d'administrateur ?", "How to add packages to a bundle": "Comment ajouter des paquets à un lot", "I understand": "Je comprends", "Icons": "Icônes", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Si vous avez activé la sauvegarde dans le cloud, il sera sauvegardé en tant que Gist GitHub sur ce compte.", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignorer les commandes personnalisées de pré-installation et de post-installation lors de l'importation de paquets par lot", "Ignore future updates for this package": "Ignorer les futures mises à jour pour ce paquet", "Ignore packages from {pm} when showing a notification about updates": "Ignorer les paquets de {pm} lors de l'affichage d'une notification de mise à jour", "Ignore selected packages": "Ignorer les paquets sélectionnés", "Ignore special characters": "Ignorer les caractères spéciaux", "Ignore updates for the selected packages": "Ignorer les mises à jour pour les paquets sélectionnés", "Ignore updates for this package": "Ignorer les mises à jour pour ce paquet", "Ignored updates": "Mises à jour ignorées", "Ignored version": "Version ignorée", "Import": "Importer", "Import packages": "Importer des paquets", "Import packages from a file": "Importer des paquets depuis un fichier", "Import settings from a local file": "Importer les paramètres depuis un fichier local", "In order to add packages to a bundle, you will need to: ": "Pour ajouter des paquets à un lot, vous devez : ", "Initializing WingetUI...": "Initialisation de WingetUI...", "Install": "Installer", "Install Scoop": "Installer Scoop", "Install and more": "Installer et plus", "Install and update preferences": "Installation et mise à jour des préférences", "Install as administrator": "Installer en tant qu'administrateur", "Install available updates automatically": "Installer automatiquement les mises à jour disponibles", "Install location can't be changed for {0} packages": "L'emplacement d'installation ne peut pas être modifié pour {0} paquets", "Install location:": "Emplacement d'installation :", "Install options": "Options d'installation", "Install packages from a file": "Installer des paquets depuis un fichier", "Install prerelease versions of UniGetUI": "Installer les préversions d'UniGetUI", "Install script": "Script d'installation", "Install selected packages": "Installer les paquets sélectionnés ", "Install selected packages with administrator privileges": "Installer les paquets sélectionnés avec les privilèges d'administrateur", "Install selection": "Installer la sélection", "Install the latest prerelease version": "Installer la dernière version préliminaire", "Install updates automatically": "Installer les mises à jour automatiquement", "Install {0}": "Installer {0}", "Installation canceled by the user!": "Installation annulée par l'utilisateur !", "Installation failed": "Échec de l'installation", "Installation options": "Options d'installation", "Installation scope:": "Portée de l'installation", "Installation succeeded": "Installation réussie", "Installed Packages": "Paquets installés", "Installed Version": "Version installée", "Installed packages": "Paquets installés", "Installer SHA256": "SHA256 de l'installateur", "Installer SHA512": "SHA512 de l'installateur", "Installer Type": "Type d'installateur", "Installer URL": "URL de l'installateur", "Installer not available": "L'installateur n'est pas disponible", "Instance {0} responded, quitting...": "L'instance {0} répondu, fermeture...", "Instant search": "Recherche instantanée", "Integrity checks can be disabled from the Experimental Settings": "Les contrôles d'intégrité peuvent être désactivés à partir des paramètres expérimentaux", "Integrity checks skipped": "Contrôles d'intégrité ignorés", "Integrity checks will not be performed during this operation": "Les contrôles d'intégrité ne seront pas effectués pendant cette opération", "Interactive installation": "Installation interactive", "Interactive operation": "Fonctionnement interactif", "Interactive uninstall": "Désinstallation interactive", "Interactive update": "Mise à jour interactive", "Internet connection settings": "Paramètres de connexion Internet", "Is this package missing the icon?": "Manque-t-il une icône pour ce paquet ?", "Is your language missing or incomplete?": "Votre langue est-elle manquante ou incomplète ?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Il n'est pas garanti que les informations d'identification fournies seront stockées en toute sécurité. Vous pouvez donc tout aussi bien ne pas utiliser les informations d'identification de votre compte bancaire.", "It is recommended to restart UniGetUI after WinGet has been repaired": "Il est recommandé de redémarrer UniGetUI après que WinGet a été réparé", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Il est fortement recommandé de réinstaller UniGetUI pour remédier à la situation.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Il semblerait que WinGet ne fonctionne pas correctement. Voulez-vous essayer de ré<PERSON><PERSON> ?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Il semblerait que vous ayez exécuté WingetUI en tant qu'administrateur, ce qui n'est pas recommandé. Vous pouvez toujours utiliser le programme, mais nous recommandons fortement de ne pas exécuter WingetUI avec les privilèges d'administrateur. Cliquez sur \"{showDetails}\" pour en savoir plus.", "Language": "<PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "Langue, thème et autres préférences diverses", "Last updated:": "Dernière date de mise à jour :", "Latest": "<PERSON><PERSON><PERSON>", "Latest Version": "Dernière version", "Latest Version:": "Dernière version :", "Latest details...": "Derniers détails...", "Launching subprocess...": "Lancement des processus en arrière-plan...", "Leave empty for default": "Laisser vide pour utiliser la valeur par défaut", "License": "Licence", "Licenses": "Licences", "Light": "<PERSON>", "List": "Liste", "Live command-line output": "Sortie de la ligne de commande en temps réel", "Live output": "Sortie en temps réel", "Loading UI components...": "Chargement des composants de l'interface utilisateur...", "Loading WingetUI...": "Chargement de WingetUI...", "Loading packages": "Chargement des paquets", "Loading packages, please wait...": "Chargement des paquets, veuillez patienter...", "Loading...": "Chargement...", "Local": "Local", "Local PC": "PC local", "Local backup advanced options": "Options avancées de la sauvegarde locale", "Local machine": "Machine locale", "Local package backup": "Sauvegarde locale des paquets", "Locating {pm}...": "Localisation de {pm} ...", "Log in": "Connexion", "Log in failed: ": "Échec de la connexion :", "Log in to enable cloud backup": "Connectez-vous pour activer la sauvegarde dans le cloud", "Log in with GitHub": "Se connecter avec GitHub", "Log in with GitHub to enable cloud package backup.": "Connectez-vous à GitHub pour activer la sauvegarde des paquets dans le cloud.", "Log level:": "Niveau de journalisation :", "Log out": "Déconnexion", "Log out failed: ": "Échec de la déconnexion :", "Log out from GitHub": "Se déconnecter de GitHub", "Looking for packages...": "Recherche de paquets ...", "Machine | Global": "Machine | Globale", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Des arguments de ligne de commande mal formés peuvent endommager les paquets, voire permettre à un acteur malveillant d'obtenir une exécution privilégiée. Par conséquent, l'importation d'arguments de ligne de commande personnalisés est désactivée par défaut.", "Manage": "<PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "<PERSON><PERSON><PERSON> les paramètres d'UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "G<PERSON>rer le comportement de démarrage automatique de UniGetUI depuis les paramètres de l'application", "Manage ignored packages": "<PERSON><PERSON><PERSON> les paquets ignorés", "Manage ignored updates": "<PERSON><PERSON><PERSON> les mises à jour ignorées", "Manage shortcuts": "<PERSON><PERSON><PERSON> r<PERSON>", "Manage telemetry settings": "<PERSON><PERSON><PERSON> les paramètres de télémétrie", "Manage {0} sources": "<PERSON><PERSON><PERSON> les sources {0}", "Manifest": "Manifeste", "Manifests": "Manifestes", "Manual scan": "<PERSON><PERSON><PERSON> man<PERSON>", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Le gestionnaire de paquets officiel de Microsoft. Rempli de paquets bien connus et vérifiés.<br>Contient : <b><PERSON><PERSON><PERSON> généraux, Applications du Microsoft Store</b>.", "Missing dependency": "Dépendance manquante", "More": "Plus", "More details": "Plus de détails", "More details about the shared data and how it will be processed": "Plus de détails sur les données partagées et la manière dont elles seront traitées", "More info": "Plus d'informations", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOTE : Ce programme de dépannage peut être désactivé depuis les paramètres d'UniGetUI, dans la section WinGet", "Name": "Nom", "New": "Nouveau", "New Version": "Nouvelle version", "New bundle": "Nouveau lot", "New version": "Nouvelle version", "Nice! Backups will be uploaded to a private gist on your account": "Super ! Les sauvegardes seront téléchargées dans un Gist privé de votre compte.", "No": "Non", "No applicable installer was found for the package {0}": "Aucun programme d'installation applicable n'a été trouvé pour le paquet {0}", "No dependencies specified": "Aucune dépendance spécifiée", "No new shortcuts were found during the scan.": "Aucun nouveau raccourci n'a été trouvé au cours de l'analyse.", "No packages found": "<PERSON><PERSON><PERSON> paquet trouvé", "No packages found matching the input criteria": "<PERSON><PERSON><PERSON> paquet trouvé correspondant aux critères", "No packages have been added yet": "Aucun paquet n'a encore été ajouté", "No packages selected": "<PERSON><PERSON><PERSON> paquet s<PERSON>", "No packages were found": "<PERSON><PERSON><PERSON> paquet n'a été trouvé", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Aucune information personnelle n'est collectée ni envoyée, et les données collectées sont anonymisées, de sorte qu'il est impossible de remonter jusqu'à vous.", "No results were found matching the input criteria": "Aucun résultat correspondant aux critères n'a été trouvé", "No sources found": "Aucune source trouvée", "No sources were found": "Aucune source n'a été trouvée", "No updates are available": "Aucune mise à jour n'est disponible", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Le gestionnaire de paquets de Node JS. Plein de bibliothèques et d'autres utilitaires qui tournent autour du monde de JavaScript.<br>Contient : <b>Bibliothèques JavaScript Node et autres utilitaires associés</b>", "Not available": "Non disponible", "Not finding the file you are looking for? Make sure it has been added to path.": "Vous ne trouvez pas le fichier que vous cherchez ? Assurez-vous qu'il a été ajouté au chemin d'accès.", "Not found": "Non trouvé", "Not right now": "Pas maintenant", "Notes:": "Notes :", "Notification preferences": "Préférences de notification", "Notification tray options": "Options de notification", "Notification types": "Types de notification", "NuPkg (zipped manifest)": "NuPkg (manifeste compressé)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Open UniGetUI": "Ouvrir UniGetUI", "Open UniGetUI security settings": "Ou<PERSON><PERSON>r les paramètres de sécurité d'UniGetUI", "Open WingetUI": "<PERSON><PERSON><PERSON><PERSON>r WingetUI", "Open backup location": "Ouvrir l'emplacement de sauvegarde ", "Open existing bundle": "Ouvrir un lot existant...", "Open install location": "Ouvrir l'emplacement d'installation", "Open the welcome wizard": "<PERSON><PERSON><PERSON><PERSON><PERSON> l'assistant de bienvenue", "Operation canceled by user": "Opération annulée par l'utilisateur", "Operation cancelled": "Opération annulée", "Operation history": "Historique des opérations", "Operation in progress": "Opération en cours", "Operation on queue (position {0})...": "Opération dans la file d'attente (position {0})...", "Operation profile:": "Profil de l'opération :", "Options saved": "Options enregistrées", "Order by:": "Trier par :", "Other": "<PERSON><PERSON>", "Other settings": "Autres paramètres", "Package": "<PERSON><PERSON>", "Package Bundles": "<PERSON> de paquets", "Package ID": "ID du paquet", "Package Manager": "Gestionnaires de paquets", "Package Manager logs": "Journaux du gestionnaire de paquets", "Package Managers": "Gestionnaires de paquets", "Package Name": "Nom du paquet", "Package backup": "Sauvegarde du p<PERSON>t", "Package backup settings": "Paramètres de sauvegarde des paquets", "Package bundle": "<PERSON> de p<PERSON>ts", "Package details": "<PERSON><PERSON><PERSON> du paque<PERSON>", "Package lists": "Listes de paquets", "Package management made easy": "Gestion des paquets facilitée", "Package manager": "Gestionnaire de paquets", "Package manager preferences": "Gestionnaires de paquets", "Package managers": "Gestionnaires de paquets", "Package not found": "Paquet introuvable", "Package operation preferences": "Préférences de fonctionnement des paquets", "Package update preferences": "Préférences de mise à jour des paquets", "Package {name} from {manager}": "Paquet {name} de {manager} ", "Package's default": "Valeur par défaut du paquet", "Packages": "Paquets", "Packages found: {0}": "Paquets trouvés : {0}", "Partially": "Partiellement", "Password": "Mot de passe", "Paste a valid URL to the database": "Coller une URL valide dans la base de données", "Pause updates for": "Su<PERSON><PERSON><PERSON> les mises à jour pour", "Perform a backup now": "Effectuer une sauvegarde maintenant", "Perform a cloud backup now": "Effectuer une sauvegarde dans le cloud maintenant", "Perform a local backup now": "Effectuer une sauvegarde locale maintenant", "Perform integrity checks at startup": "Effectuer des contrôles d'intégrité au démarrage", "Performing backup, please wait...": "Sauvegarde en cours, veuillez patienter...", "Periodically perform a backup of the installed packages": "Effectuer périodiquement une sauvegarde des paquets installés", "Periodically perform a cloud backup of the installed packages": "Effectuer périodiquement une sauvegarde dans le cloud des paquets installés", "Periodically perform a local backup of the installed packages": "Effectuer une sauvegarde locale périodique des paquets installés.", "Please check the installation options for this package and try again": "Veuillez vérifier les options d'installation de ce paquet et réessayer", "Please click on \"Continue\" to continue": "Veuillez cliquer sur \"Continuer\" pour continuer", "Please enter at least 3 characters": "Veuillez entrer au moins 3 caractères", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Veuillez noter que certains paquets peuvent ne pas être installables, en raison des gestionnaires de paquets qui sont activés sur cette machine.", "Please note that not all package managers may fully support this feature": "Veuillez noter que tous les gestionnaires de paquets ne prennent pas toujours en charge cette fonctionnalité", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Veuillez noter que les paquets provenant de certaines sources peuvent ne pas être exportables. Ils ont été grisés et ne seront pas exportés.", "Please run UniGetUI as a regular user and try again.": "Veuillez exécuter UniGetUI en tant qu'utilisateur standard et réessayer.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Consultez la sortie de la ligne de commande ou référez-vous à l'historique des opérations pour plus d'informations sur le problème.", "Please select how you want to configure WingetUI": "Veuillez sélectionner comment vous souhaitez configurer WingetUI", "Please try again later": "Veuillez réessayer plus tard", "Please type at least two characters": "Veuillez taper au moins deux caractères", "Please wait": "<PERSON><PERSON><PERSON><PERSON>er", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Veuillez patienter pendant l'installation de {0}. Une fenêtre noire peut s'afficher. Veuillez patienter jusqu'à ce qu'elle se ferme.", "Please wait...": "Veuillez patienter...", "Portable": "Portable", "Portable mode": "Mode portable", "Post-install command:": "Commande de post-installation :", "Post-uninstall command:": "Commande de post-désinstallation :", "Post-update command:": "Commande de post-mise à jour :", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Le gestionnaire de paquets de PowerShell. Bibliothèques et scripts pour étendre les capacités de PowerShell.<br>Contient : <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>\n", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Les commandes pré et post-installation peuvent avoir des conséquences néfastes sur votre appareil, si elles sont conçues pour cela. Importer les commandes d'un bundle peut être très dangereux, sauf si vous faites confiance à la source de ce bundle.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Les commandes de pré-installation et de post-installation seront exécutées avant et après l'installation, la mise à niveau ou la désinstallation d'un paquet. Attention : elles peuvent endommager des éléments si elles ne sont pas utilisées avec précaution.", "Pre-install command:": "Commande de pré-installation :", "Pre-uninstall command:": "Commande de pré-désinstallation :", "Pre-update command:": "Commande de pré-mise à jour :", "PreRelease": "Préversion", "Preparing packages, please wait...": "Préparation des paquets, veuillez patienter...", "Proceed at your own risk.": "Procédez à vos propres risques.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Interdire toute forme d'élévation via UniGetUI Elevator ou GSudo", "Proxy URL": "URL du proxy", "Proxy compatibility table": "Tableau de compatibilité des proxys", "Proxy settings": "Paramètres du proxy", "Proxy settings, etc.": "Paramètres du proxy, etc.", "Publication date:": "Date de publication :", "Publisher": "<PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Le gestionnaire de bibliothèques de Python. Plein de bibliothèques Python et d'autres utilitaires liés à Python.<br>Contient : <b>Bibliothèques Python et autres utilitaires associés</b>", "Quit": "<PERSON><PERSON><PERSON>", "Quit WingetUI": "Q<PERSON>ter WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "<PERSON><PERSON><PERSON><PERSON> les invites UAC, élever les installations par défaut, déverrouiller certaines fonctionnalités dangereuses, etc.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Consultez les journaux UniGetUI pour obtenir plus de détails sur les fichiers concernés.", "Reinstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reinstall package": "<PERSON><PERSON><PERSON><PERSON><PERSON> le paquet", "Related settings": "Paramètres associés", "Release notes": "Notes de publication", "Release notes URL": "URL des notes de version", "Release notes URL:": "URL des notes de version :", "Release notes:": "Notes de version :", "Reload": "Recharger", "Reload log": "Recharger les journaux", "Removal failed": "Échec de la suppression", "Removal succeeded": "Suppression réussie", "Remove from list": "<PERSON><PERSON><PERSON><PERSON> de la liste", "Remove permanent data": "Supprimer les données permanentes", "Remove selection from bundle": "Retirer la sélection du lot", "Remove successful installs/uninstalls/updates from the installation list": "Supprimer les installations/désinstallations/mises à jour réussies de la liste d'installation", "Removing source {source}": "Suppression de la source {source}", "Removing source {source} from {manager}": "Suppression de la source {source} de {manager}", "Repair UniGetUI": "R<PERSON>parer UniGetUI", "Repair WinGet": "<PERSON><PERSON><PERSON><PERSON>", "Report an issue or submit a feature request": "Signaler un problème ou soumettre une demande de fonctionnalité", "Repository": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reset": "Réinitialiser", "Reset Scoop's global app cache": "Réinitialiser le cache global des applications de Scoop", "Reset UniGetUI": "Réinitialiser UniGetUI", "Reset WinGet": "Réinitialiser WinGet", "Reset Winget sources (might help if no packages are listed)": "Réinitialiser les sources WinGet (peut aider si aucun paquet n'est listé)", "Reset WingetUI": "Réinitialiser UniGetUI", "Reset WingetUI and its preferences": "Réinitialiser UniGetUI et ses préférences", "Reset WingetUI icon and screenshot cache": "Réinitialiser le cache des icônes et des captures d'écran de UniGetUI", "Reset list": "Réinitialiser la liste", "Resetting Winget sources - WingetUI": "Réinitialisation des sources de WinGet - WingetUI", "Restart": "<PERSON><PERSON><PERSON><PERSON>", "Restart UniGetUI": "Redémarrer UniGetUI", "Restart WingetUI": "Redémarrer WingetUI", "Restart WingetUI to fully apply changes": "Redémarrer WingetUI pour appliquer les changements", "Restart later": "Redémarrer plus tard", "Restart now": "Redémarrer maintenant", "Restart required": "Redémarrage requis", "Restart your PC to finish installation": "Redémarrez votre PC pour terminer l'installation", "Restart your computer to finish the installation": "Redémarrer votre ordinateur pour terminer l'installation", "Restore a backup from the cloud": "Restaurer une sauvegarde à partir du cloud", "Restrictions on package managers": "Restrictions sur les gestionnaires de paquets", "Restrictions on package operations": "Restrictions sur les opérations sur les paquets", "Restrictions when importing package bundles": "Restrictions lors de l'importation de lots de paquets", "Retry": "<PERSON><PERSON><PERSON><PERSON>", "Retry as administrator": "Réessayer en tant qu'administrateur", "Retry failed operations": "Réessayer les opérations qui ont échoué", "Retry interactively": "Réessayer de manière interactive", "Retry skipping integrity checks": "Réessayer en ignorant les contrôles d'intégrité", "Retrying, please wait...": "Nouvelle tentative, veuillez patienter...", "Return to top": "Retour<PERSON> en haut", "Run": "Exécuter", "Run as admin": "Exécuter en tant qu'administrateur", "Run cleanup and clear cache": "Exécuter un nettoyage et vider le cache", "Run last": "<PERSON><PERSON> der<PERSON>", "Run next": "Lancer le prochain", "Run now": "Lancer maintenant", "Running the installer...": "Exécution du programme d'installation...", "Running the uninstaller...": "Exécution du programme de désinstallation...", "Running the updater...": "Exécution du programme de mise à jour...", "Save": "Enregistrer", "Save File": "Enregis<PERSON><PERSON> le fi<PERSON>er", "Save and close": "Enregistrer et fermer", "Save as": "Enregistrer sous", "Save bundle as": "Enregistrer le lot sous...", "Save now": "Enregistrer maintenant", "Saving packages, please wait...": "Enregistrement des paquets, veuillez patienter...", "Scoop Installer - WingetUI": "Installateur de Scoop - WingetUI", "Scoop Uninstaller - WingetUI": "Déinstallateur de Scoop - WingetUI", "Scoop package": "<PERSON><PERSON>", "Search": "Recherche", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Rechercher des logiciels de bureau, m'avertir lorsque des mises à jour sont disponibles et ne pas faire de choses de geeks. Je ne veux pas que WingetUI soit trop compliqué, je veux juste un simple <b>magasin de logiciels</b>.", "Search for packages": "Rechercher des paquets", "Search for packages to start": "Rechercher des paquets pour démarrer", "Search mode": "Mode de recherche", "Search on available updates": "Rechercher des mises à jour disponibles", "Search on your software": "Rechercher votre logiciel", "Searching for installed packages...": "Recherche des paquets installés en cours...", "Searching for packages...": "Recherche de paquets en cours...", "Searching for updates...": "Recherche de mises à jour en cours...", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Sélectionnez \"{item}\" pour ajouter votre bucket personnalisé", "Select a folder": "Sélectionner un dossier", "Select all": "<PERSON><PERSON>", "Select all packages": "Sélectionner tous les paquets", "Select backup": "Sélectionner la sauvegarde", "Select only <b>if you know what you are doing</b>.": "Sélectionnez seulement <b>si vous savez ce que vous faites</b>.", "Select package file": "Sélect<PERSON><PERSON> le fichier du paquet", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Sélectionnez la sauvegarde que vous souhaitez ouvrir. Plus tard, vous pourrez revoir les paquets/programmes que vous souhaitez restaurer.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Sélectionnez les processus qui doivent être fermés avant l'installation, la mise à jour ou la désinstallation de ce paquet.", "Select the source you want to add:": "Sélectionner la source que vous voulez ajouter :", "Select upgradable packages by default": "Sélectionner les paquets à mettre à jour par défaut", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Sélectionner quels <b>gestionnaires de paquets</b> à utiliser ({0}), configurer comment les paquets sont installés, gérer le fonctionnement des droits d'administrateur, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake envoyé. En attente de la réponse de l'instance... ({0}%)", "Set a custom backup file name": "Définir un nom de fichier de sauvegarde personnalisé", "Set custom backup file name": "Définir un nom de fichier de sauvegarde personnalisé", "Settings": "Paramètres", "Share": "Partager", "Share WingetUI": "Partager WingetUI", "Share anonymous usage data": "Partager les données d'utilisation anonymes", "Share this package": "Partager ce paquet", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Si vous modifiez les paramètres de sécurité, vous devrez ouvrir à nouveau le lot pour que les modifications soient prises en compte.", "Show UniGetUI on the system tray": "Afficher UniGetUI dans la zone de notification", "Show UniGetUI's version and build number on the titlebar.": "Afficher la version d'UniGetUI dans la barre de titre", "Show WingetUI": "Afficher WingetUI", "Show a notification when an installation fails": "Afficher une notification quand une installation échoue", "Show a notification when an installation finishes successfully": "Afficher une notification quand une installation se termine avec succès", "Show a notification when an operation fails": "Afficher une notification lorsqu'une opération échoue", "Show a notification when an operation finishes successfully": "Afficher une notification lorsqu'une opération se termine avec succès", "Show a notification when there are available updates": "Afficher une notification quand des mises à jour sont disponibles", "Show a silent notification when an operation is running": "Afficher une notification silencieuse lorsqu'une opération est en cours", "Show details": "Aff<PERSON>r les détails", "Show in explorer": "Afficher dans l'explorateur", "Show info about the package on the Updates tab": "Afficher les informations du paquet sur l'onglet Mises à jour", "Show missing translation strings": "Afficher les traductions manquantes", "Show notifications on different events": "Afficher des notifications sur différents événements", "Show package details": "A<PERSON><PERSON><PERSON> les détails du paquet", "Show package icons on package lists": "Afficher les icônes des paquets dans la liste des paquets", "Show similar packages": "Afficher des paquets similaires", "Show the live output": "A<PERSON>icher la sortie en temps réel", "Size": "<PERSON><PERSON>", "Skip": "Passer", "Skip hash check": "Passer la vérification du hachage", "Skip hash checks": "Passer les vérifications du hachage", "Skip integrity checks": "Passer les vérifications d'intégrité", "Skip minor updates for this package": "Ignorer les mises à jour mineures pour ce paquet", "Skip the hash check when installing the selected packages": "Passer la vérification du hachage lors de l'installation des paquets sélectionnés", "Skip the hash check when updating the selected packages": "Passer la vérification du hachage lors de la mise à jour des paquets sélectionnés", "Skip this version": "Ignorer cette version", "Software Updates": "<PERSON>ses à jour", "Something went wrong": "<PERSON><PERSON><PERSON> chose ne s'est pas passé comme prévu", "Something went wrong while launching the updater.": "Un problème s'est produit lors du lancement de l'outil de mise à jour.", "Source": "Source", "Source URL:": "URL source :", "Source added successfully": "Source ajoutée avec succès", "Source addition failed": "L'ajout de la source à échoué", "Source name:": "Nom de la source :", "Source removal failed": "La suppression de la source a échouée", "Source removed successfully": "Source supprimée avec succès", "Source:": "Source :", "Sources": "Sources", "Start": "<PERSON><PERSON><PERSON><PERSON>", "Starting daemons...": "Démarrage des démons...", "Starting operation...": "Démarrage de l'opération...", "Startup options": "Options de démarrage", "Status": "Statut", "Stuck here? Skip initialization": "Bloqué ici ? Passer l'initialisation", "Success!": "Succès !", "Suport the developer": "Soutenir le développeur", "Support me": "Me soutenir", "Support the developer": "Soutenir le développeur", "Systems are now ready to go!": "Les systèmes sont maintenant prêts à fonctionner !", "Telemetry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Texte", "Text file": "<PERSON><PERSON>er texte", "Thank you ❤": "<PERSON><PERSON><PERSON> ❤", "Thank you 😉": "<PERSON><PERSON><PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Le gestionnaire de paquets de Rust.<br>Contient : <b>Bibliothèques Rust et programmes écrits en Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "La sauvegarde n'inclura PAS de fichier binaire ni de données sauvegardées par un programme.", "The backup will be performed after login.": "La sauvegarde sera exécutée après la connexion.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "La sauvegarde inclura la liste complète des paquets installés, ainsi que leurs options d'installation. Les mises à jour et versions ignorées seront également sauvegardées.", "The bundle was created successfully on {0}": "Le lot a été créé avec succès sur {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Le lot que vous essayez de charger semble être invalide. Veuillez vérifier le fichier et réessayer.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Le checksum de l'installateur ne coïncide pas avec la valeur attendue, et l'authenticité de l'installateur ne peut pas être vérifiée. Si vous faites confiance à l'éditeur, {0} le paquet passera à nouveau la vérification du hachage. ", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Le gestionnaire de paquets classique pour Windows. Vous y trouverez tout.<br>Contient : <b>Logiciels généraux</b>", "The cloud backup completed successfully.": "La sauvegarde dans le cloud s'est terminée avec succès.", "The cloud backup has been loaded successfully.": "La sauvegarde dans le cloud a été chargée avec succès.", "The current bundle has no packages. Add some packages to get started": "Le lot actuel ne contient aucun paquet. Ajoutez quelques paquets pour commencer", "The executable file for {0} was not found": "Le fichier exécutable de {0} n'a pas été trouvé", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Les options suivantes seront appliquées par défaut chaque fois qu'un paquet {0} est installé, mis à niveau ou désinstallé.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Les paquets suivants vont être exportés dans un fichier JSON. Aucune donnée utilisateur ou fichier binaire ne sera enregistré.", "The following packages are going to be installed on your system.": "Les paquets suivants vont être installés sur votre système.", "The following settings may pose a security risk, hence they are disabled by default.": "Les paramètres suivants peuvent présenter un risque pour la sécurité, c'est pourquoi ils sont désactivés par défaut.", "The following settings will be applied each time this package is installed, updated or removed.": "Les paramètres suivants seront appliqués à chaque fois que ce paquet sera installé, mis à jour ou supprimé.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Les paramètres suivants seront appliqués chaque fois que ce paquet sera installé, mis à jour ou supprimé. Ils seront enregistrés automatiquement.", "The icons and screenshots are maintained by users like you!": "Les icônes et les captures d'écran sont maintenues par des utilisateurs tout comme vous !", "The installation script saved to {0}": "Le script d'installation enregistré dans {0}", "The installer authenticity could not be verified.": "L'authenticité de l'installateur n'a pas pu être vérifiée.", "The installer has an invalid checksum": "Le programme d'installation a une somme de contrôle invalide", "The installer hash does not match the expected value.": "Le hachage de l'installateur ne correspond pas à la valeur attendue.", "The local icon cache currently takes {0} MB": "Le cache des icônes occupe actuellement {0} Mo", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Le but principal de ce projet est de créer une interface utilisateur intuitive pour gérer les gestionnaires de paquets en ligne de commande les plus populaires pour Windows, tel que WinGet et Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Le paquet \"{0}\" n'a pas été trouvé dans le gestionnaire de paquets \"{1}\"", "The package bundle could not be created due to an error.": "Le lot de paquets n'a pas pu être créé en raison d'une erreur", "The package bundle is not valid": "Le lot de paquets n'est pas valide", "The package manager \"{0}\" is disabled": "Le gestionnaire de paquets \"{0}\" est désactivé", "The package manager \"{0}\" was not found": "Le gestionnaire de paquets \"{0}\" n'a pas été trouvé", "The package {0} from {1} was not found.": "<PERSON> paquet {0} de {1} n'a pas été trouvé.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Les paquets listés ici ne seront pas pris en compte lors de la vérification des mises à jour. Double-cliquez dessus ou cliquez sur le bouton à leur droite pour ne plus ignorer leurs mises à jour.", "The selected packages have been blacklisted": "Les paquets sélectionnés ont été ajoutés à la liste noire", "The settings will list, in their descriptions, the potential security issues they may have.": "Les paramètres énumèrent, dans leur description, les problèmes de sécurité potentiels qu'ils peuvent présenter.", "The size of the backup is estimated to be less than 1MB.": "La taille de la sauvegarde est estimée à moins de 1 Mo.", "The source {source} was added to {manager} successfully": "La source {source} a <PERSON><PERSON> a<PERSON>té à {manager} avec succès", "The source {source} was removed from {manager} successfully": "La source {source} a été supprimé de {manager} avec succès", "The system tray icon must be enabled in order for notifications to work": "L'icône de la barre d'état système doit être activée pour que les notifications fonctionnent", "The update process has been aborted.": "Le processus de mise à jour a été interrompu.", "The update process will start after closing UniGetUI": "Le processus de mise à jour démarrera après la fermeture d'UniGetUI", "The update will be installed upon closing WingetUI": "La mise à jour sera installée à la fermeture de WingetUI", "The update will not continue.": "La mise à jour ne sera pas poursuivie.", "The user has canceled {0}, that was a requirement for {1} to be run": "L'utilisateur a annulé {0}, qui était nécessaire à l'exécution de {1}", "There are no new UniGetUI versions to be installed": "Il n'y a pas de nouvelle version d'UniGetUI à installer.", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Il y a des opérations en cours. Quitter WingetUI pourrait les faire échouer. Voulez-vous continuer ?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Il existe d'excellentes vidéos sur YouTube qui présentent WingetUI et ses capacités. Vous pourrez apprendre des trucs et des astuces utiles !", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Il y a deux principales raisons de ne pas exécuter WingetUI en tant qu'administrateur :\nLa première est que le gestionnaire de paquets Scoop peut causer des problèmes avec certaines commandes quand il est exécuté en tant qu'administrateur.\nLa seconde est qu'exécuter WingetUI en tant qu'administrateur signifie que n'importe quel paquet que vous téléchargez sera exécuté en tant qu'administrateur (et ce n'est pas sécurisé).\nRappelez-vous que si vous avez besoin d'installer un paquet spécifique en tant qu'administrateur, vous pouvez toujours faire un clic droit sur l'élément -> Installer/Mettre à jour/Désinstaller en tant qu'administrateur.", "There is an error with the configuration of the package manager \"{0}\"": "Il y a une erreur avec la configuration du gestionnaire de paquets \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Une installation est en cours. Si vous fermez WingetUI, celle-ci peut échouer et avoir des résultats inattendus. Voulez-vous fermer WingetUI malgré tout ?", "They are the programs in charge of installing, updating and removing packages.": "Ce sont les programmes en charge de l'installation, de la mise à jour et de la désinstallation des paquets.", "Third-party licenses": "Licences tiers", "This could represent a <b>security risk</b>.": "<PERSON><PERSON> pourrait représenter un <b>risque de sécurité</b>.", "This is not recommended.": "Ceci n'est pas recommandé.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Ceci est probablement dû au fait que le paquet qui vous a été envoyé a été supprimé, ou publié sur un gestionnaire de paquets que vous n'avez pas d'activé. L'ID reçu est {0}", "This is the <b>default choice</b>.": "Il s'agit du <b>choix par défaut</b>.", "This may help if WinGet packages are not shown": "ceci peut aider si les paquets de WinGet n'apparaissent pas", "This may help if no packages are listed": "ceci peut aider si aucun paquet n'est listé", "This may take a minute or two": "<PERSON><PERSON> peut prendre une minute ou deux", "This operation is running interactively.": "Cette opération s'exécute de manière interactive.", "This operation is running with administrator privileges.": "Cette opération est exécutée avec les privilèges d'administrateur.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Cette option ENTRAINERA des problèmes. Toute opération incapable de s'élever elle-même échouera. L'installation/mise à jour/désinstallation en tant qu'administrateur NE FONCTIONNE PAS.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Ce lot de paquets contient des paramètres potentiellement dangereux qui peuvent être ignorés par défaut.", "This package can be updated": "<PERSON> paquet peut être mis à jour", "This package can be updated to version {0}": "<PERSON> paquet peut être mis à jour vers la version {0}", "This package can be upgraded to version {0}": "<PERSON> paquet peut être mis à jour vers la version {0}", "This package cannot be installed from an elevated context.": "Ce paquet ne peut pas être installé avec une élévation de privilèges d'administrateur.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Ce paquet n'a pas de capture d'écran ou l'icône est manquante ? Contribuez à WingetUI en ajoutant les icônes manquantes et les captures d'écran à notre base de données ouverte et publique.", "This package is already installed": "Ce paquet est déjà installé", "This package is being processed": "Ce paquet est en cours de traitement", "This package is not available": "Ce paquet n'est pas disponible", "This package is on the queue": "Ce paquet est dans la fil d'attente", "This process is running with administrator privileges": "Ce processus est exécuté avec les privilèges d'administrateur", "This project has no connection with the official {0} project — it's completely unofficial.": "Ce projet n'a aucun lien avec le projet officiel {0} — c'est complètement non officiel.", "This setting is disabled": "Ce paramètre est désactivé", "This wizard will help you configure and customize WingetUI!": "Cet assistant vous aidera à configurer et à customiser WingetUI !", "Toggle search filters pane": "Afficher/masquer le volet des filtres de recherche", "Translators": "Traducteurs", "Try to kill the processes that refuse to close when requested to": "Essayer de tuer les processus qui refusent de se fermer lorsqu'on le leur demande.", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Activer cette option permet de modifier le fichier exécutable utilisé pour interagir avec les gestionnaires de paquets. Bien que cela permette une personnalisation plus fine de vos processus d'installation, cela peut également s'avérer dangereux", "Type here the name and the URL of the source you want to add, separed by a space.": "Tapez ici le nom et l'URL de la source que vous souhaitez ajouter, séparés par un espace.", "Unable to find package": "Impossible de trouver le paquet", "Unable to load informarion": "Impossible de charger les informations", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI recueille des données d'utilisation anonymes afin d'améliorer l'expérience utilisateur.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI recueille des données d'utilisation anonymes dans le seul but de comprendre et d'améliorer l'expérience utilisateur.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI a détecté un nouveau raccourci sur le bureau qui peut être supprimé automatiquement.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI a détecté les raccourcis sur le bureau suivants qui peuvent être supprimés automatiquement lors de futures mises à jours", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI a détecté {0} nouveau(x) raccourci(s) sur le bureau pouvant être supprimé(s) automatiquement.", "UniGetUI is being updated...": "UniGetUI est en cours de mise à jour...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI n'est lié à aucun des gestionnaires de paquets compatibles. UniGetUI est un projet indépendant.", "UniGetUI on the background and system tray": "UniGetUI en arrière-plan et dans la barre des tâches", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI ou certains de ses composants sont manquants ou corrompus.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI à besoin de {0} pour fonctionner, mais il n'a pas été trouvé sur votre système.", "UniGetUI startup page:": "Page de démarrage d'UniGetUI :", "UniGetUI updater": "Mise à jour d'UniGetUI", "UniGetUI version {0} is being downloaded.": "La version {0} d'UniGetUI est en cours de téléchargement.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} est prêt à être installé.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Désins<PERSON><PERSON> Scoop (et ses paquets)", "Uninstall and more": "Désinstaller et plus", "Uninstall and remove data": "Désinstaller et supprimer les données", "Uninstall as administrator": "Désinstaller en tant qu'administrateur", "Uninstall canceled by the user!": "Désinstallation annulée par l'utilisateur !", "Uninstall failed": "Échec de la désinstallation", "Uninstall options": "Options de désinstallation", "Uninstall package": "Désins<PERSON><PERSON> le paquet", "Uninstall package, then reinstall it": "Désins<PERSON><PERSON> le paquet, puis le réinstaller", "Uninstall package, then update it": "D<PERSON><PERSON><PERSON><PERSON> le paquet, puis le mettre à jour", "Uninstall previous versions when updated": "Désinstaller les versions précédentes lors de la mise à jour", "Uninstall selected packages": "Désinstaller les paquets sélectionnés", "Uninstall selection": "Désinstaller la sélection", "Uninstall succeeded": "Désinstallation réussie", "Uninstall the selected packages with administrator privileges": "Désinstaller les paquets sélectionnés avec les privilèges d'administrateur", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Les paquets désinstallables avec l'origine listé comme \"{0}\" ne sont publiés sur aucun gestionnaire de paquets, donc il n'y a pas d'information disponible à leur sujet.", "Unknown": "Inconnu", "Unknown size": "<PERSON><PERSON> inconnue", "Unset or unknown": "Non défini ou inconnu", "Up to date": "À jour", "Update": "Mettre à jour", "Update WingetUI automatically": "Mettre à jour WingetUI automatiquement", "Update all": "Tout mettre à jour", "Update and more": "Mettre à jour et plus", "Update as administrator": "Mettre à jour en tant qu'administrateur", "Update check frequency, automatically install updates, etc.": "Fréquence de vérification des mises à jour, installation automatique des mises à jour, etc.", "Update checking": null, "Update date": "Date de mise à jour", "Update failed": "Échec de la mise à jour", "Update found!": "Mise à jour trouvée !", "Update now": "Mettre à jour maintenant", "Update options": "Options de mise à jour", "Update package indexes on launch": "Mettre à jour les index des paquets au lancement", "Update packages automatically": "Mettre à jour les paquets automatiquement", "Update selected packages": "Mettre à jour les paquets sélectionnés", "Update selected packages with administrator privileges": "Mettre à jour les paquets sélectionnés avec les privilèges d'administrateur", "Update selection": "Mettre à jour la sélection", "Update succeeded": "Mise à jour réussie", "Update to version {0}": "Mettre à jour vers la version {0}", "Update to {0} available": "Mise à jour vers {0} disponible", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Mettre à jour automatiquement les portfiles Git de vcpkg (Git doit être installé)", "Updates": "<PERSON>ses à jour", "Updates available!": "Mises à jour disponibles !", "Updates for this package are ignored": "Les mises à jour pour ce paquet sont ignorées", "Updates found!": "Mises à jour trouvées !", "Updates preferences": "Préférences de mises à jour", "Updating WingetUI": "<PERSON>se à jour de WingetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Utiliser l'ancien WinGet fourni au lieu de cmdlets PowerShell", "Use a custom icon and screenshot database URL": "Utiliser une base de données d'icônes et de captures d'écran personnalisées", "Use bundled WinGet instead of PowerShell CMDlets": "Utiliser WinGet fourni au lieu de cmdlets PowerShell", "Use bundled WinGet instead of system WinGet": "Utiliser WinGet intégré au lieu de WinGet du système", "Use installed GSudo instead of UniGetUI Elevator": "Utiliser GSudo (si installé) à la place du UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Utiliser GSudo installé au lieu de celui fourni", "Use system Chocolatey": "Utiliser Chocolatey du système", "Use system Chocolatey (Needs a restart)": "Utiliser Chocolatey du système (nécessite un redémarrage)", "Use system Winget (Needs a restart)": "Utiliser WinGet du système (nécessite un redémarrage)", "Use system Winget (System language must be set to english)": "Utiliser WinGet du système (La langue du système doit être l'anglais)", "Use the WinGet COM API to fetch packages": "Utiliser l'API COM de WinGet pour récupérer des paquets", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Utiliser le module PowerShell WinGet au lieu de l'API COM WinGet", "Useful links": "Liens utiles", "User": "Utilisa<PERSON>ur", "User interface preferences": "Paramètres de l'interface utilisateur", "User | Local": "Utilisateur | Local", "Username": "Nom d'utilisateur", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "L'utilisation de Winget implique l'acceptation de la licence GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Utiliser WingetUI implique l'acceptation de la licence MIT (MIT License)", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Le chemin racine de vcpkg n'a pas été trouvé. Veuillez définir la variable d'environnement %VCPKG_ROOT% ou définissez la dans les paramètres d'UniGetUI", "Vcpkg was not found on your system.": "Vcpkg n'a pas été trouvé sur votre système.", "Verbose": "Verbose", "Version": "Version", "Version to install:": "Version à installer :", "Version:": "Version :", "View GitHub Profile": "Voir le profil GitHub", "View WingetUI on GitHub": "Voir WingetUI sur GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Voir le code source de UniGetUI. De là, vous pouvez signaler des bugs ou suggérer des fonctionnalités, ou même contribuer directement au projet UniGetUI.", "View mode:": "Mode d'affichage :", "View on UniGetUI": "Voir sur UniGetUI", "View page on browser": "Voir la page dans le navigateur", "View {0} logs": "Voir les journaux de {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Attendez que l'appareil soit connecté à Internet avant d'effectuer des tâches nécessitant une connexion à Internet", "Waiting for other installations to finish...": "En attente d'autres installations pour terminer...", "Waiting for {0} to complete...": "Attente de la fin de {0}...", "Warning": "Avertissement", "Warning!": "Attention !", "We are checking for updates.": "Nous vérifions les mises à jour.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Nous n'avons pas pu charger les informations détaillées de ce paquet, parce qu'elles n'ont été trouvées dans aucune de vos sources de paquets.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Nous n'avons pas pu charger les informations détaillées de ce paquet, parce qu'il n'a pas été installé depuis un gestionnaire de paquets disponible.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Nous n'avons pas pu {action} {package}. Veuillez réessayer plus tard. Cliquez sur \"{showDetails}\" pour obtenir les journaux de l'installateur.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Nous n'avons pas pu {action} {package}. Veuillez réessayer plus tard. Cliquez sur \"{showDetails}\" pour obtenir les journaux du désinstallateur.", "We couldn't find any package": "Nous n'avons trouvé aucun paquet", "Welcome to WingetUI": "Bienvenue dans WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Lors de l'installation de paquets par lot, réinstaller les paquets déjà installés", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Lorsque de nouveaux raccourcis sont détectés, supprimez-les automatiquement au lieu d'afficher cette boîte de dialogue.", "Which backup do you want to open?": "Quelle sauvegarde voulez-vous ouvrir ?", "Which package managers do you want to use?": "Quels gestionnaires de paquets souhaitez-vous utiliser ?", "Which source do you want to add?": "Quelle source voulez-vous ajouter ?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Alors que WinGet peut être utilisé avec WingetUI, WingetUI peut être utilisé avec d'autres gestionnaires de paquets, ce qui peut porter à confusion. WingetUI a été conçu pour fonctionner seulement avec WinGet, mais ce n'est plus le cas à présent, et par conséquent WingetUI ne représente pas ce que ce projet vise à devenir.", "WinGet could not be repaired": "WinGet n'a pas pu être réparé", "WinGet malfunction detected": "Dysfonctionnement de WinGet détecté", "WinGet was repaired successfully": "WinGet a été réparé avec succès", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - Tout est à jour", "WingetUI - {0} updates are available": "WingetUI - {0} mises à jour sont disponibles", "WingetUI - {0} {1}": "WingetUI - {1} de {0}", "WingetUI Homepage": "<PERSON> d'accue<PERSON> de WingetUI", "WingetUI Homepage - Share this link!": "Page d'accueil de WingetUI - Partagez ce lien !", "WingetUI License": "Licence de WingetUI", "WingetUI Log": "Journaux de WingetUI", "WingetUI Repository": "Dépôt de WingetUI", "WingetUI Settings": "Paramètres de WingetUI", "WingetUI Settings File": "Fichier de configuration de WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI utilise les bibliothèques suivantes. Sans elles, WingetUI n'aurait pas pu exister.", "WingetUI Version {0}": "WingetUI Version {0}", "WingetUI autostart behaviour, application launch settings": "Comportement du démarrage automatique de WingetUI, paramètres de lancement de l'application", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI peut vérifier si votre logiciel a des mises à jour disponibles, et les installer automatiquement si vous le voulez", "WingetUI display language:": "Langue d'affichage de WingetUI :", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI a été exécuté en tant qu'administrateur, ce qui n'est pas recommandé. Lorsque WingetUI est exécuté en tant qu'administrateur, TOUTES les opérations lancées depuis WingetUI auront des privilèges d'administrateur. Vous pouvez toujours utiliser le programme, mais nous recommandons fortement de ne pas exécuter WingetUI avec les privilèges d'administrateur.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI a été traduit dans plus de 40 langues grâce aux traducteurs bénévoles. Merci 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI n'a pas été traduit automatiquement. Les utilisateurs suivants ont été en charge des traductions :", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI est une application qui vous permet de gérer vos logiciels plus facilement, en fournissant une interface graphique tout-en-un pour vos gestionnaires de paquets en ligne de commandes.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI est renommé pour souligner la différence entre WingetUI (l'interface que vous utilisez actuellement) et WinGet (un gestionnaire de paquet développé par Microsoft avec lequel je n'ai aucun lien)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI est en cours de mise à jour. Une fois celle-ci finie, WingetUI redémarrera automatiquement", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI est gratuit, et le sera pour toujours. Aucune publicité, pas de carte de crédit, pas de version premium. 100% gratuit, pour toujours.", "WingetUI log": "Journaux de WingetUI", "WingetUI tray application preferences": "Paramètres de l'icône de WingetUI dans la zone de notifcation", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI utilise les bibliothèques suivantes. Sans elles, WingetUI n'aurait pas pu exister.", "WingetUI version {0} is being downloaded.": "WingetUI version {0} est en cours de téléchargement.", "WingetUI will become {newname} soon!": "WingetUI va devenir {newname} prochainement !", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI ne vérifiera pas les mises à jour périodiquement. Elle seront toujours vérifiées au lancement, mais vous n'en serez pas averti.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI affichera une invite UAC à chaque fois qu'un paquet nécessitera une élévation pour être installé.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI sera prochainement nommé {newname}. <PERSON><PERSON> ne représentera aucun changement dans l'application. <PERSON><PERSON> (le développeur), je poursuivrai le développement de ce projet comme je le fais actuellement, mais sous un autre nom.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI n'aurait pas été possible sans l'aide de nos chers contributeurs. Consultez leur profil <PERSON>, WingetUI ne serait pas possible sans eux !", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI n'aurait pas été possible sans l'aide des contributeurs. Merci à tous 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} est prêt à être installé.", "Write here the process names here, separated by commas (,)": "Saisissez ici les noms des processus, séparés par des virgules (,)", "Yes": "O<PERSON>", "You are logged in as {0} (@{1})": "Vous êtes connecté en tant que {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Vous pouvez modifier ce comportement dans les paramètres de sécurité d'UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Vous pouvez définir les commandes qui seront exécutées avant ou après l'installation, la mise à jour ou la désinstallation de ce paquet. Elles seront exécutées dans une invite de commande, de sorte que les scripts CMD fonctionneront ici.", "You have currently version {0} installed": "Vous avez actuellement la version {0} installée", "You have installed WingetUI Version {0}": "Vous avez installé WingetUI Version {0}", "You may lose unsaved data": "Vous risquez de perdre des données non enregistrées", "You may need to install {pm} in order to use it with WingetUI.": "Il est possible que vous ayez besoin d'installer {pm} pour pouvoir l'utiliser avec WingetUI.", "You may restart your computer later if you wish": "Vous pouvez redémarrer votre ordinateur plus tard si vous le souhaitez", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Vous ne serez invité à le faire qu'une seule fois, et les droits d'administrateur seront accordés aux paquets qui les demandent.", "You will be prompted only once, and every future installation will be elevated automatically.": "Vous ne serez invité à le faire qu'une seule fois, et toutes les installations futures seront élevées automatiquement.", "You will likely need to interact with the installer.": "Vous devrez probablement interagir avec le programme d'installation.", "[RAN AS ADMINISTRATOR]": "EXÉCUTÉ EN TANT QU'ADMINISTRATEUR", "buy me a coffee": "m'offrir un café", "extracted": "extrait", "feature": "fonctionnalité", "formerly WingetUI": "anciennement WingetUI", "homepage": "page d'accueil", "install": "installer", "installation": "installation", "installed": "installé", "installing": "installation en cours", "library": "bibliothèque", "mandatory": "obligatoire", "option": "option", "optional": "optionnel", "uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstallation": "Désinstallation", "uninstalled": "désinstallé", "uninstalling": "désinstallation en cours", "update(noun)": "mise à jour", "update(verb)": "mettre à jour", "updated": "mis à jour", "updating": "Mise à jour en cours", "version {0}": "version {0}", "{0} Install options are currently locked because {0} follows the default install options.": "Les options d'installation de {0} sont actuellement bloquées parce que {0} suit les options d'installation par défaut.", "{0} Uninstallation": "Désinstallation de {0}", "{0} aborted": "{0} interrompue", "{0} can be updated": "{0} peut être mis à jour", "{0} can be updated to version {1}": "{0} peut être mis à jour vers la version {1}", "{0} days": "{0} jours", "{0} desktop shortcuts created": "{0} r<PERSON><PERSON><PERSON><PERSON>(s) sur le bureau créé(s)", "{0} failed": "{0} a écho<PERSON>", "{0} has been installed successfully.": "{0} a été installé avec succès", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} a été installé avec succès. Il est recommandé de redémarrer UniGetUI pour terminer l'installation.", "{0} has failed, that was a requirement for {1} to be run": "{0} a <PERSON><PERSON><PERSON>, c'était une condition pour que {1} soit exécuté", "{0} homepage": "Page d'accueil de {0}", "{0} hours": "{0} heures", "{0} installation": "Installation de {0}", "{0} installation options": "Options d'installation de {0}", "{0} installer is being downloaded": "Le programme d'installation {0} est en cours de téléchargement", "{0} is being installed": "{0} est en cours d'installation", "{0} is being uninstalled": "{0} est en cours de désinstallation", "{0} is being updated": "{0} est en cours de mise à jour", "{0} is being updated to version {1}": "{0} est en cours de mise à jour vers la version {1}", "{0} is disabled": "{0} est désactivé(e)", "{0} minutes": "{0} minutes", "{0} months": "{0} mois", "{0} packages are being updated": "{0} paquets sont en cours de mise à jour", "{0} packages can be updated": "{0} paquets peuvent être mis à jour", "{0} packages found": "{0} paquets trouvés", "{0} packages were found": "{0} paquets ont été trouvés", "{0} packages were found, {1} of which match the specified filters.": "{0} paquets ont été trouvés, {1} d'entre eux correspond·ent aux filtres spécifiés.", "{0} selected": "{0} sélectionné", "{0} settings": "Réglages de {0}", "{0} status": "Statut de {0} ", "{0} succeeded": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} update": "Mise à jour de {0}", "{0} updates are available": "{0} mises à jour sont disponibles", "{0} was {1} successfully!": "{0} a été {1} avec succès !", "{0} weeks": "{0} semaines", "{0} years": "{0} ans", "{0} {1} failed": "{0} {1}  a <PERSON><PERSON><PERSON>", "{package} Installation": "Installation de {package}", "{package} Uninstall": "Désinstallation de {package}", "{package} Update": "Mise à jour de {package}", "{package} could not be installed": "{package} n'a pas pu être installé", "{package} could not be uninstalled": "{package} n'a pas pu être désinstallé", "{package} could not be updated": "{package} n'a pas pu être mis à jour", "{package} installation failed": "L'installation de {package} a échoué", "{package} installer could not be downloaded": "Le programme d'installation de {package} n'a pas pu être téléchargé", "{package} installer download": "Téléchargement du programme d'installation de {package}", "{package} installer was downloaded successfully": "Le programme d'installation de {package} a été téléchargé avec succès", "{package} uninstall failed": "La désinstallation de {package} a échoué", "{package} update failed": "La mise à jour de {package} a échoué", "{package} update failed. Click here for more details.": "La mise à jour de {package} a échoué. Cliquez ici pour plus de détails.", "{package} was installed successfully": "{package} a été installé avec succès", "{package} was uninstalled successfully": "{package} a été désinstallé avec succès", "{package} was updated successfully": "{package} a été mis à jour avec succès", "{pcName} installed packages": "Paquets installés sur {pcName}", "{pm} could not be found": "{pm} n'a pas pu être trouvé", "{pm} found: {state}": "{pm} a trouvé : {state}", "{pm} is disabled": "{pm} est désactivé", "{pm} is enabled and ready to go": "{pm} est activé et prêt à être utilisé", "{pm} package manager specific preferences": "Paramètres spécifiques au gestionnaire de paquets {pm}", "{pm} preferences": "Paramètres de {pm}", "{pm} version:": "Version de {pm} :", "{pm} was not found!": "{pm} n'a pas été trouvé !"}