# Warp Configuration for UniGetUI

This configuration optimizes <PERSON><PERSON>'s indexing of the UniGetUI repository by excluding binary files, build artifacts, and other non-searchable content.

## Indexing Exclusions

### Large Binary Files (Primary Issue)
- InstallerExtras/appsdk.exe          # 60.75 MB installer
- src/UniGetUI.PackageEngine.Managers.Chocolatey/choco-cli/choco.exe  # 11.11 MB
- media/UniGetUI Media.pptx           # 6.72 MB presentation
- src/UniGetUI.PackageEngine.Managers.WinGet/winget-cli_x64/WindowsPackageManager.dll  # 6.54 MB
- src/UniGetUI.PackageEngine.Managers.WinGet/winget-cli_x64/libsmartscreenn.dll       # 5.78 MB

### Binary File Types
- *.exe
- *.dll
- *.msi
- *.zip
- *.7z
- *.rar
- *.iso
- *.bin

### Image Files (Non-searchable)
- *.png
- *.jpg
- *.jpeg
- *.gif
- *.bmp
- *.ico
- *.svg
- *.webp

### Media Files
- *.pptx
- *.pdf
- *.mp4
- *.avi
- *.mov
- *.wmv

### Build Artifacts and Generated Files
- **/bin/
- **/obj/
- **/out/
- **/outpublish/
- **/packages/
- **/.vs/
- **/.vscode/
- **/Generated Files/
- *.user
- *.old

### Directories to Exclude
- InstallerExtras/
- media/
- output/
- env/
- unigetui_bin/
- .git/

### Package Manager Artifacts
- src/UniGetUI/choco-cli/logs/
- src/UniGetUI/choco-cli/lib/
- src/UniGetUI/choco-cli/lib-bad/
- src/UniGetUI/choco-cli/.chocolatey/
- src/UniGetUI/choco-cli/lib-bkp/
- src/UniGetUI/choco-cli/extensions/
- src/UniGetUI/choco-cli/redirects/

### Log Files
- *.log
- *.nupkg

### Temporary and Cache Files
- *.pyc
- *.tmp
- *.cache
- *~

## Indexing Focus Areas

### Primary Source Code (High Priority)
- src/**/*.cs                         # C# source files
- src/**/*.xaml                       # UI markup files
- src/**/*.csproj                     # Project files

### Configuration Files (Medium Priority)
- *.json
- *.yml
- *.yaml
- *.config
- *.xml
- *.toml

### Documentation (Medium Priority)
- *.md
- *.txt
- README*
- LICENSE*
- CHANGELOG*

### Scripts (Medium Priority)
- *.ps1
- *.py
- *.bat
- *.cmd
- *.sh

## Notes

This configuration excludes approximately 98.4 MB of large binary files that provide no searchable content value, including the problematic 60.75 MB `appsdk.exe` file that was likely causing indexing timeouts.

The remaining ~24 MB of primarily source code, configuration files, and documentation should index quickly and provide comprehensive search capabilities across the actual codebase.

Generated by Warp diagnostic analysis on 2025-08-18.
