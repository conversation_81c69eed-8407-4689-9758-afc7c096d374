{"\"{0}\" is a local package and can't be shared": "\"{0}\" ლოკალური პაკეტია და მისი გაზიარება შეუძლებელია", "\"{0}\" is a local package and does not have available details": "\"{0}\" ლოკალური პაკეტია და არ აქვს ხელმისაწვდომი დეტალები", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" ლოკლაური პაკეტია და არ არის თავსებადი ამ ფუნქციასთან", "(Last checked: {0})": "(ბოლო შემოწმება: {0})", "(Number {0} in the queue)": "რიგის ნომერი: {0}", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@marticliment, @ppvnf", "0 packages found": "ნაპოვნია 0 პაკეტი", "0 updates found": "ნაპოვნია 0 განახლება", "1 - Errors": "1 - შეცდომა", "1 day": "1 დღე", "1 hour": "1 საათი", "1 month": "1 თვე", "1 package was found": "ნაპოვნია 1 პაკეტი", "1 update is available": "ხელმისაწვდომია 1 განახლება", "1 week": "1 კვირა", "1 year": "1 წელი", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. გადადით {0} ან {1} გვერდზე.", "2 - Warnings": "2 - გაფრთხილება", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. მოძებნეთ პაკეტი(ები) რომლბეიც გინდათ დაამატოთ კრებულში, და მონიშნეთ მათ გასწვრი ყველაზე მარცხენა თოლია.", "3 - Information (less)": "3 - ინფორმაცია (ნაკლები)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. როდესაც შეარჩევთ პაკეტებს, რომელთა დამატება გსურთ კრებულში, იპოვეთ და დააწკაპუნეთ ოფციაზე \"{0}\" ხელსაწყოთა პანელზე.", "4 - Information (more)": "4 - ინფორმაცია (მეტი)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. თქვენი პაკეტები დაემატება კრებულს. შეგიძლიათ გააგრძელოთ პაკეტების დამატება, ან კრებულის ექსპორტი.", "5 - information (debug)": "5 - ინფორმაცია (დებაგი)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "პოპულარული C/C++ ბიბლიოთეკების მმართველი. სავსეა C/C++ ბიბლიოთეკებით და სხვა მასთან დაკავშირებული უტილიტებით.<br> შეიცავს: <b>C/C++ ბიბლიოთეკებს და შესაბამის უტილიტებს</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "რეპოზიტორია სავსე ინსტრუმენტბითა და გამშვები ფაილებით Microsoft .NET ეკოსისტემისთვის.<br> შეიცავს: <b>.NET-თან დაკავშირებულ ინსტრუმენტებსა და სკრიპტებს</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "ინსტრუმენტებით სავსე რეპოზიტორია, რომელიც შექმნილია Microsoft-ის .NET ეკოსისტემის გათვალისწინებით. <br> შეიცავს: <b>.NET-თან ასოცირებულ ინსტრუმენტებს</b>", "A restart is required": "საჭიროა გადატვირთვა", "Abort install if pre-install command fails": "ინსტალაციის შეწყვეტა პრე-ინსტალაციის ბრძანების ჩაშლისას", "Abort uninstall if pre-uninstall command fails": "დეინსტალაციის შეწყვეტა პრე-დეინსტალაციის ბრძანების ჩაშლისას", "Abort update if pre-update command fails": "განახლების შეწყვეტა პრე-განახლების ბრძანების ჩაშლისას", "About": "შესახებ", "About Qt6": "Qt6-ის შესახებ", "About WingetUI": "UniGetUI-ის შესახებ", "About WingetUI version {0}": "UniGetUI-ის {0} ვერსიის შესახებ", "About the dev": "დეველოპერის შესახებ", "Accept": "მიღება", "Action when double-clicking packages, hide successful installations": "მოქმედება პაკეტებზე ორჯერ დაწკაპუნებით, წარმატებული ინსტალაციების დამალვა", "Add": "დამატება", "Add a source to {0}": "{0}-ში წყაროს  დამატება", "Add a timestamp to the backup file names": "ბექაფის ფაილების სახელებზე დროის ანაბეჭდის დამატება", "Add a timestamp to the backup files": "ბექაფის ფაილების სახელებზე დროის ანაბეჭდის დამატება", "Add packages or open an existing bundle": "დაამატეთ პაკეტები ან გახსენით არსებული კრებული", "Add packages or open an existing package bundle": "დაამატეთ პაკეტები ან გახსენით არსებული პაკეტების კრებული", "Add packages to bundle": "პაკეტების კრებულში დამატება", "Add packages to start": "დაამატეთ პაკეტები დასაწყებად", "Add selection to bundle": "მონიშვნის კრებულში დამატება", "Add source": "წყაროს დამატება", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "განახლებების რომლებიც ჩაიშალა 'შესაძლო განახლებები ვერ მოიძებნა' ინგორირებული განახლებების სიაში დამატება", "Adding source {source}": "{source} წყაროს დამატება", "Adding source {source} to {manager}": "{source} წყაროს დამატება {manager}-ში", "Addition succeeded": "წარმატებით დაემატა", "Administrator privileges": "ადმინისტრატორის პრივილეგიები", "Administrator privileges preferences": "ადმინისტრატორის პრივილეგიების პარამეტრები", "Administrator rights": "ადმინისტრატორის უფლებები", "Administrator rights and other dangerous settings": "ადმინისტრატორის უფლებები და სხვა საშიში პარამეტრები", "Advanced options": "გაფართოებული ოფციები", "All files": "ყველა ფაილი", "All versions": "ყველა ვერსია", "Allow changing the paths for package manager executables": "პაკეტების მმართველების გამშვებ ფაილების მისმართების ცვლილების დაშვება", "Allow custom command-line arguments": "მორგებული ბრძანების ზოლის არგუმენტების დაშვება", "Allow importing custom command-line arguments when importing packages from a bundle": "მორგებული ბრძანების ზოლის არგუმენტების იმპორტირების დაშვება პაკეტების კრებულიდან იმპორტისას", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "მორგებული პრეინსტალაციის და პოსტ ინსტალაციის ბრძანებების იმპორტირების დაშვება პაკეტების კრებულიდან იმპორტისას", "Allow package operations to be performed in parallel": "პაკეტებზე პარალელური ოპერაციების შესრულების დაშვება", "Allow parallel installs (NOT RECOMMENDED)": "პარალელური ინსტალაციების დაშვება (არ არის რეკომენდებული)", "Allow pre-release versions": "პრერელიზ ვერსიების დაშვება", "Allow {pm} operations to be performed in parallel": "{pm} ოპერაციების პარალელურად შესრულების დაშვება", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "ალტერნატიულად, თქვენ ასევე შეგიძლიათ დააყენოთ {0} Windows PowerShell-ში შემდეგი ბრძანების გაშვებით:", "Always elevate {pm} installations by default": "ყოველთვის ელევაცია  {pm} ინსტალაციებისას ნაგულისხმევად", "Always run {pm} operations with administrator rights": "{pm}-ის ოპერაციების ყოველთვის ადმინისტრატორის უფლებით გაშვება", "An error occurred": "დაფიქსირდა შეცდომა", "An error occurred when adding the source: ": "დაფიქსირდა შეცდომა წყაროს დამატებისას:", "An error occurred when attempting to show the package with Id {0}": "შეცდომა დაფიქსირდა პაკეტის დათვალიერებისას, რომლის ID არის {0}", "An error occurred when checking for updates: ": "შეცდომა დაფიქსირდა განახლებების შემოწმებისას:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "შეცდომა ბექაფის ჩატვირთვისას:", "An error occurred while logging in: ": "შეცდომა შესვლისას:", "An error occurred while processing this package": "შეცდომა დაფიქსირდა ამ პაკეტის დამუშავებისას", "An error occurred:": "დაფიქსირდა შეცდომა", "An interal error occurred. Please view the log for further details.": "დაფიქსირდა შიდა შეცდომა.  დამატებითი დეტალებისთვის გთხოვთ ნახოთ ჟურნალი.", "An unexpected error occurred:": "დაფიქსირდა მოულოდნელი შეცდომა:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "გაუთვალისწინებელი პრობლემა დაფიქსირდა WinGet-ის შეკეთებისას. გთხოვთ მოგვიანებით ცადეთ", "An update was found!": "ნაპოვნია განახლება!", "Android Subsystem": "Android ქვესისტემა", "Another source": "სხვა წყარო", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "ინსტალაციის ან განახლების ოპერაციის დროს შექმნილი ნებისმიერი ახალი მალსახმობი ავტომატურად წაიშლება, ნაცვლად იმისა, რომ პირველად აღმოჩენისას გამოჩნდეს დადასტურების მოთხოვნა.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "UniGetUI-ის გარეთ შექმნილი ან შეცვლილი მალსახმობი იგნორირებული იქნება. მათ დამატებას {0} ღილაკის საშუალებით შეძლებთ.", "Any unsaved changes will be lost": "ნებისმიერი შეუნახავი ცვლილება დაიკარგება", "App Name": "აპის სახელი", "Appearance": "იერსახე", "Application theme, startup page, package icons, clear successful installs automatically": "აპლიკაციის თემა, სასტარტო გვერდი, პაკეტების ხატულები, წარმატებული ინსტალაციების ავტომატურად წაშლა", "Application theme:": "აპლიკაციის თემა:", "Apply": "გამოყენება", "Architecture to install:": "დასაინსტალირებელი არქიტექტურა:", "Are these screenshots wron or blurry?": "ეს სქრონშოტები არასწორი ან გადღაბნილია?", "Are you really sure you want to enable this feature?": "დარწმუნებული ხართ, რომ გსურთ ამ ფუნქციის ჩართვა?", "Are you sure you want to create a new package bundle? ": "დარწმუნებული ხართ, რომ გინდათ ახალი პაკეტების კრებულის შექმნა?", "Are you sure you want to delete all shortcuts?": "დარწმუნებული ხართ, რომ გინდათ წაშალოთ ყველა მალსახმობი?", "Are you sure?": "დარწმუნებული ხართ?", "Ascendant": "ასცენდენტი", "Ask for administrator privileges once for each batch of operations": "ადმინისტრატორის პრივილეგიების მოთხოვნა მხოლოდ ერჯერადად ოპერაციების ჯგუფის გაშვებისას", "Ask for administrator rights when required": "ადმინისტრატორის უფლებების მოთხოვნა როცა საჭიროა", "Ask once or always for administrator rights, elevate installations by default": "ადმინისტრატორის უფლებების ერთხელ ან ყოველთვის მოთხოვნა, ინსტალაციების პროვილეგიებით გაშვება ნაგულისხმევად", "Ask only once for administrator privileges": "ადმინისტრატორის უფლებების მხოლოდ ერთხელ მოთხოვნა", "Ask only once for administrator privileges (not recommended)": "ადმინისტრატორის უფლებების მხოლოდ ერთხელ მოთხოვნა (არ არის რეკომენდებული)", "Ask to delete desktop shortcuts created during an install or upgrade.": "სამუშაო მაგიდის მალსახმობების წაშლის მოთხოვნა ინსტალაციის ან განახლებისას", "Attention required": "საჭიროა ყურადღება", "Authenticate to the proxy with an user and a password": "პროქსისთან აუთენტიფიკაცია მომხმარებლით და პაროლით", "Author": "ავტორი", "Automatic desktop shortcut remover": "ავტომატური სამუშაო მაგიდის მალსახმობების წამშლელი", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "დაყენებული პაკეტების სიის ავტომატურად შენახვა მათი მარტივი აღდგენისათვის.", "Automatically save a list of your installed packages on your computer.": "დაყენებული პროგრამების სიის შენახვა თქვენს კომპიუტერზე.", "Autostart WingetUI in the notifications area": "UniGetUI-ს ავტომატური გაშვება შეტყობინებების პანელში", "Available Updates": "ხელმისაწვდომი განახლებები", "Available updates: {0}": "ხელმისაწვდომი განახლებები: {0}", "Available updates: {0}, not finished yet...": "ხელმისაწვდომი განახლებებ: {0}, ჯერ არ დასრულებულა...", "Backing up packages to GitHub Gist...": "პაკეტების ბექაფი GitHub Gist-ში...", "Backup": "ბექაფი", "Backup Failed": "ბექაფი ჩაიშალა", "Backup Successful": "წარმატებული ბექაფი", "Backup and Restore": "ბექაფი და აღდგენა", "Backup installed packages": "დაყენებული პაკეტების ბექაფი", "Backup location": "ბექაფბის ლოკაცია", "Become a contributor": "გახდი მონაწილე", "Become a translator": "გახდი მთარგმნელი", "Begin the process to select a cloud backup and review which packages to restore": "ღღუბლოვანი ბექაფის შერჩევის პროცესის დაწყება და აღსადგენი პაკეტების მიმოხილვა", "Beta features and other options that shouldn't be touched": "ბეტა ფუნქციონალი და სხვა პარამეტრები, რომლებსაც ხელი არ უნდა ახლოთ", "Both": "ორივე", "Bundle security report": "კრებულის უსაფრთხოების რეპორტი", "But here are other things you can do to learn about WingetUI even more:": "მაგრამ აქ არის რაღაცეები, რისი გაკეთებაც შეგიძლიათ UniGetUI-ის შესახებ მეტის გაგებისთვის:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "პაეკტების მენეჯერის გამორთვის შემდეგ თქვენ ვერ ნახავთ მისი პაკეტების განახლებებს.", "Cache administrator rights and elevate installers by default": "ადმინისტრატორის უფლებების ქეშირება და ინსტალატორების პრივილეგიებით გაშვება ნაგულისხმევად", "Cache administrator rights, but elevate installers only when required": "ადმინისტრატორის უფლებების ქეშირება, მაგრამ ინსტალატორების პროვილეგიებით გაშვება მხოლოდ საჭიროების შემთხვევაში", "Cache was reset successfully!": "ქეში წარმატებით დარესეტდა!", "Can't {0} {1}": "ვერ მოხერხდა {0}{1}", "Cancel": "გაუქმება", "Cancel all operations": "ყველა ოპერაციის გაუქმება", "Change backup output directory": "ბექაფის დირექტორიის შეცვლა", "Change default options": "ნაგულისხმევი პარამეტრების შეცვლა", "Change how UniGetUI checks and installs available updates for your packages": "შეცვალე თუ როგორ ამოწმებს და აყენებს UniGetUI ხელმისაწვდომ განახლებებს თქვენი პაკეტებისთვის", "Change how UniGetUI handles install, update and uninstall operations.": "შეცვალეთ თუ როგორ შეასრულებს UniGetUI ინსტალაციის, განახლების და დეინსტალაციის ოპერაციებს.", "Change how UniGetUI installs packages, and checks and installs available updates": "შეცვალე თუ როგორ ამოწმებს და აყენებს UniGetUI ხელმისაწვდომ განახლებებს თქვენი პაკეტებისთვის", "Change how operations request administrator rights": "შეცვალეთ, თუ როგორ ითხოვენ ოპერაციები ადმინისტრატორის უფლებებს", "Change install location": "ინსტალაციის ლოკაციის შეცვლა", "Change this": "შეცვალე ეს", "Change this and unlock": "შეცვალე ეს და განბლოკე", "Check for package updates periodically": "პაკეტის განახლების პერიოდული შემოწმება", "Check for updates": "განახლებების შემოწმება", "Check for updates every:": "განახლებების შემოწმება ყოველ:", "Check for updates periodically": "განახლებების პერიოდული შემოწმება", "Check for updates regularly, and ask me what to do when updates are found.": "განახლებების რეგულარული შემოწმება და შემდეგ შემეკითხე რა ვქნა, როცა განახლებას ვიპოვი.", "Check for updates regularly, and automatically install available ones.": "განახლებების რეგულარული შემოწმება და ხელმისაწვდომი განახლებების ავტომატური ინსტალაცია.", "Check out my {0} and my {1}!": "ნახეთ ჩემი {0} და ჩემი {1}!", "Check out some WingetUI overviews": "ნახეთ ზოგიერთი UniGetUI-ის განხილვა", "Checking for other running instances...": "მოწმდება სხვა გაშვებული ინსტანციები...", "Checking for updates...": "განახლებების შემოწმება...", "Checking found instace(s)...": "ნაპოვნი ინსტაციის(ების) შემოწმება...", "Choose how many operations shouls be performed in parallel": "აირჩიე რამდენი ოპერაცია უნდა შესრულდეს პარალელურად", "Clear cache": "ქეშის გაწმენდა", "Clear finished operations": "დასრულებული ოპერაციების გასუფთავება", "Clear selection": "მონიშვნის გაწმენდა", "Clear successful operations": "წარმატებული ოპერაციების გაწმენდა", "Clear successful operations from the operation list after a 5 second delay": "წარმატებული ოპერაციების სიიდან ამოღება 5 წამიანი დაყოვნების შემდეგ", "Clear the local icon cache": "ლოკალური ხატულების ქეშის გაწმენდა", "Clearing Scoop cache - WingetUI": "Scoop-ის ქეშის გაწმენდა - UniGetUI", "Clearing Scoop cache...": "Scoop-ის ქეშის გაწმენდა", "Click here for more details": "დაწკაპეთ აქ მეტი დეტალისთვის", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "დაწკაპეთ ინსტალაციაზე, რომ დაიწყოს ინსტალაციის პროცესი. თუ გამოტოვებთ ინსტალაცია, UniGetUI შესაძლოა არ იმუშაოს გამართულად", "Close": "დახურვა", "Close UniGetUI to the system tray": "UniGetUI-ის სისტემურ პანელში ჩახურვა", "Close WingetUI to the notification area": "UniGetUI-ის შეტყობინებების არეში ჩახურვა", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "ღღუბლოვანი ბექაფი იყენებს პრივატულ GitHub Gist-ს, რომ შეინახოს დაყენებული პაკეტების სია", "Cloud package backup": "პაკეტების ღრუბლოვანი ბექაფი", "Command-line Output": "ბრძანების-ზოლის გამოსავალი", "Command-line to run:": "ბრძანების ზოლში გაეშვება:", "Compare query against": "მოთხოვნის შემოწმება წინააღმდეგ", "Compatible with authentication": "თავსებადია აუთენტიფიკაციასთან", "Compatible with proxy": "თავსებადია პროქსისთან", "Component Information": "კომპონენტის ინფორმაცია", "Concurrency and execution": "კონკურენცია და გაშვება", "Connect the internet using a custom proxy": "ინტერნეტთან მორგებული პროქსით პასუხი", "Continue": "გაგრძელება", "Contribute to the icon and screenshot repository": "ხატულებისა და სქრონშოტების რეპოზიტორიაში დამატება", "Contributors": "კონტრიბუტორები", "Copy": "კოპირება", "Copy to clipboard": "გაცვლის ბუფერში კოპირება", "Could not add source": "წყარო ვერ დაემატა", "Could not add source {source} to {manager}": "წყარო {source} ვერ დაემატა {manager}-ში", "Could not back up packages to GitHub Gist: ": "ვერ შევნიახე პაკეტების ბეაფი GitHub Gist-ში: ", "Could not create bundle": "კრებული ვერ შეიქმნა", "Could not load announcements - ": "ვერ მოხერხდა ანონსების ჩატვირთვა -", "Could not load announcements - HTTP status code is $CODE": "ვერ მოხერხდა ანონსების ჩატვირთვა - HTTP სტატუსის კოდია $CODE", "Could not remove source": "წყარო ვერ წაიშალა", "Could not remove source {source} from {manager}": "ვერ წაიშალა {source} წყარო {manager}-დან", "Could not remove {source} from {manager}": "ვერ წაიშალა {source} წყარო {manager}-დან", "Create .ps1 script": null, "Credentials": "ანგარიშის მონაცემები", "Current Version": "მიმდინარე ვერსია", "Current status: Not logged in": "მიმდინარე სტატუსი: არ არის შესული", "Current user": "მიმდინარე მომხმარებელი", "Custom arguments:": "მორგებული არგუმენტები", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "მორგებულ ბრძანების არგუმენტებს შეუძლია შეცვალოს პროცესი რომლითაც პროგრამები ინსტალირდება, ახლდება ან დეინსტალირდება ისე, რომ UniGetUI ვერ შეძლებს მის კონტროლ. მორგებულ ბრძანების არგუმენტებს შეუძლია პაკეტების დაზიანება. გამოიყენეთ სიფრთხილის ზომების დაცვით.", "Custom command-line arguments:": "მორგებული ბრძანების-ზოლის არგუმენტები:", "Custom install arguments:": "მორგებული ინსტალაციის არგუმენტები:", "Custom uninstall arguments:": "მორგებული დეინსტალაციის არგუმენტები:", "Custom update arguments:": "მორგებული განახლების არგუმენტები:", "Customize WingetUI - for hackers and advanced users only": "UniGetUI-ის მორგება - მხოლოდ ჰაკერებისთვის და გამოცდილი მომხმარებლებისთვის", "DEBUG BUILD": "დებაგ ანაწყობი", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "პასუხისმგებლობის უარყოფა: ჩვენ არ ვართ პასუხისმგებელი ჩამოტვირთულ პაკეტებზე. გთხოვთ დააყენოთ მხოლოდ სანდო პროგრამები.", "Dark": "მუქი", "Decline": "უარყოფა", "Default": "ნაგულისხმევი", "Default installation options for {0} packages": "ნაგულისმევი ინსტალაციის პარამერები {0} პაკეტებისთვის", "Default preferences - suitable for regular users": "ნაგულისხმევი პარამეტრები - მორგებულია ჩვეულებრივ მომხმარებლებზე", "Default vcpkg triplet": "vcpkg-ის ნაგულისხმევი ტრიპლეტი", "Delete?": "წაიშალოს?", "Dependencies:": "დამოკიდებულებები:", "Descendant": "დესცენდენტი", "Description:": "აღწერა", "Desktop shortcut created": "შეიქმნა მალსახმობი სამუშაო მაგიდაზე", "Details of the report:": "რეპორტის დეტალები:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "პროგრამირება შრომატევადია, და ეს აპლიკაცია უფასოა. მაგრამ თუ შენ მოგწონს ყოველთვის შეგიძლია <b> მიყიდო ყავა </b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "პირდაპირ დაყენება პაკეტზე \"{discoveryTab}\"-ში ორჯერ დაწკაპებით (ნაცვლად პაკეტის ინფოს ჩვენებისა)", "Disable new share API (port 7058)": "გამორთეთ ახალი გაზიარების API (პორტი 7058)", "Disable the 1-minute timeout for package-related operations": "ოპერაციებისთვის 1 წუთიანი პაკეტებთან დაკავშირებული ვადის გამორთვა", "Disclaimer": "პასუხისმგებლობის უარყოფა", "Discover Packages": "აღმოაჩინეთ პაკეტები", "Discover packages": "აღმოაჩინეთ პაკეტები", "Distinguish between\nuppercase and lowercase": "გაარჩიე მაღალი და დაბალი რეგისტრი", "Distinguish between uppercase and lowercase": "განასხვავე დიდსა და პატარა რეგისტრს შორის", "Do NOT check for updates": "არ შემოწმდეს განახლებები", "Do an interactive install for the selected packages": "ინტერაქტიული ინსტალაციის შესრულება არჩეული პაკეტებისთვის\n", "Do an interactive uninstall for the selected packages": "ინტერაქტიული დეინსტალაციის შესრულება არჩეული პაკეტებისთვის\n", "Do an interactive update for the selected packages": "ინტერაქტიული განახლების შესრულება არჩეული პაკეტებისთვის\n", "Do not automatically install updates when the battery saver is on": "არ დაყენდეს განახლებები ავვტომატურად აკუმლატორის დაზოგვის რეჟიმში ", "Do not automatically install updates when the network connection is metered": "არ დაყენდეს ავტომატური განახლებები, როცა ქსელთან კავშირი ლიმიტირებულია (დეპოზიტი)", "Do not download new app translations from GitHub automatically": "არ ჩამოიტვირთოს  აპების ახალი თარგმანები GitHub-დან ავტომატურად", "Do not ignore updates for this package anymore": "არ დაიგნორდეს განახლებები ამ პაკეტისთვის მომავალში", "Do not remove successful operations from the list automatically": "არ წაშალო წარმატებული ოპერაციები ამ სიიდან ავტომატურად ", "Do not show this dialog again for {0}": "არ მაჩვენო ეს დიალოგი {0}-მდე", "Do not update package indexes on launch": "არ განაახლო პაკეტების ინდექსები გაშვებისას", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "ეთანხმებით თუ არა, რომ UniGetUI აგროვებს და აგზავნის გამოყენების ანონიმურ სტატისტიკას, მხოლოდ მომხმარებლის გამოცდილების გაგებისა და გაუმჯობესების მიზნით?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "თქვენთვის სასარგებლოა UniGetUI? შესაძლოა მოგინდეთ მხარი დაუჭიროთ ჩემს სამუშაოს, ამით მე შევძლებ გავაგრძელო UniGetUI-ის განვითარება პაკეტების მართვის საუკეთესო ინტერფეისად.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "თქვენთვის სასარგებლოა UniGetUI? გსურთ მხარი დაუჭიროთ დეველოპერს? თუ ასეა შეგიძლიათ {0}, ეს ძალიან სასარგებლოა!", "Do you really want to reset this list? This action cannot be reverted.": "რეალურად გსურთ ამ სიის დარესეტება? ეს მოქმედება უკან არ ბრუნდება.", "Do you really want to uninstall the following {0} packages?": "ნამდვილად გსურთ წაშალოთ შემდეგი პაკეტები {0} ?", "Do you really want to uninstall {0} packages?": "ნამდვილად გსურთ წაშალოთ პაკეტები {0}", "Do you really want to uninstall {0}?": "ნამდვილად გსურთ წაშალოთ {0}?", "Do you want to restart your computer now?": "ნამდვილად გსურთ თქვენი კომპიუტერის ახლა გადატვირთვა?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "გსურთ თარგმნოთ UniGetUI თქვენს ენაზე? ნახეთ როგორ შეგიძლიათ დაგვეხმაროთ <a style=\"color:{0} \" href=\"{1}\"a>აქ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "არ გსურს შემოწირულობა? არ ინერვიულოთ, თქვენ ყოველთვის შეგიძლიათ UniGetUI გაუზიაროთ თქვენს მეგობრებს. გაავრცელეთ ინფორმაცია UniGetUI-ის შესახებ.", "Donate": "დონაცია", "Done!": "დასრულდა!", "Download failed": "ჩამოტვირთვა ჩაიშალა", "Download installer": "ინსტალატორის ჩამოტვირთვა", "Download operations are not affected by this setting": "ეს პარამეტრი არ მოქმედებს ჩამოტვირთვის ოპერაციებზე", "Download selected installers": "შერჩეული ინსტალატორების ჩამოტვირთვა", "Download succeeded": "წარმატებით ჩამოიტვირთა", "Download updated language files from GitHub automatically": "ჩამოტვირთე განახლებული ენის ფაილები GitHub-დან ავტომატურად", "Downloading": "ჩამოტვირთვა", "Downloading backup...": "ბექაფის ჩამოტვირთვა...", "Downloading installer for {package}": "{package}-ის ინსტალატორის ჩამოტვირთვა", "Downloading package metadata...": "პაკეტის მეტამონაცემების ჩამოტვირთვა...", "Enable Scoop cleanup on launch": "Scoop-ის გასუფთავების ჩართვა გაშვებისას", "Enable WingetUI notifications": "UniGetUI-ის შეტყობინებების ჩართვა", "Enable an [experimental] improved WinGet troubleshooter": "ჩართე [ექსპერიმენტული] გაუმჯობესებული WinGet-ის შემკეთებელი", "Enable and disable package managers, change default install options, etc.": "პაკეტების მმართველების ჩართვა და გამორთვა, ნაგილისხმევი ინსტალაციის პარამეტრების შეცვლა და ა. შ.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "CPU-ს ფონური მოხმარების ოპტიმიზაციის ჩართვა (იხილეთ Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "ფონური API-ის ჩართვა (UniGetUI-ის ვიჯეტები და გაზიარება, პორტი 7058)", "Enable it to install packages from {pm}.": "პაკეტების {pm}-დან ინსტალაციის ჩართვა.", "Enable the automatic WinGet troubleshooter": "WinGet-ის ავტომატური შემკეთებლის ჩართვა", "Enable the new UniGetUI-Branded UAC Elevator": "ახალი UniGetUI ბრენდინგის მქონე UAC პროვილეგიების მომთხოვნის ჩართვა", "Enable the new process input handler (StdIn automated closer)": "ახალი პროცესის შეტანის ჰენდლერის ჩართვა (StdIn-ის ავტომატური დამხურავი)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "ჩართეთ ქვემოთ მოცემული პარამეტრები მხოლოდ იმ შემთხვევაში, თუ სრულად გესმით მათი მოქმედების პრინციპი, ასევე მათი შესაძლო შედეგები და საფრთხეები.", "Enable {pm}": "ჩართვა {pm}", "Enter proxy URL here": "შეიყვანეთ პროქსის URL აქ", "Entries that show in RED will be IMPORTED.": "წითლად მონიშნული ჩანაწერები იმპორტირებული იქნება.", "Entries that show in YELLOW will be IGNORED.": "ყვითლად მონიშნული ჩანაწერები იგნორირებული იქნება.", "Error": "შეცდომა", "Everything is up to date": "ყველაფერი განახლებულია", "Exact match": "ზუსტი თანხვედრა", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "მოხდება სამუშაო მაგიდაზე არსებული მალსახმობების სკანირება და თქვენ დაგჭირდებათ შერჩევა თუ რომელი დატოვოთ და რომელი წაშალოთ.", "Expand version": "ვერსიის გაშლა", "Experimental settings and developer options": "ექსპერიმენტული პარამეტრები და დეველოპერის ოფციები", "Export": "ექსპორტი", "Export log as a file": "ფაილის სახით ექსპორტი", "Export packages": "პაკეტების ექსპორტი", "Export selected packages to a file": "შერჩეული პაკეტების ფაილად ექსპორტი", "Export settings to a local file": "პარამეტრების ლოკალურ ფაილში ექსპორტი", "Export to a file": "ფაილში ექსპორტი", "Failed": "ჩაიშალა", "Fetching available backups...": "ხელმისაწვდომი ბექაფების ჩამოტვირთვა...", "Fetching latest announcements, please wait...": "უახლესი ანონსების ჩამოტვირთვა, გთხოვთ დაიცადოთ...", "Filters": "ფილტრები", "Finish": "დასრულება", "Follow system color scheme": "სისტემური ფერთა სქემის მიდევნება", "Follow the default options when installing, upgrading or uninstalling this package": "მიყვეით ანგულისხმევ პარამეტრებს  ამ პაკეტის ინსტალაციისას, განახლებისას ან დეინსტალაციისას", "For security reasons, changing the executable file is disabled by default": "უსაფრთხოების მიზეზებიდან გამომდინარე გამშვები ფაილის შეცვლა ნაგულისხმევად გამორთულია", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "უსაფრთხოების მიზეზებიდან გამომდინარე მორგებული ბრძანების არგუმენტები ნაგულისხმევად გამორთულია. შესაცვლელად გადადით UniGetUI-ის უსაფრთხოების პარამეტრებზე.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "უსაფრთხოების მიზეზებიდან გამომდინარე პოსტ ოპერაციული და პრე ოპერაციული სკრიპტები ნაგულისხმევად გამორთულია. შესაცვლელად გადადით UniGetUI-ის უსაფრთხოების პარამეტრებზე.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "ARM-ისთვის დაკომპილირებული winget-ის ვერსიის ფორსირებულად გამოყენება (მხოლოდ ARM64 სისტემებისთვის)", "Formerly known as WingetUI": "ადრე ცნობილი როგორც WingetUI", "Found": "ნაპოვნია", "Found packages: ": "ნაპოვნია პაკეტები:", "Found packages: {0}": "ნაპოვნია პაკეტები: {0}", "Found packages: {0}, not finished yet...": "ნაპოვნია პაკეტები: {0}, ჯერ არ დასრულებულა...", "General preferences": "ზოგადი პარამეტრები", "GitHub profile": "GitHub პროფილი", "Global": "გლობალური", "Go to UniGetUI security settings": "UniGetUI-ის უსაფრთხოების პარამეტრებზე გადასვლა", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "მშვენიერი რეპოზიტორია უცნობი მაგრამ სასარგებლო უტილიტებითა და სხვა საინტერესო პაკეტებით. <br>შეიცავს: <b>უტილიტებს, ბრძანების ზოლის პროგრამებს, ზოგად პროგრამებს (საჭიროა extras კრებული)</b>", "Great! You are on the latest version.": "მშვენიერი! თქვენ უახლეს ვერსიაზე ხართ.", "Grid": "ბადე", "Help": "დახმარება", "Help and documentation": "დახმარება და დოკუმენტაცია", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "აქ შეგიძლიათ შეცვალოთ UniGetUI-ის ქცევა შემდეგ მალსახმობებთან მიმართებაში. მალსახმობის მონიშვნით UniGetUI წაშლის მათ იმ შემთხვევაში თუ მომავალი განახლებისას შეიქმნებიან. მონიშვნის მოხსნა დატოვებს მალსახმობს ხელუხლებლად.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "გამარჯობა, ჩემი სახელია მარტი და მე ვარ UniGetUI-ის  <i>დეველოპერი</i>. UniGetUI სრულად შექმნილია ჩემი თავისუფალი დროის ხარჯზე!", "Hide details": "დეტალების დამალვა", "Homepage": "სათაო გვერდი", "Hooray! No updates were found.": "ვაშა! განახლებები არ არის ნაპოვნი.", "How should installations that require administrator privileges be treated?": "როგორ უნდა განიხილებოდეს ინსტალაციები, რომლებიც საჭიროებენ ადმინისტრატორის პრივილეგიებს?", "How to add packages to a bundle": "როგორ დავამატოთ პაკეტი კრებულში", "I understand": "გასაგებია", "Icons": "ხატულები", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "თუ გაქვთ ღრუბლოვანი ბექაფი ჩართული ის შეინახება GitHub Gist-ის სახით ამ ანგარიშზე", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "მორგებული პრეინსტალაციის და პოსტ ინსტალაციის ბრძანებების იგნორირება პაკეტების კრებულიდან იმპორტირებისას", "Ignore future updates for this package": "ამ პაკეტის მომავალი განახლებებსი იგნორირება", "Ignore packages from {pm} when showing a notification about updates": "პაკეტების განახლებების იგნორირება შეტყობინებებში {pm} რეპოზიტორიიდან", "Ignore selected packages": "მონიშნული პაკეტების იგნორირება", "Ignore special characters": "სპეციალური სიმბოლოების იგნორირება", "Ignore updates for the selected packages": "განახლებების იგნორირება შერჩეული პაკეტებისთვის", "Ignore updates for this package": "ამ პაკეტის განახლებების იგნორირება", "Ignored updates": "იგნორირებული განახლებები", "Ignored version": "იგნორირებული ვერსია", "Import": "იმპორტი", "Import packages": "პაკეტების იმპორტი", "Import packages from a file": "პაკეტების ფაილიდან იმპორტი", "Import settings from a local file": "პარამეტრების ლოკალური ფაილიდან იმპორტი", "In order to add packages to a bundle, you will need to: ": "იმისთვის, რომ დაამატოთ პაკეტები კრებულში საჭიროა:", "Initializing WingetUI...": "UniGetUI-ის ინიციალიზაცია...", "Install": "ინსტალაცია", "Install Scoop": "Scoop-ის ინსტალაცია", "Install and more": "ინსტალაცია და მეტი", "Install and update preferences": "ინსტალაციისა და განახლების პარამეტრები", "Install as administrator": "ადმინისტრატორის უფლებებით ინსტალაცია", "Install available updates automatically": "ხელმისაწვდომი განახლებების ავტომატური ინსტალაცია", "Install location can't be changed for {0} packages": "ინსტალაციის ლოკაცია ვერ შეიცვლება {0} პაკეტებისთვის", "Install location:": "ინსტალაციის ლოკაცია:", "Install options": "ინსტალაციის პარამეტრები", "Install packages from a file": "პაკეტების ფაილიდან ინსტალაცია", "Install prerelease versions of UniGetUI": "UniGetUI-ის პრერელიზების ინსტალაცია", "Install script": null, "Install selected packages": "შერჩეული პაკეტების ინსტალაცია", "Install selected packages with administrator privileges": "შერჩეული პაკეტების ადმინისტრატორის პრივილეგიებით ინსტალაცია", "Install selection": "შერჩეულების ინსტალაცია", "Install the latest prerelease version": "უახლესი პრერელიზ ვერსიის ინსტალაცია", "Install updates automatically": "განახლებების ავტომატური ინსტალაცია", "Install {0}": "{0}-ის ინსტალაცია", "Installation canceled by the user!": "ინსტალაცია მომხმარებელმა გააუქმა!", "Installation failed": "ინსტალაცია ჩაიშალა", "Installation options": "ინსტალაციის პარამეტრები", "Installation scope:": "ინსტალაციის ფარგლები:", "Installation succeeded": "ინსტალაცია წარმატებით განხორციელდა", "Installed Packages": "დაყენებული პაკეტები", "Installed Version": "დაყენებული პაკეტები", "Installed packages": "დაყენებული პაკეტები", "Installer SHA256": "ინსტალატორის SHA256", "Installer SHA512": "ინსტალატორის SHA256", "Installer Type": "ინსტალატორის ტიპი", "Installer URL": "ინსტალატორის URL", "Installer not available": "ინსტალატორი არ არის ხელმისაწვდომი", "Instance {0} responded, quitting...": "ინსტანცია {0} გვიპასუხა, გასვლა...", "Instant search": "სწრაფი ძიება", "Integrity checks can be disabled from the Experimental Settings": "ინტეგრულების შემოწმება შეგიძლიათ გამორთოთ ექსპერიმენტული პარამეტრებიდან", "Integrity checks skipped": "ინტეგრულობის შემოწმება გამოტოვებულია", "Integrity checks will not be performed during this operation": "ამ ოპერაციისას არ მოხდება ინტეგრულობის შემოწმება", "Interactive installation": "ინტერაქტიული ინსტალაცია", "Interactive operation": "ინტერაქტიული ოპერაცია", "Interactive uninstall": "ინტერაქტიული წაშლა", "Interactive update": "ინტერაქტიული განახლება", "Internet connection settings": "ინტერნეტთან კავშირის პარამეტრები", "Is this package missing the icon?": "ამ პაკეტს ხატულა არ აქვს?", "Is your language missing or incomplete?": "თქვენი ენა არ არის ან დაუსრულებელია?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "გარანტირებული არ არის, რომ მოწოდებული ანგარიშები უსაფრთხოდ შეინახება, ასე რომ თქვენ შეიძლება ასევე არ გამოიყენოთ თქვენი საბანკო ანგარიშის მონაცემები", "It is recommended to restart UniGetUI after WinGet has been repaired": "რეკომენდებულია თავიდან გაუშვათ UniGetUI მას შემდეგ რაც შეკეთდა  WinGet ", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "ამ სიტუაციაში დიდწილად რეკომენდებულია UniGetUI-ის ხელახლა ინსტალაცია", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "როგორც ჩანს WinGet  არ მუშაობს გამართულად. გსურთ სცადოთ WinGet-ის შეკეთება?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "როგორც ჩანს თქვენ გაუშვით UniGetUI ადმინისტრატორის უფლებებით, რაც არ არის რეკომენდებული. თქვენ შეგიძლიათ განაგრძოთ პროგრამის გამოყენება, მაგრამ ჩვენი დაჟინებული რჩევა იქნება არ გაუშვათ UniGetUI ადმინისტრატორის პრივილეგიებით. დაწკაპეთ \"{showDetails}\" რომ ნახოთ რატომ", "Language": "ენა", "Language, theme and other miscellaneous preferences": "ენა, თემა და სხვა დამატებითი პარამეტრები", "Last updated:": "ბოლო განახლება:", "Latest": "უახლესი", "Latest Version": "უახლესი ვერსია", "Latest Version:": "უახლესი ვერსია:", "Latest details...": "უახლესი დეტალები...", "Launching subprocess...": "ქვეპროცესების გაშვება...", "Leave empty for default": "დატოვეთ ცარიელი ნაგულისხმევად", "License": "ლიცენზია", "Licenses": "ლიცენზიები", "Light": "ნათლი", "List": "სია", "Live command-line output": "პირდაპირი ბრძანების-ზოლის გამოსავალი", "Live output": "პირდაპირი გამოსავალი", "Loading UI components...": "სამომხმარებლო ინტერფეისის კომპონენტების ჩატვირთვა...", "Loading WingetUI...": "იტვირთება UniGetUI...", "Loading packages": "პაკეტების ჩატვირთვა", "Loading packages, please wait...": "პაკეტების ჩატვირთვა, გთხოვთ მოიცადოთ...", "Loading...": "ჩატვირთვა...", "Local": "ლოკალური", "Local PC": "ლოკალური კომპიუტერი", "Local backup advanced options": "ლოკალური ბექაფის დამატებითი პარამეტრები", "Local machine": "ლოკალური მანქანა", "Local package backup": "პაკეტების ლოკალური ბექაფი", "Locating {pm}...": "იძებნება {pm}...", "Log in": "შესვლა", "Log in failed: ": "შესვლა ჩაიშალა:", "Log in to enable cloud backup": "ღრუბლოვანი ბექაფისთვის საჭიროა შესვლა", "Log in with GitHub": "GitHub-ით შესვლა", "Log in with GitHub to enable cloud package backup.": "შედით GitHub-ით, რომ ჩართოთ პაკეტების ღრუბლოვანი ბექაფი", "Log level:": "ჟურნალის დონე:", "Log out": "გამოსვლა", "Log out failed: ": "გამოსვლა ჩაიშალა:", "Log out from GitHub": "GitHub-დან გამოსვლა", "Looking for packages...": "იძებნება პაკეტები...", "Machine | Global": "მანქანა | გლობალური", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "არასწორად შედგენილ ბრძანების არგუმენტებს შეუძლია პაკეტების დაზიანება ან თუნდაც ბოროტმოქმედს მისცეს პრივილეგირებული შესრულების უფლება. ამიტომაც მორგებული ბრძანების არგუმენტების იმპორტირება ნაგულისხმევად გამორთულია", "Manage": "მართვა", "Manage UniGetUI settings": "UniGetUI-ის პარამეტრების მართვა", "Manage WingetUI autostart behaviour from the Settings app": "მართეთ UniGetUI-ის ავტომატურად გაშვება პარამეტრების აპლიკაციიდან", "Manage ignored packages": "იგნორირებული პაკეტების მართვა", "Manage ignored updates": "იგნორირებული განახლებების მართვა", "Manage shortcuts": "მალსახმობების მართვა", "Manage telemetry settings": "ტელემეტრიის პარამეტრების მართვა", "Manage {0} sources": "{0} წყაროების მართვა", "Manifest": "მანიფესტი", "Manifests": "მანიფესტები", "Manual scan": "ხელით სკანირება", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoft-ის ოფიციალური პაკეტების მენეჯერი. სავსეა ცნობილი და გადამოწმებული პაკეტებით <br> შეიცავს: <b>ზოგად პროგრამებს, აპებს Microsoft Store-დან</b>", "Missing dependency": "აკლია დამოკიდებულება", "More": "მეტი", "More details": "მეტი დეტალი", "More details about the shared data and how it will be processed": "დამატებითი ინფორმაცია გაზიარებული ინფორმაციიდან და როგორ იქნება გამოიყენებული ის", "More info": "მეტი ინფო", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "შენიშვნა: ეს პრობლემების გადამჭრელი შეგიძლიათ გამორთოთ UniGetUI-ის პარამეტრებიდან, WinGet სექციაში.", "Name": "სახელი", "New": "ახალი", "New Version": "ახალი ვერსია", "New bundle": "ახალი კრებული", "New version": "ახალი ვერსია", "Nice! Backups will be uploaded to a private gist on your account": "მშვენიერი! ბექაფები აიტვირთება პრივატულ gist-ში თქვენს ანგარიშზე", "No": "არა", "No applicable installer was found for the package {0}": "არ არის შესაბამისი ინსტალატორი ნაპოვნი {0} პაკეტისთვის", "No dependencies specified": "არ არის მითითებული დამოკიდებულებები", "No new shortcuts were found during the scan.": "სკანირებისას არ იქნა ნაპოვნი ახალი მალსახმობები.", "No packages found": "პაკეტები არ არის ნაპოვნი", "No packages found matching the input criteria": "მოთხოვნილი კრიტერიუმით არ არის ნაპოვნი პაკეტები", "No packages have been added yet": "პაკეტები ჯერ არ დამატებულა", "No packages selected": "პაკეტები არ არის შერჩეული", "No packages were found": "პაკეტები არ არის ნაპოვნი", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "არანაირი პერსონალური მონაცემი არ გროვდება და არც გადაიცემა, დაგროვილი ინფორმაცია ანონიმურია ისე, რომ არ უთითებს თქვენზე.", "No results were found matching the input criteria": "შევანილი კრიტერიუმებით არ იქნა ნაპოვნი არაფერი", "No sources found": "წყაროები არ არის ნაპოვნი", "No sources were found": "წყაროები არ იქნა ნაპოვნი", "No updates are available": "განახლებები არ არის ხელმისაწვდომი", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS პაკეტების მართველი. სავსეა ბიბლიოთეკებითა და უტილიტებით javascript-ის სამყაროდან <br>შეიჩავს: <b>Node javascript ბიბლიოთეკებს და სხვა შესაბამის უტილიტებს</b>", "Not available": "მიუწვდომელია", "Not finding the file you are looking for? Make sure it has been added to path.": "ვერ პოულობ ფაილს რომელსაც ეძებ? დარწმუნდი, რომ ის დამატებულია path-ში.", "Not found": "არ არის ნაპოვნი", "Not right now": "ახლა არა", "Notes:": "ჩანაწერები:", "Notification preferences": "შეტყობინებების პარამეტრები", "Notification tray options": "შეტყობინებების პანელის პარამეტრები", "Notification types": "შეტყობინებების ტიპები", "NuPkg (zipped manifest)": "NuPkg (დაზიპული მანიფესტი)", "OK": "OK", "Ok": "Ok", "Open": "გახსნა", "Open GitHub": "GitHub-ის გახსნა", "Open UniGetUI": "UniGetUI-ის გახსნა", "Open UniGetUI security settings": "UniGetUI-ის უსაფრთხოების პარამეტრების გახსნა", "Open WingetUI": "UniGetUI-ის გახსნა", "Open backup location": "ბექაფის ლოკაციის გახსნა", "Open existing bundle": "არსებული კრებულის გახსნა", "Open install location": "ინსტალაციის ლოკაციის გახსნა", "Open the welcome wizard": "მისასალმებელი ოსტატის გახსნა", "Operation canceled by user": "ოპერაცია მომხმარებელმა გააუქმა", "Operation cancelled": "ოპერაცია გაუქმდა", "Operation history": "ოპერაციის ისტორია", "Operation in progress": "მიმდინარეობს ოპერაცია", "Operation on queue (position {0})...": "ოპერაცია რიგშია (პოზიცია {0})...", "Operation profile:": "ოპერაციის პროფილი:", "Options saved": "პარამეტრები შენახულია", "Order by:": "დალაგება:", "Other": "სხვა", "Other settings": "სხვა პარამეტრები", "Package": "პაკეტი", "Package Bundles": "პაკეტების კრებულები", "Package ID": "პაკეტის ID", "Package Manager": "პაკეტების მენეჯერი", "Package Manager logs": "პაკეტების მენეჯერის ჟურნალები", "Package Managers": "პაკეტების მენეჯერები", "Package Name": "პაკეტის სახელი", "Package backup": "პაკეტის ბექაფი", "Package backup settings": "პაკეტის ბექაფის პარამეტრები", "Package bundle": "პაკეტის კრებული", "Package details": "პაკეტის დეტალები", "Package lists": "პაკეტების სიები", "Package management made easy": "გამარტივებული პაკეტების მართვა", "Package manager": "პაკეტების მმართველი", "Package manager preferences": "პაკეტების მენეჯერის პარამეტრები", "Package managers": "პაკეტების მენეჯერები", "Package not found": "პაკეტი არ არის ნაპოვნი", "Package operation preferences": "პაკეტის ოპერაციის პარამეტრები", "Package update preferences": "პაკეტის განახლების პარამეტრები", "Package {name} from {manager}": "{name} პაკეტი {manager}-დან ", "Package's default": "პაკეტის ნაგულისხმევი", "Packages": "პაკეტები", "Packages found: {0}": "ნაპოვნი პაკეტები: {0}", "Partially": "ნაწილობრივ", "Password": "პაროლი", "Paste a valid URL to the database": "ჩასვით მონაცემთა ბაზის ვალიდური URL ", "Pause updates for": "განახლებების დაპაუზება", "Perform a backup now": "ბექაფის შექმნა ახლა", "Perform a cloud backup now": "ღრუბლოვანი ბექაფის ახლა გაშვება", "Perform a local backup now": "ლოკალური ბექაფის ახლა გაშვება", "Perform integrity checks at startup": "ინტეგრულობის შემოწმება გაშვებისას", "Performing backup, please wait...": "ბექაფის შექმნა, გთხოვთ მოიცადოთ...", "Periodically perform a backup of the installed packages": "პერიოდულად შექმენი დაყენებული პაკეტების ბექაფი", "Periodically perform a cloud backup of the installed packages": "პერიოდულად შექმენი დაყენებული პაკეტების ღრუბლოვანი ბექაფი", "Periodically perform a local backup of the installed packages": "დაყენებული პაკეტების ბექაფის პერიოდულად შესრულება", "Please check the installation options for this package and try again": "შეამოწმეთ ინსტალაციის პარამეტრები ამ პაკეტისთვის და ხელახლა ცადეთ", "Please click on \"Continue\" to continue": "გთხოვთ დაწკაპოთ \"გაგრძელება\" რომ გაგრძელდეს", "Please enter at least 3 characters": "შეიყვანეთ 3 სიმბოლო მაინც", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "გაითვალისწინეთ, რომ ზოგიერთი პაკეტი შესაძლოა არ იყოს ინსტალირებადი, იმის მიხედვით თუ რომელი პაკეტების მენეჯერებია ჩართული მანქანაზე.", "Please note that not all package managers may fully support this feature": "გაითვალისწინეთ, რომ ყველა პაკეტების მენეჯერს შესაძლოა არ ქონდეთ ამ ფუნქციის სრული მხარდაჭერა", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "გაითვალისწინეთ, რომ პაკეტები ზოგიერთი წყაროდან შესაძლოა არ იყოს ექსპორტირებადი. ისინი ნაცრისფერადაა მონიშნული და არ დაექსპორტდება.", "Please run UniGetUI as a regular user and try again.": "გთხოვთ გაუშვათ UniGetUI როგორც ჩვეულებრივმა მომხმარებელმა და ხელახლა ცადეთ.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "გთხოვთ ნახლოთ ბრძანების ზოლის გამოსავალი ან ოპერაციების ისტორია პრობლემის შესახებ მეტი ინფორმაციის გასაგებად.", "Please select how you want to configure WingetUI": "გთხოვთ შეარჩიეთ თუ როგორ გსურთ UniGetUI-ის კონფიგურაცია", "Please try again later": "გთხოვთ სცადოთ მოგვიანებით", "Please type at least two characters": "აკრიფეთ ორი სიმბოლო მაინც", "Please wait": "გთხოვთ მოიცადოთ", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "მოიცადეთ სანამ {0} დაყენდება. შესაძლოა გამოჩნდეს შავი (ან ლურჯი) ფანჯარა. დაელოდეთ სანამ არ დაიხურება.", "Please wait...": "გთხოვთ მოიცადოთ...", "Portable": "პორტატული", "Portable mode": "პორტატული რეჟიმი", "Post-install command:": "პოსტ ინსტალაციის ბრძანება:", "Post-uninstall command:": "პოსტ დეინსტალაციის ბრძანება:", "Post-update command:": "პოსტ განახლების ბრძანება:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell-ის პაკეტების მმართველი. იპოვეთ ბიბლიოთეკები და სკრიპტები რომ გააფართოოთ PowerShell-ის შესაძლებლობები<br>შეიცავს: <b>მოდულებს, სკრიპტებს, ქომადლეტებს</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "პრე და პოსტ ინსტალაციის ბრძანებებს შეულძია ზიანის მიყენება, თუ არასწორად არის შედგენილი. ეს შესაძლოა ძალიან საშიში იყოს კრებულიდან მათი იმპორტირება თუ თქვენ არ ენდობით პაკეტების კრებულის მომწოდებელს.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "პრე და პოსტ ინსტალაციის ბრძანებები გაეშვება პაკეტის ინსტალაციამდე, შემდეგ, განახლებისას ან დეინსტალაციისას. გაითვალისწინეთ, რომ ამას შეუძლია ზიანის მოტანა თუ არ გამოვიყენებთ სიფთხილის დაცვით.", "Pre-install command:": "პრე ინსტალაციის ბრძანება:", "Pre-uninstall command:": "პრე დეინსტალაციის ბრძანება:", "Pre-update command:": "პრე განახლების ბრძანება:", "PreRelease": "პრერელიზი", "Preparing packages, please wait...": "პაკეტების მომზადება, გთხოვთ მოიცადოთ...", "Proceed at your own risk.": "გააგრძელეთ საკუთარი რისკის ფასად.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "ნებისმიერი ელევაციის დაბლოკვა UniGetUI Elevator ან GSudo-ს საშუალებით", "Proxy URL": "პროქსის URL", "Proxy compatibility table": "პროქსის თავსებადობის ცხრილი", "Proxy settings": "პროქსის პარამეტრები", "Proxy settings, etc.": "პროქსის პარამეტრები, სხვ.", "Publication date:": "გამოქვეყნების თარიღი:", "Publisher": "გამომცემი", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python-ის ბიბლიოთეკების მმართველი. სავსეა პითონის ბიბლიოთეკებით და სხვა მასთან დაკავშირებული უტილიტებით<br> შეიცავს: <b>Python-ის ბიბლიოთეკებსა და შესაბამის უტილიტებს</b>", "Quit": "გასვლა", "Quit WingetUI": "UniGetUI-დან გასვლა", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "UAC დიალოგების შემცირება, ინსტალაციების ელევაცია ნაგულისხმევად, ზოგიერთი საშიში ფუნქციის ჩართვა და ა.შ.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "ჩახედეთ UniGetUI-ის ჟურნალში, რომ გაიგოთ მეტი ინფორმაცია მონაწილე ფაილ(ებ)ის შესახებ.", "Reinstall": "ხელახალი ინსტალაცია", "Reinstall package": "პაკეტის ხელახალი ინსტალაცია", "Related settings": "დაკავშირებული პარამეტრები", "Release notes": "რელიზის ჩანაწერები", "Release notes URL": "რელიზის ჩანაწერების URL", "Release notes URL:": "რელიზის ჩანაწერების URL", "Release notes:": "რელიზის ჩანაწერები", "Reload": "ხელახლა ჩატვირთვა", "Reload log": "ჟურნალის ხელახლა ჩატვირთვა", "Removal failed": "წაშლა ჩაიშალა", "Removal succeeded": "წაშლა წარმატებით დასრულდა", "Remove from list": "სიიდან წაშლა", "Remove permanent data": "პერმანენტული მონაცემების წაშლა", "Remove selection from bundle": "მონიშნულის კრებულიდან წაშლა", "Remove successful installs/uninstalls/updates from the installation list": "წრმატებული ინსტალაციების/წაშლების/განახლებების წაშლა სიიდან", "Removing source {source}": "{source} წყაროს წაშლა", "Removing source {source} from {manager}": "{source} წყაროს წაშლა {manager}-დან", "Repair UniGetUI": "UniGetUI-ის შეკეთება", "Repair WinGet": "WinGet-ის შეკეთება", "Report an issue or submit a feature request": "შეგვატყობინეთ პრობლემის შესახებ ან შემოგვთავაზეთ იდეა", "Repository": "რეპოზიტორია", "Reset": "რესეტი", "Reset Scoop's global app cache": "Scoop-ის გლობალური ქეშის დარესეტება", "Reset UniGetUI": "UniGetUI-ის დარესეტება", "Reset WinGet": "WinGet-ის დარესეტება", "Reset Winget sources (might help if no packages are listed)": "Winget-ის წყაროების დარესეტება (შესაძლოა გამოგადგეთ, როცა პაკეტები არ ჩანს სიაში)", "Reset WingetUI": "UniGetUI-ის დარესეტება", "Reset WingetUI and its preferences": "UniGetUI-ისა და მისი პარამეტრების რესეტი", "Reset WingetUI icon and screenshot cache": "UniGetUI-ის ხატულებისა და სქრინშოტების ქეშის რესეტი", "Reset list": "სიის რესეტი", "Resetting Winget sources - WingetUI": "რესეტდება WinGet-ის წყაროები - UniGetUI", "Restart": "გადატვირთვა", "Restart UniGetUI": "UniGetUI-ის გადატვირთვა", "Restart WingetUI": "UniGetUI-ის გადატვირთვა", "Restart WingetUI to fully apply changes": "ცვლილებების სრულად ასახვისთვის UniGetUI-ის გადატვირთვა", "Restart later": "მოგვიანებით გადატვირთვა", "Restart now": "ახლა გადატვირთვა", "Restart required": "საჭიროა გადატვირთვა", "Restart your PC to finish installation": "კომპიუტერის გადატვირთვა ინსტალაციის დასრულებისთვის", "Restart your computer to finish the installation": "გადატვირთეთ თქვენი კომპიუტერი ინსტალაციის დასრულებისთვის", "Restore a backup from the cloud": "ბექაფის ღრუბლიდან აღდგენა", "Restrictions on package managers": "პაკეტების მმართველების შეზღუდვები", "Restrictions on package operations": "პაკეტებზე ოპერაციების შეზღუდვები", "Restrictions when importing package bundles": "შეზღუდვები პაკეტების კრებული იმპორტირებისას", "Retry": "ხელახლა ცდა", "Retry as administrator": "ხელახლა ცდა როგორც ადმინისტრატორი", "Retry failed operations": "ჩაშლილი ოპერაციების ხელახლა ცდა", "Retry interactively": "ხელახლა ცდა ინტერაქტიულად", "Retry skipping integrity checks": "იტეგრულობის შემოწმების გამოტოვების ხელახლა ცდა", "Retrying, please wait...": "ხელახლა ცდა, გთხოვთ მოიცადოთ...", "Return to top": "თავში დაბრუნება", "Run": "გაშვება", "Run as admin": "ადმინისტრატორით გაშვება", "Run cleanup and clear cache": "გაწმენდისა და ქეშის გასუფთავების გაშვება", "Run last": "ბოლოს გაშვება", "Run next": "შემდეგის გაშვება", "Run now": "ახლა გაშვება", "Running the installer...": "ეშვება ინსტალატორი...", "Running the uninstaller...": "ეშვება დეინსტალატორი...", "Running the updater...": "ეშევება განმაახელებელი...", "Save": "შენახვა", "Save File": "ფაილის შენახვა", "Save and close": "შენახვა და დახურვა", "Save as": "შენახვა როგორც", "Save bundle as": "კრებულის შენახვა როგორც", "Save now": "ახლა შენახვა", "Saving packages, please wait...": "პაკეტების შენახვა, გთხოვთ მოიცადოთ...", "Scoop Installer - WingetUI": "Scoop ინსტალატორი - UniGetUI", "Scoop Uninstaller - WingetUI": "Scoop დეინსტალატორი - UniGetUI", "Scoop package": "Scoop-ის პაკეტი", "Search": "ძიება", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "მოძებნე სამაგიდო პროგრამები, შემატყობინე განახლებების შესახებ და არ ქნა რთული რამეები. მე არ მინდა UniGetUI-ის გართულება, მე მხოლოდ მარტივი <b>პროგრამული უზრუნველყოფის მაღაზია</b> მჭირდება", "Search for packages": "პაკეტების ძიება", "Search for packages to start": "დაწყებისთვის მოძებნეთ პაკეტები", "Search mode": "ძიების რეჟიმი", "Search on available updates": "ხელმისაწვდომ განახლებებში ძიება", "Search on your software": "თქვნს პროგრამებში ძიება", "Searching for installed packages...": "იძებნება დაყენებული პაკეტები...", "Searching for packages...": "პაკეტების ძიება...", "Searching for updates...": "განახლებების ძიება...", "Select": "შერჩევა", "Select \"{item}\" to add your custom bucket": "შეარჩიეთ \"{item}\" რომ დაამატოთ მორგებულ კალათაში", "Select a folder": "ფოლდერის შერჩევა", "Select all": "ყველას მონიშვნა", "Select all packages": "ყველა პაკეტის მონიშვნა", "Select backup": "შეარჩიეთ ბექაფი", "Select only <b>if you know what you are doing</b>.": "შეარჩიე მხოლოდ <b>თუ იცი რას აკეთებ</b>.", "Select package file": "შეარჩიეთ პაკეტის ფაილი", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "შეარჩიეთ ბექაფი რომლის გახსნაც გსურთ. მოგივანებით თქვენ შეძლებთ განიხილოთ თუ რომელი პაკეტების/პროგრამების აღგენა გსურთ.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "შეარჩიეთ პროცესები, რომლებიც უნდა დაიხუროს მანამ სანამ ეს პაკეტი დაინსტალირდება, განახლდება ან დეინსტალირდება.", "Select the source you want to add:": "შეარჩიეთ წყაროები, რომლების დამატებაც გინდათ:", "Select upgradable packages by default": "განახლებადი პაკეტების ნაგულისხმევად შერჩევა", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "შეარჩიეთ თუ რომელი <b>პაკეტების მმართველი</b> გამოვიყენოთ ({0}), პაკეტების ინსტალაციის კონფიგურაცია, ადინისტრატორის უფლებების მართვა და სხვა.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "გაიგზავნა ხელის ჩამორთმევა. ველოდები ინსტანციის მსმენელის პასუხს... ({0}%)", "Set a custom backup file name": "მიუთითეთ ბექაფის ფაილის მორგებული სახელი", "Set custom backup file name": "დააყენეთ მორგებული სარეზერვო ფაილის სახელი", "Settings": "პარამეტრები", "Share": "გაზიარება", "Share WingetUI": "გააზიარე UniGetUI", "Share anonymous usage data": "ანონიმური მოხმარების მონაცემების გაზიარება", "Share this package": "ამ პაკეტის გაზიარება", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "თუ უსაფრთხოების პარამეტრებს შეცვლით, ცვლილებების ძალაში შესასვლელად კრებულის ხელახლა გახსნა დაგჭირდებათ.", "Show UniGetUI on the system tray": "UniGetUI-ის სისტემურ პანელში ჩვენება", "Show UniGetUI's version and build number on the titlebar.": "UniGetUI-ს ვერსიის ჩვენება სათაურის ზოლში", "Show WingetUI": "UniGetUI-ის ჩვენება", "Show a notification when an installation fails": "შეტყობინების ჩვენება ინსტალაციის ჩაშლისას", "Show a notification when an installation finishes successfully": "შეტყობინების ჩვენება ინსტალაციის წარმატებულად დასრულებისას", "Show a notification when an operation fails": "შეტყობინების ჩვენება ოპერაციის ჩაშლისას", "Show a notification when an operation finishes successfully": "შეტყობინების ჩვენება ოპერაციის წარმატებულად დასრულებისას", "Show a notification when there are available updates": "შეტყობინების ჩვენება როცა ხელმისაწვდომია განახლებები", "Show a silent notification when an operation is running": "ჩუმი შეტყობინების ჩვენება როცა მიმდინარეობს ოპერაცია", "Show details": "დეტალების ჩვენება", "Show in explorer": "ექსპლორერში ჩვენება", "Show info about the package on the Updates tab": "პაკეტის ინფოს ნახვა განახლებების ტაბში", "Show missing translation strings": "ნაკლული სათარგმნი სტრიქონების ჩვენება", "Show notifications on different events": "შეტყობინებების ჩვენება სხვა და სხვა მოვლენებისთვის", "Show package details": "პაკეტის დეტალების ჩვენება", "Show package icons on package lists": "პაკეტის ხატულების ჩვენება პაკეტების სიებში", "Show similar packages": "მსგავსი პაკეტების ჩვენება", "Show the live output": "პირდაპირი გამოსავლის ჩვენება", "Size": "ზომა", "Skip": "გამოტოვება", "Skip hash check": "ჰეშის შემოწმების გამოტოვება", "Skip hash checks": "ჰეშის შემოწმების გამოტოვება", "Skip integrity checks": "ინტეგრულობის შემოწმების გამოტოვება", "Skip minor updates for this package": "მინორული განახლებების გამოტოვება ამ პაკეტისთვის", "Skip the hash check when installing the selected packages": "ჰეშის შემოწმების გამოტოვება შერჩეული პაკეტების ინსტალაციისას", "Skip the hash check when updating the selected packages": "ჰეშის შემოწმების გამოტოვება შერჩეული პაკეტების ინსტალაციისას", "Skip this version": "ვერსიის გამოტოვება", "Software Updates": "პროგრამების განახლებები", "Something went wrong": "რაღაც ჩაიშალა", "Something went wrong while launching the updater.": "რაღაც ჩაიშალა განმაახლებლის გაშვებისას", "Source": "წყარო", "Source URL:": "წყაროს URL:", "Source added successfully": "წყარო წარმატებით დაემატა", "Source addition failed": "წყაროს დამატება ჩაიშალა", "Source name:": "წყაროს სახელი:", "Source removal failed": "წყაროს წაშლა ჩაიშალა", "Source removed successfully": "წყარო წარმატებით წაიშალა", "Source:": "წყარო:", "Sources": "წყაროები", "Start": "დაწყება", "Starting daemons...": "დემონების გაშვება...", "Starting operation...": "ოპერაციების გაშვება...", "Startup options": "ჩართვის ოფციები", "Status": "სტატუსი", "Stuck here? Skip initialization": "გაიჭედეთ? გამოტოვეთ ინიციალიზაცია", "Success!": null, "Suport the developer": "დეველოპერის მხარდაჭერა", "Support me": "მხარი დამიჭირე მე", "Support the developer": "დეველოპერის მხარდაჭერა", "Systems are now ready to go!": "სისტემები მზად არის გაშვებისთვის!", "Telemetry": "ტელემეტრია", "Text": "ტექსტი", "Text file": "ტექსტური ფალი", "Thank you ❤": "გმადლობთ ❤", "Thank you 😉": "გმადლობთ 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust-ის პაკეტების მმართველი.<br> შეიცავს: <b>Rust-ის ბიბლიოთეკებსა და Rust-ზე დაწერილ პროგრამებს</b>", "The backup will NOT include any binary file nor any program's saved data.": "ბექაფში არ იქნება არანაირი ბინარული ფაილი და არც პროგრამის მიერ შენახული მონაცემები.", "The backup will be performed after login.": "ბექაფი სისტემაში შესვლის შემდეგ შესრულდება", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "ბექაფი შეიცავდეს იქნება დაყენებული პაკეტების სრულ სიას და მათი ინსტალაციის პარამეტრებს. იგნორირებული განახლებები და გამოტივებული ვერსიებიც კი შენახული იქნება.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "კრებული, რომლის ჩატვირთვაც გსურთ არ არის ვალიდური. შეამოწმეთ ფაილი და ხელახლა ცადეთ.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "ინსტალატორის საკონტროლო ჯამი არ ეთანხმება მოსალოდნელ მნიშვნელობას და ინსტალატორის აუთენტურობის შემოწმება შეუძლებელია. თუ თქვენ ენდობით გამომცემელს, {0} პაკეტი ხელახლა ჰეშის შემოწმების გარეშე.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "კლასიკური პაკეტების მენეჯერი Windows-ისთვის. თქვენ იქ ყველაფერს იპოვით. <br>შეიცავს: <b>ზოგად პროგრამებს</b>", "The cloud backup completed successfully.": "ღღუბლოვანი ბექაფი წარმატებით დასრულდა.", "The cloud backup has been loaded successfully.": "ღრუბლოვანი ბექაფი წარმატებით ჩაიტვირთა.", "The current bundle has no packages. Add some packages to get started": "მიმდინარე კრებულში არ არის პაკეტები. დაამატე რამდენიმე პაკეტი რომ დავიწყოთ", "The executable file for {0} was not found": "ვერ მოიძებნა {0}-ის გამშვები ფაილი", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "შემდეგი პარამეტრები იქნება გამოყენებული ნაგულისხმევად ყოველ ჯერზე  {0} პაკეტის ინსტალაციისას, განახლებისას ან დეინსტალაციისას.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "შემდეგი პაკეტები დაექსპორტდება JSON ფაილში. არანაირი მონაცემი მომხმარებლის შესახებ ან ბინარული ფაილები არ იქნება შენახული.", "The following packages are going to be installed on your system.": "შემდეგი პაკეტები დაყენდება თქვენს სისტემაში.", "The following settings may pose a security risk, hence they are disabled by default.": "შემდეგი პარამეტრები შეიცავს შესაძლო უსაფრთხოების რისკებს, ამიტომაც ისინი გამორთულია ნაგულისხმევად.", "The following settings will be applied each time this package is installed, updated or removed.": "შემდეგი პარამეტრები იქნება გამოყენები ყოველ ჯერზე ამ პაკეტის დაყენებისას, განახლებისას ან წაშლისას.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "შემდეგი პარამეტრები იქნება გამოყენები ყოველ ჯერზე ამ პაკეტის დაყენებისას, განახლებისას ან წაშლისას. ისინი შეინახება ავტომატურად.", "The icons and screenshots are maintained by users like you!": "ხატულები და სქრინშოტები მოწოდებულია თქვენნაირი მომხმარებლების მიერ!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "ვერ გადამოწმდა ინსტალატორის აუთენტურობა", "The installer has an invalid checksum": "ინსტალატორს არავალიდური საკონტროლო ჯამი აქვს", "The installer hash does not match the expected value.": "ინსტალატორის ჰეში არ ემთხვევა მოსალოდნელ მნიშვნელობას.", "The local icon cache currently takes {0} MB": "ლოკალური ხატულების ქეში ახლა იკავებს {0} მბ-ს", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "ამ პროექტის მთავარი მიზანია შეიქმნას ინტუიციური UI ყველაზე გავრცელებული CLI პაკეტების მენეჯერებისთვის Windows-ზე, როგორებიცაა Winget და Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "პაკეტი\"{0}\" არ იქნა ნაპოვნი \"{1}\" პაკეტების მენეჯერში", "The package bundle could not be created due to an error.": "პაკეტების კრებული ვერ შეიქმნა შეცდომის გამო.", "The package bundle is not valid": "პაკეტების კრებული არ არის ვალიდური", "The package manager \"{0}\" is disabled": "პაკეტების მენეჯერი \"{0}\" გამორთულია", "The package manager \"{0}\" was not found": "პაკეტების მენეჯერი \"{0}\" ვერ იქნა ნაპოვნი", "The package {0} from {1} was not found.": "პაკეტი {0}, {1}-დან ვერ იქნა ნაპოვნი.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "აქ ჩამოთვლილი პაკეტები არ იქნება გათვალისწინებული განახლებების შემოწმებისას. ორჯერ დაწკაპეთ მათზე ან ღილაკზე მარჯვნივ რომ შეტყვიტოთ მათი განახლებების იგნორირება.", "The selected packages have been blacklisted": "შერჩეული პაკეტები შავ სიაში იქნა შეტანილი", "The settings will list, in their descriptions, the potential security issues they may have.": "აღწერებში იქნება ამ პარამეტრების სია და მათი პოტენციური უსაფრთხოების პრობლემები.", "The size of the backup is estimated to be less than 1MB.": "ბექაფის მოსალოდნელი ზომა 1 მბ-ზე ნაკლებია", "The source {source} was added to {manager} successfully": "წყარო {source} წარმატებით დაემატა {manager}-ში", "The source {source} was removed from {manager} successfully": "წყარო {source} წარმატებით ამოიშალა {manager}-დან", "The system tray icon must be enabled in order for notifications to work": "სისტემური პანელის ხატულა უნდა იყოს ჩართული შეტყობინებების მუშაობისთვის", "The update process has been aborted.": "განახლების პროცესი შეწყვეტილ იქნა", "The update process will start after closing UniGetUI": "განახლების პროცესი დაიწყება UniGetUI-ის დახურვის შემდეგ", "The update will be installed upon closing WingetUI": "განხლება დაყენებული იქნება UniGetUI-ის დახურვისთანავე", "The update will not continue.": "განახლება არ გაგრძელდება.", "The user has canceled {0}, that was a requirement for {1} to be run": "მომხმარებელმა გააუქმა {0}, ეს იყო მოთხოვნა {1}-ის გაშვებისთვის", "There are no new UniGetUI versions to be installed": "არ არის UniGetUI-ის ახალი ვერსიები", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "მიმდინარეობს ოერაციები. UniGetUI-ის დახურვა მათ ჩაშლას გამოიწვევს. გსურთ გარძელება?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "YouTube-ში არის შესანიშნავი ვიდეოები სადაც ნაჩვენებია UniGetUI-ის შესაძლებლობები. თქვენ ნახოთ ისწავლოთ სასარგებლო ხრიკები და რჩევები!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "არის ორი მიზეზი თუ რატომ არ უნდა გაუშვათ UniGetUI როგორც ადმინისტრატორმა:\nპირველი არის ის, რომ Scoop პაკეტების მმართველს შეექმნება პრობლემები ზოგიერთ ბრძანებასთან ადმინისტრატორის უფლებებით გაშვებისას.\nმეორე არის ის, რომ  UniGetUI-ის გაშვება ადმინისტრატორით ნიშნავს, რომ ნებისმიერი გადმოწერილი პაკეტი ასევე გაეშვება ადმინისტრატორის უფლებებით.", "There is an error with the configuration of the package manager \"{0}\"": "შეცდომაა \"{0}\" პაკეტების მმართველის კონფიგურაციაში", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "მიმდინარეობს ინსტალაციის პროცესი. თუ თქვენ დახურავთ UniGetUI-ის, ინსტალაცია შესაძლოა ჩაიშალოს და მიიღოთ არასასურველი შედეგები. ნამდვილად გსურთ დახუროთ UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "ეს არის პროგრამები, რომლითაც ხორციელდება პაკეტების ინსტალაცია, განახლება და წაშლა.", "Third-party licenses": "მესამე-მხარის ლიცენზიები", "This could represent a <b>security risk</b>.": "ეს შესაძლოა <b>უსაფრთხოების რისკი</b> აღმოჩნდეს.", "This is not recommended.": "ეს არ არის რეკომენდებული.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "ეს შესაძლოა იმიტომ ხდება, რომ პაკეთი რომელიც თქვენ გამოგზავნეთ წაშლილია ან გამოქვეყნებულია პაკეტების მმართველში, რომელიც არ გაქვს გააქტიურებული. მიღებული აიდი არის {0}", "This is the <b>default choice</b>.": "ეს არის <b>ნაგულისხმევი არჩევანი</b>.", "This may help if WinGet packages are not shown": "ამან შესაძლოა გამოასწოროს თუ WinGet-ის პაკეტები არ ჩანს", "This may help if no packages are listed": "ეს შეიძლება დაგეხმაროთ, თუ პაკეტები არ არის ჩამოთვლილი", "This may take a minute or two": "ამას ერთი-ორი წუთი დაჭირდება", "This operation is running interactively.": "ეს ოპერაცია ინტერაქტიულად ეშვება.", "This operation is running with administrator privileges.": "ეს ოპერაცია ადმინისტრატორის პრივილეგიებით ეშვება.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "ეს პარამეტრი პრობლემებს გამოიწვევს. ნებისმიერი ოპერაცია, რომელიც ვერ შეძლებს თავისით ელევაციას ჩაიშლება. ინსტალაცია/განახლება/დეინსტალაცია ადმინისტრატორის უფლებებით არ იმუშავებს", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "პაკეტების ეს კრებული შეიცავს ზოგიერთ პარამეტრს, რომლებიც პოტენციურად საშიშია და შესაძლოა ინგორირებულ იქნას ნაგულისხმევად.", "This package can be updated": "შესაძლებელია ამ პაკეტის განახლება", "This package can be updated to version {0}": "ამ პაკეტის განახლება შესაძლებელია {0} ვერსიამდე", "This package can be upgraded to version {0}": "ამ პაკეტის განახლება შესაძლებელია {0} ვერსიამდე", "This package cannot be installed from an elevated context.": "ეს პაკეტი ვერ დაყენდება ადმინისტრატორის პრივილეგიებით.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "ამ პაკეტს არ აქვს სქრინშოტები ან აკლია ხატულა? შეიტანეთ წვლილი UniGetUI-ში დაკარგული ხატულების და სქრინშოტების დამატებით ჩვენს ღია, საჯარო მონაცემთა ბაზაში.", "This package is already installed": "ეს პაკეტი უკვე დაყენებულია", "This package is being processed": "მიმდინარეობს პაკეტის დამუშავება", "This package is not available": "ეს პაკეტი არ არის ხელმისაწვდომი", "This package is on the queue": "პაკეტი რიგშია", "This process is running with administrator privileges": "ეს პროცესი გაშვებულია ადმინისტრატორის პრივილეგიებით", "This project has no connection with the official {0} project — it's completely unofficial.": "ამ პროექტს არ აქვს კავშირი ოფიციალურ {0} პროექტთან - ის სრულიად არაოფიციალურია", "This setting is disabled": "ეს პარამეტრი გამორთულია", "This wizard will help you configure and customize WingetUI!": "ეს ოსტატი დაგეხმარებათ UniGetUI-ის კონფიგურაციაში და მორგებაში!", "Toggle search filters pane": "ძიების ფილტრების პანელის გადართვა", "Translators": "მთარგმნელები", "Try to kill the processes that refuse to close when requested to": "პროცესების ძალისმიერად გათიშვის ცდა, რომლებიც არ ითიშება მოთხოვნისას", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "ამის ჩართვით თქვენ ნებას რთავ გამოყენებულ იქნას შეცვლილი გამშვები ფაილები პაკეტების მმართველებთან მუშაობისთვის. ეს მოგცემთ უფრო მეტ მორგებადობას და ფუნქციონალს ინსტალაცისაც თუმცა არის საშიშიც", "Type here the name and the URL of the source you want to add, separed by a space.": "შეიყვანეთ სფეისით გამოყოფილი წყაროს სახელი და URL, რომელის დამატებაც გინდათ.", "Unable to find package": "შეუძლებელია პაკეტის მოძებნა", "Unable to load informarion": "შეუძლებელია ინფორმაციის ჩატვირთვა", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI აგროვებს ანონიმური გამოყენების მონაცემებს მომხმარებლის გამოცდილების გასაუმჯობესებლად.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI აგროვებს ანონიმური გამოყენების მონაცემებს მომხმარებლის გამოცდილების გაგებისა და გაუმჯობესების მიზნით.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI-მ აღმოაჩინა დესკტოპის ახალი მალსახმობი, რომელიც შეიძლება ავტომატურად წაიშალოს.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI-მ აღმოაჩინა შემდეგი დესკტოპის მალსახმობები, რომლებიც შეიძლება ავტომატურად წაიშალოს მომავალი განახლებებისას", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI-მ აღმოაჩინა {0} ახალი დესკტოპის მალსახმობები, რომლებიც შეიძლება ავტომატურად წაიშალოს.", "UniGetUI is being updated...": "მიმდინარეობს UniGetUI-ის განახლება...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI არ არის დაკავშირებული რომელიმე თავსებად პაკეტის მენეჯერთან. UniGetUI არის დამოუკიდებელი პროექტი.", "UniGetUI on the background and system tray": "UniGetUI ფონურ რეჟიმში და სისტემური პანელი", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI ან მისი ზოგიერთი კომპონენტი ვერ იქნა ნაპოვნი ან დაზიანებულია.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI მოითხოვს {0} მუშაობისთვის, მაგრამ ის არ მოიძებნა თქვენს სისტემაში.", "UniGetUI startup page:": "UniGetUI გაშვების გვერდი:", "UniGetUI updater": "UniGetUI განმაახლებელი", "UniGetUI version {0} is being downloaded.": "მიმდინარეობს UniGetUI-ის {0} ვერსიის ჩამოტვირთვა.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} მზად არის ინსტალაციისთვის.", "Uninstall": "წაშლა", "Uninstall Scoop (and its packages)": "Scoop-ის (და მისი პაკეტების) დეინსტალაცია", "Uninstall and more": "დეინსტალაცია და მეტი", "Uninstall and remove data": "დეინსტალაცია და მონაცემების წაშლა", "Uninstall as administrator": "დეინსტალაცია ადმინისტრატორის უფლებებით", "Uninstall canceled by the user!": "დეინსტალაცია გააუქმა მომხმარებელმა!", "Uninstall failed": "დეინსტალაცია ჩაიშალა", "Uninstall options": "დეინსტალაციის პარამეტრები", "Uninstall package": "პაკეტის დეინსტალაცია", "Uninstall package, then reinstall it": "პაკეტის დეინსტალაცია და ხელახლა ინსტალაცია", "Uninstall package, then update it": "პაკეტის დეინსტალაცია და შემდგომ მისი განახლება", "Uninstall previous versions when updated": "განახლებისას წინა ვერსიების დეინსტალაცია", "Uninstall selected packages": "შერჩეული პაკეტების დეინსტალაცია", "Uninstall selection": "მონიშნულის დეინსტალაცია", "Uninstall succeeded": "დეინსტალაცია წარმატებით დასრულდა", "Uninstall the selected packages with administrator privileges": "შერჩეული პაკეტების ადმინისტრატორის პრივილეგიებით დეინსტალაცია", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "დეინსტალირებადი პაკეტები წარმომავლობით როგორც \"{0}\" არ არის გამოქვეყნებული არც ერთ პაკეტების მმართველში, ასე რომ არ არის ინფორმაცია რომ ვაჩვენოთ მათ შესახებ.", "Unknown": "უცნობი", "Unknown size": "უცნობი ზომა", "Unset or unknown": "არ არის მითითებული ან უცნობია", "Up to date": "განახლებულია", "Update": "განახლება", "Update WingetUI automatically": "UniGetUI ავტომატური განახლება", "Update all": "ყველას განახლება", "Update and more": "განახლებები და მეტი", "Update as administrator": "ადმინისტრატორით განახლება", "Update check frequency, automatically install updates, etc.": "განახლებების შემოწმების სიხშირე, განახლებების ავტომატური ინსტალაცია და სხვ.", "Update checking": null, "Update date": "განახლების თარიღი", "Update failed": "განახლება ჩაიშალა", "Update found!": "ნაპოვნია განახლება!", "Update now": "ახლა განახლება", "Update options": "განახლების პარამეტრები", "Update package indexes on launch": "პაკეტების ინდექსების გაშვებისას განახლება", "Update packages automatically": "პაკეტების ავტომატური განახლება", "Update selected packages": "შერჩეული პაკეტების განახლება", "Update selected packages with administrator privileges": "შერჩეული პაკეტების ადმინისტრატორის პრივილეგიებით განახლება", "Update selection": "მონიშნულის განახლება", "Update succeeded": "განახლება წარმატებულად დასრულდა", "Update to version {0}": "განახლება {0} ვერსიამდე", "Update to {0} available": "ხელმისაწვდომია განახლება {0}-მდე", "Update vcpkg's Git portfiles automatically (requires Git installed)": "vcpkg-ის Git პორტფილების ავტომატურად განახლება (საჭიროებს Git-ის დაინსტალირებას)", "Updates": "განახლებები", "Updates available!": "ხელმისაწვდომია განახლებები!", "Updates for this package are ignored": "ამ პაკეტის განახლებები იგნორირებულია", "Updates found!": "ნაპოვნია განახლებები!", "Updates preferences": "განახლებების პარამეტრები", "Updating WingetUI": "UniGetUI-ის განახლება", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "მოძველებული WinGet-ის გამოყენება PowerShell CMDLet-ების ნაცვლად", "Use a custom icon and screenshot database URL": "მორგებული ხატულებისა და სქრინშოტების მონაცემთა ბაზის URL", "Use bundled WinGet instead of PowerShell CMDlets": "ჩაშენებული WinGet-ის გამოყენება PowerShell CMDlet-ების ნაცვლად", "Use bundled WinGet instead of system WinGet": "ჩაშენებული WinGet-ის გამოყენება სისტემური WinGet-ის ნაცვლად", "Use installed GSudo instead of UniGetUI Elevator": "GSudo-ს გამოყენება UniGetUI Elevator-ის ნაცვლად", "Use installed GSudo instead of the bundled one": "დაყენებული GSudo-ს გამოყენება ჩაშენებულის ნაცვლად", "Use system Chocolatey": "სისტემური Chocolatey-ის გამოყენება", "Use system Chocolatey (Needs a restart)": "სისტემური Chocolatey-ის გამოყენება (საჭიროებს გადატვირთვას)", "Use system Winget (Needs a restart)": "სისტემური Winget-ის გამოყენება (საჭიროებს გადატვირთვას)", "Use system Winget (System language must be set to english)": "სისტემური WinGet-ის გამოყენება (სისტემის ენა უნდა იყოს ინგლისური)", "Use the WinGet COM API to fetch packages": "WinGet COM API-ის გამოყენება პაკეტების ჩამოტვირთვისთვის", "Use the WinGet PowerShell Module instead of the WinGet COM API": "WinGet-ის PowerShell მოდულის გამოყენება WinGet COM API-ის ნაცვლად", "Useful links": "სასარგებლო ბმულები", "User": "მომხმარებელი", "User interface preferences": "სამომხმარებლო ინტერფეისის პარამეტრები", "User | Local": "მომხმარებელი | ლოკალური", "Username": "მომხმარებლის სახელი", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "UniGetUI-ის გამოყენება ნიშნავს GNU Lesser General Public License v2.1-ის მიღებას", "Using WingetUI implies the acceptation of the MIT License": "UniGetUI-ის გამოყენება ნიშნავს MIT ლიცენზიის მიღებას", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg ძირეული დირექტორია არ იქნა ნაპოვნი. გთხოვთ გამართოთ %VCPKG_ROOT% გარემოს ცვლდი UniGetUI პარამეტრებში", "Vcpkg was not found on your system.": "Vcpkg არ იქნა ნაპოვნი თქვენს სისტემაზე", "Verbose": "უფრო ინფორმატიული", "Version": "ვერსია", "Version to install:": "დასაყენებელი ვერსია:", "Version:": "ვერსია:", "View GitHub Profile": "GitHub-ის პროფილის ნახვა", "View WingetUI on GitHub": "UniGetUI-ის GitHub-ზე ნახვა", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "UniGetUI-ის წყარო კოდის ნახვა. იქ შეგიძლიათ ბაგების დარეპორტება ან ფუნქციონალის შემოთავაზება, ასევე პირდაპირ მიიღოთ UniGetUI პროექტში მონაწილეობა", "View mode:": "ნახვის რეჟიმი:", "View on UniGetUI": "UniGetUI-ში ნახვა", "View page on browser": "გვერდის ბრაუზერში ნახვა", "View {0} logs": "{0} ჟურნალის ნახვა", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "მოცდა სანამ მოწყობილობა დაუკავშირდება ინტერნეტს მანამ სანამ მოხდება ინტერნეტთან დაკავშირების საჭიროების მქონე ამოცანების შესრულება.", "Waiting for other installations to finish...": "ველოდებით სხვა ინსტალაციების დასრულებას...", "Waiting for {0} to complete...": "ველოდებით {0}-ის დასრულებას...", "Warning": "გაფრთხილება", "Warning!": "გაფრთხილება!", "We are checking for updates.": "ვამოწმებთ განახლებებს.", "We could not load detailed information about this package, because it was not found in any of your package sources": "ჩვენ ვერ შევძელით ჩაგვეტვირთა დეტალური ინფორმაცია ამ პაკეტის შესახებ, იმიტომ, რომ ის არ იქნა ნაპოვნი არც ერთ თქვენს პაკეტების წყაროში.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "ჩვენ ვერ ჩავტვირთეთ დამატებითი ინფორმაცია ამ პაკეტის შესახებ, იმიტომ რომ ის არ დაყენებულა ხელმისაწვდომი პაკეტების მმართველის მიერ.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "ჩვენ ვერ შევძელით {action} {package}. გთხოვთ ხელახლა ცადოდ მოგვიანებით. დაწკაპეთ \"{showDetails}\" რომ ნახოთ ინსტალატორის ჟურნალი.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "ჩვენ ვერ შევძელით {action} {package}. გთხოვთ ხელახლა ცადოდ მოგვიანებით. დაწკაპეთ \"{showDetails}\" რომ ნახოთ დეინსტალატორის ჟურნალი.", "We couldn't find any package": "ჩვენ ვერ ვიპოვეთ ვერც ერთი პაკეტი", "Welcome to WingetUI": "მოგესალმებათ UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "პაკეტების კრებულიდან ერთდროულად დაყენებისას დაყენდეს უკვე დაყენებული პაკეტებიც", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "ახალი მალსახმობების აღმოჩენისას, ამ დიალოგის ჩვენების ნაცვლად ავტომატურად წაშალეთ ისინი.", "Which backup do you want to open?": "რომელი ბექაფის გახსნა გსურთ?", "Which package managers do you want to use?": "რომელი პაკეტების მმართველების გამოყენება გსურთ?", "Which source do you want to add?": "რომელიწ წყაროს დამატება გსურთ?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "რადგან WinGet-ის გამოყენება შესაძლებელია UniGeUI-ში, ამავდროულად თქვენ შეგიძლიათ სხვა პაკეტების მმართველების გამოყენებაც, რამაც შესაძლოა დაგაბნიოთ. წარსულში UniGetUI შეიქმნა მხოლოდ Winget-თან მუშაობისთვის, მაგრამ ეს უკვე ასე არის.", "WinGet could not be repaired": "შეუძლებელია WinGet-ის შეკეთება", "WinGet malfunction detected": "დაფიქსირდა WinGet-ის გაუმართაობა", "WinGet was repaired successfully": "WinGet წარმატებით შეკეთდა", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - ყველაფერი განახლებულია", "WingetUI - {0} updates are available": "UniGetUI - ხელმისაწვდომია {0} განახლება", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "UniGetUI ვებ-საიტი", "WingetUI Homepage - Share this link!": "UniGetUI-ის ვებ-საიტი - გააზიარე ეს ბმული!", "WingetUI License": "UniGetUI-ის ლიცენზია", "WingetUI Log": "UniGetUI-ის ჟურნალი", "WingetUI Repository": "UniGetUI რეპოზიტორია", "WingetUI Settings": "UniGetUI-ს პარამეტრები", "WingetUI Settings File": "UniGetUI-ის პარამეტრების ფაილი", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI იყენებს შემდეგ ბიბლიოთეკებს. მათ გარეშე UniGetUI-ს არსებობა შეუძლებელი იქნებოდა.", "WingetUI Version {0}": "UniGetUI ვერსია {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI ავტომატური დაწყების ქცევა, აპლიკაციის გაშვების პარამეტრები", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI-ს შეუძლია შეამოწმოს, არის  თუ არა თქვენი პროგრამულ უზრუნველყოფის  განახლებები ხელმისაწვდომი და თუ გსურთ, ავტომატურად დააინსტალირებს მათ", "WingetUI display language:": "UniGetUI-ის ინტერფეისის ენა:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI გაშვებულია როგორც ადმინისტრატორი, რაც არ არის რეკომენდებული. UniGetUI-ის პრივილეგიებით გაშვებისას, UniGetUI-დან გაშვებული ყველა ოპერაციას ექნება ადმინისტრატორის პრივილეგიები. თქვენ კვლავ შეგიძლიათ გამოიყენოთ პროგრამა, მაგრამ ჩვენ გირჩევთ არ გაუშვათ UniGetUI ადმინისტრატორის პრივილეგიებით.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI თარგმნილია 40-ზე მეტ ენაზე მოხალისეების მიერ. მადლობა 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI არ არის თარგმნილი მანქანურად! ამ მომხმარებლებმა უზრუნველყვეს თარგმნა:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI არის აპლიკაცია, რომელიც გიადვილებთ პროგრამული უზრუნველყოფის მათვას, ეს მიიღწევა ყველა-ერთში გრაფიკული ინფტერფეისის საშუალებით თქვენი ბრძანების ზოლის პაკეტების მენეჯერებისთვის.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI-ის სახელის შეცვლის მიზეზი არის ის, რომ მოხდეს მკაფიო განსხვავება UniGetUI-სა (ინტერფეისი რომელსაც ახლა იყენებთ) და WinGet-ს (Microsoft-ის მერ შექმნილი პაკეტების მენეჯერი, რომელთანაც მე არ მაქვს კავშირი) შორის.", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI ახლდება. დასრულების შემდეგ, UniGetUI თავად გადაიტვირთება", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI უფასოა და სამუდამოდ უფასო იქნება. არც რეკლამები, არც საკრედიტო ბარათი, არც პრემიუმ ვერსია. 100% უფასო, სამუდამოდ.", "WingetUI log": "UniGetUI-ის ჟურნალი", "WingetUI tray application preferences": "UniGetUI სისტემური პანელის აპლიკაციის პარამეტრები", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI იყენებს შემდეგ ბიბლიოთეკებს. მათ გარეშე UniGetUI-ს არსებობა შეუძლებელი იქნებოდა.", "WingetUI version {0} is being downloaded.": "იტვირთება UniGetUI-ის {0} ვერსია.", "WingetUI will become {newname} soon!": "UniGetUI მალე {newname} გახდება!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI არ შეამოწმებს განახლებებს პერიოდულად. განახლებები კვლავ შემოწმდება გაშვებისას, მაგრამ თქვე ამის შესახებ გაფრთხილებას არ მიიღებთ.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI აჩვენებს UAC მოთხოვნას ყოველ ჯერზე, როცა პაკეტი მოითხოვს პრივილეგიებს დაყენებისას.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI მალე გახდება {newname}. ამით აპლიკაცია არ შეცვლება. მე (დეველოპერი) გავაგრძელებ მუშობას ამ პროექტზე ისევე, როგორც ახლა, მაგრამ სხვა სახელით.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI შეუძლებელი იქნებოდა ჩვენი ძვირფასი კონტრიბუტორების დახმარების გარეშე. ნახეთ მათი GitHub პროფილები, UniGetUI მათ გარეშე შეუძლებელი იქნებოდა!\n", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI-ის არსებობა შეუძლებელი იქნებოდა შემომწირველების დახმარების გარეშე. მადლობა თქვენ 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} მზადაა ინსტალაციისთვის.", "Write here the process names here, separated by commas (,)": "აქ შეიყვანეთ პროცესების სახელები, გამოყავით მძიმეებით (,)", "Yes": "კი", "You are logged in as {0} (@{1})": "თქვენ შესული ხართ როგორც {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "თქვენ შეგიძლიათ ამის შეცვლა UniGetUI-ის უსაფრთხოების პარამეტრებში.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "თქვენ შეგიძლიათ განსაზღვროთ ბძანებები, რომლებიც გაეშვება პაკეტის ინსტალაციის განახლების და დეინსტალაციის დაწყებამდე ან შემდეგ. ბრძანებები ტერმინალში გაეშვება, ასე რომ შეგიძლიათ CMD სკრიპტების გამოყენებაც.", "You have currently version {0} installed": "თქვენი მიმდინარე ვერსიაა {0}", "You have installed WingetUI Version {0}": "თქვენ დააყენეთ UniGetUI-ის {0} ვერსია", "You may lose unsaved data": "თქვენ შესაძლოა დაკარგოთ შეუნახავი მონაცემები", "You may need to install {pm} in order to use it with WingetUI.": "თქვენ შესაძლოა დაგჭირდეთ {pm} იმისთვის, რომ UniGetUI-თ ისარგებლოთ.", "You may restart your computer later if you wish": "თუ გსურთ თქვენ შეგიძლიათ გადატვირთოთ თქვენი კომპიუტერი მოგვიანებით", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "თქვენ ამას მხოლო ერთხელ შეგეკითხებიან და ადმინისტრატორის უფლებები გამოყენებული იქნება პაკეტებისთვის, რომლებიც ამას მოითხოვენ.", "You will be prompted only once, and every future installation will be elevated automatically.": "თქვენ ამას მხოლოდ ერთხელ შეგეკითხებიან და ყოველი შემდგომი ინსტალაცია ავტომატურად გაეშვება პრივილეგიებით", "You will likely need to interact with the installer.": "თვენ შესაძლოა ინსტალატორთან ინტერაქცია მოგიწიოთ", "[RAN AS ADMINISTRATOR]": "ადმინისტრატორით გაშვება", "buy me a coffee": "ყავაზე დამპატიჟე", "extracted": "გამოარქივდა", "feature": "ფუნქცია", "formerly WingetUI": "ადრე ცნობილი როგორც WingetUI", "homepage": "ვებ-საიტი", "install": "დაინსტალირება", "installation": "ინსტალაცია", "installed": "დაინსტალირდა", "installing": "ინსტალირდება", "library": "ბიბლიოთეკა", "mandatory": "სავალდებულო", "option": "ოფცია", "optional": "არასავალდებულო", "uninstall": "დეინსტალაცია", "uninstallation": "დეინსტალაცია", "uninstalled": "დეინსტალირდა", "uninstalling": "დეინსტალაცია", "update(noun)": "განახლება", "update(verb)": "განახლება", "updated": "განახლდა", "updating": "ახლდება", "version {0}": "ვერსია {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} ინსტალაციის პარამეტრები ამჟამად დაბლოკილია იმიტომ, რომ {0} იყენებს ნაგულისმევ ინსტალაციის პარამეტრებს.", "{0} Uninstallation": "{0} დეინსტალაცია", "{0} aborted": "{0} გაუქმდა", "{0} can be updated": "{0} შესაძლებელია განახლება", "{0} can be updated to version {1}": "{0} შესაძლებელია განახლდეს {1} ვერსიამდე", "{0} days": "{0} დღე", "{0} desktop shortcuts created": "{0} სამუშაო მაგიდაზე შეიქმნა მალსახმობი", "{0} failed": "{0} ჩაიშალა", "{0} has been installed successfully.": "{0}  წარმატებით დაინსტალირდა", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} წარმატებით დაყენდა. რეკომენდებულია UniGetUI-ის ხელახლა გაშვება ინსტალაციის დასრულებისთვის", "{0} has failed, that was a requirement for {1} to be run": "{0} ჩაიშალა, ის იყო აუცილებელი {1}-ის გაშვებისთვის", "{0} homepage": "{0} მთავარი გვერდი", "{0} hours": "{0} საათი", "{0} installation": "{0} ინსტალაცია", "{0} installation options": "{0} ინსტალაციის პარამეტრები", "{0} installer is being downloaded": "იტვირთება {0}-ის ინსტალატორი", "{0} is being installed": "ინსტალირდება {0}", "{0} is being uninstalled": "{0} დეინსტალირდება", "{0} is being updated": "ახლდება {0}", "{0} is being updated to version {1}": "{0} ახლდება {1} ვერსიამდე", "{0} is disabled": "{0} გამორთულია", "{0} minutes": "{0} წუთი", "{0} months": "{0} თვე", "{0} packages are being updated": "მიმდინარეობს {0} პაკეტის განახლება", "{0} packages can be updated": "შესაძლებელი {0} პაკეტის განახლება", "{0} packages found": "ნაპოვნია {0} პაკეტი", "{0} packages were found": "ნაპოვნია {0} პაკეტი", "{0} packages were found, {1} of which match the specified filters.": "ნაპოვნია {0} პაკეტი, {1} შეესაბამება მითითებულ ფილტრებს.", "{0} selected": null, "{0} settings": "{0}-ის პარამეტრები", "{0} status": "{0} სტატუსი", "{0} succeeded": "{0} წარმატებულია", "{0} update": "{0} განახლება", "{0} updates are available": "ხელმისაწვდომა {0}  განახლება", "{0} was {1} successfully!": " {0} იყო {1} წარმატებით!", "{0} weeks": "{0} კვირა", "{0} years": "{0} წელი", "{0} {1} failed": "{0} {1} ჩაიშალა", "{package} Installation": "{package} ინსტალაცია", "{package} Uninstall": "{package} დეინსტალაცია", "{package} Update": "{package} განახლება", "{package} could not be installed": "{package}-ის ინსტალაცია შეუძლებელია", "{package} could not be uninstalled": "{package}-ის დეინსტალაცია შეუძლებელია", "{package} could not be updated": "{package}-ის განახლება შეუძლებელია", "{package} installation failed": "{package}-ის ინსტალაცია ჩაიშალა", "{package} installer could not be downloaded": "ვერ ხერხდება {package}-ის ინსტალატორის ჩამოტვირთვა ", "{package} installer download": "{package}-ის ინსტალატორის ჩამოტვირთვა", "{package} installer was downloaded successfully": "{package}-ის ინსტალატორი წარმატებით ჩამოიტვირთა", "{package} uninstall failed": "{package} წაშლა ჩაიშალა", "{package} update failed": "{package} განახლება ჩაიშალა", "{package} update failed. Click here for more details.": "{package} განახლება ჩაიშალა. დაწკაპეთ აქ მეტი დეტალისთვით.", "{package} was installed successfully": "{package} წარმატებით დაინსტალირდა", "{package} was uninstalled successfully": "{package} წარმატებით წაიშალა", "{package} was updated successfully": "{package} წარმატებით განახლდა", "{pcName} installed packages": "{pcName} დაყენებული პაკეტები", "{pm} could not be found": "{pm} არ არის ნაპოვნი", "{pm} found: {state}": "{pm} ნაპოვნია: {state}", "{pm} is disabled": "{pm} გამორთულია", "{pm} is enabled and ready to go": "{pm} ჩართულია და მზად არის გასაშვებად", "{pm} package manager specific preferences": "{pm} პაკეტების მენეჯერის სპეციფიური პარამეტრები", "{pm} preferences": "{pm} პარამეტრები", "{pm} version:": "{pm} ვერსია:", "{pm} was not found!": "{pm} ვერ იქნა ნაპოვნი!"}