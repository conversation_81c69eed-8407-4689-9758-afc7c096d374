{"\"{0}\" is a local package and can't be shared": "\"{0}\" é um pacote local e não pode ser partilhado", "\"{0}\" is a local package and does not have available details": "\"{0}\" é um pacote local e não tem detalhes disponíveis", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" é um pacote local e não é compatível com esta funcionalidade", "(Last checked: {0})": "(Verificado em: {0})", "(Number {0} in the queue)": "(Número {0} na fila)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@T<PERSON><PERSON>_<PERSON>, @PoetaGA, @Putocoroa, @100Nome", "0 packages found": "Nenhum pacote encontrado", "0 updates found": "Nenhuma atualização encontrada", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 dia", "1 hour": "1 hora", "1 month": "1 mês", "1 package was found": "1 pacote encontrado", "1 update is available": "1 atualização disponível", "1 week": "1 semana", "1 year": "1 ano", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON> para a página \"{0}\" ou \"{1}\".", "2 - Warnings": "2 - Avisos", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Localize o(s) pacote(s) que deseja adicionar ao conjunto e, selecione a caixa mais à esquerda.", "3 - Information (less)": "3 - Informação (menos)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON>uando os pacotes que deseja adicionar ao conjunto estiverem selecionados, encontre e clique na opção \"{0}\" na barra de tarefas.", "4 - Information (more)": "4 - Informação (mais)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. O<PERSON> seus pacotes foram adicionados ao conjunto. Pode continuar a adicionar pacotes ou, exportar o conjunto.", "5 - information (debug)": "5 - Informação (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Gestor de bibliotecas de C/C++. Cheio de bibliotecas C/C++ e outros utilitários relacionados com C/C++.<br>Contém: <b>bibliotecas C/C++ e utilitários relacionados</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Um repositório cheio de ferramentas projetadas com o ecossistema .NET da Microsoft em mente.<br>Contém: <b>Ferramentas e scripts relacionado(a)s com o .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Um repositório cheio de ferramentas projetadas com o ecossistema .NET da Microsoft em mente.<br>Contém: <b>Ferramentas relacionadas com o .NET</b>", "A restart is required": "É necessária uma reinicialização", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "Sobre", "About Qt6": "Sobre o Qt6", "About WingetUI": "Sobre o UniGetUI", "About WingetUI version {0}": "Sobre a versão {0} do UniGetUI", "About the dev": "Sobre o programador ", "Accept": "Aceitar", "Action when double-clicking packages, hide successful installations": "Ação ao clicar duas vezes em pacotes, ocultar instalações bem-sucedidas", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "Adicionar uma fonte a {0}", "Add a timestamp to the backup file names": "Adicionar data e hora aos nomes dos ficheiros de cópia de segurança", "Add a timestamp to the backup files": "Adicionar data e hora aos ficheiros de cópia de segurança", "Add packages or open an existing bundle": "Adicionar paco<PERSON> ou abrir um conjunto existente", "Add packages or open an existing package bundle": "Adicionar pacotes ou abrir um conjunto de pacotes existente", "Add packages to bundle": "<PERSON><PERSON><PERSON><PERSON> pacotes ao conjunto", "Add packages to start": "Adicionar paco<PERSON> para começar", "Add selection to bundle": "Adicionar a seleção ao conjunto", "Add source": "<PERSON><PERSON><PERSON><PERSON> fonte", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Adicionar atualizações que falham com o erro \"nenhuma atualização encontrada\" à lista de atualizações ignoradas", "Adding source {source}": "Adicionando fonte {source}", "Adding source {source} to {manager}": "Adicionar fonte {source} a {manager}", "Addition succeeded": "Adição bem sucedida", "Administrator privileges": "Privilégios de administrador", "Administrator privileges preferences": "Preferências dos privilégios de administrador", "Administrator rights": "Direitos de administrador", "Administrator rights and other dangerous settings": "Direitos de administrador e outras definições perigosas", "Advanced options": "Opções avançadas", "All files": "Todos os ficheiros", "All versions": "<PERSON><PERSON> as vers<PERSON><PERSON>", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "<PERSON><PERSON><PERSON> que as operações dos pacotes possam correr em paralelo", "Allow parallel installs (NOT RECOMMENDED)": "Permitir instalaçõ<PERSON> paralelas (NÃO RECOMENDADO)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Permitir que operações {pm} sejam executadas em paralelo", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Em alternativa, também pode instalar {0} ao executar o seguinte comando numa janela do Windows PowerShell:", "Always elevate {pm} installations by default": "Elevar sempre as instalações do {pm} por defeito ", "Always run {pm} operations with administrator rights": "Executar sempre operações {pm} com direitos de administrador", "An error occurred": "Ocorreu um erro", "An error occurred when adding the source: ": "Ocorreu um erro ao adicionar a fonte: ", "An error occurred when attempting to show the package with Id {0}": "Ocorreu um erro ao tentar mostrar o pacote com o ID {0}", "An error occurred when checking for updates: ": "Ocorreu um erro ao procurar por atualizaçoes:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "Ocorreu um erro ao carregar uma cópia de segurança:", "An error occurred while logging in: ": "Ocorreu um erro ao iniciar sessão:", "An error occurred while processing this package": "Ocorreu um erro a processar este pacote", "An error occurred:": "Ocorreu um erro:", "An interal error occurred. Please view the log for further details.": "Ocorreu um erro interno. Por favor veja os registos para mais detalhes.", "An unexpected error occurred:": "Ocorreu um erro inesperado:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Ocorreu um problema inesperado ao tentar reparar o WinGet. Por favor, tente mais tarde", "An update was found!": "Foi encontrada uma atualização!", "Android Subsystem": "Subsistema Android", "Another source": "Outra fonte", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Quaisquer novos atalhos criados durante uma operação de instalação ou atualização serão apagados automaticamente, em vez de mostrar um pedido de confirmação na primeira vez que são detetados", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Quaisquer atalhos criados ou modificados fora do UniGetUI serão ignorados. Poderá adicioná-los através do botão {0}", "Any unsaved changes will be lost": "<PERSON><PERSON> as alterações não guardadas serão perdidas", "App Name": "<PERSON><PERSON>", "Appearance": "Aparência", "Application theme, startup page, package icons, clear successful installs automatically": "Tema da aplicação, página de arranque, ícones de pacotes, limpar instalações bem-sucedidas automaticamente", "Application theme:": "Tema da aplicação:", "Apply": "Aplicar", "Architecture to install:": "Arquitetura para instalar:", "Are these screenshots wron or blurry?": "As capturas de ecrã estão incorrectas ou desfocadas?", "Are you really sure you want to enable this feature?": "Tem mesmo a certeza de que pretende ativar esta funcionalidade?", "Are you sure you want to create a new package bundle? ": "Tem a certeza de que pretende criar um novo conjunto de pacotes?", "Are you sure you want to delete all shortcuts?": "Tem a certeza que pretende apagar todos os atalhos?", "Are you sure?": "Tem certeza?", "Ascendant": "Ascendente", "Ask for administrator privileges once for each batch of operations": "Pedir direitos de administrador uma vez para cada conjunto de operações", "Ask for administrator rights when required": "Pedir direitos de administrador quando necessário", "Ask once or always for administrator rights, elevate installations by default": "Pedir direitos de administrador uma vez ou sempre, elevar as instalações por defeito ", "Ask only once for administrator privileges": "Pedir direitos de administrador apenas uma vez", "Ask only once for administrator privileges (not recommended)": "Pedir direitos de administrador apenas uma vez (não recomendado)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Pedir para eliminar atalhos do ambiente de trabalho criados durante uma instalação ou atualização.", "Attention required": "Atenção necessária", "Authenticate to the proxy with an user and a password": "Autenticar com o proxy com nome de utilizador e palavra-passe", "Author": "Autor(a)", "Automatic desktop shortcut remover": "Remoção automática de atalhos do ambiente de trabalho", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Guardar automaticamente uma lista de todos os pacotes instalados para ser mas fácil reinstalá-los.", "Automatically save a list of your installed packages on your computer.": "Guardar automaticamente uma lista dos pacotes instalados no teu computador.", "Autostart WingetUI in the notifications area": "Iniciar automaticamente o UniGetUI para a área de notificações", "Available Updates": "Atualizações disponíveis", "Available updates: {0}": "Atualizações disponíveis: {0} ", "Available updates: {0}, not finished yet...": "Atualizações disponíveis: {0}, ainda não terminou...", "Backing up packages to GitHub Gist...": null, "Backup": "Cópia de segurança", "Backup Failed": "Cópia de segurança falhou", "Backup Successful": "Cópia de segurança bem-sucedida", "Backup and Restore": "Cópia de segurança e restauro", "Backup installed packages": "Fazer cópia de segurança dos pacotes instalados", "Backup location": "Localização da cópia de segurança", "Become a contributor": "Torne-se num contribuidor", "Become a translator": "Torne-se num tradutor", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Recursos beta e outras opções que não devem ser alteradas", "Both": "Ambos", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Mas aqui estão outras coisas que pode fazer para aprender ainda mais sobre o UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Ao desativar um gestor de pacotes, deixar<PERSON> de poder ver ou atualizar os seus pacotes.", "Cache administrator rights and elevate installers by default": "Armazenar em cache os direitos de administrador e elevar os instaladores por defeito ", "Cache administrator rights, but elevate installers only when required": "Armazenar em cache os direitos de administrador, mas elevar os instaladores somente quando necessário", "Cache was reset successfully!": "A cache foi reposta com sucesso!", "Can't {0} {1}": "Não é possível {0} o {1}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "Change backup output directory": "Alterar diretório do ficheiro de cópia de segurança", "Change default options": "Alterar opções padrão", "Change how UniGetUI checks and installs available updates for your packages": "Altera a forma como o UniGetUI verifica e instala as atualizações disponíveis para os seus pacotes", "Change how UniGetUI handles install, update and uninstall operations.": "Mudar como o UniGetUI lida com as operações de instalação, atualização e desinstalação.", "Change how UniGetUI installs packages, and checks and installs available updates": "Mudar como o UniGetUI instala pacotes e verifica e instala atualizações disponíveis", "Change how operations request administrator rights": "<PERSON><PERSON> como as operações pedem direitos de administrador", "Change install location": "Alterar diretório de instalação", "Change this": "<PERSON><PERSON>r isto", "Change this and unlock": "Alterar isto e desblo<PERSON>ar", "Check for package updates periodically": "Verificar se há atualizações de pacotes periodicamente", "Check for updates": "Verificar atualizações", "Check for updates every:": "Procurar atualizações a cada:", "Check for updates periodically": "Procurar atualizações periodicamente", "Check for updates regularly, and ask me what to do when updates are found.": "Procurar atualizações regularmente, e perguntar-me o que fazer quando forem encontradas.", "Check for updates regularly, and automatically install available ones.": "Procurar atualizações regularmente e instalar automaticamente as disponíveis.", "Check out my {0} and my {1}!": "Verifica o meu {0} e a minha {1}!", "Check out some WingetUI overviews": "<PERSON>eja alguns tutoriais gerais sobre o UniGetUI", "Checking for other running instances...": "A verificar outras instâncias em execução...", "Checking for updates...": "A verificar atualizações...", "Checking found instace(s)...": "A verificar instância(s) encontrada(s)...", "Choose how many operations shouls be performed in parallel": "Escolha quantas operações devem ser efetuadas em paralelo", "Clear cache": "Limpar cache", "Clear finished operations": "Limpar operações concluídas", "Clear selection": "<PERSON><PERSON> ", "Clear successful operations": "Operações com sucesso e sem erros", "Clear successful operations from the operation list after a 5 second delay": "<PERSON><PERSON> as operações bem-sucedidas da lista de operações após 5 segundos", "Clear the local icon cache": "Limpe a cache local de ícones", "Clearing Scoop cache - WingetUI": "A limpar a cache do Scoop - UniGetUI", "Clearing Scoop cache...": "A limpar a cache do Scoop...", "Click here for more details": "Clique aqui para mais de<PERSON>hes", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Clique em Instalar para iniciar o processo de instalação. Se saltar a instalação, o UniGetUI poderá não funcionar como esperado.", "Close": "<PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Fechar o UniGetUI para a bandeja do sistema", "Close WingetUI to the notification area": "Fechar o UniGetUI para a área de notificações", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "<PERSON><PERSON><PERSON> da linha de comandos", "Command-line to run:": "<PERSON><PERSON> de comandos para correr:", "Compare query against": "Comparar consulta com", "Compatible with authentication": "Compatível com autenticação", "Compatible with proxy": "Compatível com proxy", "Component Information": "Informações dos Componentes", "Concurrency and execution": "Simultaneidade e execução", "Connect the internet using a custom proxy": "Ligar à internet usando um proxy personalizado", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Contribuir para o repositório de ícones e capturas de ecrã", "Contributors": "Contribuidores", "Copy": "Copiar", "Copy to clipboard": "Copiar para área de transferência", "Could not add source": "Não foi possível adicionar a fonte", "Could not add source {source} to {manager}": "Não foi possível adicionar a fonte {source} a {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "Não foi possível criar o conjunto de pacotes", "Could not load announcements - ": "Não foi possível carregar os anúncios - ", "Could not load announcements - HTTP status code is $CODE": "Não foi possível carregar os anúncios - O código de estado do HTTP é $CODE", "Could not remove source": "Não foi possível remover a fonte", "Could not remove source {source} from {manager}": "Não foi possível remover a fonte {source} do {manager}", "Could not remove {source} from {manager}": "Não foi possível remover {source} de {manager}", "Create .ps1 script": null, "Credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Current Version": "<PERSON><PERSON><PERSON> ", "Current status: Not logged in": null, "Current user": "<PERSON><PERSON><PERSON><PERSON>", "Custom arguments:": "Argumentos personalizados:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Argumentos de linha de comando personalizados:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Personalizar UniGetUI - apenas para hackers e utilizadores avançados", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "AVISO: NÃO SOM<PERSON> RESPONSÁVEIS PELOS PACOTES DESCARREGADOS. CERTIFIQUE-SE DE INSTALAR APENAS SOFTWARE DE CONFIANÇA.", "Dark": "Escuro", "Decline": "<PERSON><PERSON><PERSON><PERSON>", "Default": "Padrão", "Default installation options for {0} packages": "Opções de instalação padrão para {0} pacotes", "Default preferences - suitable for regular users": "Preferências padrão - adequado para utilizadores frequentes", "Default vcpkg triplet": "Trio vcpkg por defeito", "Delete?": "Apagar?", "Dependencies:": "Dependências:", "Descendant": "Descendente", "Description:": "Descrição:", "Desktop shortcut created": "Atalho criado no ambiente de trabalho", "Details of the report:": "Detalhes do relatório:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "O desenvolvimento de software é difícil e esta aplicação é gratuita. Mas se gostou da aplicação, pode sempre <b>comprar-me um café</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Instalar directamente ao clicar duas vezes num item na secção \"{discoveryTab}\" (em vez de mostrar as informações do pacote)", "Disable new share API (port 7058)": "Desativar a nova API de partilha (porta 7058)", "Disable the 1-minute timeout for package-related operations": "Desativar o tempo limite de 1 minuto para operações relacionadas com pacotes", "Disclaimer": "Renúncia de responsabilidade", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON>", "Discover packages": "<PERSON><PERSON><PERSON><PERSON> paco<PERSON>", "Distinguish between\nuppercase and lowercase": "Distinguir entre maiúsculas \ne minúsculas", "Distinguish between uppercase and lowercase": "Distinguir entre maiúsculas \ne minúsculas", "Do NOT check for updates": "NÃO procurar atualiza<PERSON>es ", "Do an interactive install for the selected packages": "Fazer uma instalação interativa para os pacotes selecionados ", "Do an interactive uninstall for the selected packages": "Fazer uma uma desinstalação interativa para os pacotes selecionados ", "Do an interactive update for the selected packages": "Fazer uma atualização interativa para os pacotes selecionados ", "Do not automatically install updates when the battery saver is on": "Não instalar atualizações automaticamente quando a poupança de bateria está ativa", "Do not automatically install updates when the network connection is metered": "Não instalar atualizações automaticamente quando a ligação à rede é limitada", "Do not download new app translations from GitHub automatically": "Não descarregar automaticamente novas versões das traduções do GitHub", "Do not ignore updates for this package anymore": "<PERSON><PERSON><PERSON> <PERSON> as atualizações deste pacote", "Do not remove successful operations from the list automatically": "Não remover automaticamente operações bem sucedidas da lista", "Do not show this dialog again for {0}": "Não mostrar esta caixa de diálogo novamente para {0}", "Do not update package indexes on launch": "Não atualizar os índices de pacotes ao iniciar", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Aceita que o UniGetUI recolha e envie estatísticas de uso anónimas, com o único propósito de entender e melhorar a experiência do utilizador?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Acha o UniGetUI útil? Se puder, pode apoiar meu trabalho, para que eu possa continuar a fazer do UniGetUI a interface definitiva de gestão de pacotes.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Acha o UniGetUI útil? Gostaria de apoiar o programador? Se sim, pode {0}, ajuda muito!", "Do you really want to reset this list? This action cannot be reverted.": "Tem a certeza que deseja repor esta lista? Esta ação não pode ser revertida.", "Do you really want to uninstall the following {0} packages?": "Deseja realmente desinstalar os seguin<PERSON> {0} paco<PERSON>?", "Do you really want to uninstall {0} packages?": "Deseja realmente desinstalar {0} paco<PERSON>?", "Do you really want to uninstall {0}?": "Deseja realmente desinstalar {0}?", "Do you want to restart your computer now?": "Deseja reiniciar o computador agora?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Quer traduzir o UniGetUI para o seu idioma? Veja como contribuir <a style=\"color:{0}\" href=\"{1}\"a>AQUI!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Não está com vontade de doar? Não se preocupe, pode sempre partilhar o UniGetUI com os seus amigos. Divulgue o UniGetUI.", "Donate": "<PERSON><PERSON>", "Done!": "<PERSON><PERSON>!", "Download failed": "A descarga falhou", "Download installer": "<PERSON><PERSON><PERSON><PERSON> instalador", "Download operations are not affected by this setting": "Operações de descarga não são afetadas por esta definição", "Download selected installers": "Descarregar instaladores selecionados", "Download succeeded": "<PERSON><PERSON><PERSON> bem sucedida", "Download updated language files from GitHub automatically": "Descarregar automaticamente novas versões das traduções do GitHub", "Downloading": "A descarregar", "Downloading backup...": "A descarregar cópia de segurança...", "Downloading installer for {package}": "A descarregar o instalador para {package}", "Downloading package metadata...": "A descarregar metadados do pacote...", "Enable Scoop cleanup on launch": "Ativar a limpeza do Scoop ao iniciar", "Enable WingetUI notifications": "Ativar notificações do UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Ativar um solucionador de problemas melhorado [experimental] do UniGetUI", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "Ativar as otimizações de utilização de CPU em segundo plano (ver o Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Ativar api em segundo plano (UniGetUI Widgets and Sharing, porta 7058)", "Enable it to install packages from {pm}.": "Ative-o para instalar pacotes de {pm}", "Enable the automatic WinGet troubleshooter": "Ativar a resolução de problemas automática do WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Ativar a nova caixa de diálogo UAC do UniGetUI", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Ativar o {pm}", "Enter proxy URL here": "Introduza o URL do proxy aqui", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": "Entradas a AMARELO serão IGNORADAS.", "Error": "Erro", "Everything is up to date": "Todos os pacotes estão atualizados", "Exact match": "Correspondência exata", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Atalhos existentes na área de trabalho serão analizados e, ter<PERSON> de escolher quais deseja manter e quais deseja remover.", "Expand version": "Expandir", "Experimental settings and developer options": "Definições experimentais e opções do programador ", "Export": "Exportar", "Export log as a file": "Exportar log para um ficheiro", "Export packages": "Exportar pacotes", "Export selected packages to a file": "Exportar seleção para um ficheiro", "Export settings to a local file": "Exportar definições para um ficheiro local", "Export to a file": "Exportar para um ficheiro", "Failed": "Fal<PERSON>", "Fetching available backups...": null, "Fetching latest announcements, please wait...": "A descarregar os anúncios mais recentes, por favor aguarde...", "Filters": "<PERSON><PERSON><PERSON>", "Finish": "Finalizar", "Follow system color scheme": "Padrão do sistema", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Utilizar a versão do Winget compilada para ARM (APENAS PARA SISTEMAS ARM64)", "Formerly known as WingetUI": "Anteriormente conhecido como WingetUI", "Found": "Encontrado", "Found packages: ": "Pacotes encontrados:", "Found packages: {0}": "Pacotes encontrados: {0}", "Found packages: {0}, not finished yet...": "Pacotes encontrados: {0}, ainda não terminou...", "General preferences": "Preferências gerais", "GitHub profile": "Perfil do GitHub", "Global": "Global", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Ótimo repositório de utilitários desconhecidos, ma<PERSON>, e outros pacotes interessantes.<br>Contém: <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Programas de linha de comando, Software geral (requer o bucket extras)</b>", "Great! You are on the latest version.": "Excelente! Tem a versão mais recente.", "Grid": "Grelha", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Ajuda e documentação", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Aqui pode alterar o comportamento do UniGetUI em relação aos seguintes atalhos. Marcar um atalho fará com que o UniGetUI o apague se for criado numa atualização futura. Desmarcá-lo manterá o atalho intacto", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON>, o meu nome é Martí, e sou o <i>programador</i> do UniGetUI. O UniGetUI foi feito inteiramente no meu tempo livre!", "Hide details": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "Homepage": "Página Inicial", "Hooray! No updates were found.": "Viva! Nenhuma atualização encontrada!", "How should installations that require administrator privileges be treated?": "Como devem ser tratadas as instalações que requerem privilégios de administrador?", "How to add packages to a bundle": "Como adicionar pacotes a um conjunto", "I understand": "<PERSON><PERSON><PERSON>", "Icons": "Ícones", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Ignorar atualizações futuras para este pacote", "Ignore packages from {pm} when showing a notification about updates": "Ignorar pacotes de {pm} ao mostrar uma notificação sobre atualizações", "Ignore selected packages": "Ignorar pacotes selecionados ", "Ignore special characters": "Ignorar caracteres especiais", "Ignore updates for the selected packages": "Ignorar atualizações para os pacotes selecionados ", "Ignore updates for this package": "Ignorar atualizaç<PERSON> ", "Ignored updates": "Atualizações ignoradas", "Ignored version": "<PERSON><PERSON><PERSON>", "Import": "Importar", "Import packages": "Importar pacotes", "Import packages from a file": "Importar pacotes de um ficheiro", "Import settings from a local file": "Importar definições de um ficheiro local", "In order to add packages to a bundle, you will need to: ": "Para adicionar pacotes a um conjunto, terá de:", "Initializing WingetUI...": "A iniciar o UniGetUI...", "Install": "Instalar", "Install Scoop": "In<PERSON>ar o <PERSON>oop", "Install and more": "Instalar e mais", "Install and update preferences": "Instalar e atualizar preferências", "Install as administrator": "Instalar como administrador", "Install available updates automatically": "Instalar as atualizações disponíveis automaticamente", "Install location can't be changed for {0} packages": "A localização da instalação não pode ser alterada para {0} pacotes", "Install location:": "Local de instalação:", "Install options": "Opções de instalação", "Install packages from a file": "Instalar pacotes de um ficheiro", "Install prerelease versions of UniGetUI": "Instalar versões beta do UniGetUI", "Install script": null, "Install selected packages": "Instalar pacotes selecionados ", "Install selected packages with administrator privileges": "Instalar pacotes selecionados com privilégios de administrador", "Install selection": "Instalar se<PERSON>ção", "Install the latest prerelease version": "Instalar a versão mais recente de pré-lançamento", "Install updates automatically": "Instalar atualizações automaticamente", "Install {0}": "Instalar {0}", "Installation canceled by the user!": "Instalação cancelada pelo utilizador!", "Installation failed": "Instalação falhada", "Installation options": "Opções de instalação", "Installation scope:": "Âmbito da instalação:", "Installation succeeded": "Instalação bem sucedida", "Installed Packages": "Pacotes Instalados", "Installed Version": "Versão Instalada", "Installed packages": "Pacotes instalados", "Installer SHA256": "SHA256 do Instalador", "Installer SHA512": "SHA512 do instalador", "Installer Type": "Tipo de Instalador", "Installer URL": "URL do Instalador ", "Installer not available": "Instalador não disponível", "Instance {0} responded, quitting...": "A instância {0} respondeu, a encerrar...", "Instant search": "<PERSON><PERSON><PERSON>", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Verificações de integridade ignoradas", "Integrity checks will not be performed during this operation": "Verificações de integridade não serão realizadas durante esta operação", "Interactive installation": "Instalação interativa ", "Interactive operation": "Operação interativa", "Interactive uninstall": "Desinstalação interativa ", "Interactive update": "Atualização interativa ", "Internet connection settings": "Definições de ligação à internet", "Is this package missing the icon?": "Falta o ícone deste pacote?", "Is your language missing or incomplete?": "O seu idioma está em falta ou incompleto?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Não é garantido que as credenciais providenciadas sejam guardadas de forma segura, será melhor não utilizar as credenciais da sua conta bancária", "It is recommended to restart UniGetUI after WinGet has been repaired": "É recomendado reiniciar o UniGetUI após o WinGet ter sido reparado", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Parece que o WinGet não está a funcionar corretamente. Quer tentar reparar o WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Parece que executou o UniGetUI como administrador, o que não é recomendado. Ainda pode usar o programa, mas é altamente recomendável não executar o UniGetUI com privilégios de administrador. Clique em \"{showDetails}\" para ver o porquê.", "Language": "Idioma", "Language, theme and other miscellaneous preferences": "Idioma, tema e outras preferências", "Last updated:": "Última atualização:", "Latest": "Último", "Latest Version": "Última Versão", "Latest Version:": "Última Versão:", "Latest details...": "Últimos de<PERSON>...", "Launching subprocess...": "A iniciar subprocesso...", "Leave empty for default": "Deixar vazio para padrão", "License": "Licença", "Licenses": "Licenças", "Light": "<PERSON><PERSON><PERSON>", "List": "Lista", "Live command-line output": "Output da linha de comandos em tempo real", "Live output": "Saída ao vivo", "Loading UI components...": "A carregar os componentes da interface de utilizador...", "Loading WingetUI...": "A carregar o UniGetUI...", "Loading packages": "A carregar pacotes", "Loading packages, please wait...": "A carregar pacotes, por favor aguarde...", "Loading...": "A carregar...", "Local": "Local", "Local PC": "Computador local", "Local backup advanced options": null, "Local machine": "Computador local", "Local package backup": null, "Locating {pm}...": "A localizar {pm}...", "Log in": "<PERSON><PERSON><PERSON>", "Log in failed: ": "Falhar ao iniciar sessão: ", "Log in to enable cloud backup": "Inicia sessão para ativar cópias de segurança na nuvem", "Log in with GitHub": "Iniciar sessão com o GitHub", "Log in with GitHub to enable cloud package backup.": "Inicia sessão com o GitHub para ativar cópias de segurança de pacotes na nuvem.", "Log level:": "Nível de registo:", "Log out": "<PERSON><PERSON><PERSON><PERSON>", "Log out failed: ": "Falha ao terminar sessão:", "Log out from GitHub": "Terminar sessão do GitHub", "Looking for packages...": "À procura de pacotes...", "Machine | Global": "Máquina | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "<PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "Gerir definições do UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Gerir o comportamento de inicialização automática do UniGetUI a partir da aplicação Deffinições do Windows", "Manage ignored packages": "<PERSON><PERSON><PERSON> paco<PERSON> ignorados", "Manage ignored updates": "<PERSON><PERSON><PERSON> atualizações ignoradas", "Manage shortcuts": "<PERSON><PERSON><PERSON>", "Manage telemetry settings": "<PERSON><PERSON><PERSON> definiç<PERSON>es de telemetria", "Manage {0} sources": "G<PERSON>r {0} fontes", "Manifest": "Manifesto", "Manifests": "Manifestos", "Manual scan": "Pesquisa manual", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Gestor de pacotes oficial da Microsoft. Cheio de pacotes conhecidos e verificados.<br>Contém: <b>Software geral, aplicações da Microsoft Store</b>", "Missing dependency": "Dependência em falta", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON>", "More details about the shared data and how it will be processed": "Mais detalhes acerca dos dados partilhados e de como serão processados", "More info": "Mais informaç<PERSON>", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOTA: Esta resolução de problemas pode ser desativada nas definições do UniGetUI, na secção WinGet", "Name": "Nome", "New": "Novo", "New Version": "Nova Versão", "New bundle": "Novo conjunto", "New version": "Nova versão", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Não", "No applicable installer was found for the package {0}": "Nenhum instalador aplicável foi encontrado para o pacote {0}", "No dependencies specified": null, "No new shortcuts were found during the scan.": "<PERSON>ão foram encontrados novos atalhos durante a pesquisa.", "No packages found": "Nenhum pacote encontrado", "No packages found matching the input criteria": "Não foram encontrados pacotes que correspondam aos critérios de pesquisa ", "No packages have been added yet": "Nenhum pacote foi adicionado ainda", "No packages selected": "Nenhum pacote sele<PERSON>ado", "No packages were found": "Nenhum pacote encontrado", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Nenhuma informação pessoal é coletada nem enviada e, os dados coletados são anonimizados, de modo a que não possam ser rastreados até si.", "No results were found matching the input criteria": "Nenhum resultado encontrado correspondente aos critérios de pesquisa", "No sources found": "Fontes não encontradas", "No sources were found": "Fontes não encontradas", "No updates are available": "Não há atualizações disponíveis", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Gestor de pacotes do Node JS. Cheio de bibliotecas e outros utilitários no mundo Javascript.<br>Contém: <b>Bibliotecas Javascript do Node e outros utilitários relacionados</b>", "Not available": "Não disponível", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Não encontrado", "Not right now": "<PERSON><PERSON><PERSON> n<PERSON>", "Notes:": "Observações:", "Notification preferences": "Preferências de notificações", "Notification tray options": "Opções da área de notificações", "Notification types": "Tipos de notificação", "NuPkg (zipped manifest)": "NuPkg (manifesto comprimido)", "OK": "OK", "Ok": "Ok", "Open": "Abrir", "Open GitHub": "<PERSON><PERSON><PERSON>", "Open UniGetUI": "Abrir UniGetUI", "Open UniGetUI security settings": null, "Open WingetUI": "Abrir UniGetUI", "Open backup location": "Abrir pasta da cópia de segurança", "Open existing bundle": "Abrir conjunto existente", "Open install location": "Abrir local de instalação", "Open the welcome wizard": "<PERSON><PERSON>r o assistente de boas-vindas", "Operation canceled by user": "Operação cancelada pelo utilizador", "Operation cancelled": "Operação cancelada", "Operation history": "Histórico de operações ", "Operation in progress": "Operação em andamento", "Operation on queue (position {0})...": "Operação em fila de espera (posição {0})", "Operation profile:": "Perfil da operação:", "Options saved": "Opções salvas", "Order by:": "Ordenar por:", "Other": "Outro(a)", "Other settings": "Outras definições", "Package": "<PERSON><PERSON>", "Package Bundles": "Conjunto de paco<PERSON>", "Package ID": "ID do Pacote", "Package Manager": "<PERSON><PERSON><PERSON> de paco<PERSON>", "Package Manager logs": "Logs do gestor de pacotes", "Package Managers": "Gestores de pacotes", "Package Name": "Nome do Pacote", "Package backup": "Cópia de segurança dos pacotes", "Package backup settings": null, "Package bundle": "Conjunto de paco<PERSON>", "Package details": "Detalhes do pacote", "Package lists": "Listas de pacotes", "Package management made easy": "Gestão de pacotes de forma fácil", "Package manager": "<PERSON><PERSON><PERSON> de paco<PERSON>", "Package manager preferences": "Preferências do gestor de pacotes", "Package managers": "Gestores de pacotes", "Package not found": "Pacote não encontrado", "Package operation preferences": "Preferências das operações de pacotes", "Package update preferences": "Preferências da atualização de pacotes", "Package {name} from {manager}": "<PERSON><PERSON> {name} de {manager}", "Package's default": "Padrão do pacote", "Packages": "<PERSON><PERSON>", "Packages found: {0}": "Pacotes encontrados: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON>", "Password": "Palavra-passe", "Paste a valid URL to the database": "Cole um URL válido na base de dados", "Pause updates for": "Pausar atualizações por", "Perform a backup now": "Fazer cópia de segurança agora", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "A fazer cópia de segurança, por favor aguarde...", "Periodically perform a backup of the installed packages": "Fazer cópia de segurança periódica dos pacotes instalados", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "Verifique as opções de instalação para este pacote e tente novamente", "Please click on \"Continue\" to continue": "Por favor clique em \"Continuar\" para continuar", "Please enter at least 3 characters": "Por favor introduza pelo menos 3 caracteres", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Note que alguns pacotes podem não ser instaláveis devido aos gestores de pacotes ativos neste sistema.", "Please note that not all package managers may fully support this feature": "Por favor tenha em conta que nem todos os gestores de pacotes conseguem suportar totalmente esta funcionalidade ", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Note que pacotes de certas origens podem não ser exportáveis. Eles estão escurecidos e não serão exportados.", "Please run UniGetUI as a regular user and try again.": "Execute o UniGetUI como um utilizador padrão e tente novamente.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "<PERSON>r fovar, verifique o output da linha de comando ou o Histórico de Operações para obter mais informações sobre o problema.", "Please select how you want to configure WingetUI": "Por favor, selecione como deseja configurar o UniGetUI", "Please try again later": "Por favor tente novamente mais tarde", "Please type at least two characters": "Por favor, escreva pelo menos dois caracteres", "Please wait": "Por favor aguarde", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Por favor aguarde enquanto {0} está a ser instalado. Pode aparecer uma janela preta (ou azul). Por favor aguarde até que esta feche.", "Please wait...": "Por favor, aguarde...", "Portable": "<PERSON><PERSON><PERSON>", "Portable mode": "<PERSON><PERSON>", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Gestor de pacotes do PowerShell. Encontrar bibliotecas e scripts para expandir os recursos do PowerShell.<br>Contém: <b><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "Pré-lançamento", "Preparing packages, please wait...": "A preparar pacotes, por favor aguarde...", "Proceed at your own risk.": "Prossiga por sua conta e risco.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "URL do proxy", "Proxy compatibility table": "Tabela de compatibilidade de proxy", "Proxy settings": "Definições de proxy", "Proxy settings, etc.": "Definições de proxy, etc.", "Publication date:": "Data de publicação:", "Publisher": "Publicador", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Gestor de bibliotecas do Python. Cheio de bibliotecas Python e outros utilitários relacionados com o Python.<br>Contém: <b>Bibliotecas Python e utilitários relacionados</b>", "Quit": "<PERSON><PERSON>", "Quit WingetUI": "Sair do UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "Reins<PERSON>ar", "Reinstall package": "Reinstalando paco<PERSON>", "Related settings": "Definições relacionadas", "Release notes": "Notas de lançamento", "Release notes URL": "URL das Notas de lançamento", "Release notes URL:": "URL das notas da versão:", "Release notes:": "Notas da versão:", "Reload": "<PERSON><PERSON><PERSON><PERSON>", "Reload log": "Recarregar log", "Removal failed": "Falha na Remoção", "Removal succeeded": "Remoção bem-sucedida", "Remove from list": "Remover da lista", "Remove permanent data": "Remover dados permanentes", "Remove selection from bundle": "Remover seleção do conjunto", "Remove successful installs/uninstalls/updates from the installation list": "Remover instalações/desinstalações/atualizações bem-sucedidas da lista de instalação", "Removing source {source}": "A remover a fonte {source}", "Removing source {source} from {manager}": "Remover fonte {source} de {manager}", "Repair UniGetUI": null, "Repair WinGet": "<PERSON><PERSON><PERSON>", "Report an issue or submit a feature request": "Reportar um problema ou submeter um pedido de um recurso", "Repository": "Repositório", "Reset": "Repor", "Reset Scoop's global app cache": "Repor a cache global de aplicações do Scoop", "Reset UniGetUI": "Repôr UniGetUI", "Reset WinGet": "<PERSON><PERSON><PERSON>", "Reset Winget sources (might help if no packages are listed)": "Repor as font<PERSON> do <PERSON>et (pode ajudar se nenhum pacote estiver listado)", "Reset WingetUI": "Repor UniGetUI", "Reset WingetUI and its preferences": "Repor o UniGetUI e as suas preferências", "Reset WingetUI icon and screenshot cache": "Repor a cache de ícones e de capturas de ecrã do UniGetUI", "Reset list": "<PERSON><PERSON><PERSON> lista", "Resetting Winget sources - WingetUI": "A repor as fontes do Winget - UniGetUI", "Restart": "Reiniciar", "Restart UniGetUI": "Reiniciar o UniGetUI", "Restart WingetUI": "Reiniciar UniGetUI", "Restart WingetUI to fully apply changes": "Reinicia o UniGetUI para aplicar as alterações", "Restart later": "Reiniciar mais tarde", "Restart now": "Reiniciar agora", "Restart required": "É necessário reiniciar", "Restart your PC to finish installation": "Reinicie o seu computador para concluir a instalação ", "Restart your computer to finish the installation": "Reinicie o computador para concluir a instalação ", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Nova tentativa", "Retry as administrator": "Voltar a tentar como administrador", "Retry failed operations": "Voltar a tentar as operações que falharam", "Retry interactively": "Voltar a tentar de forma interativa", "Retry skipping integrity checks": "Voltar a tentar, saltando as verificações de integridade", "Retrying, please wait...": "A tentar novamente, por favor aguarde...", "Return to top": "Voltar ao topo", "Run": "Executar", "Run as admin": "Executar como administrador", "Run cleanup and clear cache": "Executar limpeza e limpar cache", "Run last": "Executar no fim", "Run next": "Executar a seguir", "Run now": "Executar agora", "Running the installer...": "A executar o instalador...", "Running the uninstaller...": "A executar o desinstalador...", "Running the updater...": "A executar o atualizador...", "Save": "Guardar", "Save File": "<PERSON><PERSON>", "Save and close": "<PERSON>var e fechar", "Save as": "Guardar como", "Save bundle as": "<PERSON><PERSON> conjunto como", "Save now": "<PERSON><PERSON> agora", "Saving packages, please wait...": "A salvar pacotes, por favor aguarde...", "Scoop Installer - WingetUI": "Instalador Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Desinstalador Scoop - UniGetUI", "Scoop package": "Pacote do Scoop", "Search": "Procurar", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Pesquisar software de desktop, avisar-me quando houver atualizações disponíveis e não fazer coisas de nerd. Não quero que o UniGetUI seja complicado, só quero uma <b>loja de software</b> simples", "Search for packages": "<PERSON><PERSON><PERSON><PERSON> paco<PERSON>", "Search for packages to start": "Pesquisar por pacotes para iniciar", "Search mode": "<PERSON>do de pesquisa", "Search on available updates": "Pesquisar nas atualizações disponíveis", "Search on your software": "Pesquisar nos seus programas", "Searching for installed packages...": "A procurar pacotes instalados...", "Searching for packages...": "A procurar pacotes...", "Searching for updates...": "A procurar atualizações...", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Seleciona \"{item}\" para adicionar teu bucket personalizado", "Select a folder": "Seleccionar uma pasta", "Select all": "Selecionar todos", "Select all packages": "Selecionar todos os pacotes", "Select backup": "Selecionar cópia de segurança", "Select only <b>if you know what you are doing</b>.": "Selecionar apenas <b>se souber o que está a fazer</b>.", "Select package file": "Selecionar ficheiro de pacote", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "Selecionar a fonte que deseja adicionar:", "Select upgradable packages by default": "Selecione os pacotes atualizáveis por defeito", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Selecionar quais os <b>gestores de pacotes</b> a utilizar ({0}), configurar como os pacotes são instalados, gerir como os direitos de administrador são tratados, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake enviado. A esperar resposta da instância... ({0}%)", "Set a custom backup file name": "Definir nome personalizado do ficheiro de cópia de segurança", "Set custom backup file name": "Definir nome personalizado do ficheiro de cópia de segurança", "Settings": "Definições", "Share": "Partilhar", "Share WingetUI": "Partilhar UniGetUI", "Share anonymous usage data": "Partilhar dados de utilização anónimos", "Share this package": "Partilhar este pacote", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Mostrar UniGetUI na bandeja do sistema", "Show UniGetUI's version and build number on the titlebar.": "Mostrar a versão do UniGetUI na barra de título", "Show WingetUI": "Mostrar o UniGetUI", "Show a notification when an installation fails": "Mostrar uma notificação quando uma instalação falhar", "Show a notification when an installation finishes successfully": "Mostrar uma notificação quando uma instalação for concluída com sucesso", "Show a notification when an operation fails": "Mostrar uma notificação quando uma operação falha", "Show a notification when an operation finishes successfully": "Mostrar uma notificação quando uma operação é completada com sucesso", "Show a notification when there are available updates": "Mostrar uma notificação quando houver atualizações disponíveis", "Show a silent notification when an operation is running": "Mostrar uma notificação silenciosa quando uma operação está a decorrer", "Show details": "<PERSON><PERSON> de<PERSON>", "Show in explorer": "Mostrar no explorador", "Show info about the package on the Updates tab": "Mostrar informações sobre o pacote no separador Atualizações", "Show missing translation strings": "Mostrar texto ainda não traduzido", "Show notifications on different events": "Mostrar notificações sobre eventos diferentes", "Show package details": "Mostrar detalhes do pacote", "Show package icons on package lists": "Mostrar ícones de pacotes em listas de pacotes", "Show similar packages": "Mostrar pacotes similares", "Show the live output": "Mostrar o output em tempo real", "Size": "<PERSON><PERSON><PERSON>", "Skip": "<PERSON><PERSON><PERSON>", "Skip hash check": "Ignorar verificação de hash", "Skip hash checks": "Ignorar verificações de hash", "Skip integrity checks": "Ignorar verificações de integridade", "Skip minor updates for this package": "Ignorar atualizações menores para este pacote", "Skip the hash check when installing the selected packages": "Ignorar a verificação de hash ao instalar os pacotes selecionados", "Skip the hash check when updating the selected packages": "Ignorar a verificação de hash ao atualizar os pacotes selecionados", "Skip this version": "Ignorar esta versão", "Software Updates": "Atualizações de Software", "Something went wrong": "Alguma coisa correu mal", "Something went wrong while launching the updater.": "Algo correu mal ao iniciar o atualizador.", "Source": "Fonte", "Source URL:": "URL da fonte:", "Source added successfully": "Fonte adicionada com sucesso", "Source addition failed": "Adi<PERSON> da fonte falhou", "Source name:": "<PERSON>me da fonte", "Source removal failed": "Remoção da fonte falhou", "Source removed successfully": "Fonte removida com sucesso", "Source:": "Fonte:", "Sources": "<PERSON><PERSON><PERSON>", "Start": "Iniciar", "Starting daemons...": "A iniciar daemons...", "Starting operation...": "A iniciar a operação...", "Startup options": "Opções de arranque", "Status": "Estado", "Stuck here? Skip initialization": "Preso aqui? Saltar a inicialização", "Success!": null, "Suport the developer": "A<PERSON><PERSON> o programador", "Support me": "<PERSON><PERSON><PERSON>-me", "Support the developer": "Ajude o programador", "Systems are now ready to go!": "Os sistemas estão prontos a funcionar!", "Telemetry": "Telemetria", "Text": "Texto", "Text file": "Ficheiro de texto", "Thank you ❤": "<PERSON><PERSON><PERSON> ❤", "Thank you 😉": "<PERSON><PERSON><PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "O gestor de pacotes Rust. <br>Contém: <b>Bibliotecas Rust e programas escritos em Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "A cópia de segurança NÃO incluirá nenhum ficheiro binário nem dados guardados de nenhum programa.", "The backup will be performed after login.": "A cópia de segurança será realizada após o login.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "A cópia de segurança incluirá a lista completa dos pacotes instalados e as suas opções de instalação. Atualizações ignoradas e versões ignoradas também serão guardadas.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "O pacote que está a tentar carregar parece ser inválido. Verifique o ficheiro e tente novamente.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "A checksum do instalador não coincide com o valor esperado e a autenticidade do instalador não pode ser verificada. Se confiares no editor, {0} o pacote ignorará a verificação de hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "O gestor de pacotes mais conhecido para Windows. Irá encontrar de tudo lá. <br>Contém: <b>Software Geral</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "O atual conjunto não tem pacotes. Adicione alguns pacotes para começar", "The executable file for {0} was not found": "O ficheiro executável para {0} não foi encontrado", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "<PERSON><PERSON> se<PERSON><PERSON> pacotes serão exportados para um ficheiro JSON. Nenhum dado do utilizador ou ficheiro serão guardados.", "The following packages are going to be installed on your system.": "<PERSON><PERSON> se<PERSON><PERSON> pacotes serão instalados no sistema.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "As seguintes configurações serão aplicadas sempre que este pacote for instalado, atualizado ou removido.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "As configurações a seguir serão aplicadas sempre que este pacote for instalado, actualizado ou removido. Elas serão salvas automaticamente.", "The icons and screenshots are maintained by users like you!": "Os ícones e as capturas de ecrã são mantidos por utilizadores como você!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "Não foi possível verificar a autenticidade do instalador.", "The installer has an invalid checksum": "O instalador tem um checksum inválido", "The installer hash does not match the expected value.": "O hash do instalador não corresponde com o valor expectável.", "The local icon cache currently takes {0} MB": "A cache de ícones locais ocupa atualmente {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "O objectivo principal deste projecto é criar uma interface de utilizador intuitiva para os gestores de pacotes CLI mais comuns para o Windows, como o Winget e o Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "O pacote \"{0}\" não foi encontrado no gestor de pacotes \"{1}\"", "The package bundle could not be created due to an error.": "O conjunto de pacotes não pôde ser criado devido a um erro.", "The package bundle is not valid": "O conjunto de pacotes não é válido", "The package manager \"{0}\" is disabled": "O gestor de pacotes \"{0}\" está desativado", "The package manager \"{0}\" was not found": "O gestor de pacotes \"{0}\" não foi encontrado", "The package {0} from {1} was not found.": "O pacote {0} de {1} não foi encontrado.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "<PERSON>s pacotes listados aqui não serão tidos em conta na verificação de atualizações. Clique duas vezes neles ou clique no botão à direita para parar de ignorar suas atualizações.", "The selected packages have been blacklisted": "Os pacotes selecionados foram colocados na lista negra", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "O tamanho da cópia de segurança é estimado em menos de 1 MB.", "The source {source} was added to {manager} successfully": "A fonte {source} foi adicionada a {manager} com sucesso", "The source {source} was removed from {manager} successfully": "A fonte {source} foi removida de {manager} com sucesso", "The system tray icon must be enabled in order for notifications to work": "O ícone da bandeja do sistema deve estar ativo para que as notificações funcionem", "The update process has been aborted.": "O processo de atualização foi interrompido.", "The update process will start after closing UniGetUI": "A atualização será iniciada após fechar o UniGetUI", "The update will be installed upon closing WingetUI": "A atualização será instalada depois de sair do UniGetUI", "The update will not continue.": "A atualização não irá continuar.", "The user has canceled {0}, that was a requirement for {1} to be run": "O utilizador cancelou {0}, que era pré-requisito para executar {1}", "There are no new UniGetUI versions to be installed": "Não existem novas versões do UniGetUI para instalar", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Existem operações em andamento. Sair do UniGetUI pode fazer com que elas falhem. Quer continuar?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Há alguns vídeos ótimos no YouTube que mostram o UniGetUI e os seus recursos. Pode aprender truques e dicas úteis!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Há dois motivos principais para não executar o UniGetUI como administrador: O primeiro é que o gestor de pacotes Scoop pode ter problemas com alguns comandos quando executado com direitos de administrador. O segundo é que executar o UniGetUI como administrador significa que qualquer pacote que descarregue será executado como administrador (o que não é seguro). Lembre-se de que, se necessitar de instalar um pacote específico como administrador, pode sempre clicar com o botão direito do rato no item -> Instalar/Atualizar/Desinstalar como administrador.", "There is an error with the configuration of the package manager \"{0}\"": "Existe um erro na configuração do gestor de pacotes \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Há uma instalação em andamento. Se fechares o UniGetUI, a instalação poderá falhar e ter resultados inesperados. Ainda desejas sair do UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "São os programas encarregados de instalar, atualizar e remover pacotes.", "Third-party licenses": "Licenças de terceiros", "This could represent a <b>security risk</b>.": "<PERSON><PERSON> pode representar um <b>risco de segurança</b>.", "This is not recommended.": "Isto não é recomendado.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Isto provavelmente deve-se ao facto de que o pacote que recebeu foi removido ou publicado num gestor de pacotes que não foi ativado. O ID recebido é {0}", "This is the <b>default choice</b>.": "Esta é a <b>es<PERSON><PERSON><PERSON> pad<PERSON></b>.", "This may help if WinGet packages are not shown": "<PERSON><PERSON> pode ajudar se os pacotes do Winget não forem mostrados", "This may help if no packages are listed": "<PERSON><PERSON> pode ajudar se nenhum pacote for listado", "This may take a minute or two": "<PERSON>to pode demorar um minuto ou dois", "This operation is running interactively.": "Esta operação está a correr de forma interativa.", "This operation is running with administrator privileges.": "Esta operação está a correr com privilégios de administrador.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Este pacote pode ser actualizado", "This package can be updated to version {0}": "Este pacote pode ser atualizado para a versão {0}", "This package can be upgraded to version {0}": "Este pacote pode ser atualizado para a versão {0}", "This package cannot be installed from an elevated context.": "Este pacote não pode ser instalado a partir de um comando elevado.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Este pacote não possui capturas de ecrã ou o ícone está em falta? Contribua para o UniGetUI adicionando os ícones e capturas de ecrã ausentes à nossa base de dados pública e aberta.", "This package is already installed": "Este pacote já está instalado", "This package is being processed": "Este pacote está a ser processado", "This package is not available": "Este pacote não está disponível", "This package is on the queue": "Este pacote está na fila", "This process is running with administrator privileges": "Este processo está a ser executado com privilégios de administrador", "This project has no connection with the official {0} project — it's completely unofficial.": "Este projeto não tem nenhuma relação com o projeto oficial do {0} — é totalmente não oficial.", "This setting is disabled": "Esta definição está desativada", "This wizard will help you configure and customize WingetUI!": "Este assistente ajudá-lo-á a configurar e personalizar o UniGetUI!", "Toggle search filters pane": "Mostrar/esconder painel de filtros de pesquisa", "Translators": "<PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Tenta matar os processos que se recusam a fechar após pedido", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Escreva aqui o nome e a URL da fonte que deseja adicionar, separados por um espaço.", "Unable to find package": "Não foi possível encontrar o pacote", "Unable to load informarion": "Não foi possí<PERSON> carre<PERSON> as informações", "UniGetUI collects anonymous usage data in order to improve the user experience.": "O UniGetUI coleta dados de uso anónimos para melhorar a experiência do utilizador.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "O UniGetUI coleta dados de utilização anónimos com o único propósito de compreender e melhor a experiência do utilizador.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "O UniGetUI detetou um novo atalho no ambiente de trabalho que pode ser eliminado automaticamente.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "O UniGetUI detetou os seguintes atalhos na área de trabalho que podem ser removidos automaticamente em futuras atualizações", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "O UniGetUI detetou {0} novos atalhos na área de trabalho que podem ser eliminados automaticamente.", "UniGetUI is being updated...": "O UniGetUI está a ser atualizado...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "O UniGetUI não está relacionado com nenhum gestor de pacotes compatível. O UniGetUI é um projeto independente.", "UniGetUI on the background and system tray": "Manter o UniGetUI no fundo e na bandeja do sistema", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "O UniGetUI requer {0} para operar, mas não foi encontrado no seu sistema.", "UniGetUI startup page:": "Página inicial do UniGetUI:", "UniGetUI updater": "Atualizador do UniGetUI", "UniGetUI version {0} is being downloaded.": "A versão {0} do UniGetUI está a ser descarregada.", "UniGetUI {0} is ready to be installed.": "O UniGetUI {0} está pronto para ser instalado.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Desinstalar o Scoop (e os seus pacotes)", "Uninstall and more": "Desinstalar e mais", "Uninstall and remove data": "Desinstalar e remover dados", "Uninstall as administrator": "Desinstalar como administrador", "Uninstall canceled by the user!": "Desinstalação cancelada pelo utilizador!", "Uninstall failed": "Desinstalação falhou", "Uninstall options": "Opções de desinstalação", "Uninstall package": "Desinstalar pacote", "Uninstall package, then reinstall it": "Desinstalar pacote e depois reinstalar", "Uninstall package, then update it": "Desinstalar pacote e depois atualizar", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Desinstalar selecionados", "Uninstall selection": null, "Uninstall succeeded": "Desinstalação bem-sucedida", "Uninstall the selected packages with administrator privileges": "Desinstalar os pacotes selecionados com privilégios de administrador", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Pacotes desinstaláveis com a fonte listada como \"{0}\" não foram publicados em nenhum gestor de pacotes, não há informações disponíveis para mostrar sobre eles.", "Unknown": "Desconhecido", "Unknown size": "<PERSON><PERSON><PERSON>", "Unset or unknown": "Não selecionado ou desconhecido", "Up to date": "Atualizado", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Atualizar o UniGetUI automaticamente", "Update all": "<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "Update and more": "Atualizar e mais", "Update as administrator": "Atualizar como administrador", "Update check frequency, automatically install updates, etc.": "Frequência de busca de atualizações, instalar atualizações automaticamente, etc.", "Update checking": null, "Update date": "Data de atualização\n", "Update failed": "Atualização falhou", "Update found!": "Atualização encontrada!", "Update now": "Atual<PERSON>r agora", "Update options": "Opções de atualização", "Update package indexes on launch": "Atualizar os indíces dos pacotes ao iniciar", "Update packages automatically": "At<PERSON><PERSON>r pacotes automaticamente", "Update selected packages": "Atualizar pacotes selecionados", "Update selected packages with administrator privileges": "Atualizar os pacotes selecionados com privilégios de administrador", "Update selection": null, "Update succeeded": "Atualização bem-sucedida", "Update to version {0}": "Atualizar para a versão {0}", "Update to {0} available": "Atualização para {0} disponível", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Atualizar os portfiles Git do vcpkg automaticamente (requer Git instalado)", "Updates": "Atualizações", "Updates available!": "Atualizações disponíveis!", "Updates for this package are ignored": "As atualizações para este pacote estão a ser ignoradas", "Updates found!": "Atualizações encontradas!", "Updates preferences": "Preferências de atualização", "Updating WingetUI": "A atualizar o UniGetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Utilize os CMDlets originais incluídos no WinGet em vez dos CMDlets do PowerShell", "Use a custom icon and screenshot database URL": "Utilizar uma base de dados de ícones e capturas de ecrã personalizada", "Use bundled WinGet instead of PowerShell CMDlets": "Utilize os CMDlets incluídos no WinGet em vez dos CMDlets do PowerShell", "Use bundled WinGet instead of system WinGet": "Utilizar o WinGet da aplicação UnigetUI em vez do WinGet do sistema", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Utilizar o GSudo instalado em vez do incluído (é necessário reiniciar)", "Use system Chocolatey": "Utilizar do Chocolatey do sistema", "Use system Chocolatey (Needs a restart)": "Utilizar o Chocolatey do sistema (é necessário reiniciar)", "Use system Winget (Needs a restart)": "Utilizar o Winget do sistema (é necessário reiniciar)", "Use system Winget (System language must be set to english)": "Utilize o Winget do sistema (o idioma do sistema deve estar definido como Inglês)", "Use the WinGet COM API to fetch packages": "Use a API COM do WinGet para pesquisar pacotes", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Utilizar o módulo PowerShell WinGet da aplicação UnigetUI em vez da API COM do WinGet do sistema", "Useful links": "<PERSON><PERSON>", "User": "Utilizador", "User interface preferences": "Preferências da interface do utilizador", "User | Local": "Utilizador | Local", "Username": "Nome de utilizador", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "A utilização do UniGetUI implica a aceitação da Licença GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "A utilização do UniGetUI implica a aceitação da Licença MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "A raiz do vcpkg não foi encontrada. Defina a variável de ambiente %VCPKG_ROOT% ou defina-a nas definições do UniGetUI", "Vcpkg was not found on your system.": "Vcpkg não foi encontrado no seu sistema.", "Verbose": "<PERSON><PERSON><PERSON><PERSON>", "Version": "Vers<PERSON>", "Version to install:": "Versão para instalar:", "Version:": "Versão:", "View GitHub Profile": "Ver perfil GitHub", "View WingetUI on GitHub": "Visualizar o UniGetUI no GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Veja o código-fonte do UniGetUI. A partir daí, pode reportar bugs ou sugerir funcionalidades, ou mesmo contribuir directamente para o projeto do UniGetUI", "View mode:": "Modo de visualização:", "View on UniGetUI": "Ver no UniGetUI", "View page on browser": "Ver página no Browser", "View {0} logs": "Ver {0} registos", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Aguardar que o dispositivo esteja ligado à internet antes de tentar executar tarefas que requerem conexão à internet.", "Waiting for other installations to finish...": "A aguardar a conclusão das outras instalações...", "Waiting for {0} to complete...": "Aguardando que {0} termine...", "Warning": "Atenção", "Warning!": "Aviso!", "We are checking for updates.": "Estamos a verificar atualizações.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Não foi possível carregar informações detalhadas sobre este pacote, porque ele não foi encontrado em nenhuma das suas fontes de pacotes", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Não foi possível carregar informações detalhadas sobre este pacote, porque ele não foi instalado a partir de um gestor de pacotes disponível.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Não foi possível {action} o {package}. Por favor, tenta novamente mais tarde. Clica em \"{showDetails}\" para obter os logs do instalador.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Não foi possível {action} {package}. Por favor, tenta novamente mais tarde. Clica em \"{showDetails}\" para obter os logs do desinstalador.", "We couldn't find any package": "Não foi encontrado nenhum pacote", "Welcome to WingetUI": "Bem-vindo ao UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Quando novos atalhos são detetados, apagá-los automaticamente em vez de mostrar este diálogo.", "Which backup do you want to open?": "Que cópia de segurança quer abrir?", "Which package managers do you want to use?": "Quais gestores de pacotes deseja utilizar?", "Which source do you want to add?": "Qual fonte deseja adicionar?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Embora o Winget possa ser usado no WingetUI (nome antigo), o UniGetUI pode ser usado com outros gestores de pacotes, o que pode ser confuso. No passado, o WingetUI (nome antigo) foi projetado para funcionar apenas com o Winget, mas isso já não é verdade e, portanto, o nome WingetUI não representa o que este projeto pretende ser.", "WinGet could not be repaired": "O WinGet não pôde ser reparado", "WinGet malfunction detected": "Detetado erro no funcionamento do Winget", "WinGet was repaired successfully": "O WinGet foi reparado com sucesso", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Está tudo atualizado", "WingetUI - {0} updates are available": "UniGetUI - {0} atualizações disponíveis", "WingetUI - {0} {1}": "UniGetUI - {1} de {0}", "WingetUI Homepage": "Página do UniGetUI", "WingetUI Homepage - Share this link!": "Página do UniGetUI - Partilha este link", "WingetUI License": "Licença UniGetUI", "WingetUI Log": "Log do UniGetUI", "WingetUI Repository": "Repositório UniGetUI", "WingetUI Settings": "Definições", "WingetUI Settings File": "Ficheiro de definições do UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI usa as seguintes bibliotecas. Sem elas, o UniGetUI não teria sido possível.", "WingetUI Version {0}": "Versão UniGetUI {0}", "WingetUI autostart behaviour, application launch settings": "Comportamento de arranque automático do UniGetUI, definições de arranque da aplicação", "WingetUI can check if your software has available updates, and install them automatically if you want to": "O  UniGetUI pode verificar se o teu software tem atualizações disponíveis, e instalá-las automaticamente.", "WingetUI display language:": "Idioma de exibição do UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "O UniGetUI foi executado como administrador, o que não é recomendado. Ao executar o UniGetUI como administrador, TODAS as operações iniciadas a partir do UniGetUI terão privilégios de administrador. Ainda pode usar o programa, mas é altamente recomendável não executar o UniGetUI com privilégios de administrador.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "O UniGetUI foi traduzido para mais de 40 idiomas graças a tradutores voluntários. Obrigado 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "O UniGetUI não foi traduzido por serviços de tradução automáticos! Os seguintes utilizadores foram responsáveis pelas traduções:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "O UniGetUI é uma aplicação que facilita a gestão do seu software, fornecendo uma interface gráfica completa para os seus gestores de pacotes de linha de comandos.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI será renomeado para enfatizar a diferença entre UniGetUI (a interface que está a usar agora) e Winget (um gestor de pacotes desenvolvido pela Microsoft com a qual não tenho relação)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI está a ser atualizado. Quando terminar, o UniGetUI será reiniciado automaticamente", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "O UniGetUI é gratuito e será gratuito para sempre. Sem an<PERSON>, sem necessidade de cartão de crédito, sem versão premium. 100% grátis, para sempre.", "WingetUI log": "Log do UniGetUI", "WingetUI tray application preferences": "Preferências do ícone do UniGetUI na bandeja do sistema", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI usa as seguintes bibliotecas. Sem elas, o UniGetUI não teria sido possível.", "WingetUI version {0} is being downloaded.": "A versão {0} do UniGetUI está a ser descarregada.", "WingetUI will become {newname} soon!": "O WingetUI em breve será {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "O UniGetUI não procurará atualizações periodicamente. A verificação ocorrerá no arranque da aplicação, mas não será notificado.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "O UniGetUI mostrará um prompt do UAC sempre que um pacote exigir elevação para ser instalado.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "O WingetUI em breve será renomeado para {newname}. Isto não representará nenhuma alteração na aplicação. Eu (o programador) continuarei o desenvolvimento deste projeto como atualmente, mas com um nome diferente.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "O UniGetUI não teria sido possível sem a ajuda dos nossos queridos colaboradores. Espreite os seus perfis no GitHub, o UniGetUI não seria possível sem eles!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "O UniGetUI não teria sido possível sem a ajuda dos seus colaboradores. Obrigado a todos 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} está pronto para ser instalado", "Write here the process names here, separated by commas (,)": null, "Yes": "<PERSON>m", "You are logged in as {0} (@{1})": "Tem sessão iniciada como {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Pode alterar este comportamento nas definições de segurança do UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "Tem atualmente a versão {0} instalada", "You have installed WingetUI Version {0}": "Instalou o UniGetUI versão {0}", "You may lose unsaved data": "Poderá perder dados não guardados", "You may need to install {pm} in order to use it with WingetUI.": "Pode ser necessário instalar {pm} para ser possível usá-lo com o UniGetUI.", "You may restart your computer later if you wish": "Pode reiniciar o computador mais tarde, se desejar", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Será solicitado apenas uma vez e os direitos de administrador serão concedidos aos pacotes que os solicitarem.", "You will be prompted only once, and every future installation will be elevated automatically.": "Será solicitado apenas uma vez, e todas as instalações futuras serão elevadas automaticamente.", "You will likely need to interact with the installer.": "Provavelmente terá de interagir com o instalador.", "[RAN AS ADMINISTRATOR]": "EXECUTADO COMO ADMINISTRADOR", "buy me a coffee": "comprar-me um café", "extracted": "<PERSON><PERSON><PERSON>", "feature": "funcionalidade", "formerly WingetUI": "anteriormente WingetUI", "homepage": "Página de Internet", "install": "instalar", "installation": "instalação", "installed": "instalado", "installing": "A instalar", "library": "biblioteca", "mandatory": "obrigatório", "option": "opção", "optional": "opcional", "uninstall": "desinstalar", "uninstallation": "desinstalação", "uninstalled": "desinstalado", "uninstalling": "a desinstalar", "update(noun)": "atualização", "update(verb)": "atual<PERSON>r", "updated": "atualizado", "updating": "a atualizar", "version {0}": "vers<PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} opções de instalação estão de momento bloqueadas porque {0} respeita as opções de instalação padrão.", "{0} Uninstallation": "Desinstalação do {0}", "{0} aborted": "{0} abor<PERSON>a", "{0} can be updated": "{0} pode ser atualizado", "{0} can be updated to version {1}": "{0} pode ser atualizado para a versão {1}", "{0} days": "{0} dias", "{0} desktop shortcuts created": "{0} atalhos de ambiente de trabalho criados", "{0} failed": "{0} f<PERSON><PERSON><PERSON>", "{0} has been installed successfully.": "{0} foi instalado com sucesso.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} foi instalado com sucesso. É recomendável reiniciar o UniGetUI para terminar a instalação", "{0} has failed, that was a requirement for {1} to be run": "{0} f<PERSON><PERSON>, era um pré-requisito para executar {1}", "{0} homepage": "{0} p<PERSON><PERSON>a inicial", "{0} hours": "{0} horas", "{0} installation": "Instalação do {0}", "{0} installation options": "{0} opções de instalação", "{0} installer is being downloaded": "O instalador {0} está a ser descarregado", "{0} is being installed": "{0} está a ser instalado", "{0} is being uninstalled": "{0} está a ser desinstalado", "{0} is being updated": "{0} está a ser atualizado", "{0} is being updated to version {1}": "{0} está a ser atualizado para a versão {1}", "{0} is disabled": "O {0} está desativado", "{0} minutes": "{0} minutos", "{0} months": "{0} meses", "{0} packages are being updated": "{0} pacotes estão a ser atualizados", "{0} packages can be updated": "{0} paco<PERSON> podem ser atualizados", "{0} packages found": "{0} pacotes encontrados", "{0} packages were found": "{0} pacotes foram encontrados", "{0} packages were found, {1} of which match the specified filters.": "{0} paco<PERSON> foram encontrados, {1} dos quais correspondem aos filtros especificados", "{0} selected": null, "{0} settings": "Definições de {0}", "{0} status": "Status de {0}", "{0} succeeded": "{0} bem-su<PERSON><PERSON>", "{0} update": "Atualização do {0}", "{0} updates are available": "{0} atualizações disponíveis", "{0} was {1} successfully!": "{0} foi {1} com sucesso!", "{0} weeks": "{0} semanas", "{0} years": "{0} anos", "{0} {1} failed": "A {1} de {0} falhou", "{package} Installation": "{package} Installação", "{package} Uninstall": "{package} Desinstalar", "{package} Update": "{package} Atualizar", "{package} could not be installed": "{package} não pôde ser instalado.", "{package} could not be uninstalled": "{package} não pôde ser desinstalado.", "{package} could not be updated": "{package} não pôde ser atualizado.", "{package} installation failed": "{package} instalação falhou.", "{package} installer could not be downloaded": "Não foi possível descarregar o instalador do {package}", "{package} installer download": "Descarregar instalador do {package}", "{package} installer was downloaded successfully": "O instalador do {package} foi descarregado com sucesso", "{package} uninstall failed": "Desinstalação de {package}  falhou.", "{package} update failed": "Atualização de {package} falhou.", "{package} update failed. Click here for more details.": "Atualização de {package} falhou. Clique aqui para mais informações.", "{package} was installed successfully": "{package} foi instalado com sucesso", "{package} was uninstalled successfully": "{package} foi desinstalado com sucesso", "{package} was updated successfully": "{package} foi atualizado com sucesso", "{pcName} installed packages": "{pcName} pacotes instaldos", "{pm} could not be found": "{pm} não foi encontrado", "{pm} found: {state}": "{pm} encontrado: {state}", "{pm} is disabled": "{pm} está desativado", "{pm} is enabled and ready to go": "{pm} está ativo e pronto a funcionar", "{pm} package manager specific preferences": "Preferências específicas do gestor de pacotes {pm}", "{pm} preferences": "Preferências do {pm}", "{pm} version:": "{pm} versão:", "{pm} was not found!": "{pm} não foi encontrado!"}