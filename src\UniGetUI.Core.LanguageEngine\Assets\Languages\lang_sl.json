{"\"{0}\" is a local package and can't be shared": "\"{0}\" je lokalni paket in ga ni mogoče deliti", "\"{0}\" is a local package and does not have available details": "\"{0}\" je lokalni paket in nima na voljo podrobnosti", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" je lokal<PERSON> paket in ni združljiv s to funkcijo.", "(Last checked: {0})": "(Nazadnje preverjeno: {0})", "(Number {0} in the queue)": "(Številka {0} v čakalni vrsti)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@rumplin", "0 packages found": "0 najdenih paketov", "0 updates found": "0 najdenih posodobitev", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 dan", "1 hour": "1 uro", "1 month": "1 mesec", "1 package was found": "Najden 1 paket", "1 update is available": "Na voljo je 1 posodobitev", "1 week": "1 teden", "1 year": "1 leto", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Pomaknite se na stran \"{0}\" ali \"{1}\".", "2 - Warnings": "2 - <PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Poiščite pakete, ki jih želite dodati v sveženj, in izberite njihovo skrajno levo potrditveno polje.", "3 - Information (less)": "3 - <PERSON><PERSON><PERSON><PERSON><PERSON> (manj)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> paketi, ki jih želite dodati v sveženj, v orodni vrstici poiščite in kliknite možnost \"{0}\".", "4 - Information (more)": "4 - <PERSON><PERSON><PERSON><PERSON><PERSON> (več)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Vaši paketi bodo dodani v paket. Lahko nadaljujete z dodajanjem paketov ali izvozite sveženj.", "5 - information (debug)": "5 - Informacije (razhroščevanje)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Priljubljen upravitelj knjižnic C/C++. Polno knjižnic C/C++ in drugih pripomočkov, povezanih s C/C++<br>Vsebuje: <b>Knjižnice C/C++ in sorodni pripomočki</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Repoz<PERSON><PERSON><PERSON>, poln orodij in izvršljivih datotek, zasnovanih z mislijo na Microsoftov ekosistem .NET.<br>Vsebuje: <b>orod<PERSON> in skripte, povezane z .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "<PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON> orod<PERSON>, zasnovanih z mislijo na Microsoftov ekosistem .NET.<br>Vsebuje: <b><PERSON><PERSON><PERSON>, povezana z .NET</b>", "A restart is required": "Potreben je ponovni zagon", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "O nas", "About Qt6": "O Qt6", "About WingetUI": "O WingetUI", "About WingetUI version {0}": "O WingetUI verziji {0}", "About the dev": "O razvijalcih", "Accept": "<PERSON><PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "Akcija za dvojni klik paketa, skrij uspešne instalacije", "Add": "<PERSON><PERSON><PERSON>", "Add a source to {0}": "Dodajte vir v {0}", "Add a timestamp to the backup file names": "Dodajte časovni žig v imena datotek varnostne kopije", "Add a timestamp to the backup files": "Varnostnim datotekam dodajte časovni žig", "Add packages or open an existing bundle": "Dodajte pakete ali odprite obstoječi sveženj", "Add packages or open an existing package bundle": "Dodaj pakete ali odpri obstoječi sveženj paketov", "Add packages to bundle": "Dodajte pakete v sveženj", "Add packages to start": "Dodaj pakete za začetek", "Add selection to bundle": "Dodaj izbor v sveženj", "Add source": "<PERSON><PERSON>j vir", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Na seznam prezrtih posodobitev dodajte posodobitve, ki niso uspele z \"ni ustrezne posodobitve\".", "Adding source {source}": "<PERSON><PERSON><PERSON><PERSON> vira {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON><PERSON> vira {source} v {manager}", "Addition succeeded": "Dodajanje je uspelo", "Administrator privileges": "Skrbniške pravice", "Administrator privileges preferences": "Nastavitve skrbniških pravic", "Administrator rights": "Administratorske pravice", "Administrator rights and other dangerous settings": null, "Advanced options": "<PERSON><PERSON><PERSON><PERSON>", "All files": "<PERSON><PERSON> dato<PERSON>ke", "All versions": "Vse različice", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "<PERSON><PERSON><PERSON>, da se paketne operacije izvajajo paralelno", "Allow parallel installs (NOT RECOMMENDED)": "Dovoli vzporedne instalacije (NI PRIPOROČENO)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "<PERSON><PERSON><PERSON>, da se operacije {pm} izvajajo vzporedno", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Lahko pa tudi namestite {0} ta<PERSON>, da zaženete naslednji ukaz v pozivu Windows PowerShell:", "Always elevate {pm} installations by default": "Privzeto vedno dvigni namestitve {pm}", "Always run {pm} operations with administrator rights": "Operacije {pm} vedno izvajajte s skrbniškimi pravicami", "An error occurred": "<PERSON><PERSON><PERSON><PERSON> je do napake", "An error occurred when adding the source: ": "Pri dodajanju vira je prišlo do napake:", "An error occurred when attempting to show the package with Id {0}": "Pripetila se je napaka med prikazovanjem paketa z Id {0}", "An error occurred when checking for updates: ": "Pri preverjanju posodobitev je prišlo do napake:", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "Med obdelavo tega paketa je prišlo do napake", "An error occurred:": "Pripetila se je napaka:", "An interal error occurred. Please view the log for further details.": "P<PERSON>š<PERSON> je do notranje napake. Za več podrobnosti si oglejte dnevnik.", "An unexpected error occurred:": "<PERSON><PERSON><PERSON><PERSON> je do nepredvidene napake:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Med poskusom popravila programa WinGet je prišlo do nepričakovane težave. Poskusite znova pozneje", "An update was found!": "Na<PERSON><PERSON>a je bila poso<PERSON><PERSON>ev!", "Android Subsystem": "Podsistem Android", "Another source": "Drug vir", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Vse nove b<PERSON>, ustvarjene med namestitvijo ali posodobitvijo, bodo sa<PERSON>o <PERSON>, namesto da bi se prvič prikazalo potrditveno okno.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "<PERSON><PERSON>, ustvarjene ali spremenjene zunaj UniGetUI, bodo prezrte. Dodate jih lahko z gumbom {0}.", "Any unsaved changes will be lost": "Vse neshranjene spremembe bodo izgubljene", "App Name": "<PERSON><PERSON>", "Appearance": "<PERSON><PERSON>z", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> stran, i<PERSON><PERSON>, samodejno brisanje uspešnih namestitev", "Application theme:": "<PERSON>me aplikacije:", "Apply": null, "Architecture to install:": "Arhitektura za namestitev:", "Are these screenshots wron or blurry?": "Ali so zas<PERSON>ke slike napačne ali meglene?", "Are you really sure you want to enable this feature?": "<PERSON> ste re<PERSON> pre<PERSON>, da <PERSON><PERSON><PERSON> to funkcijo?", "Are you sure you want to create a new package bundle? ": "<PERSON>, da ž<PERSON>te ustvariti nov svež<PERSON>j paketov?", "Are you sure you want to delete all shortcuts?": "<PERSON>, da želite izbrisati vse bližnjice?", "Are you sure?": "<PERSON> ste prepri<PERSON>?", "Ascendant": "Naraščaj<PERSON>če", "Ask for administrator privileges once for each batch of operations": "Enkrat zahtevajte skrbniške pravice za vsak sklop operacij", "Ask for administrator rights when required": "Po potrebi zahtevajte skrbniške pravice", "Ask once or always for administrator rights, elevate installations by default": "Vprašaj enkrat ali vedno za skrbniške pravice, uporabi pri namestitvah privzeto", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Samo enkrat vprašaj za skrbniške pravice (ni priporočljivo)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Vprašajte za brisanje bližnjic na namizju, ustvarjenih med namestitvijo ali nadgradnjo.", "Attention required": "Potrebna pozornost", "Authenticate to the proxy with an user and a password": "Overite se pri posredniškem strežniku z uporabniškim imenom in geslom", "Author": "A<PERSON><PERSON>", "Automatic desktop shortcut remover": "Samodejni odstranjevalec bližnjic na namizju", "Automatically save a list of all your installed packages to easily restore them.": "Samodejno shranite seznam vseh <PERSON><PERSON><PERSON><PERSON><PERSON>, da jih prep<PERSON>to obnov<PERSON>.", "Automatically save a list of your installed packages on your computer.": "Samodejno shranite seznam svojih nameščenih paketov v svoj računalnik.", "Autostart WingetUI in the notifications area": "Samodejni zagon WingetUI v območje za obvestila", "Available Updates": "Razpoložljive posodobitve", "Available updates: {0}": "Razpoložljive posodobitve: {0}", "Available updates: {0}, not finished yet...": "Razpoložljive posodobitve: {0}, še nedo<PERSON>...", "Backing up packages to GitHub Gist...": null, "Backup": "Varnostna kopija", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Varnostno kopirajte nameščene pakete", "Backup location": "Lokacija varnostne kopije", "Become a contributor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Become a translator": "Postani<PERSON> preva<PERSON><PERSON>", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Beta funkcije in druge možnosti, ki se jih ne bi smeli dotikati", "Both": "Oboje", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "<PERSON><PERSON><PERSON> pa so še druge stvari, ki jih lahko naredite, da se o WingetUI naučite še več:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Če izklopite upravitelja paketov, ne boste mogli več videti ali posodobiti njegovih paketov.", "Cache administrator rights and elevate installers by default": "Privzeto predpomni skrbniške pravice in za namestitvene programe", "Cache administrator rights, but elevate installers only when required": "Predpomni skrbniške pravice, vendar povišajte namestitvene programe le, ko je to potrebno", "Cache was reset successfully!": "Predpomnilnik je bil uspešno ponastavljen!", "Can't {0} {1}": "Ni možno {0} {1}", "Cancel": "Prekliči", "Cancel all operations": "Prekliči vse operacije", "Change backup output directory": "Spremenite lokacijo za varnostne kopije", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "Spremenite, kako UniGetUI preverja in namešča razpoložljive posodobitve za vaše pakete", "Change how UniGetUI handles install, update and uninstall operations.": "Spremenite, kako UniGetUI upravlja z namestitvami, posodobitvami in odstranitvami.", "Change how UniGetUI installs packages, and checks and installs available updates": "Spremenite način namestitve paketov in preverjanja ter nameščanja razpoložljivih posodobitev", "Change how operations request administrator rights": "Spremenite način zahtevanja skrbniških pravic za operacije", "Change install location": "Spremenite lokacijo namestitve", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Periodično preverjaj posodobitve paketov", "Check for updates": "Preverite posodobitve", "Check for updates every:": "Preveri za posodobitve vsak:", "Check for updates periodically": "Redno preverjaj posodobitve", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON><PERSON> preverjaj posodobitve in me vprašaj, kaj naj storim, ko najdem posodobitve.", "Check for updates regularly, and automatically install available ones.": "<PERSON><PERSON> pre<PERSON>, ali so na voljo posodobitve, in samodejno namestite tiste, ki so na voljo.", "Check out my {0} and my {1}!": "Poglej moj {0} in mojo {1}!", "Check out some WingetUI overviews": "Oglejte si nekaj pregledov WingetUI", "Checking for other running instances...": "Preverjam druge instance v teku...", "Checking for updates...": "Preverjam za posodobitve...", "Checking found instace(s)...": "Preverjam najdene instance...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON><PERSON>, koliko operacij naj se izvede hkrati", "Clear cache": "Počisti predpomnilnik", "Clear finished operations": null, "Clear selection": "Odznači vse", "Clear successful operations": "<PERSON>isto uspešne operacije", "Clear successful operations from the operation list after a 5 second delay": "Po 5 sekundnem zamiku izbrišite uspešne operacije s seznama operacij", "Clear the local icon cache": "Počistite lokalni predpomnilnik ikon", "Clearing Scoop cache - WingetUI": "Čiščenje predpomnilnika Scoop - WingetUI", "Clearing Scoop cache...": "Praznim Scoop predpomnilnik...", "Click here for more details": "Kliknite tukaj za več podrobnosti", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "<PERSON><PERSON><PERSON><PERSON>, da začnete postopek namestitve. Če preskočite namestitev, UniGetUI morda ne bo deloval po pričakovanjih.", "Close": "<PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Zaprite UniGetUI v sistemsko vrstico", "Close WingetUI to the notification area": "Zapri WingetUI v območje za obvestila", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "Izpis ukazne vrstice", "Command-line to run:": "Ukazna vrstica za izvedbo:", "Compare query against": "Primerjaj poizved<PERSON> z", "Compatible with authentication": "Združljivo z overjanjem", "Compatible with proxy": "Združljivo s posredniškim strežnikom", "Component Information": "Podatki o komponenti", "Concurrency and execution": "Vzporednost in izvajanje", "Connect the internet using a custom proxy": "Povežite se z internetom prek prilagojenega posredniškega strežnika", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Prispevajte k repozitoriju ikon in posnetkov zaslona", "Contributors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopiraj v odložišče", "Could not add source": "Vira ni bilo mogoče dodati", "Could not add source {source} to {manager}": "Ni bilo mogoče dodati vira {source} v {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "Ni bilo mogoče ustvariti svežnja", "Could not load announcements - ": "Ni bilo mogoče naložiti obvestil -", "Could not load announcements - HTTP status code is $CODE": "Obvestil ni bilo mogoče naložiti – statusna koda HTTP je $CODE", "Could not remove source": "Vira ni bilo mogoče odstraniti", "Could not remove source {source} from {manager}": "Vira {source} ni bilo mogoče odstraniti iz {manager}", "Could not remove {source} from {manager}": "{source} ni bilo mogoče odstraniti iz {manager}", "Credentials": "Poverilnice", "Current Version": "Trenutna raz<PERSON>č<PERSON>", "Current status: Not logged in": null, "Current user": "Trenutni uporabnik", "Custom arguments:": "Argumenti po meri:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Argumenti ukazne vrstice po meri:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Prilagodite WingetUI - samo za hekerje in napredne uporabnike", "DEBUG BUILD": "RAZVOJNA RAZLIČICA", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "IZJAVA O OMEJITVI ODGOVORNOSTI: NE ODGOVARJAMO ZA PRENESENE PAKETE. POSKRBITE, DA BOSTE NAMESTILI LE PROGRAMSKO OPREMO, KI JI ZAUPATE.", "Dark": "Temna", "Decline": "Zavrni", "Default": "Privzeto", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Privzete nastavitve - primerne za običajne uporabnike", "Default vcpkg triplet": "Privzeti vcpkg triplet", "Delete?": "Odstrani?", "Dependencies:": null, "Descendant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Description:": "Opis:", "Desktop shortcut created": "Bližnjica na namizju je ustvarjena", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Razvijanje je težko in ta aplikacija je brezplačna. Če pa vam je bila aplikacija všeč, mi lahko vedno <b>častite kavo</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Neposredna namestitev, ko dvokliknete element na zavihku \"{discoveryTab}\" (namesto prikaza informacij o paketu)", "Disable new share API (port 7058)": "Onemogoči API za novo skupno rabo (vrata 7058)", "Disable the 1-minute timeout for package-related operations": "Onemogočite 1-minutno časovno omejitev za operacije, povezane s paketom", "Disclaimer": "Izjava o omejitvi odgovornosti", "Discover Packages": "<PERSON><PERSON><PERSON> pakete", "Discover packages": "Odk<PERSON><PERSON><PERSON> pakete", "Distinguish between\nuppercase and lowercase": "Razlikovati med velikimi in malimi črkami", "Distinguish between uppercase and lowercase": "Razlikuj med velikimi in malimi črkami", "Do NOT check for updates": "NE preverjajte posodobitev", "Do an interactive install for the selected packages": "Izvedite interaktivno namestitev za izbrane pakete", "Do an interactive uninstall for the selected packages": "Izvedite interaktivno odstranitev za izbrane pakete", "Do an interactive update for the selected packages": "Izvedite interaktivno posodobitev za izbrane pakete", "Do not automatically install updates when the battery saver is on": "Ne <PERSON><PERSON><PERSON><PERSON> poso<PERSON> sa<PERSON>, ko je vklopljen varčevalnik baterije", "Do not automatically install updates when the network connection is metered": "Ne <PERSON><PERSON><PERSON><PERSON> poso<PERSON>ev samodejno pri merjeni povezavi", "Do not download new app translations from GitHub automatically": "Ne prenesi samodejno novih prevodov z GitHub", "Do not ignore updates for this package anymore": "Ne prezrite več posodobitev za ta paket", "Do not remove successful operations from the list automatically": "Ne odstranjujte uspešnih operacij s seznama samodejno", "Do not show this dialog again for {0}": "Ne prikaži več tega pogovornega okna za {0}", "Do not update package indexes on launch": "Ne posodabljaj indeksov paketov ob zagonu", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "<PERSON>, da UniGetUI zbira in pošilja anonimne statistike uporabe izključno z namenom izboljšanja uporabniške izkušnje?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Se vam zdi WingetUI uporaben? <PERSON><PERSON> lahko, boste morda želeli podpreti moje delo, da bom lahko še naprej izdeloval WingetUI kot najboljši vmesnik za upravljanje paketov.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Se vam zdi WingetUI uporaben? Bi radi podprli razvijalca? Če je tako, lahko mi {0}, zelo pomaga!", "Do you really want to reset this list? This action cannot be reverted.": "Ali res želite ponastaviti ta seznam? Tega dejanja ni mogoče razveljaviti.", "Do you really want to uninstall the following {0} packages?": "Ali res želite odstraniti naslednje pakete {0}?", "Do you really want to uninstall {0} packages?": "Ali ste prepričano da želite odstraniti {0} p<PERSON><PERSON>?", "Do you really want to uninstall {0}?": "Ali z<PERSON> želite odstraniti {0}?", "Do you want to restart your computer now?": "Ali želite ponovno zagnati vašo napravo zdaj?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Ali želite prevesti WingetUI v svoj jezik? Oglejte si, kako p<PERSON> <a style=\"color:{0}\" href=\"{1}\">TUKAJ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Vam ni do darovanja? Ne skrbite, WingetUI lahko vedno delite s prijatelji. Razširite besedo o WingetUI.", "Donate": "<PERSON><PERSON><PERSON><PERSON>", "Done!": null, "Download failed": "Prenos ni uspel", "Download installer": "Prenesite namestitveni program", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Prenos uspel", "Download updated language files from GitHub automatically": "Samodejno prenesite posodobljene jezikovne datoteke z GitHub", "Downloading": "Prenašanje", "Downloading backup...": null, "Downloading installer for {package}": "Prenos namestitvenega programa za {package}", "Downloading package metadata...": "Prenašanje metapodatkov paketa...", "Enable Scoop cleanup on launch": "Omogoči čiščenje Scoop-a ob zagonu", "Enable WingetUI notifications": "Omogoči obvestila WingetUI", "Enable an [experimental] improved WinGet troubleshooter": "Omogoči [pos<PERSON><PERSON>] izboljšano orodje za odpravljanje težav z WinGet", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "Omogoči optimizacije porabe CPU v ozadju (glej zahtevo za združitev #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Omogoči API v ozadju (WingetUI Widgets and Sharing, vrata 7058)", "Enable it to install packages from {pm}.": "Omogočite ga za namestitev paketov iz {pm}.", "Enable the automatic WinGet troubleshooter": "Omogočite samodejno orodje za odpravljanje težav WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Omogočite novo UAC Elevator z blagovno znamko UniGetUI", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "<PERSON><PERSON><PERSON><PERSON><PERSON> {pm}", "Enter proxy URL here": "Vnesite URL posredniškega strežnika tukaj", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "Napaka", "Everything is up to date": "V<PERSON> je posodobljeno", "Exact match": "Natančno ujemanje", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Obstoječe bližnjice na namizju bodo pregledane, izbrati boste morali, katere želite obdržati in katere odstraniti.", "Expand version": "Razširite različico", "Experimental settings and developer options": "Eksperimentalne nastavitve in opcije za razvijalce", "Export": "Izvoz", "Export log as a file": "Izvozi dnevnik kot datoteko", "Export packages": "Izvozi pakete", "Export selected packages to a file": "Izvozi označene pakete v datoteko", "Export settings to a local file": "Izvozi nastavitve v lokalno datoteko", "Export to a file": "Izvozi v datoteko", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "Pridobivanje najnovejših objav, počakajte ...", "Filters": "<PERSON><PERSON><PERSON>", "Finish": "Konec", "Follow system color scheme": "Sledi sistemski barvni shemi", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Uporabi ARM prevedeno različico winget (SAMO ZA SISTEME ARM64)", "Formerly known as WingetUI": "Prej znan kot WingetUI", "Found": "Najden<PERSON>", "Found packages: ": "<PERSON><PERSON><PERSON><PERSON> paketi:", "Found packages: {0}": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON><PERSON>: {0}, ne<PERSON><PERSON><PERSON><PERSON><PERSON>...", "General preferences": "Splošne nastavitve", "GitHub profile": "GitHub profil", "Global": "Globalno", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Odlično skladišče neznanih, a uporabnih pripomočkov in drugih zanimivih paketov.<br>Vsebuje: <b><PERSON><PERSON><PERSON><PERSON><PERSON>, programe ukazne vrstice, splošno programsko opremo (potrebno je vedro z dodatki)</b>", "Great! You are on the latest version.": "Odlično! Ste na najnovejši različici.", "Grid": "Mreža", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Pomoč in dokumentacija", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Tukaj lahko spremenite vedenje UniGetUI glede naslednjih bližnjic. Če označite bližnjico, jo bo UniGetUI izbrisal, če bo ustvarjena pri prihodnji nadgradnji. Če jo počistite, bo bliž<PERSON>ca ostala nedotaknjena", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON><PERSON>, ime mi je Martí in sem <i>razvijalec</i> aplikacije WingetUI. WingetUI je bil v celoti izdelan v mojem prostem času!", "Hide details": "Skrij podrobnosti", "Homepage": "Domača stran", "Hooray! No updates were found.": "<PERSON><PERSON>! Ni najdenih posodo<PERSON>ev!", "How should installations that require administrator privileges be treated?": "<PERSON>ko naj ravnamo z namestitvami, ki zahtevajo skrbniške pravice?", "How to add packages to a bundle": "Kako dodati pakete v sveženj", "I understand": "Razumem", "Icons": "Ikone", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "<PERSON><PERSON><PERSON>raj posodobitve za ta paket", "Ignore packages from {pm} when showing a notification about updates": "Pre<PERSON>ri pakete iz {pm} pri prikazu obvestil o posodobitvah", "Ignore selected packages": "<PERSON>zri izbra<PERSON> pakete", "Ignore special characters": "Ignorirajte posebne znake", "Ignore updates for the selected packages": "<PERSON>zri posodobitve za izbrane pakete", "Ignore updates for this package": "<PERSON><PERSON><PERSON>raj posodobitve za ta paket", "Ignored updates": "Prezrte posodobitve", "Ignored version": "Prezrte različice", "Import": "<PERSON><PERSON><PERSON>", "Import packages": "Uvozi pakete", "Import packages from a file": "Uvozi pakete iz datoteke", "Import settings from a local file": "Uvozi nastavitve iz lokalne datoteke", "In order to add packages to a bundle, you will need to: ": "Za dodajanje paketov v sveženj morate:", "Initializing WingetUI...": "Inicializacija WingetUI ...", "Install": "<PERSON><PERSON>", "Install Scoop": "<PERSON><PERSON>", "Install and more": null, "Install and update preferences": "Nastavitve namestitve in posodobitev", "Install as administrator": "Namesti kot skrbnik", "Install available updates automatically": "Samodejno namestite razpoložljive posodobitve", "Install location can't be changed for {0} packages": null, "Install location:": "Lokacija namestitve:", "Install options": null, "Install packages from a file": "<PERSON><PERSON> pakete iz datoteke", "Install prerelease versions of UniGetUI": "Namestite predizdajne različice UniGetUI", "Install selected packages": "Namestite izbrane pakete", "Install selected packages with administrator privileges": "Namestite izbrane pakete s skrbniškimi pravicami", "Install selection": "Namestite izbor", "Install the latest prerelease version": "<PERSON><PERSON> najnove<PERSON><PERSON><PERSON> različico pred izdajo", "Install updates automatically": "Samodejno namestite posodobitve", "Install {0}": "<PERSON><PERSON> {0}", "Installation canceled by the user!": "Nameščanje prekinil uporabnik!", "Installation failed": "Namestitev ni uspela", "Installation options": "Možnosti namestitve", "Installation scope:": "Obseg namestitve:", "Installation succeeded": "Namestitev je uspela", "Installed Packages": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Installed Version": "Nameščena različica", "Installed packages": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Installer SHA256": "Namestitveni program SHA256", "Installer SHA512": "Installer SHA512", "Installer Type": "Namestitveni program tip", "Installer URL": "Namestitveni program URL", "Installer not available": "Namestitveni program ni na voljo", "Instance {0} responded, quitting...": "Instanca {0} se odzvala, zapiram...", "Instant search": "Ta<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Preverjanje integritete preskočeno", "Integrity checks will not be performed during this operation": "Preverjanje integritete ne bo izvedeno med to operacijo", "Interactive installation": "Interaktivna namestitev", "Interactive operation": "Interaktivna operacija", "Interactive uninstall": "Interaktivna odstranitev", "Interactive update": "Interaktivna <PERSON>ev", "Internet connection settings": "Nastavitve internetne povezave", "Is this package missing the icon?": "Ali ima ta paket manjk<PERSON> i<PERSON>?", "Is your language missing or incomplete?": "Ali vaš jezik manjka ali je nepopoln?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "<PERSON>, da bodo poverilnice varno shran<PERSON>ne, zato ne uporabljajte banč<PERSON> podatkov", "It is recommended to restart UniGetUI after WinGet has been repaired": "Priporo<PERSON><PERSON><PERSON><PERSON> je, da znova zaženete UniGetUI, ko je WinGet popravljen", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "<PERSON><PERSON><PERSON> je, da WinGet ne deluje pravilno. <PERSON> poskusiti popraviti WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON><PERSON> je, da ste WingetUI zagnali kot skrbnik, kar ni priporočljivo. Še vedno lahko uporabljate program, vendar priporočamo, da WingetUI ne izvajate s skrbniškimi pravicami. Kliknite \"{showDetails}\", da vidite zakaj.", "Language": "<PERSON><PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON>, tema in druge raznovrstne nastavitve", "Last updated:": "<PERSON><PERSON><PERSON>:", "Latest": "Zadnje", "Latest Version": "Zadnja različica", "Latest Version:": "Zadnja različica:", "Latest details...": "Zadnje podrobnosti...", "Launching subprocess...": "Zagon podprocesa ...", "Leave empty for default": "Pustite prazno kot privzeto", "License": "Licenca", "Licenses": "Licence", "Light": "<PERSON><PERSON><PERSON>", "List": "Seznam", "Live command-line output": "Trenutni izpis ukazne vrstice", "Live output": "Trenutni izpis", "Loading UI components...": "Nalagam UI komponente...", "Loading WingetUI...": "Nalagam WingetUI...", "Loading packages": "<PERSON><PERSON><PERSON><PERSON>", "Loading packages, please wait...": "<PERSON><PERSON><PERSON><PERSON>, poč<PERSON>j<PERSON> ...", "Loading...": "Nalagam...", "Local": "Lokalno", "Local PC": "Lokalni PC", "Local backup advanced options": null, "Local machine": "Lokalni sistem", "Local package backup": null, "Locating {pm}...": "<PERSON><PERSON><PERSON><PERSON> {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "<PERSON><PERSON>:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON>...", "Machine | Global": "Mašina | Globalno", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "Upravljaj", "Manage UniGetUI settings": "Upravljaj nastavitve UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Upravljajte obnašanje samodejnega zagona WingetUI v aplikaciji Nastavitve", "Manage ignored packages": "Upravljanje prezrtih paketov", "Manage ignored updates": "Upravljanje prezrtih posodobitev", "Manage shortcuts": "Upravljanje bližnjic", "Manage telemetry settings": "Upravljaj nastavitve telemetrije", "Manage {0} sources": "Upravljaj {0} virov", "Manifest": "Manifest", "Manifests": "<PERSON><PERSON><PERSON><PERSON>", "Manual scan": "<PERSON><PERSON><PERSON><PERSON>", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoftov uradni upravitelj paketov. Polno znanih in preverjenih paketov<br>Vsebuje: <b>Splošno programsko opremo, aplikacije Microsoft Store</b>", "Missing dependency": "Manjka programska odvisnost", "More": "Več", "More details": "<PERSON><PERSON><PERSON>", "More details about the shared data and how it will be processed": "Več podrobnosti o deljenih podatkih in njihovi obdelavi", "More info": "Več informacij", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "OPOMBA: To orodje za odpravljanje težav lahko onemogočite v nastavitvah UniGetUI v razdelku WinGet", "Name": "<PERSON><PERSON>", "New": null, "New Version": "Nova Različica", "New bundle": "Novi sveženj", "New version": "Nova različica", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Ne", "No applicable installer was found for the package {0}": "Za paket {0} ni bilo najdenega ustreznega namestitvenega programa", "No dependencies specified": null, "No new shortcuts were found during the scan.": "Med iskanjem niso bile najdene nove bližnjice.", "No packages found": "<PERSON> najdenih paketov", "No packages found matching the input criteria": "Najden ni bil noben paket, ki bi ustrezal kriterijem vnosa", "No packages have been added yet": "<PERSON>ben paket še ni bil dodan", "No packages selected": "<PERSON> i<PERSON>nih paketov", "No packages were found": "Najden ni bil noben paket", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Osebni podatki se ne zbirajo niti pošiljajo, zbrani podatki so anonimizirani in jih ni mogoče povezati z vami.", "No results were found matching the input criteria": "Ni bilo najdenih rezultatov, ki bi ustrezali kriterijem vnosa", "No sources found": "<PERSON> virov", "No sources were found": "<PERSON>iri niso bili najdeni", "No updates are available": "Na voljo ni nobena poso<PERSON><PERSON>ev", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Upravitelj paketov Node JS. Polno kn<PERSON>ž<PERSON> in drugih pripomočkov, ki krožijo po svetu javascripta<br>Vsebuje: <b>Knjižnice javascript voz<PERSON><PERSON><PERSON><PERSON> in druge povezane pripomočke</b>", "Not available": "<PERSON> na voljo", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "<PERSON> najdeno", "Not right now": "Ne zdaj", "Notes:": "Op<PERSON>e:", "Notification preferences": "Nastavi<PERSON><PERSON> obvestil", "Notification tray options": "Možnosti vrstice z obvestili", "Notification types": "<PERSON><PERSON><PERSON> obvestil", "NuPkg (zipped manifest)": "NuPkg (stisnjen manifest)", "OK": "V redu", "Ok": "V redu", "Open": "Odpri", "Open GitHub": "Odp<PERSON> G<PERSON>", "Open UniGetUI": "Odprite UniGetUI", "Open UniGetUI security settings": null, "Open WingetUI": "Odprite WingetUI", "Open backup location": "Odpri lokacijo varnostne kopije", "Open existing bundle": "Odpri obstoječi sveženj", "Open install location": "Odpri mesto namestitve", "Open the welcome wizard": "Odprite čarovnika za dobrodošlico", "Operation canceled by user": "Operacijo je preklical uporabnik", "Operation cancelled": "Operacija preklicana", "Operation history": "Z<PERSON>do<PERSON>", "Operation in progress": "Operacija v teku", "Operation on queue (position {0})...": "Operacija v čakalni vrsti (položaj {0}) ...", "Operation profile:": null, "Options saved": "Možnosti shranjene", "Order by:": "Razvrsti po:", "Other": "Ostalo", "Other settings": "Druge nastavitve", "Package": null, "Package Bundles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package ID": "ID paketa", "Package Manager": "Upravitel<PERSON> p<PERSON>", "Package Manager logs": "Dnevniki upravitelja paketov", "Package Managers": "Upravitelji paketov", "Package Name": "<PERSON><PERSON> paketa", "Package backup": "Varnostna kopija paketa", "Package backup settings": null, "Package bundle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package details": "<PERSON><PERSON><PERSON><PERSON><PERSON> paketa", "Package lists": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Package management made easy": "Upravljanje paketov na enostaven način", "Package manager": "Upravljalnik paketov", "Package manager preferences": "Nastavitve upravitelja paketov", "Package managers": "Upravljalniki paketov", "Package not found": "<PERSON>et ni bil najden", "Package operation preferences": "Nastavitve operacij paketov", "Package update preferences": "Nastavit<PERSON> poso<PERSON><PERSON> paketov", "Package {name} from {manager}": "Paket {name} od {manager}", "Package's default": null, "Packages": "Paketi", "Packages found: {0}": "<PERSON><PERSON><PERSON><PERSON> paketi: {0}", "Partially": "Delno", "Password": "<PERSON><PERSON><PERSON>", "Paste a valid URL to the database": "Prilepite veljaven URL v bazo podatkov", "Pause updates for": null, "Perform a backup now": "Izvedite varnostno kopijo zdaj", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "Izvajanje varnostne kopije, počakajte ...", "Periodically perform a backup of the installed packages": "Občasno naredite varnostno kopijo nameš<PERSON><PERSON> paketov", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "Preverite možnosti namestitve za ta paket in poskusite znova", "Please click on \"Continue\" to continue": "Kliknite »Nadaljuj« za nadaljevanje", "Please enter at least 3 characters": "Vnesite vsaj 3 znake", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da <PERSON><PERSON><PERSON><PERSON> paketov morda ne bo mogoče namestiti zaradi upraviteljev paketov, ki so omogočeni na tej napravi.", "Please note that not all package managers may fully support this feature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da vsi upravljalniki paketov morda ne podpirajo te funkcije", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da paketov iz določenih virov morda ni mogoče izvoziti. Obarvani so sivo in ne bodo izvoženi.", "Please run UniGetUI as a regular user and try again.": "Zaženite UniGetUI kot običajni uporabnik in poskusite znova.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Za nadaljnje informacije o težavi si oglejte Izpis ukazne vrstice ali glejte Zgodovino delovanja.", "Please select how you want to configure WingetUI": "<PERSON><PERSON><PERSON><PERSON>, kako želite konfigurirati WingetUI", "Please try again later": "Poskusite znova pozneje", "Please type at least two characters": "Vnesite vsaj dva znaka", "Please wait": "Prosim počakajte", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, da se {0} names<PERSON>. Lahko se prikaže črno okno. <PERSON><PERSON><PERSON><PERSON><PERSON>, da se zapre.", "Please wait...": "Prosim počakajte...", "Portable": "Prenosna različica", "Portable mode": "Prenosni način", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Upravitelj paketov PowerShell. Poiščite knjižnice in skripte za razširitev zmogljivosti lupine PowerShell<br>Vsebuje: <b><PERSON><PERSON><PERSON>, skripte, cmdlete</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "PreRelease", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON> ...", "Proceed at your own risk.": "Nadaljujte na lastno odgovornost.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "URL posredniškega strežnika", "Proxy compatibility table": "Tabela združljivosti posredniškega strežnika", "Proxy settings": "Nastavitve posredniškega strežnika", "Proxy settings, etc.": "Nastavitve posredniškega strežnika itd.", "Publication date:": "Da<PERSON> objave:", "Publisher": "Založnik", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Upravitelj knjižnice Python. Polno knjižnic Python in drugih pripomočkov, povezanih s Pythonom<br>Vsebuje: <b>Knjižnice Python in sorodni pripomočki</b>", "Quit": "Izhod", "Quit WingetUI": "Zapri WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "Ponovno namestite", "Reinstall package": "Ponovno namestite paket", "Related settings": "Povezane nastavitve", "Release notes": "Opomb<PERSON> ob izdaji", "Release notes URL": "URL opomb ob izdaji", "Release notes URL:": "URL opomb ob izdaji:", "Release notes:": "<PERSON><PERSON><PERSON> ob i<PERSON>ji:", "Reload": "Osveži", "Reload log": "Osveži dnevnik", "Removal failed": "Odstranjevanje spodletelo", "Removal succeeded": "Odstranjevanje us<PERSON>šno", "Remove from list": "Odstrani iz seznama", "Remove permanent data": "Odstranite trajne podatke", "Remove selection from bundle": "Odstrani izbiro iz svežnja", "Remove successful installs/uninstalls/updates from the installation list": "Odstranite uspešne namestitve/odstranitve/posodobitve s seznama namestitve", "Removing source {source}": "Odstranjevanje vira {source}", "Removing source {source} from {manager}": "Odstranjevanje vira {source} iz {manager}", "Repair UniGetUI": null, "Repair WinGet": "Popravi WinGet", "Report an issue or submit a feature request": "Prijavite težavo ali oddajte zahtevo po novi funkciji", "Repository": "<PERSON>oz<PERSON><PERSON><PERSON>", "Reset": "<PERSON><PERSON><PERSON>", "Reset Scoop's global app cache": "Ponastavite globalni predpomnilnik aplikacije Scoop", "Reset UniGetUI": "Ponastavite UniGetUI", "Reset WinGet": "Ponastavite <PERSON>", "Reset Winget sources (might help if no packages are listed)": "Ponastavi vire Winget (morda <PERSON>, če ni najdenih nobenih paketov)", "Reset WingetUI": "Ponastavite WingetUI", "Reset WingetUI and its preferences": "Ponastavite WingetUI in njegove nastavitve", "Reset WingetUI icon and screenshot cache": "Ponastavite ikono WingetUI in predpomnilnik posnetkov zaslona", "Reset list": "Ponast<PERSON>", "Resetting Winget sources - WingetUI": "Ponastavitev virov Winget - WingetUI", "Restart": "Ponovni zagon", "Restart UniGetUI": "Ponovni zagon UniGetUI", "Restart WingetUI": "Ponovno zaženi WingetUI", "Restart WingetUI to fully apply changes": "Znova zaženite WingetUI, da v celoti uveljavite spremembe", "Restart later": "Ponovno zaženi kasneje", "Restart now": "Ponovni zagon", "Restart required": "Potreben ponovni zagon", "Restart your PC to finish installation": "Ponovno zaženite vaš računalnik za dokončanje namestitve", "Restart your computer to finish the installation": "Za dokončanje namestitve znova zaženite napravo", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Poskusite znova", "Retry as administrator": "Poskusite znova kot skrbnik", "Retry failed operations": "Poskusi znova z neuspešnimi operacijami", "Retry interactively": "Poskusite znova interaktivno", "Retry skipping integrity checks": "Ponovno poskusi brez preverjanja integritete", "Retrying, please wait...": "<PERSON><PERSON>ni pos<PERSON>, počakajte ...", "Return to top": "<PERSON><PERSON><PERSON> na vrh", "Run": "Zaženi", "Run as admin": "Zaženi kot skrbnik", "Run cleanup and clear cache": "Zaženite čiščenje in počistite predpomnilnik", "Run last": "Zaženi zadnje", "Run next": "Zaženi naslednje", "Run now": "Zaženi zdaj", "Running the installer...": "Zagon namestitvenega programa ...", "Running the uninstaller...": "Zagon programa za odstranjevanje ...", "Running the updater...": "Izvajanje posodobitve ...", "Save": null, "Save File": "<PERSON><PERSON><PERSON>", "Save and close": "Shrani in zapri", "Save as": null, "Save bundle as": "Shrani sveženj kot", "Save now": "S<PERSON>ni zdaj", "Saving packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, poč<PERSON>j<PERSON> ...", "Scoop Installer - WingetUI": "<PERSON><PERSON>ler - WingetUI", "Scoop Uninstaller - WingetUI": "<PERSON><PERSON> - WingetUI", "Scoop package": "<PERSON><PERSON> paket", "Search": "<PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Poiščite namizno programsko opremo, opozorite me, ko so na voljo posodobitve, in ne počnite piflarskih stvari. <PERSON>e želim, da WingetUI preveč komp<PERSON>, ž<PERSON><PERSON> samo preprosto <b>trgovino s programsko opremo</b>", "Search for packages": "Išči za pakete", "Search for packages to start": "Za začetek poiščite pakete", "Search mode": "<PERSON><PERSON><PERSON>", "Search on available updates": "Poiščite razpoložljive posodobitve", "Search on your software": "Iščite v vaši programski opremi", "Searching for installed packages...": "Iš<PERSON><PERSON> za namešč<PERSON> pakete...", "Searching for packages...": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Searching for updates...": "<PERSON><PERSON><PERSON> ...", "Select": "Izberi", "Select \"{item}\" to add your custom bucket": "Izberite \"{item}\", da dodate svoj bucket po meri", "Select a folder": "Izberi mapo", "Select all": "Označi vse", "Select all packages": "Izberite vse pakete", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON> samo, <b><PERSON>e veste, kaj po<PERSON>e</b>.", "Select package file": "Izberi paketno datoteko", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "<PERSON><PERSON><PERSON><PERSON> vir, ki ga ž<PERSON>te dodati:", "Select upgradable packages by default": "Privzeto izberite nadgradljive pakete", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON><PERSON> <b>uprav<PERSON><PERSON><PERSON><PERSON> pake<PERSON></b> <PERSON><PERSON><PERSON> up<PERSON> ({0}), k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kako se namestijo paketi, upravljajte, kako se obravnavajo skrbniške pravice itd.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Poslano rokovanje. Čakanje na odgovor... ({0}%)", "Set a custom backup file name": "Nastavite ime datoteke varnostne kopije po meri", "Set custom backup file name": "Nastavite ime datoteke varnostne kopije po meri", "Settings": "Nastavitve", "Share": "Deli", "Share WingetUI": "Deli WingetUI", "Share anonymous usage data": "Deli anonimne podatke o uporabi", "Share this package": "<PERSON>i paket", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Prikaži UniGetUI na sistemski vrstici", "Show UniGetUI's version and build number on the titlebar.": "Pokaži različico UniGetUI in številko izgradnje v naslovni vrstici.", "Show WingetUI": "Prikaži WingetUI", "Show a notification when an installation fails": "<PERSON><PERSON><PERSON><PERSON> ob<PERSON>, ko namestitev ne uspe", "Show a notification when an installation finishes successfully": "Pokažite obvestilo, ko se namestitev uspešno konča", "Show a notification when an operation fails": "Po<PERSON><PERSON><PERSON> ob<PERSON>, ko operacija ne uspe", "Show a notification when an operation finishes successfully": "Pokažite obvestilo, ko se operacija uspešno konča", "Show a notification when there are available updates": "<PERSON><PERSON><PERSON><PERSON> ob<PERSON>, ko so na voljo posodobitve", "Show a silent notification when an operation is running": "Pokaži tiho obvestilo, ko se operacija izvaja", "Show details": "Prikaži podrobnosti", "Show in explorer": "Pokaži v raziskovalcu", "Show info about the package on the Updates tab": "Prikaži informacije o paketu na zavihku Posodobitve", "Show missing translation strings": "P<PERSON><PERSON><PERSON> man<PERSON>če prevajalske nize", "Show notifications on different events": "Prikažite obvestila o različnih dogodkih", "Show package details": "Prikaži podrobnosti paketa", "Show package icons on package lists": "Pokaži ikone paketov na seznamih paketov", "Show similar packages": "Prikaži podob<PERSON> pakete", "Show the live output": "Prikaži izhod v živo", "Size": "Velikost", "Skip": "Preskoči", "Skip hash check": "Preskoči preverjanje zgoščevanja", "Skip hash checks": "Preskoči preverjanje zgoščene vrednosti", "Skip integrity checks": "Preskoči preverjanje celovitosti", "Skip minor updates for this package": "Preskoči manjše posodobitve za ta paket", "Skip the hash check when installing the selected packages": "Preskočite preverjanje zgoščene vrednosti pri namestitvi izbranih paketov", "Skip the hash check when updating the selected packages": "Pri posodabljanju izbranih paketov preskočite preverjanje zgoščene vrednosti", "Skip this version": "Preskoči to različico", "Software Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON> paketov", "Something went wrong": "Nekaj je <PERSON> na<PERSON>", "Something went wrong while launching the updater.": "Pri zagonu posodobitvenega programa je prišlo do napake.", "Source": "Vir", "Source URL:": "URL vira:", "Source added successfully": "<PERSON>ir us<PERSON> dodan", "Source addition failed": "Dodajanje vira ni uspelo", "Source name:": "<PERSON><PERSON> vira:", "Source removal failed": "Odstranitev vira ni uspela", "Source removed successfully": "Vir uspešno odstranjen", "Source:": "Vir:", "Sources": "Viri", "Start": "Začni", "Starting daemons...": "Zaganjam daemons...", "Starting operation...": "Zaganjanje operacije...", "Startup options": "Možnosti zagona", "Status": "<PERSON><PERSON>", "Stuck here? Skip initialization": "Ste obtičali? Preskočite inicializacijo", "Suport the developer": "Podprite razvijalca", "Support me": "Podpri me", "Support the developer": "Podpri razvijalca", "Systems are now ready to go!": "Sistemi so zdaj pripravljeni za uporabo!", "Telemetry": "Telemetrija", "Text": "<PERSON><PERSON><PERSON>", "Text file": "Tekstovna datoteka", "Thank you ❤": "Hval<PERSON> ❤", "Thank you 😉": "Hval<PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Upravitelj paketov Rust.<br>Vsebuje: <b>Knjižnice in programe Rust, napisane v Rustu</b>", "The backup will NOT include any binary file nor any program's saved data.": "Varnostna kopija NE bo vključevala nobene binarne datoteke ali shranjenih podatkov katerega koli programa.", "The backup will be performed after login.": "Varnostno kopiranje bo izvedeno po prijavi.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Varnostna kopija bo vsebovala celoten seznam name<PERSON><PERSON><PERSON><PERSON> paketov in možnosti njihove namestitve. Shranjene bodo tudi prezrte posodobitve in preskočene različice.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON><PERSON>, da pake<PERSON>, ki ga posku<PERSON> naložiti, ni veljaven. Preverite datoteko in poskusite znova.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Kontrolna vsota namestitvenega programa ne sovpada s pričakovano vrednostjo in pristnosti namestitvenega programa ni mogoče preveriti. Če zaupate založniku, {0} paket znova brez preverjanje kontrolne vsote.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Klasični upravitelj paketov za Windows. Tukaj boste našli vse. <br>Vsebuje: <b>Splošno programsko opremo</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Trenutni sveženj nima paketov. Za začetek dodajte nekaj paketov", "The executable file for {0} was not found": "Izvršljiva datoteka za {0} ni bila najdena", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Naslednji paketi bodo izvoženi v datoteko JSON. Nobeni uporabniški podatki ali binarne datoteke ne bodo shranjeni.", "The following packages are going to be installed on your system.": "Naslednji paketi bodo nameščeni v vašem sistemu.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "Naslednje nastavitve bodo uporabljene vs<PERSON>č, ko bo ta paket <PERSON>, posodobljen ali odstranjen.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Naslednje nastavitve bodo uporabljene vs<PERSON>č, ko bo ta paket <PERSON>, posodobljen ali odstranjen. Shranjeni bodo samodejno.", "The icons and screenshots are maintained by users like you!": "Ikone in posnetke zaslona vzdržujejo uporabniki, kot ste vi!", "The installer authenticity could not be verified.": "Pristnosti namestitvenega programa ni bilo mogoče preveriti.", "The installer has an invalid checksum": "Instalacija ima neveljavno kontrolno vsoto", "The installer hash does not match the expected value.": "Zgoščena vrednost namestitvenega programa se ne ujema s pričakovano vrednostjo.", "The local icon cache currently takes {0} MB": "Lokalni predpomnilnik ikon trenutno zaseda {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Glavni cilj tega projekta je ustvariti intuitiven uporabniški vmesnik za najpogostejše upravitelje paketov CLI za Windows, kot sta Winget in Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Paket \"{0}\" ni bil najden v upravitelju paketov \"{1}\"", "The package bundle could not be created due to an error.": "Paketa ni bilo mogoče ustvariti zaradi napake.", "The package bundle is not valid": "Sveženj paketov ni veljaven", "The package manager \"{0}\" is disabled": "Upravi<PERSON><PERSON> pake<PERSON> \"{0}\" je onemogo<PERSON>en", "The package manager \"{0}\" was not found": "Upravitel<PERSON> paketov \"{0}\" ni bil najden", "The package {0} from {1} was not found.": "Paket {0} od {1} ni bil najden.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "<PERSON><PERSON><PERSON>den<PERSON> paketi ne bodo upoštevani pri preverjanju posodobitev. Dvokliknite jih ali kliknite gumb na njihovi desni, če želite prenehati ignorirati njihove posodobitve.", "The selected packages have been blacklisted": "Izbrani paketi so bili uvrščeni na črni seznam", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Velikost varnostne kopije je ocenjena na manj kot 1 MB.", "The source {source} was added to {manager} successfully": "Vir {source} je bil uspeš<PERSON> dodan v {manager}", "The source {source} was removed from {manager} successfully": "Vir {source} je bil uspešno odstranjen iz {manager}", "The system tray icon must be enabled in order for notifications to work": "Ikona v sistemski vrstici mora biti omogočena za delovanje obvestil", "The update process has been aborted.": "Postopek posodobitve je bil prekinjen.", "The update process will start after closing UniGetUI": "Postopek posodabljanja se bo začel po zaprtju UniGetUI", "The update will be installed upon closing WingetUI": "<PERSON><PERSON><PERSON><PERSON><PERSON> bo <PERSON><PERSON><PERSON><PERSON>, ko zaprete WingetUI", "The update will not continue.": "Po<PERSON>dobitev se ne bo nadaljevala.", "The user has canceled {0}, that was a requirement for {1} to be run": "Uporabnik je preklical {0}, kar je bilo potrebno za zagon {1}", "There are no new UniGetUI versions to be installed": "Ni novih različic UniGetUI za namestitev", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "V teku so operacije. Zapustitev WingetUI lahko povzroči njihovo odpoved. Želite nadaljevati?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Na YouTubu je nekaj odli<PERSON><PERSON><PERSON> videoposnetkov, ki prikazujejo WingetUI in njegove zmogljivosti. Lahko se naučite koristnih trikov in nasvetov!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Obstajata dva glavna razloga, da WingetUI ne zaženete kot skrbnik:\\n Prvi je, da lahko upravljalnik paketov Scoop povzroči težave z nekaterimi ukazi, če se izvaja s skrbniškimi pravicami.\\n Drugi je, da zagon WingetUI kot skrbnik pomeni, da kateri koli paket ki ga prenesete, se bo zagnal kot skrbnik (in to ni varno).\\n Ne pozabite, da če morate namestiti določen paket kot skrbnik, lahko vedno z desno miškino tipko kliknete element -> Namesti/Posodobi/Odstrani kot skrbnik.", "There is an error with the configuration of the package manager \"{0}\"": "<PERSON><PERSON><PERSON><PERSON> je do napake pri konfiguraciji upravitelja paketov \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "V teku je namestitev. Če zaprete WingetUI, lahko namestitev ne uspe in ima nepričakovane rezultate. <PERSON> še vedno želite zapustiti WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "So <PERSON>i, ki so zadolženi za namestitev, posodabljanje in odstranjevanje paketov.", "Third-party licenses": "Licence tretjih oseb", "This could represent a <b>security risk</b>.": "To lahko predstavlja <b>varnostno tveganje</b>.", "This is not recommended.": "To ni priporočljivo.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "To je verjet<PERSON> p<PERSON>, da je bil paket, ki ste ga pre<PERSON>, odstranjen ali objavljen v upravitelju paketov, ki ga niste omogočili. Prejeti ID je {0}", "This is the <b>default choice</b>.": "To je <b>pri<PERSON><PERSON><PERSON> <PERSON></b>.", "This may help if WinGet packages are not shown": "To la<PERSON><PERSON> p<PERSON>, <PERSON><PERSON> paketi <PERSON> niso p<PERSON>zani", "This may help if no packages are listed": "To lah<PERSON> pomaga, če ni prikazanih paketov", "This may take a minute or two": "To lahko traja minuto ali dve", "This operation is running interactively.": "Ta operacija se izvaja interaktivno.", "This operation is running with administrator privileges.": "Ta operacija se izvaja s skrbniškimi pravicami.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Ta paket je mogoče posodobiti", "This package can be updated to version {0}": "Ta paket je mogoče posodobiti na različico {0}", "This package can be upgraded to version {0}": "Ta paket je mogoče nadgraditi na različico {0}", "This package cannot be installed from an elevated context.": "Tega paketa ni mogoče namestiti iz povišanega konteksta.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Ta paket nima posnetkov zaslona ali manjka ikona? Prispevajte k WingetUI tako, da dodate manjkajoče ikone in posnetke zaslona v našo odprto javno bazo podatkov.", "This package is already installed": "Ta paket je že name<PERSON>en", "This package is being processed": "Ta paket je v obdelavi", "This package is not available": "Ta paket ni na voljo", "This package is on the queue": "Ta paket je v čakalni vrsti", "This process is running with administrator privileges": "Ta proces teče z administracijskimi pravicami", "This project has no connection with the official {0} project — it's completely unofficial.": "Ta projekt nima nobene povezave z uradnim projektom {0} – je popolnoma neuraden.", "This setting is disabled": "Ta nastavitev je onemogočena", "This wizard will help you configure and customize WingetUI!": "Ta čarovnik vam bo pomagal konfigurirati in prilagoditi WingetUI!", "Toggle search filters pane": "<PERSON>k<PERSON> is<PERSON>lt<PERSON>", "Translators": "Prevajalci", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON>j vnesite ime in URL vira, ki ga <PERSON><PERSON><PERSON> do<PERSON>, lo<PERSON><PERSON> s presledkom.", "Unable to find package": "Paketa ni mogoče najti", "Unable to load informarion": "Informacij ni mogoče naložiti", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI zbira anonimne podatke o uporabi z namenom izboljšanja uporabniške izkušnje.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI zbira anonimne podatke izključno za razumevanje in izboljšanje uporabniške izkušnje.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI je zaznal novo bližnjico na namizju, ki jo je mogoče samodejno izbrisati.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI je zaznal naslednje bližnjice, ki jih je mogoče samodejno odstraniti ob prihodnjih nadgradnjah", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI je zaznal {0} novih bližnjic na namizju, ki jih je mogoče samodejno izbrisati.", "UniGetUI is being updated...": "UniGetUI se posodablja ...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI ni povezan z nobenim od združljivih upravljalnikov paketov. UniGetUI je neodvisen projekt.", "UniGetUI on the background and system tray": "UniGetUI v ozadju in sistemski vrstici", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI potrebuje {0} za delovanje, vendar ni bil najden v vašem sistemu.", "UniGetUI startup page:": "Začetna stran UniGetUI:", "UniGetUI updater": "UniGetUI posodobitveni program", "UniGetUI version {0} is being downloaded.": "Prenos različice UniGetUI {0} poteka.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} je pripravljen za namestitev.", "Uninstall": "Odstrani", "Uninstall Scoop (and its packages)": "Odstrani Scoop (in njegove pakete)", "Uninstall and more": null, "Uninstall and remove data": "Odmestite in odstranite podatke", "Uninstall as administrator": "Odstrani kot skrbnik", "Uninstall canceled by the user!": "Uporabnik je preklical odstranitev!", "Uninstall failed": "Odmestitev ni uspela", "Uninstall options": null, "Uninstall package": "Odstrani paket", "Uninstall package, then reinstall it": "Odstranite paket in ga nato znova namestite", "Uninstall package, then update it": "Odstranite paket in ga nato posodobite", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Odstrani izbrane pakete", "Uninstall selection": null, "Uninstall succeeded": "Odmestitev uspešna", "Uninstall the selected packages with administrator privileges": "Odstranite izbrane pakete s skrbniškimi pravicami", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "<PERSON><PERSON>, ki jih ni mogoče namestiti in katerih izvor je naveden kot \"{0}\", niso objavljeni v nobenem upravitelju paketov, zato o njih ni na voljo nobenih informacij.", "Unknown": "Neznano", "Unknown size": "Neznana velikost", "Unset or unknown": "Nepostavljeno ali <PERSON>nano", "Up to date": "Ažurno", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Posodobi WingetUI samodejno", "Update all": "Posodobi vse", "Update and more": null, "Update as administrator": "Posodobi kot skrbnik", "Update check frequency, automatically install updates, etc.": "Pogostost preverjanja posodobitev, samodejna namestitev posodobitev itd.", "Update date": "<PERSON><PERSON> posodo<PERSON>ve", "Update failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> spodletela", "Update found!": "<PERSON><PERSON><PERSON><PERSON> poso<PERSON>ev!", "Update now": "Posodobi zdaj", "Update options": null, "Update package indexes on launch": "<PERSON><PERSON> z<PERSON>u posodobi in<PERSON><PERSON> paketov", "Update packages automatically": "Posodobi pakete samodejno", "Update selected packages": "Posodobi <PERSON> pakete", "Update selected packages with administrator privileges": "Posodobite izbrane pakete s skrbniškimi pravicami", "Update selection": null, "Update succeeded": "Posodobite uspešna", "Update to version {0}": "Posodobite na različico {0}", "Update to {0} available": "Posodobitev na {0} na voljo", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Samodejno posodobi Git portfile za vcpkg (zahteva nameščen Git)", "Updates": "Posodobitve", "Updates available!": "Na voljo so posodobitve!", "Updates for this package are ignored": "Posodobitve za ta paket so prezrte", "Updates found!": "Najdene posodobitve!", "Updates preferences": "Posodobitve nastavitev", "Updating WingetUI": "Posodobitev WingetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Uporabi starejši priložen WinGet namesto PowerShell CMDLetov", "Use a custom icon and screenshot database URL": "Uporabite ikono po meri in URL zbirke podatkov posnetkov zaslona", "Use bundled WinGet instead of PowerShell CMDlets": "Uporabi priložen WinGet namesto PowerShell CMDLetov", "Use bundled WinGet instead of system WinGet": "Uporabite vgrajeni WinGet namesto sistemskega WinGet", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Uporabi nameščen GSudo <PERSON> prilo<PERSON> (zahteva ponovni zagon aplikacije)", "Use system Chocolatey": "Uporabite sistem Chocolatey", "Use system Chocolatey (Needs a restart)": "Uporabi sistemski Chocolatey (Potreben je ponovni zagon)", "Use system Winget (Needs a restart)": "Uporabi sistemski Winget (Potreben je ponovni zagon)", "Use system Winget (System language must be set to english)": "Uporabi sistem Winget (sistemski jezik mora biti nastavljen na angleščino)", "Use the WinGet COM API to fetch packages": "Uporabi COM API WinGet za pridobivanje paketov", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Uporabi PowerShell modul WinGet namesto COM API", "Useful links": "Uporabne povezave", "User": "Uporabnik", "User interface preferences": "Nastavitve uporabniškega vmesnika", "User | Local": "Uporabnik | Lokalno", "Username": "Uporabniško ime", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Uporaba WingetUI pomeni sprejetje licence GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Z uporabo WingetUI se strinjate z MIT licenco", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Korena vcpkg ni bilo mogoče najti. Določite okoljsko spremenljivko %VCPKG_ROOT% ali jo nastavite v nastavitvah UniGetUI.", "Vcpkg was not found on your system.": "Vcpkg ni bil najden v vašem sistemu.", "Verbose": "Podrobno", "Version": "Različica", "Version to install:": "Verzija za namestitev:", "Version:": null, "View GitHub Profile": "Ogled profila <PERSON>", "View WingetUI on GitHub": "Oglejte si WingetUI na GitHubu", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Oglejte si izvorno kodo WingetUI. Od tam lahko poročate o napakah ali predlagate funkcije ali pa dogodki prispevajo neposredno k projektu WingetUI", "View mode:": "<PERSON><PERSON><PERSON> pog<PERSON>:", "View on UniGetUI": "Ogled v UniGetUI", "View page on browser": "Oglej si stran v brskalniku", "View {0} logs": "Ogled dne<PERSON><PERSON> za {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, da se naprava poveže z internetom, preden začnete z opravili, ki zahtevajo povezavo.", "Waiting for other installations to finish...": "<PERSON><PERSON><PERSON>, da se druge namestitve končajo...", "Waiting for {0} to complete...": "Čakanje na zaključek {0}...", "Warning": "<PERSON><PERSON><PERSON><PERSON>", "Warning!": "Opozorilo!", "We are checking for updates.": "Preverjamo posodobitve.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Podrobnih informacij o tem paketu nismo mogli naložiti, ker ga ni bilo mogoče najti v nobenem od vaših virov paketov", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Podrobnih informacij o tem paketu nismo mogli naložiti, ker ni bil nameščen iz razpoložljivega upravitelja paketov.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON><PERSON> mogli {action} {package}. Prosim poskusite kasneje. Kliknite \"{showDetails}\", da dobite dnevnik namestitvenega programa.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON><PERSON> mogli {action} {package}. Prosim poskusite kasneje. Kliknite \"{showDetails}\", da dobite dnevnike programa za odstranjevanje.", "We couldn't find any package": "Nismo našli nobenega paketa", "Welcome to WingetUI": "Dobrodošli v WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON> so zaznane nove bliž<PERSON>, jih samodejno izbriši brez prikaza tega pogovornega okna.", "Which backup do you want to open?": null, "Which package managers do you want to use?": "Katere upravitelje paketov želite uporabiti?", "Which source do you want to add?": "<PERSON><PERSON> vir <PERSON> do<PERSON>?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Medtem ko se Winget lahko uporablja znotraj WingetUI, se WingetUI lahko uporablja z drugimi upravitelji paketov, kar je lahko zmedeno. V preteklosti je bil WingetUI zasnovan tako, da deluje samo z Wingetom, vendar to ni več res, zato WingetUI ne predstavlja tega, kar ta projekt želi postati.", "WinGet could not be repaired": "WinGet ni bilo mogoče popraviti", "WinGet malfunction detected": "<PERSON>aznana okvara WinGet", "WinGet was repaired successfully": "WinGet je bil uspešno popravljen", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "WingetUI - Vse je posodobljeno", "WingetUI - {0} updates are available": "WingetUI - Na voljo je {0} p<PERSON><PERSON><PERSON><PERSON>", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "Domača stran WingetUI", "WingetUI Homepage - Share this link!": "Domača stran WingetUI – delite to povezavo!", "WingetUI License": "Licenca WingetUI", "WingetUI Log": "Dnevnik WingetUI", "WingetUI Repository": "Repozitorij <PERSON>", "WingetUI Settings": "WingetUI nastavitve", "WingetUI Settings File": "Datoteka z nastavitvami WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI Uporablja naslednje knjižnice. Brez njih WingetUI ne bi bil mogoč.", "WingetUI Version {0}": "WingetUI različica {0}", "WingetUI autostart behaviour, application launch settings": "WingetUI vedenje samodejnega zagona, nastavitve zagona aplikacije", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI lahko preveri, ali ima vaša programska oprema na voljo posodobitve, in jih po želji samodejno namesti", "WingetUI display language:": "Prikazni jezik v WingetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI je bil zagnan kot skrbnik, kar ni priporočljivo. Ko WingetUI izvajate kot skrbnik, bo imela VSAKA operacija, zagnana iz WingetUI, skrbniške pravice. Še vedno lahko uporabljate program, vendar zelo priporočamo, da WingetUI ne izvajate s skrbniškimi pravicami.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI je bil zahvaljujoč prostovoljnim prevajalcem preveden v več kot 40 jezikov. Hvala 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI ni bil strojno preveden! Za prevode so bili zadolženi naslednji uporabniki:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI je aplikacija, ki poenostavi upravljanje vaše programske opreme z zagotavljanjem grafičnega vmesnika vse v enem za vaše upravitelje paketov v ukazni vrstici.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI se bo pre<PERSON>, da se poudari razlika med WingetUI (vmesnik, ki ga trenutno uporabljate) in Winget (upravitelj paketov, ki ga je razvil Microsoft in s katerim nisem povezan)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI se posodablja. Po končani operaciji se bo WingetUI ponovno zagnal", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI je brezplačen in bo brezplačen za vedno. <PERSON><PERSON>, brez kreditne kartice, brez premium različice. 100 % brezplačno, za vedno.", "WingetUI log": "WingetUI dnevnik", "WingetUI tray application preferences": "WingetUI nastavitve sistemske vrstice", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI uporablja naslednje knjižnice. Brez njih WingetUI ne bi bil mogoč.", "WingetUI version {0} is being downloaded.": "WingetUI različica {0} se prenaša.", "WingetUI will become {newname} soon!": "WingetUI bo kmalu postal {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI ne bo redno preverjal posodobitev. <PERSON><PERSON> vedno bodo preverjeni ob zagonu, vendar nanje ne boste opozorjeni.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI bo prikazal poziv UAC vsakič, ko bo paket zahteval namestitev nadgradnje.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI se bo kmalu imenoval {newname}. To ne pomeni nobene spremembe v aplikaciji. Jaz (razvijalec) bom nadaljeval z razvojem tega projekta, kot ga trenutno počnem, vendar pod drugim imenom.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI ne bi bil mogoč brez pomoči naših dragih vzdrževalcev. Oglejte si njihove GitHub profile, WingetUI brez njih ne bi bil mogoč!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI ne bi bil mogoč brez pomoči sodelavcev. Hvala vsem 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} je pripravljen za namestitev.", "Write here the process names here, separated by commas (,)": null, "Yes": "Da", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "Trenutno imate nameščeno različico {0}", "You have installed WingetUI Version {0}": "Namestili ste različico WingetUI {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "<PERSON>rda boste morali namestiti {pm}, če ga želite uporabljati z WingetUI.", "You may restart your computer later if you wish": "Če želite, lahko računalnik znova zaženete pozneje", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Poziv boste prejeli samo en<PERSON>t, skrb<PERSON>ške pravice pa bodo dodel<PERSON> pake<PERSON>, ki jih zahte<PERSON>.", "You will be prompted only once, and every future installation will be elevated automatically.": "Pozvani boste le enkrat in vsaka prihodnja namestitev bo samodejno dvignjena.", "You will likely need to interact with the installer.": "Verjetno boste morali sodelovati z namestitvenim programom.", "[RAN AS ADMINISTRATOR]": "ZAGNANO KOT SKRBNIK", "buy me a coffee": "kup<PERSON><PERSON> kavo", "extracted": "ekstrahirano", "feature": "funkcija", "formerly WingetUI": "prej <PERSON>", "homepage": "spletno stran", "install": "namesti", "installation": "namestitev", "installed": "<PERSON><PERSON><PERSON><PERSON>", "installing": "<PERSON><PERSON><PERSON><PERSON>", "library": "knjižnica", "mandatory": null, "option": "opcija", "optional": null, "uninstall": "odstrani", "uninstallation": "odstranitev", "uninstalled": "odstranjeno", "uninstalling": "odstranjujem", "update(noun)": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update(verb)": "poso<PERSON><PERSON>i", "updated": "posodobljeno", "updating": "posodabljanje", "version {0}": "različica {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} Odstranjevanje", "{0} aborted": "{0} prek<PERSON><PERSON>", "{0} can be updated": "{0} la<PERSON><PERSON> posodobite", "{0} can be updated to version {1}": "{0} je mogoče posodobiti na različico {1}", "{0} days": "{0} dni", "{0} desktop shortcuts created": "{0} ustvarjenih bližnjic na namizju", "{0} failed": "{0} spodletelo", "{0} has been installed successfully.": "{0} je bil us<PERSON><PERSON><PERSON>.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} je bil uspešno nameščen. Priporočljivo je, da znova zaženete UniGetUI, da dokončate namestitev", "{0} has failed, that was a requirement for {1} to be run": "{0} ni uspelo, kar je bilo potrebno za zagon {1}", "{0} homepage": "{0} do<PERSON><PERSON><PERSON> stran", "{0} hours": "{0} ur", "{0} installation": "{0} instalac<PERSON>", "{0} installation options": "Možnosti namestitve {0}", "{0} installer is being downloaded": "Namestitveni program za {0} se prenaša", "{0} is being installed": "{0} se <PERSON><PERSON><PERSON><PERSON>", "{0} is being uninstalled": "{0} se odstranjuje", "{0} is being updated": "{0} se posodablja", "{0} is being updated to version {1}": "{0} se posodablja na različico {1}", "{0} is disabled": "{0} je <PERSON><PERSON><PERSON><PERSON>", "{0} minutes": "{0} minut", "{0} months": "{0} mesecev", "{0} packages are being updated": "posodabljam {0} paketov", "{0} packages can be updated": "{0} pake<PERSON> lahko posodobite", "{0} packages found": "{0} na<PERSON><PERSON><PERSON> paketov", "{0} packages were found": "{0} p<PERSON><PERSON> je bilo najdenih", "{0} packages were found, {1} of which match the specified filters.": "Najdenih je bilo {0} p<PERSON><PERSON>, od katerih se {1} ujema z navedenimi filtri.", "{0} settings": "Nastavitve za {0}", "{0} status": "<PERSON><PERSON> {0}", "{0} succeeded": "{0} je <PERSON><PERSON><PERSON><PERSON>", "{0} update": "{0} p<PERSON><PERSON><PERSON><PERSON>", "{0} updates are available": "{0} p<PERSON><PERSON><PERSON><PERSON> je na voljo", "{0} was {1} successfully!": "{0} je {1} us<PERSON><PERSON><PERSON>!", "{0} weeks": "{0} te<PERSON><PERSON>", "{0} years": "{0} let", "{0} {1} failed": "{0} {1} spodletelo", "{package} Installation": "{package} Namestitev", "{package} Uninstall": "{package} Odstranitev", "{package} Update": "{package} Poso<PERSON>bitev", "{package} could not be installed": "{package} ni bilo mogoče namestiti", "{package} could not be uninstalled": "{package} ni bilo mogoče odstraniti", "{package} could not be updated": "{package} ni bilo mogoče posodobiti", "{package} installation failed": "Namestitev {package} ni uspela", "{package} installer could not be downloaded": "Namestitvenega programa za {package} ni bilo mogoče prenesti", "{package} installer download": "Prenos namestitvenega programa za {package}", "{package} installer was downloaded successfully": "Namestitveni program za {package} je bil uspešno prenesen", "{package} uninstall failed": "Odstranitev {package} ni uspela", "{package} update failed": "Posodobitev {package} ni uspela", "{package} update failed. Click here for more details.": "Posodobitev {package} ni uspela. Kliknite tukaj za več podrobnosti.", "{package} was installed successfully": "{package} je bil us<PERSON><PERSON><PERSON>", "{package} was uninstalled successfully": "{package} je bil uspešno odstranjen", "{package} was updated successfully": "{package} je bil us<PERSON><PERSON> posodobljen", "{pcName} installed packages": "{pcName} <PERSON><PERSON><PERSON><PERSON><PERSON>", "{pm} could not be found": "{pm} ni bilo mogoče najti", "{pm} found: {state}": "{pm} najdeno: {state}", "{pm} is disabled": "{pm} je one<PERSON><PERSON><PERSON>", "{pm} is enabled and ready to go": "{pm} je o<PERSON><PERSON><PERSON><PERSON> in pripravljen za uporabo", "{pm} package manager specific preferences": "{pm} specifične nastavitve upravitelja paketov", "{pm} preferences": "{pm} nastavitve", "{pm} version:": "{pm} različica:", "{pm} was not found!": "{pm} ni bilo mogoče najti!"}