<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.GeneralPages.PackageManagerPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages.GeneralPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    mc:Ignorable="d">

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel Orientation="Vertical">

            <widgets:CheckboxCard_Dict
                x:Name="EnableManager"
                CornerRadius="8"
                DictionaryName="DisabledManagers"
                FontSize="18"
                FontWeight="SemiBold" />

            <widgets:TranslatedTextBlock
                x:Name="StatusTitle"
                Margin="4,32,4,8"
                FontWeight="SemiBold" />

            <!--  STATUS INFO BAR  -->
            <InfoBar
                x:Name="ManagerStatusBar"
                CornerRadius="8,8,0,0"
                IsClosable="false"
                IsOpen="True">
                <InfoBar.ActionButton>
                    <HyperlinkButton x:Name="ShowVersionHyperlink" Click="ShowVersionHyperlink_Click">
                        <widgets:TranslatedTextBlock Text="Expand version" />
                    </HyperlinkButton>
                </InfoBar.ActionButton>
                <InfoBar.Content>
                    <TextBlock
                        Name="LongVersionTextBlock"
                        FontFamily="Consolas"
                        TextWrapping="Wrap"
                        Visibility="Collapsed" />
                </InfoBar.Content>
            </InfoBar>
            <controls:SettingsCard
                x:Name="ManagerExecutable"
                Padding="24,16,24,16"
                HorizontalContentAlignment="Left"
                BorderThickness="1,0,1,1"
                ContentAlignment="Vertical"
                CornerRadius="0,0,0,0">
                <controls:SettingsCard.Header>
                    <TextBlock Text="Select the executable to be used. The following list shows the executables found by UniGetUI" />
                </controls:SettingsCard.Header>
                <controls:SettingsCard.Content>
                    <Grid HorizontalAlignment="Left" RowSpacing="4" ColumnSpacing="4">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <widgets:TranslatedTextBlock
                            Grid.ColumnSpan="2"
                            FontSize="12"
                            FontWeight="SemiBold"
                            Opacity="0.7"
                            Text="Not finding the file you are looking for? Make sure it has been added to path." />
                        <ComboBox
                            x:Name="ExecutableComboBox"
                            Grid.Row="1"
                            Grid.ColumnSpan="2"
                            HorizontalAlignment="Stretch" />
                        <widgets:TranslatedTextBlock
                            x:Name="ExeFileWarningText"
                            Grid.Row="2"
                            VerticalAlignment="Center"
                            FontSize="12"
                            FontWeight="SemiBold"
                            Foreground="{ThemeResource SystemErrorTextColor}"
                            Opacity="0.7"
                            Text="For security reasons, changing the executable file is disabled by default"
                            WrappingMode="Wrap" />
                        <HyperlinkButton
                            x:Name="GoToSecureSettingsBtn"
                            Grid.Row="2"
                            Grid.Column="1"
                            Padding="0"
                            Click="GoToSecureSettingsBtn_Click">
                            <widgets:TranslatedTextBlock
                                FontSize="12"
                                FontWeight="SemiBold"
                                Text="Change this" />
                        </HyperlinkButton>
                    </Grid>
                </controls:SettingsCard.Content>
            </controls:SettingsCard>

            <controls:SettingsCard
                x:Name="ManagerPath"
                Padding="24,8,0,8"
                BorderThickness="1,0,1,1"
                Click="ManagerPath_Click"
                CornerRadius="0,0,8,8"
                IsClickEnabled="True">
                <controls:SettingsCard.ActionIcon>
                    <widgets:LocalIcon Icon="Empty" />
                </controls:SettingsCard.ActionIcon>
                <controls:SettingsCard.Content>
                    <SymbolIcon
                        Name="CopyButtonIcon"
                        Height="24"
                        Margin="0"
                        Symbol="Copy" />
                </controls:SettingsCard.Content>
                <controls:SettingsCard.Header>
                    <TextBlock
                        Grid.ColumnSpan="2"
                        FontSize="12"
                        FontWeight="SemiBold"
                        Opacity="0.7"
                        Text="Current executable file:" />
                </controls:SettingsCard.Header>
                <controls:SettingsCard.Description>
                    <TextBlock
                        Name="LocationLabel"
                        FontFamily="Consolas"
                        FontSize="14"
                        TextWrapping="Wrap" />
                </controls:SettingsCard.Description>
            </controls:SettingsCard>

            <widgets:TranslatedTextBlock
                x:Name="InstallOptionsTitle"
                Margin="4,32,4,8"
                FontWeight="SemiBold" />

            <controls:SettingsCard
                x:Name="InstallOptionsPanel"
                HorizontalContentAlignment="Stretch"
                CornerRadius="8" />

            <widgets:TranslatedTextBlock
                x:Name="SettingsTitle"
                Margin="4,32,4,8"
                FontWeight="SemiBold" />

            <UserControl
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                IsEnabled="{x:Bind EnableManager._checkbox.IsOn, Mode=OneWay}">
                <StackPanel
                    Name="ExtraControls"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Orientation="Vertical" />
            </UserControl>
            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Related settings" />

            <controls:SettingsCard
                x:Name="ManagerLogs"
                Click="ManagerLogs_Click"
                CornerRadius="8"
                IsClickEnabled="True">
                <controls:SettingsCard.HeaderIcon>
                    <widgets:LocalIcon Icon="Console" />
                </controls:SettingsCard.HeaderIcon>

                <controls:SettingsCard.Description>
                    <TextBlock
                        x:Name="ManagerLogsLabel"
                        FontFamily="Consolas"
                        FontSize="14"
                        TextWrapping="Wrap" />
                </controls:SettingsCard.Description>
            </controls:SettingsCard>

            <controls:SettingsCard
                x:Name="AppExecutionAliasWarning"
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,8,8"
                IsClickEnabled="False">
                <controls:SettingsCard.HeaderIcon>
                    <widgets:LocalIcon Icon="Settings" />
                </controls:SettingsCard.HeaderIcon>

                <controls:SettingsCard.Description>
                    <TextBlock
                        x:Name="AppExecutionAliasWarningLabel"
                        FontFamily="Consolas"
                        FontSize="14"
                        TextWrapping="Wrap" />
                </controls:SettingsCard.Description>
            </controls:SettingsCard>
        </StackPanel>
    </ScrollViewer>
</Page>
