<?xml version="1.0" encoding="utf-8" ?>
<Application
    x:Class="UniGetUI.MainApp"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:UniGetUI"
    xmlns:widgets="using:UniGetUI.Interface.Widgets">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
                <!--  Other merged dictionaries here  -->
            </ResourceDictionary.MergedDictionaries>
            <!--  Other app resources here  -->

            <Style
                x:Key="BetterMenuItem"
                BasedOn="{StaticResource DefaultMenuFlyoutItemStyle}"
                TargetType="MenuFlyoutItem">
                <Setter Property="CornerRadius" Value="4" />
                <Setter Property="VerticalContentAlignment" Value="Center" />
                <Setter Property="Height" Value="36" />
            </Style>

            <Style
                x:Key="BetterToggleMenuItem"
                BasedOn="{StaticResource DefaultToggleMenuFlyoutItemStyle}"
                TargetType="ToggleMenuFlyoutItem">
                <Setter Property="CornerRadius" Value="4" />
                <Setter Property="VerticalContentAlignment" Value="Center" />
                <Setter Property="Height" Value="36" />
            </Style>

            <FontFamily x:Key="SymbolFont">/Assets/Symbols/Font/fonts/UniGetUI-Symbols.ttf#UniGetUI-Symbols</FontFamily>


            <Style
                x:Name="BetterContextMenu"
                BasedOn="{StaticResource DefaultMenuFlyoutPresenterStyle}"
                TargetType="MenuFlyoutPresenter">
                <Setter Property="CornerRadius" Value="8" />
                <Setter Property="Margin" Value="0" />
                <Setter Property="BorderBrush" Value="{ThemeResource DividerStrokeColorDefaultBrush}" />
            </Style>

            <Style x:Key="BetterFlyoutPresenterStyle" TargetType="FlyoutPresenter">
                <Setter Property="Background" Value="{ThemeResource MenuFlyoutPresenterBackground}" />
                <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                <Setter Property="VerticalContentAlignment" Value="Stretch" />
                <Setter Property="IsTabStop" Value="False" />
                <Setter Property="BorderBrush" Value="{ThemeResource FlyoutBorderThemeBrush}" />
                <Setter Property="BorderThickness" Value="{ThemeResource FlyoutBorderThemeThickness}" />
                <Setter Property="Padding" Value="{StaticResource FlyoutContentPadding}" />
                <Setter Property="MinWidth" Value="{ThemeResource FlyoutThemeMinWidth}" />
                <Setter Property="MaxWidth" Value="{ThemeResource FlyoutThemeMaxWidth}" />
                <Setter Property="MinHeight" Value="{ThemeResource FlyoutThemeMinHeight}" />
                <Setter Property="MaxHeight" Value="{ThemeResource FlyoutThemeMaxHeight}" />
                <Setter Property="ScrollViewer.HorizontalScrollMode" Value="Auto" />
                <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
                <Setter Property="ScrollViewer.VerticalScrollMode" Value="Auto" />
                <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
                <Setter Property="ScrollViewer.ZoomMode" Value="Disabled" />
                <Setter Property="CornerRadius" Value="{ThemeResource OverlayCornerRadius}" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="FlyoutPresenter">
                            <Border
                                Background="{TemplateBinding Background}"
                                BackgroundSizing="InnerBorderEdge"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}">
                                <ScrollViewer
                                    x:Name="ScrollViewer"
                                    AutomationProperties.AccessibilityView="Raw"
                                    HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                                    HorizontalScrollMode="{TemplateBinding ScrollViewer.HorizontalScrollMode}"
                                    VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                                    VerticalScrollMode="{TemplateBinding ScrollViewer.VerticalScrollMode}"
                                    ZoomMode="{TemplateBinding ScrollViewer.ZoomMode}">
                                    <ContentPresenter
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        ContentTransitions="{TemplateBinding ContentTransitions}" />
                                </ScrollViewer>
                            </Border>

                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>



            <Style BasedOn="{StaticResource DefaultListViewItemStyle}" TargetType="TreeViewItem">
                <Setter Property="Padding" Value="0" />
                <Setter Property="Background" Value="{ThemeResource TreeViewItemBackground}" />
                <Setter Property="BorderBrush" Value="{ThemeResource TreeViewItemBorderBrush}" />
                <Setter Property="BorderThickness" Value="{ThemeResource TreeViewItemBorderThemeThickness}" />
                <Setter Property="GlyphBrush" Value="{ThemeResource TreeViewItemForeground}" />
                <Setter Property="MinHeight" Value="{ThemeResource TreeViewItemMinHeight}" />
                <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                <Setter Property="FocusVisualMargin" Value="0,-1,0,-1" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TreeViewItem">
                            <Grid
                                x:Name="ContentPresenterGrid"
                                Margin="{ThemeResource TreeViewItemPresenterMargin}"
                                Padding="{ThemeResource TreeViewItemPresenterPadding}"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}">
                                <Rectangle
                                    x:Name="SelectionIndicator"
                                    Width="3"
                                    Height="16"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Fill="{ThemeResource TreeViewItemSelectionIndicatorForeground}"
                                    Opacity="0"
                                    RadiusX="2"
                                    RadiusY="2" />
                                <Grid
                                    x:Name="MultiSelectGrid"
                                    Margin="{ThemeResource TreeViewItemMultiSelectSelectedItemBorderMargin}"
                                    Padding="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.Indentation}"
                                    BorderBrush="Transparent"
                                    BorderThickness="{ThemeResource TreeViewItemBorderThemeThickness}"
                                    CornerRadius="{ThemeResource ControlCornerRadius}">

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition />
                                    </Grid.ColumnDefinitions>
                                    <Grid Grid.Column="0">
                                        <CheckBox
                                            x:Name="MultiSelectCheckBox"
                                            Width="24"
                                            MinWidth="24"
                                            MinHeight="{ThemeResource TreeViewItemMultiSelectCheckBoxMinHeight}"
                                            Margin="10,0,0,0"
                                            VerticalAlignment="Center"
                                            AutomationProperties.AccessibilityView="Raw"
                                            IsTabStop="False"
                                            Visibility="Collapsed" />
                                        <Border
                                            x:Name="MultiArrangeOverlayTextBorder"
                                            Height="20"
                                            MinWidth="20"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Background="{ThemeResource SystemControlBackgroundAccentBrush}"
                                            BorderBrush="{ThemeResource SystemControlBackgroundChromeWhiteBrush}"
                                            BorderThickness="1"
                                            CornerRadius="{ThemeResource ControlCornerRadius}"
                                            IsHitTestVisible="False"
                                            Visibility="Collapsed">
                                            <TextBlock
                                                x:Name="MultiArrangeOverlayText"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                AutomationProperties.AccessibilityView="Raw"
                                                Foreground="{ThemeResource SystemControlForegroundChromeWhiteBrush}"
                                                IsHitTestVisible="False"
                                                Style="{ThemeResource CaptionTextBlockStyle}"
                                                Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.DragItemsCount}" />
                                        </Border>
                                    </Grid>
                                    <Grid
                                        x:Name="ExpandCollapseChevron"
                                        Grid.Column="1"
                                        Width="20"
                                        Padding="0,0,0,0"
                                        Background="Transparent"
                                        Opacity="{TemplateBinding GlyphOpacity}">
                                        <TextBlock
                                            x:Name="CollapsedGlyph"
                                            Width="12"
                                            Height="12"
                                            Padding="2"
                                            VerticalAlignment="Center"
                                            AutomationProperties.AccessibilityView="Raw"
                                            FontFamily="{StaticResource SymbolThemeFontFamily}"
                                            FontSize="{TemplateBinding GlyphSize}"
                                            Foreground="{TemplateBinding GlyphBrush}"
                                            IsHitTestVisible="False"
                                            IsTextScaleFactorEnabled="False"
                                            Text="{TemplateBinding CollapsedGlyph}"
                                            Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.CollapsedGlyphVisibility}" />
                                        <TextBlock
                                            x:Name="ExpandedGlyph"
                                            Width="12"
                                            Height="12"
                                            Padding="2"
                                            VerticalAlignment="Center"
                                            AutomationProperties.AccessibilityView="Raw"
                                            FontFamily="{StaticResource SymbolThemeFontFamily}"
                                            FontSize="{TemplateBinding GlyphSize}"
                                            Foreground="{TemplateBinding GlyphBrush}"
                                            IsHitTestVisible="False"
                                            IsTextScaleFactorEnabled="False"
                                            Text="{TemplateBinding ExpandedGlyph}"
                                            Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TreeViewItemTemplateSettings.ExpandedGlyphVisibility}" />
                                    </Grid>
                                    <ContentPresenter
                                        x:Name="ContentPresenter"
                                        Grid.Column="2"
                                        MinHeight="{ThemeResource TreeViewItemContentHeight}"
                                        Margin="{TemplateBinding Padding}"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        ContentTransitions="{TemplateBinding ContentTransitions}" />

                                </Grid>

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal" />

                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenterGrid.Background" Value="{ThemeResource TreeViewItemBackgroundPointerOver}" />
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TreeViewItemForegroundPointerOver}" />
                                                <Setter Target="SelectionIndicator.Fill" Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPointerOver}" />
                                                <Setter Target="CollapsedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundPointerOver}" />
                                                <Setter Target="ExpandedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundPointerOver}" />
                                                <Setter Target="ContentPresenterGrid.BorderBrush" Value="{ThemeResource TreeViewItemBorderBrushPointerOver}" />
                                                <Setter Target="SelectionIndicator.Opacity" Value="0" />

                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenterGrid.Background" Value="{ThemeResource TreeViewItemBackgroundPressed}" />
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TreeViewItemForegroundPressed}" />
                                                <Setter Target="SelectionIndicator.Fill" Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPressed}" />
                                                <Setter Target="CollapsedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundPressed}" />
                                                <Setter Target="ExpandedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundPressed}" />
                                                <Setter Target="ContentPresenterGrid.BorderBrush" Value="{ThemeResource TreeViewItemBorderBrushPressed}" />
                                                <Setter Target="SelectionIndicator.Opacity" Value="0" />

                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Selected">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenterGrid.Background" Value="{ThemeResource TreeViewItemBackgroundSelected}" />
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TreeViewItemForegroundSelected}" />
                                                <Setter Target="SelectionIndicator.Fill" Value="{ThemeResource TreeViewItemSelectionIndicatorForeground}" />
                                                <Setter Target="CollapsedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundSelected}" />
                                                <Setter Target="ExpandedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundSelected}" />
                                                <Setter Target="ContentPresenterGrid.BorderBrush" Value="{ThemeResource TreeViewItemBorderBrushSelected}" />
                                                <Setter Target="SelectionIndicator.Opacity" Value="1" />

                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="PointerOverSelected">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenterGrid.Background" Value="{ThemeResource TreeViewItemBackgroundSelectedPointerOver}" />
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TreeViewItemForegroundSelectedPointerOver}" />
                                                <Setter Target="SelectionIndicator.Fill" Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPointerOver}" />
                                                <Setter Target="CollapsedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundSelectedPointerOver}" />
                                                <Setter Target="ExpandedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundSelectedPointerOver}" />
                                                <Setter Target="ContentPresenterGrid.BorderBrush" Value="{ThemeResource TreeViewItemBorderBrushSelectedPointerOver}" />
                                                <Setter Target="SelectionIndicator.Opacity" Value="1" />

                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="PressedSelected">
                                            <VisualState.Setters>
                                                <Setter Target="ContentPresenterGrid.Background" Value="{ThemeResource TreeViewItemBackgroundSelectedPressed}" />
                                                <Setter Target="ContentPresenter.Foreground" Value="{ThemeResource TreeViewItemForegroundSelectedPressed}" />
                                                <Setter Target="SelectionIndicator.Fill" Value="{ThemeResource TreeViewItemSelectionIndicatorForegroundPressed}" />
                                                <Setter Target="CollapsedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundSelectedPressed}" />
                                                <Setter Target="ExpandedGlyph.Foreground" Value="{ThemeResource TreeViewItemForegroundSelectedPressed}" />
                                                <Setter Target="ContentPresenterGrid.BorderBrush" Value="{ThemeResource TreeViewItemBorderBrushSelectedPressed}" />
                                                <Setter Target="SelectionIndicator.Opacity" Value="1" />

                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="ReorderedPlaceholder">

                                            <Storyboard>
                                                <FadeOutThemeAnimation TargetName="MultiSelectGrid" />
                                            </Storyboard>
                                        </VisualState>

                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="TreeViewMultiSelectStates">
                                        <VisualState x:Name="TreeViewMultiSelectDisabled" />
                                        <VisualState x:Name="TreeViewMultiSelectEnabledUnselected">
                                            <VisualState.Setters>
                                                <Setter Target="MultiSelectCheckBox.Visibility" Value="Visible" />
                                                <Setter Target="ExpandCollapseChevron.Padding" Value="0,0,0,0" />
                                                <Setter Target="ContentPresenterGrid.Padding" Value="0" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="TreeViewMultiSelectEnabledSelected">
                                            <VisualState.Setters>
                                                <Setter Target="MultiSelectCheckBox.Visibility" Value="Visible" />
                                                <Setter Target="MultiSelectGrid.Background" Value="{ThemeResource TreeViewItemBackground}" />
                                                <Setter Target="MultiSelectGrid.BorderBrush" Value="{ThemeResource TreeViewItemMultiSelectBorderBrushSelected}" />
                                                <Setter Target="ExpandCollapseChevron.Padding" Value="0,0,0,0" />
                                                <Setter Target="ContentPresenterGrid.Padding" Value="0" />

                                            </VisualState.Setters>
                                        </VisualState>

                                    </VisualStateGroup>

                                    <VisualStateGroup x:Name="DragStates">
                                        <VisualState x:Name="NotDragging" />

                                        <VisualState x:Name="MultipleDraggingPrimary">
                                            <VisualState.Setters>
                                                <Setter Target="MultiSelectCheckBox.Opacity" Value="0" />
                                                <Setter Target="MultiArrangeOverlayTextBorder.Visibility" Value="Visible" />

                                            </VisualState.Setters>
                                        </VisualState>

                                    </VisualStateGroup>

                                </VisualStateManager.VisualStateGroups>
                            </Grid>

                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>


            <Style TargetType="AppBarButton">
                <Setter Property="Background" Value="{ThemeResource AppBarButtonBackground}" />
                <Setter Property="Foreground" Value="{ThemeResource AppBarButtonForeground}" />
                <Setter Property="BorderBrush" Value="{ThemeResource AppBarButtonBorderBrush}" />
                <Setter Property="HorizontalAlignment" Value="Left" />
                <Setter Property="VerticalAlignment" Value="Top" />
                <Setter Property="FontFamily" Value="{ThemeResource ContentControlThemeFontFamily}" />
                <Setter Property="FontWeight" Value="Normal" />
                <Setter Property="Width" Value="48" />
                <Setter Property="UseSystemFocusVisuals" Value="{StaticResource UseSystemFocusVisuals}" />
                <Setter Property="FocusVisualMargin" Value="-3" />
                <Setter Property="AllowFocusOnInteraction" Value="False" />
                <Setter Property="CornerRadius" Value="{ThemeResource ControlCornerRadius}" />
                <Setter Property="KeyboardAcceleratorPlacementMode" Value="Hidden" />
                <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="AppBarButton">
                            <Grid
                                x:Name="Root"
                                MinWidth="{TemplateBinding MinWidth}"
                                MaxWidth="{TemplateBinding MaxWidth}"
                                Background="Transparent">
                                <Border
                                    x:Name="AppBarButtonInnerBorder"
                                    Margin="{ThemeResource AppBarButtonInnerBorderMargin}"
                                    Background="{TemplateBinding Background}"
                                    BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Control.IsTemplateFocusTarget="True"
                                    CornerRadius="{TemplateBinding CornerRadius}">
                                    <Border.BackgroundTransition>
                                        <BrushTransition Duration="0:0:0.083" />
                                    </Border.BackgroundTransition>
                                </Border>
                                <Grid x:Name="ContentRoot" MinHeight="{ThemeResource AppBarThemeMinHeight}">

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <Viewbox
                                        x:Name="ContentViewbox"
                                        Height="24"
                                        Margin="0,12,0,12"
                                        HorizontalAlignment="Stretch"
                                        AutomationProperties.AccessibilityView="Raw">
                                        <ContentPresenter
                                            x:Name="Content"
                                            Content="{TemplateBinding Icon}"
                                            Foreground="{TemplateBinding Foreground}"
                                            ToolTipService.ToolTip="{TemplateBinding Label}" />
                                    </Viewbox>
                                    <TextBlock
                                        x:Name="TextLabel"
                                        Grid.Row="1"
                                        Margin="0,0,0,0"
                                        AutomationProperties.AccessibilityView="Raw"
                                        FontFamily="{TemplateBinding FontFamily}"
                                        FontSize="12"
                                        Foreground="{TemplateBinding Foreground}"
                                        Text="{TemplateBinding Label}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap" />
                                    <TextBlock
                                        x:Name="OverflowTextLabel"
                                        Margin="12,0,12,0"
                                        Padding="{ThemeResource AppBarButtonOverflowTextLabelPadding}"
                                        HorizontalAlignment="Stretch"
                                        VerticalAlignment="Center"
                                        AutomationProperties.AccessibilityView="Raw"
                                        FontFamily="{TemplateBinding FontFamily}"
                                        FontSize="{ThemeResource ControlContentThemeFontSize}"
                                        Foreground="{TemplateBinding Foreground}"
                                        Text="{TemplateBinding Label}"
                                        TextAlignment="Left"
                                        TextTrimming="Clip"
                                        TextWrapping="NoWrap"
                                        Visibility="Collapsed" />
                                    <TextBlock
                                        x:Name="KeyboardAcceleratorTextLabel"
                                        Grid.Column="1"
                                        MinWidth="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.KeyboardAcceleratorTextMinWidth}"
                                        Margin="24,0,12,0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        AutomationProperties.AccessibilityView="Raw"
                                        Foreground="{ThemeResource AppBarButtonKeyboardAcceleratorTextForeground}"
                                        Style="{ThemeResource CaptionTextBlockStyle}"
                                        Text="{TemplateBinding KeyboardAcceleratorTextOverride}"
                                        Visibility="Collapsed" />
                                    <Grid
                                        x:Name="SubItemChevronPanel"
                                        Grid.Column="2"
                                        Visibility="Collapsed">
                                        <FontIcon
                                            x:Name="SubItemChevron"
                                            Margin="{ThemeResource AppBarButtonSubItemChevronMargin}"
                                            VerticalAlignment="Top"
                                            AutomationProperties.AccessibilityView="Raw"
                                            FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                            FontSize="{ThemeResource AppBarButtonSubItemChevronFontSize}"
                                            Foreground="{ThemeResource AppBarButtonSubItemChevronForeground}"
                                            Glyph="{ThemeResource AppBarButtonFlyoutGlyph}"
                                            MirroredWhenRightToLeft="True" />
                                        <FontIcon
                                            x:Name="OverflowSubItemChevron"
                                            Margin="{ThemeResource AppBarButtonSecondarySubItemChevronMargin}"
                                            VerticalAlignment="Center"
                                            AutomationProperties.AccessibilityView="Raw"
                                            FontFamily="{ThemeResource SymbolThemeFontFamily}"
                                            FontSize="{ThemeResource AppBarButtonSecondarySubItemChevronFontSize}"
                                            Foreground="{ThemeResource AppBarButtonSubItemChevronForeground}"
                                            Glyph="{ThemeResource AppBarButtonOverflowFlyoutGlyph}"
                                            MirroredWhenRightToLeft="True"
                                            Visibility="Collapsed" />
                                    </Grid>

                                </Grid>

                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="ApplicationViewStates">
                                        <VisualState x:Name="FullSize" />
                                        <VisualState x:Name="Compact">
                                            <VisualState.Setters>
                                                <Setter Target="AppBarButtonInnerBorder.Margin" Value="{StaticResource AppBarButtonInnerBorderCompactMargin}" />

                                            </VisualState.Setters>

                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TextLabel" Storyboard.TargetProperty="Visibility">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="Collapsed" />
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="LabelOnRight">

                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentViewbox" Storyboard.TargetProperty="Margin">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="12,12,0,12" />
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentRoot" Storyboard.TargetProperty="MinHeight">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource AppBarThemeCompactHeight}" />
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TextLabel" Storyboard.TargetProperty="(Grid.Row)">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="0" />
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TextLabel" Storyboard.TargetProperty="(Grid.Column)">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="1" />
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TextLabel" Storyboard.TargetProperty="TextAlignment">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="Left" />
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TextLabel" Storyboard.TargetProperty="Margin">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{StaticResource AppBarButtonTextLabelOnRightMargin}" />
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="SubItemChevron" Storyboard.TargetProperty="Margin">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource AppBarButtonSubItemChevronLabelOnRightMargin}" />
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="LabelCollapsed">

                                            <Storyboard>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="ContentRoot" Storyboard.TargetProperty="MinHeight">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="{ThemeResource AppBarThemeCompactHeight}" />
                                                </ObjectAnimationUsingKeyFrames>
                                                <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TextLabel" Storyboard.TargetProperty="Visibility">
                                                    <DiscreteObjectKeyFrame KeyTime="0" Value="Collapsed" />
                                                </ObjectAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                        <VisualState x:Name="Overflow">
                                            <VisualState.Setters>
                                                <Setter Target="ContentRoot.MinHeight" Value="0" />
                                                <Setter Target="AppBarButtonInnerBorder.Margin" Value="{StaticResource AppBarButtonInnerBorderOverflowMargin}" />
                                                <Setter Target="ContentViewbox.Visibility" Value="Collapsed" />
                                                <Setter Target="TextLabel.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowTextLabel.Visibility" Value="Visible" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="OverflowWithToggleButtons">
                                            <VisualState.Setters>
                                                <Setter Target="ContentRoot.MinHeight" Value="0" />
                                                <Setter Target="AppBarButtonInnerBorder.Margin" Value="{StaticResource AppBarButtonInnerBorderOverflowMargin}" />
                                                <Setter Target="ContentViewbox.Visibility" Value="Collapsed" />
                                                <Setter Target="TextLabel.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowTextLabel.Visibility" Value="Visible" />
                                                <Setter Target="OverflowTextLabel.Margin" Value="38,0,12,0" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="OverflowWithMenuIcons">
                                            <VisualState.Setters>
                                                <Setter Target="ContentRoot.MinHeight" Value="0" />
                                                <Setter Target="AppBarButtonInnerBorder.Margin" Value="{StaticResource AppBarButtonInnerBorderOverflowMargin}" />
                                                <Setter Target="ContentViewbox.HorizontalAlignment" Value="Left" />
                                                <Setter Target="ContentViewbox.VerticalAlignment" Value="Center" />
                                                <Setter Target="ContentViewbox.Width" Value="24" />
                                                <Setter Target="ContentViewbox.Height" Value="24" />
                                                <Setter Target="ContentViewbox.Margin" Value="12,0,8,0" />
                                                <Setter Target="TextLabel.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowTextLabel.Visibility" Value="Visible" />
                                                <Setter Target="OverflowTextLabel.Margin" Value="38,0,12,0" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="OverflowWithToggleButtonsAndMenuIcons">
                                            <VisualState.Setters>
                                                <Setter Target="ContentRoot.MinHeight" Value="0" />
                                                <Setter Target="AppBarButtonInnerBorder.Margin" Value="{StaticResource AppBarButtonInnerBorderOverflowMargin}" />
                                                <Setter Target="ContentViewbox.HorizontalAlignment" Value="Left" />
                                                <Setter Target="ContentViewbox.VerticalAlignment" Value="Center" />
                                                <Setter Target="ContentViewbox.Width" Value="24" />
                                                <Setter Target="ContentViewbox.Height" Value="24" />
                                                <Setter Target="ContentViewbox.Margin" Value="34,0,8,0" />
                                                <Setter Target="TextLabel.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowTextLabel.Visibility" Value="Visible" />
                                                <Setter Target="OverflowTextLabel.Margin" Value="76,0,12,0" />

                                            </VisualState.Setters>
                                        </VisualState>

                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal" />

                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="AppBarButtonInnerBorder.Background" Value="{ThemeResource AppBarButtonBackgroundPointerOver}" />
                                                <Setter Target="AppBarButtonInnerBorder.BorderBrush" Value="{ThemeResource AppBarButtonBorderBrushPointerOver}" />
                                                <Setter Target="Content.Foreground" Value="{ThemeResource AppBarButtonForegroundPointerOver}" />
                                                <Setter Target="TextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPointerOver}" />
                                                <Setter Target="OverflowTextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPointerOver}" />
                                                <Setter Target="SubItemChevron.Foreground" Value="{ThemeResource AppBarButtonSubItemChevronForegroundPointerOver}" />
                                                <Setter Target="KeyboardAcceleratorTextLabel.Foreground" Value="{ThemeResource AppBarButtonKeyboardAcceleratorTextForegroundPointerOver}" />

                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="AppBarButtonInnerBorder.Background" Value="{ThemeResource AppBarButtonBackgroundPressed}" />
                                                <Setter Target="AppBarButtonInnerBorder.BorderBrush" Value="{ThemeResource AppBarButtonBorderBrushPressed}" />
                                                <Setter Target="Content.Foreground" Value="{ThemeResource AppBarButtonForegroundPressed}" />
                                                <Setter Target="TextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPressed}" />
                                                <Setter Target="OverflowTextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPressed}" />
                                                <Setter Target="SubItemChevron.Foreground" Value="{ThemeResource AppBarButtonSubItemChevronForegroundPressed}" />
                                                <Setter Target="KeyboardAcceleratorTextLabel.Foreground" Value="{ThemeResource AppBarButtonKeyboardAcceleratorTextForegroundPressed}" />

                                            </VisualState.Setters>
                                        </VisualState>

                                        <VisualState x:Name="Disabled">
                                            <VisualState.Setters>
                                                <Setter Target="AppBarButtonInnerBorder.Background" Value="{ThemeResource AppBarButtonBackgroundDisabled}" />
                                                <Setter Target="AppBarButtonInnerBorder.BorderBrush" Value="{ThemeResource AppBarButtonBorderBrushDisabled}" />
                                                <Setter Target="Content.Foreground" Value="{ThemeResource AppBarButtonForegroundDisabled}" />
                                                <Setter Target="TextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundDisabled}" />
                                                <Setter Target="OverflowTextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundDisabled}" />
                                                <Setter Target="SubItemChevron.Foreground" Value="{ThemeResource AppBarButtonSubItemChevronForegroundDisabled}" />
                                                <Setter Target="KeyboardAcceleratorTextLabel.Foreground" Value="{ThemeResource AppBarButtonKeyboardAcceleratorTextForegroundDisabled}" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="OverflowNormal">
                                            <VisualState.Setters>
                                                <Setter Target="SubItemChevron.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowSubItemChevron.Visibility" Value="Visible" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="OverflowPointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="AppBarButtonInnerBorder.Background" Value="{ThemeResource AppBarButtonBackgroundPointerOver}" />
                                                <Setter Target="AppBarButtonInnerBorder.BorderBrush" Value="{ThemeResource AppBarButtonBorderBrushPointerOver}" />
                                                <Setter Target="Content.Foreground" Value="{ThemeResource AppBarButtonForegroundPointerOver}" />
                                                <Setter Target="TextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPointerOver}" />
                                                <Setter Target="OverflowTextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPointerOver}" />
                                                <Setter Target="KeyboardAcceleratorTextLabel.Foreground" Value="{ThemeResource AppBarButtonKeyboardAcceleratorTextForegroundPointerOver}" />
                                                <Setter Target="SubItemChevron.Foreground" Value="{ThemeResource AppBarButtonSubItemChevronForegroundPointerOver}" />
                                                <Setter Target="SubItemChevron.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowSubItemChevron.Visibility" Value="Visible" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="OverflowPressed">
                                            <VisualState.Setters>
                                                <Setter Target="AppBarButtonInnerBorder.Background" Value="{ThemeResource AppBarButtonBackgroundPressed}" />
                                                <Setter Target="AppBarButtonInnerBorder.BorderBrush" Value="{ThemeResource AppBarButtonBorderBrushPressed}" />
                                                <Setter Target="Content.Foreground" Value="{ThemeResource AppBarButtonForegroundPressed}" />
                                                <Setter Target="TextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPressed}" />
                                                <Setter Target="OverflowTextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundPressed}" />
                                                <Setter Target="KeyboardAcceleratorTextLabel.Foreground" Value="{ThemeResource AppBarButtonKeyboardAcceleratorTextForegroundPressed}" />
                                                <Setter Target="SubItemChevron.Foreground" Value="{ThemeResource AppBarButtonSubItemChevronForegroundPressed}" />
                                                <Setter Target="SubItemChevron.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowSubItemChevron.Visibility" Value="Visible" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="OverflowSubMenuOpened">
                                            <VisualState.Setters>
                                                <Setter Target="AppBarButtonInnerBorder.Background" Value="{ThemeResource AppBarButtonBackgroundSubMenuOpened}" />
                                                <Setter Target="AppBarButtonInnerBorder.BorderBrush" Value="{ThemeResource AppBarButtonBorderBrushSubMenuOpened}" />
                                                <Setter Target="Content.Foreground" Value="{ThemeResource AppBarButtonForegroundSubMenuOpened}" />
                                                <Setter Target="TextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundSubMenuOpened}" />
                                                <Setter Target="OverflowTextLabel.Foreground" Value="{ThemeResource AppBarButtonForegroundSubMenuOpened}" />
                                                <Setter Target="KeyboardAcceleratorTextLabel.Foreground" Value="{ThemeResource AppBarButtonKeyboardAcceleratorTextForegroundSubMenuOpened}" />
                                                <Setter Target="SubItemChevron.Foreground" Value="{ThemeResource AppBarButtonSubItemChevronForegroundSubMenuOpened}" />
                                                <Setter Target="SubItemChevron.Visibility" Value="Collapsed" />
                                                <Setter Target="OverflowSubItemChevron.Visibility" Value="Visible" />

                                            </VisualState.Setters>
                                        </VisualState>

                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="InputModeStates">
                                        <VisualState x:Name="InputModeDefault" />
                                        <VisualState x:Name="TouchInputMode">
                                            <VisualState.Setters>
                                                <Setter Target="OverflowTextLabel.Padding" Value="{ThemeResource AppBarButtonOverflowTextTouchMargin}" />

                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="GameControllerInputMode">
                                            <VisualState.Setters>
                                                <Setter Target="OverflowTextLabel.Padding" Value="{ThemeResource AppBarButtonOverflowTextTouchMargin}" />

                                            </VisualState.Setters>
                                        </VisualState>

                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="KeyboardAcceleratorTextVisibility">
                                        <VisualState x:Name="KeyboardAcceleratorTextCollapsed" />
                                        <VisualState x:Name="KeyboardAcceleratorTextVisible">
                                            <VisualState.Setters>
                                                <Setter Target="KeyboardAcceleratorTextLabel.Visibility" Value="Visible" />

                                            </VisualState.Setters>
                                        </VisualState>

                                    </VisualStateGroup>
                                    <VisualStateGroup x:Name="FlyoutStates">
                                        <VisualState x:Name="NoFlyout" />
                                        <VisualState x:Name="HasFlyout">
                                            <VisualState.Setters>
                                                <Setter Target="SubItemChevronPanel.Visibility" Value="{ThemeResource AppBarButtonHasFlyoutChevronVisibility}" />

                                            </VisualState.Setters>
                                        </VisualState>

                                    </VisualStateGroup>

                                </VisualStateManager.VisualStateGroups>
                            </Grid>

                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>
