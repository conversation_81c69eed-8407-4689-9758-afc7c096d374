<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Interface.Dialogs.PackageDetailsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Interface.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:toolkit="using:CommunityToolkit.WinUI.Controls"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    xmlns:xaml="using:Microsoft.UI.Xaml.Controls"
    mc:Ignorable="d">

    <Grid>
        <ScrollViewer
            Margin="0,0,-15,0"
            Padding="0,0,15,0"
            HorizontalAlignment="Stretch">
            <Grid
                Name="MainGrid"
                ColumnSpacing="32"
                RowSpacing="16">

                <Grid Name="LeftPanel" RowSpacing="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                </Grid>
                <Grid Name="RightPanel" RowSpacing="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                </Grid>
                <!--  Title + Icon  -->
                <Grid
                    Name="TitlePanel"
                    HorizontalAlignment="Stretch"
                    ColumnSpacing="8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Border
                        Grid.Column="0"
                        Width="128"
                        Height="128"
                        Background="{ThemeResource ApplicationPageBackgroundThemeBrush}"
                        BorderThickness="10"
                        CornerRadius="24">
                        <Image
                            Name="PackageIcon"
                            Width="80"
                            Height="80"
                            Margin="8" />
                    </Border>

                    <TextBlock
                        Name="PackageName"
                        Grid.Column="1"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        FontFamily="Segoe UI Variable Display"
                        FontSize="50"
                        FontWeight="Bold"
                        TextWrapping="Wrap" />
                </Grid>

                <!--  Tags + Description  -->
                <StackPanel
                    Name="DescriptionPanel"
                    VerticalAlignment="Top"
                    Orientation="Vertical">
                    <xaml:ItemsRepeater Name="TagGridView" ItemsSource="{x:Bind ShowableTags}">
                        <xaml:ItemsRepeater.ItemTemplate>
                            <DataTemplate x:DataType="TextBlock">
                                <Border
                                    Height="24"
                                    Margin="2"
                                    Padding="8,2,8,2"
                                    VerticalAlignment="Center"
                                    Background="{ThemeResource AccentAAFillColorDefaultBrush}"
                                    CornerRadius="12">
                                    <TextBlock Foreground="{ThemeResource TextOnAccentAAFillColorPrimary}" Text="{x:Bind Text}" />
                                </Border>
                            </DataTemplate>
                        </xaml:ItemsRepeater.ItemTemplate>
                        <xaml:ItemsRepeater.Layout>
                            <toolkit:WrapLayout x:Name="Wrap" />
                        </xaml:ItemsRepeater.Layout>
                    </xaml:ItemsRepeater>
                </StackPanel>

                <!--  Basic Info Panel  -->
                <RichTextBlock
                    Name="BasicInfoPanelText"
                    VerticalAlignment="Top"
                    LineHeight="25"
                    TextWrapping="Wrap">
                    <Paragraph>
                        <Run x:Name="DescriptionContent" />
                        <LineBreak />
                        <LineBreak />
                        <Run x:Name="HomepageUrl_Label" FontWeight="Bold">HOMEPAGE_LABEL:</Run>
                        <Hyperlink x:Name="HomepageUrl_Content" NavigateUri="about:blank">Helo</Hyperlink>
                        <LineBreak />
                        <Run x:Name="Publisher_Label" FontWeight="Bold">PUBLISHER_LABEL:</Run>
                        <Run x:Name="Publisher_Content">Helo</Run>
                        <LineBreak />
                        <Run x:Name="Author_Label" FontWeight="Bold">AUTHOR_LABEL:</Run>
                        <Run x:Name="Author_Content">Helo</Run>
                        <LineBreak />
                        <Run x:Name="License_Label" FontWeight="Bold">LICENSE_LABEL:</Run>
                        <Run x:Name="License_Content_Text">Helo</Run>
                        <Hyperlink x:Name="License_Content_Uri" NavigateUri="about:blank">(Helo)</Hyperlink>
                        <LineBreak />
                        <Run x:Name="UpdateDate_Label" FontWeight="Bold">Update date:</Run>
                        <Run x:Name="UpdateDate_Content">Helo</Run>
                        <LineBreak />
                        <Run x:Name="Source_Label" FontWeight="Bold">SOURCE_LABEL:</Run>
                        <Run x:Name="Source_Content">Helo</Run>
                    </Paragraph>
                </RichTextBlock>

                <!--  Install and Share button  -->
                <Grid
                    Name="ActionsPanel"
                    VerticalAlignment="Top"
                    ColumnSpacing="8"
                    RowSpacing="8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="10000*" MaxWidth="250" />
                        <ColumnDefinition Width="*" MinWidth="16" />
                        <ColumnDefinition Width="10000*" MaxWidth="250" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Button
                        Name="ShareButton"
                        Grid.Column="0"
                        Height="40"
                        HorizontalAlignment="Stretch"
                        HorizontalContentAlignment="Center"
                        Click="ShareButton_Click"
                        CornerRadius="8">
                        <StackPanel
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Orientation="Horizontal"
                            Spacing="8">
                            <widgets:LocalIcon
                                Width="20"
                                Height="20"
                                Icon="share" />
                            <widgets:TranslatedTextBlock VerticalAlignment="Center" Text="Share this package" />
                        </StackPanel>
                    </Button>


                    <Grid Grid.Column="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <Button
                            Name="MainActionButton"
                            Grid.Column="0"
                            Height="40"
                            HorizontalAlignment="Stretch"
                            Click="ActionButton_Click"
                            Content="Action!"
                            CornerRadius="8,0,0,8"
                            Style="{StaticResource AccentButtonStyle}" />

                        <DropDownButton
                            Name="ExtendedActionButton"
                            Grid.Column="1"
                            Width="30"
                            Height="40"
                            Margin="1,0,0,0"
                            Padding="0,0,2,0"
                            CornerRadius="0,8,8,0"
                            Style="{StaticResource AccentButtonStyle}">
                            <DropDownButton.Content>
                                <FontIcon FontSize="13" Glyph="&#xE70D;" />
                            </DropDownButton.Content>
                            <DropDownButton.Flyout>
                                <widgets:BetterMenu x:Name="ExtendedActionsMenu" Placement="BottomEdgeAlignedRight" />
                            </DropDownButton.Flyout>
                        </DropDownButton>
                    </Grid>
                </Grid>

                <!--  Screenshots  -->
                <StackPanel
                    Name="ScreenshotsPanel"
                    Orientation="Vertical"
                    Spacing="8">
                    <FlipView Name="ScreenshotsCarroussel" CornerRadius="8">
                        <FlipView.Items>
                            <FlipViewItem>
                                <Grid Margin="30,10,30,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <widgets:TranslatedTextBlock
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        Text="This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database."
                                        WrappingMode="WrapWholeWords" />
                                    <HyperlinkButton
                                        Grid.Column="1"
                                        VerticalAlignment="Stretch"
                                        NavigateUri="https://marticliment.com/unigetui/help/icons-and-screenshots/#about-icons">
                                        <widgets:TranslatedTextBlock VerticalAlignment="Center" Text="Become a contributor" />
                                    </HyperlinkButton>
                                </Grid>
                            </FlipViewItem>
                        </FlipView.Items>
                    </FlipView>
                    <PipsPager
                        x:Name="FlipViewPipsPager"
                        Margin="0"
                        HorizontalAlignment="Center"
                        NumberOfPages="{x:Bind ScreenshotsCarroussel.Items.Count}"
                        SelectedPageIndex="{x:Bind Path=ScreenshotsCarroussel.SelectedIndex, Mode=TwoWay}" />

                    <Grid Name="IconsExtraBanner" Visibility="Collapsed">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <widgets:TranslatedTextBlock
                            Grid.Column="0"
                            Text="This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database."
                            WrappingMode="WrapWholeWords" />
                        <HyperlinkButton
                            Grid.Column="1"
                            VerticalAlignment="Stretch"
                            NavigateUri="https://marticliment.com/unigetui/help/icons-and-screenshots/#about-icons">
                            <widgets:TranslatedTextBlock VerticalAlignment="Center" Text="Become a contributor" />
                        </HyperlinkButton>
                    </Grid>
                </StackPanel>

                <!--  Install Options  -->
                <Border Name="InstallOptionsBorder">
                    <Expander
                        Name="InstallOptionsExpander"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Top"
                        HorizontalContentAlignment="Stretch"
                        VerticalContentAlignment="Stretch"
                        CornerRadius="8">
                        <Expander.Header>
                            <Grid ColumnSpacing="12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <widgets:LocalIcon Height="24" Icon="options" />
                                <widgets:TranslatedTextBlock
                                    Grid.Column="1"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    FontWeight="SemiBold"
                                    Text="Installation options" />
                                <Button
                                    x:Name="SaveInstallOptionsButton"
                                    Grid.Column="2"
                                    Height="34"
                                    MinWidth="60"
                                    Click="SaveInstallOptionsButton_Click"
                                    CornerRadius="6"
                                    IsEnabled="{x:Bind InstallOptionsExpander.IsExpanded, Mode=OneWay}"
                                    Style="{ThemeResource AccentButtonStyle}">
                                    <widgets:TranslatedTextBlock Text="Save" />
                                </Button>
                            </Grid>
                        </Expander.Header>
                    </Expander>
                </Border>

                <!--  Package Details  -->
                <RichTextBlock
                    Name="DetailsPanelText"
                    VerticalAlignment="Top"
                    LineHeight="25"
                    TextWrapping="Wrap">
                    <Paragraph>
                        <Run x:Name="PackageId_Label" FontWeight="Bold">Package Id:</Run>
                        <Run x:Name="PackageId_Content">Helo</Run>
                        <LineBreak />
                        <Run x:Name="ManifestUrl_Label" FontWeight="Bold">Manifest: :</Run>
                        <Hyperlink x:Name="ManifestUrl_Content" NavigateUri="about:blank">Helo</Hyperlink>
                        <LineBreak />
                        <Run x:Name="Version_Label" FontWeight="Bold">(Installed) Version:</Run>
                        <Run x:Name="Version_Content">Helo</Run>
                        <LineBreak />
                        <LineBreak />
                        <Run x:Name="InstallerType_Label" FontWeight="Bold">Installer type:</Run>
                        <Run x:Name="InstallerType_Content">Helo</Run>
                        <LineBreak />
                        <Run x:Name="InstallerUrl_Label" FontWeight="Bold">Installer Url:</Run>
                        <Hyperlink x:Name="InstallerUrl_Content" NavigateUri="about:blank">(Helo)</Hyperlink>
                        <LineBreak />
                        <Run x:Name="InstallerHash_Label" FontWeight="Bold">Installer hash:</Run>
                        <Run x:Name="InstallerHash_Content">Helo</Run>
                        <LineBreak />
                        <Hyperlink
                            x:Name="DownloadInstaller_Button"
                            FontWeight="Bold"
                            TextDecorations="None">
                            Download installer
                        </Hyperlink>
                        <Run x:Name="InstallerSize_Content" />
                        <LineBreak />
                        <LineBreak />
                        <Run x:Name="Dependencies_Label" FontWeight="Bold">Dependencies:</Run>
                    </Paragraph>
                    <Paragraph x:Name="DependenciesParagraph"/>
                    <Paragraph>
                        <LineBreak />
                        <Run x:Name="ReleaseNotes_Label" FontWeight="Bold">Release Notes:</Run>
                        <Run x:Name="ReleaseNotes_Content">Helo</Run>
                        <LineBreak />
                        <Run x:Name="ReleaseNotesUrl_Label" FontWeight="Bold">Release notes Url:</Run>
                        <Hyperlink x:Name="ReleaseNotesUrl_Content" NavigateUri="about:blank">(Helo)</Hyperlink>
                    </Paragraph>
                </RichTextBlock>

            </Grid>
        </ScrollViewer>
        <widgets:DialogCloseButton Margin="0,-24,-24,0" Click="CloseButton_Click" />
        <ProgressBar
            Name="LoadingIndicator"
            Margin="-30,-24,-30,0"
            VerticalAlignment="Top"
            IsIndeterminate="True" />
    </Grid>
</Page>
