<Project>
    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <TargetFramework>net8.0-windows10.0.26100.0</TargetFramework>
        <TargetPlatformMinVersion>10.0.19041.0</TargetPlatformMinVersion>
        <!-- IF BUILD FAILS DUE TO MISSING Microsoft.Management.Deployment NAMESPACE,
            TOGGLE THE LAST NUMBER OF THE LINE BELOW 1 UNIT UP OR DOWN, AND REBUILD-->
        <WindowsSdkPackageVersion>10.0.26100.57</WindowsSdkPackageVersion>

        <SdkVersion>8.0.407</SdkVersion>
        <Authors><PERSON><PERSON> and the contributors</Authors>
        <PublisherName><PERSON><PERSON> Climent</PublisherName>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup>
        <WindowsAppSDKSelfContained>true</WindowsAppSDKSelfContained>
        <RuntimeIdentifiers>win-x64;win-arm64</RuntimeIdentifiers>
        <RuntimeIdentifier>win-$(Platform)</RuntimeIdentifier>
        <Platforms>x64</Platforms>
        <PublishSelfContained>true</PublishSelfContained>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <Configurations>Debug;Release</Configurations>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)' == 'Release'">
        <Optimize>true</Optimize>
        <WholeProgramOptimization>true</WholeProgramOptimization>
        <TieredCompilation>true</TieredCompilation>
        <PublishReadyToRun>true</PublishReadyToRun>
        <EnableProfileOptimization>true</EnableProfileOptimization>
    </PropertyGroup>

    <PropertyGroup>
        <LangVersion>latest</LangVersion>
        <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    </PropertyGroup>
</Project>
