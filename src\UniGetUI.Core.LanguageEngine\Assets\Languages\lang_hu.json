{"\"{0}\" is a local package and can't be shared": "\"{0}\" egy helyi csomag és nem osztható meg.", "\"{0}\" is a local package and does not have available details": "\"{0}\" egy helyi csomag és nem rendelkezik elérhető részletekkel", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" egy he<PERSON><PERSON>, és nem kompatibilis ezzel a funkcióval.", "(Last checked: {0})": "(<PERSON><PERSON><PERSON><PERSON>: {0})", "(Number {0} in the queue)": "({0} a várós<PERSON><PERSON>)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@gidano", "0 packages found": "0 csomag található", "0 updates found": "0 talált frissítés", "1 - Errors": "<PERSON><PERSON><PERSON>", "1 day": "1 nap", "1 hour": "1 óra", "1 month": "1 hónap", "1 package was found": "1 csomagot találtunk", "1 update is available": "1 frissítés érhető el", "1 week": "1 hét", "1 year": "1 év", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a \"{0}\" vagy \"{1}\" old<PERSON>ra.", "2 - Warnings": "Figyelmeztetések", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON> meg a köteghez hozzáadni kívánt csomago(ka)t, és jelölje be a bal oldali jelölőnégyzetet.", "3 - Information (less)": "Információ (kevesebb)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. Amikor a köteghez hozzáadni kívánt csomagok ki vannak jelölve, keresse meg az eszköztáron a \"{0}\" opci<PERSON>t, és kattintson rá.", "4 - Information (more)": "Információ (több)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. A köteghez hozzáadásra kerülnek a csomagok. Folytathatja a csomagok hozzáadását, vagy export<PERSON>lhatja a csomagot.", "5 - information (debug)": "Információ (hibakeresés)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Egy népszerű C/C++ köny<PERSON>t<PERSON><PERSON> kezel<PERSON>. Tele van C/C++ könyvtárakkal és egyéb C/C++hoz kapcsolódó segésprogramokkal<br>Tartalma: <b>C/C++ könyvt<PERSON>rak és kapcsolódó segépdprogramok</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "A Microsoft .NET ökoszisztémára tervezett eszközök és futtatható fájlok tárháza.<br>Tatalma: <b>.NET kapcsolódó eszközök és szkriptek</b>\n", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "A Microsoft .NET ökoszisztémáját szem előtt tartva tervezett eszközökkel teli tárház.<br>Tartalmaz: <b>.NET-hez kapcsolódó eszközöket</b>", "A restart is required": "Újraindításra van szükség", "Abort install if pre-install command fails": "A telepítés megszakítása, ha a telepítést megelőző parancs sikertelenül működik", "Abort uninstall if pre-uninstall command fails": "Az eltávolítás me<PERSON>zakítása, ha az eltávolítás el<PERSON>tti parancs sikertelen", "Abort update if pre-update command fails": "A frissítés me<PERSON>zakítása, ha a frissítést megelőző parancs sikertelenül működik", "About": "<PERSON><PERSON><PERSON><PERSON>", "About Qt6": "A Qt6-ról", "About WingetUI": "A WingetUI-ról", "About WingetUI version {0}": "A WingetUI {0} verziójáról", "About the dev": "A fejlesztőről", "Accept": "Elfogad", "Action when double-clicking packages, hide successful installations": "Művelet a csomagokra való dupla kattintáskor, a sikeres telepítések elrejtése", "Add": "Hozzáadás", "Add a source to {0}": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ho<PERSON> eh<PERSON>, {0}\n", "Add a timestamp to the backup file names": "Időbélyegző hozzáadása a mentési fájlnevekhez", "Add a timestamp to the backup files": "Időbélyegző hozzáadása a biztonsági mentés fájlokhoz", "Add packages or open an existing bundle": "Csomagok hozzáadása vagy egy meglévő köteg megnyitása", "Add packages or open an existing package bundle": "\nCsomagok hozzáadása vagy egy meglévő csomag köteg megnyitása", "Add packages to bundle": "Csomagok hozzáadása a köteghez", "Add packages to start": "Adjon hozzá csomagokat az indításhoz", "Add selection to bundle": "Kijelölés hozzáadása a köteghez", "Add source": "<PERSON><PERSON><PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "A 'nem található megfelelő frissítés' hibaüzenettel sikertelen frissítések hozzáadása a figyelmen kívül hagyott frissítések listájához", "Adding source {source}": "<PERSON><PERSON><PERSON> {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON> {source} ide {manager}", "Addition succeeded": "A hozzáadás sikeres volt", "Administrator privileges": "Rendszergazdai jogosultságok", "Administrator privileges preferences": "Rendszergazdai jogosultságok beállításai", "Administrator rights": "Rendszergazdai jogok", "Administrator rights and other dangerous settings": "Rendszergazdai jogok és egyéb veszélyes beállítások", "Advanced options": "<PERSON><PERSON><PERSON>", "All files": "Összes fájl", "All versions": "Minden változat", "Allow changing the paths for package manager executables": "Lehetővé teszi a csomagkezelő futtatható fájljainak elérési útvonal módosítását", "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Egyéni előtelepítési és utótelepítési parancsok importálásának engedélyezése csomagok csomagból történő importálása esetén", "Allow package operations to be performed in parallel": "A csomagműveletek párhuzamos végrehajtásának lehetővé tétele", "Allow parallel installs (NOT RECOMMENDED)": "Párhuzamos telepítések engedélyezése (NEM AJÁNLOTT)", "Allow pre-release versions": "Engedélyezze az előzetes kiadású verziókat", "Allow {pm} operations to be performed in parallel": "{pm} műveletek párhuzamos végrehajtásának engedélyezése", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternatívaként a(z) {0} telepítése a Windows PowerShell-ben a következő parancs futtatás<PERSON>val is elvégezhető:", "Always elevate {pm} installations by default": "Alapértelmezés szerint mindig emelje ki a {pm} telepítéseket", "Always run {pm} operations with administrator rights": "A {pm} műveleteket mindig rendszergazdai jogokkal futtassa", "An error occurred": "<PERSON><PERSON>", "An error occurred when adding the source: ": "Hiba történt a forrás hozzáadásakor:", "An error occurred when attempting to show the package with Id {0}": "Hiba történt a {0} azonosítóval rendelkező csomag megjelenítésekor.", "An error occurred when checking for updates: ": "Hiba történt a frissítések ellenőrzése során:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "Hiba történt a biztonsági mentés betöltése közben:", "An error occurred while logging in: ": "Hiba történt a bejelentkezés során:", "An error occurred while processing this package": "Hiba történt a csomag feldolgozása során", "An error occurred:": "<PERSON><PERSON> t<PERSON>:", "An interal error occurred. Please view the log for further details.": "Belső hiba történt. További részletekért tekintse meg a naplót.", "An unexpected error occurred:": "Váratlan hiba tö<PERSON>:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Váratlan probléma merült fel a WinGet javítási kísérlete során. Próbálja később újra", "An update was found!": "Ta<PERSON><PERSON>ltunk egy frissítést!", "Android Subsystem": "Android Alrendszer", "Another source": "<PERSON><PERSON> másik for<PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "A telepítés vagy frissítés során létrehozott új parancsikonok auto. törl<PERSON>dnek, <PERSON><PERSON><PERSON>, hogy az első észlelésükkor megerősítést kérnének.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Az UniGetUI-n kívül létrehozott vagy módosított parancsikonok figyelmen kívül maradnak. Ezeket a {0} gombon keresztül tudja majd hozzáadni.", "Any unsaved changes will be lost": "Minden el nem mentett módosítás elveszik", "App Name": "App neve", "Appearance": "<PERSON><PERSON><PERSON><PERSON>", "Application theme, startup page, package icons, clear successful installs automatically": "Alkalma<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, sikeres telepítések automatikus törlése", "Application theme:": "Alkalmazás téma:", "Apply": "Alkalmaz", "Architecture to install:": "Telepítendő architektúra:", "Are these screenshots wron or blurry?": "Ezek a képernyőképek hibásak vagy homályosak?", "Are you really sure you want to enable this feature?": "Tényleg biztosan engedélyezni szeretné ezt a funkciót?", "Are you sure you want to create a new package bundle? ": "Biztos, hogy új csomag köteget szeretne létrehozni?", "Are you sure you want to delete all shortcuts?": "Biztos, hogy az összes parancsikont törölni szeretné?", "Are you sure?": "<PERSON><PERSON><PERSON> benne?", "Ascendant": "Növekvő", "Ask for administrator privileges once for each batch of operations": "Minden műveletköteghez egyszer kérjen rendszergazdai jogosultságot.", "Ask for administrator rights when required": "Szükség esetén rendszergazdai jogok kérése", "Ask once or always for administrator rights, elevate installations by default": "Egyszer vagy mindig kérjen rendszergazdai jogokat, alapértelmezés szerint emelje meg a telepítési jogokat.", "Ask only once for administrator privileges": "Csak egyszer kérjen rendszergazdai jogosultságokat", "Ask only once for administrator privileges (not recommended)": "Csak egyszer kérjen rendszergazdai jogosultságokat (nem ajánlott)", "Ask to delete desktop shortcuts created during an install or upgrade.": "A telepítés vagy frissítés során létrehozott asztali parancsikonok törlésének kérése.", "Attention required": "<PERSON><PERSON><PERSON><PERSON>", "Authenticate to the proxy with an user and a password": "Proxy hitelesítés egy felhasználóval és egy jelszóval", "Author": "Szerző", "Automatic desktop shortcut remover": "Automatikus asztali parancsikon eltávolító", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Autom. elmenti az összes telepített csomag <PERSON>, hogy könnyen v<PERSON>zaállítha<PERSON> a<PERSON>.", "Automatically save a list of your installed packages on your computer.": "Automatikusan elmenti a telepített csomagok listáját a számítógépen.", "Autostart WingetUI in the notifications area": "A WingetUI auto. indítása az értesítési területen", "Available Updates": "Elérhető frissítések", "Available updates: {0}": "Elérhető frissítések: {0}", "Available updates: {0}, not finished yet...": "Elérhető frissítések: {0}, még nincs k<PERSON>z...", "Backing up packages to GitHub Gist...": "Csomagok bizt. mentése a GitHub Gistre...", "Backup": "Biztons<PERSON>gi <PERSON>", "Backup Failed": "A biztonsági mentés nem sikerült", "Backup Successful": "A biztonsági mentés sikeres", "Backup and Restore": "Bizt. mentés és Helyreállítás", "Backup installed packages": "A telepített csomagok biztonsági mentése", "Backup location": "Bizt. mentés he<PERSON>e", "Become a contributor": "Legyen közreműködő", "Become a translator": "<PERSON><PERSON><PERSON> for<PERSON>", "Begin the process to select a cloud backup and review which packages to restore": "Kezdje el a folyamatot a felhőalapú bizt. mentés kiválasztásához és a visszaállítandó csomagok áttekintéséhez.", "Beta features and other options that shouldn't be touched": "Béta funkciók és egyéb opciók, amely<PERSON><PERSON><PERSON> nem szabadna hozzányúlni", "Both": "Mindkettő", "Bundle security report": "Köteg-biztonsági j<PERSON>", "But here are other things you can do to learn about WingetUI even more:": "<PERSON> más dolgok is amiket megtehet, hogy még többet tudjon meg a WingetUI-ról:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Ha kikapcsol egy c<PERSON>, ak<PERSON> többé nem láthatja vagy frissíthe<PERSON> a csomagjait.", "Cache administrator rights and elevate installers by default": "Rendszergazdai jogok gyorsítótárba helyezése és a telepítési szint  emelése alapértelmezésben", "Cache administrator rights, but elevate installers only when required": "Rendszergazdai jogok gyorsítótárba he<PERSON>ezése, de csak szükség esetén adjon jogot a telepítőnek.", "Cache was reset successfully!": "A gyorsítótár törlése sikeres!", "Can't {0} {1}": "<PERSON><PERSON> le<PERSON> {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": "Minden művelet törlése", "Change backup output directory": "A biztonsági mentés kimeneti könyvtárának módosítása", "Change default options": "Alapért. beállítások módosítása", "Change how UniGetUI checks and installs available updates for your packages": "Az UniGetUI csomagok elérhető frissítései ellenőrzésének és telepítési módjának módosítása", "Change how UniGetUI handles install, update and uninstall operations.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az UniGetUI miként kezelje a telepítési, frissítési és eltávolítási műveleteket.", "Change how UniGetUI installs packages, and checks and installs available updates": "Az UniGetUI csomagok telepítésének, valamint a rendelkezésre álló frissítések ellenőrzési és telepítési módjának változtatása", "Change how operations request administrator rights": "A műveletek rendszergazdai jog igénylési módjának változtatása", "Change install location": "Telepítés helyének módosítása", "Change this": "Változtassa meg ezt", "Change this and unlock": "Változtassa meg és oldja fel ezt", "Check for package updates periodically": "Rendszeresen ellenőrizze a csomag frissítéseit", "Check for updates": "Frissítések ellenőrzése", "Check for updates every:": "Ellenőrz<PERSON> gyakorisága:", "Check for updates periodically": "Rendszeresen ellenőrizze a frissítéseket", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON>ds<PERSON><PERSON><PERSON>, és kérdezzen rá mit tegyen, ha frissítéseket talál.", "Check for updates regularly, and automatically install available ones.": "Rendszeresen ellenőrizze és automatikusan telepítse az elérhető frissítéseket.", "Check out my {0} and my {1}!": "Tekintse meg a következőt: {0} és {1}!", "Check out some WingetUI overviews": "Nézzen meg néhány WingetUI ismertetőt", "Checking for other running instances...": "Más futó példányok keresése...", "Checking for updates...": "Frissítések keresése...", "Checking found instace(s)...": "<PERSON><PERSON><PERSON><PERSON>(ok) ellenőrzése...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, hogy hány művelet legyen párhuzamosan végrehajtva", "Clear cache": "Gyors.t<PERSON><PERSON>", "Clear finished operations": "Befejezett műveletek törlése", "Clear selection": "Kiválasztás törlése", "Clear successful operations": "Sikeres műveletek eltávolítása", "Clear successful operations from the operation list after a 5 second delay": "A sikeres műveletek törlése a műveleti listából 5 mp késleltetés után.", "Clear the local icon cache": "A helyi ikon gyors.t<PERSON>r tö<PERSON>", "Clearing Scoop cache - WingetUI": "Scoop gyorsí<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON> - WingetUI", "Clearing Scoop cache...": "<PERSON>oop gyors<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>...", "Click here for more details": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Kattintson a Telepítés gombra a telepítési folyamat megkezdéséhez. Ha kihagyja a telepítést előfordulhat, hogy az UniGetUI nem a várt módon fog működni.", "Close": "<PERSON><PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Az UniGetUI bezárása a tálcára", "Close WingetUI to the notification area": "A WingetUI bezárása az értesítési területre", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "A felhőmentés egy privát GitHub Gist-et használ a telepített csomagok listájának tárolására.", "Cloud package backup": "Felhőbeni csomag biztonsági mentés", "Command-line Output": "Parancssori kimenet", "Command-line to run:": "Futtatandó parancssor:", "Compare query against": "Hasonlítsa össze a lekérdezést", "Compatible with authentication": "Kompatibilitás hitelesítéssel", "Compatible with proxy": "Kompatibilitás proxy-val", "Component Information": "Komponens információ", "Concurrency and execution": "Egyidejűség és végrehajtás", "Connect the internet using a custom proxy": "Csatlakozás az internethez egyéni proxy hasz<PERSON><PERSON><PERSON><PERSON>", "Continue": "Folytatás", "Contribute to the icon and screenshot repository": "Hozzájárulás az ikon- és képernyőkép-tárhoz", "Contributors": "Közreműködők", "Copy": "Másol", "Copy to clipboard": "Másol a vágólapra", "Could not add source": "<PERSON>em tudta felvenni a forr<PERSON>t", "Could not add source {source} to {manager}": "<PERSON><PERSON>ni a forr<PERSON>t {source} a kezelőhöz {manager}", "Could not back up packages to GitHub Gist: ": "<PERSON>em <PERSON> biztonsági másolatot készíteni a csomagokról a GitHub Gistre:", "Could not create bundle": "<PERSON><PERSON> létrehozni a köteget", "Could not load announcements - ": "<PERSON><PERSON> betölteni a közleményeket -", "Could not load announcements - HTTP status code is $CODE": "<PERSON><PERSON> betölteni a közleményeket – a HTTP állapotkód: $CODE", "Could not remove source": "<PERSON>em tudta <PERSON>távolítani a forrást", "Could not remove source {source} from {manager}": "<PERSON><PERSON>ávolítani a forr<PERSON>t {source} a kezelőből {manager}", "Could not remove {source} from {manager}": "<PERSON><PERSON> a {source} eltávolítása a {manager}-b<PERSON>l", "Create .ps1 script": null, "Credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Current Version": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "Current status: Not logged in": "Jelenlegi állapot: <PERSON><PERSON><PERSON>", "Current user": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "Custom arguments:": "<PERSON><PERSON><PERSON><PERSON> argumentumok:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Egy<PERSON>i parancssori argumentumok:", "Custom install arguments:": "Egyéni telepítési argumentumok:", "Custom uninstall arguments:": "Egyéni eltávolítási argumentumok:", "Custom update arguments:": "Egyéni frissítési argumentumok:", "Customize WingetUI - for hackers and advanced users only": "A WingetUI testreszabása – csak hackerek és haladó felhasználók számára", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "NYILATKOZAT: NEM VÁLLALUNK FELELŐSSÉGET A LETÖLTÖTT CSOMAGOKÉRT. GYŐZŐDJÖN MEG RÓLA, HOGY CSAK MEGBÍZHATÓ SZOFTVEREKET TELEPÍT.\n", "Dark": "<PERSON><PERSON><PERSON><PERSON>", "Decline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default installation options for {0} packages": "{0} csomag alapért. telepítési beállí<PERSON>ásai", "Default preferences - suitable for regular users": "Alapértelmezett beállítások - a rendszeres felhasználók számára", "Default vcpkg triplet": "Alapértelmezett vcpkg triplet", "Delete?": "Törl<PERSON>?", "Dependencies:": "Függőségek:", "Descendant": "<PERSON><PERSON><PERSON><PERSON>", "Description:": "Leírás:", "Desktop shortcut created": "<PERSON><PERSON><PERSON><PERSON> paranc<PERSON> lé<PERSON>z<PERSON>", "Details of the report:": "A jelentés részletei:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "A fejlesztés nehéz, és ez az alkalmazás ingyenes. De ha tetszett az app, bármikor <b>meghívhat egy kávéra</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Közvetlen telepítés a \"{discoveryTab}\" lapon lévő elemre való dupla kattint<PERSON> (a csomaginformáció megjelení<PERSON><PERSON> he<PERSON>)", "Disable new share API (port 7058)": "Új megosztási API letiltása (7058-as port)", "Disable the 1-minute timeout for package-related operations": "Az 1 perces időkorlát kikapcsolása a csomaggal kapcsolatos műveleteknél", "Disclaimer": "Nyilatkozat", "Discover Packages": "Csomagok felfedezése", "Discover packages": "Csomagok felfedezése", "Distinguish between\nuppercase and lowercase": "Megkülönböztetve a nagy- és kisbetűk", "Distinguish between uppercase and lowercase": "Tegyen különbséget a kis- és nagybetűk között", "Do NOT check for updates": "NE keressen frissítéseket", "Do an interactive install for the selected packages": "A kiválasztott csomagok interaktív telepítése", "Do an interactive uninstall for the selected packages": "A kiválasztott csomagok interaktív eltávolítása", "Do an interactive update for the selected packages": "A kiválasztott csomagok interaktív frissítése", "Do not automatically install updates when the battery saver is on": "Ne telepítse autom. a frissítéseket, ha az akkukímélő funkció be van kapcsolva.", "Do not automatically install updates when the network connection is metered": "Ne telepítse autom. a frissítéseket, ha a há<PERSON>ózati ka<PERSON>olat nem korlátlan.", "Do not download new app translations from GitHub automatically": "Ne töltse le autom. az új app fordításokat a GitHubról", "Do not ignore updates for this package anymore": "Többé ne hagyja figyelmen kívül a csomag frissítéseit", "Do not remove successful operations from the list automatically": "A sikeres műveleteket ne távolítsa el automatikusan a listáról", "Do not show this dialog again for {0}": "Ne jelenítse meg ezt a {0} párbeszédpanelt újra", "Do not update package indexes on launch": "Ne frissítse a csomag indexeket indításkor", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Elfogad<PERSON>, hogy az UniGetUI anonim használati statisztikákat gyűjt és küld, kizárólag a felhasználói élmény megértése és javítása céljából?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "<PERSON>n szerint hasznos a WingetUI? Ha tudja, t<PERSON><PERSON>gassa a munkámat, hogy a WingetUI-t továbbra is a legjobb csomagkezelő felületté tehessem.\n", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Hasznosnak találja a WingetUI-t? Szeretné támogatni a fejlesztőt? Ha megteheti {0}, ezzel sokat segít!", "Do you really want to reset this list? This action cannot be reverted.": "Tényleg alaphelyzetbe állítja ezt a listát? Ezt a műveletet nem lehet v<PERSON>.", "Do you really want to uninstall the following {0} packages?": "Tényleg szeretné eltávolítani a következő {0} csomagot?", "Do you really want to uninstall {0} packages?": "Biztosan szeretne eltávolítani {0} csomagot?", "Do you really want to uninstall {0}?": "Biztosan el akarja távolítani a következőt: {0}?", "Do you want to restart your computer now?": "Szeretné most újraindítani a számítógépet?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "<PERSON><PERSON><PERSON><PERSON><PERSON> a WingetUI-t a saját nyelvére? Tekintse meg a hozzájáru<PERSON><PERSON> módj<PERSON>t <a style=\"color:{0}\" href=\"{1}\"a>ITT!</a>\n", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Nincs kedve adományozni? <PERSON>e aggódjon, a WingetUI-t bármikor megoszthatja barátaival. Terjessze a WingetUI hírét.", "Donate": "<PERSON><PERSON><PERSON><PERSON>", "Done!": "Kész!", "Download failed": "Sikertelen letöltés", "Download installer": "Telepítő letöltése", "Download operations are not affected by this setting": "A letöltési műveleteket ez a beállítás nem befolyásolja", "Download selected installers": "Kiválasztott telepítők letöltése", "Download succeeded": "A letöltés sikeres volt", "Download updated language files from GitHub automatically": "Frissített nyelvi fájlok auto. letöltése a GitHubról", "Downloading": "Letöltés", "Downloading backup...": "Bizt. mentés letöltése...", "Downloading installer for {package}": "A(z) {package} telepítőjének letöltése", "Downloading package metadata...": "Csomag metaadatainak letöltése...", "Enable Scoop cleanup on launch": "Scoop tisztítás engedélyezése indításkor", "Enable WingetUI notifications": "WingetUI értesítések engedélyezése", "Enable an [experimental] improved WinGet troubleshooter": "Engedélyezze a [kísérleti] továbbfejlesztett WinGet hibaelhárítót", "Enable and disable package managers, change default install options, etc.": "Csomagkezelők engedélyezése és letiltása, alapért. telepítési beállítások módosítása, stb.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Engedélyezze a CPU háttérben használat optimalizálását", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "A háttér api engedélyezése (WingetUI Widgets and Sharing, 7058-as port)", "Enable it to install packages from {pm}.": "Engedélyezze a csomagok telepítéséhez innen {pm}.", "Enable the automatic WinGet troubleshooter": "Az autom. WinGet hibaelhárítás engedélyezése", "Enable the new UniGetUI-Branded UAC Elevator": "Az új UniGetUI-Branded UAC Elevator aktiválása", "Enable the new process input handler (StdIn automated closer)": "Az új folyamat bemeneti kezelőjének engedélyezése (StdIn automatizált zárás)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "<PERSON><PERSON> al<PERSON><PERSON><PERSON> be<PERSON>llításokat AKKOR ÉS CSAK IS AKKOR enged<PERSON>, ha teljesen tiszt<PERSON><PERSON>l, hogy mit tesznek, és milyen következményekkel és veszélyekkel járhatnak.", "Enable {pm}": "{pm} engedélyezése", "Enter proxy URL here": "Adja meg a Proxy URL-t", "Entries that show in RED will be IMPORTED.": "A PIROS bejegyzések IMPORTÁLVA lesznek.", "Entries that show in YELLOW will be IGNORED.": "A SÁRGA színnel jelölt bejegyzések figyelmen kívül maradnak.", "Error": "Hiba", "Everything is up to date": "<PERSON><PERSON>z", "Exact match": "Pontos egyezés", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Az asztalon meglévő parancsikonok átvizsgálásra kerülnek és ki kell választania, mely<PERSON>t szeretné megtartani vagy eltávolítani.", "Expand version": "Bővített változat", "Experimental settings and developer options": "Kísérleti beállítások és fejlesztői lehetőségek", "Export": "Exportálás", "Export log as a file": "A napló exportálása fájlként", "Export packages": "Csomagok exportálása", "Export selected packages to a file": "A kiválasztott csomagok exportálása fájlba", "Export settings to a local file": "Beállítások exportálása helyi fájlba", "Export to a file": "Export<PERSON>l egy fájlba", "Failed": "Sikertelen", "Fetching available backups...": "Elérhető biztonsági mentések lekérése...", "Fetching latest announcements, please wait...": "A legfrissebb bejelentések letöltése, kis türelmet...", "Filters": "Szűrők", "Finish": "Befejez", "Follow system color scheme": "Kövesse a rendszer színsémáját", "Follow the default options when installing, upgrading or uninstalling this package": "Kövesse az alapért. beállításokat a csomag telepítésekor, frissítésekor vagy eltávolításakor.", "For security reasons, changing the executable file is disabled by default": "Biztonsági okok<PERSON>ól a futtatható fájl módosítása alapértelmezés szerint le van tiltva.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Biztonsági okokból az egyéni parancssori argumentumok alapértelmezés szerint le vannak tiltva. Ennek megváltoztatásához lépjen az UniGetUI biztonsági beállításaihoz.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Biztonsági okokból a művelet előtti és utáni szkriptek alapértelmezés szerint le vannak tiltva. Ennek megváltoztatásához lépjen az UniGetUI biztonsági beállításaihoz.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "ARM fordított winget ve<PERSON><PERSON><PERSON> (CSAK ARM64 RENDSZERHEZ)", "Formerly known as WingetUI": "Korábban WingetUI néven ismert", "Found": "<PERSON><PERSON>", "Found packages: ": "<PERSON><PERSON><PERSON><PERSON> csoma<PERSON>:", "Found packages: {0}": "<PERSON><PERSON><PERSON><PERSON> csomagok: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON><PERSON>: {0}, még nem fejez<PERSON><PERSON> be...", "General preferences": "Általán<PERSON>", "GitHub profile": "GitHub profil", "Global": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Go to UniGetUI security settings": "Lépjen az UniGetUI biztonsági beállításaihoz", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "<PERSON><PERSON><PERSON><PERSON>, de hasznos segédprogramok és más érdekes csomagok nagyszerű tárháza.<br>Tartalma: <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> (extra bucket szükséges)</b>", "Great! You are on the latest version.": "Nagyszerű! Ön a legújabb verziót használja.", "Grid": "<PERSON><PERSON><PERSON>", "Help": "S<PERSON>gó", "Help and documentation": "Súgó és dokumentáció", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Itt módosíthatja az UniGetUI viselkedését a következő parancsikonokkal kapcsolatban. Ha bejelöl egy parancsikont, akkor az UniGetUI törölni fogja azt, ha egy későbbi frissítéskor létrejön. Ha nem jelöli ki, a parancsikon érintetlenül marad.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, a nevem Martí, a WingetUI <i>fejlesztője</i> vagyok. A WingetUI teljes egészében a szabadidőmben készült!", "Hide details": "Részletek elrejtése", "Homepage": "<PERSON><PERSON>", "Hooray! No updates were found.": "Minden rendben! <PERSON>nc<PERSON> több frissí<PERSON>s!", "How should installations that require administrator privileges be treated?": "<PERSON><PERSON><PERSON> kell kezelni a rendszergazdai jogosultságokat igénylő telepítéseket?", "How to add packages to a bundle": "Csomagok hozzáadása egy köteghez", "I understand": "Meg<PERSON><PERSON>tte<PERSON>", "Icons": "Ikonok", "Id": "Azonosító", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Ha engedélyezte a felhőalapú mentést, akkor az egy GitHub Gist-ként lesz elmentve ezen a fiókon.", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Az egyéni előtelepítési és utótelepítési parancsok figyelmen kívül hagyása csomagok csomagokból történő importálása esetén", "Ignore future updates for this package": "A csomag jövőbeli frissítéseit hagyja figyelmen kívül", "Ignore packages from {pm} when showing a notification about updates": "A {pm} csomagok figyelmen kívül hagyása a frissítésekről szóló értesítés megjelenítésekor", "Ignore selected packages": "Kiválasztott csomagok figyelmen kívül hagyása", "Ignore special characters": "Speciális karakterek figyelmen kívül hagyása", "Ignore updates for the selected packages": "A kiválasztott csomagok frissítéseinek figyelmen kívül hagyása", "Ignore updates for this package": "Hagyja figyelmen kívül a csomag frissítéseit", "Ignored updates": "Figyelmen kívül hagyott frissítések", "Ignored version": "Figyelmen kívül hagyott verzió", "Import": "Import", "Import packages": "Csomagok importálása", "Import packages from a file": "Csomagok importálása fájlból", "Import settings from a local file": "Beállítások importálása helyi fájlból", "In order to add packages to a bundle, you will need to: ": "<PERSON><PERSON><PERSON>, hogy csomagokat adjon hozzá egy k<PERSON>, a következőkre van szüksége:", "Initializing WingetUI...": "WingetUI inicializálása...", "Install": "Telepítés", "Install Scoop": "Scoop telepítés", "Install and more": "Telepítés és egyebek", "Install and update preferences": "Telepítési és frissítési preferenciák", "Install as administrator": "Telepítés rendszergazdaként", "Install available updates automatically": "A rendelkezésre álló frissítések automatikus telepítése", "Install location can't be changed for {0} packages": "A telepítési hely nem módosítható {0} csomag esetében", "Install location:": "Telepítés he<PERSON>:", "Install options": "Telepítési lehetőségek", "Install packages from a file": "Csomagok telepítése fájlból", "Install prerelease versions of UniGetUI": "Az UniGetUI előzetes verzióinak telepítése", "Install script": null, "Install selected packages": "A kiválasztott csomagok telepítése", "Install selected packages with administrator privileges": "A kiválasztott csomagok telepítése rendszergazdai jogosultságokkal", "Install selection": "Telepítés kiválasztása", "Install the latest prerelease version": "Telepítse a legújabb előzetes verziót", "Install updates automatically": "Frissítések automatikus telepítése", "Install {0}": "{0} telepítése", "Installation canceled by the user!": "A telepítést törölte a felhasználó!", "Installation failed": "Sikertelen telepítés", "Installation options": "Telepítési lehetőségek", "Installation scope:": "Scope telepítés:", "Installation succeeded": "A telepítés sikeres volt", "Installed Packages": "Telepített Csomagok", "Installed Version": "Telepített verzió", "Installed packages": "Telepített csomagok", "Installer SHA256": "SHA256 telepítő", "Installer SHA512": "Telepítő SHA512", "Installer Type": "Telepítő típusa", "Installer URL": "Telepítő URL", "Installer not available": "Telepítő nem érhető el", "Instance {0} responded, quitting...": "{0} esetben v<PERSON>lt, kilépés...", "Instant search": "Azonnali kere<PERSON>és", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Kihagyott integritás ellenőrzések", "Integrity checks will not be performed during this operation": "A művelet során nem kerül sor integritás ellenőrzésekre.", "Interactive installation": "Interaktív telepítés", "Interactive operation": "Interaktív műveletek", "Interactive uninstall": "Interaktív eltávolítás", "Interactive update": "Interaktív frissítés", "Internet connection settings": "Internet kapcsolat beállításai", "Is this package missing the icon?": "Hiányzik ebből a csomagból az ikon?", "Is your language missing or incomplete?": "Hiányzik vagy hiányos az Ön nyelve?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "<PERSON><PERSON>, hogy a megadott hitelesítő adatokat biztonságosan tá<PERSON>, <PERSON><PERSON> ak<PERSON>r ne is használja a bankszámlája hitelesítő adatait.", "It is recommended to restart UniGetUI after WinGet has been repaired": "A WinGet javítása után ajánlott az UniGetUI újraindítása.", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "<PERSON><PERSON>, a WinGet nem működik megfelelően. Megpróbálja kijavítani a WinGet-et?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON>, a WingetUI-t rendszergazdak<PERSON>t futtatta, ami nem a<PERSON>. A programot továbbra is használ<PERSON><PERSON> javasoljuk, hogy a WingetUI-t ne futtassa rendszergazdai jogosultságokkal. Kattintson a \"{showDetails}\" gombra, hogy megtudja miért.\n", "Language": "Nyelv", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON><PERSON>, téma és más egyéb be<PERSON>ll<PERSON>", "Last updated:": "U<PERSON><PERSON><PERSON> fris<PERSON>té<PERSON>:", "Latest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Latest Version": "Leg<PERSON><PERSON><PERSON> verzi<PERSON>", "Latest Version:": "Legújabb verzió:", "Latest details...": "Legújabb részletek...", "Launching subprocess...": "Alfolyamat indítása...", "Leave empty for default": "Alapértékhez hagyja üresen", "License": "Licenc", "Licenses": "Licencek", "Light": "<PERSON>il<PERSON><PERSON>", "List": "Lista", "Live command-line output": "É<PERSON>ő paranc<PERSON>ri kimenet", "Live output": "<PERSON><PERSON><PERSON> kime<PERSON>", "Loading UI components...": "UI összetevők betöltése...", "Loading WingetUI...": "WingetUI betöltése...", "Loading packages": "Csomagok betöltése", "Loading packages, please wait...": "Csomagok betöltése, kis türelmet...", "Loading...": "Betöltés...", "Local": "<PERSON><PERSON><PERSON>", "Local PC": "<PERSON><PERSON><PERSON>", "Local backup advanced options": "<PERSON>lyi bi<PERSON>gi mentés speciá<PERSON>", "Local machine": "<PERSON><PERSON><PERSON>", "Local package backup": "<PERSON><PERSON><PERSON> csomag biztonsági <PERSON>", "Locating {pm}...": "{pm} keresése...", "Log in": "Bejelentkezés", "Log in failed: ": "Sikertelen bejelentkezés:", "Log in to enable cloud backup": "Jelentkezzen be a felhőalapú biztonsági mentés engedélyezéséhez", "Log in with GitHub": "Bejelentkezés GitHub-fiókkal", "Log in with GitHub to enable cloud package backup.": "Jelentkezzen be a GitHub-bal a felhőalapú csomag bizt. mentés engedélyezéséhez.", "Log level:": "<PERSON><PERSON><PERSON><PERSON> szint:", "Log out": "Kijelentkezés", "Log out failed: ": "A kijelentkezés sikertelen:", "Log out from GitHub": "Kijelentkezés a GitHubról", "Looking for packages...": "Csomagok keresése...", "Machine | Global": "Gép | Globális", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "Kezelés", "Manage UniGetUI settings": "UniGetUI beállítások kezelése", "Manage WingetUI autostart behaviour from the Settings app": "A WingetUI auto. indítási viselkedésének kezelése a Beállítások alkalmazásból", "Manage ignored packages": "Figyelmen kívül hagyott csomagok kezelése", "Manage ignored updates": "Ignor<PERSON>lt frissítések kezelése", "Manage shortcuts": "A parancsikonok kezelése", "Manage telemetry settings": "Telemetriai beállítások kezelése", "Manage {0} sources": "{0} forr<PERSON><PERSON> kezelése", "Manifest": "Jegyzék", "Manifests": "Jegyzékek", "Manual scan": "<PERSON><PERSON><PERSON> v<PERSON>", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "A Microsoft hivatalos csomagkezelője. Tele van jól ismert és ellenőrzött csomagokkal<br>Tartalma: <<PERSON><PERSON><PERSON><PERSON><PERSON> szoftverek, Microsoft Store alkalmazások</b>.", "Missing dependency": "Hiányzó fü<PERSON>őség", "More": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "More details": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "More details about the shared data and how it will be processed": "További részletek a megosztott adatokról és azok feldolgozásának módjáról", "More info": "<PERSON><PERSON><PERSON> infó", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "MEGJEGYZÉS: Ez a hibaelhárító kikapcsolható az UniGetUI beállításai között, a WinGet szakaszban.", "Name": "Név", "New": "<PERSON><PERSON>", "New Version": "<PERSON><PERSON>", "New bundle": "<PERSON><PERSON>", "New version": "<PERSON><PERSON>", "Nice! Backups will be uploaded to a private gist on your account": "Szép! A biztonsági mentések feltöltődnek a fiókod privát listájára.", "No": "Nem", "No applicable installer was found for the package {0}": "Nem talá<PERSON> megfelelő telepítőt a {0} csomaghoz", "No dependencies specified": "Nincsenek megadott függőségek", "No new shortcuts were found during the scan.": "A vizsgálat során nem találtunk új parancsikonokat.", "No packages found": "<PERSON><PERSON>", "No packages found matching the input criteria": "Nem ta<PERSON> a beviteli feltételeknek megfelelő csomagokat", "No packages have been added yet": "Még nem adtunk hozzá csomagokat", "No packages selected": "<PERSON><PERSON><PERSON> csomag", "No packages were found": "<PERSON><PERSON> csomagokat", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Nem gyűjtünk és nem küldünk személyes adatokat. Az összegyűjtött adatokat anonimizáljuk, így nem lehet v<PERSON>zavezetni Önhöz.", "No results were found matching the input criteria": "<PERSON><PERSON><PERSON> ta<PERSON> a beviteli feltételeknek megfelelő eredményre", "No sources found": "<PERSON><PERSON>", "No sources were found": "<PERSON><PERSON>", "No updates are available": "<PERSON><PERSON><PERSON> f<PERSON>", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "A Node JS csomagkezelője. Tele van könyvtárakkal és egyéb segédprogramokkal, amelyek a javascript vil<PERSON>g<PERSON><PERSON> k<PERSON><br>Tartalma: <b>Node javascript könyvtárak és egyéb kapcsolódó segédprogramok</b>", "Not available": "<PERSON>em <PERSON> el", "Not finding the file you are looking for? Make sure it has been added to path.": "Nem találja a keresett fájlt? Győződjön meg róla, hogy hozzáadta az elérési útvonalhoz.", "Not found": "<PERSON><PERSON>", "Not right now": "Most nem.", "Notes:": "Megjegyzések:", "Notification preferences": "Értesítési be<PERSON>", "Notification tray options": "Tálca értesítés beállításai", "Notification types": "Értesítés <PERSON>", "NuPkg (zipped manifest)": "NuPkg (tömörített manifeszt)", "OK": "OK", "Ok": "Ok", "Open": "Megnyit", "Open GitHub": "A GitHub megnyitása", "Open UniGetUI": "Az UniGetUI megnyitása", "Open UniGetUI security settings": "UniGetUI biztonsági beállítások megnyitása", "Open WingetUI": "WingetUI megnyitása", "Open backup location": "A biztonsági mentés helyének <PERSON>a", "Open existing bundle": "Meglévő köteg megnyitása", "Open install location": "Telepítés he<PERSON>", "Open the welcome wizard": "Az üdvö<PERSON><PERSON><PERSON> varázsló me<PERSON>a", "Operation canceled by user": "Felhas<PERSON><PERSON><PERSON><PERSON> törölt művelet", "Operation cancelled": "A művelet törlésre került", "Operation history": "Műveleti előzmények", "Operation in progress": "Folyamatban lévő művelet", "Operation on queue (position {0})...": "A művelet a várólistán ({0}. pozíció)...", "Operation profile:": "Műveleti profil:", "Options saved": "Mentett opciók", "Order by:": "Rendezés:", "Other": "<PERSON><PERSON><PERSON><PERSON>", "Other settings": "<PERSON><PERSON><PERSON><PERSON>", "Package": "Csomag", "Package Bundles": "Csomag kötegek", "Package ID": "Csomag azonosító", "Package Manager": "Csomagkezelő", "Package Manager logs": "Csomagkezelő naplók", "Package Managers": "Csomagkezelők", "Package Name": "Csomagnév", "Package backup": "Csomag biztonsági mentése", "Package backup settings": "Csomag bizt. mentés beállításai", "Package bundle": "Csomag köteg", "Package details": "A csomag részletei", "Package lists": "Csomag listák", "Package management made easy": "Csomagkezelés egyszerűen", "Package manager": "Csomagkezelő", "Package manager preferences": "Csomagkezelő preferenciái", "Package managers": "Csomagkezelők", "Package not found": "Csomag nem található", "Package operation preferences": "Csomag működési preferenciái", "Package update preferences": "Csomag frissítési preferenciái", "Package {name} from {manager}": "Csomag {name} a {manager}-b<PERSON>l", "Package's default": "A csomag alapértelmezett", "Packages": "Csomag", "Packages found: {0}": "<PERSON><PERSON><PERSON>lt csomag: {0}", "Partially": "Részben", "Password": "Je<PERSON><PERSON><PERSON>", "Paste a valid URL to the database": "Érvényes URL beillesztése az adatbázisba", "Pause updates for": "Frissítései szüneteltetése", "Perform a backup now": "Biztonsági mentés végrehajtása most", "Perform a cloud backup now": "Végezzen felhőmentést most", "Perform a local backup now": "Helyi bizt. mentés végrehajtása most", "Perform integrity checks at startup": null, "Performing backup, please wait...": "Biztonsági mentés végrehajtása, kis türelmet...", "Periodically perform a backup of the installed packages": "Rendszeresen készítsen biztonsági mentést a telepített csomagokról", "Periodically perform a cloud backup of the installed packages": "Rendszeresen végezzen felhő mentést a telepített csomagokról", "Periodically perform a local backup of the installed packages": "Rendszeresen készítsen helyi biztonsági mentést a telepített csomagokról.", "Please check the installation options for this package and try again": "Kérjük ellenőrizze a csomag telepítési beállításait, és próbálja meg újra.", "Please click on \"Continue\" to continue": "A folytatáshoz kattintson a \"Folytatás\" gombra", "Please enter at least 3 characters": "Min. 3 karaktert írjon be", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON>, hogy bizonyos csomagok nem telepíthetők a gépen engedélyezett csomagkezelők miatt.", "Please note that not all package managers may fully support this feature": "<PERSON><PERSON><PERSON>mbe, hogy nem minden csomagkezelő támogatja teljes mértékben ezt a funkciót.", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Vegye figyelembe, hogy bizonyos forrásokból származó csomagok nem exportálhatók. Ezek kiszürkítettek és nem exportáluk.", "Please run UniGetUI as a regular user and try again.": "Kérjük futtassa az UniGetUI-t <PERSON><PERSON><PERSON>, és próbálja meg új<PERSON>.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "A problémával kap<PERSON>olatos további információkért tekintse meg a parancssori kimenetet, vagy ol<PERSON> el a Műveleti előzményeket.", "Please select how you want to configure WingetUI": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, hogyan szeretné konfigurálni a WingetUI-t", "Please try again later": "Kérjük próbálja később újra", "Please type at least two characters": "Legalább két karak<PERSON>t í<PERSON>jon be", "Please wait": "<PERSON><PERSON>", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, amíg {0} telepítése folyamatban van. Egy fekete ablak jelenhet meg. <PERSON><PERSON>, amíg <PERSON>.", "Please wait...": "<PERSON><PERSON> t<PERSON>...", "Portable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Portable mode": "<PERSON><PERSON><PERSON><PERSON><PERSON> mód", "Post-install command:": "Telepítés utáni parancs:", "Post-uninstall command:": "Eltávolítás utáni parancs:", "Post-update command:": "Frissítés utáni parancs:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "A PowerShell csomagkezelője. A PowerShell képességeit bővítő könyvtárak és szkriptek keresése<br>Tartalma: <b><PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON>k, parancsf<PERSON><PERSON>lok</b>\n", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": "Telepítés <PERSON> parancs:", "Pre-uninstall command:": "Eltávolí<PERSON><PERSON> el<PERSON> parancs:", "Pre-update command:": "Friss<PERSON><PERSON><PERSON> parancs:", "PreRelease": "Korai kiadás", "Preparing packages, please wait...": "Csomagok előkészítése, kis türelmet...", "Proceed at your own risk.": "<PERSON><PERSON><PERSON><PERSON> felelősségére folytassa.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Tiltsa meg az UniGetUI Elevator vagy GSudo segítségével történő bármilyen kie<PERSON>kedést", "Proxy URL": "Proxy URL", "Proxy compatibility table": "Proxy kompatibilitási táblázat", "Proxy settings": "Proxy <PERSON><PERSON><PERSON><PERSON><PERSON>", "Proxy settings, etc.": "Proxy <PERSON><PERSON>, stb.", "Publication date:": "<PERSON><PERSON><PERSON><PERSON>:", "Publisher": "<PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "A Python könyvtárkezelője. Tele van python könyvtárakkal és egyéb pythonhoz kapcsolódó segédprogramokkal<br>Tartalma: <b>Python könyvtárak és kapcsolódó segédprogramok</b>", "Quit": "Kilépés", "Quit WingetUI": "Az UniGetUI elhagyása", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Az UAC-kérelmek csökkentése, a telepítések alapértelmezett kiemelése, bizonyos veszélyes funkciók feloldása stb.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "Újratelepítés", "Reinstall package": "Csomag ú<PERSON>lepítés", "Related settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Release notes": "Kiadási megjegyzések", "Release notes URL": "Kiadási megjegyzések URL", "Release notes URL:": "Kiadási megjegyzések URL:", "Release notes:": "Kiadási megjegyzések:", "Reload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reload log": "Na<PERSON><PERSON>ó <PERSON>ö<PERSON>", "Removal failed": "Eltávolí<PERSON><PERSON>", "Removal succeeded": "Az eltávolítás sikeres volt", "Remove from list": "Eltávolítás a listáról", "Remove permanent data": "Állandó adatok eltávolítása", "Remove selection from bundle": "A kiválasztás eltávolítása a kötegből", "Remove successful installs/uninstalls/updates from the installation list": "A sikeres telepítések/eltávolítások/frissí<PERSON>sek eltávolítása a telepítési listáról", "Removing source {source}": "<PERSON><PERSON><PERSON> eltávolítása {source}", "Removing source {source} from {manager}": "A forrás {source} eltávolítása innen {manager}", "Repair UniGetUI": null, "Repair WinGet": "WinGet javít<PERSON>", "Report an issue or submit a feature request": "<PERSON><PERSON><PERSON> be egy <PERSON>, v<PERSON><PERSON> k<PERSON><PERSON> be egy <PERSON><PERSON><PERSON> k<PERSON>", "Repository": "<PERSON><PERSON><PERSON><PERSON><PERSON> (repo)", "Reset": "Újrakezd", "Reset Scoop's global app cache": "A Scoop globális alkalmazás gyorsítótárának alapra állítása", "Reset UniGetUI": "UniGetUI alapra állítása", "Reset WinGet": "WinGet alapra állítása", "Reset Winget sources (might help if no packages are listed)": "Winget források visszaállítása (se<PERSON><PERSON><PERSON><PERSON>, ha nincs csomag a listán)", "Reset WingetUI": "A WingetUI alaphelyzetbe állítása", "Reset WingetUI and its preferences": "A WingetUI és beállításai alaphelyzetbe állítása", "Reset WingetUI icon and screenshot cache": "Ürítse ki a WingetUI ikon és képernyőkép gyorsítótárát", "Reset list": "Lista alapra állítása", "Resetting Winget sources - WingetUI": "Winget források visszaállítása - WingetUI", "Restart": "Újraindítás", "Restart UniGetUI": "Az UniGetUI újraindítása", "Restart WingetUI": "Indítsa újra a WingetUI-t", "Restart WingetUI to fully apply changes": "A WingetUI újraindítása a változások teljes körű alkalmazásához", "Restart later": "Újraindí<PERSON><PERSON> k<PERSON>", "Restart now": "Újraindítás most", "Restart required": "Újraindítás szükséges", "Restart your PC to finish installation": "A telepítés befejezéséhez indítsa újra a számítógépet", "Restart your computer to finish the installation": "Indítsa újra a számítógépet a telepítés véglegesítéséhez", "Restore a backup from the cloud": "Biztonsági mentés visszaállítása a felhőből", "Restrictions on package managers": "A csomagkezelőkre vonatkozó korlátozások", "Restrictions on package operations": "A csomagműveletekre vonatkozó korlátozások", "Restrictions when importing package bundles": "Korlátozások csomagkötegek importálásakor", "Retry": "Újra", "Retry as administrator": "Újabb próba rendszergazdaként", "Retry failed operations": "Sikertelen műveletek megismétlése", "Retry interactively": "Újabb próba interaktívan", "Retry skipping integrity checks": "Kihagyott integritás ellenőrzések megismétlése", "Retrying, please wait...": "Újra prób<PERSON><PERSON>, kis türelmet...", "Return to top": "<PERSON><PERSON><PERSON> f<PERSON>ülre", "Run": "Futtat", "Run as admin": "Futtatás rendszergazdaként", "Run cleanup and clear cache": "Futtassa a tisztítást és törölje a gyorsítótárat", "Run last": "Futtatás utoljára", "Run next": "Futtatás következőnek", "Run now": "Futtatás most", "Running the installer...": "A telepítő futtatása...", "Running the uninstaller...": "Az eltávolító futtatása...", "Running the updater...": "A frissítő futtatása...", "Save": "Ment", "Save File": "<PERSON><PERSON><PERSON><PERSON>", "Save and close": "Mentés és bezárás", "Save as": "Ment, mint", "Save bundle as": "Mentse a köteget, mint", "Save now": "<PERSON><PERSON><PERSON> most", "Saving packages, please wait...": "Csomagok mentése, kis türelmet...", "Scoop Installer - WingetUI": "Scoop telepítő - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop eltávolító - WingetUI", "Scoop package": "Sc<PERSON> csomag", "Search": "Keresés", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "<PERSON><PERSON><PERSON>z<PERSON>i <PERSON>zo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ha frissítések állnak rendelkezésre, és ne csináljon kocka dolgokat. <PERSON><PERSON>, hogy a WingetUI túlbonyolítsa, csak egy egyszerű <b>szoftvertárolót</b> szeretnék.", "Search for packages": "Csomagok keresése", "Search for packages to start": "Csomagok keresése a kezdéshez", "Search mode": "Keres<PERSON><PERSON> mód", "Search on available updates": "Keresés az elérhető frissítések között", "Search on your software": "Keresés a saját szoftverek között", "Searching for installed packages...": "Telepített csomagok keresése...", "Searching for packages...": "Csomagok keresése...", "Searching for updates...": "Frissítések keresése...", "Select": "Kiválasztás", "Select \"{item}\" to add your custom bucket": "Válassza ki a \"{item}\" lehetőséget az egyéni bucket hozzáadásához.", "Select a folder": "Válasszon ki egy map<PERSON>", "Select all": "Mindet kiválaszt", "Select all packages": "Az összes csomag kiválasztása", "Select backup": "Biztonsági mentés kiválasztása", "Select only <b>if you know what you are doing</b>.": "Csak akkor válassza e<PERSON>t, <b>ha tudja, mit csin<PERSON>l</b>.", "Select package file": "Válassza ki a csomagfájlt", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Válassza ki a megnyitni kívánt biztonsági másolatot. <PERSON><PERSON><PERSON><PERSON>, hogy mely csomagokat/programokat szeretné v<PERSON>.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Válassza ki azokat a folyamatokat, amelyeket a csomag telepítése, frissítése vagy eltávolítása előtt le kell zárni.", "Select the source you want to add:": "Válassza ki a hozzáadni kívánt forrást:", "Select upgradable packages by default": "Alapértelmezés szerint frissíthető csomagok kiválasztása", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ho<PERSON> <b>c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b> <PERSON><PERSON><PERSON><PERSON><PERSON> ({0}), be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a csomagok telepítésének mó<PERSON>, k<PERSON><PERSON><PERSON><PERSON> a rendszergazdai jogokat, stb.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Kézfogás elküldve. Várjuk a figyelő válaszát... ({0}%)", "Set a custom backup file name": "Egyéni mentési fájlnév beállítása", "Set custom backup file name": "Egyéni mentési fájlnév beállítása", "Settings": "Beállítások", "Share": "Megoszt", "Share WingetUI": "A WingetUI megosztása", "Share anonymous usage data": "Névtelen használati adatok megosztása", "Share this package": "Ossza meg ezt a csomagot", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Ha módosítja a biztonsági be<PERSON>, ú<PERSON>ra meg kell nyitnia a csomagot, hogy a módosítások hatályba lépjenek.", "Show UniGetUI on the system tray": "Az UniGetUI megjelenítése a tálcán", "Show UniGetUI's version and build number on the titlebar.": "Az UniGetUI-verzió megjelenítése a címsoron", "Show WingetUI": "WingetUI megjelenítése", "Show a notification when an installation fails": "Értesítés me<PERSON>lení<PERSON>, ha a telepítés sikertelen", "Show a notification when an installation finishes successfully": "Értesítés megjelenítése a telepítés sikeres befejezéséről", "Show a notification when an operation fails": "Értesítés <PERSON>, ha egy művelet sikertelen", "Show a notification when an operation finishes successfully": "Értesítés me<PERSON>, ha egy művelet sikeresen befejeződik", "Show a notification when there are available updates": "Értesítés me<PERSON>, ha elérhető frissítések vannak", "Show a silent notification when an operation is running": "Csendes értesítés me<PERSON>, amikor egy mű<PERSON> fut", "Show details": "Részletek megjelenítése", "Show in explorer": "Megjelenítés a keresőben", "Show info about the package on the Updates tab": "A csomaginformáció megjelenítése a Frissítések lapon", "Show missing translation strings": "<PERSON><PERSON><PERSON><PERSON><PERSON> fordí<PERSON>ási stringek megjelenítése", "Show notifications on different events": "Értesítések megjelenítése különböző eseményekről", "Show package details": "Csomag részletei", "Show package icons on package lists": "Csomag ikonok megjelenítése a csomaglistákon", "Show similar packages": "Has<PERSON><PERSON>ó csomagok megjelenítése", "Show the live output": "Az élő kimenet megjelenítése", "Size": "<PERSON><PERSON><PERSON>", "Skip": "<PERSON><PERSON><PERSON>", "Skip hash check": "Hash ellenőrzés k<PERSON>ag<PERSON>a", "Skip hash checks": "Hash ellenőrzések kihagyása", "Skip integrity checks": "Integritás ellenőrzések kihagyása", "Skip minor updates for this package": "Kisebb frissítések kihagyása ehhez a csomaghoz", "Skip the hash check when installing the selected packages": "A kiválasztott csomagok telepítésekor a hash ellenőrzés kihagyása", "Skip the hash check when updating the selected packages": "A kiválasztott csomagok frissítésekor a hash ellenőrzés kihagyása", "Skip this version": "Hagyja ki ezt a verziót", "Software Updates": "Szoftver frissítések", "Something went wrong": "Valami nincs rendben", "Something went wrong while launching the updater.": "Valamilyen hiba történt a frissítő indítása közben.", "Source": "<PERSON><PERSON><PERSON>", "Source URL:": "Forrás URL:", "Source added successfully": "<PERSON><PERSON><PERSON> ho<PERSON>", "Source addition failed": "A forrás hozzáadása sikertelen", "Source name:": "Forr<PERSON> neve:", "Source removal failed": "A forrás eltávolítása sikertelen", "Source removed successfully": "<PERSON><PERSON><PERSON>volít<PERSON>", "Source:": "Forrás:", "Sources": "Források", "Start": "Start", "Starting daemons...": "Démonok indítása...", "Starting operation...": "Művelet indítása...", "Startup options": "Indítási lehetőségek", "Status": "<PERSON><PERSON><PERSON>", "Stuck here? Skip initialization": "Itt ragadt? Hagyja ki az inicializálást", "Success!": null, "Suport the developer": "Támogassa a fejlesztőt", "Support me": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Support the developer": "Támogassa a fejlesztőt", "Systems are now ready to go!": "A rendszerek most már k<PERSON>zen állnak az indulásra!", "Telemetry": "Telemetria", "Text": "Szöveg", "Text file": "Szövegfájl", "Thank you ❤": "Köszönöm ❤", "Thank you 😉": "Köszönöm 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "A Rust csomagkezelő. <br> <PERSON>rta<PERSON>: <b> Rust könyvtárak és Rust nyelven írt programok <b>", "The backup will NOT include any binary file nor any program's saved data.": "A biztonsági mentés NEM tartalmaz semmilyen bináris fájlt vagy program mentett adatait.", "The backup will be performed after login.": "A biztonsági mentés a bejelentkezés után történik.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "A biztonsági mentés tartalmazza a telepített csomagok teljes listáját és a telepítési beállításokat. A figyelmen kívül hagyott frissítések és kihagyott verziók is mentésre k<PERSON>ülnek.\n", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "A köteg amelyet be akar t<PERSON>, érvénytelennek tűnik. Ellenőrizze a fájlt, és próbálja meg újra.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "A telepítő ellenőrző összege nem egyezik meg a várt érték<PERSON>, a telepítő hitelessége nem ellenőrizhető. Ha megbízik a kiadóban, {0} a csomag ismét kimarad a hash ellenőrzésből.\n", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "\nA klasszikus csomagkezelő Windowshoz. Mindent megtalálsz benne. <br>Tartalma: <b><PERSON><PERSON><PERSON><PERSON><PERSON> szoftverek</b>", "The cloud backup completed successfully.": "A felhőalapú biztonsági mentés sikeresen befejeződött.", "The cloud backup has been loaded successfully.": "A felhőalapú biztonsági mentés sikeresen betöltődött.", "The current bundle has no packages. Add some packages to get started": "Az aktuális köteg nem tartalmaz csomagokat. A kezdéshez adjon hozzá néhány csomagot", "The executable file for {0} was not found": "A {0} futt<PERSON>ató fájlja nem található", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "A következő beállítások alapértelmezés szerint minden alkalommal alkalmazásra k<PERSON>ü<PERSON>, amikor egy {0} csomag telepítése, frissítése vagy eltávolítása történik.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "A következő csomagokat fogjuk exportálni egy JSON fájlba. Felhasználói adatok vagy binárisok nem kerülnek elmentésre.", "The following packages are going to be installed on your system.": "A következő csomagokat kell telepíteni a rendszerre.", "The following settings may pose a security risk, hence they are disabled by default.": "A következő beállítások biztonsági kockázatot jelenthetnek, ez<PERSON>rt alapértelmezés szerint le vannak tiltva.", "The following settings will be applied each time this package is installed, updated or removed.": "A következő beállítások minden egyes alkalommal alkalmazásra k<PERSON>ülnek, amikor a csomag telepítése, frissítése vagy eltávolítása megtörténik.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "A következő beállítások minden egyes alkalommal alkalmazásra kerülnek, amikor a csomag telepítése, frissítése vagy eltávolítása megtörténik. A beállítások automatikusan mentésre kerülnek.\n", "The icons and screenshots are maintained by users like you!": "Az ikonokat és a képernyőképeket Önhöz hasonló felhasználók tartják karban!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "A telepítő hitelességét nem sikerült ellenőrizni.", "The installer has an invalid checksum": "A telepítő érvénytelen ellenőrző összeggel rendelkezik", "The installer hash does not match the expected value.": "A telepítő hash értéke nem felel meg a várt értéknek.", "The local icon cache currently takes {0} MB": "A helyi ikon gyo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} MB helyet foglal el.", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "A projekt fő célja egy intuitív felhasználói felület létrehozása a Windows leggyakoribb CLI csomagkezelőihez, mint például a Winget és a Scoop", "The package \"{0}\" was not found on the package manager \"{1}\"": "A \"{0}\" csomagot nem találta a \"{1}\" csomagkezelő.", "The package bundle could not be created due to an error.": "A csomag köteget hiba miatt nem sikerült létrehozni.", "The package bundle is not valid": "A csomag köteg nem érvényes", "The package manager \"{0}\" is disabled": "A \"{0}\" csomag<PERSON><PERSON><PERSON>", "The package manager \"{0}\" was not found": "A \"{0}\" csomagkezelőt nem találtuk meg.", "The package {0} from {1} was not found.": "A csomag {0} innen {1} nem található.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Az itt felsorolt csomagokat nem veszi figyelembe a rendszer a frissítések keresésekor. Kattintson ráju<PERSON> du<PERSON>, vagy katti<PERSON> a jobb old<PERSON> gombra, ha nem szeretné tovább figyelmen kívül hagyni a frissítéseiket.\n", "The selected packages have been blacklisted": "A kiválasztott csomagok feketelistára kerültek", "The settings will list, in their descriptions, the potential security issues they may have.": "A beállítások a leírásukban felsorolják a lehetséges biztonsági problémákat.", "The size of the backup is estimated to be less than 1MB.": "A biztonsági mentés becsült mérete kevesebb, mint 1 MB.", "The source {source} was added to {manager} successfully": "A forrás {source} si<PERSON><PERSON>n hozzá lett adva {manager}.", "The source {source} was removed from {manager} successfully": "A {source} for<PERSON><PERSON>ávolítva innen {manager}", "The system tray icon must be enabled in order for notifications to work": "Az értesítések működéséhez engedélyezni kell a tálcaikont.", "The update process has been aborted.": "A frissítési folyamat megszakadt.", "The update process will start after closing UniGetUI": "A frissítési folyamat az UniGetUI bezárása után kezdődik.", "The update will be installed upon closing WingetUI": "A frissítés a WingetUI bezárásakor települ.", "The update will not continue.": "A frissítés nem folytatódik.", "The user has canceled {0}, that was a requirement for {1} to be run": "A felhasználó törölte ezt {0}, ami felt<PERSON>tele volt {1} futtatásának", "There are no new UniGetUI versions to be installed": "Nincsenek új UniGetUI verziók, amelyeket telepíteni kellene", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Folyamatban vannak műveletek. A WingetUI elhagyása ezek meghibásodását okozhatja. Folytatni szeretné?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "A YouTube-on van néhány nagyszerű videó, amely bemutatja a WingetUI-t és annak képességeit. Hasznos trükköket és tippeket ismerhet meg!\n", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Két fő oka van <PERSON>, hogy ne futtassuk a WingetUI-t rendszergazdaként: <PERSON><PERSON> első az, hogy a Scoop csomagkezelő problémákat okozhat bizonyos parancsokkal, ha rendszergazdai jogokkal fut. A második az, hogy a WingetUI rendszergazdaként való futtatása azt jelenti, hogy minden letöltött csomag rendszergazdaként fut (és ez nem biztonságos). <PERSON>e feledje, hogy ha egy adott csomagot rendszergazdaként kell telepítenie, mindig kattintson a jobb gombbal az elemre -> Telepítés/Frissítés/Eltávolítás rendszergazdaként.", "There is an error with the configuration of the package manager \"{0}\"": "<PERSON><PERSON> van a \"{0}\" csomagkezelő konfigurációjával.", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "A telepítés folyamatban van. Ha bezárja a WingetUI-t, a telepítés meghiúsulhat és váratlan eredménnyel járhat. Még mindig itt akarja hagyni a WingetUI-t?", "They are the programs in charge of installing, updating and removing packages.": "Ezek a programok felelősek a csomagok telepítéséért, frissítéséért és eltávolításáért.", "Third-party licenses": "Harmadik fél licencei", "This could represent a <b>security risk</b>.": "Ez <b>bi<PERSON><PERSON><PERSON><PERSON> k<PERSON></b> j<PERSON><PERSON><PERSON><PERSON>.", "This is not recommended.": "<PERSON>z nem a<PERSON>.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Ennek oka valószínűleg az, hogy a küldött csomagot eltávolították, vagy egy olyan csomagkezelőn tették közzé, amely nincs engedélyezve. A kapott azonosító {0}", "This is the <b>default choice</b>.": "Ez az <b>alapértelmezett választás</b>.", "This may help if WinGet packages are not shown": "<PERSON><PERSON> seg<PERSON><PERSON>, ha a Win<PERSON>et csomagok nem jelennek meg", "This may help if no packages are listed": "<PERSON><PERSON> se<PERSON><PERSON>, ha nincsenek csomagok a listában", "This may take a minute or two": "<PERSON>z egy-két percet vehet igénybe", "This operation is running interactively.": "Ez a művelet interaktívan fut.", "This operation is running with administrator privileges.": "Ez a művelet rendszergazdai jogosultságokkal fut.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Ez az opció problémákat fog okozni. Minden olyan <PERSON>, amely nem képes mag<PERSON> kiemel<PERSON>, SIKERTELEN lesz. A rendszergazdaként történő telepítés/frissítés/eltávolítás NEM FOG MŰKÖDNI.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Ez a csomag köteg tartalmazott néhány potenciálisan vesz<PERSON><PERSON>, amelyeket alapértelmezés szerint figyelmen kívül lehet hagyni.", "This package can be updated": "Ez a csomag frissíthető", "This package can be updated to version {0}": "Ez a csomag frissíthető a következő verzióra: {0}", "This package can be upgraded to version {0}": "Ez a csomag frissíthető a {0} verzióra.", "This package cannot be installed from an elevated context.": "Ez a csomag nem telepíthető emelt szintű környezetből.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Ez a csomag nem rendelkezik képernyőképekkel, esetleg hiányzik az ikon? Segítsen a WingetUI-nak a hiányzó ikonok és képernyőképek hozzáadásával a nyílt, nyilvános adatbázisunkhoz.\n", "This package is already installed": "Ez a csomag már telepítve van", "This package is being processed": "Ez a csomag feldolgozás alatt áll", "This package is not available": "Ez a csomag nem elérhető", "This package is on the queue": "Ez a csomag várólistán van", "This process is running with administrator privileges": "Ez a folyamat rendszergazdai jogosultságokkal fut", "This project has no connection with the official {0} project — it's completely unofficial.": "Ennek a projektnek semmi köze a hivatalos {0} projekthez - teljesen nem hivatalos.\n", "This setting is disabled": "<PERSON>z a beállítás <PERSON> tilt<PERSON>", "This wizard will help you configure and customize WingetUI!": "Ez a varázsló segíti a WingetUI konfigurálását és testreszabását!", "Toggle search filters pane": "Keresési szűrők panel váltás", "Translators": "Fordítók", "Try to kill the processes that refuse to close when requested to": "Próbálja meg leállítani azokat a folyamatokat, am<PERSON><PERSON> nem hajlandóak be<PERSON>rni, amikor erre k<PERSON>.", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Ennek bekapcsolása lehetővé teszi a csomagkezelőkkel való interakcióhoz használt futtatható fájl megváltoztatását. Ez lehetővé teszi a telepítési folyamatok finomabb testreszabás<PERSON>t, de vesz<PERSON><PERSON><PERSON> is lehet.", "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON><PERSON> be ide a hozzáadni kívánt forrás nevét és URL címét, szóközzel elválasztva.", "Unable to find package": "<PERSON><PERSON> a csomag", "Unable to load informarion": "<PERSON><PERSON> betölteni az információkat", "UniGetUI collects anonymous usage data in order to improve the user experience.": "Az UniGetUI anonim használati adatokat gyűjt a felhasználói élmény javítása érdekében.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "Az UniGetUI anonim használati adatokat gyűjt, kizárólag azzal a céllal, hogy megértse és javítsa a felhasználói élményt.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "Az UniGetUI egy új asztali parancsikont észlelt, amely auto. törölhető.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "Az UniGetUI a következő asztali parancsikonokat észlelte, amelyek a jövőbeni frissítések során auto. eltávolíthatók", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "Az UniGetUI Az UniGetUI {0}  új asztali parancsikont észlelt, amelyek automatikusan törölhetők.", "UniGetUI is being updated...": "Az UniGetUI frissül...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "Az UniGetUI nem kapcsolódik egyik kompatibilis csomagkezelőhöz sem. Az UniGetUI egy független projekt.", "UniGetUI on the background and system tray": "UniGetUI a háttérben és a tálcán", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "Az UniGetUI működéséhez {0} sz<PERSON><PERSON><PERSON>ges, de nem találtuk meg a rendszerében.", "UniGetUI startup page:": "UniGetUI kezdőlap:", "UniGetUI updater": "UniGetUI frissítő", "UniGetUI version {0} is being downloaded.": "Az UniGetUI {0} verziójának letöltése folyamatban van.", "UniGetUI {0} is ready to be installed.": "Az UniGetUI {0} készen áll a telepítésre.", "Uninstall": "Eltávolítás", "Uninstall Scoop (and its packages)": "<PERSON> (és csomagjai) eltávolítása", "Uninstall and more": "Eltávolítás és egyebek", "Uninstall and remove data": "Eltávolítás és adatok eltávolítása", "Uninstall as administrator": "Eltávolítás rendszergazdaként", "Uninstall canceled by the user!": "Az eltávolítást a felhasználó megszakította!", "Uninstall failed": "<PERSON>z eltávolí<PERSON><PERSON>", "Uninstall options": "Eltávolítás lehetőségei", "Uninstall package": "Csomag eltávolítása", "Uninstall package, then reinstall it": "Távolítsa el a csomagot, majd telepítse újra", "Uninstall package, then update it": "Távolítsa el a csomagot, majd frissítse azt", "Uninstall previous versions when updated": "A korábbi verziók eltávolítása frissítéskor", "Uninstall selected packages": "A kiválasztott csomagok eltávolítása", "Uninstall selection": null, "Uninstall succeeded": "Az eltávolítás sikeres volt", "Uninstall the selected packages with administrator privileges": "A kiválasztott csomagok eltávolítása rendszergazdai jogosultságokkal", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "A \"{0}\" eredetű eltávolítható csomagok nem jelennek meg egyetlen csomagkezelőben sem, így nem áll rendelkezésre semmilyen információ róluk.\n", "Unknown": "Ismeretlen", "Unknown size": "<PERSON><PERSON><PERSON><PERSON> méret", "Unset or unknown": "Be nem <PERSON><PERSON><PERSON><PERSON> vagy is<PERSON>tlen", "Up to date": "Naprak<PERSON><PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "A WingetUI automatikus frissítése", "Update all": "Az összes frissítése", "Update and more": "Frissítés és egyebek", "Update as administrator": "Frissítés rendszergazdaként", "Update check frequency, automatically install updates, etc.": "Frissítések ellenőrz. g<PERSON><PERSON>, frissítések autom. telepítése, stb.", "Update checking": null, "Update date": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update failed": "A frissítés si<PERSON>telen", "Update found!": "Frissítés található!", "Update now": "<PERSON><PERSON><PERSON><PERSON><PERSON> most", "Update options": "Frissítés <PERSON>égei", "Update package indexes on launch": "Csomagindexek frissítése indításkor", "Update packages automatically": "Csomagok autom. frissítése", "Update selected packages": "Kiválasztott csomagok frissítése", "Update selected packages with administrator privileges": "A kiválasztott csomagok frissítése rendszergazdai jogosultságokkal", "Update selection": null, "Update succeeded": "A frissítés sikeres volt", "Update to version {0}": "Frissítés a {0} verzióra", "Update to {0} available": "Frissítés - {0} elérhető", "Update vcpkg's Git portfiles automatically (requires Git installed)": "A vcpkg Git portfájlok auto. frissítése (telepített Git szükséges)", "Updates": "Friss<PERSON><PERSON>sek", "Updates available!": "Frissítések érhetők el!", "Updates for this package are ignored": "A csomag frissítései figyelmen kívül maradnak", "Updates found!": "Frissítés található!", "Updates preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updating WingetUI": "A WingetUI frissítése", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "A PowerShell CMDLets helyett a Legacy csomagban lévő WinGet használata", "Use a custom icon and screenshot database URL": "Egyéni ikon és képernyőkép adatbázis URL használata", "Use bundled WinGet instead of PowerShell CMDlets": "A csomagban található WinGet használata a PowerShell CMDlets helyett", "Use bundled WinGet instead of system WinGet": "A csomagban lévő WinGet használata a rendszerbeli WinGet helyett", "Use installed GSudo instead of UniGetUI Elevator": "A telepített GSudo használata az UniGetUI Elevator helyett", "Use installed GSudo instead of the bundled one": "A telepített GSudo használata a kötegben lévő helyett (az alkalmazás újraindítása szükséges)", "Use system Chocolatey": "Használja a rendszerbeli Chocolatey-t", "Use system Chocolatey (Needs a restart)": "A rendszer Chocolatey használata (újraindítás szükséges)", "Use system Winget (Needs a restart)": "Rendszer Winget használata (újra kell indítani)", "Use system Winget (System language must be set to english)": "A rendszerbeli Winget használata (A rendszer nyelvének angolra kell lennie beállítva)", "Use the WinGet COM API to fetch packages": "A WinGet COM API használata a csomagok lekérdezéséhez", "Use the WinGet PowerShell Module instead of the WinGet COM API": "A WinGet PowerShell modul használata a WinGet COM API helyett", "Useful links": "<PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User interface preferences": "A felhasználói felület beállításai", "User | Local": "Felhasználói | Helyi", "Username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "A WingetUI használata a GNU Lesser General Public License v2.1 licenc elfogadását jelenti.", "Using WingetUI implies the acceptation of the MIT License": "A WingetUI használata magában foglalja az MIT-licenc elfogadását", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg root nem található. K<PERSON>rj<PERSON>k, adja meg a %VCPKG_ROOT% környezeti változót, vagy hat<PERSON> meg az UniGetUI beállításaiból.", "Vcpkg was not found on your system.": "A Vcpkg nem található a rendszerében.", "Verbose": "Bővebben", "Version": "<PERSON><PERSON><PERSON><PERSON>", "Version to install:": "Telepítendő verzió:", "Version:": "Verzió:", "View GitHub Profile": "GitHub profil megtekintése", "View WingetUI on GitHub": "WingetUI a GitHubon", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "A WingetUI forráskódjának megtekintése. <PERSON><PERSON> j<PERSON> hib<PERSON>, java<PERSON><PERSON>, v<PERSON><PERSON> a<PERSON><PERSON><PERSON> is hozzájárulhat a WingetUI projekthez.\n", "View mode:": "Megtekin<PERSON><PERSON> mód:", "View on UniGetUI": "Megtekintés az Unigetui-n", "View page on browser": "Megtekintés böngészőben", "View {0} logs": "{0} naplók megtekintése", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>, amíg a készülék csatlakozik az internethez, miel<PERSON>tt kapcsolatot igénylő feladatokat próbálna végrehajtani.", "Waiting for other installations to finish...": "Várakozás a többi telepítés befejezésére...", "Waiting for {0} to complete...": "Várakozás {0} befejezésére...", "Warning": "Figyelmeztetés", "Warning!": "Figyelem!", "We are checking for updates.": "Ellenőrizzük a frissítéseket.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Nem tudunk r<PERSON>zletes információt betölteni erről a csomagról, mert nem találtuk meg egyik csomagforrásban sem.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Nem tudunk r<PERSON>zletes információkat betölteni erről a csomagról, mert nem egy elérhető csomagkezelőből lett telepítve.\n", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON><PERSON> tud<PERSON> {action} - {package}. Próbálja később újra. Kattintson a \"{showDetails}\" gombra a telepítő naplóinak megtekintéséhez.\n", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON><PERSON> t<PERSON> {action} - {package}. Próbálja később újra. Kattintson a \"{showDetails}\" gombra az eltávolító program naplóinak megtekintéséhez.", "We couldn't find any package": "<PERSON><PERSON> ta<PERSON> semmilyen c<PERSON>", "Welcome to WingetUI": "Köszönti a WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": "A csomagok kötegelt telepítésekor a már telepített csomagokat is telepíti.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Mikor új parancsikonokat é<PERSON>lel, autom. törli ezeket ah<PERSON>, hogy megjelenítené ezt a párbeszédpanelt.", "Which backup do you want to open?": "Melyik biztonsági mentést szeretné megnyitni?", "Which package managers do you want to use?": "Melyik csomagkezelőt szeretné használni?", "Which source do you want to add?": "Melyik forrást s<PERSON>etné ho<PERSON>áadni?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Míg a Winget a WingetUI-n belül <PERSON>, a WingetUI más csomagkezelőkkel is hasz<PERSON><PERSON><PERSON><PERSON>, ami zavaró lehet. A múltban a WingetUI-t úgy tervezték, hogy csak a Winget-tel mű<PERSON>ö<PERSON>, de ez már nem igaz, és ezért a WingetUI nem azt képviseli, amivé ez a projekt válni szeretne.\n", "WinGet could not be repaired": "A WinGet nem javítható", "WinGet malfunction detected": "WinGet hibás működés észlelve", "WinGet was repaired successfully": "A WinGet sikeresen javítva lett", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - Minden naprakész\n", "WingetUI - {0} updates are available": "WingetUI – {0} frissítés érhető el", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI honlap", "WingetUI Homepage - Share this link!": "WingetUI honlap - Ossza meg ezt a linket!", "WingetUI License": "WingetUI licenc", "WingetUI Log": "WingetUI Napló", "WingetUI Repository": "WingetUI Repository", "WingetUI Settings": "WingetUI beállítások", "WingetUI Settings File": "WingetUI beállítások fájl", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "A WingetUI a következő könyvtárakat használja. Ezek nélkül a WingetUI nem jöhetett volna létre.", "WingetUI Version {0}": "WingetUI verzió {0}", "WingetUI autostart behaviour, application launch settings": "WingetUI auto. indítási viselkedés, alkalmazás indítási beállítások", "WingetUI can check if your software has available updates, and install them automatically if you want to": "A WingetUI ellenőrizni tudja, hogy a szoftver rendelkezik-e elérhető frissí<PERSON>kel, és ha szeretné automatikusan telepíti azokat.", "WingetUI display language:": "WingetUI megjelenítési nyelve:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "A WingetUI-t rendszergazdakén<PERSON> futtatta, ami nem a<PERSON>. Ha a WingetUI-t rendszergazdaként futtatja, MINDEN WingetUI-ból indított művelet rendszergazdai jogosultságokkal rendelkezik. A programot továbbra is <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, de <PERSON><PERSON><PERSON> java<PERSON>, hogy a WingetUI-t ne futtassa rendszergazdai jogosultságokkal.\n", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "A WingetUI-t több mint 40 nyelvre fordították le az önkéntes fordítóknak köszönhetően. Köszönöm 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "A WingetUI nem gépi fordítással készült! A következő felhasználók feleltek a fordításokért:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "A WingetUI egy olyan al<PERSON>, amely megkönnyíti a szoftverek kezelését, mivel egy minden egyben grafikus felületet biztosít a parancssori csomagkezelők számára.\n", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "A WingetUI-t a<PERSON><PERSON><PERSON>vez<PERSON>, ho<PERSON> hangsúlyozzuk a különbséget a WingetUI (a most használt felület) és a Winget (a Microsoft által fejlesztett csomagkezelő, amellyel nem állok kapcsolatban) között.\n", "WingetUI is being updated. When finished, WingetUI will restart itself": "A WingetUI frissítés alatt áll. Ha befejeződött, a WingetUI újraindul.", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "A WingetUI ingyenes, és örökké az is marad. <PERSON>nc<PERSON>, nincs <PERSON>, nincs prémium verzió. 100%-ban ingyenes, örökre.", "WingetUI log": "WingetUI napló", "WingetUI tray application preferences": "WingetUI tálca alkalmazás beállításai", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "A WingetUI a következő könyvtárakat használja. Ezek nélkül a WingetUI nem jöhetett volna létre.", "WingetUI version {0} is being downloaded.": "A WingetUI {0} verziójának letöltése folyamatban van.\n", "WingetUI will become {newname} soon!": "WingetUI hamarosan {newname} lesz!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "A WingetUI nem ellenőrzi rendszeresen a frissítéseket. Indításkor továbbra is ellen<PERSON><PERSON><PERSON> fog, de nem kap figyelmeztetést ezekről.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "A WingetUI minden alkalommal megjelenít egy UAC promptot, amikor egy csomag telepítéséhez emelt szint szükséges.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "A WingetUI-nak hamarosan {newname} lesz a neve. Ez nem jelent semmilyen változást az alkalmazásban. Én (a fejlesztő) folytatni fogom a projekt fejlesztését, ahogy most is teszem, de más néven.\n", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "A WingetUI nem jöhetett volna létre kedves közreműködőink segítsége nélkül. Nézze meg GitHub profiljaikat, a WingetUI nem lenne lehetséges nélkülük!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "A WingetUI nem jöhetett volna létre a közreműködők segítsége nélkül. Köszönjük mindenkinek 🥳", "WingetUI {0} is ready to be installed.": "A WingetUI {0} k<PERSON><PERSON> áll a telepítésre.", "Write here the process names here, separated by commas (,)": "<PERSON><PERSON>ja ide a folyamatok neveit vesszővel (,) elválasztva.", "Yes": "Igen", "You are logged in as {0} (@{1})": "<PERSON>n be <PERSON>, mint {0} (@{1} )", "You can change this behavior on UniGetUI security settings.": "Ezt a viselkedést megváltoztathatja az UniGetUI biztonsági beállításainál.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Meghatározhatja azokat a parancsokat, am<PERSON><PERSON> a csomag telepítése, frissítése vagy eltávolítása előtt vagy után futnak. Ezek a parancsok egy parancssorban fognak futni, így a CMD szkriptek itt is működni fognak.", "You have currently version {0} installed": "<PERSON><PERSON><PERSON> a {0} ve<PERSON><PERSON><PERSON> van telepítve", "You have installed WingetUI Version {0}": "Ön telepítette a WingetUI {0} verzióját.", "You may lose unsaved data": "Elveszítheti a nem mentett adatokat", "You may need to install {pm} in order to use it with WingetUI.": "<PERSON><PERSON><PERSON>, hogy telepítenie kell a {pm}-t, hogy a WingetUI-val használni tudja.", "You may restart your computer later if you wish": "Később újraindíthatja a számítógépet, ha szeretné.", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "A rendszer csak egyszer fogja kérni, és a rendszergazdai jogokat az azokat igénylő csomagok kapják meg.", "You will be prompted only once, and every future installation will be elevated automatically.": "A rendszer csak egyszer fogja kérni, és minden további telepítés auto. emelt jogokal történik.", "You will likely need to interact with the installer.": "Valószínűleg rá kell pillantani a telepítőre.", "[RAN AS ADMINISTRATOR]": "RENDSZERGAZDAKÉNT FUTOTT", "buy me a coffee": "hívjon meg egy ká<PERSON>", "extracted": "kivont", "feature": "<PERSON><PERSON><PERSON>", "formerly WingetUI": "korábban WingetUI", "homepage": "honlap", "install": "telepítés", "installation": "telepítés", "installed": "telepítve", "installing": "telepítés", "library": "könyvtár", "mandatory": "kötelező", "option": "opci<PERSON>", "optional": "lehetőség", "uninstall": "eltávolítás", "uninstallation": "eltávolítás", "uninstalled": "eltávolítva", "uninstalling": "eltávolítás", "update(noun)": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "update(verb)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updated": "frissítve", "updating": "f<PERSON><PERSON><PERSON><PERSON><PERSON>", "version {0}": "verzi<PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": " {0} telepítési beállításai j<PERSON>nleg <PERSON>, mivel {0} követi az alapért. telepítési beállításokat.", "{0} Uninstallation": "{0} Eltávolítás", "{0} aborted": "{0} megszakítva", "{0} can be updated": "{0} frissí<PERSON>tő", "{0} can be updated to version {1}": "{0} frissíthető {1} verzióra.", "{0} days": "{0} nap", "{0} desktop shortcuts created": "{0} <PERSON><PERSON><PERSON><PERSON> paranc<PERSON> létrehozva", "{0} failed": "{0} nem <PERSON>", "{0} has been installed successfully.": "{0} si<PERSON><PERSON>n telepítve.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} sikeresen telepítve. A telepítés befejezéséhez ajánlott újraindítani az UniGetUI-t.", "{0} has failed, that was a requirement for {1} to be run": "{0} nem si<PERSON>, ez volt a feltétele {1} futtatásának", "{0} homepage": "{0} honlap", "{0} hours": "{0} <PERSON>ra", "{0} installation": "{0} telepítés", "{0} installation options": "{0} telepítési lehetőségek", "{0} installer is being downloaded": "{0} telepítő letöltése folyamatban van", "{0} is being installed": "{0} telep<PERSON><PERSON><PERSON>", "{0} is being uninstalled": "{0} eltávolításra kerül", "{0} is being updated": "{0} f<PERSON><PERSON><PERSON>l", "{0} is being updated to version {1}": "{0} frissül az {1} verzióra.", "{0} is disabled": "{0} <PERSON>", "{0} minutes": "{0} perc", "{0} months": "{0} hónap", "{0} packages are being updated": "{0} csomag friss<PERSON>l", "{0} packages can be updated": "{0} csomag friss<PERSON>ő", "{0} packages found": "{0} csomag <PERSON>ó", "{0} packages were found": "{0} csomagot tal<PERSON>unk", "{0} packages were found, {1} of which match the specified filters.": "{0} c<PERSON><PERSON><PERSON>, am<PERSON><PERSON> {1} megfelel a megadott szűrőknek.", "{0} selected": null, "{0} settings": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} status": "{0} <PERSON><PERSON><PERSON>", "{0} succeeded": "{0} sikeres", "{0} update": "{0} f<PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} updates are available": "{0} f<PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} was {1} successfully!": "{0} si<PERSON>esen {1}!", "{0} weeks": "{0} hét", "{0} years": "{0} év", "{0} {1} failed": "{0} {1} <PERSON><PERSON><PERSON><PERSON>", "{package} Installation": "{package} Telepítés", "{package} Uninstall": "{package} Eltávolítás", "{package} Update": "{package} <PERSON><PERSON><PERSON><PERSON><PERSON>", "{package} could not be installed": "{package} nem tudott települni", "{package} could not be uninstalled": "{package} nem lehetett eltá<PERSON>lítani", "{package} could not be updated": "{package} nem tudott f<PERSON>i", "{package} installation failed": "{package} telepí<PERSON><PERSON> si<PERSON>en", "{package} installer could not be downloaded": "{package} a telepítőt nem lehetett letölteni", "{package} installer download": "{package} a telepítő letöltése", "{package} installer was downloaded successfully": "{package} a telepítő sikeresen letöltődött", "{package} uninstall failed": "{package} eltávolítása sikertelen", "{package} update failed": "{package} fris<PERSON><PERSON><PERSON><PERSON>en", "{package} update failed. Click here for more details.": "{package} friss<PERSON><PERSON><PERSON> si<PERSON>telen. További részletekért kattintson ide.", "{package} was installed successfully": "{package} telepítése si<PERSON>esen me<PERSON>", "{package} was uninstalled successfully": "{package} eltávolítása si<PERSON>esen me<PERSON>t", "{package} was updated successfully": "{package} si<PERSON><PERSON>n friss<PERSON>ve", "{pcName} installed packages": "{pcName} telepített csomagok", "{pm} could not be found": "{pm} nem ta<PERSON>", "{pm} found: {state}": "{pm} találat: {state}", "{pm} is disabled": "{pm} le <PERSON>", "{pm} is enabled and ready to go": "{pm} en<PERSON><PERSON><PERSON><PERSON><PERSON>, és használatra kész", "{pm} package manager specific preferences": "{pm} csomagkezelő specifikus beállítások", "{pm} preferences": "{pm} be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{pm} version:": "{pm} verzió:", "{pm} was not found!": "{pm} nem tal<PERSON>!"}