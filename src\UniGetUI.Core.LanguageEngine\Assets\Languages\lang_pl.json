{"\"{0}\" is a local package and can't be shared": "\"{0}\" jest pakietem lokalnym i nie może być udostęp<PERSON>ny", "\"{0}\" is a local package and does not have available details": "\"{0}\" jest pakietem lokalnym i nie ma dostępnych szczegółów", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" jest pakietem lokalnym i nie jest kompatybilny z tą funkcją", "(Last checked: {0})": "(Ostatnio sprawdzane: {0})", "(Number {0} in the queue)": "(Numer {0} w kolejce)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@juliazero, @RegularGvy13, @<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, @kwi<PERSON><PERSON><PERSON>, @ThePhaseless, @GrzegorzKi, @ikarmus2001, @s<PERSON><PERSON><PERSON>, @H4qu3r", "0 packages found": "Znaleziono 0 pakietów", "0 updates found": "Znaleziono 0 aktualizacji", "1 - Errors": "1 - <PERSON><PERSON><PERSON><PERSON>", "1 day": "1 dzień", "1 hour": "1 godzina", "1 month": "1 miesiąc", "1 package was found": "Znaleziono 1 pakiet", "1 update is available": "Dostępna 1 aktualizacja", "1 week": "1 tydzień", "1 year": "1 rok", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON><PERSON> do strony \"{0}\" lub \"{1}\".", "2 - Warnings": "2 - Ostrzeżenia", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON><PERSON><PERSON>(y), kt<PERSON>re chcesz dodać do paczki i zaznacz ich pola wyboru znajdujące się po lewej.", "3 - Information (less)": "3 - Informacje (mniej)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. Po wybraniu paki<PERSON>ów, które chcesz dodać do paczki, znajdź i kliknij opcję \"{0}\" na pasku narzędzi.", "4 - Information (more)": "4 - Informacje (więcej)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON> pakiety zostaną dodane do paczki. Kontynu<PERSON>j <PERSON>, lub eksportuj paczkę.", "5 - information (debug)": "5 - Inform<PERSON><PERSON> (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Menedżer bibliotek C/C++. Pełen bibliotek C/C++ i innych narzędzi związanych z C/C++ <br>Zawiera: <b>biblioteki C/C++ i powiązane narzędzia</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Repozytorium pełne narzędzi i plików wykonywalnych zaprojektowanych z myślą o ekosystemie .NET firmy Microsoft.<br>Zawiera: <b>narzędzia i skrypty powiązane z .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Repozytorium pełne narzędzi zaprojektowanych z myślą o ekosystemie .NET firmy Microsoft. <br>Zawiera: <b>narzędzia powiązane z .NET</b>", "A restart is required": "<PERSON><PERSON><PERSON><PERSON> jest ponowne urucho<PERSON>nie", "Abort install if pre-install command fails": "<PERSON><PERSON><PERSON><PERSON><PERSON>tal<PERSON>, jeśli wykonanie komendy przedinstalacyjnej zakończy się niepowodzeniem", "Abort uninstall if pre-uninstall command fails": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jeśli wykonanie komendy przeddezinstalacyjnej zakończy się niepowodzeniem", "Abort update if pre-update command fails": "Przer<PERSON>j aktual<PERSON>, jeśli wykonanie komendy przedaktualizacyjnej zakończy się niepowodzeniem", "About": "<PERSON><PERSON><PERSON><PERSON>j", "About Qt6": "O Qt6", "About WingetUI": "O UniGetUI", "About WingetUI version {0}": "O wersji {0} UniGetUI", "About the dev": "O deweloperze", "Accept": "Ak<PERSON>pt<PERSON>j", "Action when double-clicking packages, hide successful installations": "Akcja po dwukrotnym kliknięciu pakietów, ukryj pomyślne instalacje", "Add": "<PERSON><PERSON><PERSON>", "Add a source to {0}": "Do<PERSON>j <PERSON>ło do {0}", "Add a timestamp to the backup file names": "Dodaj znacznik czasu do nazwy plików kopii zapasowych", "Add a timestamp to the backup files": "Dodaj znacznik czasu do plików kopii zapasowych", "Add packages or open an existing bundle": "<PERSON><PERSON>j pakiety albo otw<PERSON>rz istniejącą paczkę", "Add packages or open an existing package bundle": "Do<PERSON>j pakiety albo otw<PERSON>rz istniejącą paczkę pakietów", "Add packages to bundle": "Dodaj pakiety do paczki", "Add packages to start": "<PERSON><PERSON><PERSON>, dodaj paki<PERSON>", "Add selection to bundle": "Dodaj wybrane do paczki", "Add source": "<PERSON><PERSON><PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Dodaj aktualizacje, które zakończyły się niepowodzeniem z komunikatem „nie znaleziono odpowiedniej aktualizacji” do listy ignorowanych aktualizacji", "Adding source {source}": "Dodawan<PERSON>ódła {source}", "Adding source {source} to {manager}": "Do<PERSON><PERSON><PERSON> {source} do {manager}", "Addition succeeded": "Dodanie powiodło się", "Administrator privileges": "Uprawnienia administratora", "Administrator privileges preferences": "Ustawienia uprawnień administratora", "Administrator rights": "Uprawnienia administratora", "Administrator rights and other dangerous settings": "Uprawnienia administratora i inne niebezpieczne ustawienia", "Advanced options": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "All files": "Wszystkie pliki", "All versions": "Wszystkie wersje", "Allow changing the paths for package manager executables": "Zezwól na modyfikowanie ścieżek dla plików wykonalnych menedżerów pakietów", "Allow custom command-line arguments": "Zezwalaj na niestandardowe argumenty wiersza poleceń", "Allow importing custom command-line arguments when importing packages from a bundle": "Zezwalaj na importowanie niestandardowych argumentów wiersza poleceń podczas importowania pakietów z paczki", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Zezwól na importowanie niestandardowych komend przed- i poinstalacyjnych podczas importowania pakietów z paczki", "Allow package operations to be performed in parallel": "Pozwól na równoległe wykonywanie operacji na pakietach", "Allow parallel installs (NOT RECOMMENDED)": "Zezwalaj na instalacje równoległe (NIEZALECANE)", "Allow pre-release versions": "Zezwól na wersje wczesnego dostępu", "Allow {pm} operations to be performed in parallel": "Pozwól {pm} na równoległe wykonywanie operacji", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, m<PERSON><PERSON><PERSON><PERSON> {0}, wykonując następującą komendę w Windows PowerShell:", "Always elevate {pm} installations by default": "<PERSON><PERSON><PERSON> podnoś uprawienia dla {pm} podczas instalacji", "Always run {pm} operations with administrator rights": "Zawsze uruchamiaj operacje {pm} z uprawnieniami administratora", "An error occurred": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd", "An error occurred when adding the source: ": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas dodawania źródła:", "An error occurred when attempting to show the package with Id {0}": "Wystąpił błąd podczas próby wyświetlenia paczki o ID {0}", "An error occurred when checking for updates: ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas sprawdzania dostępności aktualizacji:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas ładowania kopii zapasowej:", "An error occurred while logging in: ": "Wystą<PERSON>ł błąd podczas logowania:", "An error occurred while processing this package": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas przetwarzania tego pakietu", "An error occurred:": "Wystą<PERSON>ł błąd:", "An interal error occurred. Please view the log for further details.": "Wystąpił błąd wewnętrzny. Sprawdź dziennik zdarzeń, aby uzyskać więcej informacji.", "An unexpected error occurred:": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł nieoczekiwany błąd:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Wystąpił nieoczekiwany błąd podczas próby naprawy WinGet. Spróbuj ponownie później", "An update was found!": "Znaleziono aktualizację!", "Android Subsystem": "Podsystem Android", "Another source": "Inne źródło", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Wszelkie nowe skróty utworzone podczas instalacji lub aktualizacji będą usuwane automatycznie, zamiast wyświetlania monitu o potwierdzenie przy pierwszym wykryciu.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Wszelkie skróty utworzone lub zmodyfikowane poza UniGetUI będą ignorowane. Będzie je można dodać za pomocą przycisku {0}.", "Any unsaved changes will be lost": "Wszelkie niezapisane zmiany zostaną utracone", "App Name": "Nazwa aplikacji", "Appearance": "Wygląd", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON><PERSON>, s<PERSON><PERSON>, <PERSON><PERSON><PERSON>, w<PERSON><PERSON><PERSON><PERSON><PERSON> pomyślne instalacje automatycznie", "Application theme:": "<PERSON><PERSON>w <PERSON>acji:", "Apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Architecture to install:": "Architektura do instalacji:", "Are these screenshots wron or blurry?": "Czy te zrzuty ekranu są błędne lub niewyraźne?", "Are you really sure you want to enable this feature?": "<PERSON>zy na pewno chcesz włączyć tę funkcję?", "Are you sure you want to create a new package bundle? ": "<PERSON>zy na pewno chcesz utworzyć nową paczkę?", "Are you sure you want to delete all shortcuts?": "<PERSON>zy na pewno chcesz usunąć wszystkie skróty?", "Are you sure?": "Na pewno?", "Ascendant": "Rosnąco", "Ask for administrator privileges once for each batch of operations": "Poproś o uprawnienia administratora raz dla każdej grupy operacji", "Ask for administrator rights when required": "Poproś o uprawnienia administratora gdy będą wymagane", "Ask once or always for administrator rights, elevate installations by default": "Pytaj raz lub zawsze o prawa administratora, domyślnie podnoś uprawnienia instalacji", "Ask only once for administrator privileges": "Pytaj tylko raz o uprawnienia administratora", "Ask only once for administrator privileges (not recommended)": "Pytaj tylko raz o uprawnienia administratora (niezalecane)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Zapytaj czy usunąć ikony stworzone podczas instalacji lub aktualizacji.", "Attention required": "<PERSON><PERSON><PERSON> uwa<PERSON>", "Authenticate to the proxy with an user and a password": "Uwierzytelnianie do serwera proxy za pomocą użytkownika i hasła", "Author": "Autor", "Automatic desktop shortcut remover": "Automatyczne narzędzie do usuwania skrótów z pulpitu", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Automatyczne zapisywanie listy wszystkich zainstalowanych pakietów w celu ich łatwego przywrócenia.", "Automatically save a list of your installed packages on your computer.": "Automatycznie zapisz listę zainstalowanych pakietów na komputerze.", "Autostart WingetUI in the notifications area": "Autostart UniGetUI w obszarze powiadomień", "Available Updates": "Dostępne aktualizacje", "Available updates: {0}": "Dostępne aktualizacje: {0}", "Available updates: {0}, not finished yet...": "Dostępne aktualizacje: {0}, jeszcze szukam...", "Backing up packages to GitHub Gist...": "Tworzenie kopii zapasowej pakietów w GitHub Gist...", "Backup": "<PERSON><PERSON><PERSON><PERSON> kop<PERSON>", "Backup Failed": "Wykonanie kopii zapasowej nie powiodło się", "Backup Successful": "Wykonanie kopii zapasowej powiodło się", "Backup and Restore": "Tworzenie i przywracanie kopii zapasowej", "Backup installed packages": "Kopia zapasowa zainstalowanych pakietów", "Backup location": "Lokalizacja kopii zapasowej", "Become a contributor": "Zostań współtwórcą", "Become a translator": "Zostań tłumaczem", "Begin the process to select a cloud backup and review which packages to restore": "Rozpocznij proces wyboru kopii zapasowej w chmurze i sprawdź, które pakiety chcesz przywrócić.", "Beta features and other options that shouldn't be touched": "Funkcje beta i inne opcje, które nie powinny być <PERSON>ż<PERSON>wane", "Both": "Oba", "Bundle security report": "Raport bezpieczeństwa paczki", "But here are other things you can do to learn about WingetUI even more:": "Ale są też inne rzeczy, które moż<PERSON>z <PERSON>, aby dowiedzieć się więcej o UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Wyłączenie menedżera pakietów spowoduje, że nie będzie można już wyświetlać ani aktualizować jego pakietów.", "Cache administrator rights and elevate installers by default": "Zapamiętaj uprawnienia administratora i używaj ich domyślnie przy instalacji", "Cache administrator rights, but elevate installers only when required": "Zapamiętaj uprawnienia administratora, ale używaj ich tylko gdy instalator tego wymaga.", "Cache was reset successfully!": "Pamięć podręczna aplikacji wyczyszczona!", "Can't {0} {1}": "<PERSON><PERSON> m<PERSON> {0} {1}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "Anuluj wszystkie operacje", "Change backup output directory": "Zmień katalog wyjściowy kopii zapasowej", "Change default options": "Zmień opcje domyślne", "Change how UniGetUI checks and installs available updates for your packages": "Zmie<PERSON> sposób, w jaki UniGetUI sprawdza i instaluje dostępne aktualizacje pakietów.", "Change how UniGetUI handles install, update and uninstall operations.": "<PERSON><PERSON><PERSON>ób, w jaki UniGetUI obsługuje operacje instalacji, aktualizacji i odinstalowywania.", "Change how UniGetUI installs packages, and checks and installs available updates": "<PERSON><PERSON><PERSON> spo<PERSON>ób, w jaki UniGetUI instaluje pakiety oraz sprawdza i instaluje dostępne aktualizacje", "Change how operations request administrator rights": "Z<PERSON><PERSON> spo<PERSON>ób, w jaki operacje żądają uprawnień administratora", "Change install location": "<PERSON><PERSON><PERSON>", "Change this": "Zmień to", "Change this and unlock": "Zmień to i odblokuj", "Check for package updates periodically": "Okresowo sprawdzaj dostępność aktualizacji pakietów", "Check for updates": "Sprawdź aktualizacje", "Check for updates every:": "Sprawdzaj aktualizacje co:", "Check for updates periodically": "Sprawdzaj okresowo aktualizacje", "Check for updates regularly, and ask me what to do when updates are found.": "Sprawdzaj regularnie aktualizacje i zapytaj mnie co zrobić gdy zostaną znalezione.", "Check for updates regularly, and automatically install available ones.": "Regularnie sprawdzaj dostępność aktualizacji i automatycznie instaluj dostępne.", "Check out my {0} and my {1}!": "Sprawdź moje {0} i moje {1}!", "Check out some WingetUI overviews": "Sprawdź kilka recenzji UniGetUI", "Checking for other running instances...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, czy inne instancje są uruchomione...", "Checking for updates...": "Sprawdzanie aktualizacji...", "Checking found instace(s)...": "Sprawdzanie znalezionych instancji...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON><PERSON> liczbę operacji, które mają być wykonywane równolegle", "Clear cache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pamięć podręczną", "Clear finished operations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ukończone operacje", "Clear selection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wybór", "Clear successful operations": "W<PERSON><PERSON><PERSON>ść pomyślnie ukończone operacje", "Clear successful operations from the operation list after a 5 second delay": "Wyczyść pomyślne operacje z listy operacji po 5 sekundach przerwy", "Clear the local icon cache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pamięć podręczną ikon", "Clearing Scoop cache - WingetUI": "Czyszczenia pamięci podręcznej Scoop - UniGetUI", "Clearing Scoop cache...": "Czyszczenie pamięci podręcznej Scoop'a...", "Click here for more details": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby uzyskać więcej szczegółów", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "<PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> instalację. <PERSON><PERSON><PERSON>, UniGetUI może nie działać jak należy.", "Close": "Zamknij", "Close UniGetUI to the system tray": "Zamknij UniGetUI do paska zadań", "Close WingetUI to the notification area": "Zamknij UniGetUI do obszaru powiadomień", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Kopia zapasowa w chmurze wykorzystuje prywatny GitHub Gist do przechowywania listy zainstalowanych pakietów", "Cloud package backup": "Kopia zapasowa pakietów w chmurze", "Command-line Output": "Wyjście wiersza poleceń", "Command-line to run:": "Polecenie do wykonania:", "Compare query against": "Porównaj zapytanie z", "Compatible with authentication": "Zgodny z uwierzytelnianiem", "Compatible with proxy": "Kompatybilny z proxy", "Component Information": "Informacje o składnikach", "Concurrency and execution": "Równoczesność i wykonywanie zadań", "Connect the internet using a custom proxy": "Połącz przy pomocy własnego proxy", "Continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Przyczyń się do repozytorium ikon i zrzutów ekranu", "Contributors": "Kontrybutorzy", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopiuj do schowka", "Could not add source": "<PERSON><PERSON> można <PERSON>ó<PERSON>ła", "Could not add source {source} to {manager}": " <PERSON><PERSON> moż<PERSON> do<PERSON> {source} do {manager}", "Could not back up packages to GitHub Gist: ": "Tworzenie kopii zapasowej pakietów w GitHub Gist nie powiodło się:", "Could not create bundle": "Nie można utworzyć paczki", "Could not load announcements - ": "<PERSON>e można wczytać ogłoszeń -", "Could not load announcements - HTTP status code is $CODE": "Nie można załadować ogłoszeń - kod stanu HTTP to $CODE", "Could not remove source": "<PERSON><PERSON> można <PERSON>ó<PERSON>", "Could not remove source {source} from {manager}": "<PERSON><PERSON> można <PERSON> {source} z {manager}\n", "Could not remove {source} from {manager}": "<PERSON><PERSON> m<PERSON> {source} z {manager}", "Create .ps1 script": null, "Credentials": "Poświadczenia", "Current Version": "Aktualna we<PERSON>", "Current status: Not logged in": "Obecny status: Niezalogowany", "Current user": "Aktualny użytkownik", "Custom arguments:": "Argume<PERSON><PERSON>:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Niestandardowe argumenty wiersza poleceń mogą zmie<PERSON> spos<PERSON>b, w jaki programy są instalowane, uaktualniane lub odinstalowywane, w spos<PERSON>b, którego UniGetUI nie będzie w stanie kontrolować. Używanie niestandardowych wierszy poleceń może uszkodzić pakiety. Postępuj ostrożnie.", "Custom command-line arguments:": "Niestandardowe argumenty linii poleceń:", "Custom install arguments:": "Niestandardowe argumenty instalacji:", "Custom uninstall arguments:": "Niestandardowe argumenty dezin<PERSON>ji:", "Custom update arguments:": "Niestandardowe argumenty aktualizacji:", "Customize WingetUI - for hackers and advanced users only": "Dostosuj UniGetUI - tylko dla hakerów i zaawansowanych użytkowników", "DEBUG BUILD": "DEBUG BUILD", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ZASTRZEŻENIE: NIE PONOSIMY ODPOWIEDZIALNOŚCI ZA POBRANE PAKIETY. PROSIMY O UPEWNIENIE SIĘ, ŻE INSTALUJESZ TYLKO ZAUFANE OPROGRAMOWANIE.", "Dark": "Ciemny", "Decline": "<PERSON><PERSON><PERSON><PERSON>", "Default": "Domy<PERSON><PERSON><PERSON>", "Default installation options for {0} packages": "Domyślne opcje instalacji dla {0} pakietów", "Default preferences - suitable for regular users": "Domyślne ustawienia - odpowiednie dla zwykłych użytkowników", "Default vcpkg triplet": "Domyślne zmienne potrójne vcpkg", "Delete?": "<PERSON><PERSON><PERSON><PERSON>?", "Dependencies:": "Zależności:", "Descendant": "Malejąco", "Description:": "Opis:", "Desktop shortcut created": "Stworzono skrót na pulpicie", "Details of the report:": "Szczegóły raportu:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Programowanie jest trudne, a ta aplikacja jest za darmo. Ale jeśli lubisz tą aplikację zawsze możesz <b>kup<PERSON> mi kawę</b> ;)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Instaluj po dwukrotnym kliknięciu elementu w zakładce \"{discoveryTab}\" (zamiast wyświetlania informacji o pakiecie).", "Disable new share API (port 7058)": "Wyłącz nowe API od udostępniania (port 7058)", "Disable the 1-minute timeout for package-related operations": "Wyłącz 1-minutowy limit czasu dla operacji pakietów", "Disclaimer": "Zastrzeżenie", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover packages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Distinguish between\nuppercase and lowercase": "Rozróżniaj wielkie i małe litery", "Distinguish between uppercase and lowercase": "Rozróżniaj wielke i małe litery", "Do NOT check for updates": "NIE szukaj aktualizacji", "Do an interactive install for the selected packages": "Wykonaj interaktywną instalację wybranych pakietów", "Do an interactive uninstall for the selected packages": "Wykonaj interaktywną deinstalcję wybranych pakietów", "Do an interactive update for the selected packages": "Wykonaj interaktywną aktualizację wybranych pakietów", "Do not automatically install updates when the battery saver is on": "<PERSON>e instaluj automatycznie aktualizacji, gdy oszczędzanie baterii jest włączone", "Do not automatically install updates when the network connection is metered": "<PERSON>e instaluj automatycznie aktualizacji, gdy połączenie sieciowe jest taryfowe", "Do not download new app translations from GitHub automatically": "Nie pobieraj nowych tłumaczeń aplikacji z GitHuba automatycznie", "Do not ignore updates for this package anymore": "Nie ignoruj już aktualizacji dla tego pakietu", "Do not remove successful operations from the list automatically": "Nie usuwaj automatycznie pomyślnie zakończonych operacji z listy", "Do not show this dialog again for {0}": "<PERSON><PERSON> poka<PERSON>j ponownie tego okna dialogowego dla {0}", "Do not update package indexes on launch": "Nie aktualizuj indeksów pakietów podczas uruchamiania", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "<PERSON><PERSON>, że UniGetUI zbiera i wysyła anonimowe statystyki użytkowania, wyłącznie w celu zrozumienia i poprawy doświadczenia użytkownika?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "<PERSON><PERSON>, że UniGetUI jest przydatny? <PERSON><PERSON><PERSON> m<PERSON>, moż<PERSON><PERSON> wesprzeć moją pracę, abym mógł kontynuować tworzenie UniGetUI jako najlepszego interfejsu do zarządzania pakietami.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "<PERSON><PERSON>, że UniGetUI jest przydatny? Chcesz wesprzeć dewelopera? <PERSON><PERSON><PERSON> tak, m<PERSON><PERSON><PERSON><PERSON> {0}, to bar<PERSON><PERSON> poma<PERSON>!", "Do you really want to reset this list? This action cannot be reverted.": "<PERSON><PERSON> nap<PERSON> chcesz zresetować tę listę? Tej akcji nie można cofnąć.", "Do you really want to uninstall the following {0} packages?": "Na pewno chcesz usunąć {0} pakietów?", "Do you really want to uninstall {0} packages?": "Na pewno chcesz odinstalować pakiet {0}?", "Do you really want to uninstall {0}?": "Czy na pewno chcesz odinstalować {0}?", "Do you want to restart your computer now?": "<PERSON><PERSON> ch<PERSON>z teraz uruchomić ponownie komputer?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Chcesz przetłumaczyć UniGetUI na swój język? <PERSON><PERSON><PERSON><PERSON>, jak możesz pomóc <a style=\"color:{0}\" href=\"{1}\"a>TUTAJ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Nie masz ochoty na darowiznę? Nie martw się, zaws<PERSON> możesz podzielić się UniGetUI ze znajomymi. Rozpowszechniaj informacje o UniGetUI.", "Donate": "Darowizna", "Done!": "Ukończono!", "Download failed": "Pobieranie nie powiodło się", "Download installer": "<PERSON><PERSON>rz instalator", "Download operations are not affected by this setting": "To ustawienie nie ma wpływu na operacje pobierania", "Download selected installers": "Pobierz wybrane instalatory", "Download succeeded": "Pobieranie powiodło się", "Download updated language files from GitHub automatically": "Pobieraj automatycznie zaktualizowane pliki językowe z GitHuba", "Downloading": "Pobieranie", "Downloading backup...": "Pobieranie kopii zapasowej...", "Downloading installer for {package}": "Pobieranie instalatora dla {package}", "Downloading package metadata...": "Pobieranie metadanych pakietu...", "Enable Scoop cleanup on launch": "Włącz czyszczenie Scoop podczas uruchamiania", "Enable WingetUI notifications": "Włącz powiadomienia UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "<PERSON><PERSON><PERSON><PERSON> [eksperymentalne] ulepszone narzędzie do rozwiązywania problemów z WinGet", "Enable and disable package managers, change default install options, etc.": "Aktywuj i dezaktywuj menedżery pakietów, zmień domyślne ustawienia instalacji, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Włącz optymalizacje wykorzystania procesora w tle (<PERSON><PERSON>ull Request #3278).", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Włącz API w tle (Widgets for UniGetUI and Sharing, port 7058)", "Enable it to install packages from {pm}.": "Włącz instalację pakietów z {pm}.", "Enable the automatic WinGet troubleshooter": "Włącz automatyczne rozwiązywanie problemów dla WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Włącz nowy UniGetUI UAC Elevator", "Enable the new process input handler (StdIn automated closer)": "Włącz nowy moduł obsługi danych wejściowych procesu (automatyczne zamykanie StdIn)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Włącz poniższe ustawienia TYLKO WTEDY, GDY w pełni rozumiesz, jak d<PERSON>, oraz jakie mogą wywołać konsekwencje i zagrożenia.", "Enable {pm}": "<PERSON><PERSON><PERSON><PERSON> {pm}", "Enter proxy URL here": "Wprowadź tutaj adres serwera proxy", "Entries that show in RED will be IMPORTED.": "Pozycje zaznaczone na CZERWONO zostaną ZAIMPORTOWANE.", "Entries that show in YELLOW will be IGNORED.": "Pozycje zaznaczone na ŻÓŁTO zostaną ZIGNOROWANE.", "Error": "Błąd", "Everything is up to date": "Wsz<PERSON>tko jest aktualne", "Exact match": "Dokładne dopasowanie", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Istniejące skróty na pulpicie zostaną przeskanowane i będziesz musiał(a) w<PERSON><PERSON>ć, które z nich zachować, a które usunąć.", "Expand version": "Rozwiń wersję", "Experimental settings and developer options": "Eksperymentalne ustawienia i opcje deweloperskie", "Export": "Eksport", "Export log as a file": "Eksportuj dziennik jako plik", "Export packages": "Eksportuj pakiety", "Export selected packages to a file": "Eksportuj wybrane pakiety do pliku", "Export settings to a local file": "Eksportuj ustawienia do pliku", "Export to a file": "Eksportuj do pliku", "Failed": "Niepowodzenie", "Fetching available backups...": "Pozyskiwanie dostępnych kopii zapasowych...", "Fetching latest announcements, please wait...": "Pobieranie najnowszych ogłoszeń, pro<PERSON><PERSON> cze<PERSON>...", "Filters": "Filtry", "Finish": "Zakończ", "Follow system color scheme": "Zgodnie ze schematem kolorów systemu", "Follow the default options when installing, upgrading or uninstalling this package": "Wykorzystuj domyślne ustawienia podczas instalacji, aktualizacji lub dezinstalacji tego pakietu", "For security reasons, changing the executable file is disabled by default": "Ze względów bezpieczeństwa zmiana pliku wykonywalnego jest domyślnie wyłączona.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Ze względów bezpieczeństwa niestandardowe argumenty wiersza poleceń są domyślnie wyłączone. Aby to <PERSON><PERSON><PERSON><PERSON>, przejdź do ustawień zabezpieczeń UniGetUI. ", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Ze względów bezpieczeństwa skrypty przed i po czynnościach są domyślnie wyłączone. Aby to <PERSON><PERSON><PERSON><PERSON>, przejdź do ustawień bezpieczeństwa UniGetUI.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "<PERSON><PERSON><PERSON><PERSON> wersję skompilowaną dla ARM (TYLKO DLA SYSTEMÓW ARM64)", "Formerly known as WingetUI": "<PERSON><PERSON><PERSON> z<PERSON> jako <PERSON>", "Found": "Znaleziono", "Found packages: ": "Znalezione pakiety:", "Found packages: {0}": "Znalezione pakiety: {0}", "Found packages: {0}, not finished yet...": "Znalezione pakiety: {0}, jeszcze szukam...", "General preferences": "Ogólne ustawienia", "GitHub profile": "<PERSON><PERSON>", "Global": "Globalne", "Go to UniGetUI security settings": "Przejdź do ustawień bezpieczeństwa UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Świetne repozytorium nieznanych, ale przydatnych narzędzi i innych interesujących pakietów.<br>Zawiera: <b><PERSON><PERSON><PERSON><PERSON><PERSON>, programy wiersza poleceń, ogólne oprogramowanie (wymagane dodatkowe repozytorium)</b>", "Great! You are on the latest version.": "Świetnie! Masz najnowszą wersję!", "Grid": "Siatka", "Help": "Pomoc", "Help and documentation": "Pomoc i dokumentacja", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Tutaj możesz zmienić zachowanie UniGetUI w odniesieniu do następujących skrótów. Zaznaczenie skrótu spowoduje, że UniGetUI usunie go, jeśli zostanie utworzony podczas przyszłej aktualizacji. Odznaczenie go spowoduje, że skrót pozostanie nienaruszony", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON><PERSON>, nazywam się Martí i jestem <i>programistą</i> UniGetUI. UniGetUI został w całości stworzony w moim wolnym czasie!", "Hide details": "<PERSON>k<PERSON>j <PERSON>egóły", "Homepage": "Strona główna", "Hooray! No updates were found.": "Super! Nie znaleziono żadnych aktualizacji.", "How should installations that require administrator privileges be treated?": "<PERSON>ak instalacje wymagające uprawnień administratora powinny by<PERSON> traktowane?", "How to add packages to a bundle": "<PERSON>ak dodać pakiety do paczki", "I understand": "<PERSON><PERSON><PERSON><PERSON>", "Icons": "<PERSON><PERSON><PERSON>", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "<PERSON><PERSON><PERSON> aktywowano kopię zapasową w chmurze, bę<PERSON><PERSON> ona zapisywana jako GitHub Gist na tym koncie", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignoruj niestandardowe komendy przed- i poinstalacyjne podczas importowania pakietów z paczki", "Ignore future updates for this package": "Ignoruj przyszłe aktualizacje tego pakietu", "Ignore packages from {pm} when showing a notification about updates": "Ignoruj pakiety z {pm} podczas wyświetlania powiadomień o aktualizacjach", "Ignore selected packages": "Ignoruj zaznaczone pakiety", "Ignore special characters": "Ignoruj znaki specjalne", "Ignore updates for the selected packages": "Ignoruj aktualizacje dla wybranych pakietów", "Ignore updates for this package": "Zignoruj aktualizacje tego pakietu", "Ignored updates": "Ignorowane aktualizacje", "Ignored version": "Ignorowane wersje", "Import": "Import", "Import packages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import packages from a file": "Importuj pakiety z pliku", "Import settings from a local file": "Importuj ustawienia z pliku", "In order to add packages to a bundle, you will need to: ": "<PERSON><PERSON> do<PERSON><PERSON> pakiety do paczki, musisz: ", "Initializing WingetUI...": "Uruchamianie UniGetUI...", "Install": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install Scoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install and more": "Zainstaluj i więcej", "Install and update preferences": "Zainstaluj i zaktualizuj preferencje", "Install as administrator": "Zain<PERSON><PERSON>j jako administrator", "Install available updates automatically": "Automatyczne instaluj dostępne aktualizacje", "Install location can't be changed for {0} packages": "Nie można zmienić lokalizacji instalacji dla {0} pakietów.", "Install location:": "<PERSON><PERSON><PERSON><PERSON> instalacji:", "Install options": "<PERSON><PERSON><PERSON>", "Install packages from a file": "Zainstaluj pakiety z pliku", "Install prerelease versions of UniGetUI": "Zainstaluj wstępne wersje UniGetUI", "Install script": null, "Install selected packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> wy<PERSON> paki<PERSON>", "Install selected packages with administrator privileges": "Zainstaluj wybrane pakiety używając uprawnień administratora", "Install selection": "<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>", "Install the latest prerelease version": "Zainstaluj najnowszą wersję rozwojową", "Install updates automatically": "Instaluj aktualizacje automatycznie", "Install {0}": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "Installation canceled by the user!": "Instalacja anulowana przez użytkownika!", "Installation failed": "Instalacja nie powiodła się", "Installation options": "<PERSON><PERSON><PERSON>", "Installation scope:": "<PERSON><PERSON><PERSON> instalacji:", "Installation succeeded": "Instalacja powiodła się", "Installed Packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> paki<PERSON>", "Installed Version": "Zainstalowana we<PERSON>ja", "Installed packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> paki<PERSON>", "Installer SHA256": "SHA256 instalatora", "Installer SHA512": "SHA512 instalatora", "Installer Type": "Typ instalatora", "Installer URL": "Adres URL instalatora", "Installer not available": "Instalator nie jest dos<PERSON>ny", "Instance {0} responded, quitting...": "Instancja {0} od<PERSON>wiedziała, zamykanie...", "Instant search": "Natychmiastowe wyszukiwanie", "Integrity checks can be disabled from the Experimental Settings": "Sprawdzanie spójności może zostać wyłączone w Ustawieniach Eksperymentalnych", "Integrity checks skipped": "Pominięto sprawd<PERSON>ie s<PERSON>ó<PERSON>", "Integrity checks will not be performed during this operation": "Podczas tej operacji nie będzie sprawdzana spójno<PERSON>ć", "Interactive installation": "Interaktywna instalacja", "Interactive operation": "Interaktywna operacja", "Interactive uninstall": "Interaktywna deinstalacja", "Interactive update": "Interaktywna aktualizacja", "Internet connection settings": "Ustawienia połączenia internetowego", "Is this package missing the icon?": "<PERSON><PERSON> w tym pakiecie brakuje ikony?", "Is your language missing or incomplete?": "<PERSON><PERSON> brakuje Twojego języka lub jest on niekompletny?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "<PERSON>e ma <PERSON>, że podane dane uwierzytelniające będą przechowywane bezpiecznie, dlatego nie należy używać danych uwierzytelniających konta bankowego", "It is recommended to restart UniGetUI after WinGet has been repaired": "Zalecany jest restart UniGetUI po naprawieniu WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Zdecydowanie zaleca się przeinstalowanie UniGetUI, w celu rozwiązania sytuacji.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Wygląda na to, że WinGet nie działa prawidłowo. <PERSON><PERSON> podj<PERSON> próbę naprawy WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Wygląda na to, że uruchomiono UniGetUI jako administrator, co nie jest zalecane. Możesz nadal używać programu, ale bard<PERSON>ale<PERSON>, aby nie uruchamiać UniGetUI z uprawnieniami administratora. Kliknij na \"{showDetails}\", aby zob<PERSON><PERSON><PERSON> dlaczego.", "Language": "Język", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON><PERSON>, motyw i inne preferencje", "Last updated:": "Ostatnia aktualizacja:", "Latest": "<PERSON><PERSON><PERSON><PERSON>", "Latest Version": "Najn<PERSON><PERSON> wersja", "Latest Version:": "Najnowsza wersja:", "Latest details...": "Szczegóły najnowszej wersji...", "Launching subprocess...": "Uruchamiam podproces...", "Leave empty for default": "Zostaw puste dla ustawień domyślnych", "License": "Licencja", "Licenses": "Licencje", "Light": "<PERSON><PERSON><PERSON>", "List": "Lista", "Live command-line output": "Wyniki w terminalu na żywo", "Live output": "Dane wyjściowe na żywo", "Loading UI components...": "Ładowanie komponentów interfejsu...", "Loading WingetUI...": "Ładowanie UniGetUI...", "Loading packages": "Wczytywanie pakietów", "Loading packages, please wait...": "Wczytywan<PERSON> p<PERSON>ów, pro<PERSON><PERSON> c<PERSON>...", "Loading...": "Ładowanie...", "Local": "Lokalnie", "Local PC": "Komputer", "Local backup advanced options": "Op<PERSON>je zaawansowane lokalnej kopii zapasowej", "Local machine": "Lokalna <PERSON>", "Local package backup": "Lokalna kopia z<PERSON>sowa", "Locating {pm}...": "<PERSON><PERSON><PERSON><PERSON><PERSON> {pm}...", "Log in": "<PERSON><PERSON><PERSON><PERSON>", "Log in failed: ": "Logowanie nie powiodło się:", "Log in to enable cloud backup": "<PERSON><PERSON><PERSON><PERSON>, aby <PERSON><PERSON><PERSON><PERSON><PERSON>ć tworzenie kopii zapasowych w chmurze", "Log in with GitHub": "Zaloguj się do GitHub", "Log in with GitHub to enable cloud package backup.": "Zaloguj się do GitHub, aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tworzenie kopii zapasowych pakietów w chmurze.", "Log level:": "Poziom logowania:", "Log out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Log out failed: ": "Wylogowywanie nie powiodło się:", "Log out from GitHub": "Wyloguj się z GitHub", "Looking for packages...": "Szukanie pakietów", "Machine | Global": "Maszyna | Globalnie", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Błędnie sformułowane argumenty wiersza poleceń mogą uszkodzić pakiety lub nawet umo<PERSON><PERSON><PERSON><PERSON> atakującemu wykonanie kodu z podwyższonymi uprawnieniami. Z tego powodu, importowanie niestandardowych argumentów jest domyślnie wyłączone", "Manage": "Zarządzaj", "Manage UniGetUI settings": "Zarządzaj ustawieniami UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Zarządzanie autostartem WingetUI z poziomu aplikacji Ustawienia", "Manage ignored packages": "Zarządzaj ignorowanymi pakietami", "Manage ignored updates": "Zarządzaj ignorowanymi aktualizacjami", "Manage shortcuts": "Zarządzaj skrótami", "Manage telemetry settings": "Zarządzaj ustawieniami telemetrii", "Manage {0} sources": "Zarząd<PERSON><PERSON> {0}", "Manifest": "Manifest", "Manifests": "Manifesty:", "Manual scan": "Ręczne skanowanie", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Oficjalny menedżer pakietów firmy Microsoft. Pełen dobrze znanych i zweryfikowanych pakietów<br>Zawiera: <b>ogólne oprogramowanie, aplikacje Microsoft Store</b>", "Missing dependency": "Brakująca zależność", "More": "<PERSON><PERSON><PERSON><PERSON>j", "More details": "Więcej szczegółów", "More details about the shared data and how it will be processed": "Więcej szczegółów na temat udostępnianych danych i sposobu ich przetwarzania", "More info": "Więcej informacji", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "UWAGA: Funkcję rozwiązywania problemów można wyłączyć w ustawieniach UniGetUI, w sekcji WinGet", "Name": "Nazwa", "New": "Nowy", "New Version": "Nowa wersja", "New bundle": "Nowa paczka", "New version": "Nowa wersja", "Nice! Backups will be uploaded to a private gist on your account": "Świetnie! Kopie zapasowe zostaną przesłane do prywatnego gistu na Twoim koncie.", "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": "Nie znaleziono odpowiedniego instalatora dla pakietu {0}", "No dependencies specified": "Brak wymienionych zależności", "No new shortcuts were found during the scan.": "Podczas skanowania nie znaleziono żadnych nowych skrótów.", "No packages found": "Nie znaleziono pakietów", "No packages found matching the input criteria": "Nie znaleziono pakietów spełniających podane kryteria", "No packages have been added yet": "<PERSON>e dodano jeszcze pakietów", "No packages selected": "<PERSON><PERSON> wybrano paki<PERSON>ów", "No packages were found": "Nie znaleziono pakietów", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Nie gromadzimy ani nie przesyłamy żadnych danych osobowych. Zebrane dane są anonimizowane, więc nie ma możliwości ich powiązania z Twoją osobą.", "No results were found matching the input criteria": "Nie znaleziono żadnych wyników spełniających podane kryteria", "No sources found": "Nie znaleziono źródeł", "No sources were found": "Nie znaleziono źródeł", "No updates are available": "Brak dostępnych aktualizacji", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Mendedżer pakietów NodeJS. Pełen bibliotek i innych narzędzi, które krążą po świecie JavaScriptu <br>Zawiera: <b>biblioteki javascriptowe dla Node i powiązane narzędzia</b>", "Not available": "Niedostępne", "Not finding the file you are looking for? Make sure it has been added to path.": "Nie moż<PERSON>z z<PERSON> pliku, którego szukasz? Upewnij się, że został on dodany do ścieżki.", "Not found": "Nie znaleziono", "Not right now": "<PERSON><PERSON> te<PERSON>", "Notes:": "Notatki:", "Notification preferences": "Ustawienia powiadomień", "Notification tray options": "Opcje paska powiadomień", "Notification types": "Typy powiadomień", "NuPkg (zipped manifest)": "NuPkg (spakowany manifest)", "OK": "Potwierdź", "Ok": "Tak", "Open": "Otwórz", "Open GitHub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Open UniGetUI": "Otwórz UniGetUI", "Open UniGetUI security settings": "Otwórz ustawienia bezpieczeństwa UniGetUI", "Open WingetUI": "Otwórz UniGetUI", "Open backup location": "Otwórz lokalizację kopii zapasowej", "Open existing bundle": "O<PERSON><PERSON><PERSON><PERSON> is<PERSON>nieją<PERSON>ą paczkę", "Open install location": "Otwórz lokalizację instalacji", "Open the welcome wizard": "Otwórz kreator powitania", "Operation canceled by user": "Operacja anulowana przez użytkownika", "Operation cancelled": "<PERSON><PERSON><PERSON>", "Operation history": "Historia", "Operation in progress": "Operacja w toku", "Operation on queue (position {0})...": "Operacja jest w kolejce (pozycja {0})...", "Operation profile:": "Profil <PERSON>:", "Options saved": "Zapisano opcje", "Order by:": "<PERSON><PERSON><PERSON><PERSON>:", "Other": "<PERSON><PERSON>", "Other settings": "Inne us<PERSON>", "Package": "<PERSON><PERSON>", "Package Bundles": "Paczki pakietów", "Package ID": "ID pakietu", "Package Manager": "Menedżer pakietów", "Package Manager logs": "Dziennik zdarzeń menedżera pakietów", "Package Managers": "Menedżery pakietów", "Package Name": "<PERSON>zwa pakietu", "Package backup": "Kopia zapasowa pakietów", "Package backup settings": "Ustawienia kopii zapasowych pakietów", "Package bundle": "Paczka pakietów", "Package details": "Szczegóły pakietu", "Package lists": "Lista pakietów", "Package management made easy": "Łatwe zarządzanie pakietami", "Package manager": "Menedżer pakietów", "Package manager preferences": "Preferencje menedżerów pakietów", "Package managers": "Menedżery pakietów", "Package not found": "Nie znaleziono pakietu", "Package operation preferences": "Preferencje operacji na pakietach", "Package update preferences": "Preferencje aktualizacji pakietów", "Package {name} from {manager}": "<PERSON><PERSON> {name} z {manager}", "Package's default": "Domyślne ustawienia pakietu", "Packages": "<PERSON><PERSON><PERSON>", "Packages found: {0}": "Znalezione pakiety: {0}", "Partially": "C<PERSON><PERSON><PERSON>ciowo", "Password": "<PERSON><PERSON><PERSON>", "Paste a valid URL to the database": "Wklej prawidłowy adres URL do bazy danych", "Pause updates for": "Wstrzymaj aktualizacje na", "Perform a backup now": "Utwórz kopię zapasową teraz", "Perform a cloud backup now": "Utwórz kopię zapasową w chmurze teraz", "Perform a local backup now": "Utwórz lokalną kopię zapasową teraz", "Perform integrity checks at startup": "Sprawdź spójność podczas uruchamiania", "Performing backup, please wait...": "Tworzy się kopia zapasowa, pro<PERSON><PERSON> cze<PERSON>...", "Periodically perform a backup of the installed packages": "Okresowo wykonuj kopię zapasową zainstalowanych pakietów", "Periodically perform a cloud backup of the installed packages": "Okresowo wykonuj kopię zapasową zainstalowanych pakietów w chmurze", "Periodically perform a local backup of the installed packages": "Okresowo wykonuj lokalną kopię zapasową zainstalowanych pakietów", "Please check the installation options for this package and try again": "Sprawdź opcje instalacji tego pakietu i spróbuj ponownie", "Please click on \"Continue\" to continue": "K<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>uu<PERSON>\", a<PERSON> k<PERSON><PERSON><PERSON><PERSON>", "Please enter at least 3 characters": "Wpisz co najmniej 3 znaki", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON><PERSON>, że niektóre pakiety mogą nie być możliwe do zainstalowania ze względu na menedżery pakietów, które są włączone na tym komputerze.", "Please note that not all package managers may fully support this feature": "Nie wszystkie menedżery pakietów mogą w pełni obsługiwać tę funkcję", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "<PERSON><PERSON><PERSON><PERSON>, że pakiety z niektórych źródeł mogą nie być eksportowalne. Zostały one wyszarzone i nie będą eksportowane.", "Please run UniGetUI as a regular user and try again.": "Uruchom UniGetUI jako zwykły użytkownik i spróbuj ponownie.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Więcej informacji na ten temat można znaleźć w Wyjściu wiersza poleceń lub w Historii operacji.", "Please select how you want to configure WingetUI": "<PERSON><PERSON><PERSON><PERSON>, w jaki <PERSON><PERSON> skonfigurować UniGetUI", "Please try again later": "Proszę spróbować ponownie później", "Please type at least two characters": "Proszę wprowadź co najmniej dwa znaki", "Please wait": "<PERSON><PERSON><PERSON>", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "<PERSON><PERSON>ę czekać aż do zakończenia instalacji {0}. Może pojawić się czarne okno. Poczekaj aż ono się zamknie.", "Please wait...": "<PERSON><PERSON><PERSON> cze<PERSON>...", "Portable": "Przenośny", "Portable mode": "<PERSON><PERSON>", "Post-install command:": "Komenda poinstalacyjna:", "Post-uninstall command:": "Komenda podezintalacyjna:", "Post-update command:": "Komenda poaktualizacyjna:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Menedżer pakietów PowerShella. Znajdź biblioteki i skrypty, aby rozsz<PERSON><PERSON> możliwości PowerShella<br>Zawiera: <b>moduły, skrypty, polecenia</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Polecenia wykonywane przed i po instalacji, mogą wyrządzić poważne szkody Twojemu u<PERSON>, jeśli zostały do tego celowo zaprojektowane. Importowanie poleceń z pakietu bywa bardzo <PERSON>, chyba że masz pełne zaufanie do jego źródła", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Polecenia wykonywane przed i po instalacji, zostaną uruchomione przed i po zainstalowaniu, uaktualnieniu lub odinstalowaniu pakietu. <PERSON><PERSON><PERSON><PERSON>, że mogą one co<PERSON> zep<PERSON>, jeśli nie zostaną użyte z rozwagą", "Pre-install command:": "Komenda przedinstalacyjna:", "Pre-uninstall command:": "Komenda przeddezinstalacyjna:", "Pre-update command:": "Komenda przedaktualizacyjna:", "PreRelease": "Wersja przedpremierowa", "Preparing packages, please wait...": "Przygotowywanie pakietów, pro<PERSON><PERSON> cze<PERSON>...", "Proceed at your own risk.": "Postępuj na własne ryzyko.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Zabroń wszelkiego rodzaju podnoszenia za pomocą UniGetUI Elevator lub GSudo.", "Proxy URL": "<PERSON><PERSON> serwera proxy", "Proxy compatibility table": "Tabela zgodności serwerów proxy", "Proxy settings": "Ustawienia proxy", "Proxy settings, etc.": "Ustawienia proxy itp.", "Publication date:": "Data publikacji:", "Publisher": "Wydawca", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Menedżer bibliotek Pythona. Pełen bibliotek Pythona i innych narzędzi związanych z Pythonem <br>Zawiera: <b>biblioteki Pythona i powiązane narzędzia</b>", "Quit": "Wyjdź", "Quit WingetUI": "Zamknij UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Zmniejsz liczbę monitów UAC, domyślnie podnoś uprawnienia instalacji, odblokuj niektóre niebezpieczne funkcje itp.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Zapoznaj się z dziennikami UniGetUI, aby u<PERSON><PERSON><PERSON> więcej szczegółów dotyczących pliku(ów), których dotyczy problem", "Reinstall": "<PERSON><PERSON><PERSON><PERSON><PERSON> pono<PERSON>", "Reinstall package": "<PERSON><PERSON><PERSON><PERSON><PERSON> pakiet ponownie", "Related settings": "Powiązane ustawienia", "Release notes": "Informacje o wydaniu", "Release notes URL": "Adres URL informacji o wydaniu", "Release notes URL:": "Adres informacji o wydaniu:", "Release notes:": "Lista zmian:", "Reload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reload log": "Przeładuj d<PERSON>nn<PERSON>", "Removal failed": "Usunięcie nie powiodło się", "Removal succeeded": "Usunięto p<PERSON>", "Remove from list": "Usuń z listy", "Remove permanent data": "<PERSON><PERSON><PERSON> trwale dane", "Remove selection from bundle": "Usuń wybrane z paczki", "Remove successful installs/uninstalls/updates from the installation list": "Usuń pozytywne instalacje/deinstalacje/aktualizacje z listy instalacji", "Removing source {source}": "<PERSON><PERSON><PERSON><PERSON>ódła {source}", "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON><PERSON> {source} z {manager}", "Repair UniGetUI": "Napraw UniGetUI", "Repair WinGet": "<PERSON><PERSON>w <PERSON>", "Report an issue or submit a feature request": "Zgłoś problem lub prośbę o dodanie funkcji", "Repository": "Repozytorium", "Reset": "Zresetuj", "Reset Scoop's global app cache": "Zresetuj pamięć podręczną Scoop'a", "Reset UniGetUI": "Zresetuj UniGetUI", "Reset WinGet": "Zresetuj WinGet", "Reset Winget sources (might help if no packages are listed)": "Zresetuj źródła Winget (może pomóc jeśli lista pakietów jest pusta)", "Reset WingetUI": "Zresetuj UniGetUI", "Reset WingetUI and its preferences": "Zresetuj UniGetUI i jego ustawienia", "Reset WingetUI icon and screenshot cache": "Zresetuj pamięć podręczną ikon i zrzutów ekranu UniGetUI", "Reset list": "Zresetuj listę", "Resetting Winget sources - WingetUI": "Resetowanie źródeł Winget - UniGetUI", "Restart": "<PERSON><PERSON><PERSON><PERSON> ponownie", "Restart UniGetUI": "Uruchom ponownie UniGetUI", "Restart WingetUI": "Zrestartuj UniGetUI", "Restart WingetUI to fully apply changes": "Uruchom ponownie UniGetUI żeby zastosowac wszystkie zmiany", "Restart later": "Uruchom ponownie później", "Restart now": "Uruchom ponownie teraz", "Restart required": "<PERSON><PERSON>agane ponowne uruchomienie", "Restart your PC to finish installation": "Uruchom ponownie komputer żeby dokończyć instalację", "Restart your computer to finish the installation": "Uruchom ponownie komputer, aby <PERSON><PERSON><PERSON> instalację", "Restore a backup from the cloud": "Przywróć kopię zapasową z chmury", "Restrictions on package managers": "Ograniczenia menedżerów pakietów", "Restrictions on package operations": "Ograniczenia dotyczące operacji związanych z pakietami", "Restrictions when importing package bundles": "Ograniczenia podczas importowania paczek pakietów", "Retry": "Ponów", "Retry as administrator": "Spróbuj ponownie jako administrator", "Retry failed operations": "Ponów nieudane operacje", "Retry interactively": "Ponów interaktywnie", "Retry skipping integrity checks": "Ponów próbę, pomijając sprawdzanie spójności", "Retrying, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON> cze<PERSON>...", "Return to top": "Wróć na górę", "Run": "Uruchom", "Run as admin": "Uruchom jako administrator", "Run cleanup and clear cache": "Uruchom czyszczenie i wyczyść pamięć podręczną", "Run last": "Uruchom ostatnie", "Run next": "Uruchom następne", "Run now": "Uruchom teraz", "Running the installer...": "Uruchomienie instalatora...", "Running the uninstaller...": "Uruchomienie deinstalatora...", "Running the updater...": "Uruchomienie aktualizacji...", "Save": "<PERSON><PERSON><PERSON><PERSON>", "Save File": "Zapisz plik", "Save and close": "Zapisz i zamknij", "Save as": "<PERSON>ap<PERSON>z jako", "Save bundle as": "Zapisz paczkę jako", "Save now": "Zapisz teraz", "Saving packages, please wait...": "Zapisywanie <PERSON>, <PERSON><PERSON><PERSON>...", "Scoop Installer - WingetUI": "Instalator Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Deinstalator Scoop - UniGetUI", "Scoop package": "<PERSON><PERSON>", "Search": "Szukaj", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "<PERSON><PERSON><PERSON> a<PERSON>, informuj mnie kiedy dostępne są aktualizacje i nie rób nerdowskich rzeczy. Nie chc<PERSON>, aby UniGetUI nadmiernie komplikował, chcę tylko prostego <b>sklepu z oprogramowaniem</b>", "Search for packages": "<PERSON>ys<PERSON><PERSON> pakiety", "Search for packages to start": "<PERSON><PERSON><PERSON>, wyszukaj pakiety", "Search mode": "<PERSON><PERSON> w<PERSON>zukiwan<PERSON>", "Search on available updates": "Wyszukaj w dostępnych aktualizacjach", "Search on your software": "Wyszukaj w twoich aplikacjach", "Searching for installed packages...": "Wyszukiwanie zainstalowanych pakietów...", "Searching for packages...": "Wyszukiwanie pakietów...", "Searching for updates...": "Wyszukiwanie aktualizacji...", "Select": "<PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "W<PERSON><PERSON><PERSON> \"{item}\" żeby dodać do własnego repozytorium", "Select a folder": "W<PERSON><PERSON><PERSON> folder", "Select all": "Zaznacz wszystkie", "Select all packages": "Zaznacz wszystkie pakiety", "Select backup": "<PERSON><PERSON><PERSON>rz kopię zapasową", "Select only <b>if you know what you are doing</b>.": "Zaznacz tylko <b>je<PERSON>li wiesz co robisz</b>", "Select package file": "Wybierz plik pakietu", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "<PERSON><PERSON><PERSON>rz kopi<PERSON> z<PERSON>w<PERSON>, kt<PERSON><PERSON>ą chcesz otworzyć. Później będziesz mógł spraw<PERSON>, kt<PERSON>re pakiety/programy chcesz przywrócić.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "<PERSON><PERSON><PERSON><PERSON> procesy, które powinny zostać zamknięte przed zainstalowaniem, aktualizacją lub odinstalowaniem tego pakietu.", "Select the source you want to add:": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> ch<PERSON>z dodać:", "Select upgradable packages by default": "Wybierz domyślnie pakiety z możliwością aktualizacji", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> <b><PERSON><PERSON><PERSON><PERSON> pakietów</b> ma<PERSON><PERSON> <PERSON> ({0}), skonfiguruj sposób instalacji pakietów, zarządzaj sposobem obsługi uprawnień administratora itp.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Wysłano handshake. Oczekiwanie na odpowiedź serwera... ({0}%)", "Set a custom backup file name": "Ustaw własną nazwę pliku z kopią zapasową", "Set custom backup file name": "Ustaw własną nazwę pliku kopii zapasowej", "Settings": "Ustawienia", "Share": "Udostępnij", "Share WingetUI": "Udostępnij UniGetUI", "Share anonymous usage data": "Udostępnij anonimowe dane o użytkowaniu", "Share this package": "Udostępnij ten pakiet", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "W przypadku zmiany ustawień zabezpieczeń konieczne będzie ponowne otwarcie paczki, aby zmiany zaczęły obowiązywać.", "Show UniGetUI on the system tray": "Pokaż UniGetUI w zasobniku systemowym", "Show UniGetUI's version and build number on the titlebar.": "Pokaż wersję UniGetUI na pasku tytułowym", "Show WingetUI": "Pokaż UniGetUI", "Show a notification when an installation fails": "Pokaż powiadomienie gdy instalacja się nie powiedzie", "Show a notification when an installation finishes successfully": "Pokaż powiadomienie gdy instalacja się powiedzie", "Show a notification when an operation fails": "Pokaż powiadomienie gdy operacja się nie powiedzie", "Show a notification when an operation finishes successfully": "Pokaż powiadomienie gdy operacja się powiedzie", "Show a notification when there are available updates": "Wyświetl powiadomienie gdy aktualizacje są dostępne", "Show a silent notification when an operation is running": "Pokaż ciche powiadomienie gdy operacja jest wykonywana", "Show details": "Pokaż szczegóły", "Show in explorer": "Pokaż w eksploratorze", "Show info about the package on the Updates tab": "Wyświetl informacje o pakiecie w zakładce aktualizacji", "Show missing translation strings": "Pokaż brakujące ciągi tłumaczeń", "Show notifications on different events": "Pokaż powiadomienia o różnych wydarzeniach", "Show package details": "Pokaż szczegóły pakietu", "Show package icons on package lists": "Pokaż ikony pakietu na liście pakietów", "Show similar packages": "Pokaż podobne pakiety", "Show the live output": "Wyświetlaj wykonywane operacje na żywo", "Size": "Rozmiar", "Skip": "Pomiń", "Skip hash check": "Pomiń weryfikacje hash'a", "Skip hash checks": "Pomiń weryfikacje hash'a", "Skip integrity checks": "Pomiń sprawdzanie spójności", "Skip minor updates for this package": "Pomiń drobne aktualizacje dla tego pakietu", "Skip the hash check when installing the selected packages": "Pomiń sprawdzanie skrótu podczas instalacji wybranych pakietów", "Skip the hash check when updating the selected packages": "Pomiń sprawdzanie skrótu podczas aktualizacji wybranych pakietów", "Skip this version": "Pomiń tę wersję", "Software Updates": "Aktualizacje pakietów", "Something went wrong": "Coś poszło nie tak", "Something went wrong while launching the updater.": "W<PERSON><PERSON><PERSON><PERSON>ł błąd podczas uruchamiania aktualizatora.", "Source": "Źródło", "Source URL:": "Adres URL źródła:", "Source added successfully": "Źródło dodano p<PERSON>ie", "Source addition failed": "Dodanie źródła nie powiodło się", "Source name:": "Nazwa źródła:", "Source removal failed": "<PERSON><PERSON> udało us<PERSON> się źródła", "Source removed successfully": "Źródło zostało pomyślnie usunięte", "Source:": "Źródło:", "Sources": "Źródła", "Start": "Rozpocznij", "Starting daemons...": "Uruchamianie usług...", "Starting operation...": "Rozpoczęcie działania...", "Startup options": "Opcje u<PERSON>ia", "Status": "Status", "Stuck here? Skip initialization": "Zawiesiło się? Pomiń inicjalizaję.", "Success!": null, "Suport the developer": "<PERSON><PERSON><PERSON><PERSON>", "Support me": "Wes<PERSON><PERSON><PERSON><PERSON> mnie", "Support the developer": "Wes<PERSON><PERSON><PERSON><PERSON>", "Systems are now ready to go!": "Systemy są gotowe do d<PERSON>łania!", "Telemetry": "Telemetria", "Text": "Tekst", "Text file": "Plik tekstowy", "Thank you ❤": "Dziękuję ❤", "Thank you 😉": "Dziękuję 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Menedżer pakietów Rust.<br>Zawiera: <b>biblioteki Rust oraz programy napisane w Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Kopia zapasowa NIE będzie zawierać żadnych plików binarnych ani zapisanych danych programu.", "The backup will be performed after login.": "Kopia zapasowa zostanie wykonana po zalogowaniu.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Kopia zapasowa będzie zawierać pełną listę zainstalowanych pakietów i ich opcji instalacji. Zapisane zostaną również zignorowane aktualizacje i pominięte wersje.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> pr<PERSON><PERSON><PERSON><PERSON><PERSON>, jest ni<PERSON><PERSON>. Sprawdź proszę plik i spróbuj ponownie.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Suma kontrolna instalatora nie pokrywa się z oczekiwaną wartością i autentyczność instalatora nie może być zweryfikowana. <PERSON><PERSON><PERSON><PERSON> w<PERSON>, {0} pakiet ponownie pomijając sprawdzanie hasha.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Klasyczny menedżer pakietów dla Windows. Znajdziesz tam wszystko. <br>Zawiera: <b>ogólne oprogramowanie</b>", "The cloud backup completed successfully.": "Tworzenie kopii zapasowej w chmurze zakończone powodzeniem.", "The cloud backup has been loaded successfully.": "Ładowanie kopii zapasowej z chmury zakończone powodzeniem.", "The current bundle has no packages. Add some packages to get started": "Obecna paczka nie zawiera pakietów. Dodaj pakiety aby r<PERSON>", "The executable file for {0} was not found": "Plik wykonywalny dla {0} nie został odnaleziony", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Następujące opcje będą stosowane domyślnie za każdym razem, gdy pakiet {0} zostanie zainstalowany, zaktualizowany lub odinstalowany.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Następujące pakiety zostaną wyeksportowane do pliku JSON. Żadne dane użytkownika ani pliki binarne nie zostaną zapisane.", "The following packages are going to be installed on your system.": "W systemie zostaną zainstalowane następujące pakiety.", "The following settings may pose a security risk, hence they are disabled by default.": "Poniższe ustawienia mogą stanowić zagrożenie, więc domyślnie są dezaktywowane.", "The following settings will be applied each time this package is installed, updated or removed.": "Poniższe ustawienia będą stosowane za każdym razem, gdy pakiet zostanie instalowany, aktualizowany lub usuwany.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Poniższe ustawienia będą stosowane za każdym razem, gdy pakiet zostanie zainstalowany, zaktualizowany lub usunięty. Zostaną one zapisane automatycznie.", "The icons and screenshots are maintained by users like you!": "Ikony oraz zrzuty ekranu są zarządzane przez użytkowników takich jak ty!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "Nie można zweryfikować autentyczności instalatora.", "The installer has an invalid checksum": "Instalator ma niepoprawną sumę kontrolną.", "The installer hash does not match the expected value.": "Hash instalatora nie odpowiada oczekiwanej <PERSON>.", "The local icon cache currently takes {0} MB": "<PERSON><PERSON><PERSON>ć podręczna ikon obecnie zajmuje {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Głównym celem tego projektu jest tworzenie intuicyjnego interfejsu do zarządzania najpopularniejszymi menedżerami pakietów dla Windowsa takich jak Winget i Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "<PERSON><PERSON><PERSON> \"{0}\" nie została znaleziona w menedżerze pakietów \"{1}\"", "The package bundle could not be created due to an error.": "Nie można utworzyć paczki pakietów z powodu błędu.", "The package bundle is not valid": "Paczka pakietów jest nieprawidłowa", "The package manager \"{0}\" is disabled": "Menedżer pakietów \"{0}\" jest wy<PERSON><PERSON><PERSON>ony", "The package manager \"{0}\" was not found": "Menedżer pakietów \"{0}\" nie został znaleziony", "The package {0} from {1} was not found.": "Pakiet {0} z {1} nie został znaleziony.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Pakiety wymienione tutaj nie będą brane pod uwagę podczas sprawdzania aktualizacji. Kliknij je dwukrotnie lub kliknij przycisk po ich prawej stronie, aby prz<PERSON>ć ignorować ich aktualizacje.", "The selected packages have been blacklisted": "Wybrane pakiety zostały umieszczone na czarnej liście", "The settings will list, in their descriptions, the potential security issues they may have.": "W opisach ustawień zostaną wymienione potencjalne problemy związane z bezpieczeństwem, które mogą one powodować.", "The size of the backup is estimated to be less than 1MB.": "Rozmiar kopii zapasowej jest szacowany na mniej niż 1MB.", "The source {source} was added to {manager} successfully": "Źródło {source} zostało dodane pomyślnie do {manager}", "The source {source} was removed from {manager} successfully": "Źródło {source} zostało usunięte pomyślnie z {manager}", "The system tray icon must be enabled in order for notifications to work": "Aby powiadomienia działały, ikona na pasku zadań musi być włączona", "The update process has been aborted.": "Proces aktualizacji został przerwany.", "The update process will start after closing UniGetUI": "Proces aktualizacji rozpocznie się po zamknięciu UniGetUI", "The update will be installed upon closing WingetUI": "Aktualizacja zostanie zainstalowana po zamknięciu UniGetUI", "The update will not continue.": "Aktualizacja nie będzie kontynuowana.", "The user has canceled {0}, that was a requirement for {1} to be run": "Użytkownik anulował {0}, co by<PERSON><PERSON> warunk<PERSON>m uruchomienia {1}", "There are no new UniGetUI versions to be installed": "<PERSON>e ma nowych wersji UniGetUI do zainstalowania", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Operacje są w toku. Zamknięcie UniGetUI może spowodować ich niepowodzenie. <PERSON><PERSON> ch<PERSON> kontynuować?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Na YouTube jest kilka świetnych filmów, które prezentują UniGetUI i jego możliwości. Możesz nauczyć się przydatnych sztuczek i wskazówek!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Istnieją dwa główne powody, dla których nie powinno uruchamiać się UniGetUI jako administrator. <PERSON><PERSON> z nich to problem z komendami menedżera pakietów Scoop, który może przysparzać problemów kiedy zostanie uruchomiony z uprawnieniami administratora. <PERSON><PERSON> powodem jest to, że uruchomienie UniGetUI jako administrator oznacza, że pakiety które pobierzesz również będą uruchamiane z uprawnieniami administratora (co nie jest bezpieczne). <PERSON><PERSON><PERSON><PERSON><PERSON> jed<PERSON>k, że jeśli chcesz zainstalować jakiś pakiet jako administrator to mo<PERSON><PERSON><PERSON> to zrobić klikając prawym klawiszem myszy i wybrać \"Zainstaluj/Zaktualizuj/Odinstaluj jako administrator\".", "There is an error with the configuration of the package manager \"{0}\"": "Konfiguracja menedżera pakietów \"{0}\" jest błędna", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Trwa instalacja. Jeśli zamkniesz UniGetUI, instalacja może się nie udać i skutkować nieoczekiwanymi rezultatami. Czy na pewno chcesz zamknąć UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Są to programy odpowiedzialne za instalowanie, aktualizowanie i usuwanie pakietów.", "Third-party licenses": "Licencje zewnętrzne", "This could represent a <b>security risk</b>.": "<PERSON><PERSON><PERSON> to stanowić <b>zagrożenie bezpieczeństwa</b>.", "This is not recommended.": "<PERSON>e jest to zale<PERSON>.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Jest to praw<PERSON><PERSON>do<PERSON><PERSON> spowodowane tym, <PERSON><PERSON> pakiet, został usunięty lub opublikowany w menedżerze pakietów, którego nie masz włączonego. Otrzymane ID to:  {0}", "This is the <b>default choice</b>.": "Jest to <b><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>ó<PERSON></b>.", "This may help if WinGet packages are not shown": "<PERSON> mo<PERSON><PERSON>, g<PERSON> p<PERSON><PERSON> WinGet nie są pokazywane", "This may help if no packages are listed": "<PERSON> mo<PERSON><PERSON>, g<PERSON> <PERSON><PERSON> pakiety nie są pokazywane", "This may take a minute or two": "To może z<PERSON>ć minutę albo dwie", "This operation is running interactively.": "Operacja ta jest wykonywana interaktywnie.", "This operation is running with administrator privileges.": "Ta operacja jest wykonywana z uprawnieniami administratora.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Ta opcja SPOWODUJE problemy. Ka<PERSON>da operacja, która nie może uzyskać uprawnień administratora, zakończy się niepowodzeniem. Instalacja/aktualizacja/deinstalacja jako administrator NIE BĘDZIE DZIAŁAĆ.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Ta paczka pakietów zawiera pewne ustawienia, które mogą być potencjalnie niebezpieczne i mogą być domyślnie ignorowane.", "This package can be updated": "Ten pakiet może zostać zaktualizowany", "This package can be updated to version {0}": "Ten pakiet można zaktualizować do wersji {0}", "This package can be upgraded to version {0}": "Pakiet może zostać zaktualizowany do wersji {0}", "This package cannot be installed from an elevated context.": "Tego pakietu nie można zainstalować z podwyższonego kontekstu.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Ten pakiet nie ma zrzutów ekranu lub brakuje ikony? Pomóż UniGetUI dodając brakujące ikony i zrzuty ekranu do naszej otwartej, publicznej bazy danych.", "This package is already installed": "Ten pakiet jest już z<PERSON>ny", "This package is being processed": "Ten pakiet jest przetwarzany", "This package is not available": "Ten pakiet nie jest dostę<PERSON>ny", "This package is on the queue": "Ten pakiet jest w kolejce", "This process is running with administrator privileges": "Ten proces jest uruchomiony z prawami administratora", "This project has no connection with the official {0} project — it's completely unofficial.": "Ten projekt nie ma żadnego związku z oficjalnym projektem {0} - jest całkowicie nieoficjalny.", "This setting is disabled": "To usta<PERSON><PERSON>e jest zab<PERSON><PERSON><PERSON>e", "This wizard will help you configure and customize WingetUI!": "Ten kreator pomoże ci skonfigurować i dostosować UniGetUI!", "Toggle search filters pane": "Przełącz panel filtrów wyszukiwania", "Translators": "Tłumacze", "Try to kill the processes that refuse to close when requested to": "Spróbuj wymusić zatrzymanie procesów, które odmawiają zamknięcia", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Włączenie tej opcji umożliwia zmianę pliku wykonywalnego używanego do interakcji z menedżerami pakietów. <PERSON><PERSON><PERSON> pozwala to na bardziej szczegółowe dostosowanie procesów instalacji, może być również niebezpieczne.", "Type here the name and the URL of the source you want to add, separed by a space.": "Wpisz tutaj nazwę i adres URL źródła, kt<PERSON><PERSON> ch<PERSON>, oddzielaj<PERSON><PERSON> je spacją.", "Unable to find package": "Nie można odnaleźć pakietu", "Unable to load informarion": "Nie udało się załadować informacji", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI zbiera anonimowe dane dotyczące użytkowania w celu poprawy doświadczeń użytkownika.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI zbiera anonimowe dane dotyczące użytkowania w celu zrozumienia i poprawy doświadczeń użytkownika.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI wykrył nowy skrót na pulpicie, który można automatycznie usunąć.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI wykrył następujące skróty na pulpicie, które mogą zostać automatycznie usunięte podczas przyszłych aktualizacji", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI wykrył {0} nowych skrótów na pulpicie, które mogą zostać automatycznie usunięte.", "UniGetUI is being updated...": "UniGetUI jest aktualizowany...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI nie jest powiązany z żadnym z kompatybilnych menedżerów pakietów. UniGetUI jest niezależnym projektem.", "UniGetUI on the background and system tray": "UniGetUI na tle i pasku zadań", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI lub niektóre jego komponenty są niekompletne albo uszkodzone.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI wymaga {0} do działania, lecz nie został znaleziony w systemie.", "UniGetUI startup page:": "Strona startowa UniGetUI:", "UniGetUI updater": "Aktualizator UniGetUI ", "UniGetUI version {0} is being downloaded.": "Wersja {0} UniGetUI jest pobierana.", "UniGetUI {0} is ready to be installed.": "UniGetUI w wersji {0} jest gotowy do zainstalowania.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (i jego pakiety)", "Uninstall and more": "Odinstaluj i więcej", "Uninstall and remove data": "Odinstaluj i usuń dane", "Uninstall as administrator": "Odinstaluj jako administrator", "Uninstall canceled by the user!": "Usuwanie aplikacji zostało anulowane przez użytkownika!", "Uninstall failed": "Odinstalowywanie nie powiodło się", "Uninstall options": "<PERSON><PERSON><PERSON>", "Uninstall package": "<PERSON><PERSON><PERSON><PERSON><PERSON> paki<PERSON>", "Uninstall package, then reinstall it": "Odinstaluj i ponownie zainstaluj pakiet", "Uninstall package, then update it": "<PERSON><PERSON><PERSON><PERSON>j, a potem zaktualizuj pakiet", "Uninstall previous versions when updated": "Odinstaluj poprzednie wersje po aktualizacji", "Uninstall selected packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> wy<PERSON> paki<PERSON>", "Uninstall selection": "<PERSON>dinstal<PERSON>j wybrane", "Uninstall succeeded": "Odinstalowanie powiodło się", "Uninstall the selected packages with administrator privileges": "Odinstaluj wybrane pakiety z uprawnieniami administratora", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Pakiety które można odinstalowa<PERSON> ze źrdółem \"{0}\" nie są opublikowane w żadnym menedżerze pakietów, więc nie ma żadnych informacji, które można by o nich pokaza<PERSON>.", "Unknown": "Brak informacji", "Unknown size": "<PERSON><PERSON><PERSON><PERSON>", "Unset or unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub nieznana", "Up to date": "Aktualny", "Update": "Zaktualizuj", "Update WingetUI automatically": "Aktualizuj UniGetUI automatycznie", "Update all": "Zaktualizuj wszystko", "Update and more": "Aktualizuj i więcej", "Update as administrator": "Zaktualizuj jako administrator", "Update check frequency, automatically install updates, etc.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sprawdzania aktualizacji, automatyczna instalacja aktualizacji itp.", "Update checking": null, "Update date": "Data aktualizacji", "Update failed": "Aktualizacja nie powiodła się", "Update found!": "Znaleziono aktualizację!", "Update now": "Zaktualizuj te<PERSON>", "Update options": "Opcje aktualizacji", "Update package indexes on launch": "Aktualizuj indeks pakietów przy starcie", "Update packages automatically": "Aktualizuj pakiety automatycznie", "Update selected packages": "Zaktualizuj wybrane p<PERSON>", "Update selected packages with administrator privileges": "Zaktualizuj wybrane pakiety z uprawnieniami administratora", "Update selection": "Zaktualizuj wybrane", "Update succeeded": "Aktualizacja się powiodła", "Update to version {0}": "Zaktualizuj do wersji {0}", "Update to {0} available": "jest dostępna aktualizacja do {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Automatyczna aktualizacja plików Git vcpkg (wymaga zainstalowanego Git)", "Updates": "Aktualizacje", "Updates available!": "Są dostępne aktualizacje!", "Updates for this package are ignored": "Aktualizacje tego pakietu są ignorowane", "Updates found!": "Znaleziono aktualizacje!", "Updates preferences": "Ustawienia aktualizacji", "Updating WingetUI": "Aktualizacja UniGetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Używaj starszego, dołączonego WinGet zamiast PowerShell CMDLet", "Use a custom icon and screenshot database URL": "Użyj niestandardowej ikony i adresu URL bazy danych zrzutów ekranu", "Use bundled WinGet instead of PowerShell CMDlets": "Używaj dołączonego WinGet zamiast PowerShell CMDlet", "Use bundled WinGet instead of system WinGet": "Użyj wbudowanego WinGet zamiast systemowego", "Use installed GSudo instead of UniGetUI Elevator": "Użyj zainstalowanego GSudo zamiast UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Użyj zainstalowanego GSudo zamiast wbudowanego (wymaga restartu aplikacji)", "Use system Chocolatey": "Użyj systemowego Chocolatey", "Use system Chocolatey (Needs a restart)": "Użyj systemowego Chocolatey (wymaga ponownego uruchomienia)", "Use system Winget (Needs a restart)": "Użyj systemowego Winget (wymaga restartu)", "Use system Winget (System language must be set to english)": "Użyj systemowego WinGet (język systemu musi być ustawiony na angielski)", "Use the WinGet COM API to fetch packages": "Używaj WinGet COM API, aby pob<PERSON> pakiety\n", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Użyj modułu WinGet PowerShell zamiast WinGet COM API", "Useful links": "Użyteczne adresy", "User": "Użytkownik", "User interface preferences": "Ustawienia interfejsu użytkownika", "User | Local": "Użytkownik | Lokalnie", "Username": "Nazwa użytkownika", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Korzystanie z UniGetUI oznacza akceptację licencji GNU Lesser General Public License v2.1.", "Using WingetUI implies the acceptation of the MIT License": "Korzystanie z UniGetUI oznacza akceptację licencji MIT.", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Nie znaleziono katalogu głównego vcpkg. Zdefiniuj zmienną środowiskową %VCPKG_ROOT% lub zdefiniuj ją w ustawieniach UniGetUI", "Vcpkg was not found on your system.": "Nie znaleziono vcpkg w systemie.", "Verbose": "Wyczerpujący", "Version": "<PERSON><PERSON><PERSON>", "Version to install:": "Wersja do instalacji:", "Version:": "Wersja:", "View GitHub Profile": "Zobacz profil na GitHub", "View WingetUI on GitHub": "Zobacz UniGetUI na GitHubie", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Zobacz kod źródłowy UniGetUI. Tam moż<PERSON>z zgła<PERSON><PERSON><PERSON> błędy lub sugerowa<PERSON> nowe funkcje, a nawet bezpośrednio wspomóc projekt UniGetUI", "View mode:": "Tryb widoku:", "View on UniGetUI": "Zobacz w UniGetUI", "View page on browser": "Zobacz w przeglądarce", "View {0} logs": "Zobacz logi {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Przed przystąpieniem do wykonywania zadań wymagających połączenia internetowego należy zaczekać, aż urządzenie zostanie połączone z internetem.", "Waiting for other installations to finish...": "Oczekiwanie na ukończenie pozostałych instalacji...", "Waiting for {0} to complete...": "Oczekiwanie na zakończenie {0}...", "Warning": "Ostrzeżenie", "Warning!": "Ostrzeżenie!", "We are checking for updates.": "Sprawdzamy dos<PERSON>ę<PERSON>ność aktualizacji.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Nie mogliśmy załadować szczegółowych informacji o tym pakiecie, ponieważ nie został on znaleziony w żadnym z twoich źródeł pakietów.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Nie mogliśmy wczytać szczegółowych informacji o tym pakiecie, ponieważ nie został on zainstalowany z dostępnego menedżera pakietów.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON><PERSON> mog<PERSON> wyk<PERSON> {action} na {package}. Spróbuj ponownie później. Kliknij na \"{showDetails}\", aby u<PERSON><PERSON>ć logi z instalatora.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON><PERSON> m<PERSON> {action} {package}. Spróbuj ponownie później. Kliknij na \"{showDetails}\", aby uzyskać logi z deinstalatora.", "We couldn't find any package": "<PERSON><PERSON> mogliśmy znaleźć żadnego pakietu", "Welcome to WingetUI": "Witaj w UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Podczas instalacji pakietów z paczki, zainstaluj również pakiety, które są już zainstalowane.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON>dy zostaną wykryte nowe skróty, usuń je automatycznie zamiast wyświ<PERSON>lać to okno dialogowe.", "Which backup do you want to open?": "Którą kopię zapasową chcesz otworzyć?", "Which package managers do you want to use?": "Których menedżerów pakietów chcesz używać?", "Which source do you want to add?": "<PERSON><PERSON><PERSON> ch<PERSON> do<PERSON>?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Chociaż WinGet może być używany w ramach UniGetUI, UniGetUI można używać z innymi menedżerami pakietów, co może być mylące. W przeszłości UniGetUI był zaprojektowany do współpracy wyłącznie z Winget, ale obecnie tak nie jest i dlatego nazwa UniGetUI nie reprezentuje tego, czym ten projekt ma się stać.", "WinGet could not be repaired": "WinGet nie został naprawiony", "WinGet malfunction detected": "<PERSON><PERSON>ryto a<PERSON> WinGet", "WinGet was repaired successfully": "WinGet naprawiono pomyślnie", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Wszystko jest zaktualizowane", "WingetUI - {0} updates are available": "UniGetUI - {0} aktualizacji dostępnych", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "Strona domowa UniGetUI", "WingetUI Homepage - Share this link!": "Strona domowa UniGetUI - podziel się tym linkiem!", "WingetUI License": "Licencja UniGetUI", "WingetUI Log": "Dziennik zdarzeń UniGetUI", "WingetUI Repository": "Repozytorium UniGetUI", "WingetUI Settings": "Ustawienia UniGetUI", "WingetUI Settings File": "Plik z ustawieniami UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI używa poniższych bibliotek. Bez nich UniGetUI by nie p<PERSON><PERSON><PERSON>.", "WingetUI Version {0}": "Wersja UniGetUI {0}", "WingetUI autostart behaviour, application launch settings": "Zachowanie autostartu UniGetUI, opcje uruchamiania aplikacji", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI może sprawdzić, czy oprogramowanie ma dostępne aktualizacje i zainstalować je automatycznie, je<PERSON><PERSON> ch<PERSON>z", "WingetUI display language:": "Język interfejsu UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI został uruchomiony z prawami administratora, co nie jest zalecane. Po uruchomieniu UniGetUI z prawmi administratora, KAŻDA operacja uruchomiona z UniGetUI będzie miała uprawnienia administratora. Możesz nadal korzystać z programu, ale zdecydowanie zalecamy, aby nie uruchamiać UniGetUI z uprawnieniami administratora.\n", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI został przetłumaczony na ponad 40 języków dzięki tłumaczom-wolontariuszom. Dziękuję 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI nie został przetłumaczony przy użyciu translatora! Tłumaczenia zostały wykonane przez tych użytkowników:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI to aplikacja, która ułatwia zarządzanie oprogramowaniem, zapewniając kompleksowy interfejs graficzny dla menedżerów pakietów wiersza poleceń.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "Nazwa WinGetUI została zmieniona w celu podkreślenia różnicy między UniGetUI (interfejsem, którego używasz teraz) a WinGet (menedżerem pakietów opracowanym przez firmę Microsoft, z którym nie jestem związany)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI jest aktualizowany. Kiedy skończy, nastąpi restart programu.", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI jest darmowy i pozostanie darmowy na zawsze. Bez reklam, bez karty kredytowej, bez wersji premium. W 100% za darmo, na zawsze.", "WingetUI log": "Dziennik zdarzeń UniGetUI", "WingetUI tray application preferences": "Ustawienia UniGetUI na pasku powiadomień", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI korzysta z następujących bibliotek. Bez nich UniGetUI by nie p<PERSON><PERSON><PERSON>.", "WingetUI version {0} is being downloaded.": "Wersja {0} UniGetUI jest pobierana.", "WingetUI will become {newname} soon!": "UniGetUI niedługo stanie się {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI nie będzie okresowo sprawdzać dostępności aktualizacji. Będą one nadal sprawdzane podczas uruchamiania, ale nie będziesz o nich informowany.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI wyświetli monit UAC za każdym razem, gdy pakiet wymaga podniesienia uprawnień do instalacji.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI wkrótce zmieni nazwę na {newname}. <PERSON><PERSON> <PERSON><PERSON><PERSON> to oznaczać żadnych zmian w aplikacji. Ja (deweloper) będę kontynuował rozwój tego projektu, tak jak robię to teraz, ale pod inną nazwą.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "Stworzenie UniGetUI nie byłoby możliwe bez pomocy naszych świetnych kontrybutorów. Odwiedź ich profile na GitHub. UniGetUI bez nich by nie istniał!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI nie powstałby bez pomocy kontrybutorów. Dziękuję wam wszystkim 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI w wersji {0} jest gotowy do zainstalowania.", "Write here the process names here, separated by commas (,)": "Wpisz tutaj nazwy procesów, odseparowane przecinkami (,)", "Yes": "Tak", "You are logged in as {0} (@{1})": "Zalogowano jako {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "<PERSON><PERSON><PERSON><PERSON> to zachowanie w ustawieniach bezpieczeństwa UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "<PERSON><PERSON><PERSON><PERSON> pole<PERSON>, kt<PERSON>re będą uruchamiane przed lub po zainstalowaniu, aktualizacji lub odinstalowaniu tego pakietu. Będą one uruchamiane w w<PERSON><PERSON> pole<PERSON>, więc skrypty CMD będą tutaj działa<PERSON>.", "You have currently version {0} installed": "Aktualnie masz zainstalowaną wersję {0}", "You have installed WingetUI Version {0}": "Zainstalowano UniGetUI w wersji {0}", "You may lose unsaved data": "<PERSON><PERSON><PERSON><PERSON> utracić niezapisane dane", "You may need to install {pm} in order to use it with WingetUI.": "<PERSON><PERSON><PERSON> być konieczne zainstalowanie {pm}, aby <PERSON><PERSON> go z UniGetUI.", "You may restart your computer later if you wish": "Mo<PERSON><PERSON><PERSON> uru<PERSON>ć ponownie komputer później", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Zostaniesz poproszony tylko raz, a uprawnienia administratora zostaną przyznane pakietom, które o nie poproszą.", "You will be prompted only once, and every future installation will be elevated automatically.": "Zostaniesz poproszony tylko raz, a każda kolejna instalacja przebiegnie z wyższymi uprawnieniami.", "You will likely need to interact with the installer.": "Prawdopodobnie konieczna będzie interakcja z instalatorem.", "[RAN AS ADMINISTRATOR]": "URUCHOMIONY JAKO ADMINISTRATOR", "buy me a coffee": "kup mi kawę", "extracted": "wypakowany", "feature": "<PERSON><PERSON><PERSON>", "formerly WingetUI": "<PERSON><PERSON><PERSON>", "homepage": "strona główna", "install": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "installation": "instalacja", "installed": "zainstalowano", "installing": "instalowanie", "library": "biblioteka", "mandatory": "obowiązkowe", "option": "op<PERSON><PERSON>", "optional": "opcjonalne", "uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstallation": "odinstalowywanie", "uninstalled": "odinstalowano", "uninstalling": "odinstalowywanie", "update(noun)": "aktualizacja", "update(verb)": "aktualizuj", "updated": "zaktualizowano", "updating": "aktualizacja", "version {0}": "we<PERSON><PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} opcje instalacji są obecnie zablokowane, ponieważ {0} używa domyślnych opcji instalacji.", "{0} Uninstallation": "Odinstalowywanie {0}", "{0} aborted": "Anulowany: {0}", "{0} can be updated": "<PERSON><PERSON><PERSON> zak<PERSON>alizować: {0}", "{0} can be updated to version {1}": "{0} m<PERSON>ż<PERSON> być zaktualizowany do wersji {1}", "{0} days": "{0} dni", "{0} desktop shortcuts created": "Utworzono {0} skrótów na pulpicie", "{0} failed": "{0} nie powiodło się", "{0} has been installed successfully.": "{0} zainstalowano pomyślnie.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} zainstalowano pomyślnie. Zalecany jest restart UniGetUI by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> instalację", "{0} has failed, that was a requirement for {1} to be run": "{0} nie pow<PERSON><PERSON><PERSON>o si<PERSON>, co by<PERSON>o warunkiem uruchomienia {1}", "{0} homepage": "Strona domowa {0}", "{0} hours": "{0} <PERSON><PERSON>(y)", "{0} installation": "{0} instalowanie", "{0} installation options": "{0} - op<PERSON><PERSON>", "{0} installer is being downloaded": "Trwa pobieranie instalatora {0}", "{0} is being installed": "{0} jest instalowany", "{0} is being uninstalled": "{0} jest odin<PERSON><PERSON><PERSON><PERSON>y", "{0} is being updated": "{0} jest aktualiz<PERSON><PERSON>", "{0} is being updated to version {1}": "{0} jest aktualizowany do wersji {1}", "{0} is disabled": "{0} jest w<PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} minutes": "{0} minut", "{0} months": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} packages are being updated": "Aktualizowane pakiety: {0}", "{0} packages can be updated": "{0} pakietów może zostać zaktualizowanych", "{0} packages found": "Znalezione pakiety: {0}", "{0} packages were found": "Znalezionie pakiety: {0}", "{0} packages were found, {1} of which match the specified filters.": "Znaleziono {0} pakietów, z których {1} pasuje do określonych filtrów.", "{0} selected": null, "{0} settings": "Ustawienia {0}", "{0} status": "Status {0}", "{0} succeeded": "{0} zakończono sukcesem", "{0} update": "Aktualizacja {0}", "{0} updates are available": "{0} aktualizacji jest dostępnych", "{0} was {1} successfully!": "{0} zostało {1} pomyślnie!", "{0} weeks": "{0} tygodni", "{0} years": "{0} lat", "{0} {1} failed": "{0} {1} nie powiodło się", "{package} Installation": "Instalowanie {package}", "{package} Uninstall": "Odinstaluj {package}", "{package} Update": "Aktualizuj {package}", "{package} could not be installed": "{package} nie może <PERSON><PERSON> z<PERSON>", "{package} could not be uninstalled": "{package} nie może by<PERSON> o<PERSON>ny", "{package} could not be updated": "{package} nie może <PERSON><PERSON>", "{package} installation failed": "Instalacja {package} nie powiodła się", "{package} installer could not be downloaded": "Instalator {package} nie mógł zostać pobrany", "{package} installer download": "Pobierz instalator {package}", "{package} installer was downloaded successfully": "Instalator {package} został pobrany pomyślnie", "{package} uninstall failed": "Odinstalowanie {package} nie powiodło się", "{package} update failed": "Aktualizacja {package} nie powiodła się", "{package} update failed. Click here for more details.": "Aktualizacja {package} nie powiodła się. Kliknij tutaj po więcej szczegółów.", "{package} was installed successfully": "{package} został zainstalowany pomyślnie", "{package} was uninstalled successfully": "{package} został odinstalowany pomyślnie", "{package} was updated successfully": "{package} został zaktualizowany pomyślnie", "{pcName} installed packages": "{pcName} ma zainstalowanych pakietów", "{pm} could not be found": "<PERSON><PERSON> m<PERSON>ż<PERSON> {pm}", "{pm} found: {state}": "Znaleziono {pm}: {state}", "{pm} is disabled": "{pm} jest w<PERSON><PERSON><PERSON><PERSON><PERSON>", "{pm} is enabled and ready to go": "{pm} jest włączony i gotowy do użycia", "{pm} package manager specific preferences": "Ustawienia menedżera pakietów {pm}", "{pm} preferences": "Ustawienia {pm}", "{pm} version:": "We<PERSON><PERSON> {pm}:", "{pm} was not found!": "Nie znaleziono {pm}!"}