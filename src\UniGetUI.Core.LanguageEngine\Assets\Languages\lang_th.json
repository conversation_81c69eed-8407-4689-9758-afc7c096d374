{"\"{0}\" is a local package and can't be shared": "\"{0}\" เป็นแพ็กเกจภายในเครื่องและไม่สามารถแชร์ได้ ", "\"{0}\" is a local package and does not have available details": "\"{0}\" เป็นแพ็กเกจภายในเครื่องและไม่มีรายละเอียดที่สามารถแสดงได้", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" เป็นแพ็กเกจภายในเครื่องและไม่สามารถใช้งานร่วมกับฟีเจอร์นี้ได้", "(Last checked: {0})": "(ตรวจสอบล่าสุด: {0})", "(Number {0} in the queue)": "(ลำดับ {0} ในคิว)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@dulapahv, @a<PERSON><PERSON><PERSON><PERSON>, @r<PERSON><PERSON><PERSON><PERSON>, @vestearth, @hanchain", "0 packages found": "พบ 0 แพ็กเกจ", "0 updates found": "พบ 0 อัปเดต", "1 - Errors": "1 - ข้อผิดพลาด", "1 day": "1 วัน", "1 hour": "1 ชั่วโมง", "1 month": "1 เดือน", "1 package was found": "พบ 1 แพ็กเกจ", "1 update is available": "มีการอัปเดต 1 รายการ", "1 week": "1 สัปดาห์", "1 year": "1 ปี", "1. Navigate to the \"{0}\" or \"{1}\" page.": "โปรดไปที่หน้าของ \"{0}\" หรือ \"{1}\"", "2 - Warnings": "2 - คำเตือน", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. ค้นหาแพ็กเกจที่คุณต้องการเพิ่มลงในชุดแพ็กเกจ จากนั้นเลือกช่องทำเครื่องหมายที่อยู่ซ้ายสุดของแต่ละรายการ", "3 - Information (less)": "3 - ข้อมูล (ย่อ)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. เมื่อเลือกแพ็กเกจที่ต้องการเพิ่มเข้าในบันเดิลแล้ว ให้หาและคลิกตัวเลือก \"{0}\" บนแถบเครื่องมือ", "4 - Information (more)": "4 - ข้อมูล (ละเอียด)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. แพ็กเกจของคุณจะถูกเพิ่มลงในชุดแพ็กเกจเรียบร้อยแล้ว คุณสามารถเพิ่มแพ็กเกจต่อไปได้ หรือส่งออกชุดแพ็กเกจนี้", "5 - information (debug)": "5 - ข้อมูล (ดีบั๊ก)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "ตัวจัดการไลบรารี C/C++ ยอดนิยม มีไลบรารี C/C++ และเครื่องมืออื่น ๆ ที่เกี่ยวข้องกับ C/C++ มากมาย<br>ประกอบด้วย: <b>ไลบรารี C/C++ และเครื่องมือที่เกี่ยวข้อง</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "คลังข้อมูลที่เต็มไปด้วยเครื่องมือและโปรแกรมสั่งทำการที่ออกแบบมาโดยคำนึงถึงระบบนิเวศ .NET ของ Microsoft<br>ประกอบด้วย: <b>เครื่องมือและสคริปต์ที่เกี่ยวข้องกับ .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "คลังข้อมูลที่เต็มไปด้วยเครื่องมือที่ออกแบบมาโดยคำนึงถึงระบบนิเวศ .NET ของ Microsoft<br>ประกอบด้วย: <b>เครื่องมือที่เกี่ยวข้องกับ .NET</b>", "A restart is required": "จำเป็นต้องรีสตาร์ท", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "เกี่ยวกับ", "About Qt6": "เกี่ยวกับ Qt6", "About WingetUI": "เกี่ยวกับ UniGetUI", "About WingetUI version {0}": "เกี่ยวกับ UniGetUI เวอร์ชัน {0}", "About the dev": "เกี่ยวกับผู้พัฒนา", "Accept": "ยอมรับ", "Action when double-clicking packages, hide successful installations": "ดำเนินการเมื่อดับเบิ้ลคลิกที่แพ็กเกจ และซ่อนการติดตั้งที่สำเร็จ", "Add": "เพิ่ม", "Add a source to {0}": "เพิ่มซอร์ซไปที่ {0}", "Add a timestamp to the backup file names": "เพิ่มการบันทึกเวลาในชื่อไฟล์สำรองข้อมูล", "Add a timestamp to the backup files": "เพิ่มการบันทึกเวลาในไฟล์สำรองข้อมูล", "Add packages or open an existing bundle": "เพิ่มแพ็กเกจหรือเปิดบันเดิลที่มีอยู่", "Add packages or open an existing package bundle": "เพิ่มแพ็กเกจหรือเปิดชุดแพ็กเกจ(บันเดิล) ที่มีอยู่", "Add packages to bundle": "เพิ่มแพ็กเกจเข้าไปในชุดแพ็กเกจ(บันเดิล)", "Add packages to start": "เพิ่มแพ็กเกจเพื่อเริ่มใช้งาน", "Add selection to bundle": "เพิ่มรายการที่เลือกไว้ไปยังบันเดิล", "Add source": "เพิ่มซอร์ซ", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "เพิ่มรายการอัปเดตที่ไม่สำเร็จด้วยข้อความ ‘ไม่พบการอัปเดตที่เหมาะสม’ ลงในรายการอัปเดตที่ถูกละเว้น", "Adding source {source}": "กำลังเพิ่มแหล่งที่มา (ซอร์ซ) {source}", "Adding source {source} to {manager}": "กำลังเพิ่มซอร์ซ {source} ไปยัง {manager}", "Addition succeeded": "การเพิ่มสำเร็จ", "Administrator privileges": "สิทธิ์ผู้ดูแลระบบ", "Administrator privileges preferences": "การตั้งค่าสิทธิ์ผู้ดูแลระบบ", "Administrator rights": "สิทธิ์ผู้ดูแลระบบ", "Administrator rights and other dangerous settings": null, "Advanced options": "ตัวเลือกขั้นสูง", "All files": "ไฟล์ทั้งหมด", "All versions": "เวอร์ชันทั้งหมด", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "อนุญาตให้การดำเนินการดำเนินการพร้อมกัน", "Allow parallel installs (NOT RECOMMENDED)": "อนุญาตให้ติดตั้งพร้อมกัน (ไม่แนะนำ)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "อนุญาตให้ดำเนินการ {pm} ดำเนินการพร้อมกัน", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "อีกทางหนึ่ง คุณสามารถติดตั้ง {0} ได้โดยการรันคำสั่งต่อไปนี้ใน Windows PowerShell prompt:", "Always elevate {pm} installations by default": "ยกระดับการติดตั้ง {pm} เป็นค่าเริ่มต้นเสมอ", "Always run {pm} operations with administrator rights": "เรียกใช้การดำเนินการ {pm} ด้วยสิทธิ์ผู้ดูแลระบบเสมอ", "An error occurred": "มีข้อผิดพลาดเกิดขึ้น", "An error occurred when adding the source: ": "มีข้อผิดพลาดเกิดขึ้นขณะเพิ่มซอร์ซ:", "An error occurred when attempting to show the package with Id {0}": "เกิดข้อผิดพลาดขณะพยายามแสดงแพ็กเกจที่มี Id {0}", "An error occurred when checking for updates: ": "มีข้อผิดพลาดเกิดขึ้นขณะตรวจสอบการอัปเดต:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "มีข้อผิดพลาดเกิดขึ้นขณะประมวลผลแพ็กเกจนี้", "An error occurred:": "เกิดข้อผิดพลาด:", "An interal error occurred. Please view the log for further details.": "เกิดข้อผิดพลาดภายใน โปรดดูบันทึกสำหรับรายละเอียดเพิ่มเติม", "An unexpected error occurred:": "มีข้อผิดพลาดที่ไม่คาดคิดเกิดขึ้น", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "เกิดปัญหาที่ไม่คาดคิดขึ้น ขณะพยายามซ่อมแซม WinGet กรุณาลองใหม่อีกครั้งในภายหลัง\n\n", "An update was found!": "พบการอัปเดต!", "Android Subsystem": "ระบบย่อยสำหรับ Android", "Another source": "แหล่งที่มาอื่น", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "ทางลัดใหม่ที่ถูกสร้างขึ้นระหว่างการติดตั้งหรือการอัปเดตจะถูกลบออกโดยอัตโนมัติ แทนที่จะแสดงหน้าต่างยืนยันในครั้งแรกที่ตรวจพบ", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "ทางลัดที่สร้างหรือแก้ไขนอก UniGetUI จะถูกละเว้น คุณสามารถเพิ่มทางลัดเหล่านั้นได้ผ่านปุ่ม {0}", "Any unsaved changes will be lost": "การเปลี่ยนแปลงที่ยังไม่ได้บันทึกจะหายไป", "App Name": "ชื่อแอพ", "Appearance": "การแสดงผล", "Application theme, startup page, package icons, clear successful installs automatically": "ธีมแอปพลิเคชัน, หน้าเริ่มต้น, ไอคอนแพ็กเกจ, ล้างการติดตั้งที่สำเร็จโดยอัตโนมัติ", "Application theme:": "ธีม:", "Apply": null, "Architecture to install:": "สถาปัตยกรรมที่จะทำการติดตั้ง:", "Are these screenshots wron or blurry?": "สกรีนช็อตพวกนี้ผิดพลาดหรือเบลอหรือเปล่า?", "Are you really sure you want to enable this feature?": "คุณแน่ใจจริงๆ หรือไม่ที่ต้องการเปิดใช้งานฟีเจอร์นี้?", "Are you sure you want to create a new package bundle? ": "คุณแน่ใจหรือไม่ที่ต้องการสร้างแพ็กเกจบันเดิลใหม่?", "Are you sure you want to delete all shortcuts?": "คุณแน่ใจหรือไม่ที่ต้องการลบทางลัดทั้งหมด?", "Are you sure?": "คุณแน่ใจใช่ไหม", "Ascendant": "เด่นขึ้น", "Ask for administrator privileges once for each batch of operations": "ขอสิทธิ์ผู้ดูแลระบบหนึ่งครั้งสำหรับการดำเนินการแต่ละชุด", "Ask for administrator rights when required": "ขอสิทธิ์ผู้ดูแลระบบเมื่อจำเป็น", "Ask once or always for administrator rights, elevate installations by default": "ถามครั้งเดียวหรือเรื่อย ๆ สำหรับสิทธิ์ผู้ดูแลระบบ เพื่อการยกระดับการติดตั้งเป็นค่าเริ่มต้น", "Ask only once for administrator privileges": "ขอสิทธิ์ผู้ดูแลระบบครั้งเดียวเท่านั้น", "Ask only once for administrator privileges (not recommended)": "ขอสิทธิ์ผู้ดูแลระบบครั้งเดียวเท่านั้น (ไม่แนะนำ)", "Ask to delete desktop shortcuts created during an install or upgrade.": "สอบถามการลบทางลัดเดสก์ท็อปที่สร้างจากการติดตั้งหรืออัปเกรด", "Attention required": "ต้องการความสนใจ", "Authenticate to the proxy with an user and a password": "ยืนยันตัวตนเพื่อเข้าใช้พร็อกซีด้วยชื่อผู้ใช้และรหัสผ่าน", "Author": "ผู้จัดทำ", "Automatic desktop shortcut remover": "เครื่องมือลบทางลัดหน้าจออัตโนมัติ\n", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "บันทึกรายการแพ็กเกจทั้งหมดที่ติดตั้งไว้โดยอัตโนมัติเพื่อทำให้การกู้คืนง่ายขึ้น", "Automatically save a list of your installed packages on your computer.": "บันทึกรายการแพ็กเกจที่ติดตั้งบนคอมพิวเตอร์ของคุณโดยอัตโนมัติ", "Autostart WingetUI in the notifications area": "เริ่ม UniGetUI อัตโนมัติในถาดการแจ้งเตือน", "Available Updates": "การอัปเดตที่พร้อมใช้งาน", "Available updates: {0}": "อัปเดตที่พบ: {0}", "Available updates: {0}, not finished yet...": "อัปเดตที่พบ: {0} และยังไม่เสร็จ...", "Backing up packages to GitHub Gist...": "กำลังสำรองข้อมูลแพ็กเกจไปยัง GitHub Gist...", "Backup": "สำรองข้อมูล", "Backup Failed": null, "Backup Successful": "สำรองข้อมูลสำเร็จ", "Backup and Restore": null, "Backup installed packages": "สำรองข้อมูลแพ็กเกจที่ติดตั้งแล้ว", "Backup location": "โฟลเดอร์สำรองข้อมูล", "Become a contributor": "มาเป็นผู้ร่วมให้ข้อมูล", "Become a translator": "มาเป็นผู้แปล", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "คุณสมบัติเบต้าและตัวเลือกอื่น ๆ ที่ไม่ควรแตะต้อง", "Both": "ทั้งคู่", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "แต่นี่คือสิ่งอื่น ๆ ที่คุณสามารถทำเพื่อเรียนรู้เพิ่มเติมเกี่ยวกับ UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "การปิดใช้งานตัวจัดการแพ็กเกจจะทำให้คุณจะไม่สามารถดูหรืออัปเดตแพ็กเกจได้อีกต่อไป", "Cache administrator rights and elevate installers by default": "แคชสิทธิ์ผู้ดูแลระบบ และยกระดับตัวติดตั้งเป็นค่าเริ่มต้น", "Cache administrator rights, but elevate installers only when required": "แคชสิทธิ์ผู้ดูแลระบบแต่ยกระดับตัวติดตั้งเมื่อจำเป็นเท่านั้น", "Cache was reset successfully!": "การรีเซ็ตแคชเสร็จสมบูรณ์", "Can't {0} {1}": "ไม่สามารถ {0} {1}", "Cancel": "ยกเลิก", "Cancel all operations": "ยกเลิกการดำเนินการทั้งหมด", "Change backup output directory": "เปลี่ยนไดเร็กทอรีเอาท์พุตของการสำรองข้อมูล", "Change default options": "เปลี่ยนการตั้งค่าเริ่มต้น", "Change how UniGetUI checks and installs available updates for your packages": "เปลี่ยนวิธีที่ UniGetUI ตรวจสอบและติดตั้งการอัปเดตที่พร้อมใช้งานสำหรับแพ็กเกจของคุณ", "Change how UniGetUI handles install, update and uninstall operations.": "กำหนดวิธีการจัดการการติดตั้ง อัปเดต และการถอนการติดตั้งของ UniGetUI", "Change how UniGetUI installs packages, and checks and installs available updates": "กำหนดวิธีการติดตั้งแพ็กเกจและตรวจสอบติดตั้งการอัปเดตของ UniGetUI", "Change how operations request administrator rights": "เปลี่ยนวิธีที่การดำเนินการขอสิทธิ์ผู้ดูแลระบบ", "Change install location": "เปลี่ยนตำแหน่งการติดตั้ง", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "ตรวจสอบการอัปเดตแพ็กเกจเป็นระยะ ๆ", "Check for updates": "ตรวจสอบการอัปเดต", "Check for updates every:": "ตรวจสอบการอัปเดตทุก:", "Check for updates periodically": "ตรวจสอบการอัปเดตเป็นระยะ", "Check for updates regularly, and ask me what to do when updates are found.": "ตรวจสอบอัปเดตอย่างสม่ำเสมอ และถามฉันว่าจะดำเนินการอย่างไรเมื่อมีอัปเดต", "Check for updates regularly, and automatically install available ones.": "ตรวจสอบอัปเดตอย่างสม่ำเสมอ และดำเนินการอัปเดตโดยอัตโนมัติ", "Check out my {0} and my {1}!": "ดู {0} และ {1} ของผมสิ!", "Check out some WingetUI overviews": "มาดูความคิดเห็นเกี่ยวกับ UniGetUI สิ", "Checking for other running instances...": "กำลังค้นหาอินสแตนซ์อื่น ๆ ที่กำลังทำงานอยู่...", "Checking for updates...": "กำลังตรวจสอบอัปเดต...", "Checking found instace(s)...": "กำลังตรวจสอบอินสแตนซ์ที่พบ...", "Choose how many operations shouls be performed in parallel": "เลือกจำนวนการดำเนินการให้ดำเนินการพร้อมกัน", "Clear cache": "ล้างแคช", "Clear finished operations": "ล้างการดำเนินการที่เสร็จสิ้นแล้ว", "Clear selection": "ล้างการเลือก", "Clear successful operations": "ล้างการดำเนินการที่สำเร็จแล้ว", "Clear successful operations from the operation list after a 5 second delay": "ล้างการดำเนินการที่สำเร็จออกจากรายการ หลังจากเวลาผ่านไป 5 วินาที", "Clear the local icon cache": "ล้างแคชไอคอนในเครื่อง", "Clearing Scoop cache - WingetUI": "กำลังล้างแคช Scoop - UniGetUI", "Clearing Scoop cache...": "กำลังล้างแคชของ Scoop", "Click here for more details": "คลิกที่นี่เพื่อดูรายละเอียดเพิ่มเติม", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "คลิกที่ติดตั้ง เพื่อเริ่มต้นขั้นตอนการติดตั้ง ถ้าข้ามการติดตั้ง UniGetUI อาจไม่ทำงานตามที่คาดไว้", "Close": "ปิด", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "ปิด UniGetUI ไปที่ถาดการแจ้งเตือน", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "เอาต์พุตบรรทัดคำสั่ง", "Command-line to run:": null, "Compare query against": "เปรียบเทียบคิวรีกับ", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "ข้อมูลคอมโพเนนต์", "Concurrency and execution": null, "Connect the internet using a custom proxy": "เชื่อมต่ออินเทอร์เน็ตโดยใช้พร็อกซี่กำหนดเอง", "Continue": "ดำเนินการต่อ", "Contribute to the icon and screenshot repository": "มีส่วนร่วมในคลังข้อมูล ไอคอน และภาพหน้าจอ", "Contributors": "ผู้ที่มีส่วนช่วย", "Copy": "คัดลอก", "Copy to clipboard": "คัดลอกไปยังคลิปบอร์ด", "Could not add source": "ไม่สามารถเพิ่มแหล่งที่มาได้", "Could not add source {source} to {manager}": "ไม่สามารถเพิ่มซอร์ซ {source} ให้กับ {manager}", "Could not back up packages to GitHub Gist: ": "ไม่สามารถสำรองข้อมูลแพ็กเกจไปยัง GitHub Gist:", "Could not create bundle": "ไม่สามารถสร้างชุดแพ็กเกจ(บันเดิล)ได้", "Could not load announcements - ": "ไม่สามารถโหลดประกาศได้ -", "Could not load announcements - HTTP status code is $CODE": "ไม่สามารถโหลดประกาศ - รหัสสถานะ HTTP คือ $CODE", "Could not remove source": null, "Could not remove source {source} from {manager}": "ไม่สามารถลบซอร์ซ {source} ออกจาก {manager}", "Could not remove {source} from {manager}": null, "Create .ps1 script": null, "Credentials": "ข้อมูลรับรอง (Credentials)\n\n", "Current Version": "เวอร์ชันปัจจุบัน", "Current status: Not logged in": "สถานะตอนนี้: ยังไม่ได้ลงชื่อเข้าใช้", "Current user": "ผู้ใช้ปัจจุบัน", "Custom arguments:": "อาร์กิวเมนต์ที่กำหนดเอง:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "อาร์กิวเมนต์บรรทัดคำสั่งที่กำหนดเอง:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "ปรับแต่ง UniGetUI - สำหรับแฮกเกอร์ และผู้ใช้ขั้นสูงเท่านั้น", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ข้อควรระวัง: เราไม่รับผิดชอบแพ็กเกจที่ดาวน์โหลดมา กรุณาตรวจสอบให้แน่ใจว่าคุณติดตั้งซอฟต์แวร์ที่เชื่อถือได้เท่านั้น", "Dark": "มืด", "Decline": "ปฏิเสธ", "Default": "ค่าเริ่มต้น", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "ค่าเริ่มต้น - เหมาะสำหรับผู้ใช้ทั่วไป", "Default vcpkg triplet": "vcpkg triplet ค่าเริ่มต้น", "Delete?": "ลบ?", "Dependencies:": null, "Descendant": "รายการย่อย", "Description:": "รายละเอียด:", "Desktop shortcut created": "สร้างทางลัดบนเดสก์ท็อปแล้ว", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "การพัฒนาคือเรื่องยาก และแอปพลิเคชันนี้ฟรี แต่หากคุณชื่นชอบแอปพลิเคชันนี้ คุณสามารถ<b>เลี้ยงขนมพวกเรา</b>ได้เสมอ :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "ติดตั้งโดยตรงเมื่อดับเบิลคลิกที่รายการบนแท็บ \"{discoveryTab}\" (แทนที่จะแสดงข้อมูลแพ็กเกจ)", "Disable new share API (port 7058)": "ปิดการใช้งาน share API ตัวใหม่ (port 7058)", "Disable the 1-minute timeout for package-related operations": "ปิดใช้งานการหมดเวลา 1 นาทีในการดำเนินการแพ็กเกจ", "Disclaimer": "คำสงวนสิทธิ์", "Discover Packages": "ค้นหาแพ็กเกจ", "Discover packages": "ค้นหาแพ็กเกจ", "Distinguish between\nuppercase and lowercase": "แยกความแตกต่างระหว่างตัวพิมพ์ใหญ่และตัวพิมพ์เล็ก", "Distinguish between uppercase and lowercase": "แยกความแตกต่างระหว่างตัวพิมพ์ใหญ่และตัวพิมพ์เล็ก", "Do NOT check for updates": "ไม่ต้องตรวจสอบอัปเดต", "Do an interactive install for the selected packages": "ดำเนินการติดตั้งแบบอินเตอร์แอคทีฟสำหรับแพ็กเกจที่เลือก", "Do an interactive uninstall for the selected packages": "ดำเนินการถอนการติดตั้งแบบอินเตอร์แอคทีฟสำหรับแพ็กเกจที่เลือก", "Do an interactive update for the selected packages": "ดำเนินการอัปเดตแบบอินเตอร์แอคทีฟสำหรับแพ็กเกจที่เลือก", "Do not automatically install updates when the battery saver is on": "ไม่ติดตั้งการอัปเดตโดยอัตโนมัติเมื่อเปิดโหมดประหยัดแบตเตอรี่", "Do not automatically install updates when the network connection is metered": "ไม่ติดตั้งการอัปเดตโดยอัตโนมัติเมื่อการเชื่อมต่อเครื่อข่ายคิดราคาการใช้งาน", "Do not download new app translations from GitHub automatically": "ไม่ต้องดาวน์โหลดคำแปลใหม่จาก GitHub โดยอัตโนมัติ", "Do not ignore updates for this package anymore": "เลิกเพิกเฉยการอัปเดตแพ็กเกจนี้", "Do not remove successful operations from the list automatically": "ไม่ต้องลบการดำเนินการที่สำเร็จออกจากรายการโดยอัตโนมัติ", "Do not show this dialog again for {0}": "อย่าแสดงกล่องข้อความนี้อีกสำหรับ {0}", "Do not update package indexes on launch": "ไม่ต้องอัปเดตดัชนีแพ็กเกจตอนเปิดแอปพลิเคชัน", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "คุณยินยอมให้ UniGetUI เก็บและส่งข้อมูลสถิติการใช้งานแบบไม่เปิดเผยตัวตน เพื่อจุดประสงค์ในการพัฒนาและปรับปรุงประสบการณ์ของผู้ใช้ หรือไม่?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "คุณคิดว่า UniGetUI มีประโยชน์ใช่ไหม คุณสามารถสนับสนุนงานของผมหากทำได้ เพื่อช่วยให้ผมสามารถทำให้ UniGetUI เป็นสุดยอดอินเทอร์เฟซตัวจัดการแพ็กเกจขั้นสูงต่อไปได้", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "คุณเห็นว่าแอพนี้มีประโยชน์หรือไม่? ต้องการที่จะสนับสนุนผู้พัฒนาหรือไม่? ถ้าต้องการสามารถ {0} มันจะช่วยพวกเรามาก!", "Do you really want to reset this list? This action cannot be reverted.": "คุณแน่ใจที่จะรีเซ็ตรายการนี้หรือไม่? การกระทำนี้ไม่สามารถเรียกคืนได้", "Do you really want to uninstall the following {0} packages?": "คุณต้องการถอนการติดตั้ง {0} แพ็กเกจต่อไปนี้หรือไม่", "Do you really want to uninstall {0} packages?": "คุณแน่ใจหรือไม่ที่จะถอนการติดตั้ง {0} แพ็กเกจ", "Do you really want to uninstall {0}?": "คุณต้องการถอนการติดตั้ง {0} ใช่ไหม", "Do you want to restart your computer now?": "คุณต้องการที่จะรีสตาร์ทคอมพิวเตอร์ของคุณตอนนี้หรือไม่?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "คุณต้องการแปลภาษาของ UniGetUI เป็นภาษาของคุณหรือไม่? <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "ไม่พร้อมบริจาคก็ไม่เป็นไร คุณสามารถชวนเพื่อนของคุณมาใช้ UniGetUI และช่วยกระจายข่าวเกี่ยวกับ UniGetUI", "Donate": "บริจาค", "Done!": "เสร็จสิ้น", "Download failed": "ดาวน์โหลดไม่สำเร็จ", "Download installer": "ดาวน์โหลดตัวติดตั้ง", "Download operations are not affected by this setting": "การดาวน์โหลดไม่ได้รับผลกระทบจากการตั้งค่านี้", "Download selected installers": "ดาวน์โหลดตัวติดตั้งที่เลือกไว้", "Download succeeded": "ดาวน์โหลดสำเร็จ", "Download updated language files from GitHub automatically": "ดาวน์โหลดไฟล์แปลภาษาที่อัปเดตล่าสุดจาก GitHub โดยอัตโนมัติ", "Downloading": "กำลังดาวน์โหลด", "Downloading backup...": "กำลังดาวน์โหลดข้อมูลสำรอง...", "Downloading installer for {package}": "กำลังดาวน์โหลดตัวติดตั้งสำหรับ {package}", "Downloading package metadata...": "กำลังดาวน์โหลด metadata ของแพ็กเกจ", "Enable Scoop cleanup on launch": "เปิดใช้งานการล้างข้อมูล Scoop เมื่อเปิดโปรแกรม", "Enable WingetUI notifications": "เปิดการแจ้งเตือน UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "เปิดใช้ [experimental] เพื่อปรับปรุง WinGet troubleshooter", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "เปิดใช้งานการปรับปรุงประสิทธิภาพการใช้ CPU ในพื้นหลัง (อ่านเพิ่มเติมที่ Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "เปิดใช้งาน API พื้นหลัง (Widgets สำหรับ UniGetUI และการแชร์ พอร์ต 7058)", "Enable it to install packages from {pm}.": "เปิดใช้งานเพื่อติดตั้งแพ็กเกจจาก {pm}", "Enable the automatic WinGet troubleshooter": "เปิดใช้ WinGet troubleshooter อัตโนมัติ", "Enable the new UniGetUI-Branded UAC Elevator": "ใช้งานตัวยกระกับสิทธิผู้ใช้ของ UniGetUI", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "เปิดตัวเลือกการตั้งค่านี้ก็ต่อเมื่อคุณเข้าใจว่าสิ่งนี้ใช้ทำอะไรและผลข้างเคียงและอันตรายที่อาจเกิดขึ้น", "Enable {pm}": "เปิด {pm}", "Enter proxy URL here": "กรอก URL ของ proxy ที่นี่\n\n", "Entries that show in RED will be IMPORTED.": "บรรทัดสีแดงจะถูกนำเข้า", "Entries that show in YELLOW will be IGNORED.": "บรรทัดสีเหลืองจะถูกละเว้น", "Error": "ข้อผิดพลาด", "Everything is up to date": "ทุกอย่างได้รับการอัปเดตแล้ว", "Exact match": "ตรงทั้งหมด", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": "ขยายข้อมูลเวอร์ชัน", "Experimental settings and developer options": "การตั้งค่าการทดลองและการตั้งค่าสำหรับนักพัฒนา", "Export": "ส่งออก", "Export log as a file": "ส่งออกบันทึกไปยังไฟล์", "Export packages": "ส่งออกแพ็กเกจ", "Export selected packages to a file": "ส่งออกแพ็กเกจที่เลือกไปยังไฟล์", "Export settings to a local file": "ส่งออกการตั้งค่าไปยังไฟล์ในเครื่อง", "Export to a file": "ส่งออกไปยังไฟล์", "Failed": "ล้มเหลว", "Fetching available backups...": null, "Fetching latest announcements, please wait...": "กำลังดึงข้อมูลประกาศล่าสุด โปรดรอสักครู่...", "Filters": "ฟิลเตอร์", "Finish": "เสร็จสิ้น", "Follow system color scheme": "ตามธีมของระบบ", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "บังคับให้ใช้ winget เวอร์ชันที่คอมไพล์เป็น ARM (เฉพาะระบบ ARM64)", "Formerly known as WingetUI": "เดิมชื่อ WingetUI", "Found": "พบ", "Found packages: ": "แพ็กเกจที่พบ:", "Found packages: {0}": "แพ็กเกจที่พบ: {0}", "Found packages: {0}, not finished yet...": "แพ็กเกจที่พบ: {0} และยังไม่เสร็จ...", "General preferences": "การตั้งค่า", "GitHub profile": "โปรไฟล์ GitHub", "Global": "ส่วนกลาง", "Go to UniGetUI security settings": "ไปยังการตั้งค่าความปลอดภัยของ UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "คลังข้อมูลที่ยอดเยี่ยมของโปรแกรมอรรถประโยชน์ที่ไม่เป็นที่รู้จักแต่มีประโยชน์ และแพ็กเกจอื่น ๆ ที่น่าสนใจ<br>ประกอบด้วย: <b>โปรแกรมอรรถประโยชน์ โปรแกรมบรรทัดคำสั่ง ซอฟต์แวร์ทั่วไป (ต้องมีบักเก็ตเพิ่มเติม)</b>", "Great! You are on the latest version.": "เยี่ยมมาก! คุณใช้เวอร์ชันล่าสุดแล้ว", "Grid": "ตาราง", "Help": "ความช่วยเหลือ", "Help and documentation": "ความช่วยเหลือ เเละเอกสารประกอบ", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "คุณสามารถเปลี่ยนแปลงพฤติกรรมของ UniGetUI สำหรับทางลัดเหล่านี้ การเลือกทางลัดจะให้ UniGetUI ลบทางลัดนั้นหากมีการสร้างขึ้นในการอัปเกรดครั้งต่อไป การไม่เลือกจะคงทางลัดไว้เหมือนเดิม", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "สวัสดี ผมชื่อ Martí และผมเป็น <i>ผู้พัฒนา</i> UniGetUI ซึ่งถูกสร้างในเวลาว่างของผม!", "Hide details": "ซ่อนรายละเอียด", "Homepage": "เว็บไซต์", "Hooray! No updates were found.": "ไชโย! ไม่พบการอัปเดต", "How should installations that require administrator privileges be treated?": "การติดตั้งที่ต้องการสิทธิ์ผู้ดูแลระบบควรจะถูกจัดการอย่างไร?", "How to add packages to a bundle": "วิธีการเพิ่มแพ็กเกจไปยังชุดแพ็กเกจ", "I understand": "ฉันเข้าใจแล้ว", "Icons": "ไอคอน", "Id": "ไอดี", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "ละเว้นการอัปเดตในอนาคตสำหรับแพ็กเกจนี้", "Ignore packages from {pm} when showing a notification about updates": "ละเว้นแพ็กเกจจาก {pm} เมื่อแสดงการแจ้งเตือนเกี่ยวกับการอัปเดต", "Ignore selected packages": "ละเว้นแพ็กเกจที่เลือก", "Ignore special characters": "ละเว้นอักขระพิเศษ", "Ignore updates for the selected packages": "ละเว้นการอัปเดตสำหรับแพ็กเกจที่เลือก", "Ignore updates for this package": "ละเว้นการอัปเดตสำหรับแพ็กเกจนี้", "Ignored updates": "การอัปเดตที่ถูกละเว้น", "Ignored version": "เวอร์ชันที่ถูกละเว้น", "Import": "นำเข้า", "Import packages": "นำเข้าแพ็กเกจ", "Import packages from a file": "นำเข้าแพ็กเกจจากไฟล์", "Import settings from a local file": "นำเข้าการตั้งค่าจากไฟล์ในเครื่อง", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "กำลังเริ่มต้น UniGetUI...", "Install": "ติดตั้ง", "Install Scoop": "ติดตั้ง Scoop", "Install and more": null, "Install and update preferences": "ติดตั้งและอัพเดทการตั้งค่า", "Install as administrator": "ติดตั้งในฐานะผู้ดูแลระบบ", "Install available updates automatically": "ติดตั้งการอัปเดตที่พร้อมใช้งานโดยอัตโนมัติ", "Install location can't be changed for {0} packages": null, "Install location:": "ตำแหน่งติดตั้ง:", "Install options": null, "Install packages from a file": "ติดตั้งแพ็กเกจจากไฟล์", "Install prerelease versions of UniGetUI": null, "Install script": null, "Install selected packages": "ติดตั้งแพ็กเกจที่เลือก", "Install selected packages with administrator privileges": "ติดตั้งแพ็กเกจที่เลือกด้วยสิทธิ์ผู้ดูแลระแบบ", "Install selection": "ติดตั้งรายการที่เลือก", "Install the latest prerelease version": "ติดตั้งเวอร์ชันทดสอบล่าสุด", "Install updates automatically": "ติดตั้งอัปเดตโดยอัตโนมัติ", "Install {0}": null, "Installation canceled by the user!": "การติดตั้งถูกยกเลิก", "Installation failed": "การติดตั้งล้มเหลว", "Installation options": "การตั้งค่าการติดตั้ง", "Installation scope:": "ขอบเขตการติดตั้ง:", "Installation succeeded": "การติดตั้งสำเร็จ", "Installed Packages": "แพ็กเกจ​ที่ติดตั้งแล้ว", "Installed Version": "เวอร์ชันที่ติดตั้ง", "Installed packages": null, "Installer SHA256": "SHA256 ของตัวติดตั้ง", "Installer SHA512": "SHA512 ของตัวติดตั้ง", "Installer Type": "ประเภทการติดตั้ง", "Installer URL": "URL ติดตั้ง", "Installer not available": "ตัวติดตั้งไม่พร้อมใช้งาน", "Instance {0} responded, quitting...": "อินสแตนซ์ {0} ตอบกลับ กำลังออก...", "Instant search": "ค้นหาทันที", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "ติดตั้งแบบอินเตอร์แอคทีฟ", "Interactive operation": null, "Interactive uninstall": "การถอนการติดตั้งแบบอินเตอร์แอคทีฟ", "Interactive update": "การอัปเดตแบบอินเตอร์แอคทีฟ", "Internet connection settings": null, "Is this package missing the icon?": "แพ็กเกจนี้ไม่มีไอคอนหรือเปล่า?", "Is your language missing or incomplete?": "ไม่พบภาษาของคุณหรือการแปลในภาษาของคุณยังไม่สมบูรณ์ใช่ไหม", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "ไม่มีการรับประกันว่าข้อมูลส่วนตัวที่ให้ไว้จะถูกเก็บไว้อย่างปลอดภัย ดังนั้นคุณไม่ควรใช้ข้อมูลประจำตัวของบัญชีธนาคารของคุณ", "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "ดูเหมือนว่าคุณได้รัน UniGetUI ในฐานะผู้ดูแลระบบ ซึ่งไม่แนะนำ เรายังคงสามารถใช้โปรแกรมได้ แต่เราขอแนะนำให้ไม่รัน UniGetUI ด้วยสิทธิ์ผู้ดูแลระบบ คลิกที่ \"{showDetails}\" เพื่อดูรายละเอียดเพิ่มเติมว่าทำไมถึงไม่แนะนำให้รันด้วยสิทธิ์ผู้ดูแลระบบ", "Language": "ภาษา", "Language, theme and other miscellaneous preferences": "การตั้งค่าภาษา ธีม และการตั้งค่าอื่น ๆ ที่เกี่ยวข้อง", "Last updated:": "อัปเดตล่าสุด:", "Latest": "ล่าสุด", "Latest Version": "เวอร์ชันล่าสุด", "Latest Version:": "เวอร์ชันล่าสุด:", "Latest details...": "รายละเอียดล่าสุด...", "Launching subprocess...": "กำลังเริ่มกระบวนการย่อย...", "Leave empty for default": "เว้นว่างไว้เพื่อใช้ค่าเริ่มต้น", "License": "ลิขสิทธ์", "Licenses": "ลิขสิทธิ์", "Light": "สว่าง", "List": "รายการ", "Live command-line output": "เอาต์พุตบรรทัดคำสั่งแบบสด", "Live output": "เอาต์พุตสด", "Loading UI components...": "กำลังโหลด UI คอมโพเนนต์...", "Loading WingetUI...": "กำลังโหลด UniGetUI...", "Loading packages": "กำลังโหลดแพ็กเกจ", "Loading packages, please wait...": "กำลังโหลดแพ็กเกจ โปรดรอสักครู่...", "Loading...": "กำลังโหลด...", "Local": "เฉพาะที่", "Local PC": "พีซีเครื่องนี้", "Local backup advanced options": null, "Local machine": "คอมพิวเตอร์เครื่องนี้", "Local package backup": null, "Locating {pm}...": "กำลังค้นหา {pm}...", "Log in": null, "Log in failed: ": "ลงชื่อเข้าใช้ไม่สำเร็จ:", "Log in to enable cloud backup": "ลงชื่อเข้าใช้เพื่อสำรองข้อมูลบนคลาวด์", "Log in with GitHub": "ลงชื่อเข้าใช้ด้วย GitHub", "Log in with GitHub to enable cloud package backup.": "ลงชื่อเข้าใช้ด้วย GitHub เพื่อสำรองข้อมูลแพ็กเกจบนคลาวด์", "Log level:": "ระดับบันทึก:", "Log out": "ออกจากระบบ", "Log out failed: ": "ออกจากระบบไม่สำเร็จ:", "Log out from GitHub": null, "Looking for packages...": "กำลังค้นหาแพ็กเกจ...", "Machine | Global": "เครื่อง | ส่วนกลาง", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "จัดการตัวเลือกการเริ่มโดยอัตโนมัติของ UniGetUI จากแอปการตั้งค่า", "Manage ignored packages": "จัดการแพ็กเกจที่ถูกละเว้น", "Manage ignored updates": "จัดการการอัปเดตที่ถูกละเว้น", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": "จัดการ {0} แหล่งข้อมูล", "Manifest": "แมนิเฟสต์", "Manifests": "แมนิเฟสต์", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "ตัวจัดการแพ็กเกจอย่างเป็นทางการของ Microsoft ที่เต็มด้วยแพ็กเกจที่เป็นที่รู้จักกันดี และผ่านการตรวจสอบแล้ว<br>ประกอบด้วย: <b>ซอฟต์แวร์ทั่วไป, แอปจาก Microsoft Store</b>", "Missing dependency": "Dependency ขาดหายไป", "More": "เพิ่มเติม", "More details": "รายละเอียดเพิ่มเติม", "More details about the shared data and how it will be processed": "รายละเอียดเพิ่มเติมเกี่ยวกับข้อมูลที่ใช้ร่วมกันและวิธีการประมวลผล", "More info": "ข้อมูลเพิ่มเติม", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "ชื่อ", "New": "ใหม่", "New Version": "เวอร์ชันใหม่", "New bundle": "สร้างบันเดิลใหม่", "New version": "เวอร์ชันใหม่", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "ไม่", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "ไม่พบแพ็กเกจ", "No packages found matching the input criteria": "ไม่พบแพ็กเกจที่ตรงกับเกณฑ์การค้นหา", "No packages have been added yet": "ยังไม่มีการเพิ่มแพ็กเกจ", "No packages selected": "ไม่มีแพ็กเกจที่ถูกเลือก", "No packages were found": "ไม่พบแพ็กเกจ", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "ไม่มีการเก็บรวบรวมหรือส่งข้อมูลส่วนบุคคล และข้อมูลที่เก็บรวบรวมจะถูกทำให้ไม่ระบุตัวตน จึงไม่สามารถติดตามกลับไปถึงคุณได้", "No results were found matching the input criteria": "ไม่พบผลลัพธ์ที่ตรงกับเกณฑ์การค้นหา", "No sources found": "ไม่พบแหล่งข้อมูล", "No sources were found": "ไม่พบแหล่งข้อมูล", "No updates are available": "ไม่มีการอัปเดต", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "ตัวจัดการแพ็กเกจ Node JS ที่เต็มไปด้วยไลบรารีและเครื่องมืออื่น ๆ ที่เกี่ยวข้องกับ JavaScript<br>ประกอบด้วย: <b>ไลบรารี JavaScript และเครื่องมือที่เกี่ยวข้องอื่น ๆ</b>", "Not available": "ไม่มีข้อมูล", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "ไม่พบ", "Not right now": "ไม่ใช่ตอนนี้", "Notes:": "โน้ต:", "Notification preferences": "การตั้งค่าการแจ้งเตือน", "Notification tray options": "การตั้งค่าการแจ้งเตือนในถาดระบบ", "Notification types": "ประเภทการแจ้งเตือน", "NuPkg (zipped manifest)": "NuPkg (ไฟล์ Manifest ที่บีบอัด)", "OK": "โอเค", "Ok": "โอเค", "Open": "เปิด", "Open GitHub": "เปิด GitHub", "Open UniGetUI": null, "Open UniGetUI security settings": "เปิดการตั้งค่าความปลอดภัยของ UniGetUI", "Open WingetUI": "เปิด UniGetUI", "Open backup location": "เปิดตำแหน่งที่เก็บข้อมูลสำรอง", "Open existing bundle": "เปิดบันเดิลที่มีอยู่", "Open install location": null, "Open the welcome wizard": "เปิด welcome wizard", "Operation canceled by user": null, "Operation cancelled": "การดำเนินการถูกยกเลิก", "Operation history": "ประวัติการดำเนินการ", "Operation in progress": "กำลังดำเนินการ", "Operation on queue (position {0})...": "การดำเนินการอยู่ในคิว (ลำดับที่ {0})...", "Operation profile:": null, "Options saved": "การตั้งค่าถูกบันทึกเรียบร้อย", "Order by:": "เรียงลำดับโดย:", "Other": "อื่น ๆ", "Other settings": "การตั้งค่าอื่นๆ", "Package": "แพ็กเกจ", "Package Bundles": "แพ็กเกจบันเดิล", "Package ID": "แพ็กเกจ ID", "Package Manager": "ตัวจัดการแพ็กเกจ", "Package Manager logs": "บันทึกการทำงานของตัวจัดการแพ็กเกจ", "Package Managers": "ตัวจัดการแพ็กเกจ", "Package Name": "ชื่อแพ็กเกจ", "Package backup": null, "Package backup settings": "การตั้งค่าการสำรองข้อมูลแพ็กเกจ", "Package bundle": "แพ็กเกจบันเดิล", "Package details": "รายละเอียดแพ็กเกจ", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "การตั้งค่าตัวจัดการแพ็กเกจ", "Package managers": null, "Package not found": "ไม่พบแพ็กเกจ", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "แพ็กเกจ", "Packages found: {0}": "แพ็กเกจที่พบ: {0}", "Partially": null, "Password": "รหัสผ่าน", "Paste a valid URL to the database": "วาง URL ที่ถูกต้องไปยังฐานข้อมูล", "Pause updates for": null, "Perform a backup now": "สำรองข้อมูลเดี๋ยวนี้", "Perform a cloud backup now": "สำรองข้อมูลบนคลาวด์เดี๋ยวนี้", "Perform a local backup now": "สำรองข้อมูลในเครื่องเดี๋ยวนี้", "Perform integrity checks at startup": null, "Performing backup, please wait...": "กำลังสำรองข้อมูล โปรดรอสักครู่...", "Periodically perform a backup of the installed packages": "สำรองข้อมูลแพ็กเกจที่ติดตั้งแล้วเป็นระยะ", "Periodically perform a cloud backup of the installed packages": "สำรองข้อมูลแพ็กเกจที่ติดตั้งแล้วบนคลาวด์เป็นระยะ", "Periodically perform a local backup of the installed packages": "สำรองข้อมูลแพ็กเกจที่ติดตั้งแล้วในเครื่องเป็นระยะ", "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": "โปรดคลิกที่ \"ดำเนินการต่อ\" เพื่อดำเนินการต่อ", "Please enter at least 3 characters": "โปรดป้อนอย่างน้อย 3 ตัวอักษร", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "โปรดทราบว่าแพ็กเกจบางรายการอาจไม่สามารถติดตั้งได้เนื่องจากตัวจัดการแพ็กเกจที่เปิดใช้งานบนเครื่องนี้", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "โปรดทราบว่าแพ็กเกจจากแหล่งข้อมูลบางแห่งอาจไม่สามารถส่งออกได้ แพ็กเกจเหล่านี้ถูกทำให้เป็นสีเทาและจะไม่ถูกส่งออก", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "โปรดดูเอาต์พุตบรรทัดคำสั่งหรืออ้างอิงประวัติการดำเนินการสำหรับข้อมูลเพิ่มเติมเกี่ยวกับปัญหานี้", "Please select how you want to configure WingetUI": "โปรดเลือกวิธีการตั้งค่า UniGetUI ของคุณ", "Please try again later": "กรุณาลองใหม่ในภายหลัง", "Please type at least two characters": "กรุณาพิมพ์อย่างน้อยสองตัวอักษร", "Please wait": "โปรดรอ", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "กรุณารอสักครู่ขณะที่ {0} กำลังติดตั้ง หน้าต่างสีดำ (หรือสีน้ำเงิน) อาจปรากฏขึ้น กรุณารอจนกว่าจะปิดลง", "Please wait...": "กรุณารอสักครู่...", "Portable": "พกพา", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "ตัวจัดการแพ็กเกจของ PowerShell ค้นหาไลบรารีและสคริปต์เพื่อเพิ่มขีดความสามารถของ PowerShell<br>ประกอบด้วย: <b>โมดูล, สคริปต์, Cmdlets</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "ก่อนเผยแพร่", "Preparing packages, please wait...": "กำลังเตรียมแพ็กเกจ โปรดรอสักครู่...", "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "วันที่เผยแพร่:", "Publisher": "ผู้เผยแพร่", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "ตัวจัดการไลบรารีของ Python ที่เต็มไปด้วยไลบรารี Python และเครื่องมือที่เกี่ยวข้องกับ Python อื่น ๆ<br>ประกอบด้วย: <b>ไลบรารี Python และเครื่องมือที่เกี่ยวข้องกับ Python</b>", "Quit": "ออก", "Quit WingetUI": "ออกจาก UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": "ติดตั้งแพ็กเกจอีกรอบ", "Related settings": null, "Release notes": "รายละเอียดการปรับปรุง", "Release notes URL": "URL บันทึกประจำเวอร์ชัน", "Release notes URL:": "URL บันทึกการออกเวอร์ชัน:", "Release notes:": "บันทึกการออกเวอร์ชัน:", "Reload": "รีโหลด", "Reload log": "รีโหลดบันทึก", "Removal failed": "การลบล้มเหลว", "Removal succeeded": "การลบสำเร็จ", "Remove from list": "ลบออกจากรายการ", "Remove permanent data": "ลบข้อมูลถาวร", "Remove selection from bundle": "ลบรายการที่เลือกออกจากบันเดิล", "Remove successful installs/uninstalls/updates from the installation list": "ลบการติดตั้ง/การถอนการติดตั้ง/การอัปเดตที่ประสบความสำเร็จ ออกจากรายการการติดตั้ง", "Removing source {source}": null, "Removing source {source} from {manager}": "กำลังลบซอร์ซ {source} ออกจาก {manager}", "Repair UniGetUI": null, "Repair WinGet": "ซ่อมแซม WinGet", "Report an issue or submit a feature request": "รายงานปัญหาหรือส่งคำขอคุณสมบัติใหม่", "Repository": "คลังข้อมูล", "Reset": "รีเซ็ต", "Reset Scoop's global app cache": "รีเซ็ตแคชแอปส่วนกลางของ Scoop", "Reset UniGetUI": "รีเซ็ต UniGetUI", "Reset WinGet": "รีเซ็ต WinGet", "Reset Winget sources (might help if no packages are listed)": "รีเซ็ตแหล่งข้อมูลสำหรับ Winget (อาจช่วยในกรณีที่ไม่มีแพ็กเกจแสดงในรายการ)", "Reset WingetUI": "รีเซ็ต UniGetUI", "Reset WingetUI and its preferences": "รีเซ็ต UniGetUI และการตั้งค่า", "Reset WingetUI icon and screenshot cache": "รีเซ็ตแคชไอคอนและภาพสกรีนช็อตของ UniGetUI", "Reset list": "รีเซ็ตรายการ", "Resetting Winget sources - WingetUI": "กำลังรีเซ็ตแหล่งข้อมูลสำหรับ Winget - UniGetUI", "Restart": "รีสตาร์ท", "Restart UniGetUI": "รีสตาร์ท UniGetUI", "Restart WingetUI": "รีสตาร์ท UniGetUI", "Restart WingetUI to fully apply changes": "รีสตาร์ท UniGetUI เพื่อให้การเปลี่ยนแปลงทั้งหมดเป็นผลสมบูรณ์", "Restart later": "รีสตาร์ททีหลัง", "Restart now": "รีสตาร์ทเดี๋ยวนี้", "Restart required": "จำเป็นต้องรีสตาร์ท", "Restart your PC to finish installation": "รีสตาร์ทคอมพิวเตอร์ของคุณเพื่อสำเร็จกระบวนการติดตั้ง", "Restart your computer to finish the installation": "รีสตาร์ทคอมพิวเตอร์ของคุณเพื่อสำเร็จกระบวนการติดตั้ง", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "ลองใหม่อีกครั้ง", "Retry as administrator": "ทำซ้ำในฐานะผู้ดูแลระบบ", "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": "กำลังลองใหม่ โปรดรอสักครู่...", "Return to top": "กลับไปข้างบน", "Run": "เรียกใช้", "Run as admin": "รันด้วยสิทธิ์ผู้ดูแลระบบ", "Run cleanup and clear cache": "เรียกใช้การล้างข้อมูลและล้างแคช", "Run last": "ดำเนินการรายการสุดท้าย", "Run next": "ดำเนินการรายการต่อไป", "Run now": "ดำเนินการทันที", "Running the installer...": "กำลังรันตัวติดตั้ง...", "Running the uninstaller...": "กำลังรันตัวถอนการติดตั้ง...", "Running the updater...": "กำลังรันตัวอัปเดต...", "Save": "บันทึก", "Save File": "เซฟไฟล์", "Save and close": "บันทึกและปิด", "Save as": "บันทึกเป็น", "Save bundle as": "บันทึกบันเดิลเป็น", "Save now": "บันทึกเดี๋ยวนี้", "Saving packages, please wait...": "กำลังบันทึกแพ็กเกจ โปรดรอสักครู่...", "Scoop Installer - WingetUI": "ตัวติดตั้ง Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "ตัวถอนการติดตั้ง Scoop - UniGetUI", "Scoop package": "แพ็กเกจ Scoop", "Search": "ค้นหา", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "ค้นหาซอฟต์แวร์สำหรับเดสก์ท็อป แจ้งเตือนเมื่อมีอัปเดต และไม่ทำสิ่งที่ซับซ้อนเกินไป ฉันไม่อยากให้ UniGetUI มีความซับซ้อน แต่ต้องการให้ UniGetUI เป็นเพียง <b>ซอฟต์แวร์สโตร์</b> ที่ใช้งานได้ง่าย", "Search for packages": "ค้นหาแพ็กเกจ", "Search for packages to start": "ค้นหาแพ็กเกจเพื่อเริ่มต้น", "Search mode": "โหมดการค้นหา", "Search on available updates": "ค้นหาจากอัปเดต", "Search on your software": "ค้นหาจากซอฟต์แวร์ของคุณ", "Searching for installed packages...": "กำลังค้นหาแพ็กเกจที่ติดตั้งแล้ว...", "Searching for packages...": "กำลังค้นหาแพ็กเกจ...", "Searching for updates...": "กำลังค้นหาอัปเดต...", "Select": "เลือก", "Select \"{item}\" to add your custom bucket": "เลือก \"{item}\" ไปยังบักเก็ตที่คุณกำหนดเอง", "Select a folder": "เลือกโฟลเดอร์", "Select all": "เลือกทั้งหมด", "Select all packages": "เลือกแพ็กเกจทั้งหมด", "Select backup": "เลือกข้อมูลสำรอง", "Select only <b>if you know what you are doing</b>.": "เลือก<b>หากคุณรู้ว่าคุณกำลังทำอะไร</b>เท่านั้น", "Select package file": "เลือกไฟล์แพ็กเกจ", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "เลือกซอร์ซที่คุณต้องการเพิ่ม:", "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "เลือก<b>ตัวจัดการแพ็กเกจ</b>ที่ต้องการใช้ ({0}), กำหนดวิธีการติดตั้งแพ็กเกจ, จัดการวิธีการให้สิทธิ์ผู้ดูแลระบบ, และอื่น ๆ", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "ทำการส่ง handshake ไปแล้ว กำลังรอคำตอบจากอินสแตนซ์ลิสเทนเนอร์ (instance listener)... ({0}%)", "Set a custom backup file name": "ตั้งชื่อไฟล์สำรองข้อมูลเอง", "Set custom backup file name": "ตั้งชื่อไฟล์สำรองข้อมูลเอง", "Settings": "การตั้งค่า", "Share": "แชร์", "Share WingetUI": "แบ่งปัน UniGetUI", "Share anonymous usage data": null, "Share this package": "แชร์แพ็กเกจนี้", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "แสดง UniGetUI ในถาดระบบ", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "แสดง UniGetUI", "Show a notification when an installation fails": "แสดงการแจ้งเตือนเมื่อติดตั้งไม่สำเร็จ", "Show a notification when an installation finishes successfully": "แสดงการแจ้งเตือนเมื่อการติดตั้งเสร็จสมบูรณ์", "Show a notification when an operation fails": null, "Show a notification when an operation finishes successfully": null, "Show a notification when there are available updates": "แสดงการแจ้งเตือนเมื่อพบอัปเดต", "Show a silent notification when an operation is running": null, "Show details": "แสดงรายละเอียด", "Show in explorer": null, "Show info about the package on the Updates tab": "แสดงข้อมูลเกี่ยวกับแพ็กเกจบนแท็บอัปเดต", "Show missing translation strings": "แสดงสตริงการแปลที่ขาดหาย", "Show notifications on different events": "แสดงการแจ้งเตือนเกี่ยวกับเหตุการณ์ต่าง ๆ", "Show package details": "แสดงรายละเอียดแพ็กเกจ", "Show package icons on package lists": "แสดงแพ็คเกจไอค่อนในรายการแพ็คแกจ", "Show similar packages": "แสดงแพ็กเกจที่คล้ายกัน", "Show the live output": "แสดง live output", "Size": "ขนาด", "Skip": "ข้าม", "Skip hash check": "ข้ามการตรวจสอบแฮช", "Skip hash checks": null, "Skip integrity checks": "ข้ามการตรวจสอบความสมบูรณ์", "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "ข้ามการตรวจสอบแฮชเมื่อติดตั้งแพ็กเกจที่เลือก", "Skip the hash check when updating the selected packages": "ข้ามการตรวจสอบแฮชเมื่ออัปเดตแพ็กเกจที่เลือก", "Skip this version": "ข้ามเวอร์ชันนี้", "Software Updates": "การอัปเดต​ซอฟต์แวร์", "Something went wrong": "มีบางอย่างผิดพลาด", "Something went wrong while launching the updater.": null, "Source": "ซอร์ซ", "Source URL:": "URL ซอร์ซ:", "Source added successfully": null, "Source addition failed": "การเพิ่มซอร์ซล้มเหลว", "Source name:": "ชื่อซอร์ซ:", "Source removal failed": "การลบซอร์ซล้มเหลว", "Source removed successfully": null, "Source:": "ซอร์ซ:", "Sources": "แหล่งข้อมูล", "Start": "เริ่ม", "Starting daemons...": "กำลังเริ่ม daemons...", "Starting operation...": null, "Startup options": "การตั้งค่าการเริ่มโปรแกรม", "Status": "สถานะ", "Stuck here? Skip initialization": "ติดอยู่ที่นี่หรือเปล่า? ข้ามกระบวนการเริ่มต้น", "Success!": null, "Suport the developer": "สนับสนุนผู้พัฒนา", "Support me": "สนับสนุนผู้พัฒนา", "Support the developer": "สนับสนุนผู้พัฒนา", "Systems are now ready to go!": "ระบบพร้อมที่จะใช้งานแล้ว!", "Telemetry": null, "Text": "ข้อความ", "Text file": "ไฟล์ข้อความ", "Thank you ❤": "ขอบคุณ ❤", "Thank you 😉": "ขอบคุณ 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": "การสำรองข้อมูลจะไม่รวมถึงไฟล์ที่เป็นไบนารีหรือข้อมูลที่บันทึกไว้ของโปรแกรมใด ๆ", "The backup will be performed after login.": "การสำรองข้อมูลจะทำงานหลังจากเข้าสู่ระบบ", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "การสำรองข้อมูลจะรวมถึงรายการทั้งหมดของแพ็กเกจที่ติดตั้ง และการตั้งค่าการติดตั้งของแพ็กเกจนั้นทั้งหมด การอัปเดตที่ถูกละเว้น และเวอร์ชันที่ข้ามไปจะถูกบันทึกไว้ด้วย", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "ค่าแฮชของตัวติดตั้งไม่ตรงกับค่าที่คาดหวัง และไม่สามารถยืนยันความถูกต้องของตัวติดตั้งได้ หากคุณไว้วางใจผู้เผยแพร่ {0} แพ็กเกจอีกครั้งโดยข้ามการตรวจสอบแฮช", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "ตัวจัดการแพ็กเกจสุดคลาสสิกสำหรับ Windows คุณสามารถหาทุกอย่างได้ที่นี่ <br>ประกอบด้วย: <b>ซอฟต์แวร์ทั่วไป</b>", "The cloud backup completed successfully.": "สำรองข้อมูลบนคลาวด์สำเร็จ", "The cloud backup has been loaded successfully.": "โหลดข้อมูลสำรองบนคลาวด์สำเร็จ", "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "การตั้งค่าต่อไปนี้จะถูกนำไปใช้ทุกครั้งที่ติดตั้ง อัปเดต หรือลบแพ็กเกจ {0}", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "แพ็กเกจต่อไปนี้จะถูกส่งออกไปยังไฟล์ JSON โดยจะไม่มีข้อมูลผู้ใช้หรือไบนารีถูกบันทึกไว้", "The following packages are going to be installed on your system.": "แพ็กเกจต่อไปนี้จะถูกติดตั้งบนระบบของคุณ", "The following settings may pose a security risk, hence they are disabled by default.": "การตั้งค่าต่อไปนี้อาจก่อให้เกิดความเสี่ยงด้านความปลอดภัยจึงถูกปิดไว้โดยเริ่มต้น", "The following settings will be applied each time this package is installed, updated or removed.": "การตั้งค่าต่อไปนี้จะถูกนำไปใช้ทุกครั้งที่ติดตั้ง อัปเดต หรือลบแพ็กเกจนี้", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "การตั้งค่าต่อไปนี้จะถูกนำไปใช้ทุกครั้งที่แพ็กเกจนี้ถูกติดตั้ง อัปเดต หรือถูกลบ และจะถูกบันทึกโดยอัตโนมัติ", "The icons and screenshots are maintained by users like you!": "ไอคอนและสกรีนช็อตถูกดูแลโดยผู้ใช้อย่างคุณ!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "ตัวติดตั้งมีผลรวมตรวจสอบที่ไม่ถูกต้อง", "The installer hash does not match the expected value.": "แฮชของตัวติดตั้งไม่ตรงกับค่าคาดหมาย", "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "วัตถุประสงค์หลักของโปรเจกต์นี้คือการสร้าง UI ที่ใช้งานง่ายเพื่อจัดการตัวจัดการแพ็กเกจ CLI ที่ใช้กันมากที่สุดสำหรับ Windows เช่น Winget และ Scoop", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": "ตัวจัดการแพ็กเกจ \"{0}\" ถูกปิดใช้งาน", "The package manager \"{0}\" was not found": "ไม่พบตัวจัดการแพ็กเกจ \"{0}\"", "The package {0} from {1} was not found.": "ไม่พบแพ็กเกจ {0} จาก {1}", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "แพ็กเกจที่มีรายชื่อดังต่อไปนี้จะไม่ถูกตรวจสอบการอัปเดต ดับเบิลคลิกที่รายชื่อหรือคลิกที่ปุ่มทางด้านขวาของรายชื่อเพื่อยกเลิกการละเว้นการอัปเดตรายการนั้น", "The selected packages have been blacklisted": "แพ็กเกจที่เลือกได้ถูกเพิกถอนสิทธิ์ให้ใช้งานแล้ว", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "ขนาดของไฟล์สำรองคาดว่าจะน้อยกว่า 1MB", "The source {source} was added to {manager} successfully": "เพิ่มซอร์ซ {source} ไปยัง {manager} เสร็จสมบูรณ์", "The source {source} was removed from {manager} successfully": "ลบซอร์ซ {source} ออกจาก {manager} เสร็จสมบูรณ์", "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": "การอัปเดตจะได้รับการติดตั้งเมื่อปิด UniGetUI", "The update will not continue.": "การอัปเดตจะไม่ได้รับการดำเนินการต่อ", "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "มีการดำเนินงานอยู่ในขณะนี้ การออกจาก UniGetUI อาจทำให้การทำงานล้มเหลว คุณต้องการดำเนินการต่อหรือไม่", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "มีวิดีโอที่ดีบน YouTube บางส่วนที่นำเสนอ UniGetUI และความสามารถของมัน คุณสามารถเรียนรู้เทคนิคและเคล็ดลับที่มีประโยชน์ได้!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "มีเหตุผลหลักสองข้อที่ไม่ควรรัน UniGetUI เป็นผู้ดูแลระบบ: ข้อแรกคือตัวจัดการแพ็กเกจ Scoop อาจเป็นสาเหตุของปัญหาบางรายการเมื่อรันด้วยสิทธิ์ผู้ดูแลระบบ ข้อสองคือการรัน UniGetUI ในฐานะผู้ดูแลระบบหมายความว่าแพ็กเกจที่คุณดาวน์โหลดจะถูกเรียกใช้ในฐานะผู้ดูแลระบบ (และนี่ไม่ใช่วิธีการที่ปลอดภัย) โปรดจำไว้ว่าหากคุณต้องการติดตั้งแพ็กเกจเฉพาะด้วยสิทธิ์ผู้ดูแลระบบคุณสามารถคลิกขวาที่รายการ -> ติดตั้ง/อัปเดต/ถอนการติดตั้งเป็นผู้ดูแลระบบเสมอได้", "There is an error with the configuration of the package manager \"{0}\"": "เกิดข้อผิดพลาดในการกำหนดค่าของตัวจัดการแพ็กเกจ \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "มีการติดตั้งที่กำลังดำเนินการอยู่ หากคุณปิด UniGetUI การติดตั้งอาจล้มเหลว และส่งผลลัพธ์ที่ไม่คาดคิด คุณยังต้องการออกจาก UniGetUI หรือไม่?", "They are the programs in charge of installing, updating and removing packages.": "นั่นคือโปรแกรมที่รับผิดชอบในการติดตั้ง อัปเดต และถอนการติดตั้งแพ็กเกจ", "Third-party licenses": "ใบอนุญาตของบุคคลภายนอก", "This could represent a <b>security risk</b>.": "นี่อาจทำให้เกิด<b>ความเสี่ยงด้านความปลอดภัย</b>", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "น่าจะเป็นเพราะแพ็กเกจที่คุณได้รับถูกนำออกหรือเผยแพร่ในตัวจัดการแพ็กเกจที่คุณไม่ได้เปิดการใช้งาน. ID ที่ได้รับคือ {0}", "This is the <b>default choice</b>.": "นี่คือ<b>ตัวเลือกเริ่มต้น</b>", "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": "การดำเนินการนี้อาจใช้เวลาประมาณหนึ่งถึงสองนาที", "This operation is running interactively.": null, "This operation is running with administrator privileges.": "การดำเนินการนี้กำลังทำงานด้วยสิทธิ์ผู้ดูแลระบบ", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "แพ็กเกจนี้สามารถอัปเดตได้", "This package can be updated to version {0}": "แพ็กเกจนี้สามารถอัปเดตเป็นเวอร์ชัน {0} ได้", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "แพ็กเกจนี้ไม่มีภาพหน้าจอหรือขาดไอคอนใช่ไหม ขอเชิญมีส่วนร่วมกับ UniGetUI โดยการเพิ่มไอคอนและภาพหน้าจอที่ขาดหายไปในฐานข้อมูลสาธารณะแบบเปิดของเรา", "This package is already installed": "แพ็กเกจนี้ถูกติดตั้งแล้ว", "This package is being processed": "แพ็กนี้กำลังถูกประมวลผล", "This package is not available": null, "This package is on the queue": "แพ็กเกจนี้อยู่ในคิว", "This process is running with administrator privileges": "Process นี้กำลังทำงานด้วยสิทธิ์ผู้ดูแลระบบ", "This project has no connection with the official {0} project — it's completely unofficial.": "โพรเจกต์นี้ไม่มีส่วนเกี่ยวข้องกับโปรเจกต์ทางการ {0} — โพรเจกต์นี้ไม่เป็นทางการ", "This setting is disabled": "การตั้งค่านี้ถูกปิดใช้งาน", "This wizard will help you configure and customize WingetUI!": "ตัวช่วยนี้จะช่วยคุณตั้งค่า และปรับแต่ง UniGetUI!", "Toggle search filters pane": "เปิด/ปิด ตัวกรองการค้นหา", "Translators": "ผู้แปล", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "พิมพ์ชื่อและ URL ของซอร์ซที่คุณต้องการเพิ่มที่นี่ คั่นด้วยช่องว่าง", "Unable to find package": "ไม่สามารถหาแพ็กเกจได้", "Unable to load informarion": "ไม่สามารถโหลดข้อมูลได้", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI ไม่ได้มีส่วนเกี่ยวข้องกับตัวจัดการแพ็กเกจที่เข้ากันได้ใด ๆ UniGetUI เป็นโครงการอิสระ", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "ถอนการติดตั้ง", "Uninstall Scoop (and its packages)": "ถอนการติดตั้ง Scoop (และแพ็กเกจ)", "Uninstall and more": null, "Uninstall and remove data": "ถอนการติดตั้งและลบข้อมูล", "Uninstall as administrator": "ถอนการติดตั้งในฐานะผู้ดูแลระบบ", "Uninstall canceled by the user!": "การถอนการติดตั้งถูกยกเลิกโดยผู้ใช้", "Uninstall failed": "ถอนการติดตั้งล้มเหลว", "Uninstall options": null, "Uninstall package": "ถอนการติดตั้งแพ็กเกจ", "Uninstall package, then reinstall it": "ถอนการติดตั้งแพ็กเกจ แล้วติดตั้งใหม่", "Uninstall package, then update it": "ถอนการติดตั้งแพ็กเกจ แล้วทำการอัปเดต", "Uninstall previous versions when updated": null, "Uninstall selected packages": "ถอนการติดตั้งแพ็กเกจที่เลือก", "Uninstall selection": null, "Uninstall succeeded": "ถอนการติดตั้งสำเร็จ", "Uninstall the selected packages with administrator privileges": "ถอนการติดตั้งแพ็กเกจที่เลือกด้วยสิทธิ์ผู้ดูแลระบบ", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "แพ็กเกจที่ไม่สามารถถอนการติดตั้งได้ โดยมาจาก \"{0}\" ไม่ได้รับการเผยแพร่ในตัวจัดการแพ็กเกจใด ๆ ดังนั้นไม่มีข้อมูลให้แสดงเกี่ยวกับแพ็กเกจเหล่านั้น", "Unknown": "ไม่ทราบ", "Unknown size": "ไม่ทราบขนาด", "Unset or unknown": "ยังไม่ได้ตั้งค่าหรือไม่ทราบ", "Up to date": "อัพเดทล่าสุด", "Update": "อัปเดต", "Update WingetUI automatically": "อัปเดต UniGetUI โดยอัตโนมัติ", "Update all": "อัปเดตทั้งหมด", "Update and more": null, "Update as administrator": "อัปเดตในฐานะผู้ดูแลระบบ", "Update check frequency, automatically install updates, etc.": null, "Update checking": null, "Update date": "อัปเดตวันที่", "Update failed": "การอัปเดตล้มเหลว", "Update found!": "พบอัปเดต!", "Update now": "อัปเดตทันที", "Update options": null, "Update package indexes on launch": "อัปเดตดัชนีแพ็กเกจเมื่อเริ่มเปิดโปรแกรม", "Update packages automatically": "อัปเดตแพ็กเกจโดยอัตโนมัติ", "Update selected packages": "อัปเดตแพ็กเกจที่เลือก", "Update selected packages with administrator privileges": "อัปเดตแพ็กเกจที่เลือกด้วยสิทธิ์ผู้ดูแลระบบ", "Update selection": null, "Update succeeded": "อัปเดตสำเร็จแล้ว", "Update to version {0}": "อัปเดตเป็นเวอร์ชัน {0}", "Update to {0} available": "มีอัปเดตสำหรับ {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "อัปเดตไฟล์พอร์ตของ vcpkg สำหรับ Git โดยอัตโนมัติ (ต้องติดตั้ง Git แล้ว)", "Updates": "อัปเดต", "Updates available!": "มีอัปเดตใหม่!", "Updates for this package are ignored": "อัปเดตสำหรับแพ็กเกจนี้ถูกละเว้น", "Updates found!": "พบอัปเดต!", "Updates preferences": "การตั้งค่าการอัปเดต", "Updating WingetUI": "กำลังอัปเดต UniGetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "ใช้ WinGet บันเดิลแบบดั้งเดิมแทน PowerShell cmdlets", "Use a custom icon and screenshot database URL": "ใช้ URL ของฐานข้อมูลไอคอนและสกรีนช็อตที่กำหนดเอง", "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "ใช้ GSudo ที่ติดตั้งแทนที่ GSudo ที่มีให้ (ต้องรีสตาร์ทแอปพลิเคชัน)", "Use system Chocolatey": "ใช้ Chocolatey ในระบบ", "Use system Chocolatey (Needs a restart)": "ใช้ Chocolatey ของระบบ (ต้องรีสตาร์ท)", "Use system Winget (Needs a restart)": "ใช้ Winget ของระบบ (จำเป็นต้องรีสตาร์ท)", "Use system Winget (System language must be set to english)": "ใช้ Winget ในระบบ (ต้องตั้งค่าภาษาของระบบเป็นภาษาอังกฤษ)", "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": "ลิงก์ที่เป็นประโยชน์", "User": "ผู้ใช้", "User interface preferences": "การตั้งค่าอินเตอร์เฟซผู้ใช้", "User | Local": "ผู้ใช้ | เฉพาะที่", "Username": "บัญชีผู้ใช้", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "การใช้ UniGetUI ถือว่าเป็นการยอมรับสัญญาอนุญาตสาธารณะทั่วไปแบบผ่อนปรนของกนู (GNU Lesser General Public License) v2.1", "Using WingetUI implies the acceptation of the MIT License": "การใช้ UniGetUI ถือว่าเป็นการการยอมรับสัญญาอนุญาตเอ็มไอที (MIT License)", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": "ไม่พบ Vcpkg ในระบบของคุณ\n", "Verbose": "รายละเอียด", "Version": "เวอร์ชัน", "Version to install:": "เวอร์ชันที่จะทำการติดตั้ง:", "Version:": null, "View GitHub Profile": "ดูโปรไฟล์ GitHub", "View WingetUI on GitHub": "ดู UniGetUI บน GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "ดูซอร์สโค้ดของ UniGetUI จากตรงนั้นคุณสามารถรายงานบั๊กหรือแนะนำฟีเจอร์ หรือแม้แต่มีส่วนร่วมกับโปรเจกต์ UniGetUI ได้โดยตรง", "View mode:": "มุมมองการแสดงผล", "View on UniGetUI": "ดูบน UnigetUI", "View page on browser": "ดูหน้านี้บนเบราว์เซอร์", "View {0} logs": "ดูบันทึกของ {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "รอให้อุปกรณ์เชื่อมต่ออินเทอร์เน็ตก่อนที่จะพยายามทำงานที่ต้องใช้การเชื่อมต่ออินเทอร์เน็ต", "Waiting for other installations to finish...": "กำลังรอให้การติดตั้งอื่นเสร็จสิ้น", "Waiting for {0} to complete...": "กำลังรอให้ {0} เสร็จสิ้น...", "Warning": "คำเตือน", "Warning!": "คำเตือน!", "We are checking for updates.": "อยู่ระหว่างการตรวจสอบอัปเดต", "We could not load detailed information about this package, because it was not found in any of your package sources": "เราไม่สามารถโหลดข้อมูลรายละเอียดเกี่ยวกับแพ็กเกจนี้ได้ เนื่องจากไม่พบในแหล่งข้อมูลใด ๆ สำหรับแพ็กเกจของคุณ", "We could not load detailed information about this package, because it was not installed from an available package manager.": "เราไม่สามารถโหลดข้อมูลรายละเอียดเกี่ยวกับแพ็กเกจนี้ได้ เนื่องจากไม่ได้ติดตั้งจากตัวจัดการแพ็กเกจที่มีอยู่", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "เราไม่สามารถ {action} {package} ได้ในขณะนี้ โปรดลองอีกครั้งในภายหลัง คลิกที่ \"{showDetails}\" เพื่อดูบันทึกจากตัวติดตั้ง", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "เราไม่สามารถ {action} {package} ได้ในขณะนี้ โปรดลองอีกครั้งในภายหลัง คลิกที่ \"{showDetails}\" เพื่อดูบันทึกจากตัวถอนการติดตั้ง", "We couldn't find any package": "เราไม่พบแพ็กเกจใด ๆ เลย", "Welcome to WingetUI": "ยินดีต้อนรับสู่ UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "คุณต้องการใช้ตัวจัดการแพ็กเกจตัวไหนบ้าง?", "Which source do you want to add?": "คุณต้องการจะเพิ่มซอร์ซอันไหน?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "แม้ว่าจะสามารถใช้ Winget ภายใน UniGetUI ได้ แต่ UniGetUI ก็สามารถใช้ร่วมกับตัวจัดการแพ็กเกจอื่นๆ ได้ ซึ่งอาจก่อให้เกิดความสับสน เดิมที UniGetUI ได้รับการออกแบบมาเพื่อทำงานกับ Winget เท่านั้น แต่ตอนนี้ไม่ได้เป็นเช่นนั้นแล้ว ดังนั้นชื่อ WingetUI จึงไม่ได้สื่อถึงเป้าหมายของโครงการนี้", "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - ทุกอย่างอัปเดตแล้ว", "WingetUI - {0} updates are available": "UniGetUI - พบ {0} อัปเดต", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "เว็บไซต์ UniGetUI", "WingetUI Homepage - Share this link!": "เว็บไซต์ UniGetUI - แชร์ลิงก์นี้!", "WingetUI License": "ใบอนุญาต UniGetUI", "WingetUI Log": "ไฟล์บันทึก UniGetUI", "WingetUI Repository": "คลังข้อมูล UniGetUI", "WingetUI Settings": "การตั้งค่า UniGetUI", "WingetUI Settings File": "ไฟล์ตั้งค่า UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI ใช้ไลบรารีดังต่อไปนี้ UniGetUI คงไม่อาจเกิดขึ้นได้หากไม่มีไลบรารีเหล่านี้", "WingetUI Version {0}": "UniGetUI เวอร์ชัน {0}", "WingetUI autostart behaviour, application launch settings": "การเปิดทำงานโดยอัตโนมัติของ UniGetUI และการตั้งค่าการเปิดแอปพลิเคชัน", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI สามารถตรวจสอบว่าซอฟต์แวร์ของคุณมีการอัปเดตหรือไม่ และติดตั้งอัปเดตโดยอัตโนมัติหากคุณต้องการ", "WingetUI display language:": "ภาษาที่แสดงใน UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI ถูกเรียกใช้ในฐานะผู้ดูแลระบบ ซึ่งไม่แนะนำให้ทำ เมื่อเรียกใช้ UniGetUI ในฐานะผู้ดูแลระบบ ทุกการดำเนินการที่เริ่มจาก UniGetUI จะได้รับสิทธิ์ผู้ดูแลระบบ คุณยังคงสามารถใช้โปรแกรมนี้ได้ แต่เราขอแนะนำอย่างยิ่งว่าอย่าใช้งาน UniGetUI ด้วยสิทธิ์ผู้ดูแลระบบ", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI ได้รับการแปลเป็นภาษาต่าง ๆ มากกว่า 40 ภาษา ต้องขอบคุณอาสาสมัครนักแปลทุกท่าน ขอบคุณ🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI ไม่ได้ถูกแปลด้วยเครื่องมือแปลภาษา แต่ถูกแปลโดย:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI เป็นแอปพลิเคชันที่ทำให้การจัดการซอฟต์แวร์ของคุณง่ายขึ้น โดยมอบอินเทอร์เฟซผู้ใช้แบบกราฟิกที่ครอบคลุมสำหรับตัวจัดการแพ็กเกจบรรทัดคำสั่งของคุณ", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI จะถูกเปลี่ยนชื่อเพื่อสร้างความแตกต่างอย่างชัดเจนระหว่าง UniGetUI (อินเทอร์เฟซที่คุณใช้อยู่ตอนนี้) และ WinGet (ตัวจัดการแพ็กเกจที่พัฒนาโดย Microsoft ซึ่งผมไม่ได้มีส่วนเกี่ยวข้อง)", "WingetUI is being updated. When finished, WingetUI will restart itself": "กำลังอัปเดต UniGetUI เมื่ออัปเดตเสร็จสิ้น UniGetUI จะรีสตาร์ทตัวเอง", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI นั้นใช้งานได้ฟรี และจะฟรีตลอดไป ไม่มีโฆษณา ไม่ต้องใช้บัตรเครดิต ไม่มีเวอร์ชันพรีเมียม ฟรี 100% ตลอดไป", "WingetUI log": "บันทึก UniGetUI", "WingetUI tray application preferences": "การตั้งค่าถาดระบบของ UniGetUI ", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI ใช้ไลบรารีดังต่อไปนี้ UniGetUI คงไม่อาจเกิดขึ้นได้หากไม่มีไลบรารีเหล่านี้", "WingetUI version {0} is being downloaded.": "กำลังดาวน์โหลด UniGetUI เวอร์ชัน {0}", "WingetUI will become {newname} soon!": "WingetUI จะเปลี่ยนเป็น {newname} ในเร็ว ๆ นี้!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI จะไม่ตรวจสอบอัปเดตเป็นระยะ แต่จะยังคงตรวจสอบเมื่อเปิดใช้งานโปรแกรม โดยคุณจะไม่ได้รับการแจ้งเตือนเกี่ยวกับมัน", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI จะแสดงหน้าต่าง UAC ทุกครั้งเมื่อแพ็กเกจต้องการยกระดับสิทธิ์เพื่อติดตั้ง", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI จะได้รับการเปลี่ยนชื่อเป็น {newname} ในเร็วๆ นี้ ซึ่งไม่ได้สะท้อนถึงการเปลี่ยนแปลงใด ๆ ในแอปพลิเคชัน โดยผม (ผู้พัฒนา) จะดำเนินการพัฒนาโครงการนี้ต่อไปดังเช่นที่ทำอยู่ในตอนนี้ แต่ภายใต้ชื่ออื่น", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI จะเกิดขึ้นไม่ได้เลยหากไม่ได้รับความช่วยเหลือจากผู้มีส่วนช่วย ดู GitHub ของพวกเขาดูสิ UniGetUI จะเกิดขึ้นไม่ได้หากไม่มีพวกเขา!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI คงไม่เกิดขึ้นหากไม่ได้รับความช่วยเหลือจากผู้ที่มีส่วนช่วย ขอบคุณทุกท่าน 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} พร้อมสำหรับการติดตั้งแล้ว", "Write here the process names here, separated by commas (,)": null, "Yes": "ใช่", "You are logged in as {0} (@{1})": "ลงชื่อเข้าใช้ในชื่อ {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "คุณสามารถเปลี่ยนพฤติกรรมนี้ได้ที่การตั้งค่าความปลอดภัยของ UniGetUI", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "ขณะนี้คุณได้ติดตั้งเวอร์ชัน {0} แล้ว", "You have installed WingetUI Version {0}": "คุณได้ติดตั้ง UniGetUI เวอร์ชัน {0} แล้ว", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "คุณจำเป็นต้องติดตั้ง {pm} เพื่อใช้งานร่วมกับ UniGetUI", "You may restart your computer later if you wish": "คุณสามารถรีสตาร์ทคอมพิวเตอร์ของคุณในภายหลังหากคุณต้องการ", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "คุณจะได้รับคำขอสิทธิ์ผู้ดูแลระบบเพียงครั้งเดียวเท่านั้น และสิทธิ์ผู้ดูแลระบบจะถูกมอบให้แก่แพ็กเกจที่ขอใช้งานสิทธิ์นั้น", "You will be prompted only once, and every future installation will be elevated automatically.": "คุณจะได้รับคำขอสิทธิ์ผู้ดูแลระบบเพียงครั้งเดียวเท่านั้น และการติดตั้งในอนาคตทุก ๆ ครั้งจะถูกยกระดับสิทธิ์โดยอัตโนมัติ", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": "เรียกใช้ในฐานะผู้ดูแลระบบ", "buy me a coffee": "เลี้ยงขนมเราหน่อย", "extracted": "แตกไฟล์แล้ว", "feature": null, "formerly WingetUI": "เดิมชื่อ WingetUI", "homepage": "เว็บไซต์", "install": "ติดตั้ง", "installation": "การติดตั้ง", "installed": "ติดตั้งสำเร็จ", "installing": "กำลังติดตั้ง", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "ถอนการติดตั้ง", "uninstallation": "ถอนการติดตั้ง", "uninstalled": "ถอนการติดตั้ง", "uninstalling": "กำลังถอนการติดตั้ง", "update(noun)": "อัปเดต", "update(verb)": "อัปเดต", "updated": "อัปเดตแล้ว", "updating": "กำลังอัปเดต", "version {0}": "เวอร์ชัน {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} การถอนการติดตั้ง", "{0} aborted": "{0} ยกเลิก", "{0} can be updated": "{0} สามารถอัปเดตได้", "{0} can be updated to version {1}": "{0} สามารถอัปเดตเป็นเวอร์ชัน {1}", "{0} days": "{0} วัน", "{0} desktop shortcuts created": "{0} ทางลัดบนเดสก์ท็อป ถูกสร้างแล้ว\n", "{0} failed": "{0} ล้มเหลว", "{0} has been installed successfully.": "{0} ถูกติดตั้งสำเร็จ", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "ติดตั้ง {0} สำเร็จแล้ว แนะนำให้รีสตาร์ท UniGetUI เพื่อให้การติดตั้งเสร็จสมบูรณ์", "{0} has failed, that was a requirement for {1} to be run": "{0} ล้มเหลว ซึ่งเป็นข้อกำหนดที่จำเป็นสำหรับการรัน {1}\n\n", "{0} homepage": "เว็บไซต์ {0}", "{0} hours": "{0} ชั่วโมง", "{0} installation": "{0} การติดตั้ง", "{0} installation options": "{0} ตัวเลือกการติดตั้ง", "{0} installer is being downloaded": "กำลังดาวน์โหลดตัวติดตั้ง {0}", "{0} is being installed": "กำลังติดตั้ง {0}", "{0} is being uninstalled": "กำลังถอนการติดตั้ง {0}", "{0} is being updated": "{0} กำลังถูกอัปเดต", "{0} is being updated to version {1}": "{0} กำลังอัปเดตเป็นเวอร์ชัน {1}", "{0} is disabled": "{0} ถูกปิดใช้งานอยู่", "{0} minutes": "{0} นาที", "{0} months": "{0} เดือน", "{0} packages are being updated": "{0} แพ็กเกจกำลังถูกอัปเดต", "{0} packages can be updated": "{0} แพ็กเกจสามารถอัปเดตได้", "{0} packages found": "พบ {0} แพ็กเกจ", "{0} packages were found": "พบ {0} แพ็กเกจ", "{0} packages were found, {1} of which match the specified filters.": "พบแพ็กเกจ {0} รายการ โดยแพ็กเกจ {1} รายการตรงกับตัวกรองที่ระบุไว้", "{0} selected": null, "{0} settings": "{0} การตั้งค่า", "{0} status": "{0} สถานะ", "{0} succeeded": "{0} สำเร็จ", "{0} update": "อัปเดต {0}", "{0} updates are available": "มีการอัปเดตที่พร้อมใช้งาน {0} รายการ", "{0} was {1} successfully!": "{1} {0} เสร็จสมบูรณ์", "{0} weeks": "{0} สัปดาห์", "{0} years": "{0} ปี", "{0} {1} failed": "{0} {1} ล้มเหลว", "{package} Installation": "การติดตั้ง {package}", "{package} Uninstall": "การถอนการติดตั้ง {package}", "{package} Update": "อัปเดต {package}", "{package} could not be installed": "ไม่สามารถติดตั้ง {package} ได้", "{package} could not be uninstalled": "ไม่สามารถถอนการติดตั้ง {package} ได้", "{package} could not be updated": "ไม่สามารถอัปเดต {package} ได้", "{package} installation failed": "การติดตั้ง {package} ล้มเหลว", "{package} installer could not be downloaded": "ไม่สามารถดาวน์โหลดตัวติดตั้ง {package} ได้", "{package} installer download": "ดาวน์โหลดตัวติดตั้ง {package}", "{package} installer was downloaded successfully": "ดาวน์โหลดตัวติดตั้ง {package} สำเร็จแล้ว", "{package} uninstall failed": "การถอนการติดตั้ง {package} ล้มเหลว", "{package} update failed": "การอัปเดต {package} ล้มเหลว", "{package} update failed. Click here for more details.": "การอัปเดต {package} ล้มเหลว คลิกที่นี่เพื่อดูรายละเอียดเพิ่มเติม", "{package} was installed successfully": "ติดตั้ง {package} เสร็จสมบูรณ์", "{package} was uninstalled successfully": "ถอนการติดตั้ง {package} เสร็จสมบูรณ์", "{package} was updated successfully": "การอัปเดต {package} เสร็จสมบูรณ์", "{pcName} installed packages": "แพ็กเกจที่ติดตั้งใน {pcName} แล้ว", "{pm} could not be found": "ไม่พบ {pm}", "{pm} found: {state}": "{pm} พบ: {state}", "{pm} is disabled": "{pm} ถูกปิดใช้งาน", "{pm} is enabled and ready to go": "{pm} ถูกเปิดใช้งานและพร้อมใช้งานแล้ว", "{pm} package manager specific preferences": "การตั้งค่าเฉพาะของตัวจัดการแพ็กเกจ {pm}", "{pm} preferences": "การตั้งค่า {pm}", "{pm} version:": "{pm} เวอร์ชัน:", "{pm} was not found!": "ไม่พบ {pm}!"}