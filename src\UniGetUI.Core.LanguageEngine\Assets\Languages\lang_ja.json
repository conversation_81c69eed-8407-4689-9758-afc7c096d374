{"\"{0}\" is a local package and can't be shared": "「{0}」はローカル パッケージなので共有できません\n", "\"{0}\" is a local package and does not have available details": "「{0}」はローカル パッケージであり、利用可能な詳細がありません\n", "\"{0}\" is a local package and is not compatible with this feature": "「{0}」はローカル パッケージであり、この機能と互換性がありません\n", "(Last checked: {0})": "(最終確認日時: {0})", "(Number {0} in the queue)": "（キューの {0} 番目）", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "sho9029, <PERSON><PERSON>, @nob-swik, @tacostea, @an<PERSON><PERSON>, <PERSON><PERSON><PERSON>, @BHCrusher1", "0 packages found": "パッケージが見つかりませんでした", "0 updates found": "アップデートが見つかりませんでした", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 日", "1 hour": "1 時間", "1 month": "1ヶ月 ", "1 package was found": "1個のパッケージが見つかりました", "1 update is available": "1つのアップデートが利用可能です", "1 week": "1 週間", "1 year": "1年", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. \"{0}\"または\"{1}\"ページに移動します。", "2 - Warnings": "2 - 警告", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. バンドルに追加するパッケージを探して、左端のチェックボックスをオンにします。", "3 - Information (less)": "3 - 情報 (概要)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. バンドルに追加するパッケージを選択したら、ツールバーでオプション\n    「{0}」を見つけてクリックします。 ", "4 - Information (more)": "4 - 情報 (詳細)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. パッケージがバンドルに追加されます。\n    パッケージの追加を続けるか、バンドルをエクスポートしてください。 ", "5 - information (debug)": "5 - 情報 (デバッグ)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "人気のある C/C++ のライブラリ マネージャー。C/C++ ライブラリとその他の C/C++ 関連のユーティリティーが満載。<br>含まれるもの: <b>C/C++ ライブラリと関連ユーティリティー</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Microsoftの .NET 環境を念頭にデザインされたツール・実行ファイルが満載のリポジトリ。<br>含まれるもの：<b>.NET 関連のツールとスクリプト</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Microsoftの .NET 環境を念頭にデザインされたツールが満載のリポジトリ。<br>含まれるもの：<b>.NET 関連のツール</b>", "A restart is required": "再起動が必要", "Abort install if pre-install command fails": "インストール前コマンドが失敗した場合はインストールを中止する ", "Abort uninstall if pre-uninstall command fails": "アンインストール前のコマンドが失敗した場合はアンインストールを中止する ", "Abort update if pre-update command fails": "アップデート前コマンドが失敗した場合は更新を中止する ", "About": "情報", "About Qt6": "Qt6 について", "About WingetUI": "UniGetUI について", "About WingetUI version {0}": "UniGetUI バージョン {0} について", "About the dev": "開発者について", "Accept": "許可", "Action when double-clicking packages, hide successful installations": "パッケージをダブルクリックした時に、インストール成功したパッケージを非表示にする", "Add": "追加", "Add a source to {0}": "{0} へのソース追加", "Add a timestamp to the backup file names": "バックアップファイル名にタイムスタンプを追加する", "Add a timestamp to the backup files": "バックアップファイルにタイムスタンプを追加する", "Add packages or open an existing bundle": "パッケージを追加するか既存のバンドルを開いてください", "Add packages or open an existing package bundle": "パッケージを追加するか、既存のパッケージバンドルを開きます", "Add packages to bundle": "バンドルにパッケージを追加する ", "Add packages to start": "パッケージを追加してください", "Add selection to bundle": "選択したものをバンドルに追加", "Add source": "ソースを追加", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "「適用可能な更新が見つかりません」というエラーで失敗した更新を無視された更新リストに追加します。 ", "Adding source {source}": "ソースを追加 {source}", "Adding source {source} to {manager}": "ソース {source} を {manager} に追加しています", "Addition succeeded": "追加成功", "Administrator privileges": "管理者権限", "Administrator privileges preferences": "管理者権限設定", "Administrator rights": "管理者権限", "Administrator rights and other dangerous settings": "管理者権限やその他の危険な設定", "Advanced options": "詳細オプション ", "All files": "すべてのファイル", "All versions": "すべてのバージョン", "Allow changing the paths for package manager executables": "パッケージマネージャー実行ファイルのパスを変更を許可する", "Allow custom command-line arguments": "カスタム コマンドライン引数を許可する", "Allow importing custom command-line arguments when importing packages from a bundle": "バンドルからパッケージをインポートする際に、カスタム コマンドライン引数のインポートを許可する", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "バンドルからパッケージをインポートする際に、インストール前およびインストール後のカスタムコマンドのインポートを許可する。", "Allow package operations to be performed in parallel": "パッケージ操作を並列実行する", "Allow parallel installs (NOT RECOMMENDED)": "並列インストールを許可する (非推奨)", "Allow pre-release versions": "プレリリース版を許可する ", "Allow {pm} operations to be performed in parallel": "{pm} の並列動作を許可する", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "あるいは、Windows PowerShellプロンプトで以下のコマンドを実行して {0} をインストールすることもできます。", "Always elevate {pm} installations by default": "デフォルトで {pm} のインストールを昇格させる", "Always run {pm} operations with administrator rights": "{pm} の操作を常に管理者権限付きで実行する", "An error occurred": "エラーが発生しました", "An error occurred when adding the source: ": "ソースの追加時にエラーが発生しました:", "An error occurred when attempting to show the package with Id {0}": "ID {0} のパッケージを表示しようとしたときにエラーが発生しました ", "An error occurred when checking for updates: ": "アップデートの確認時にエラーが発生しました:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "バックアップの読み込み中にエラーが発生しました: ", "An error occurred while logging in: ": "ログイン中にエラーが発生しました: ", "An error occurred while processing this package": "パッケージの処理中にエラーが発生しました", "An error occurred:": "エラーが発生しました:", "An interal error occurred. Please view the log for further details.": "内部エラーが発生しました。詳細についてはログを確認してください。", "An unexpected error occurred:": "予期しないエラーが発生しました:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "WinGet の修復操作中に予期しないエラーが発生しました。後で再試行してください。", "An update was found!": "アップデートが見つかりました！", "Android Subsystem": "Android サブシステム", "Another source": "別のソース", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "インストールまたは更新操作中に作成された新しいショートカットは、初回検出時の確認プロンプトを表示せず、自動的に削除されます。 ", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "UniGetUI の外部で作成または変更されたショートカットは無視されます。{0} ボタンから追加できます。 ", "Any unsaved changes will be lost": "保存されていないすべての変更は失われます", "App Name": "アプリ名", "Appearance": "インタフェース", "Application theme, startup page, package icons, clear successful installs automatically": "アプリケーションテーマ、スタートアップページ、パッケージアイコン、成功したインストールを自動的にクリアします ", "Application theme:": "アプリテーマ:", "Apply": "適用する ", "Architecture to install:": "インストールするアーキテクチャ:", "Are these screenshots wron or blurry?": "このスクリーンショットは間違っているかぼやけていますか？", "Are you really sure you want to enable this feature?": "この機能を有効にしてもよろしいですか? ", "Are you sure you want to create a new package bundle? ": "新しいパッケージバンドルを作成しますか？", "Are you sure you want to delete all shortcuts?": "すべてのショートカットを削除してもよろしいですか? ", "Are you sure?": "よろしいですか？", "Ascendant": "昇順", "Ask for administrator privileges once for each batch of operations": "操作バッチごとに一度だけ管理者権限を求める", "Ask for administrator rights when required": "必要な場合には管理者権限を要求する", "Ask once or always for administrator rights, elevate installations by default": "管理者権限を一度だけ、または毎回要求し、デフォルトでインストールを昇格させる", "Ask only once for administrator privileges": "管理者権限を一度だけ要求する ", "Ask only once for administrator privileges (not recommended)": "管理者権限を一度だけ要求する（推奨されません） ", "Ask to delete desktop shortcuts created during an install or upgrade.": "インストール、アップグレード時に作成されるデスクトップショートカットを削除するか確認する", "Attention required": "注意が必要", "Authenticate to the proxy with an user and a password": "ユーザー名とパスワードでプロキシを認証する ", "Author": "作者", "Automatic desktop shortcut remover": "デスクトップ ショートカットの自動削除", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "インストール済みパッケージの一覧を自動的に保存し、簡単に復元できるようにします。", "Automatically save a list of your installed packages on your computer.": "インストール済みパッケージの一覧を自動的にコンピューターに保存する", "Autostart WingetUI in the notifications area": "WingetUIを通知領域で自動起動する", "Available Updates": "ソフトウェアアップデート", "Available updates: {0}": "利用可能なアップデート: {0}", "Available updates: {0}, not finished yet...": "利用可能なアップデート: {0}、進行中...", "Backing up packages to GitHub Gist...": "パッケージを GitHub Gist にバックアップしています... ", "Backup": "バックアップ", "Backup Failed": "バックアップに失敗しました ", "Backup Successful": "バックアップ成功 ", "Backup and Restore": "バックアップと復元 ", "Backup installed packages": "インストール済みパッケージのバックアップ", "Backup location": "バックアップの場所 ", "Become a contributor": "貢献者になる", "Become a translator": "翻訳者になる", "Begin the process to select a cloud backup and review which packages to restore": "クラウドバックアップを選択し、復元するパッケージを確認するプロセスを開始します ", "Beta features and other options that shouldn't be touched": "ベータ版機能やその他の触れないほうが良いオプション", "Both": "両方", "Bundle security report": "バンドルセキュリティレポート ", "But here are other things you can do to learn about WingetUI even more:": "しかし、UniGetUI についてさらに知るために、他にもできることがあります:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "パッケージマネージャーのチェックを外した場合、パッケージの内容やアップデートが見えなくなります。", "Cache administrator rights and elevate installers by default": "管理者権限をキャッシュし、インストーラーをデフォルトで昇格させる", "Cache administrator rights, but elevate installers only when required": "管理者権限をキャッシュし、必要なときだけインストーラーを昇格させる", "Cache was reset successfully!": "キャッシュのリセットが完了しました！", "Can't {0} {1}": "{1} を {0} できませんでした", "Cancel": "キャンセル", "Cancel all operations": "すべての操作をキャンセル", "Change backup output directory": "バックアップの保存先を変更する", "Change default options": "デフォルトオプションを変更する ", "Change how UniGetUI checks and installs available updates for your packages": "UniGetUIが利用可能なパッケージのアップデートを確認してインストールする方法の変更", "Change how UniGetUI handles install, update and uninstall operations.": "UniGetUI がインストール、アップデート、アンインストール操作を処理する方法を変更します。 ", "Change how UniGetUI installs packages, and checks and installs available updates": "UniGetUI がパッケージをインストールし、利用可能なアップデートをチェックしてインストールする方法を変更する ", "Change how operations request administrator rights": "管理者権限を要求する操作方法を変更する ", "Change install location": "インストール先を変更", "Change this": "これを変更する ", "Change this and unlock": "これを変更してロックを解除 ", "Check for package updates periodically": "定期的にパッケージのアップデートを確認する", "Check for updates": "アップデートの確認", "Check for updates every:": "アップデートの確認間隔:", "Check for updates periodically": "定期的にアップデートを確認する", "Check for updates regularly, and ask me what to do when updates are found.": "定期的にアップデートを確認し、アップデートが見つかった場合にどうするかを確認します。", "Check for updates regularly, and automatically install available ones.": "定期的にアップデートを確認し、利用可能なものは自動的にインストールします。", "Check out my {0} and my {1}!": "私の {0} と {1} を確認してください！", "Check out some WingetUI overviews": "UniGetUI のレビューを確認する", "Checking for other running instances...": "他の実行中のインスタンスを確認中...", "Checking for updates...": "アップデートの確認中...", "Checking found instace(s)...": "見つかったインスタンスを確認中...", "Choose how many operations shouls be performed in parallel": "並列に実行する数を選択する ", "Clear cache": "キャッシュのクリア", "Clear finished operations": "完了した操作をクリアする ", "Clear selection": "選択を解除", "Clear successful operations": "成功した操作を削除", "Clear successful operations from the operation list after a 5 second delay": "操作リストから成功した操作を 5 秒後に削除する", "Clear the local icon cache": "ローカルアイコンキャッシュをクリアする", "Clearing Scoop cache - WingetUI": "Scoop のキャッシュ消去 - UniGetUI", "Clearing Scoop cache...": "Scoop のキャッシュをクリアしています...", "Click here for more details": "詳細についてはここをクリックしてください ", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "インストール処理を開始するには [インストール] をクリックします。インストールをスキップする場合は、UniGetUI が想定通りに動作しない可能性があります。", "Close": "閉じる", "Close UniGetUI to the system tray": "UniGetUIをシステムトレイに閉じます ", "Close WingetUI to the notification area": "UniGetUI を閉じる時に通知領域へ格納する", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "クラウドバックアップでは、プライベートなGitHub Gistを使用して、インストールされているパッケージのリストを保存します ", "Cloud package backup": "クラウドパッケージのバックアップ ", "Command-line Output": "コマンドライン出力", "Command-line to run:": "実行するコマンドライン: ", "Compare query against": "検索クエリの比較対象", "Compatible with authentication": "認証に対応 ", "Compatible with proxy": "プロキシと互換性あり ", "Component Information": "コンポーネントの情報", "Concurrency and execution": "並行処理と実行", "Connect the internet using a custom proxy": "カスタムプロキシを使用してインターネットに接続する ", "Continue": "続ける", "Contribute to the icon and screenshot repository": "アイコンとスクリーンショットのリポジトリに貢献する", "Contributors": "貢献者", "Copy": "コピー", "Copy to clipboard": "クリップボードにコピー", "Could not add source": "ソースを追加できませんでした ", "Could not add source {source} to {manager}": "{source} を {manager} に追加できませんでした", "Could not back up packages to GitHub Gist: ": "パッケージを GitHub Gist にバックアップできませんでした: ", "Could not create bundle": "bundle を作成できませんでした", "Could not load announcements - ": "お知らせを読み込めませんでした - ", "Could not load announcements - HTTP status code is $CODE": "お知らせを読み込めませんでした - HTTPステータスコード $CODE", "Could not remove source": "ソースを削除できませんでした ", "Could not remove source {source} from {manager}": "{manager} からソース {source} を削除できませんでした", "Could not remove {source} from {manager}": "{manager} から {source} を削除できませんでした", "Create .ps1 script": null, "Credentials": "資格情報", "Current Version": "現在のバージョン", "Current status: Not logged in": "現在のステータス: ログインしていません ", "Current user": "現在のユーザー", "Custom arguments:": "カスタム引数: ", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "カスタム コマンドライン引数によって、プログラムのインストール、アップグレード、アンインストールの方法が UniGetUI では制御できない形で変更されることがあります。カスタム コマンドライン引数を使用するとパッケージが破損する可能性があります。慎重に操作してください。", "Custom command-line arguments:": "カスタム コマンド ライン引数:", "Custom install arguments:": "カスタムインストール引数: ", "Custom uninstall arguments:": "カスタムアンインストール引数: ", "Custom update arguments:": "カスタムアップデート引数: ", "Customize WingetUI - for hackers and advanced users only": "UniGetUI をカスタマイズする - ハッカーや上級者向け", "DEBUG BUILD": "デバッグビルド", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "免責事項: ダウンロードされたパッケージについて、我々は責任を負いません。信頼できるソフトウェアのみをインストールするようにしてください。", "Dark": "ダークテーマ", "Decline": "拒否", "Default": "デフォルト", "Default installation options for {0} packages": "{0} パッケージのデフォルトのインストール オプション\n", "Default preferences - suitable for regular users": "デフォルト設定 - 通常のユーザーに最適", "Default vcpkg triplet": "デフォルトのvcpkgトリプレット ", "Delete?": "消去しますか？ ", "Dependencies:": "依存関係:", "Descendant": "降順", "Description:": "説明:", "Desktop shortcut created": "デスクトップ ショートカットを作成しました", "Details of the report:": "レポートの詳細: ", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "開発は大変ですが、このアプリケーションは無料です。でも、もしこのアプリケーションが気に入ったなら、いつでも<b>私にコーヒーをおごっていいんですよ</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "\"{discoveryTab}\" タブで項目をダブルクリックすると、(パッケージ情報を表示する代わりに) 直接インストールします", "Disable new share API (port 7058)": "新しい共有API (ポート7058) を無効化する", "Disable the 1-minute timeout for package-related operations": "パッケージ関連の操作における1分間のタイムアウトを無効にする", "Disclaimer": "免責事項", "Discover Packages": "パッケージを探す", "Discover packages": "パッケージを探す", "Distinguish between\nuppercase and lowercase": "大文字と小文字を区別", "Distinguish between uppercase and lowercase": "大文字と小文字を区別", "Do NOT check for updates": "アップデートを確認しない", "Do an interactive install for the selected packages": "選択したパッケージの対話型インストールをおこなう", "Do an interactive uninstall for the selected packages": "選択したパッケージの対話型アンインストールを行う", "Do an interactive update for the selected packages": "選択したパッケージの対話型アップデートを行う", "Do not automatically install updates when the battery saver is on": "バッテリーセーバーがオンのときにアップデートを自動的にインストールしない ", "Do not automatically install updates when the network connection is metered": "ネットワーク接続が従量制の場合は、自動的にアップデートをインストールしない ", "Do not download new app translations from GitHub automatically": "G<PERSON><PERSON>ubから新しいアプリの翻訳を自動的にダウンロードしない", "Do not ignore updates for this package anymore": "このパッケージのアップデートを無視する", "Do not remove successful operations from the list automatically": "成功した操作をリストから自動的に削除しない", "Do not show this dialog again for {0}": "今後 {0} のダイアログを再表示しない", "Do not update package indexes on launch": "起動時にパッケージのインデックスを更新しない", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "UniGetUIが、ユーザーエクスペリエンス向上のため、匿名の利用統計を収集・送信することに同意しますか？", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "UniGetUI はお役に立っていますか？ 私が究極のパッケージ管理インターフェースを目指して \nUniGetUI を継続的に開発できるよう、もし可能であれば私の仕事をサポートしていただければ幸いです。", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "UniGetUI は便利ですか？開発者を応援したいと思いますか？もしそうなら、{0} していただければとても助かります！", "Do you really want to reset this list? This action cannot be reverted.": "このリストを本当にリセットしますか? この操作は元に戻せません。 ", "Do you really want to uninstall the following {0} packages?": "以下の {0} 個のパッケージを本当にアンインストールしますか？", "Do you really want to uninstall {0} packages?": "本当に {0} 個のパッケージをアンインストールしますか？", "Do you really want to uninstall {0}?": "本当に {0} をアンインストールしますか？", "Do you want to restart your computer now?": "PC を今すぐ再起動させますか？", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "UniGetUI をあなたの言語に翻訳してみませんか？翻訳への貢献方法については<a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>をご覧ください！\n", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "寄付には気が乗りませんか？ ご心配なく。いつでも UniGetUIを知り合いに紹介することはできます。\nUniGetUI についてヒトコト拡散するだけです。", "Donate": "寄付する", "Done!": "終了!", "Download failed": "ダウンロードに失敗しました ", "Download installer": "インストーラーをダウンロード", "Download operations are not affected by this setting": "ダウンロード操作はこの設定の影響を受けません ", "Download selected installers": "選択したインストーラをダウンロードする ", "Download succeeded": "ダウンロード成功", "Download updated language files from GitHub automatically": "最新の言語ファイルをGitHubから自動的にダウンロードする", "Downloading": "ダウンロード中", "Downloading backup...": "バックアップをダウンロードしています... ", "Downloading installer for {package}": "{package} のインストーラーをダウンロード中", "Downloading package metadata...": "パッケージのメタデータをダウンロード中...", "Enable Scoop cleanup on launch": "起動時にScoopのクリーンアップを有効にする", "Enable WingetUI notifications": "UniGetUI の通知を有効にする", "Enable an [experimental] improved WinGet troubleshooter": "[試験的] 改善された WinGet トラブルシューティング ツールを有効にする ", "Enable and disable package managers, change default install options, etc.": "パッケージ マネージャーを有効または無効にしたり、デフォルトのインストール オプションを変更したりします。 ", "Enable background CPU Usage optimizations (see Pull Request #3278)": "バックグラウンド CPU 使用率の最適化を有効にする (プルリクエスト #3278 を参照) ", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "バックグラウンドAPIを有効にする（WingetUI Widgetsと共有機能用、7058番ポート）", "Enable it to install packages from {pm}.": "パッケージを {pm} からインストールするには有効にしてください", "Enable the automatic WinGet troubleshooter": "自動WinGetトラブルシューティングツールを有効にする ", "Enable the new UniGetUI-Branded UAC Elevator": "新しい UniGetUI ブランドの UAC エレベーターを有効にする ", "Enable the new process input handler (StdIn automated closer)": "新しいプロセス入力ハンドラーを有効にする（StdIn 自動クローズ） ", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "以下の設定は、その機能と、それに伴う影響や危険性を完全に理解している場合に限り、有効にしてください。 ", "Enable {pm}": "{pm} を有効にする", "Enter proxy URL here": "ここにプロキシURLを入力してください ", "Entries that show in RED will be IMPORTED.": "赤で表示されるエントリはインポートされます。 ", "Entries that show in YELLOW will be IGNORED.": "黄色で表示されるエントリは無視されます。 ", "Error": "エラー", "Everything is up to date": "すべて最新", "Exact match": "完全一致", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "デスクトップ上の既存のショートカットがスキャンされ、保持するショートカットと削除するショートカットを選択する必要があります。 ", "Expand version": "バージョンを表示", "Experimental settings and developer options": "試験的な設定と開発者オプション", "Export": "エクスポート", "Export log as a file": "ログをファイルとしてエクスポートする", "Export packages": "パッケージをエクスポートする", "Export selected packages to a file": "選択したパッケージをファイルとしてエクスポートする", "Export settings to a local file": "設定をローカルファイルにエクスポート", "Export to a file": "ファイルにエクスポート", "Failed": "失敗しました", "Fetching available backups...": "利用可能なバックアップを取得しています... ", "Fetching latest announcements, please wait...": "最新のお知らせを取得中です。お待ち下さい…", "Filters": "フィルター", "Finish": "完了しました", "Follow system color scheme": "システムのカラーモードに従う", "Follow the default options when installing, upgrading or uninstalling this package": "このパッケージをインストール、アップグレード、またはアンインストールするときは、デフォルトのオプションに従ってください。 ", "For security reasons, changing the executable file is disabled by default": "セキュリティ上の理由から、実行ファイルの変更はデフォルトで無効になっています。 ", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "セキュリティ上の理由から、カスタム コマンドライン引数はデフォルトで無効になっています。これを変更するには、UniGetUI のセキュリティ設定に移動してください。", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "セキュリティ上の理由から、操作前および操作後のスクリプトはデフォルトで無効になっています。これを変更するには、UniGetUI のセキュリティ設定にアクセスしてください。 ", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "ARM用にコンパイルされた winget を強制（ARM64システム専用）", "Formerly known as WingetUI": "旧 WingetUI", "Found": "検出", "Found packages: ": "見つかったパッケージ数: ", "Found packages: {0}": "見つかったパッケージ数: {0}", "Found packages: {0}, not finished yet...": "見つかったパッケージ数: {0}, 引き続き検索中...", "General preferences": "全体設定", "GitHub profile": "GitHub プロフィール", "Global": "グローバル", "Go to UniGetUI security settings": "UniGetUIのセキュリティ設定に移動する ", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "知られていないが便利なユーティリティやその他の興味深いパッケージの素晴らしいリポジトリです。<br>含まれるもの: <b>ユーティリティ、コマンドラインプログラム、一般ソフトウェア (追加バケットが必要)</b>", "Great! You are on the latest version.": "素晴らしい！最新のバージョンです。", "Grid": "グリッド ", "Help": "ヘルプ", "Help and documentation": "ヘルプとドキュメント", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "ここでは、UniGetUIのショートカットに関する動作を変更できます。ショートカットにチェックを付けると、今後のアップグレードでそのショートカットが作成された場合、UniGetUIがそれを削除します。チェックを外すと、ショートカットはそのまま残ります。 ", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "こんにちは、私の名前は Martí で、WingetUI の <i>開発者</i> です。WingetUI は、すべて私の自由な時間に作られました！", "Hide details": "詳細を隠す", "Homepage": "ホームページ", "Hooray! No updates were found.": "アップデートは見つかりませんでした！", "How should installations that require administrator privileges be treated?": "管理者権限を必要とするインストールをどのように扱いますか？", "How to add packages to a bundle": "バンドルにパッケージを追加する方法 ", "I understand": "了解", "Icons": "アイコン", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "クラウドバックアップが有効になっている場合は、このアカウントにGitHub Gistとして保存されます。 ", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "バンドルからパッケージをインポートする際に、インストール前およびインストール後のカスタムコマンドを無視する。 ", "Ignore future updates for this package": "このパッケージの将来のアップデートを無視", "Ignore packages from {pm} when showing a notification about updates": "更新に関する通知を表示するときに、{pm} からのパッケージを無視します", "Ignore selected packages": "選択したパッケージを無視", "Ignore special characters": "特殊文字を無視", "Ignore updates for the selected packages": "選択したパッケージのアップデートを無視する", "Ignore updates for this package": "このパッケージのアップデートを無視する", "Ignored updates": "無視されたアップデート", "Ignored version": "無視されたバージョン", "Import": "インポート", "Import packages": "パッケージのインポート", "Import packages from a file": "パッケージをファイルからインポートする", "Import settings from a local file": "ローカルファイルから設定をインポート", "In order to add packages to a bundle, you will need to: ": "バンドルにパッケージを追加するには、次の手順が必要です。 ", "Initializing WingetUI...": "WingetUI を初期化中...", "Install": "インストール", "Install Scoop": "Scoopのインストール", "Install and more": "インストール", "Install and update preferences": "インストールと設定の更新 ", "Install as administrator": "管理者としてインストール", "Install available updates automatically": "利用可能なアップデートを自動的にインストールする", "Install location can't be changed for {0} packages": "{0} パッケージのインストール場所を変更できません ", "Install location:": "インストール先", "Install options": "インストールオプション ", "Install packages from a file": "ファイルからパッケージをインストール", "Install prerelease versions of UniGetUI": "プレリリース版のUniGetUIをインストールする", "Install script": null, "Install selected packages": "選択したパッケージをインストール", "Install selected packages with administrator privileges": "選択したパッケージを管理者権限でインストール", "Install selection": "選択したものをインストール", "Install the latest prerelease version": "最新の先行リリース版をインストールする", "Install updates automatically": "自動的にアップデートをインストールする", "Install {0}": "{0} をインストール", "Installation canceled by the user!": "ユーザーによってインストールがキャンセルされました！", "Installation failed": "インストール失敗", "Installation options": "インストールオプション", "Installation scope:": "インストールの範囲: ", "Installation succeeded": "インストール成功", "Installed Packages": "導入済みソフトウェア", "Installed Version": "インストールされているバージョン", "Installed packages": "導入済みソフトウェア", "Installer SHA256": "インストーラー SHA256", "Installer SHA512": "インストーラー SHA512", "Installer Type": "インストーラー タイプ", "Installer URL": "インストーラー URL", "Installer not available": "インストーラーが利用できません ", "Instance {0} responded, quitting...": "インスタンス{0}が応答、終了しています...", "Instant search": "インクリメンタル検索", "Integrity checks can be disabled from the Experimental Settings": "整合性チェックは「実験的設定」から無効にできます。", "Integrity checks skipped": "整合性チェックをスキップ ", "Integrity checks will not be performed during this operation": "この操作中は整合性チェックは実行されません ", "Interactive installation": "対話型インストール", "Interactive operation": "インタラクティブ操作", "Interactive uninstall": "対話型アンインストール", "Interactive update": "対話型アップデート", "Internet connection settings": "インターネット接続設定", "Is this package missing the icon?": "このパッケージにはアイコンがありませんか？", "Is your language missing or incomplete?": "ご希望の言語が見当たらない、または不完全ですか？", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "提供された認証情報が安全に保管される保証はないので、銀行口座の認証情報など重要なログイン情報を使用しない方がよいでしょう。 ", "It is recommended to restart UniGetUI after WinGet has been repaired": "WinGetが修復されたらUniGetUIを再起動することをお勧めします ", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "この状況を解決するため、UniGetUIの再インストールを強くおすすめします。", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "WinGet が正常に動作していないようです。WinGet の修復を試みますか? ", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "管理者として WingetUI を実行したようですが、これは推奨されていません。プログラムは引き続き使用できますが、WingetUI を管理者権限で実行しないことを強くお勧めします。\"{showDetails}\"をクリックして、その理由を確認してください。", "Language": "言語", "Language, theme and other miscellaneous preferences": "言語、テーマやその他の細かな設定", "Last updated:": "最終更新:", "Latest": "最新", "Latest Version": "最新バージョン", "Latest Version:": "最新バージョン:", "Latest details...": "最新情報...", "Launching subprocess...": "サブプロセスを起動中…", "Leave empty for default": "通常は空欄にしてください", "License": "ライセンス", "Licenses": "ライセンス", "Light": "ライトテーマ", "List": "一覧", "Live command-line output": "コマンドライン出力の表示", "Live output": "ライブ出力", "Loading UI components...": "UIコンポーネントの読み込み中...", "Loading WingetUI...": "WingetUI の読み込み中...", "Loading packages": "パッケージの読み込み ", "Loading packages, please wait...": "パッケージを読み込んでいます。お待ち下さい…", "Loading...": "読み込み中...", "Local": "ローカル", "Local PC": "ローカルPC", "Local backup advanced options": "ローカルバックアップの詳細オプション ", "Local machine": "ローカルマシン", "Local package backup": "ローカルパッケージのバックアップ ", "Locating {pm}...": "{pm} を探索中...", "Log in": "ログイン ", "Log in failed: ": "ログインに失敗しました： ", "Log in to enable cloud backup": "クラウドバックアップを有効にするにはログインしてください ", "Log in with GitHub": "GitHubでログイン ", "Log in with GitHub to enable cloud package backup.": "クラウド パッケージのバックアップを有効にするには、GitHub でログインします。 ", "Log level:": "ログレベル：", "Log out": "ログアウト ", "Log out failed: ": "ログアウトに失敗しました: ", "Log out from GitHub": "GitHubからログアウトする ", "Looking for packages...": "パッケージを探しています...", "Machine | Global": "PC全体（グローバル）", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "不正なコマンドライン引数はパッケージを破損させたり、悪意のある攻撃者が昇格した権限で実行を行うことを可能にする場合があります。そのため、カスタム コマンドライン引数のインポートはデフォルトで無効になっています。", "Manage": "管理", "Manage UniGetUI settings": "UniGetUI設定を管理する ", "Manage WingetUI autostart behaviour from the Settings app": "Windows の設定アプリで UniGetUI のスタートアップ設定を管理する", "Manage ignored packages": "無視されたパッケージの管理", "Manage ignored updates": "無視されたアップデートの管理", "Manage shortcuts": "ショートカットを管理", "Manage telemetry settings": "テレメトリー設定の管理", "Manage {0} sources": "{0} のソース管理", "Manifest": "マニフェスト", "Manifests": "マニフェスト", "Manual scan": "手動スキャン ", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoftの公式パッケージマネージャーです。有名で検証済みのパッケージがたくさん含まれています。<br>含まれるもの: <b>総合ソフトウェア、Microsoft Storeアプリ</b>", "Missing dependency": "必要な依存関係がありません", "More": "その他", "More details": "詳細情報", "More details about the shared data and how it will be processed": "共有データと処理方法の詳細はこちら", "More info": "詳細情報", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "注意: このトラブルシューティングは、UniGetUI 設定の WinGet セクションから無効にすることができます。 ", "Name": "名称", "New": "New", "New Version": "新しいバージョン", "New bundle": "新規バンドル", "New version": "新しいバージョン", "Nice! Backups will be uploaded to a private gist on your account": "バックアップはあなたのアカウントのプライベートGistにアップロードされます ", "No": "いいえ", "No applicable installer was found for the package {0}": "パッケージ {0} に適用可能なインストーラーが見つかりませんでした ", "No dependencies specified": "依存関係が指定されていません ", "No new shortcuts were found during the scan.": "スキャン中に新しいショートカットは見つかりませんでした。\n", "No packages found": "パッケージが見つかりません", "No packages found matching the input criteria": "入力条件に一致するパッケージは見つかりませんでした", "No packages have been added yet": "パッケージが追加されていません", "No packages selected": "パッケージが選択されていません", "No packages were found": "パッケージが見つかりませんでした", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "個人情報の収集や送信は一切行っておらず、収集されたデータは匿名化されているため、お客様個人を特定することはできません。", "No results were found matching the input criteria": "入力された条件にマッチする結果は見つかりませんでした", "No sources found": "ソースが見つかりません", "No sources were found": "ソースが設定されていません", "No updates are available": "利用できる更新がありません", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node.jsのパッケージマネージャーです。JavaScriptの世界を中心に様々なライブラリやユーティリティが含まれています。<br>含まれるもの: <b>Node.jsのJavaScriptライブラリおよびその他の関連ユーティリティ</b>", "Not available": "利用不可", "Not finding the file you are looking for? Make sure it has been added to path.": "探しているファイルが見つかりませんか？パスに追加されていることを確認してください。 ", "Not found": "不検出", "Not right now": "あとで", "Notes:": "注意事項:", "Notification preferences": "通知設定", "Notification tray options": "通知領域オプション", "Notification types": "通知の種類 ", "NuPkg (zipped manifest)": "NuPkg (zip圧縮されたマニフェストファイル)", "OK": "OK", "Ok": "OK", "Open": "開く", "Open GitHub": "GitHubを開く", "Open UniGetUI": "UniGetUI を開く", "Open UniGetUI security settings": "UniGetUIのセキュリティ設定を開く ", "Open WingetUI": "UnigetUI を開く", "Open backup location": "バックアップ先を開く", "Open existing bundle": "バンドルを開く", "Open install location": "インストール先を開く", "Open the welcome wizard": "設定ウィザードを開く", "Operation canceled by user": "操作はユーザーによってキャンセルされました", "Operation cancelled": "操作がキャンセルされました", "Operation history": "操作履歴", "Operation in progress": "作業の進行中", "Operation on queue (position {0})...": "順番を待っています（キューの {0} 番目）...", "Operation profile:": "操作プロファイル: ", "Options saved": "オプションを保存しました", "Order by:": "並べ替え: ", "Other": "その他", "Other settings": "その他の設定 ", "Package": "パッケージ ", "Package Bundles": "パッケージバンドル", "Package ID": "パッケージID", "Package Manager": "パッケージマネージャー", "Package Manager logs": "パッケージマネージャーのログ", "Package Managers": "パッケージマネージャー", "Package Name": "パッケージ名", "Package backup": "パッケージのバックアップ", "Package backup settings": "パッケージバックアップ設定 ", "Package bundle": "パッケージバンドル", "Package details": "パッケージの詳細", "Package lists": "パッケージリスト ", "Package management made easy": "パッケージ管理が簡単に ", "Package manager": "パッケージマネージャー ", "Package manager preferences": "パッケージマネージャーの設定", "Package managers": "パッケージ マネージャー", "Package not found": "パッケージが見つかりません", "Package operation preferences": "パッケージ操作の設定 ", "Package update preferences": "パッケージ更新の設定 ", "Package {name} from {manager}": "{manager} からのパッケージ {name} ", "Package's default": "パッケージのデフォルト ", "Packages": "パッケージ", "Packages found: {0}": "{0} 個のパッケージが見つかりました", "Partially": "部分的に ", "Password": "パスワード ", "Paste a valid URL to the database": "データベースの有効なURLを入力してください", "Pause updates for": "更新を一時停止 ", "Perform a backup now": "今すぐバックアップする", "Perform a cloud backup now": "今すぐクラウドバックアップを実行する ", "Perform a local backup now": "今すぐローカルバックアップを実行する ", "Perform integrity checks at startup": "起動時に整合性チェックを実行する", "Performing backup, please wait...": "バックアップ中です。お待ち下さい…", "Periodically perform a backup of the installed packages": "インストール済みパッケージのバックアップを定期的に取る", "Periodically perform a cloud backup of the installed packages": "インストール済みパッケージのクラウドバックアップを定期的に取る", "Periodically perform a local backup of the installed packages": "インストール済みパッケージのローカルバックアップを定期的に取る", "Please check the installation options for this package and try again": "このパッケージのインストールオプションを確認し、再度お試しください。", "Please click on \"Continue\" to continue": "「続行」をクリックして、続行してください。", "Please enter at least 3 characters": "少なくとも3文字入力してください", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "このマシンで有効になっているパッケージマネージャーにより、特定のパッケージがインストールできない場合があることに注意してください。", "Please note that not all package managers may fully support this feature": "すべてのパッケージマネージャがこの機能を完全にサポートしているわけではないことに注意してください。 ", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "特定のソースからのパッケージはエクスポートできない場合がありますのでご注意ください。それらはグレーアウトされており、エクスポートされません。", "Please run UniGetUI as a regular user and try again.": "UniGetUIを一般ユーザーとして実行し、もう一度お試しください。", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "この問題に関する詳細はコマンドライン出力か操作履歴をご覧ください", "Please select how you want to configure WingetUI": "WingetUI をどのように構成するかを選択してください。", "Please try again later": "後ほどもう一度お試しください。", "Please type at least two characters": "少なくとも 2 文字を入力してください", "Please wait": "お待ち下さい", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "{0}のインストールが完了するまでお待ちください。黒いウィンドウが表示される場合があります。そのウィンドウが閉じるまでお待ちください。", "Please wait...": "お待ちください...", "Portable": "ポータブル版", "Portable mode": "ポータブルモード", "Post-install command:": "インストール後コマンド:", "Post-uninstall command:": "アンインストール後コマンド:", "Post-update command:": "アップデート後コマンド:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell のパッケージマネージャーです。PowerShell の可能性を広げるライブラリーやスクリプトが見つかります。<br>含まれるもの: <b>モジュール、スクリプト、コマンドレット</b>\n", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "インストール前後のコマンドは、もし悪意を持って設計されていれば、あなたのデバイスに非常に悪質な影響を与える可能性があります。パッケージバンドルのソースを信頼しない限り、そこからコマンドをインポートするのは非常に危険です。", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "インストール前およびインストール後のコマンドは、パッケージのインストール、アップグレード、アンインストールの前後に実行されます。慎重に使用しないと問題を引き起こす可能性があります。", "Pre-install command:": "インストール前コマンド:", "Pre-uninstall command:": "アンインストール前コマンド:", "Pre-update command:": "アップデート前コマンド:", "PreRelease": "先行リリース版", "Preparing packages, please wait...": "パッケージを準備しています。お待ち下さい…", "Proceed at your own risk.": "自己責任で進めてください。", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "UniGetUI Elevator または GSudo によるあらゆる種類の昇格を禁止します ", "Proxy URL": "プロキシ URL", "Proxy compatibility table": "プロキシ互換性表", "Proxy settings": "プロキシ 設定", "Proxy settings, etc.": "プロキシ 設定など。", "Publication date:": "公開日:", "Publisher": "パブリッシャー", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Pythonのライブラリマネージャー。Pythonライブラリやその他のPython関連ユーティリティがたくさんあります<br>含まれるもの：<b>Pythonライブラリおよび関連ユーティリティ</b>", "Quit": "終了", "Quit WingetUI": "WingetUI を終了", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "UAC プロンプトを減らし、デフォルトでインストールを昇格し、特定の危険な機能のロックを解除するなど。 ", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "影響を受けたファイルに関する詳細を確認するには、UniGetUI ログを参照してください。", "Reinstall": "再インストール", "Reinstall package": "パッケージを再インストール", "Related settings": "関連設定 ", "Release notes": "リリースノート", "Release notes URL": "リリースノート URL", "Release notes URL:": "リリースノートのURL:", "Release notes:": "リリースノート:", "Reload": "再読み込み", "Reload log": "ログの再読み込み", "Removal failed": "削除に失敗しました", "Removal succeeded": "削除に成功しました", "Remove from list": "リストから削除", "Remove permanent data": "保存データの削除", "Remove selection from bundle": "選択したものをバンドルから削除", "Remove successful installs/uninstalls/updates from the installation list": "インストール・アンインストール・アップデートに成功したパッケージをインストールリストから削除します", "Removing source {source}": "ソース {source} の削除", "Removing source {source} from {manager}": "ソース {source} を {manager} から削除しています", "Repair UniGetUI": "UniGetUI の修復", "Repair WinGet": "WinGetの修復 ", "Report an issue or submit a feature request": "不具合報告・機能の要望提出", "Repository": "リポジトリ", "Reset": "リセット", "Reset Scoop's global app cache": "Scoopのグローバルアプリキャッシュをリセット", "Reset UniGetUI": "UniGetUIをリセット", "Reset WinGet": "WinGetをリセットする ", "Reset Winget sources (might help if no packages are listed)": "Winget のソースをリセットする（パッケージがリストに全く表示されない場合に有効かもしれません）", "Reset WingetUI": "WingetUI のリセット", "Reset WingetUI and its preferences": "WingetUI とその設定をリセットする", "Reset WingetUI icon and screenshot cache": "WingetUI のアイコンとスクリーンショットのキャッシュをリセットする", "Reset list": "リストを削除する", "Resetting Winget sources - WingetUI": "Winget のソース初期化 - WingetUI", "Restart": "再起動", "Restart UniGetUI": "UniGetUI を再起動", "Restart WingetUI": "WingetUIを再起動", "Restart WingetUI to fully apply changes": "変更を完全に反映するには WingetUI を再起動してください", "Restart later": "後で再起動する", "Restart now": "すぐに再起動する", "Restart required": "再起動が必要", "Restart your PC to finish installation": "インストールを完了するためにPCの再起動が必要です", "Restart your computer to finish the installation": "インストールを完了するにはコンピューターの再起動が必要です", "Restore a backup from the cloud": "クラウドからバックアップを復元する", "Restrictions on package managers": "パッケージマネージャーの制限 ", "Restrictions on package operations": "パッケージ操作の制限 ", "Restrictions when importing package bundles": "パッケージバンドルをインポートする際の制限 ", "Retry": "リトライ", "Retry as administrator": "管理者として再試行", "Retry failed operations": "失敗した操作を再試行", "Retry interactively": "対話的に再試行する", "Retry skipping integrity checks": "再試行時に整合性チェックをスキップする", "Retrying, please wait...": "リトライしています。お待ち下さい…", "Return to top": "トップに戻る", "Run": "実行", "Run as admin": "管理者として実行", "Run cleanup and clear cache": "クリーンアップとキャッシュ消去を実行する", "Run last": "最後に実行 ", "Run next": "次に実行", "Run now": "今すぐ実行", "Running the installer...": "インストーラーを実行しています...", "Running the uninstaller...": "アンインストーラーを実行しています...", "Running the updater...": "更新プログラムを実行しています...", "Save": "保存", "Save File": "ファイルを保存する", "Save and close": "保存して閉じる", "Save as": "名前を付けて保存 ", "Save bundle as": "バンドルを保存", "Save now": "今すぐ保存", "Saving packages, please wait...": "パッケージを保存しています。お待ち下さい…", "Scoop Installer - WingetUI": "Scoop インストーラー - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop アンインストーラー - WingetUI", "Scoop package": "Scoop パッケージ", "Search": "検索", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "デスクトップ ソフトウェアを検索し、アップデートが利用可能になったら警告し、おかしなことはしないでください。 WingetUI を複雑にしすぎたくない、シンプルな<b>ソフトウェア ストア</b>が欲しいだけです。", "Search for packages": "パッケージの検索", "Search for packages to start": "パッケージを検索してください", "Search mode": "検索モード", "Search on available updates": "利用可能なアップデートから検索します", "Search on your software": "インストール済みのソフトウェアから検索します", "Searching for installed packages...": "インストールされているパッケージを検索中...", "Searching for packages...": "パッケージを検索中...", "Searching for updates...": "アップデートを検索中...", "Select": "選択", "Select \"{item}\" to add your custom bucket": "カスタムバケットに追加する \"{item}\" を選択してください", "Select a folder": "フォルダを選択", "Select all": "すべて選択", "Select all packages": "全てのパッケージを選択", "Select backup": "バックアップを選択", "Select only <b>if you know what you are doing</b>.": "<b>自分が何をしているかわかっている場合</b>にのみ選択すること。", "Select package file": "パッケージファイルを選択", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "開きたいバックアップを選択してください。後で、復元したいパッケージ/プログラムを確認できます。", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "このパッケージをインストール、アップデート、またはアンインストールする前に終了する必要があるプロセスを選択します。", "Select the source you want to add:": "追加したいソースを選択してください:", "Select upgradable packages by default": "アップグレード可能なパッケージをデフォルトで選択する", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "使用する<b>パッケージ マネージャー</b> ({0}) の選択、パッケージのインストール方法の設定、管理者権限の処理方法の設定などを行います。", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "ハンドシェイクを送信しました。インスタンスからの返答を待っています...({0}%)", "Set a custom backup file name": "バックアップのファイル名をカスタマイズする", "Set custom backup file name": "カスタムバックアップファイル名を設定する", "Settings": "設定", "Share": "共有", "Share WingetUI": "WingetUI を共有する", "Share anonymous usage data": "匿名利用状況データの共有", "Share this package": "このパッケージを共有する", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "セキュリティ設定を変更した場合、変更を反映させるためには、再度バンドルを開く必要があります。", "Show UniGetUI on the system tray": "システムトレイにUniGetUIを表示する", "Show UniGetUI's version and build number on the titlebar.": "タイトルバーに UniGetUI のバージョンを表示する", "Show WingetUI": "Winget UI を表示", "Show a notification when an installation fails": "インストールが失敗した場合に通知を表示する", "Show a notification when an installation finishes successfully": "インストールが正常に終了したときに通知を表示する", "Show a notification when an operation fails": "操作が失敗したときに通知を表示する", "Show a notification when an operation finishes successfully": "操作が正常に終了したときに通知を表示する", "Show a notification when there are available updates": "利用可能なアップデートがある場合に通知を表示する", "Show a silent notification when an operation is running": "操作の実行中にサイレント通知を表示する", "Show details": "詳細を表示", "Show in explorer": "エクスプローラーで表示", "Show info about the package on the Updates tab": "パッケージについての情報をアップデートタブに表示する", "Show missing translation strings": "翻訳されていない文字列を表示する", "Show notifications on different events": "さまざまなイベントに関する通知を表示する", "Show package details": "パッケージの詳細情報を表示する", "Show package icons on package lists": "パッケージリストでパッケージアイコンを表示する", "Show similar packages": "類似パッケージ", "Show the live output": "ライブ出力を表示する", "Size": "サイズ", "Skip": "スキップ", "Skip hash check": "ハッシュチェックを行わない", "Skip hash checks": "ハッシュチェックをスキップする", "Skip integrity checks": "整合性の検証をスキップ", "Skip minor updates for this package": "このパッケージのマイナーアップデートを無視", "Skip the hash check when installing the selected packages": "選択したパッケージをインストールするときにハッシュ チェックをスキップします", "Skip the hash check when updating the selected packages": "選択したパッケージを更新するときにハッシュ チェックをスキップします", "Skip this version": "このバージョンをスキップする", "Software Updates": "ソフトウェアアップデート", "Something went wrong": "何かおかしいようです", "Something went wrong while launching the updater.": "アップデータの起動中に問題が発生しました。", "Source": "ソース", "Source URL:": "ソース URL:", "Source added successfully": "ソースが正常に追加されました", "Source addition failed": "ソース追加に失敗しました", "Source name:": "ソース名:", "Source removal failed": "ソース削除に失敗しました", "Source removed successfully": "ソースが正常に削除されました", "Source:": "ソース:", "Sources": "ソース", "Start": "スタート", "Starting daemons...": "デーモンを起動中...", "Starting operation...": "操作を開始しています... ", "Startup options": "起動オプション", "Status": "ステータス", "Stuck here? Skip initialization": "ここで止まりましたか？ 初期化をスキップします", "Success!": null, "Suport the developer": "開発者をサポートする", "Support me": "ご支援ください", "Support the developer": "開発者を支援する", "Systems are now ready to go!": "システムの準備が整いました!", "Telemetry": "テレメトリー", "Text": "テキスト", "Text file": "テキストファイル", "Thank you ❤": "よろしくお願いします❤", "Thank you 😉": "よろしくお願いします😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rustのパッケージマネージャーです。<br>含まれるもの: <b>Rustで書かれたRustのライブラリとプログラム</b>\n", "The backup will NOT include any binary file nor any program's saved data.": "バックアップには実行ファイルやプログラムの保存データは一切含まれません。", "The backup will be performed after login.": "バックアップはログイン後に実行されます。", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "バックアップにはインストールされたパッケージと各パッケージのインストールオプションの完全なリストが含まれます。\n無視されたアップデートやスキップされたバージョンも保存されます。", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "読み込もうとしているバンドルが無効なようです。ファイルを確認し、もう一度お試しください。", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "インストーラのチェックサムが期待する値と一致せず、インストーラの真正性を検証できません。パブリッシャを信頼するのであれば、ハッシュチェックをスキップして {0} パッケージを再度インストールしてください。", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Windows用のクラシックなパッケージマネージャーです。そこにはすべてが揃っています。<br>含まれるもの: <b>一般ソフトウェア</b>", "The cloud backup completed successfully.": "クラウドバックアップが正常に完了しました。", "The cloud backup has been loaded successfully.": "クラウド バックアップが正常に読み込まれました。", "The current bundle has no packages. Add some packages to get started": "現在のバンドルにはパッケージがありません。開始するにはパッケージを追加してください。", "The executable file for {0} was not found": "{0} の実行ファイルが見つかりませんでした", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "{0} パッケージをインストール、アップグレード、またはアンインストールするたびに、次のオプションがデフォルトで適用されます。 ", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "次のパッケージは JSON ファイルにエクスポートされます。ユーザーデータやバイナリは保存されません。", "The following packages are going to be installed on your system.": "次のパッケージがシステムにインストールされます。", "The following settings may pose a security risk, hence they are disabled by default.": "以下の設定はセキュリティ上のリスクをもたらす可能性があるため、デフォルトでは無効になっています。 ", "The following settings will be applied each time this package is installed, updated or removed.": "以下の設定は、このパッケージのインストール、更新、削除のたびに適用されます。", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "インストールオプションはこのパッケージがインストール、アップデート、削除されるたびに適用されます。設定内容は自動的に保存されます。", "The icons and screenshots are maintained by users like you!": "アイコンやスクリーンショットはあなたのようなユーザーによって支えられています！", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "インストーラの信頼性を確認できませんでした。", "The installer has an invalid checksum": "インストーラーのチェックサムが無効です", "The installer hash does not match the expected value.": "インストーラーのハッシュ値が予期されるものにマッチしませんでした。", "The local icon cache currently takes {0} MB": "ローカルアイコンキャッシュに {0} MB 使用しています", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "本プロジェクトの主な目的は、Winget や Scoop のような Windows 向けの主要な CLI パッケージマネージャを操作する直感的な UI を作ることです。", "The package \"{0}\" was not found on the package manager \"{1}\"": "パッケージ\"{0}\"はパッケージマネージャー\"{1}\"で見つかりませんでした。", "The package bundle could not be created due to an error.": "エラーのためパッケージ バンドルを作成できませんでした。", "The package bundle is not valid": "パッケージバンドルが無効です", "The package manager \"{0}\" is disabled": "パッケージマネージャー「{0}」は無効です", "The package manager \"{0}\" was not found": "パッケージマネージャー「{0}」が見つかりませんでした", "The package {0} from {1} was not found.": "{1} から {0} のパッケージを見つけられませんでした。", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "ここに表示されているパッケージは、アップデートをチェックする際に考慮されません。ダブルクリックするか、右のボタンをクリックして、アップデートを無視しないようにしてください。", "The selected packages have been blacklisted": "選択したパッケージを無視します", "The settings will list, in their descriptions, the potential security issues they may have.": "設定の説明には、潜在的なセキュリティ上の問題がリストされます。 ", "The size of the backup is estimated to be less than 1MB.": "バックアップのサイズは1MBに満たない見込みです。", "The source {source} was added to {manager} successfully": "ソース {source} が {manager} に無事に追加されました", "The source {source} was removed from {manager} successfully": "ソース {source} が {manager} から無事に削除されました", "The system tray icon must be enabled in order for notifications to work": "通知が機能するには、システムトレイアイコンを有効にする必要があります ", "The update process has been aborted.": "更新プロセスは中止されました。 ", "The update process will start after closing UniGetUI": "UniGetUI を閉じると更新プロセスが開始されます", "The update will be installed upon closing WingetUI": "アップデートは WingetUI を閉じる時にインストールされます", "The update will not continue.": "更新は続行されません。 ", "The user has canceled {0}, that was a requirement for {1} to be run": "ユーザーは、{1} を実行するための要件である {0} をキャンセルしました。", "There are no new UniGetUI versions to be installed": "新しいUniGetUIバージョンはありません", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "操作が進行中です。WingetUIを終了させるとそれらの操作が失敗する可能性があります。よろしいですか？", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "UniGetUIとその機能について、YouTubeで素晴らしい動画が公開されています。役立つテクニックやヒントを学ぶのに最適です。", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "WingetUI を管理者権限で実行すべきでない理由は主に 2 つあります。\\n 1つ目に、Scoopパッケージマネージャが管理者権限で実行されると、いくつかのコマンドで問題を起こす可能性があるということです。\\n 2つ目に、WingetUI を管理者として実行するということは、あなたがダウンロードしたどのパッケージも管理者として実行されるということです（これは安全ではありません）。\\n あるパッケージを管理者としてインストールする必要がある場合、いつでもその項目を右クリックし、管理者としてインストール/アップデート/アンインストールすることができるということを覚えておいてください。", "There is an error with the configuration of the package manager \"{0}\"": "パッケージ マネージャー \"{0}\" の構成にエラーがあります ", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "インストールが進行中です。WingetUI を終了するとインストールが失敗し予期しない結果を引き起こす可能性があります。それでも WingetUI を終了しますか？", "They are the programs in charge of installing, updating and removing packages.": "このソフトは、パッケージのインストール、更新、削除を担当するプログラムです。", "Third-party licenses": "サードパーティー・ライセンス", "This could represent a <b>security risk</b>.": "これは<b>セキュリティリスク</b>を意味する可能性があります。", "This is not recommended.": "推薦されません。", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "これはおそらく、送られたパッケージが削除されたか、有効にしていないパッケージマネージャで公開されたことが原因です。受信した ID は {0} です。", "This is the <b>default choice</b>.": "これは<b>デフォルトの選択</b>です。", "This may help if WinGet packages are not shown": "WinGet パッケージが表示されない場合に有効かもしれません", "This may help if no packages are listed": "パッケージがリストに表示されない場合に有効かもしれません", "This may take a minute or two": "これには1～2分かかることがあります", "This operation is running interactively.": "この操作は対話型で実行されています。", "This operation is running with administrator privileges.": "この操作は管理者権限で実行されています。", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "このオプションは問題を引き起こします。昇格できない操作はすべて失敗します。\n管理者としてインストール/アップデート/アンインストールは機能しません。 ", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "このパッケージ バンドルには潜在的に危険な設定がいくつかあり、デフォルトでは無視される可能性があります。 ", "This package can be updated": "このパッケージはアップデート可能です", "This package can be updated to version {0}": "このパッケージはバージョン {0} へとアップデート可能です", "This package can be upgraded to version {0}": "このパッケージはバージョン {0} にアップグレードできます ", "This package cannot be installed from an elevated context.": "このパッケージは管理者権限で実行された環境からインストールできません。", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "このパッケージのスクリーンショットがないか、アイコンが不足していますか？ 欠落しているアイコンやスクリーンショットを、オープンでパブリックなデータベースに追加して、UniGetUIの貢献にご協力ください。", "This package is already installed": "このパッケージはすでにインストールされています", "This package is being processed": "このパッケージはインストール作業中です", "This package is not available": "このパッケージは利用できません", "This package is on the queue": "このパッケージはキューで順番を待っています", "This process is running with administrator privileges": "このプロセスは管理者権限で実行されます", "This project has no connection with the official {0} project — it's completely unofficial.": "本プロジェクトは公式の {0} プロジェクトとは何の関わりもなく、完全に非公式です。", "This setting is disabled": "この設定は無効になっています", "This wizard will help you configure and customize WingetUI!": "WingetUI の設定とカスタマイズをお手伝いします！", "Toggle search filters pane": "検索フィルターパネルの表示を切り替える", "Translators": "翻訳者", "Try to kill the processes that refuse to close when requested to": "終了しないプロセスを強制終了する", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "これをオンにすると、パッケージマネージャーとのやり取りに使用する実行ファイルを変更できるようになります。これによりインストールプロセスをより細かくカスタマイズできるようになりますが、危険な場合もあります。", "Type here the name and the URL of the source you want to add, separed by a space.": "追加したいソースの名称とURLをスペースで間を空けて入力してください。", "Unable to find package": "パッケージが見つかりません", "Unable to load informarion": "情報を読み込めません", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUIは、ユーザー体験向上のため、匿名で使用状況データを収集しています。", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUIは、ユーザーエクスペリエンスの向上を目的に、個人を特定できない匿名化された利用データを収集します。", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI は、自動的に削除できる新しいデスクトップ ショートカットを検出しました。 ", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUIは、今後のアップグレード時に自動的に削除可能な以下のデスクトップショートカットを検出しました。", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUIは、自動的に削除可能な{0}個の新しいデスクトップショートカットを検出しました。", "UniGetUI is being updated...": "UniGetUI を更新中です... ", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUIは対応しているいずれのパッケージマネージャーとも関連していません。UniGetUIは独立したプロジェクトです。", "UniGetUI on the background and system tray": "背景とシステムトレイ上の UniGetUI ", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI またはそのコンポーネントの一部が見つからないか破損しています。 ", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUIには {0} が必要ですが、お使いのシステムで見つかりませんでした。", "UniGetUI startup page:": "UniGetUIの起動時に開くページ:", "UniGetUI updater": "UniGetUI 更新設定", "UniGetUI version {0} is being downloaded.": "UniGetUI バージョン {0} をダウンロードしています。", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} のインストール準備が完了しました。", "Uninstall": "アンインストール", "Uninstall Scoop (and its packages)": "Scoop (と Scoopパッケージ)のアンインストール", "Uninstall and more": "アンインストール", "Uninstall and remove data": "アンインストールし、データを削除します。", "Uninstall as administrator": "管理者としてアンインストール", "Uninstall canceled by the user!": "アンインストールはユーザーによってキャンセルされました！", "Uninstall failed": "アンインストールに失敗しました", "Uninstall options": "アンインストールオプション ", "Uninstall package": "パッケージをアンインストール", "Uninstall package, then reinstall it": "パッケージをアンインストールしてから再インストール", "Uninstall package, then update it": "パッケージをアンインストールしてからアップデート", "Uninstall previous versions when updated": "アップデート時に以前のバージョンをアンインストールする", "Uninstall selected packages": "選択したパッケージをアンインストール", "Uninstall selection": "アンインストールを選択", "Uninstall succeeded": "アンインストールに成功しました", "Uninstall the selected packages with administrator privileges": "選択したパッケージを管理者権限でアンインストールします", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "ソースが \"{0}\" としてリストに表示されているアンインストール可能なパッケージは、どのパッケージマネージャーにも公開されていないため、表示できる情報はありません。", "Unknown": "不明", "Unknown size": "サイズ不明 ", "Unset or unknown": "未設定または不明 ", "Up to date": "最新の状態", "Update": "アップデート", "Update WingetUI automatically": "UniGetUIを自動アップデートする", "Update all": "すべてアップデート", "Update and more": "アップデート", "Update as administrator": "管理者としてアップデート", "Update check frequency, automatically install updates, etc.": "更新のチェック頻度、更新の自動インストールなど。", "Update checking": null, "Update date": "アップデート日", "Update failed": "アップデートに失敗しました", "Update found!": "アップデートが見つかりました！", "Update now": "今すぐアップデートする", "Update options": "更新設定", "Update package indexes on launch": "起動時にパッケージインデックスを更新する", "Update packages automatically": "自動的にパッケージをアップデート", "Update selected packages": "選択したパッケージのアップデート", "Update selected packages with administrator privileges": "選択したパッケージを管理者権限でアップデートします", "Update selection": "アップデートを選択", "Update succeeded": "アップデートに成功しました", "Update to version {0}": "バージョン {0} にアップデート", "Update to {0} available": "{0} へのアップデートが利用可能です", "Update vcpkg's Git portfiles automatically (requires Git installed)": "vcpkgのGitポートファイルを自動的に更新する（Gitのインストールが必要です）", "Updates": "更新", "Updates available!": "アップデートできます！", "Updates for this package are ignored": "このパッケージへのアップデートは無視されています", "Updates found!": "アップデートが見つかりました！", "Updates preferences": "アップデート設定", "Updating WingetUI": "WingetUI をアップデートしています", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "PowerShell CMDlet の代わりに、レガシーバンドルの WinGet を使用する ", "Use a custom icon and screenshot database URL": "アイコンとスクリーンショットにカスタムデータベースを使用する", "Use bundled WinGet instead of PowerShell CMDlets": "PowerShell コマンドレットの代わりに、バンドルされた WinGet を使用する", "Use bundled WinGet instead of system WinGet": "システムの WinGet の代わりに、バンドルされた WinGet を使用する", "Use installed GSudo instead of UniGetUI Elevator": "UniGetUI Elevator の代わりに、インストールされた GSudo を使用する", "Use installed GSudo instead of the bundled one": "同梱された GSudo ではなく、インストールされた GSudo を使用する（要再起動）", "Use system Chocolatey": "システムの Chocolatey を使用", "Use system Chocolatey (Needs a restart)": "システムの Chocolatey を使用 (再起動が必要)", "Use system Winget (Needs a restart)": "システムの Winget を使用 (再起動が必要)", "Use system Winget (System language must be set to english)": "システムのWinGetを使用（システム言語を英語に設定する必要があります）", "Use the WinGet COM API to fetch packages": "WinGet COM APIを使用してパッケージを取得します ", "Use the WinGet PowerShell Module instead of the WinGet COM API": "WinGet COM APIの代わりにWinGet PowerShellモジュールを使用する ", "Useful links": "お役立ちリンク", "User": "ユーザー", "User interface preferences": "ユーザーインタフェース設定", "User | Local": "現在のユーザーのみ（ローカル）", "Username": "ユーザー名", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "UniGetUIを利用する場合、GNU 劣等一般公衆利用許諾書（GNU LGPL）v2.1 に同意したことになります。", "Using WingetUI implies the acceptation of the MIT License": "UniGetUIを使用する場合、MITライセンス に同意したことになります", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg ルートディレクトリが見つかりません。%VCPKG_ROOT% 環境変数を定義するか、UniGetUI 設定から定義してください。", "Vcpkg was not found on your system.": "Vcpkg がシステム上に見つかりませんでした。 ", "Verbose": "詳細", "Version": "バージョン", "Version to install:": "インストールするバージョン:", "Version:": "バージョン:", "View GitHub Profile": "GitHub プロフィールを表示", "View WingetUI on GitHub": "WingetUI の GitHub を見る", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "WingetUI のソース コードを表示します。そこから、バグを報告したり、機能を提案したり、WingetUI プロジェクトに直接貢献したりすることもできます。", "View mode:": "表示モード: ", "View on UniGetUI": "UniGetUI で見る", "View page on browser": "ページをブラウザで表示する", "View {0} logs": "{0} 個のログを表示 ", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "インターネット接続を必要とするタスクを実行する前に、デバイスがインターネットに接続されるまで待機する。", "Waiting for other installations to finish...": "他のインストール作業が終了するのを待機しています...", "Waiting for {0} to complete...": "{0} が完了するのを待っています...", "Warning": "警告", "Warning!": "警告！", "We are checking for updates.": "アップデートを確認しています。", "We could not load detailed information about this package, because it was not found in any of your package sources": "設定されたどのパッケージソースにも見つからないため、このパッケージに関する詳細情報をロードできませんでした", "We could not load detailed information about this package, because it was not installed from an available package manager.": "利用可能なパッケージマネージャからインストールされていないため、このパッケージに関する詳細情報を読み込むことができませんでした。", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "{package} の {action}  ができませんでした。後でもう一度試してください。 \"{showDetails}\" をクリックすると、インストーラーからログを取得できます。", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "{package} の {action} ができませんでした。後でもう一度試してください。 \"{showDetails}\" をクリックすると、アンインストーラーからログを取得できます。", "We couldn't find any package": "パッケージが見つかりませんでした", "Welcome to WingetUI": "WingetUI へようこそ", "When batch installing packages from a bundle, install also packages that are already installed": "バンドルからパッケージを一括インストールする際、既にインストールされているパッケージもインストールする", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "新しいショートカットが検出された場合、このダイアログを表示する代わりに、自動的に削除します。", "Which backup do you want to open?": "どのバックアップを開きますか？", "Which package managers do you want to use?": "どのパッケージマネージャーを使いますか？", "Which source do you want to add?": "どのソースを追加しますか？", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "WinGetはUniGetUI内で使用可能ですが、UniGetUIは他のパッケージマネージャーとも組み合わせて使用できるため、混乱を招く可能性があります。過去にはUniGetUIはWinget専用に設計されていましたが、現在は違います、したがってUniGetUIはこのプロジェクトが目指す方向性を反映していないと言えます。", "WinGet could not be repaired": "WinGetを修復できませんでした ", "WinGet malfunction detected": "WinGetの不具合を検出しました ", "WinGet was repaired successfully": "WinGetは正常に修復されました ", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "WingetUI - すべてが最新です", "WingetUI - {0} updates are available": "WingetUI - {0} 個のアップデートがあります", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI ホームページ", "WingetUI Homepage - Share this link!": "WingetUI ホームページ - このリンクを共有してください！", "WingetUI License": "WingetUI ライセンス", "WingetUI Log": "UniGetUI ログ", "WingetUI Repository": "WingetUI リポジトリ", "WingetUI Settings": "WigetUI 設定", "WingetUI Settings File": "WingetUI の設定ファイル", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI は以下のライブラリを使用しています。これらのライブラリがなければ WingetUI は存在しなかったでしょう。", "WingetUI Version {0}": "WingetUI バージョン {0}", "WingetUI autostart behaviour, application launch settings": "WingetUIの自動起動の設定、アプリケーションの起動設定", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI は、ソフトウェアに利用可能なアップデートがあるかどうかを確認し、必要に応じて自動的にインストールします。", "WingetUI display language:": "UniGetUIの表示言語", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUIは管理者権限で実行されていますが、これは推奨されません。UniGetUIを管理者権限で実行すると、UniGetUIから起動されるすべての操作が管理者権限で実行されます。プログラムは引き続き使用可能ですが、UniGetUIを管理者権限で実行しないことを強く推奨します。", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI は翻訳ボランティアの皆さんにより40以上の言語に翻訳されてきました。ありがとうございます🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI は機械翻訳されたのではありません。以下のユーザーが翻訳を担当しました:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI はコマンドライン・パッケージマネージャーに代わり一体化したGUIによって、ソフトウェアの管理をより簡単にするアプリケーションです。", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI は、Microsoft により開発されたパッケージマネージャーの１つであり私が関わったわけではない Winget との違いを明確にするため、この名称を変更することにしています。", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI をアップデートしています。完了すると WingetUI は自動的に再起動します。", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI は今もこれからもずっと無料です。広告もクレジットカード登録も、プレミアム版もありません。\n100%永久に、無料なのです。", "WingetUI log": "UniGetUI ログ", "WingetUI tray application preferences": "WingetUI 通知領域アプリケーション設定", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI は以下のライブラリを使用しています。これらのライブラリがなければ WingetUI は存在しなかったでしょう。", "WingetUI version {0} is being downloaded.": "WingetUI バージョン {0} をダウンロードしています。", "WingetUI will become {newname} soon!": "もうすぐ WingetUI は {newname} に変わります！", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUIは定期的にアップデートをチェックしません。起動時にチェックはされますが、警告はされません。", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUIは、パッケージのインストールに管理者権限への昇格が必要になるたびに、UACプロンプトを表示します。", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI は間もなく {newname} へと名称を変更します。名称変更はこのアプリケーション自体の大幅な変更を示唆するものではありません。開発者は引き続きこのプロジェクトの開発を名称以外は現在と同様に進めていきます。", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI は親愛なる貢献者たちの助けなしには実現しなかったことでしょう。彼らのGitHubプロフィールをチェックしてください。彼らなしには WingetUI は存在しなかったのです！", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI はコントリビューター(貢献者)の助けがなければ実現しなかったでしょう。 皆様ありがとうございます🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} のインストール準備が完了しました。", "Write here the process names here, separated by commas (,)": "プロセス名をコンマ(,)で区切ってここに記入してください。", "Yes": "はい", "You are logged in as {0} (@{1})": "{0}（@{1}）としてログインしています。", "You can change this behavior on UniGetUI security settings.": "この動作は UniGetUI のセキュリティ設定で変更できます。", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "このパッケージのインストール、更新、またはアンインストールされる際の前後に実行するコマンドを定義できます。これらのコマンドはコマンド プロンプトで実行されるため、CMD スクリプトを使用できます。", "You have currently version {0} installed": "現在、バージョン {0} がインストールされています", "You have installed WingetUI Version {0}": "WingetUI バージョン {0} がインストールされています", "You may lose unsaved data": "保存されていないデータが失われる可能性があります", "You may need to install {pm} in order to use it with WingetUI.": "UniGetUI で使用するには、{pm} をインストールする必要がある場合があります。", "You may restart your computer later if you wish": "あとでコンピューターを再起動することもできます。", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "一度だけUACプロンプトが表示され、管理者権限が要求されたパッケージに付与されます。", "You will be prompted only once, and every future installation will be elevated automatically.": "UACプロンプトが表示されるのは一度だけで、以後のインストールはすべて自動的に昇格します。", "You will likely need to interact with the installer.": "インストーラーと対話する必要がある可能性があります。 ", "[RAN AS ADMINISTRATOR]": "管理者として実行", "buy me a coffee": "コーヒー代を投げ銭", "extracted": "抽出された ", "feature": "機能", "formerly WingetUI": "旧 WingetUI", "homepage": "ホームページ", "install": "インストール", "installation": "インストール", "installed": "インストール済み", "installing": "インストール中", "library": "ライブラリー", "mandatory": "必須 ", "option": "オプション ", "optional": "オプション ", "uninstall": "アンインストール", "uninstallation": "アンインストール中", "uninstalled": "アンインストール済み", "uninstalling": "アンインストール中", "update(noun)": "アップデート", "update(verb)": "アップデート", "updated": "アップデート", "updating": "アップデート中", "version {0}": "バージョン {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} はデフォルトのインストール オプションに従っているため、{0} のインストール オプションは現在ロックされています。", "{0} Uninstallation": "{0} アンインストール中", "{0} aborted": "{0} を中止しました", "{0} can be updated": "{0} をアップデートできます", "{0} can be updated to version {1}": "{0} をバージョン {1} にアップデートできます", "{0} days": "{0} 日", "{0} desktop shortcuts created": "デスクトップにショートカットが{0}個作成されました。", "{0} failed": "{0} が失敗しました", "{0} has been installed successfully.": "{0}は正常にインストールされました。 ", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} が正常にインストールされました。インストールを完了するため、UniGetUI を再起動することをおすすめします。", "{0} has failed, that was a requirement for {1} to be run": "{0} が失敗しました。これは {1} を実行するための要件でした。", "{0} homepage": "{0} ホームページ", "{0} hours": "{0} 時間", "{0} installation": "{0} のインストール", "{0} installation options": "{0} \nのインストールオプション", "{0} installer is being downloaded": "{0}インストーラをダウンロードしています ", "{0} is being installed": "{0} をインストール中です", "{0} is being uninstalled": "{0} はアンインストール中です ", "{0} is being updated": "{0} をアップデート中", "{0} is being updated to version {1}": "{0} がバージョン {1} にアップデートされています", "{0} is disabled": "{0} は無効です", "{0} minutes": "{0} 分", "{0} months": "{0} 月", "{0} packages are being updated": "{0} パッケージをアップデート中", "{0} packages can be updated": "{0} 個のパッケージをアップデートできます", "{0} packages found": "{0} 個のパッケージが見つかりました", "{0} packages were found": "{0} 個のパッケージが見つかりました", "{0} packages were found, {1} of which match the specified filters.": "{0} 個のパッケージが見つかり、うち {1} 個がフィルターにマッチしました。", "{0} selected": null, "{0} settings": "{0} 設定", "{0} status": "{0} のステータス", "{0} succeeded": "{0} が成功しました", "{0} update": "{0} のアップデート", "{0} updates are available": "{0} 件のアップデートが利用可能です", "{0} was {1} successfully!": "{0} の {1} が成功しました", "{0} weeks": "{0} 週間", "{0} years": "{0} 年", "{0} {1} failed": "{0} の {1} が失敗しました", "{package} Installation": "{package} のインストール", "{package} Uninstall": "{package} のアンインストール", "{package} Update": "{package} のアップデート", "{package} could not be installed": "{package} をインストールできませんでした", "{package} could not be uninstalled": "{package} をアンインストールできませんでした", "{package} could not be updated": "{package} をアップデートできませんでした", "{package} installation failed": "{package} のインストールに失敗しました", "{package} installer could not be downloaded": "{package} インストーラーをダウンロードできませんでした", "{package} installer download": "{package} インストーラーのダウンロード", "{package} installer was downloaded successfully": "{package} インストーラーが正常にダウンロードされました", "{package} uninstall failed": "{package} のアンインストールに失敗しました", "{package} update failed": "{package} のアップデートに失敗しました", "{package} update failed. Click here for more details.": "{package} のアップデートに失敗しました。 詳細はこちらをクリックしてください。", "{package} was installed successfully": "{package} は正常にインストールされました", "{package} was uninstalled successfully": "{package} は正常にアンインストールされました", "{package} was updated successfully": "{package} は正常にアップデートされました", "{pcName} installed packages": "{pcName} にインストールされたパッケージ", "{pm} could not be found": "{pm} は見つかりませんでした", "{pm} found: {state}": "{pm} 検出: {state}", "{pm} is disabled": "{pm} は無効化されています", "{pm} is enabled and ready to go": "{pm} は有効化されており準備が完了しています", "{pm} package manager specific preferences": "{pm} パッケージマネージャー固有の設定", "{pm} preferences": "{pm} 設定", "{pm} version:": "{pm} バージョン:", "{pm} was not found!": "{pm} が見つかりませんでした！"}