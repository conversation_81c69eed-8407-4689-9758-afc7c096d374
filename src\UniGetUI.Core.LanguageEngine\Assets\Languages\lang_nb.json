{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(Sist sjekket: {0})", "(Number {0} in the queue)": "(Nummer {0} i køen)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@yrjarv, @mikaelkw", "0 packages found": "0 pakker funnet", "0 updates found": "0 oppdateringer funnet", "1 - Errors": "1 - Feilmeldinger", "1 day": "1 dag", "1 hour": "1 time", "1 month": "1 måned", "1 package was found": "1 pakke ble funnet", "1 update is available": "1 oppdatering er tilgjengelig", "1 week": "1 uke", "1 year": "1 år", "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": "2 - <PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": "3 - In<PERSON><PERSON><PERSON> (mindre)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": "4 - In<PERSON><PERSON><PERSON> (mer)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": "5 - Inform<PERSON><PERSON> (feilsøking)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>\nEn populær C/C++-bibliotekhåndterer. Full av C/C++-biblioteer og andre relaterte verktøy.<br>Inneholder:<b>C/C++-biblioteker og relaterte verktøy</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Et repository fullt av verktøy og programmer designet for Microsoft sitt .NET-økosystem.<br>Inneholder: <b>.NET-relaterte verktøy og skript</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Et repository fylt med verktøy, designet med Microsoft sitt .NET-økosystem i tankene.<br>Inneholder:<b>.NET-relaterte verktøy:</b> ", "A restart is required": "Omstart av datamaskinen er nødvendig", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "Om", "About Qt6": "Om Qt6", "About WingetUI": "Om WingetUI", "About WingetUI version {0}": "Om WingetUI versjon {0}", "About the dev": "<PERSON><PERSON> ut<PERSON>", "Accept": "<PERSON><PERSON><PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "Handling ved dobbelk<PERSON><PERSON> på pakker, sk<PERSON><PERSON> fullførte installasjoner", "Add": "Legg til", "Add a source to {0}": "Legg til en kilde til {0}", "Add a timestamp to the backup file names": "Legg til et tidsstempel til backup-filnavnene", "Add a timestamp to the backup files": "Legg til et tidsstempel til backup-filene", "Add packages or open an existing bundle": "Legg til pakker eller åpne en eksisterende bundle", "Add packages or open an existing package bundle": "Legg til pakker eller åpne en eksisterende bundle", "Add packages to bundle": null, "Add packages to start": "<PERSON><PERSON> til pakker for å starte", "Add selection to bundle": "Legg til utvalg til bundlen", "Add source": "<PERSON><PERSON> til kilde", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": "<PERSON><PERSON> til kilde {source} til {manager}", "Addition succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> tillegging", "Administrator privileges": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Administrator privileges preferences": "Preferanser for administratorret<PERSON>gheter", "Administrator rights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "Alle filer", "All versions": "Alle versjoner", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Tillat at pakkehandlinger skjer parallelt", "Allow parallel installs (NOT RECOMMENDED)": "Tillat parallelle installasjoner (IKKE ANBEFALT)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Tillat at {pm}-operasjoner kan kjøre i parallell", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Du kan eventuelt også installere {0} ved å kjøre følgende kommando i Windows PowerShell:", "Always elevate {pm} installations by default": "Alltid øk tillatelsesnivået for {pm}-installasjoner", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON> kjø<PERSON> {pm}-operasjoner med administratorrettigheter", "An error occurred": "En feil oppstod", "An error occurred when adding the source: ": "En feil oppstod da kilden ble lagt til:", "An error occurred when attempting to show the package with Id {0}": "En feil oppstod med visningen av en pakke med ID {0}", "An error occurred when checking for updates: ": "En feil oppstod ved sjekking for oppdateringer", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "En feil oppstod i behandlingen av denne pakken", "An error occurred:": "En feil oppstod:", "An interal error occurred. Please view the log for further details.": "En intern feil oppstod. Vennligst se loggen for flere detaljer.", "An unexpected error occurred:": "En uventet feil oppstod:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "En uventet feil oppstod mens vi forsøkte å reparere WinGet. Vennligst prøv igjen senere", "An update was found!": "En oppdatering ble funnet!", "Android Subsystem": "Android Subsystem", "Another source": "<PERSON><PERSON> kilde", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": "Ulagrede endringer vill gå tapt", "App Name": "Appnavn", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>, fjern suksessfulle installasjoner automatisk", "Application theme:": "Applikasjonstema:", "Apply": null, "Architecture to install:": "Arkitektur som installeres:", "Are these screenshots wron or blurry?": "Er disse skjermbildene feil eller uklare?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": "Er du sikker på at du vil lage en ny pakkebundle?", "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "<PERSON>r du sikker?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "<PERSON><PERSON><PERSON><PERSON> etter administrator<PERSON>gh<PERSON> en gang per gruppe operasjoner", "Ask for administrator rights when required": "<PERSON><PERSON><PERSON><PERSON> om administratorret<PERSON> ved behov", "Ask once or always for administrator rights, elevate installations by default": "<PERSON><PERSON><PERSON><PERSON> en gang eller alltid etter administratorrettigheter, øk tillatelsesnivået som standard", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "<PERSON><PERSON> spør om administratortilgang en gang (ikke anbefalt)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Be om å slette skrivebordssnarveier som ble opprettet under <PERSON><PERSON><PERSON><PERSON> oppgradering.", "Attention required": "Oppmerksomhet kreves", "Authenticate to the proxy with an user and a password": null, "Author": "<PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": null, "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Lagre en liste over alle dine installerte pakker automatisk for å forenkle gjenoppretting av dem.", "Automatically save a list of your installed packages on your computer.": "Lagre en liste over dine installerte pakker på datamaskinen din automatisk.", "Autostart WingetUI in the notifications area": "Start WingetUI automatisk i varselfeltet", "Available Updates": "Tilgjengelige oppdateringer", "Available updates: {0}": "Tilgjengelige oppdateringer: {0}", "Available updates: {0}, not finished yet...": "Tilgjengelige oppdateringer: {0}, jobber fortsatt...", "Backing up packages to GitHub Gist...": null, "Backup": "Ta backup", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Sikkerhetskopier installerte pakker", "Backup location": null, "Become a contributor": "Bli en bidragsyter", "Become a translator": "<PERSON><PERSON> en oversetter", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Beta-funksjonalitet og andre innstillinger som ikke bør røres", "Both": "<PERSON><PERSON><PERSON>", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Men her er andre ting du kan gjøre for å lære enda mer om WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Ved å deaktivere en pakkehåndterer kommer du ikke lenger til å kunne se eller oppdatere pakkene dens", "Cache administrator rights and elevate installers by default": "Bufre administratorrettigheter og gi installasjonsprogrammer administratorrettigheter automatisk", "Cache administrator rights, but elevate installers only when required": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, men bare gi installasjonsprogrammer administratorrettigh<PERSON> når det er nødvendig", "Cache was reset successfully!": "Buffer har blitt nullstillt!", "Can't {0} {1}": "Kan ikke {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": "Avbryt alle operasjoner", "Change backup output directory": "<PERSON><PERSON> mappen for sikkhetskopien", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "<PERSON>re hvordan UniGetUI sjekker for og installerer tilgjengelige oppdateringer for pakkene dine", "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": "<PERSON>re hvordan UniGetUI installerer pakker, samt søker etter og installerer tilgjengelige oppdateringer", "Change how operations request administrator rights": null, "Change install location": "<PERSON><PERSON> plassering for install<PERSON><PERSON>", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Sjekk etter pakkeoppdateringer med jevne mello<PERSON>rom", "Check for updates": "<PERSON> etter oppdate<PERSON>er", "Check for updates every:": "<PERSON><PERSON><PERSON> etter oppdateringer hver:", "Check for updates periodically": "<PERSON><PERSON><PERSON> etter nye oppdateringer med jevne mellomrom", "Check for updates regularly, and ask me what to do when updates are found.": "<PERSON><PERSON>k<PERSON> etter oppdateringer regelmessig og spør meg hva jeg vil gjøre når oppdateringer er funnet.", "Check for updates regularly, and automatically install available ones.": "<PERSON><PERSON><PERSON><PERSON> etter oppdateringer jevnlig, og installer tilgjengelige oppdateringer automatisk.", "Check out my {0} and my {1}!": "Sjekk ut min {0} og min {1}!", "Check out some WingetUI overviews": "Sjekk ut noen WingetUI-oversikter", "Checking for other running instances...": "<PERSON><PERSON><PERSON> etter andre kjørende instanser...", "Checking for updates...": "<PERSON><PERSON><PERSON> etter opp<PERSON>...", "Checking found instace(s)...": "<PERSON><PERSON><PERSON> oppdagede instans(er)...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": "<PERSON><PERSON><PERSON> (cache)", "Clear finished operations": null, "Clear selection": "<PERSON><PERSON><PERSON> valg", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": "<PERSON><PERSON>-cachen - WingetUI", "Clearing Scoop cache...": "<PERSON><PERSON><PERSON> Scoop sin buffer...", "Click here for more details": "Klikk her for flere de<PERSON>jer", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Trykk på \"Installer\" for å starte installasjonsprosessen. Hvis du hopper over installasjonen, kan det hende at UniGetUI ikke fungerer som den skal.", "Close": "Lukk", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Minimer WingetUI til systemstatusfeltet", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "Kommandolinje-output", "Command-line to run:": null, "Compare query against": "Sammenlign spørring mot", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Komponentinformasjon", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": "Fortsett", "Contribute to the icon and screenshot repository": "Bidra til ikon- og skjermbildearkivet", "Contributors": "Bidragsytere", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopier til utklippstavlen", "Could not add source": null, "Could not add source {source} to {manager}": "<PERSON><PERSON> ikke legge til kilde {source} til {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": "Kunne ikke laste inn annonseringer -", "Could not load announcements - HTTP status code is $CODE": "Kunne ikke laste inn annonseringer - HTTP-statuskoden er $CODE", "Could not remove source": null, "Could not remove source {source} from {manager}": "<PERSON><PERSON> ikke fjerne kilde {source} fra {manager}", "Could not remove {source} from {manager}": "<PERSON><PERSON> ikke fjerne {source} fra {manager}", "Create .ps1 script": null, "Credentials": null, "Current Version": "Nåværende versjon", "Current status: Not logged in": null, "Current user": "Nåværende bruker", "Custom arguments:": "Tilpassede parametre:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Tilpassede kommandolinje-parametre:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Tilpass WingetUI - kun for hackere og avanserte brukere", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ANSVARSFRASKRIVELSE: VI ER IKKE ANSVARLIGE FOR DE NEDLASTEDE PAKKENE. VENNLIGST BARE INNSTALLER PROGRAMVARE DU STOLER PÅ.", "Dark": "<PERSON><PERSON><PERSON>", "Decline": "Avslå", "Default": "Standard", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Standardpreferanser - tilpasset normale brukere", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "Beskrivelse:", "Desktop shortcut created": "Skrivebordsnarvei opprettet", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Utvikling er vanskelig og dette programmet er gratis, men om du liker det kan du alltids <b>spandere en kaffe på meg</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Installer direkte ved å dobbeltklikke på et element under fanen \"{discoveryTab}\" (i stedet for å vise pakkeinformasjon)", "Disable new share API (port 7058)": "Deaktiver det nye delings-API (port 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": "Ansvarsfraskrivelse", "Discover Packages": "Utforsk pakker", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "Skill mellom store og små bokstaver", "Distinguish between uppercase and lowercase": "Skill mellom store og små bokstaver", "Do NOT check for updates": "IKKE søk etter oppdateringer", "Do an interactive install for the selected packages": "Utfør en interaktiv installasjon for den valgte pakken", "Do an interactive uninstall for the selected packages": "Utfør en interaktiv avinstallasjon for den valgte pakken", "Do an interactive update for the selected packages": "Utfør en interaktiv oppdatering for den valgte pakken", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "<PERSON><PERSON><PERSON> last ned nye oversettelser av appen automatisk fra GitHub", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": "Ikke fjern suksessfulle operasjoner automatisk fra listen", "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "<PERSON>kke oppdater pakkeindekser ved oppstart", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Godtar du at UniGetUI samler inn og sender anonyme bruksstatistikker, utelukkende for å forstå og forbedre brukeropplevelsen?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Synes du at WingetUI er nyttig? <PERSON><PERSON> du kan, vil du kanskje støtte arbeidet mitt, så jeg kan fortsette å gjøre WingetUI til det ultimate pakkehåndterergrensesnittet.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Synes du at WingetUI er nyttig? Ønsker du å støtte utvikleren? I så fall kan du {0}, det hjelper masse!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": "Vil du virkelig avinstallere følgende {0} pakker?", "Do you really want to uninstall {0} packages?": "Vil du virkelig avinstallere {0} pakker?", "Do you really want to uninstall {0}?": "Vil du virkelig avinstallere {0}?", "Do you want to restart your computer now?": "Vil du starte datamaskinen på nytt nå?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Vil du oversette WingetUI til ditt språk? Se hvordan du kan bidra <a style=\"color:{0}\" href=\"{1}\"a>HER!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Har du ikke lyst til å donere? Null stress, du kan alltid dele WingetUI med vennene dine. Spre ordet om WingetUI.", "Donate": "<PERSON><PERSON><PERSON>", "Done!": null, "Download failed": "Nedlasting mislyktes", "Download installer": "Last ned installasjonsprogram", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Nedlasting var suksessfull", "Download updated language files from GitHub automatically": "Last ned oppdaterte språkfiler fra GitHub automatisk", "Downloading": "Laster ned", "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "Laster ned metadata for pakken...", "Enable Scoop cleanup on launch": "Aktiver Scoop-cleanup (nullstilling av buffer) ved oppstart", "Enable WingetUI notifications": "Aktiver varsler fra WingetUI", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktiver bakgrunns-API-et (WingetUI Widgets and Sharing, port 7058)", "Enable it to install packages from {pm}.": "Aktiver det for å installere pakker fra {pm}", "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Aktiver {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "<PERSON><PERSON>", "Everything is up to date": "Alt er oppdatert", "Exact match": "Eksakt match", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": "<PERSON><PERSON><PERSON>", "Experimental settings and developer options": "Eksperimentelle innstillinger og valg for utviklere", "Export": "Eksporter", "Export log as a file": "Eksporter logg som en fil", "Export packages": "Eksporter pakker", "Export selected packages to a file": "Eksporter valgte pakker til en fil", "Export settings to a local file": "Eksporter innstillinger til en lokal fil", "Export to a file": "Eksporter til en fil", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "Henter siste annon<PERSON>, vennligst vent...", "Filters": "Filtre", "Finish": "<PERSON><PERSON><PERSON>", "Follow system color scheme": "<PERSON><PERSON><PERSON><PERSON>", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Bruk ARM-kompilert versjon av winget (BARE FOR ARM64-SYSTEMER)", "Formerly known as WingetUI": "T<PERSON><PERSON>gere kjent som WingetUI", "Found": "Funnet", "Found packages: ": "Funnede pakker:", "Found packages: {0}": "<PERSON>t pakker: {0}", "Found packages: {0}, not finished yet...": "<PERSON>t pakker: {0}, jobber fortsatt...", "General preferences": "<PERSON><PERSON><PERSON> inn<PERSON>ill<PERSON>", "GitHub profile": "GitHub-profil", "Global": "Global", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Flott repository med ukjente men nyttige verktøy og andre interessante pakker.<br>Inneholder:<b><PERSON><PERSON><PERSON><PERSON><PERSON>, kom<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, generell programvare (ekstra buckets trengs)</b>", "Great! You are on the latest version.": "Flott! Du har den nyeste versjonen.", "Grid": null, "Help": "<PERSON><PERSON><PERSON><PERSON>", "Help and documentation": "Hjelp og dokumentasjon", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Her kan du endre hvordan UniGetUI håndterer følgende snarveier. Hvis en snarvei er avkrysset, vil UniGetUI slette den hvis den opprettes under en fremtidig oppgradering. Hvis den ikke er avkrysset, vil snarveien forbli uendret.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, mitt navn er <PERSON>í, og jeg er <i>utvikleren</i> som står bak WingetUI. WingetUI har utelukkende blitt laget på fritiden min!", "Hide details": "<PERSON><PERSON><PERSON><PERSON>", "Homepage": "<PERSON><PERSON><PERSON><PERSON>", "Hooray! No updates were found.": "Hurra! Ingen oppdateringer ble funnet!", "How should installations that require administrator privileges be treated?": "Hva skal gjøres med installasjoner som krever administratorrettigheter?", "How to add packages to a bundle": null, "I understand": "<PERSON><PERSON>", "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Ignorer fremtidige oppdateringer for denne pakken", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "<PERSON><PERSON><PERSON> valgte pakker", "Ignore special characters": "<PERSON><PERSON><PERSON> s<PERSON>gn", "Ignore updates for the selected packages": "Ignorer oppdateringer for de valgte pakkene", "Ignore updates for this package": "Ignorer oppdateringer til denne pakken", "Ignored updates": "Ignorerte oppdateringer", "Ignored version": "Ignorert versjon", "Import": "Importer", "Import packages": "Importer pakker", "Import packages from a file": "Importer pakker fra en fil", "Import settings from a local file": "Importer innstillinger fra en lokal fil", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "Initialiserer WingetUI...", "Install": "Installer", "Install Scoop": "Installer Scoop", "Install and more": null, "Install and update preferences": null, "Install as administrator": "Installer som administrator", "Install available updates automatically": "Installer tilgjengelige oppdateringer automatisk", "Install location can't be changed for {0} packages": null, "Install location:": "Installasjonsplassering:", "Install options": null, "Install packages from a file": "Installer pakker fra en fil", "Install prerelease versions of UniGetUI": "Installer forhåndsversjoner av UniGetUI", "Install script": null, "Install selected packages": "Installer valgte pakker", "Install selected packages with administrator privileges": "Installer valgte pakker med administratorrettigheter", "Install selection": "Installer utvalg", "Install the latest prerelease version": "Installer siste forhåndsutgivelsesversjon", "Install updates automatically": "Installer oppdateringer automatisk", "Install {0}": "Installer {0}", "Installation canceled by the user!": "Installasjon avbrutt av bruker!", "Installation failed": "Installasjon feilet", "Installation options": "Installasjonsvalg", "Installation scope:": "Installasjonsomfang:", "Installation succeeded": "Installasjon fullført", "Installed Packages": "Installerte pakker", "Installed Version": "Installer versjon", "Installed packages": null, "Installer SHA256": "Installer SHA256", "Installer SHA512": "Installer SHA512", "Installer Type": "Type installasjonsprogram", "Installer URL": "URL til installasjonsprogram", "Installer not available": "Installerer ikke tilgjengelig", "Instance {0} responded, quitting...": "Instans {0} ga svar, avslutter...", "Instant search": "Hurtigsøk", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Integritetssjekk hoppet over", "Integrity checks will not be performed during this operation": "Integritetssjekker vil ikke bli utført under den<PERSON>", "Interactive installation": "Interaktiv installasjon", "Interactive operation": null, "Interactive uninstall": "Interaktiv avinstallering", "Interactive update": "Interaktiv oppdatering", "Internet connection settings": null, "Is this package missing the icon?": "Mangler ikonet for denne pakken?", "Is your language missing or incomplete?": "Mangler språket ditt eller er det uferdig? (hvis språket ditt er bokmål eller nynorsk jobber jeg med saken)", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": "Det anbefales å starte UniGetUI på nytt etter at WinGet har blitt reparert", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Det ser ut til at WinGet ikke fungerer som det skal. Vil du forsøke å reparere WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Det ser ut til at du kjørte WingetUI som administrator, noe som ikke anbefales. Du kan fortsatt bruke programmet, men vi anbefaler sterkt å ikke kjøre WingetUI med administratorrettigheter. Klikk på \"{showDetails}\" for å vise årsaken til det.", "Language": null, "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tema og diverse andre preferanser", "Last updated:": "Sist oppdatert:", "Latest": "<PERSON><PERSON>", "Latest Version": "<PERSON>ste versjon", "Latest Version:": "Siste versjon:", "Latest details...": "Siste de<PERSON>...", "Launching subprocess...": "Starter subprosess...", "Leave empty for default": "La stå tomt for standardvalget", "License": "<PERSON><PERSON><PERSON>", "Licenses": "Lisenser", "Light": "Lys", "List": null, "Live command-line output": "Direkte output fra kommandolinja", "Live output": "Direkte output", "Loading UI components...": "Laster U<PERSON>-komponenter...", "Loading WingetUI...": "Laster inn WingetUI...", "Loading packages": "Laster inn pakker", "Loading packages, please wait...": "Laster p<PERSON>, vennligst vent...", "Loading...": "Laster...", "Local": "<PERSON><PERSON>", "Local PC": "Lokal PC", "Local backup advanced options": null, "Local machine": "Lokal maskin", "Local package backup": null, "Locating {pm}...": "<PERSON><PERSON> {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "Loggnivå:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Ser etter pakker...", "Machine | Global": "Maskin | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "Behandle autostartoppførsel for WingetUI fr innstillingsappen", "Manage ignored packages": "<PERSON><PERSON><PERSON> <PERSON> pakker", "Manage ignored updates": "<PERSON><PERSON><PERSON> ignore<PERSON> opp<PERSON><PERSON>er", "Manage shortcuts": null, "Manage telemetry settings": "Administrer telemetriinnstillinger", "Manage {0} sources": "Behandle {0} kilder", "Manifest": "Konfigurasjonsfil", "Manifests": "Konfigurasjonsfiler", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoft sin offisielle pakkehåndterer. Full av velkjente og verifiserte pakker<br>Inneholder: <b><PERSON><PERSON> <PERSON>var<PERSON>, Microsoft Store-apper</b>", "Missing dependency": "<PERSON><PERSON> avhengighet", "More": "<PERSON><PERSON>", "More details": "<PERSON><PERSON><PERSON>", "More details about the shared data and how it will be processed": null, "More info": "<PERSON><PERSON>", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "Navn", "New": null, "New Version": "<PERSON><PERSON> versjon", "New bundle": "Ny bundle", "New version": "<PERSON><PERSON> versjon", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "Ingen pakker funnet", "No packages found matching the input criteria": "Fant ingen pakker som samsvarer med søkekriteriene", "No packages have been added yet": "Ingen pakker har blitt lagt til enda", "No packages selected": "Ingen pakker er valgt", "No packages were found": "Ingen pakker ble funnet", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": "Ingen resultater som matchet kriteriene ble funnet", "No sources found": "Ingen kilder ble funnet", "No sources were found": "Ingen kilder har blitt funnet", "No updates are available": "Ingen oppdateringer tilgjengelige", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS sin pakkehåndterer. Full av biblioteker og andre verktøy som svever rundt i JavaScript-universet<br>Inneholder: <b>Node.js-biblioteker og andre relaterte verktøy</b>", "Not available": "<PERSON><PERSON><PERSON>", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Ikke funnet", "Not right now": "Ikke akkurat nå", "Notes:": "Notater:", "Notification preferences": "Varslingsinnstillinger", "Notification tray options": "Innstillinger for varselområdet:", "Notification types": null, "NuPkg (zipped manifest)": "NuPkg (zippet manifest)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON><PERSON>", "Open UniGetUI": "Åpne UniGetUI", "Open UniGetUI security settings": null, "Open WingetUI": "Åpne WingetUI", "Open backup location": "<PERSON><PERSON><PERSON> backup-<PERSON><PERSON><PERSON><PERSON>", "Open existing bundle": "Åpne eksisterene bundle", "Open install location": "<PERSON><PERSON><PERSON>asjonsplassering", "Open the welcome wizard": "Åpne velkomstveilederen", "Operation canceled by user": "Operasjon avbrutt av bruker", "Operation cancelled": "Handling avbrutt", "Operation history": "Handligshistorikk", "Operation in progress": "<PERSON><PERSON> som utføres", "Operation on queue (position {0})...": "Handlingen er i køen (posisjon {0})...", "Operation profile:": null, "Options saved": "Innstillinger lagret", "Order by:": null, "Other": "<PERSON>", "Other settings": null, "Package": null, "Package Bundles": "Pak<PERSON>bund<PERSON>", "Package ID": "Pakke-ID", "Package Manager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Package Manager logs": "Pakkehåndteringslogger", "Package Managers": "Pak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Package Name": "Pakkenavn", "Package backup": null, "Package backup settings": null, "Package bundle": "Pakkebundle", "Package details": "<PERSON><PERSON><PERSON><PERSON>", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": " Innstillinger for pakkehåndterer", "Package managers": null, "Package not found": "<PERSON><PERSON> ble ikke funnet", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "<PERSON><PERSON>", "Packages found: {0}": "Pakker funnet: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "Lim inn en gyldig URL til databasen", "Pause updates for": null, "Perform a backup now": "Utfør en sikkerhetskopi nå", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vennligst vent...", "Periodically perform a backup of the installed packages": "Utfør en sikkerhetskopi av de installerte pakkene med jevne mello<PERSON>rom", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": "Vennligst klikk på \"Fortsett\" for å fortsette", "Please enter at least 3 characters": "Vennligst skriv inn minst 3 tegn", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Vennligst vær oppmerksom på at enkelte pakker kan hende at ikke er installerbare, på grunn av pakkehåndtererne som er aktivert på denne maskinen.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Vennligst vær oppmerksom på at pakker fra enkelte kilder kan være at ikke er eksporterbare. De er grået ut, og kommer ikke til å bli eksportert.", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Vennligst se på kommandolinje-outputen eller handlingshistorikken for mer informasjon om problemet.", "Please select how you want to configure WingetUI": "Vennligst velg hvordan du vil konfigurere WingetUI", "Please try again later": "Vennligst prøv igjen senere", "Please type at least two characters": "<PERSON><PERSON><PERSON><PERSON><PERSON>t tast minst to tegn", "Please wait": "Vennligst vent", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "Vennligst vent...", "Portable": "Portabel", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell sin pakkehåndterer. Finn bibliotek og skript for å utvide PowerShell sine evner<br>Inneholder: <b><PERSON><PERSON><PERSON>, skript, cmdle<PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "Forhåndsutgivelse", "Preparing packages, please wait...": "<PERSON><PERSON>er pakker, vennligst vent...", "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "Publiseringsdato:", "Publisher": "Utgiver", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python sin bibliotekshåndterer. Full av Python-biblioteker og andre python-relaterte verktøy<br>Inneholder: <b>Python-biblioteker og relaterte verktøy</b>", "Quit": "Lukk", "Quit WingetUI": "Avslutt WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "Reinstaller", "Reinstall package": "Installer pakke på nytt", "Related settings": null, "Release notes": "Utgivelsesnotater", "Release notes URL": "URL for utgivelsesnotater", "Release notes URL:": "URL til utgivelsesnotater:", "Release notes:": "Utgivelsesnotater:", "Reload": "Last inn på nytt", "Reload log": "Last inn logg på nytt", "Removal failed": "<PERSON><PERSON><PERSON> feilet", "Removal succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> fjerning", "Remove from list": "<PERSON><PERSON>n fra liste", "Remove permanent data": "Fjern permanente data", "Remove selection from bundle": "<PERSON><PERSON><PERSON> utvalg fra bundle", "Remove successful installs/uninstalls/updates from the installation list": "<PERSON><PERSON>n vellykkede installasjoner/avinstallasjoner/oppdateringer fra installasjonslisten", "Removing source {source}": null, "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON> kilde {source} fra {manager}", "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": "Si fra om en feil eller send inn forslag til nye funksjoner", "Repository": "Repository", "Reset": "Tilbakestill", "Reset Scoop's global app cache": "Tilbakestill Scoop sin globale app-buffer", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "Tilbakestil<PERSON> Winget-kilder (kan hje<PERSON><PERSON> hvis ingen pakker vises)", "Reset WingetUI": "Tilbakestill WingetUI", "Reset WingetUI and its preferences": "Tilbakestill WingetUI med tilhørende innstillinger", "Reset WingetUI icon and screenshot cache": "Tilbakestill WingetUI-ikonet og bufrede skjermbilder", "Reset list": null, "Resetting Winget sources - WingetUI": "Nullstiller Winget-kilder - WingetUI", "Restart": null, "Restart UniGetUI": "Restart UniGetUI", "Restart WingetUI": "Start WingetUI på nytt", "Restart WingetUI to fully apply changes": "Start WingetUI på nytt for at alle endringene skal bli tatt i bruk", "Restart later": "Start på nytt senere", "Restart now": "Start på nytt nå", "Restart required": "Omstart kreves", "Restart your PC to finish installation": "Start PC-en på nytt for å fullføre installasjonen", "Restart your computer to finish the installation": "Start datamaskinen på nytt for å fullføre installasjonen", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "<PERSON><PERSON><PERSON><PERSON> ig<PERSON>n", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": "<PERSON>r<PERSON><PERSON> på nytt, vennligst vent...", "Return to top": "Til toppen", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Run as admin": "<PERSON><PERSON><PERSON><PERSON>m administrator", "Run cleanup and clear cache": "<PERSON><PERSON><PERSON><PERSON> oppren<PERSON> og fjern buffer", "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "<PERSON><PERSON><PERSON><PERSON> installasjonsprogrammet...", "Running the uninstaller...": "K<PERSON><PERSON>rer avinstallasjonsprogrammet", "Running the updater...": "<PERSON><PERSON><PERSON><PERSON> oppdateringsprogrammet...", "Save": null, "Save File": "Lagre fil", "Save and close": "Lagre og lukk", "Save as": null, "Save bundle as": "Lagre bundle som", "Save now": "Lagre nå", "Saving packages, please wait...": "<PERSON>g<PERSON> pakker, vennligst vent...", "Scoop Installer - WingetUI": "Scoop-installasjonsprogram - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop-avinstallasjonsprogram - WingetUI", "Scoop package": "<PERSON><PERSON>-pak<PERSON>", "Search": "<PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "<PERSON><PERSON><PERSON> etter s<PERSON>program<PERSON>e, advar meg når oppdateringer er tilgjengelige og ikke gjør nerdete ting. Jeg vil ikke at WingetUI skal overkomplisere, jeg vil bare ha en enkel <b><PERSON><PERSON><PERSON><PERSON><PERSON></b>", "Search for packages": "<PERSON><PERSON><PERSON> etter pakker", "Search for packages to start": "<PERSON><PERSON><PERSON> etter en eller flere pakker for å starte", "Search mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search on available updates": "<PERSON><PERSON><PERSON> etter tilgjengelige oppdateringer", "Search on your software": "<PERSON><PERSON><PERSON> i din programvare", "Searching for installed packages...": "<PERSON><PERSON><PERSON> etter installerte pakker...", "Searching for packages...": "<PERSON><PERSON><PERSON> etter pakker...", "Searching for updates...": "<PERSON><PERSON><PERSON> etter opp<PERSON>...", "Select": "Velg", "Select \"{item}\" to add your custom bucket": "Velg \"{item}\" for å legge til i din tilpassede bucket", "Select a folder": "Velg en mappe", "Select all": "Velg alle", "Select all packages": "Velg alle pakker", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "Bare velg <b>hvis du vet hva du gjør</b>", "Select package file": "<PERSON><PERSON>g pak<PERSON>l", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "Velg kilden du ønsker å legge til:", "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON>g hvilke <b>pakkebehandlere</b> som skal brukes ({0}), konfigurer hvordan pakker installeres, behandle hvordan administratorrettigheter håndteres, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Håndtrykk sendt. Venter på svar fra instanslytteren... ({0}%)", "Set a custom backup file name": "Velg et eget navn for sikkerhetskopifilen", "Set custom backup file name": "Velg et eget navn for sikkerhetskopifilen", "Settings": "Innstillinger", "Share": "Del", "Share WingetUI": "<PERSON>", "Share anonymous usage data": "Del anonyme bruksdata", "Share this package": "<PERSON> pakken", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Vis UniGetUI i systemfeltet", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "<PERSON><PERSON>", "Show a notification when an installation fails": "Vis et varsel når en installasjon mislykkes", "Show a notification when an installation finishes successfully": "Vis et varsel når en installasjon fullføres uten feil", "Show a notification when an operation fails": "Vis en varsling når en operasjon feiler", "Show a notification when an operation finishes successfully": "Vis en varsling når en operasjon fullføres", "Show a notification when there are available updates": "Vis et varsel når oppdateringer er tilgjengelige", "Show a silent notification when an operation is running": "Vis en stille varsling når en operasjon kjører", "Show details": "<PERSON><PERSON>", "Show in explorer": "Vis i utforsker", "Show info about the package on the Updates tab": "Vis info om pakken under <PERSON><PERSON>", "Show missing translation strings": "Vis manglende oversettels<PERSON><PERSON>nger", "Show notifications on different events": "<PERSON><PERSON> var<PERSON> for ulike hende<PERSON>er", "Show package details": "<PERSON><PERSON>", "Show package icons on package lists": null, "Show similar packages": "<PERSON>is lignende pakker", "Show the live output": "Vis utdata i sanntid", "Size": "<PERSON><PERSON><PERSON><PERSON>", "Skip": "<PERSON><PERSON> over", "Skip hash check": "<PERSON><PERSON> over sjekk av hash", "Skip hash checks": null, "Skip integrity checks": "<PERSON><PERSON> over in<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "<PERSON><PERSON> over sjekken av hash når valgte pakker installeres", "Skip the hash check when updating the selected packages": "<PERSON><PERSON> over sjekken av hash når valgte pakker oppdateres", "Skip this version": "<PERSON><PERSON> over denne vers<PERSON>en", "Software Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Something went wrong": "<PERSON>e gikk galt", "Something went wrong while launching the updater.": null, "Source": "<PERSON><PERSON>", "Source URL:": "Kilde-URL:", "Source added successfully": null, "Source addition failed": "<PERSON><PERSON> ikke legge til kilde", "Source name:": "Kildenavn:", "Source removal failed": "<PERSON><PERSON>ning av kilde feilet", "Source removed successfully": null, "Source:": "Kilde:", "Sources": "<PERSON><PERSON>", "Start": "Start", "Starting daemons...": "Starter b<PERSON><PERSON><PERSON><PERSON><PERSON>...", "Starting operation...": null, "Startup options": "Innstillinger for oppstart", "Status": "Status", "Stuck here? Skip initialization": "Kommer du ikke videre? <PERSON><PERSON> over initialiseringen", "Success!": null, "Suport the developer": "<PERSON><PERSON><PERSON>", "Support me": "<PERSON><PERSON><PERSON> meg", "Support the developer": "<PERSON><PERSON><PERSON>", "Systems are now ready to go!": "Systemene er nå klare for bruk!", "Telemetry": null, "Text": "Tekst", "Text file": "Tekstfil", "Thank you ❤": "Takk ❤", "Thank you 😉": "Takk 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": "Sikkerhetskopien vil ikke inneholde noen binære filer eller programmers lagrede data.", "The backup will be performed after login.": "Sikkerhetskopien vil utføres etter innlogging", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Sikkerhetskopien vil inneholde en fullstendig liste over de installerte pakkene og deres installasjonsvalg. Ignorerte oppdateringer og versjoner som har blitt hoppet over vil også bli lagret.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Sjekksummen til installasjonsprogrammet stemmer ikke overens med den forventede verdien, og autentisiteten til installasjonsprogrammet kan ikke bekreftes. H<PERSON> du stoler på utgiveren, {0} pakken igjen og hopp over hash-sjekken.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Den klassiske pakkehåndtereren for Windows. Du kan finne alt der. <br>Inneholder: <b>Generell programvare</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Nåværende bundle har ingen pakker. Legg til pakker for å sette i gang", "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Følgende pakker vil bli eksportert til en JSON-fil. Ingen brukerdata eller binære filer vil bli lagret.", "The following packages are going to be installed on your system.": "Følgende pakker kommer til å installeres på ditt system.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "Følgende innstillinger kommer til å bli brukt hver gang denne pakken installeres, oppdateres, eller fjernes.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Følgende innstillinger kommer til å bli påført hver gang denne pakken blir installert, op<PERSON><PERSON><PERSON>, eller fjernet. De vil bli lagret automatisk.", "The icons and screenshots are maintained by users like you!": "Ikoner og skjermbilder vedlikeholdes av brukere som deg!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "Installasjonsprogrammets sjekksum er ugyldig", "The installer hash does not match the expected value.": "Installasjonsprogrammets hash matcher ikke den forventede verdien.", "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Hovedmålet med dette prosjektet er å lage et intuitivt grensesnitt for å administrere de mest vanlige CLI-pakkehåndtererne for Windows, for eksempel Winget og Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": "Pakken {0} fra {1} ble ikke funnet.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Pakkene i denne listen kommer ikke til å bli tatt hensyn til ved sjekking etter oppdateringer. Dobbeltklikk på dem eller klikk på knappen til høyre for dem for å slutte å ignorere oppdateringene deres.", "The selected packages have been blacklisted": "<PERSON><PERSON><PERSON> pakker har blitt svartelistet", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "<PERSON><PERSON><PERSON><PERSON> på sikkerhetskopien er estimert at vil være mindre enn 1MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON> {source} ble suksessfullt lag til hos {manager}", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON> {source} ble suksessfullt fjernet fra {manager}", "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": "Oppdateringen kommer til å installeres når du lukker WIngetUI", "The update will not continue.": "Oppdateringen kommer ikke til å fortsette.", "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Det er mange pågående operasjoner. Å avslutte WingetUI kan føre til at de feiler. Vil du fortsette?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Det finnes noen flotte videoer på YouTube som viser frem WingetUI og dets funksjonalitet. Du kan lære nyttige triks og tips!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Det er to hovedgrunner til ikke å kjøre WingetUI som administrator: <PERSON> første er at Scoop-pakkebehandleren kan forårsake problemer med noen kommandoer når den kjøres med administratorrettigheter. Den andre grunnen er at å kjøre WingetUI som administrator betyr at alle pakkene du laster ned vil kjøres som administrator (og dette er ikke trygt). Husk at hvis du trenger å installere en spesifikk pakke som administrator, kan du alltid høyreklikke på elementet -> Installer/Oppdater/Avinstaller som administrator.", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "En installasjon utføres. Hvis du lukker WingetUI, kan installasjonen feile og gi uventede resultater. Vil du fortsatt avslutte WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "De er programmer som er ansvarlige for å installere, oppdatere, og fjerne pakker.", "Third-party licenses": "Tredjepartslisenser", "This could represent a <b>security risk</b>.": "<PERSON>te kan medføre en <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b>.", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Dette er mest sannsynlig fordi pakken du fikk tilsendt er fjernet, eller er publisert på en pakkehåndterer du ikke har aktivert. Mottatt ID er {0}", "This is the <b>default choice</b>.": "Dette er <b>standardvalget</b>.", "This may help if WinGet packages are not shown": "Dette kan hje<PERSON>pe hvis WinGet-pakker ikke vises", "This may help if no packages are listed": null, "This may take a minute or two": "<PERSON>te kan ta noen få minutter", "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "<PERSON>ne pakken kan oppdateres", "This package can be updated to version {0}": "<PERSON>ne pakken kan oppdateres til versjon {0}", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Denne pakken har ingen skjermbilder eller mangler et ikon? Bidra til WingetUI ved å legge til de manglende ikonene og skjermbildene til vår åpne, offentlige database.", "This package is already installed": "Denne pakken er allerede installert", "This package is being processed": "<PERSON>ne pakken behandles", "This package is not available": null, "This package is on the queue": "Denne pakken er i køen", "This process is running with administrator privileges": "<PERSON><PERSON> kjører med administratorrettigh<PERSON>", "This project has no connection with the official {0} project — it's completely unofficial.": "Dette prosjektet har ingen kobling til det offisielle {0}-prosjektet - det er fullstendig uoffisielt.", "This setting is disabled": "Denne innstillingen er deaktivert", "This wizard will help you configure and customize WingetUI!": "Denne veiviseren vil hjelpe deg med å konfigurere og tilpasse WingetUI!", "Toggle search filters pane": "Skru av/på panelet for søkefilter", "Translators": "Oversettere", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Skriv inn navn og URL til kilden du vil legge til her, skilt av mellomrom.", "Unable to find package": "Kan ikke finne pakken", "Unable to load informarion": "Kan ikke laste informasjon", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI samler inn anonyme bruksdata for å forbedre brukeropplevelsen.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI samler inn anonyme bruksdata utelukkende for å forstå og forbedre brukeropplevelsen.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI har oppdaget en ny skrivebordsnarvei som kan bli slettet automatisk", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI har oppdaget {0} nye skrivebordsnarveier som kan bli slettet automatisk", "UniGetUI is being updated...": "UniGetUI oppdateres...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI er ikke knyttet til noen av de kompatible pakkehåndtererne. UniGetUI er et uavhengig prosjekt.", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": "UniGetUI {0} er klar til å bli installert.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Avinstaller Scoop (og alle tilhørende pakker)", "Uninstall and more": null, "Uninstall and remove data": "Avinstaller og fjern data", "Uninstall as administrator": "Avinstaller som administrator", "Uninstall canceled by the user!": "Avinstallasjon avbrutt av brukeren!", "Uninstall failed": "Avinstallering feilet", "Uninstall options": null, "Uninstall package": "Avinstaller pakken", "Uninstall package, then reinstall it": "Avinstaller pakken, og så reinstaller den", "Uninstall package, then update it": "Avinstaller pakken, og så oppdater den", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Avinstaller valgte pakker", "Uninstall selection": null, "Uninstall succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> avin<PERSON>lering", "Uninstall the selected packages with administrator privileges": "Avinstaller de valgte pakkene med administratorrettigheter", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Avinstallerbare pakker med opprinnelsen oppført som \"{0}\" er ikke publisert på noen pakkebehandler, så det finnes ingen informasjon tilgjengelig for visning.", "Unknown": "<PERSON><PERSON><PERSON><PERSON>", "Unknown size": "<PERSON><PERSON><PERSON><PERSON>", "Unset or unknown": "<PERSON><PERSON><PERSON> satt eller ukjent", "Up to date": null, "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Oppdater WingetUI automatisk", "Update all": "<PERSON><PERSON><PERSON><PERSON> alle", "Update and more": null, "Update as administrator": "Oppdater som administrator", "Update check frequency, automatically install updates, etc.": null, "Update checking": null, "Update date": "Oppdateringsdato", "Update failed": "<PERSON><PERSON><PERSON><PERSON> feilet", "Update found!": "Oppdatering funnet!", "Update now": "Oppdater nå", "Update options": null, "Update package indexes on launch": "Oppdater pakkeindekser ved oppstart", "Update packages automatically": "Oppdater pakker automatisk", "Update selected packages": "<PERSON><PERSON><PERSON><PERSON> valgte pakker", "Update selected packages with administrator privileges": "Opp<PERSON><PERSON> valgte pakker med administratorrettigheter", "Update selection": null, "Update succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> oppdatering", "Update to version {0}": null, "Update to {0} available": "Oppdatering for {0} er tilgjengelig", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updates available!": "Oppdateringer er tilgjengelige!", "Updates for this package are ignored": "<PERSON><PERSON><PERSON><PERSON><PERSON> for denne pakken er ignorert", "Updates found!": "Oppdateringer funnet!", "Updates preferences": "Oppdateringsinnstillinger", "Updating WingetUI": "Oppdaterer WingetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Bruk gamle bundlede WinGet i stedet for PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Bruk et egendefinert ikon og en egendefinert URL til databasen", "Use bundled WinGet instead of PowerShell CMDlets": "Bruk bundlede WinGet i stedet for PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "Bruk bundlet WinGet i stedet for systemets WinGet", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Bruk systemets installerte GSudo i stedet for den medfølgende (krever omstart av programmet)", "Use system Chocolatey": "Bruk systemets Chocolatey", "Use system Chocolatey (Needs a restart)": "Bruk systemets Chocolatey (Omstart kreves)", "Use system Winget (Needs a restart)": "Bruk systemets Winget (Omstart kreves)", "Use system Winget (System language must be set to english)": "Bruk systemets Winget (Systemspråket må være satt til engelsk)", "Use the WinGet COM API to fetch packages": "Bruk WinGet sitt COM-API for å hente pakker", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Bruk WinGet-PowerShell-modulen i stedet for WinGet-COM-APIet", "Useful links": "<PERSON><PERSON><PERSON><PERSON> lenker", "User": "<PERSON><PERSON><PERSON>", "User interface preferences": "Alternativer for brukergrensesnitt", "User | Local": "Bruker | Lokal", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "<PERSON> bruke WingetUI medfører at du aksepterer lisensen GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Bruk av UniGetUI innebærer å godta MIT-lisensen", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": "Utfyllende", "Version": "Versjon", "Version to install:": "Versjon å installere:", "Version:": null, "View GitHub Profile": "<PERSON>is GitHub-profil", "View WingetUI on GitHub": "Vis WingetUI på GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "<PERSON>is WingetUIs kildekode. Herfra kan du rapportere feil eller foreslå ny funksjonalitet. Du kan også bidra direkte til WingetUI-prosjektet", "View mode:": null, "View on UniGetUI": null, "View page on browser": "Vis siden i nettleseren", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "Venter på at andre installasjoner skal fullføres...", "Waiting for {0} to complete...": null, "Warning": "<PERSON><PERSON><PERSON>", "Warning!": "<PERSON><PERSON><PERSON>!", "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": "Vi kunne ikke laste inn detaljert informasjon om denne pakken, ettersom den ikke ble funnet i noen av dine pakkekilder", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Vi kunne ikke laste inn detaljert informasjon om denne pakken, fordi den ikke ble installert fra en tilgjengelig pakkebehandler.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Vi kunne ikke {action} {package}. Vennligst prøv igjen senere. Klikk på \"{showDetails}\" for å vise loggene fra installasjonsprogrammet.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Vi kunne ikke {action} {package}. Vennligst prøv igjen senere. Klikk på \"{showDetails}\" for å vise loggene fra avinstallasjonsprogrammet.", "We couldn't find any package": "Vi kunne ikke finne noen pakke", "Welcome to WingetUI": "Velkommen til WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Hvilke pakkehåndterere ønsker du å bruke?", "Which source do you want to add?": "Hvilken kilde ønsker du å legge til?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Mens Winget kan brukes i WingetUI, kan WingetUI også brukes med andre pak<PERSON>ere, som kan være forvirrende. <PERSON><PERSON><PERSON> var WingetUI bare laget for å fungere med Winget, men dette stemmer ikke lenger, og derfor representerer ikke WingetUI hva dette prosjektet har som mål å bli.", "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - Alt er oppdatert", "WingetUI - {0} updates are available": "WingetUI - {0} oppdateringer er tilgjengelige", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI - Hjemmeside", "WingetUI Homepage - Share this link!": "WingetUI sin hjemmeside - <PERSON> lenken!", "WingetUI License": "WingetUI - Lisens", "WingetUI Log": "WingetUI - Logg", "WingetUI Repository": "WingetUI - Repository", "WingetUI Settings": "Inn<PERSON><PERSON><PERSON> for WingetUI", "WingetUI Settings File": "Innstillingsfil for WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI bruker følgende bibliotek. Uten dem ville ikke WingetUI vært mulig.", "WingetUI Version {0}": "WingetUI versjon {0}", "WingetUI autostart behaviour, application launch settings": "WingetUI sin autostartoppførsel, innstillinger for oppstart", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI kan sjekke om programvaren din har tilgjengelige oppdateringer, og installere dem automatisk hvis du vil", "WingetUI display language:": "WingetUI sitt visningsspråk:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI har blitt kjørt som administrator, noe som ikke anbefales. <PERSON><PERSON>r du kjører WingetUI som administrator, får ALLE programmer WingetUI starter administratortillatelser. Du kan fortsatt bruke programmet, men vi anbefaler på det sterkeste å ikke kjøre WingetUI med administratortillatelser.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI har blitt oversatt til mer enn 40 språk takket være frivillige oversettere. Takk 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI er ikke automatisk oversatt. Følgende brukere har vært ansvarlige for oversettelsene:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI er en applikasjon som forenkler håndtering av programvare, ved å tilby en alt-i-ett grafisk grensesnitt for kommandolinjepakkehåndtererne dine.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI bytter navn for å tydeliggjøre forskjellen mellom WingetUI (grensesnittet du bruker akkurat nå) og Winget (en pakkehåndterer utviklet av Microsoft som jeg ikke har noe tilkobling til)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI oppdateres. Når oppdateringen er ferdig starter WingetUI på nytt", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI er gratis, og kommer til å forbli gratis til evig tid. Ingen reklame, ingen kredittkort, ingen premiumversjon. 100% gratis, for alltid.", "WingetUI log": "<PERSON>gg for WingetUI", "WingetUI tray application preferences": "Valg for UniGetUI sin bruk av systemfeltet", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI bruker følgende bibliotek. Uten dem ville ikke WIngetUI vært mulig.", "WingetUI version {0} is being downloaded.": "WingetUI versjon {0} lastes ned.", "WingetUI will become {newname} soon!": "WingetUI kommer til å bli til {newname} snart!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI kommer ikke til å sjekke etter oppdateringer periodisk. De kommer fortsatt til å bli sjekket ved oppstart, men du kommer ikke til å bli advart om dem.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI kommer til å vise et UAC-prompt hver gang en pakke krever administratorrettigheter for å bli installert.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI kommer snart til å bytte navn til {newname}. Dette kommer ikke til å føre til noen endringer i applikasjonen. Jeg (utvikleren) kommer til å fortsette utviklingen av dette prosjektet slik jeg gjør n<PERSON>, men under et annet navn.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI hadde ikke vært mulig uten hjelp fra våre kjære bidragsytere. Sjekk ut GitHub-profilene deres, WingetUI hadde ikke vært mulig uten dem!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI ville ikke vært mulig uten hjelpen fra alle bidragsyterne. Takk alle sammen🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} er klar for installering.", "Write here the process names here, separated by commas (,)": null, "Yes": "<PERSON>a", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "<PERSON><PERSON> har du versjon {0} installert", "You have installed WingetUI Version {0}": "<PERSON> har installert WingetUI versjon {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "<PERSON> må kanskje installere {pm} for å bruke det med WingetUI.", "You may restart your computer later if you wish": "Du kan starte datamaskinen på nytt senere hvis du ønsker det", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Du vil bare bli bedt om å gi administratorrettigheter en gang, og rettighetene vil bli gitt til pakker som ber om det.", "You will be prompted only once, and every future installation will be elevated automatically.": "Du vil bare bli bedt om å gi administratorrettigheter en gang, og alle fremtidige installasjoner vil bli gitt rettigheter automatisk.", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": "KJØRTE SOM ADMINISTRATOR", "buy me a coffee": "spander en kopp kaffe på meg", "extracted": "utpakket", "feature": null, "formerly WingetUI": "tidligere WingetUI", "homepage": "hje<PERSON><PERSON>", "install": "installer", "installation": "installas<PERSON>", "installed": "installert", "installing": "installerer", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "avin<PERSON><PERSON>", "uninstallation": "avinstallasjon", "uninstalled": "a<PERSON><PERSON><PERSON><PERSON>", "uninstalling": "avin<PERSON><PERSON><PERSON>", "update(noun)": "oppdatering", "update(verb)": "oppdatere", "updated": "oppdatert", "updating": "opp<PERSON><PERSON>", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} Avinstallasjon", "{0} aborted": "{0} av<PERSON><PERSON><PERSON>", "{0} can be updated": "{0} kan oppdateres", "{0} can be updated to version {1}": "{0} kan oppdateres til versjon {1}", "{0} days": "{0} dager", "{0} desktop shortcuts created": null, "{0} failed": "{0} feilet", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": "{0} sin hjemmeside", "{0} hours": "{0} timer", "{0} installation": "{0}-<PERSON><PERSON><PERSON>", "{0} installation options": "{0} sine installasjonsvalg", "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} oppdateres", "{0} is being updated to version {1}": "{0} opp<PERSON>res til versjon {1}", "{0} is disabled": "{0} er deaktivert", "{0} minutes": "{0} minutter", "{0} months": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} packages are being updated": "{0} pak<PERSON> oppdateres", "{0} packages can be updated": "{0} pakker kan oppdateres", "{0} packages found": "{0} pakker funnet", "{0} packages were found": "{0} pakker ble funnet", "{0} packages were found, {1} of which match the specified filters.": "{0} pakker ble funnet, hvorav {1} matchet dine filtre.", "{0} selected": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} lyk<PERSON>", "{0} update": "{0}-oppdatering", "{0} updates are available": "{0} opp<PERSON><PERSON>er er tilgjengelige", "{0} was {1} successfully!": "{0} ble {1} uten feil!", "{0} weeks": "{0} uker", "{0} years": "{0} år", "{0} {1} failed": "{0} {1} feilet", "{package} Installation": "{package} Installasjon", "{package} Uninstall": "{package} Avinstaller", "{package} Update": "{package} Oppdater", "{package} could not be installed": "{package} kunne ikke installeres", "{package} could not be uninstalled": "{package} kunne ikke avinstalleres", "{package} could not be updated": "{package} kunne ikke oppdateres", "{package} installation failed": "Installasjon av {package} feilet", "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": "Avinstallasjon av {package} feilet", "{package} update failed": "Oppdatering av {package} feilet", "{package} update failed. Click here for more details.": "Oppdatering av {package} feilet. Klikk her for flere detaljer.", "{package} was installed successfully": "{package} ble suksessfullt installert", "{package} was uninstalled successfully": "{package} ble suksessfullt avinstallert", "{package} was updated successfully": "{package} ble suksessfullt oppdatert", "{pcName} installed packages": "{pcName}: <PERSON><PERSON><PERSON><PERSON> pakker", "{pm} could not be found": "{pm} kunne ikke bli funnet", "{pm} found: {state}": "{pm} funnet: {state}", "{pm} is disabled": "{pm} er deaktivert", "{pm} is enabled and ready to go": "{pm} er aktivert og klar for bruk", "{pm} package manager specific preferences": "Innstillinger som er spesifikke for {pm}-pakkehåndtereren", "{pm} preferences": "<PERSON><PERSON><PERSON><PERSON> for {pm}", "{pm} version:": "{pm} versjon: ", "{pm} was not found!": "{pm} ble ikke funnet!"}