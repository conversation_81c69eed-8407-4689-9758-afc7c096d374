<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.GeneralPages.Experimental"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages.GeneralPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    mc:Ignorable="d">

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel Orientation="Vertical">

            <widgets:CheckboxCard
                x:Name="ShowVersionNumberOnTitlebar"
                CornerRadius="8"
                SettingName="ShowVersionNumberOnTitlebar"
                StateChanged="ShowRestartBanner"
                Text="Show UniGetUI's version and build number on the titlebar." />

            <UserControl Height="16" />

            <widgets:CheckboxCard
                x:Name="DisableWidgetsApi"
                BorderThickness="1,1,1,0"
                CornerRadius="8,8,0,0"
                SettingName="DisableApi"
                StateChanged="ShowRestartBanner"
                Text="Enable background api (WingetUI Widgets and Sharing, port 7058)" />

            <widgets:CheckboxCard
                x:Name="DisableWaitForInternetConnection"
                CornerRadius="0,0,8,8"
                SettingName="DisableWaitForInternetConnection"
                StateChanged="ShowRestartBanner"
                Text="Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity." />

            <UserControl Height="16" />
            <widgets:CheckboxCard
                CornerRadius="8"
                ForceInversion="True"
                SettingName="DisableTimeoutOnPackageListingTasks"
                Text="Disable the 1-minute timeout for package-related operations" />

            <UserControl Height="16" />

            <widgets:SecureCheckboxCard
                x:Name="UseUserGSudoToggle"
                CornerRadius="8"
                SettingName="ForceUserGSudo"
                StateChanged="ShowRestartBanner"
                Text="Use installed GSudo instead of UniGetUI Elevator" />

            <UserControl Height="16" />

            <widgets:CheckboxCard
                CornerRadius="8"
                SettingName="DisableNewProcessLineHandler"
                Text="Enable the new process input handler (StdIn automated closer)" />
            <UserControl Height="16" />

            <widgets:CheckboxCard
                x:Name="DisableDownloadingNewTranslations"
                BorderThickness="1,1,1,0"
                CornerRadius="8,8,0,0"
                SettingName="DisableLangAutoUpdater"
                StateChanged="ShowRestartBanner"
                Text="Download updated language files from GitHub automatically" />

            <widgets:TextboxCard
                CornerRadius="0,0,8,8"
                HelpUrl="https://www.marticliment.com/unigetui/help/icons-and-screenshots#custom-source"
                Placeholder="Leave empty for default"
                SettingName="IconDataBaseURL"
                Text="Use a custom icon and screenshot database URL"
                ValueChanged="ShowRestartBanner" />

            <UserControl Height="16" />

            <widgets:CheckboxCard
                Name="DisableDMWThreadOptimizations"
                CornerRadius="8"
                SettingName="DisableDMWThreadOptimizations"
                Text="Enable background CPU Usage optimizations (see Pull Request #3278)" />

            <UserControl Height="16" />

            <widgets:CheckboxCard
                Name="DisableIntegrityChecks"
                CornerRadius="8"
                SettingName="DisableIntegrityChecks"
                Text="Perform integrity checks at startup" />

            <UserControl Height="16" />

            <widgets:CheckboxCard
                Name="InstallInstalledPackagesBundlesPage"
                CornerRadius="8"
                SettingName="InstallInstalledPackagesBundlesPage"
                Text="When batch installing packages from a bundle, install also packages that are already installed" />


        </StackPanel>
    </ScrollViewer>
</Page>
