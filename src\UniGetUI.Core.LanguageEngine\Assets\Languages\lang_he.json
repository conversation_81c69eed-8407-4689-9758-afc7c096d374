{"\"{0}\" is a local package and can't be shared": "\"{0}\" היא חבילה מקומית ולא ניתן לשתף אותה", "\"{0}\" is a local package and does not have available details": "\"{0}\" היא חבילה מקומית, ואין לה פרטים זמינים", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" היא חבילה מקומית, ואינה תואמת לתכונה זו", "(Last checked: {0})": "(נבדק לאחרונה: {0})", "(Number {0} in the queue)": "(מספר {0} בתור)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON><PERSON>, @maximunited", "0 packages found": "לא נמצאו חבילות", "0 updates found": "לא נמצאו עדכונים", "1 - Errors": "1 - שגי<PERSON>ות", "1 day": "יום אחד", "1 hour": "שעה אחת", "1 month": "חו<PERSON><PERSON> אחד", "1 package was found": "נמצאה חבילה אחת", "1 update is available": "1 עדכון קיים", "1 week": "שבוע אחד", "1 year": "שנה אחת", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. עבור אל הדף \"{0}\" או \"{1}\".", "2 - Warnings": "2 - התר<PERSON><PERSON>ת", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. אתר את החבילה/ות שברצונך להוסיף לצרור ובחר את תיבת הסימון השמאלית ביותר שלהן.", "3 - Information (less)": "3 - מידע (מקוצר)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. לאחר שבחרת את החבילות שברצונך להוסיף לצרור, אתר ולחץ על האפשרות \"{0}\" בסרגל הכלים.", "4 - Information (more)": "4 - מידע (מורחב)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. החבילות שלך נוספו לצרור. תוכל להמשיך להוסיף חבילות או לייצא את הצרור.", "5 - information (debug)": "5 - מידע (דיבאג)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "מנהל חבילות פופולרי עבור ספריות C/C++. כולל ספריות C/C++ וכלים נוספים הקשורים ל-C/C++<br>מכיל: <b>ספריות C/C++ וכלים נלווים</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "מאגר מלא של כלים וקובצי הפעלה שתוכננו מתוך מחשבה על מערכת האקולוגית .NET של Microsoft.<br>מכיל: <b>כלים וסקריפטים הקשורים ל-.NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "מאגר מלא בכלים שתוכננו מתוך מחשבה על המערכת האקולוגית .NET של Microsoft.<br>מכיל: <b>כלים הקשורים ל-.NET</b>", "A restart is required": "נדרשת הפעלה מחדש", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "אודות", "About Qt6": "אודות Qt6", "About WingetUI": "אודות WingetUI", "About WingetUI version {0}": "אודות גרסת WingetUI {0}", "About the dev": "אודות המפתח", "Accept": "מ<PERSON><PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "פעולה בלחיצה כפולה על חבילה, הסתר התקנות שבוצעו בהצלחה", "Add": "הוסף", "Add a source to {0}": "הוסף מקור ל- {0}", "Add a timestamp to the backup file names": "הוסף חותמת זמן לשמות קבצי הגיבוי", "Add a timestamp to the backup files": "הוסף חותמת זמן לקבצי הגיבוי", "Add packages or open an existing bundle": "הוסף חבילות או פתח צרור קיים", "Add packages or open an existing package bundle": "הוסף חבילות או פתח צרור חבילות קיים", "Add packages to bundle": "הוסף חבילות לצרור", "Add packages to start": "הוסף חבילות כדי להתחיל", "Add selection to bundle": "הוסף בחירה לצרור", "Add source": "הוסף מקור", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "הוסף עדכונים שנכשלים עם 'לא נמצא עדכון מתאים' לרשימת העדכונים שהמערכת תתעלם מהם", "Adding source {source}": "מוסיף מקור {source}", "Adding source {source} to {manager}": "מוסיף מקור {source} ל-{manager}", "Addition succeeded": "הוספה מוצלחת", "Administrator privileges": "הרשאות מנהל", "Administrator privileges preferences": "העדפות עבור הרשאות מנהל", "Administrator rights": "הרשאות מנהל מערכת", "Administrator rights and other dangerous settings": null, "Advanced options": "אפשרויות מתקדמות", "All files": "כל הקבצים", "All versions": "כל הגירסאות", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "אפשר לבצע פעולות חבילה במקביל", "Allow parallel installs (NOT RECOMMENDED)": "אפשר התקנות במקביל (לא מומלץ)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "אפשר לבצע פעולות של {pm} במקביל", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "לחלו<PERSON>ין, אתה יכול גם להתקין {0} על ידי הפעלת הפקודה הבאה ב- Windows PowerShell:", "Always elevate {pm} installations by default": "ה<PERSON><PERSON><PERSON> תמיד {pm} כברירת מחדל", "Always run {pm} operations with administrator rights": "הפעל תמיד {pm} פעולות עם זכויות מנהל", "An error occurred": "איר<PERSON>ה שגיאה", "An error occurred when adding the source: ": "אירעה שגיאה בעת הוספת המקור:", "An error occurred when attempting to show the package with Id {0}": "אירעה שגיאה בעת ניסיון להציג את החבילה עם מזהה {0}", "An error occurred when checking for updates: ": "אירעה שגיאה בעת בדיקת עדכונים:", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "אירעה שגיאה במהלך עיבוד החבילה הזו", "An error occurred:": "אירעה שגיאה:", "An interal error occurred. Please view the log for further details.": "איר<PERSON>ה שגיאה פנימית. אנא עיין ביומן לפרטים נוספים.", "An unexpected error occurred:": "שגיאה לא צפויה התרחשה:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "התרחשה בעיה בלתי צפויה בעת ניסיון לתקן את WinGet. אנא נסה שוב מאוחר יותר", "An update was found!": "נמצא עדכון!", "Android Subsystem": "תת-מערכת אנדרואיד", "Another source": "מקור נוסף", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "כל קיצור דרך חדש שיווצר במהלך התקנה או עדכון יימחק אוטומטית, במקום להציג הודעת אישור בפעם הראשונה שהוא מזוהה.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "כל קיצור דרך שנוצר או נערך מחוץ ל-UniGetUI ייבדל. תוכל להוסיף אותם באמצעות כפתור {0}.", "Any unsaved changes will be lost": "כל שינוי שלא יישמר יתבטל", "App Name": "שם אפליקציה", "Appearance": "מראה ותחושה", "Application theme, startup page, package icons, clear successful installs automatically": "ערכת נושא, דף פתיחה, סמלי חבילות, ניקוי התקנות מוצלחות באופן אוטומטי", "Application theme:": "ערכת נושא:", "Apply": null, "Architecture to install:": "ארכיטקטורה להתקנה: ", "Are these screenshots wron or blurry?": "האם תמונות המסך האלה שגויות או מטושטשות?", "Are you really sure you want to enable this feature?": "האם אתה בטוח שברצונך להפעיל תכונה זו?", "Are you sure you want to create a new package bundle? ": "האם אתה בטוח שברצונך ליצור צרור חבילות חדש?", "Are you sure you want to delete all shortcuts?": "האם אתה בטוח שברצונך למחוק את כל קיצורי הדרך?", "Are you sure?": "האם אתה בטוח?", "Ascendant": "בסדר עולה", "Ask for administrator privileges once for each batch of operations": "בקש הרשאות מנהל פעם אחת עבור כל אצווה של פעולות", "Ask for administrator rights when required": "בקש הרשאות מנהל כשהן נדרשות", "Ask once or always for administrator rights, elevate installations by default": "בקש הרשאות מנהל פעם אחת או תמיד, הרץ התקנות כמנהל כברירת מחדל", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "בקש הרשאות מנהל רק פעם אחת (לא מומלץ)", "Ask to delete desktop shortcuts created during an install or upgrade.": "בק<PERSON> למח<PERSON><PERSON> קיצורי דרך שנוצרו בשולחן העבודה במהלך התקנה או עדכון.", "Attention required": "נדרשת תשומת לב", "Authenticate to the proxy with an user and a password": "אימות מול הProxy באמצעות שם משתמש וסיסמה", "Author": "<PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "מסיר קיצורי דרך אוטומטי", "Automatically save a list of all your installed packages to easily restore them.": "שמור אוטומטית רשימה של כל החבילות המותקנות שלך כדי לשחזר אותן בקלות.", "Automatically save a list of your installed packages on your computer.": "שמור אוטומטית רשימה של החבילות המותקנות במחשב שלך.", "Autostart WingetUI in the notifications area": "הפעל אוטומטית את WingetUI אל מגש המערכת", "Available Updates": "עדכונים זמינים", "Available updates: {0}": "עדכונים זמינים: {0}", "Available updates: {0}, not finished yet...": "עדכונים זמינים: {0}, טרם הושלמו...", "Backing up packages to GitHub Gist...": null, "Backup": "גיבוי", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "גבה חבילות מותקנות", "Backup location": "מיקום הגיבוי", "Become a contributor": "הפוך לתורם", "Become a translator": "הפוך למתרגם", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "תכונות בטא ואפשרויות אחרות שאסור לגעת בהן", "Both": "שניהם", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "אבל אלה עוד דברים שאפשר לעשות כדי ללמוד אפילו יותר על WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "על ידי כיבוי של מנהל חבילות, לא תוכל עוד לראות או לעדכן את החבילות שלו.", "Cache administrator rights and elevate installers by default": "שמור הרשאות מנהל ושדרג תוכניות התקנה כברירת מחדל", "Cache administrator rights, but elevate installers only when required": "שמור הרשאות מנהל אך שדרג תוכניות התקנה רק כאשר נדרש", "Cache was reset successfully!": "ז<PERSON><PERSON><PERSON><PERSON><PERSON> מטמון אופס בהצלחה!", "Can't {0} {1}": "לא מתאפשר {0} {1}", "Cancel": "ביטול", "Cancel all operations": "בטל את כל הפעולות", "Change backup output directory": "שנה את ספריית פלט הגיבוי", "Change default options": "שנה אפשרויות ברירת מחדל", "Change how UniGetUI checks and installs available updates for your packages": "שנה את האופן שבו UniGetUI בודק ומתקין עדכונים זמינים עבור החבילות שלך", "Change how UniGetUI handles install, update and uninstall operations.": "שנה את האופן שבו UniGetUI מטפל בפעולות התקנה, עדכון והסרה.", "Change how UniGetUI installs packages, and checks and installs available updates": "שנה כיצד UniGetUI מתקין חבילות, בוד<PERSON> ומתקין עדכונים זמינים", "Change how operations request administrator rights": "שינוי האו<PERSON>ן שבו פעולות מבקשות הרשאות מנהל מערכת", "Change install location": "שנה את מיקום ההתקנה", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "<PERSON><PERSON><PERSON> עדכונים לחבילה מעת לעת", "Check for updates": "בד<PERSON><PERSON> עדכונים", "Check for updates every:": "<PERSON><PERSON><PERSON> עדכונים כל:", "Check for updates periodically": "<PERSON><PERSON><PERSON> עדכונים מעת לעת", "Check for updates regularly, and ask me what to do when updates are found.": "ח<PERSON><PERSON> באו<PERSON><PERSON> קבוע אם יש עדכונים, ושאל אותי מה לעשות כאשר נמצאים עדכונים.", "Check for updates regularly, and automatically install available ones.": "ח<PERSON><PERSON> באו<PERSON><PERSON> קבוע אם יש עדכונים, והתקן באופן אוטומטי עדכונים זמינים.", "Check out my {0} and my {1}!": "בדוק את ה-{0} וה-{1} שלי!", "Check out some WingetUI overviews": "עיין בכ<PERSON>ה סקירות כלליות של WingetUI", "Checking for other running instances...": "מחפש מופעים אחרים שרצים...", "Checking for updates...": "מח<PERSON><PERSON> עדכונים...", "Checking found instace(s)...": "בודק מופע/ים שנמצאו...", "Choose how many operations shouls be performed in parallel": "בחר כמה פעולות יתבצעו במקביל", "Clear cache": "נקה מטמון", "Clear finished operations": null, "Clear selection": "ניק<PERSON>י בחירה", "Clear successful operations": "נקה פעולות שהצליחו", "Clear successful operations from the operation list after a 5 second delay": "נקה פעולות שהצליחו מרשימת הפעולות לאחר השהייה של 5 שניות", "Clear the local icon cache": "נקה את מטמון הסמלים המקומי", "Clearing Scoop cache - WingetUI": "ניקוי מט<PERSON><PERSON><PERSON> - WingetUI", "Clearing Scoop cache...": "מנקה זיכרון מטמון של Scoop...", "Click here for more details": "לחץ כאן למידע נוסף", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "לחץ על התקן כדי להתחיל את תהליך ההתקנה. אם תדלג על ההתקנה, יתכן ש-, UniGetUI לא יעבוד כראוי. ", "Close": "סגירה", "Close UniGetUI to the system tray": "מזער את UniGetUI למגש המערכת", "Close WingetUI to the notification area": "סגור את WingetUI למרכז ההודעות", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "פלט שורת הפקודה", "Command-line to run:": "פקודה להרצה:", "Compare query against": "השווה שאילתה ל", "Compatible with authentication": "תואם לאימות", "Compatible with proxy": "תואם לפרוקסי", "Component Information": "<PERSON>י<PERSON><PERSON> רכיב", "Concurrency and execution": "מקביליות וביצוע", "Connect the internet using a custom proxy": "התח<PERSON>ר לאינ<PERSON><PERSON><PERSON><PERSON> באמצעות Proxy מותאם אישית", "Continue": "המשך", "Contribute to the icon and screenshot repository": "תרום למאגר הסמלים וצילומי מסך", "Contributors": "תורמים", "Copy": "העתק", "Copy to clipboard": "העתק ללוח", "Could not add source": "לא ניתן להוסיף מקור", "Could not add source {source} to {manager}": "לא ניתן להוסיף את המקור {source} ל-{manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "לא ניתן ליצור צרור חבילות", "Could not load announcements - ": "לא ניתן לטעון הודעות -", "Could not load announcements - HTTP status code is $CODE": "לא ניתן לטעון הודעות - קוד סטטוס HTTP הוא $CODE", "Could not remove source": "לא ניתן להסיר מקור", "Could not remove source {source} from {manager}": "לא ניתן להסיר את המקור {source} מ-{manager}", "Could not remove {source} from {manager}": "לא ניתן להסיר את {source} מתוך {manager}", "Credentials": "פרטי התחברות", "Current Version": "גרסה נוכחית", "Current status: Not logged in": null, "Current user": "משת<PERSON><PERSON> נוכחי", "Custom arguments:": "ארגומנטים מותאמים אישית:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "אגרומנטים מותאמים אישית של שורת הפקודה:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "התאם אישית את WingetUI - להאקרים ומשתמשים מתקדמים בלבד", "DEBUG BUILD": "גירסת ניפוי שגיאות", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "כתב ויתור: אי<PERSON><PERSON><PERSON> אחראים לחבילות שהורדו. אנא הקפד להתקין רק תוכנה מהימנה.", "Dark": "כהה", "Decline": "ד<PERSON><PERSON>", "Default": "ברירת מחדל", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "הגדרות ברירת מחדל - מתאים למשתמשים רגילים", "Default vcpkg triplet": "הגדרת ברירת מחדל של vcpkg triplet", "Delete?": "למחוק?", "Dependencies:": null, "Descendant": "ב<PERSON><PERSON><PERSON> יורד", "Description:": "תיאור:", "Desktop shortcut created": "נוצר קיצור דרך בשולחן העבודה", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "פיתוח זה קשה, והתוכנה הזאת חינמית. אבל אם אהבת תמיד תוכל <b>לקנות לי קפה</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "התקנה ישירה בעת לחיצה כפולה על פריט בכרטיסייה \"{discoveryTab}\" (במקום להציג את פרטי החבילה)", "Disable new share API (port 7058)": "השבת ממשק API חדש לשיתוף (פורט 7058)", "Disable the 1-minute timeout for package-related operations": "השבת את מגבלת הזמן של דקה אחת לפעולות הקשורות לחבילות", "Disclaimer": "כתב ויתור", "Discover Packages": "גלה חבילות", "Discover packages": "גלה חבילות", "Distinguish between\nuppercase and lowercase": "הבחנה בין אותיות רישיות לאותיות קטנות", "Distinguish between uppercase and lowercase": "הבחנה בין אותיות רישיות לאותיות קטנות", "Do NOT check for updates": "אל תחפש עדכונים", "Do an interactive install for the selected packages": "בצע התקנה אינטראקטיבית עבור החבילות המסומנות", "Do an interactive uninstall for the selected packages": "בצע הסרת התקנה אינטראקטיבית עבור החבילות המסומנות", "Do an interactive update for the selected packages": "בצע עדכון אינטרקטיבי עבור החבילות המסומנות", "Do not automatically install updates when the battery saver is on": "אל תתקין עדכונים באופן אוטומטי כאשר חיסכון בסוללה מופעל", "Do not automatically install updates when the network connection is metered": "אל תתקין עדכונים באופן אוטומטי כאשר חיבור הרשת מוגדר כמוגבל", "Do not download new app translations from GitHub automatically": "אל תוריד תרגומים לחדשים של יישום באופן אוטומטי", "Do not ignore updates for this package anymore": "אל תתעלם יותר מעדכונים עבור חבילה זו", "Do not remove successful operations from the list automatically": "אל תסיר פעולות מוצלחות מהרשימה באופן אוטומטי", "Do not show this dialog again for {0}": "אל תציג חלון זה שוב עבור {0}", "Do not update package indexes on launch": "אל תעדכן אינדקסים של חבילות בעת ההשקה", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "האם אתה מסכים ש-UniGetUI יאסוף וישלח נתוני שימוש אנונימיים, אך ורק לצורך הבנת חוויית המשתמש ושיפורה?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "האם אתה מוצא את WingetUI שימושי? אם אתה יכול, אולי תרצה לתמוך בעבודה שלי, כדי שאוכל להמשיך להפוך את WingetUI לממשק ניהול החבילות האולטימטיבי.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "אתה חושב ש-<PERSON><PERSON><PERSON> שימושי? רוצה לתמוך במפתח? אם כן, אתה יכול {0}, זה עוזר המון!", "Do you really want to reset this list? This action cannot be reverted.": "האם אתה בטוח שברצונך לאפס רשימה זו? פעולה זו אינה ניתנת לביטול.", "Do you really want to uninstall the following {0} packages?": "האם אתה באמת רוצה להסיר את ההתקנה של {0} החבילות הבאות?", "Do you really want to uninstall {0} packages?": "האם אתה בטוח שאתה רוצה להסיר {0} חבילות?", "Do you really want to uninstall {0}?": "האם אתה בטוח שאתה רוצה להסיר את {0}?", "Do you want to restart your computer now?": "האם אתה רוצה להפעיל מחדש את המכשיר?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "האם אתה רוצה לתרגם את WingetUI לשפה שלך? ראה איך ניתן לעשות זאת <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "לא בא לך לתרום? אל תדאג, אתה תמיד יכול לשתף את WingetUI עם החברים שלך. הפיצו את הבשורה על WingetUI.", "Donate": "תרום", "Done!": "הסתיים!", "Download failed": "ההורדה נכשלה", "Download installer": "הורד את תוכנית ההתקנה", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "ההורדה הצליחה", "Download updated language files from GitHub automatically": "הורד קבצי שפה מעודכנים מ-<PERSON><PERSON><PERSON><PERSON> באו<PERSON>ן אוטומטי", "Downloading": "מוריד", "Downloading backup...": null, "Downloading installer for {package}": "מוריד מתקין עבור {package}", "Downloading package metadata...": "מוריד מידע על חבילה...", "Enable Scoop cleanup on launch": "אפ<PERSON>ר ניק<PERSON><PERSON> Scoop בעליה", "Enable WingetUI notifications": "אפשר הודעות של WingetUI", "Enable an [experimental] improved WinGet troubleshooter": "הפעל פתרון בעיות משופר [ניסיוני] עבור WinGet", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "הפעל אופטימיזציות רקע לשימוש ב-CPU (ראה Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "אפשר API רקע (WingetUI Widgets and Sharing, פורט 7058)", "Enable it to install packages from {pm}.": "אפשר אותו להתקין חבילות מ-{pm}.", "Enable the automatic WinGet troubleshooter": "הפעל את פותר הבעיות האוטומטי של WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "הפעל את מעלית ה-UAC החדשה של UniGetUI", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "אפשר {pm}", "Enter proxy URL here": "הזן כאן כתובת URL של Proxy", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "שגיאה", "Everything is up to date": "<PERSON><PERSON><PERSON> מעודכן", "Exact match": "התא<PERSON>ה מדוייקת", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "קיצורי הדרך הקיימים בשולחן העבודה שלך ייסרקו, ותצטרך לבחור אילו לשמור ואילו להסיר.", "Expand version": "מידע גרסה", "Experimental settings and developer options": "הגדרות ניסיוניות ואפשרויות למפתחים ", "Export": "יצא", "Export log as a file": "יצא קבצי יומן רישום כקובץ", "Export packages": "יצא חבילות", "Export selected packages to a file": "יצא חבילות מסומנות לקובץ", "Export settings to a local file": "יצא הגדרות לקובץ מקומי", "Export to a file": "יצא לקובץ", "Failed": "שגיאה", "Fetching available backups...": null, "Fetching latest announcements, please wait...": "מביא את ההכרזות האחרונות, אנא המתן...", "Filters": "מסננים", "Finish": "סיים", "Follow system color scheme": "עקוב אחר צבעי המערכת", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "אכוף גרסת winget מקומפלת ל-ARM (עבור מערכות ARM64 בלבד)", "Formerly known as WingetUI": "ידוע בעבר בשם WingetUI", "Found": "נמצא", "Found packages: ": "נמצאו חבילות:", "Found packages: {0}": "חבילות שנמצאו: {0}", "Found packages: {0}, not finished yet...": "חבילות שנמצאו: {0}, טרם הושלמו...", "General preferences": "העדפות כלליות", "GitHub profile": "פרו<PERSON><PERSON><PERSON>", "Global": "גלובלי", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "מאגר נהדר של כלי עזר לא ידועים אך שימושיים וחבילות מעניינות אחרות.<br>מכיל: <b>כלי עזר, תוכניות שורת פקודה, תוכנה כללית (נדרשת דלי תוספות)</b>", "Great! You are on the latest version.": "מעולה! אתה משתמש בגרסה העדכנית ביותר.", "Grid": "רשת", "Help": "עזרה", "Help and documentation": "עזרה ותיעוד", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "כאן תוכל לשנות את אופן הפעולה של UniGetUI בנוגע לקיצורי הדרך הבאים. סימון קיצור דרך יגרום ל-UniGetUI למחוק אותו אם ייווצר בעתיד בעדכון. ביטול הסימון ישמור עליו ללא שינוי.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "ה<PERSON><PERSON>, ש<PERSON><PERSON>, ו<PERSON><PERSON><PERSON> <i>המפתח</i> של WingetUI. WingetUI נוצר כולו בזמני הפנוי!", "Hide details": "הסתר פרטים", "Homepage": "דף הבית", "Hooray! No updates were found.": "הידד! לא נמצאו עדכונים!", "How should installations that require administrator privileges be treated?": "כיצד יש להתייחס להתקנות הדורשות הרשאות מנהל?", "How to add packages to a bundle": "כיצד להוסיף חבילות לצרור", "I understand": "<PERSON><PERSON><PERSON>ן", "Icons": "סמלים", "Id": "מזהה", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "התעלם מעדכוני תכונות לחבילה זו", "Ignore packages from {pm} when showing a notification about updates": "התעלם מחבילות מ-{pm} בעת הצגת התראה על עדכונים", "Ignore selected packages": "התעלם מהחבילות המסומנות", "Ignore special characters": "התעלם מתווים מיוחדים", "Ignore updates for the selected packages": "התעלם מעדכונים עבור החבילות המסומנות", "Ignore updates for this package": "התעלם מעדכונים עבור חבילה זו", "Ignored updates": "עדכונים חסומים", "Ignored version": "גרסה חסומה", "Import": "יבא", "Import packages": "י<PERSON><PERSON> חבילות", "Import packages from a file": "יבא חבילות מקובץ", "Import settings from a local file": "יבא הגדרות מקובץ מקומי", "In order to add packages to a bundle, you will need to: ": "כדי להוסיף חבילות לצרור, עליך:", "Initializing WingetUI...": "מאתחל את WingetUI...", "Install": "הת<PERSON>ן", "Install Scoop": "<PERSON><PERSON><PERSON><PERSON>", "Install and more": null, "Install and update preferences": "התקן והעדף הגדרות", "Install as administrator": "הת<PERSON><PERSON> כמנהל מערכת", "Install available updates automatically": "הת<PERSON>ן עדכונים זמינים באופן אוטומטי", "Install location can't be changed for {0} packages": null, "Install location:": "מיקום ההתקנה:", "Install options": null, "Install packages from a file": "התקן חבילות מקובץ", "Install prerelease versions of UniGetUI": "התקן גרסאות קדם של UniGetUI", "Install selected packages": "הת<PERSON><PERSON> חבילות מסומנות", "Install selected packages with administrator privileges": "הת<PERSON><PERSON> חבילות מסומנות עם הרשאות מנהל מערכת", "Install selection": "בחירת התקנה", "Install the latest prerelease version": "התקן את גרסת ההפצה העדכנית ביותר", "Install updates automatically": "הת<PERSON>ן עדכונים באופן אוטומטי", "Install {0}": "התק<PERSON> {0}", "Installation canceled by the user!": "התקנה בוטלה על ידי המשתמש!", "Installation failed": "ההת<PERSON>נה נכשלה", "Installation options": "אפשרויות התקנה", "Installation scope:": "היקף התקנה:", "Installation succeeded": "ההת<PERSON>נה הצליחה", "Installed Packages": "חבילות מותקנות", "Installed Version": "גרסה מותקנת", "Installed packages": "חבילות מותקנות", "Installer SHA256": "התקנה SHA256", "Installer SHA512": "התקנה SHA512", "Installer Type": "סוג תוכנת התקנה", "Installer URL": "כתובת האתר של המתקין", "Installer not available": "תוכנית ההתקנה אינה זמינה", "Instance {0} responded, quitting...": "מופע {0} הגיב, יוצא...", "Instant search": "<PERSON>י<PERSON><PERSON><PERSON> מיידי", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "בדיקות תקינות דוולגו", "Integrity checks will not be performed during this operation": "בדיקות תקינות לא יתבצעו במהלך פעולה זו", "Interactive installation": "התקנה אינטראקטיבית", "Interactive operation": "הפעלה אינטראקטיבית", "Interactive uninstall": "הסרת התקנה אינטראקטיבית", "Interactive update": "עדכ<PERSON>ן אינטרקטיבי", "Internet connection settings": "הגדרות חיבור לאינטרנט", "Is this package missing the icon?": "האם לחבילה זו חסר סמל?", "Is your language missing or incomplete?": "האם השפה שלך חסרה או לא שלמה?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "לא מובטח שהפרטים שסיפקת יאוחסנו בצורה בטוחה, לכן עדיף שלא תשתמש בפרטי החשבון הבנקאי שלך", "It is recommended to restart UniGetUI after WinGet has been repaired": "מומלץ להפעיל מחדש את UniGetUI לאחר תיקון WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "נראה ש-WinGet אינו פועל כראוי. האם ברצונך לנסות לתקן את WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "נראה שהפעלת את WingetUI כמנהל מערכת, ד<PERSON>ר שאינו מומלץ. אתה עדיין יכול להשתמש בתוכנית, אבל אנחנו מאוד ממליצים לא להפעיל WingetUI עם הרשאות מנהל. לחץ על \"{showDetails}\" כדי לראות מדוע.", "Language": "שפה", "Language, theme and other miscellaneous preferences": "שפה, ערכת נושא והעדפות שונות אחרות", "Last updated:": "עוד<PERSON>ן לאחרונה:", "Latest": "עדכנית ביותר", "Latest Version": "גרסה עדכנית ביותר", "Latest Version:": "גרסה עדכנית ביותר:", "Latest details...": "פרטים עדכניים ביותר...", "Launching subprocess...": "מפעיל תהליך משנה...", "Leave empty for default": "הש<PERSON>ר ריק לברירת המחדל", "License": "רי<PERSON><PERSON><PERSON>ן", "Licenses": "רישיונות", "Light": "<PERSON><PERSON><PERSON><PERSON>", "List": "רשימה", "Live command-line output": "פלט שורת פקודה חי", "Live output": "פלט זמן אמת", "Loading UI components...": "טוען רכיבי UI...", "Loading WingetUI...": "טוען את WingetUI...", "Loading packages": "טו<PERSON><PERSON> חבילות", "Loading packages, please wait...": "טוען חבילות, אנ<PERSON> המתן...", "Loading...": "טוען...", "Local": "מק<PERSON><PERSON>י", "Local PC": "<PERSON> <PERSON>קו<PERSON>י", "Local backup advanced options": null, "Local machine": "<PERSON><PERSON><PERSON><PERSON><PERSON> מקומי", "Local package backup": null, "Locating {pm}...": "מאתר את {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "רמת יומן:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "מחפ<PERSON> חבילות...", "Machine | Global": "מכונה | גלובלי", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "ניהול", "Manage UniGetUI settings": "נהל הגדרות UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "נהל את התנהגות ההפעלה האוטומטית של WingetUI מאפליקציית ההגדרות", "Manage ignored packages": "נהל חבילות חסומות", "Manage ignored updates": "נהל עדכונים שהמערכת מתעלמת מהם", "Manage shortcuts": "נהל קיצורי דרך", "Manage telemetry settings": "נהל הגדרות טלמטריה", "Manage {0} sources": "נהל {0} מקורות", "Manifest": "מני<PERSON><PERSON>ט", "Manifests": "מניפסטים", "Manual scan": "סריקה ידנית", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "מנהל החבילות הרשמי של מיקרוסופט. מלא בחבילות ידועות ומאומתות<br>מכיל: <b>תוכנה כללית, אפליקציות Microsoft Store</b>", "Missing dependency": "חבילת תלות חסרה", "More": "עוד", "More details": "פרטים נוספים", "More details about the shared data and how it will be processed": "פרטים נוספים על הנתונים המשותפים ואופן עיבודם", "More info": "מידע נוסף", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "הערה: ניתן להשבית את פתרון הבעיות הזה מהגדרות UniGetUI, בסעיף WinGet", "Name": "שם", "New": "ח<PERSON><PERSON>", "New Version": "גרסה חדשה", "New bundle": "<PERSON><PERSON><PERSON><PERSON> חדש", "New version": "גרסה חדשה", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "לא", "No applicable installer was found for the package {0}": "לא נמצא מתקין מתאים עבור החבילה {0}", "No dependencies specified": null, "No new shortcuts were found during the scan.": "לא נמצאו קיצורי דרך חדשים במהלך הסריקה.", "No packages found": "לא נמצאו חבילות", "No packages found matching the input criteria": "לא נמצאו חבילות התואמות לקריטריוני הקלט", "No packages have been added yet": "עדיין לא נוספו חבילות", "No packages selected": "לא סומנו חבילות", "No packages were found": "לא נמצאו חבילות", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "מידע אישי לא נאסף או נשלח, והנתונים שנאספים הם אנונימיים, ואינם מקושרים אליך.", "No results were found matching the input criteria": "לא נמצאו תוצאות התואמות את קריטריוני הקלט", "No sources found": "לא נמצאו מקורות", "No sources were found": "לא נמצאו מקורות", "No updates are available": "אין עדכונים זמינים", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "מנהל החבילות של Node JS. מלא ספריות וכלי עזר אחרים המקיפים את עולם javascript.<br>מכיל: <b>ספריות Node JS וכלי עזר קשורים אחרים</b>\n", "Not available": "<PERSON><PERSON>ן", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "לא נמצא", "Not right now": "לא כעת", "Notes:": "הערות:", "Notification preferences": "העדפות הודעות", "Notification tray options": "אפשרויות מגש ההודעות", "Notification types": "סוגי התראות", "NuPkg (zipped manifest)": "NuPkg (מניפסט מכווץ)", "OK": "אישור", "Ok": "אישור", "Open": "פתח", "Open GitHub": "פתח את <PERSON>", "Open UniGetUI": "פתח את UniGetUI", "Open UniGetUI security settings": null, "Open WingetUI": "פתח את WingetUI", "Open backup location": "פתח את מיקום הגיבוי", "Open existing bundle": "פתח את הצרור הקיים", "Open install location": "פתח מיקום התקנה", "Open the welcome wizard": "פתח את אשף הפתיחה", "Operation canceled by user": "הפעולה בוטלה על ידי המשתמש", "Operation cancelled": "הפעולה בוטלה", "Operation history": "הסטוריית פעולות", "Operation in progress": "הפעולה בעיצומה", "Operation on queue (position {0})...": "פעולה בתור (מיקו<PERSON> {0})...", "Operation profile:": null, "Options saved": "אפשרויות נשמרו", "Order by:": "<PERSON>יין לפי:", "Other": "<PERSON><PERSON><PERSON>", "Other settings": "הגדרות אחרות", "Package": null, "Package Bundles": "צרורות חבילות", "Package ID": "מזהה החבילה", "Package Manager": "מנהל החבילה", "Package Manager logs": "קבצי יומן רישום של מנהל החבילות", "Package Managers": "מנהלי החבילה", "Package Name": "שם החבילה", "Package backup": "גיב<PERSON>י חבילות", "Package backup settings": null, "Package bundle": "צרור חבילות", "Package details": "פרטי החבילה", "Package lists": "רשימות חבילות", "Package management made easy": "ניהול חבילות בקלות", "Package manager": "מנהל החבילה", "Package manager preferences": "העדפות של מנהל החבילות", "Package managers": "מנה<PERSON>י חבילות", "Package not found": "החבילה לא נמצאה", "Package operation preferences": "העדפות פעולת החבילה", "Package update preferences": "העדפות עדכון חבילה", "Package {name} from {manager}": "חביל<PERSON> {name} מתוך {manager}", "Package's default": null, "Packages": "חבילות", "Packages found: {0}": "נמצאו חבילות: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON><PERSON> חלקי", "Password": "סיסמה", "Paste a valid URL to the database": "הדב<PERSON> כתובת אתר חוקית למסד הנתונים", "Pause updates for": "השהה עדכונים ל-", "Perform a backup now": "בצע גיבוי כעת", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "מבצ<PERSON> גיבוי, אנ<PERSON> המתן...", "Periodically perform a backup of the installed packages": "בצע מעת לעת גיבוי של החבילות המותקנות", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "בדו<PERSON> את אפשרויות ההתקנה של חבילה זו ונסה שוב", "Please click on \"Continue\" to continue": "אנא לחץ על \"המשך\" כדי להמשיך", "Please enter at least 3 characters": "נא להזין לפחות 3 תווים", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "שים לב שיית<PERSON><PERSON> שחבילות מסוימות לא יהיו ניתנות להתקנה, עקב מנהלי החבילות הזמינים במחשב זה.", "Please note that not all package managers may fully support this feature": "שימו לב שלא כל מנהלי החבילות עשויים לתמוך בתכונה זו באופן מלא", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "שים לב כי ייתכן שלא ניתן לייצא חבילות ממקורות מסוימים. הן הוצגו באפור ולא ייוצאו.", "Please run UniGetUI as a regular user and try again.": "הפעל את UniGetUI כמשתמש רגיל ונסה שוב.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "אנא עיין בפלט שורת הפקודה או עיין בהסטוריית המבצעים למידע נוסף על הבעיה.", "Please select how you want to configure WingetUI": "בחר כיצד ברצונך להגדיר את WingetUI", "Please try again later": "נסה שוב מאוחר יותר", "Please type at least two characters": "הקלד לפחות שני תווים", "Please wait": "המתן בבקשה", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "נא להמתין בזמן ש-{0} מותקן. יית<PERSON>ן שיופיע חלון שחור. יש להמתין עד לסגירתו.", "Please wait...": "המתן בבקשה...", "Portable": "נייד", "Portable mode": "מצ<PERSON> נייד", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "מנהל החבילות של PowerShell. מצא ספריות וסקריפטים כדי להרחיב את יכולות PowerShell<br>מכיל: <b>מודולים, סקריפטים, <PERSON>mdle<PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "טרום שחרור", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON> חבילות, אנ<PERSON> המתן...", "Proceed at your own risk.": "המשך על אחריותך בלבד.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "כתובת Proxy", "Proxy compatibility table": "טבלת תאימות פרוקסי", "Proxy settings": "הגדרות Proxy", "Proxy settings, etc.": "הגדרות Proxy, ועוד.", "Publication date:": "תאריך פרסום:", "Publisher": "מפרסם", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "מנהל הספרייה של Python. ספריות Python רבות וכלי עזר אחרים הקשורים ל- Python<br>מכיל: <b>ספריות Python וכלי עזר קשורים</b>", "Quit": "צא", "Quit WingetUI": "צא מ-WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "הת<PERSON><PERSON> מחדש", "Reinstall package": "הת<PERSON>ן מחדש את החבילה", "Related settings": "הגדרות קשורות", "Release notes": "הערות שחרור", "Release notes URL": "כתובת האתר של הערות גרסה", "Release notes URL:": "כתובת האתר של הערות גרסה:", "Release notes:": "הערות גרסה", "Reload": "<PERSON><PERSON>ן מחדש", "Reload log": "טען מחדש קובץ יומן רישום", "Removal failed": "הה<PERSON>רה נכשלה", "Removal succeeded": "ההסרה הצליחה", "Remove from list": "הסר מהרשימה", "Remove permanent data": "הסר נתונים קבועים", "Remove selection from bundle": "הסר את הבחירה מהצרור", "Remove successful installs/uninstalls/updates from the installation list": "הסרת התקנות/הסרת התקנה/עדכונים מוצלחים מרשימת ההתקנה", "Removing source {source}": "מ<PERSON><PERSON><PERSON> מקור {source}", "Removing source {source} from {manager}": "מסיר את המקור {source} מ-{manager}", "Repair UniGetUI": null, "Repair WinGet": "תקן את <PERSON>", "Report an issue or submit a feature request": "דווח על בעיה או שלח בקשה לתכונה", "Repository": "<PERSON><PERSON><PERSON><PERSON>", "Reset": "א<PERSON><PERSON>", "Reset Scoop's global app cache": "אפס את מטמון האפליקציות הגלובלי של Scoop", "Reset UniGetUI": "אפס את UniGetUI", "Reset WinGet": "אפס את <PERSON>et", "Reset Winget sources (might help if no packages are listed)": "אפס את מקורות Winget (יכול לעזור אם לא רשומות חבילות)", "Reset WingetUI": "אפס את <PERSON>", "Reset WingetUI and its preferences": "אפס את WingetUI ואת ההעדפות", "Reset WingetUI icon and screenshot cache": "אפס את מטמון הסמלים וצילומי המסך של WingetUI ", "Reset list": "אפ<PERSON> רשימה", "Resetting Winget sources - WingetUI": "אפס את מקורות Winget - WingetUI", "Restart": "הפעל מחדש", "Restart UniGetUI": "הפעל מחדש את UniGetUI", "Restart WingetUI": "הפעל מחדש את <PERSON>", "Restart WingetUI to fully apply changes": "הפעל מחדש את WingetUI כדי להחיל שינויים באופן מלא", "Restart later": "הפעל מחדש מאוחר יותר", "Restart now": "הפעל מחדש עכשיו", "Restart required": "נדרשת הפעלה מחדש", "Restart your PC to finish installation": "הפעל מחדש את המחשב שלך על מנת להשלים את ההתקנה", "Restart your computer to finish the installation": "הפעל מחדש את המחשב שלך על מנת להשלים את ההתקנה", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "נסה שוב", "Retry as administrator": "נסה שוב כמנהל מערכת", "Retry failed operations": "נסה שוב פעולות שנכשלו", "Retry interactively": "נסה שוב באינטראקטיביות", "Retry skipping integrity checks": "נסה שוב תוך דילוג על בדיקות תקינות", "Retrying, please wait...": "מנ<PERSON>ה שוב, אנ<PERSON> המתן...", "Return to top": "חז<PERSON><PERSON> למעלה", "Run": "הרץ", "Run as admin": "הפעל כמנהל מערכת", "Run cleanup and clear cache": "הפעל ניקוי ונקה מטמון", "Run last": "הפעל אחרון", "Run next": "הפעל הבא", "Run now": "הפעל כעת", "Running the installer...": "מריץ את ההתקנה...", "Running the uninstaller...": "מריץ את הסרת ההתקנה...", "Running the updater...": "מריץ את העדכון...", "Save": null, "Save File": "שמור קובץ", "Save and close": "שמור וסגור", "Save as": null, "Save bundle as": "שמור צרור בשם", "Save now": "שמו<PERSON> עכשיו", "Saving packages, please wait...": "שומר חבילות, אנ<PERSON> המתן...", "Scoop Installer - WingetUI": "מת<PERSON><PERSON>ן סקופ - WingetUI", "Scoop Uninstaller - WingetUI": "<PERSON><PERSON><PERSON><PERSON> – WingetUI", "Scoop package": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search": "חַפֵּש", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "ח<PERSON><PERSON> תוכנת שולחן עבודה, הזהר אותי כאשר עדכונים זמינים ואל תעשה דברים חנוניים. אני לא רוצה ש-WingetUI תסבך יתר על המידה, אני רק רוצה <b>חנות אפליקציות</b> פשוטה", "Search for packages": "<PERSON><PERSON><PERSON> חבילות", "Search for packages to start": "<PERSON><PERSON><PERSON> חבילות כדי להתחיל", "Search mode": "מצ<PERSON> חיפוש", "Search on available updates": "<PERSON><PERSON><PERSON> עדכונים זמינים", "Search on your software": "חפש ביישומים שלך", "Searching for installed packages...": "מח<PERSON><PERSON> חבילות מותקנות...", "Searching for packages...": "מחפ<PERSON> חבילות...", "Searching for updates...": "מח<PERSON><PERSON> עדכונים...", "Select": "ב<PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "בחר \"{item}\" כדי להוסיף את הדלי המותאם אישית שלך", "Select a folder": "<PERSON><PERSON><PERSON> תיקיה", "Select all": "<PERSON><PERSON><PERSON>ל", "Select all packages": "בחר את כל החבילות", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<PERSON>ח<PERSON> <PERSON>ק <b>אם אתה יודע מה אתה עושה</b>.", "Select package file": "בחר קובץ חבילה", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "בחר את המקור שברצונך להוסיף:", "Select upgradable packages by default": "בחר חבילו<PERSON> הניתנות לשדרוג כברירת מחדל", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON>ר באילו <b><PERSON><PERSON><PERSON><PERSON><PERSON> חבילות</b> להשתמש ({0}), הגדר את אופן התקנת החבילות, נהל את אופן הטיפול בזכויות מנהל מערכת וכו'.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "שלח לחיצת יד. ממתין למשל לתשובת המאזין... ({0}%)", "Set a custom backup file name": "הגדר שם קובץ גיבוי מותאם אישית", "Set custom backup file name": "הגדר שם קובץ גיבוי מותאם אישית", "Settings": "הגדרות", "Share": "שתף", "Share WingetUI": "שתף את WingetUI", "Share anonymous usage data": "שתף נתוני שימוש אנונימיים", "Share this package": "שתף חבילה זו", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "הצג את UniGetUI במגש המערכת", "Show UniGetUI's version and build number on the titlebar.": "הצג את גרסת UniGetUI בשורת הכותרת", "Show WingetUI": "הצג את WingetUI", "Show a notification when an installation fails": "הצג התראה כאשר התקנה נכשלת", "Show a notification when an installation finishes successfully": "הצג התראה כאשר התקנה מושלמת בהצלחה", "Show a notification when an operation fails": "הצג התראה כאשר פעולה נכשלת ", "Show a notification when an operation finishes successfully": "הצג התראה כאשר פעולה מסתיימת בהצלחה", "Show a notification when there are available updates": "הצג התראה כאשר ישנם עדכונים זמינים", "Show a silent notification when an operation is running": "הצג התראה שקטה כאשר פעולה מתבצעת", "Show details": "הצג פרטים", "Show in explorer": "הצ<PERSON> בסייר", "Show info about the package on the Updates tab": "הצג מידע אודות החבילה בכרטיסיית העדכונים", "Show missing translation strings": "הצג מחרוזות תרגום חסרות", "Show notifications on different events": "הצג התראות על אירועים שונים", "Show package details": "הצג פרטי חבילה", "Show package icons on package lists": "הצג סמלי חבילות ברשימות חבילות", "Show similar packages": "הצג חבילות דומות", "Show the live output": "הצ<PERSON> זמן אמת", "Size": "גודל", "Skip": "דלג", "Skip hash check": "דלג על בדיקת הגיבוב", "Skip hash checks": "דלג על בדיקות גיבוב", "Skip integrity checks": "דלג על בדיקות תקינות", "Skip minor updates for this package": "דלג על עדכונים מינוריים עבור חבילה זו", "Skip the hash check when installing the selected packages": "דלג על בדיקת הגיבוב בעת התקנת החבילות שנבחרו", "Skip the hash check when updating the selected packages": "דלג על בדיקת הגיבוב בעת עדכון החבילות שנבחרו", "Skip this version": "דלג על גרסה זו", "Software Updates": "עדכוני תוכנה", "Something went wrong": "משהו השתבש", "Something went wrong while launching the updater.": "משהו השתבש בעת הפעלת המעדכן.", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Source URL:": "כתובת אתר מקור:", "Source added successfully": "המקור נוסף בהצלחה", "Source addition failed": "הוספת המקור נכשלה", "Source name:": "שם המקור:", "Source removal failed": "הסרת המקור נכשלה", "Source removed successfully": "המקור הוסר בהצלחה", "Source:": "מקור:", "Sources": "מקורות", "Start": "התחל", "Starting daemons...": "מתחיל דמונים...", "Starting operation...": "מתחיל פעולה...", "Startup options": "אפשרויות אתחול", "Status": "סטטוס", "Stuck here? Skip initialization": "תקוע כאן? דלג על אתחול", "Suport the developer": "תמוך במְפַתֵּחַ", "Support me": "תמוך בי", "Support the developer": "תמכו במפתח", "Systems are now ready to go!": "המערכות מוכנות כעת להפעלה!", "Telemetry": "טלמטריה", "Text": "טֶקסט", "Text file": "קובץ טקסט", "Thank you ❤": "תודה לך ❤", "Thank you 😉": "תודה לך 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "מנהל החבילות של Rust.<br>מכיל: <b>ספריות Rust ותוכנות שנכתבו ב-Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "הגיבוי לא יכלול שום קובץ בינארי או נתונים שנשמרו של כל תוכנה.", "The backup will be performed after login.": "הגיב<PERSON>י יתבצע לאחר הכניסה.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "הגיב<PERSON>י יכלול את הרשימה המלאה של החבילות המותקנות ואפשרויות ההתקנה שלהן. גם עדכונים שהתעלמו מהם וגרסאות שדילגתם יישמרו.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "הצרור שאתה מנסה לטעון נראה לא תקין. בדוק את הקובץ ונסה שוב.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "סכום הבדיקה של המתקין אינו עולה בקנה אחד עם הערך הצפוי, ולא ניתן לאמת את האותנטיות של המתקין. אם אתה סומך על המפרסם, {0} החבילה מדלג שוב על בדיקת ה-hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "מנהל החבילות הקלאסי עבור Windows. תמצא שם הכל. <br>מכיל: <b>תוכנה כללית</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "הצרור הנו<PERSON><PERSON><PERSON> ריק. הוסף חבילות כדי להתחיל", "The executable file for {0} was not found": "לא נמצא קובץ ההפעלה עבור {0}", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "החבילות הבאות ייוצאו לקובץ JSON. נתוני משתמש או קבצים בינאריים לא יישמרו.", "The following packages are going to be installed on your system.": "החבילות הבאות עומדות להיות מותקנות על המערכת שלך.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "ההגדרות הבאות יחולו בכל פעם שהחבילה הזו תותקן, תעודכן או תוסר.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "ההגדרות הבאות יחולו בכל פעם שהחבילה הזו תותקן, תעודכן או תוסר. הן יישמרו אוטומטית.", "The icons and screenshots are maintained by users like you!": "הסמלים וצילומי המסך מתוחזקים על ידי משתמשים כמוך!", "The installer authenticity could not be verified.": "לא ניתן לאמת את אותנטיות המתקין.", "The installer has an invalid checksum": "למת<PERSON>ין יש סכום ביקורת (checksum) לא תקין", "The installer hash does not match the expected value.": "ה-hash של המתקין אינו תואם לערך הצפוי.", "The local icon cache currently takes {0} MB": "מטמון הסמלים המקומי תופס כעת {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "המטרה העיקרית של פרויקט זה היא ליצור ממשק משתמש אינטואיטיבי עבור מנהלי חבילות CLI הנפוצים ביותר עבור Windows, כגון Winget ו-Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "החבילה \"{0}\" לא נמצאה במנהל החבילות \"{1}\"", "The package bundle could not be created due to an error.": "לא ניתן היה ליצור את צרור החבילות עקב שגיאה.", "The package bundle is not valid": "צרור החבילות אינו תקף", "The package manager \"{0}\" is disabled": "מנהל החבילות \"{0}\" מושבת", "The package manager \"{0}\" was not found": "מנהל החבילות \"{0}\" לא נמצא", "The package {0} from {1} was not found.": "החבילה {0} מ-{1} לא נמצאה.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "החבילות המפורטות כאן לא יילקחו בחשבון בעת ​​בדיקת עדכונים. לחץ עליהם פעמיים או לחץ על הכפתור בצד ימין שלהם כדי להפסיק להתעלם מהעדכונים שלהם.", "The selected packages have been blacklisted": "החבילות שנבחרו הוכנסו לרשימה השחורה", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "גודל הגיבוי מוערך בפחות מ-1MB.", "The source {source} was added to {manager} successfully": "המקור {source} נוסף ל-{manager} בהצלחה", "The source {source} was removed from {manager} successfully": "המקור {source} הוסר מ-{manager} בהצלחה", "The system tray icon must be enabled in order for notifications to work": "יש להפעיל את סמל המגש כדי שההתראות יעבדו", "The update process has been aborted.": "תהליך העדכון בוטל.", "The update process will start after closing UniGetUI": "תהליך העדכון יתחיל לאחר סגירת UniGetUI", "The update will be installed upon closing WingetUI": "העדכון יותקן עם סגירת WingetUI", "The update will not continue.": "העדכון לא ימשיך.", "The user has canceled {0}, that was a requirement for {1} to be run": "המשת<PERSON><PERSON> ביטל את {0}, שהיה דרוש להפעלת {1}", "There are no new UniGetUI versions to be installed": "אין גרסאות חדשות של UniGetUI להתקנה", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "יש פעולות שוטפות. יציאה מ-WingetUI עלולה לגרום להם להיכשל. האם אתה רוצה להמשיך?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "יש כמה סרטונים מעולים ביוטיוב שמציגים את WingetUI ואת היכולות שלו. אתה יכול ללמוד טריקים וטיפים שימושיים!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "ישנן שתי סיבות עיקריות לא להפעיל את WingetUI כמנהל: הראשונה היא שמנהל החבילות של Scoop עלול לגרום לבעיות עם פקודות מסוימות כאשר הוא פועל עם זכויות מנהל. השני הוא שהפעלת WingetUI כמנהל מערכת פירושה שכל חבילה שתורד תופעל כמנהל (וזה לא בטוח). זכור שאם אתה צריך להתקין חבילה מסוימת כמנהל, אתה תמיד יכול ללחוץ לחיצה ימנית על הפריט -> התקן/עדכן/הסר כמנהל.", "There is an error with the configuration of the package manager \"{0}\"": "יש שגיאה בהגדרות של מנהל החבילות \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "יש התקנה שמתבצעת כעת. אם תסגור את WingetUI, ההתקנה עלולה להיכשל ולגרום לתוצאות בלתי צפויות. האם אתה עדיין רוצה לצאת מ-WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "הן התוכניות האחראיות על התקנה, עדכון והסרה של חבילות.", "Third-party licenses": "רישיונות צד שלישי", "This could represent a <b>security risk</b>.": "זה יכול לייצג <b>סיכ<PERSON><PERSON> ביטחוני</b>.", "This is not recommended.": "אין זה מומלץ.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "זה כנראה נובע מהעובדה שהחבילה שנשלחת הוסרה, או פורסמה במנהל חבילות שלא הפעלת. המזהה שהתקבל הוא {0}", "This is the <b>default choice</b>.": "זוהי <b>בר<PERSON>ר<PERSON> המחדל</b>.", "This may help if WinGet packages are not shown": "זה אמור לעזור אם חבילות WinGet לא מוצגות", "This may help if no packages are listed": "פעולה זו עשויה לעזור אם אין חבילות שמופיעות", "This may take a minute or two": "זה עשוי לקחת דקה או שתיים", "This operation is running interactively.": "פעולה זו פועלת באינטראקטיביות.", "This operation is running with administrator privileges.": "פעולה זו פועלת עם הרשאות מנהל מערכת.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "ניתן לעדכן את החבילה הזו", "This package can be updated to version {0}": "ניתן לעדכן חבילה זו לגרסה {0}", "This package can be upgraded to version {0}": "חבילה זו ניתנת לשדרוג לגרסה {0}", "This package cannot be installed from an elevated context.": "לא ניתן להתקין חבילה זו מתוך הקשר מוגבה.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "לחבילה הזו אין צילומי מסך או שחסר לו הסמל? תרום ל-WingetUI על ידי הוספת הסמלים וצילומי המסך החסרים למסד הנתונים הפתוח והציבורי שלנו.", "This package is already installed": "חבילה זו כבר מותקנת", "This package is being processed": "חבילה זו נמצאת בעיבוד", "This package is not available": "חבילה זו אינה זמינה", "This package is on the queue": "החבילה הזו נמצאת בתור", "This process is running with administrator privileges": "תהליך זה פועל עם הרשאות מנהל", "This project has no connection with the official {0} project — it's completely unofficial.": "לפרויקט זה אין קשר לפרויקט הרשמי של {0} - הוא לא רשמי לחלוטין.", "This setting is disabled": "הגדרה זו מושבתת", "This wizard will help you configure and customize WingetUI!": "אשף זה יעזור לך לקבוע תצורה ולהתאים אישית את WingetUI!", "Toggle search filters pane": "החלף את חלונית מסנני החיפוש", "Translators": "מתרגמים", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "הקלד כאן את השם ואת כתובת האתר של המקור שברצונך להוסיף, מופרדים ברווח.", "Unable to find package": "לא מצליח למצוא חבילה", "Unable to load informarion": "לא ניתן לטעון מידע", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI אוסף נתוני שימוש אנונימיים על מנת לשפר את חוויית המשתמש.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI אוסף נתוני שימוש אנונימיים אך ורק לצורך הבנת חוויית המשתמש ושיפורה.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI זיהה קיצור דרך חדש בשולחן העבודה שניתן למחוק אוטומטית.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI זיהה את קיצורי הדרך הבאים שניתן להסיר אוטומטית בעדכונים עתידיים", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI זיהה {0} קיצורי דרך חדשים בשולחן העבודה שניתן למחוק אוטומטית.", "UniGetUI is being updated...": "UniGetUI מתעדכן...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI אינו קשור לאף אחד ממנהלי החבילות התואמות. UniGetUI הוא פרויקט עצמאי.", "UniGetUI on the background and system tray": "UniGetUI ברקע ובמגש המערכת", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI דורש את {0} כדי לפעול, אך לא נמצא במערכת שלך.", "UniGetUI startup page:": "דף הפתיחה של UniGetUI:", "UniGetUI updater": "עדכון UniGetUI", "UniGetUI version {0} is being downloaded.": "UniGetUI גרסה {0} מורדת כעת.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} מוכן להתקנה.", "Uninstall": "הסר", "Uninstall Scoop (and its packages)": "הסר <PERSON><PERSON> (ואת החבילות שלו)", "Uninstall and more": null, "Uninstall and remove data": "הסר התקנה והסר נתונים", "Uninstall as administrator": "הסר כמנהל מערכת", "Uninstall canceled by the user!": "הסרת ההתקנה בוטלה על ידי המשתמש!", "Uninstall failed": "הסרת ההתקנה נכשלה", "Uninstall options": null, "Uninstall package": "ה<PERSON>ר חבילה", "Uninstall package, then reinstall it": "הסר את החבילה ולאחר מכן התקן אותה מחדש", "Uninstall package, then update it": "הסר את החבילה ולאחר מכן עדכן אותה", "Uninstall previous versions when updated": null, "Uninstall selected packages": "הסר את החבילות המסומנות", "Uninstall selection": null, "Uninstall succeeded": "הסרת ההתקנה הצליחה", "Uninstall the selected packages with administrator privileges": "הסר את התקנת החבילות שנבחרו עם הרשאות מנהל", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "חבילות הניתנות להסרה עם המקור הרשום כ\"{0}\" אינן מתפרסמות בשום מנהל חבילות, כך שאין מידע זמין להצגה עליהן.", "Unknown": "לא ידוע", "Unknown size": "גודל לא ידוע", "Unset or unknown": "לא מוגדר או לא ידוע", "Up to date": "מעוד<PERSON>ן", "Update": "עדכון", "Update WingetUI automatically": "עדכן את WingetUI באופן אוטומטי", "Update all": "ע<PERSON><PERSON><PERSON>כל", "Update and more": null, "Update as administrator": "ע<PERSON><PERSON><PERSON> כמנהל", "Update check frequency, automatically install updates, etc.": "תדירות בדיקת עדכונים, התקנה אוטומטית של עדכונים ועוד.", "Update date": "תאריך עדכון", "Update failed": "עד<PERSON><PERSON><PERSON> נכשל", "Update found!": "נמצא עדכון!", "Update now": "עד<PERSON><PERSON> כעת", "Update options": null, "Update package indexes on launch": "עדכן את אינדקסי החבילות בעת ההרצה", "Update packages automatically": "עד<PERSON><PERSON> חבילות באופן אוטומטי", "Update selected packages": "עדכן את החבילות המסומנות", "Update selected packages with administrator privileges": "עדכן את החבילות המסומנות עם הרשאות מנהל מערכת", "Update selection": null, "Update succeeded": "העד<PERSON><PERSON><PERSON> הצליח", "Update to version {0}": "עד<PERSON><PERSON> לגרסה {0}", "Update to {0} available": "עד<PERSON><PERSON><PERSON> לגרסה{0} ז<PERSON><PERSON>ן", "Update vcpkg's Git portfiles automatically (requires Git installed)": "עדכן את קובצי הפורט של vcpkg באופן אוטומטי (דורש התקנת Git)", "Updates": "עדכונים", "Updates available!": "עדכונים זמינים!", "Updates for this package are ignored": "עדכונים עבור חבילה זו נחסמו", "Updates found!": "נמצאו עדכונים!", "Updates preferences": "העדפות עדכונים", "Updating WingetUI": "עדכון WingetUI", "Url": "כתובת אתר", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "השתמש ב-WinGet מאגד מדור קודם במקום ב- PowerShell CMDLets", "Use a custom icon and screenshot database URL": "השתמש בסמל מותאם אישית ובכתובת אתר של מסד נתונים צילומי מסך", "Use bundled WinGet instead of PowerShell CMDlets": "השתמש ב-WinGet המצורף במקום ב-PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "השתמש בגרסת WinGet הארוזה במקום גרסת ה-WinGet של המערכת", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "השתמש ב-<PERSON><PERSON><PERSON> המותקן במקום המצורף (דורש הפעלה מחדש של האפליקציה)", "Use system Chocolatey": "השתמש במערכת Chocolatey", "Use system Chocolatey (Needs a restart)": "השתמש ב- <PERSON><PERSON> של המערכת (דורש הפעלה מחדש)", "Use system Winget (Needs a restart)": "השתמש ב- <PERSON>et של המערכת (דורש הפעלה מחדש)", "Use system Winget (System language must be set to english)": "השתמש במערכת Winget (יש להגדיר את שפת המערכת לאנגלית)", "Use the WinGet COM API to fetch packages": "השתמש ב-WinGet COM API כדי להביא חבילות", "Use the WinGet PowerShell Module instead of the WinGet COM API": "השתמש במודול ה-WinGet של PowerShell במקום בממשק הרשת של WinGet", "Useful links": "קישורים שימושיים", "User": "משת<PERSON>ש", "User interface preferences": "העדפות ממשק משתמש", "User | Local": "משתמש | מקומי", "Username": "שם-משת<PERSON><PERSON>", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "השימוש ב-Winget<PERSON> מרמז על קבלת רישיון GNU Lesser General Public License v2.1 License", "Using WingetUI implies the acceptation of the MIT License": "שימוש ב-<PERSON><PERSON><PERSON> מרמז על קבלת רישיון MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "לא נמצא נתיב השורש של vcpkg. הגדר את משתנה הסביבה %VCPKG_ROOT% או הגדר אותו מהגדרות UniGetUI", "Vcpkg was not found on your system.": "vcpkg לא נמצא במערכת שלך.", "Verbose": "מפורט", "Version": "גרסה", "Version to install:": "גרסה להתקנה:", "Version:": null, "View GitHub Profile": "הצג את פרופיל GitHub", "View WingetUI on GitHub": "הצג את WingetUI ב- GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "הצג את קוד המקור של WingetUI. משם, אתה יכול לדווח על באגים או להציע תכונות, או אפילו לתרום ישירות לפרויקט WingetUI", "View mode:": "מצב תצוגה:", "View on UniGetUI": "הצג ב-UniGetUI", "View page on browser": "צפה בדף בדפדפן", "View {0} logs": "הצג לוגים של {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "המתן לחיבור המכשיר לאינטרנט לפני ניסיון לבצע משימות הדורשות חיבור לרשת.", "Waiting for other installations to finish...": "ממתין לסיום התקנות אחרות...", "Waiting for {0} to complete...": "ממתין להשלמת {0}...", "Warning": "אזהרה", "Warning!": "אזהרה!", "We are checking for updates.": "אנו בודקים אם קיימים עדכונים.", "We could not load detailed information about this package, because it was not found in any of your package sources": "לא יכולנו לטעון מידע מפורט על חבילה זו, מכיוון שהיא לא נמצאת באף אחד ממקורות החבילה שלך.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "לא היתה לנו אפשרות לטעון מידע מפורט אודות חבילה זו, מכיוון שהיא לא הותקנה ממנהל חבילות זמין.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "איננו יכולים לבצע {action} ל-{package}. נסה שוב מאוחר יותר. לחץ על \"{showDetails}\" כדי לצפות ביומן הרישום של ההתקנה.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "איננו יכולים לבצע {action} ל-{package}. נסה שוב מאוחר יותר. לחץ על \"{showDetails}\" כדי לצפות ביומן הרישום של הסרת ההתקנה.", "We couldn't find any package": "לא מצאנו שום חבילה", "Welcome to WingetUI": "ברוכים הבאים ל-WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "כאשר מתגלים קיצורי דרך חדשים, הם יימחקו אוטומטית במקום להציג תיבת דו-שיח זו.", "Which backup do you want to open?": null, "Which package managers do you want to use?": "באילו מנה<PERSON>י חבילות ברצונך להשתמש?", "Which source do you want to add?": "איזה מקור אתה רוצה להוסיף?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "בעוד שניתן להשתמש ב-Winget בתוך WingetUI, ניתן להשתמש ב-WingetUI עם מנהלי חבילות אחרים, מה שעלול לבלבל. בעבר, WingetUI תוכנן לעבוד רק עם Winget, אבל זה לא נכון יותר, ולכן WingetUI לא מייצג את מה שהפרויקט הזה שואף להפוך.", "WinGet could not be repaired": "לא ניתן היה לתקן את WinGet", "WinGet malfunction detected": "זוהתה תקלה ב-WinGet", "WinGet was repaired successfully": "WinGet תוקן בהצלחה", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - ה<PERSON><PERSON> מעודכן", "WingetUI - {0} updates are available": "WingetUI - {0} עדכונים זמינים", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "דף הבית של WingetUI", "WingetUI Homepage - Share this link!": "דף הבית של WingetUI - שתף את הקישור הזה!", "WingetUI License": "רישי<PERSON>ן WingetUI", "WingetUI Log": "WingetUI יומן", "WingetUI Repository": "מא<PERSON>ר WingetUI", "WingetUI Settings": "הגדרות של WingetUI", "WingetUI Settings File": "קובץ ההגדרות של WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI משתמש בספריות הבאות. בלעדיהם, WingetUI לא היה אפשרי.", "WingetUI Version {0}": "WingetUI גרסה {0}", "WingetUI autostart behaviour, application launch settings": "התנהגות הפעלה אוטומטית של WingetUI, הגדרות ההפעלה של WingetUI ", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI יכול לבדוק אם לתוכנה שלך יש עדכונים זמינים, ולהתקין אותם באופן אוטומטי אם תרצה בכך", "WingetUI display language:": "שפת התצוגה של WingetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI הופעל כמנהל, וזה לא מומלץ. בעת הפעלת WingetUI כמנהל מערכת, לכל פעולה שתושק מ-WingetUI תהיה הרשאות מנהל. אתה עדיין יכול להשתמש בתוכנית, אך אנו ממליצים בחום לא להפעיל את WingetUI עם הרשאות מנהל.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI תורגם ליותר מ-40 שפות הודות למתרגמים המתנדבים. תודה לך 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI לא תורגם בתרגום מכונה! המשתמשים הבאים היו אחראים על התרגומים:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI הוא יישום שמקל על ניהול התוכנה שלך, על ידי מתן ממשק גרפי הכל-באחד עבור מנהלי חבילות שורת הפקודה שלך.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "שם WingetUI משתנה על מנת להדגיש את ההבדל בין WingetUI (הממ<PERSON><PERSON> שבו אתה משתמש כרגע) ל-Winget (מנהל חבילות שפותח על ידי מיקרוסופט שאינני קשור אליו)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI מתעדכן. בסיום, WingetUI יפעיל את עצמו מחדש", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI הוא בחינם, והוא יהיה בחינם לנצח. ללא פרסומות, ללא כרטיס אשראי, ללא גרסת פרימיום. 100% חינם, לנצח.", "WingetUI log": "יומן רישום של WingetUI", "WingetUI tray application preferences": "העדפות יישומי מגש WingetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI משתמש בספריות הבאות. בלעדיהם, WingetUI לא היה אפשרי.", "WingetUI version {0} is being downloaded.": "הורדה של WingetUI גרסה {0}.", "WingetUI will become {newname} soon!": "WingetUI יהפוך ל-{newname} בקרוב!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI לא יחפש עדכונים מעת לעת. הם עדיין ייבדקו בעת ההשקה, אך לא תנתן אזהרה לגביהם.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI יציג הנחית UAC בכל פעם שחבילה דורשת הרשאות התקנה גבוהות.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI ייקרא בקרוב {newname}. זה לא ייצג שום שינוי באפליקציה. אני (המפתח) אמשיך בפיתוח הפרויקט הזה כפי שאני עושה כרגע, אבל בשם אחר.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI לא היה אפשרי ללא העזרה של התורמים היקרים שלנו. בדוק את פרופילי GitHub שלהם, WingetUI לא היה אפשרי בלעדיהם!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI לא היה קורה ללא עזרת התורמים. תודה לכולכם 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} מו<PERSON><PERSON> להתקנה.", "Write here the process names here, separated by commas (,)": null, "Yes": "כן", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "כרגע מותקנת גרסה {0}", "You have installed WingetUI Version {0}": "התקנת את גירסת WingetUI {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "ייתכן שתצטרך להתקין את {pm} כדי להשתמש בו עם WingetUI.", "You may restart your computer later if you wish": "תוכל להפעיל מחדש את המחשב מאוחר יותר אם תרצה בכך", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "תתבקש פעם אחת בלבד, וזכויות מנהל מערכת יוענקו לחבילות המבקשות אותן.", "You will be prompted only once, and every future installation will be elevated automatically.": "תתבקש רק פעם אחת, וזכויות מנהל יינתנו לחבילות שיבקשו אותן.", "You will likely need to interact with the installer.": "כנראה שתצטרך לבצע פעולות עם המתקין.", "[RAN AS ADMINISTRATOR]": "[רץ כמנהל]", "buy me a coffee": "קנו לי קפה", "extracted": "מחולץ", "feature": "תכונה", "formerly WingetUI": "לשעבר WingetUI", "homepage": "דף הבית", "install": "הת<PERSON>ן", "installation": "התקנה", "installed": "מותקנת", "installing": "מת<PERSON><PERSON>ן", "library": "ספרייה", "mandatory": null, "option": "אפשרות", "optional": null, "uninstall": "הסר התקנה", "uninstallation": "הסרת התקנה", "uninstalled": "<PERSON>ו<PERSON><PERSON>", "uninstalling": "מסיר התקנה", "update(noun)": "עדכון", "update(verb)": "עד<PERSON><PERSON>", "updated": "עוד<PERSON>ן", "updating": "מע<PERSON><PERSON><PERSON>", "version {0}": "גרס<PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "הסרה של {0}", "{0} aborted": "{0} בוטלה", "{0} can be updated": "אפ<PERSON>ר לעדכן את {0}", "{0} can be updated to version {1}": "ניתן לעדכן את {0} לגרסה {1}", "{0} days": "{0} ימים", "{0} desktop shortcuts created": "נוצרו {0} קיצורי דרך בשולחן העבודה", "{0} failed": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} has been installed successfully.": "{0} הות<PERSON><PERSON> בהצלחה.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} הות<PERSON><PERSON> בהצלחה. מומלץ להפעיל מחדש את UniGetUI כדי להשלים את ההתקנה", "{0} has failed, that was a requirement for {1} to be run": "{0} נ<PERSON><PERSON><PERSON>, והוא היה דרוש להפעלת {1}", "{0} homepage": "דף הבית של {0}", "{0} hours": "{0} שעות", "{0} installation": "התקנה של {0}", "{0} installation options": "{0} אפשרויות התקנה", "{0} installer is being downloaded": "המת<PERSON><PERSON>ן של {0} מורד כעת", "{0} is being installed": "{0} מו<PERSON><PERSON><PERSON> כעת", "{0} is being uninstalled": "{0} מוס<PERSON> כעת", "{0} is being updated": "מעד<PERSON>ן את {0}", "{0} is being updated to version {1}": "{0} מת<PERSON><PERSON><PERSON><PERSON> לגרסה {1}", "{0} is disabled": "{0} בלתי אפשרי", "{0} minutes": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} months": "{0} חוד<PERSON>ים", "{0} packages are being updated": "מעדכ<PERSON> {0} חבילות", "{0} packages can be updated": "ע<PERSON><PERSON><PERSON><PERSON> זמין עבור {0} חבילות", "{0} packages found": "נמצאו {0} חבילות", "{0} packages were found": "נמצאו {0} חבילות", "{0} packages were found, {1} of which match the specified filters.": "נמצאו {0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {1} מהן תואמות למסננים שצוינו.", "{0} settings": "הגדרות {0}", "{0} status": "סטטוס {0}", "{0} succeeded": "{0} הצליח/ה", "{0} update": "עד<PERSON><PERSON><PERSON> של {0}", "{0} updates are available": "{0} עדכונים זמינים", "{0} was {1} successfully!": "{0} היה {1} בהצלחה!", "{0} weeks": "{0} שבועות", "{0} years": "{0} שנים", "{0} {1} failed": "נכשל {0} {1}", "{package} Installation": "{package} התקנה", "{package} Uninstall": "{package} הסרת ההתקנה", "{package} Update": "{package} עדכון", "{package} could not be installed": "לא ניתן היה להתקין את {package}", "{package} could not be uninstalled": "לא ניתן היה להסיר את ההתקנה של {package}", "{package} could not be updated": "לא ניתן היה לעדכן את {package}", "{package} installation failed": "התקנת {package} נכשלה", "{package} installer could not be downloaded": "לא ניתן להוריד את המתקין של {package}", "{package} installer download": "הורדת מתקין {package}", "{package} installer was downloaded successfully": "המת<PERSON><PERSON>ן של {package} הורד בהצלחה", "{package} uninstall failed": "הסרת ההתקנה של {package} נכשלה", "{package} update failed": "עדכון {package} נכשל", "{package} update failed. Click here for more details.": "עדכון {package} נכשל. לחץ כאן לפרטים נוספים.", "{package} was installed successfully": "{package} הות<PERSON>ן בהצלחה", "{package} was uninstalled successfully": "ההתקנה של {package} הוסרה בהצלחה", "{package} was updated successfully": "{package} עוד<PERSON><PERSON> בהצלחה", "{pcName} installed packages": "חבילות מותקנות של {pcName}", "{pm} could not be found": "לא ניתן למצוא את {pm}", "{pm} found: {state}": "{pm} נמצאו: {state}", "{pm} is disabled": "{pm} מושבת", "{pm} is enabled and ready to go": "{pm} מופעל ומוכן להפעלה", "{pm} package manager specific preferences": "הגדרות ספציפיות למנהל החבילות {pm}", "{pm} preferences": "העדפות {pm}", "{pm} version:": "{pm} גרסה:", "{pm} was not found!": "{pm} לא נמצא!"}