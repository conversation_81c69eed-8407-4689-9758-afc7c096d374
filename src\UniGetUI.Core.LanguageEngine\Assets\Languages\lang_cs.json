{"\"{0}\" is a local package and can't be shared": "\"{0}\" je lokální balíček a nemůže být nasdílen", "\"{0}\" is a local package and does not have available details": "\"{0}\" je lokální balíček a nemá dostupné detaily", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" je lokální balíček a není kompatibilní s touto <PERSON>", "(Last checked: {0})": "(Na<PERSON><PERSON><PERSON> zkontrolováno: {0})", "(Number {0} in the queue)": "(<PERSON><PERSON><PERSON><PERSON> ve frontě: {0})", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@panther7, @mlisko, @xtorlukas", "0 packages found": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "0 updates found": "Nenalezena žádná aktualizace", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 den", "1 hour": "1 hodina", "1 month": "1 měsíc", "1 package was found": "1 balíček nalezen", "1 update is available": "1 dostupná aktualizace", "1 week": "1 týden", "1 year": "1 rok", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Přejděte na stránku „{0}“ nebo „{1}“.", "2 - Warnings": "2 - Varování", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete přidat do balíčku, a zaškrtněte jejich políč<PERSON> vlevo.", "3 - Information (less)": "3 - Informace (méně)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON> výbě<PERSON>, k<PERSON><PERSON> chcete přidat do balíčku, vyhledejte na panelu nástrojů možnost „{0}“ a klikněte na ni.", "4 - Information (more)": "4 - Informace (více)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. V<PERSON>še balíčky budou přidány do balíčku. Můžete pokračovat v přidávání balíčků nebo balíček exportovat.", "5 - information (debug)": "5 - <PERSON><PERSON><PERSON> (ladění)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Oblíbený správce knihoven v jazyce C/C++. Plný knihoven a dalších nástrojů souvisejících s C/C++.<br>Obsahuje: <b>C/C++ knihovny a související nástroje</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Repozitář plný nástrojů a spustitelných souborů navržených s ohledem na ekosystém .NET společnosti Microsoft.<br>Obsahuje: <b>.NET související nástroje a skripty</b>\n", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Repozitář plný nástrojů navržených s ohledem na ekosystém .NET společnosti Microsoft. <br>Obsahuje: <b>Nástroje související s .NET</b>", "A restart is required": "Je vyžadován restart", "Abort install if pre-install command fails": "Přer<PERSON><PERSON><PERSON> instalaci, pokud předinstalační příkaz selže", "Abort uninstall if pre-uninstall command fails": "Př<PERSON><PERSON><PERSON><PERSON>, pokud předinstalační příkaz selže", "Abort update if pre-update command fails": "Přerušit aktualizaci, pokud předinstalační příkaz selže", "About": "O aplikaci", "About Qt6": "O Qt6", "About WingetUI": "O UniGetUI", "About WingetUI version {0}": "O UniGetUI verze {0}", "About the dev": "O vývojáři", "Accept": "Př<PERSON><PERSON>mout", "Action when double-clicking packages, hide successful installations": "<PERSON><PERSON><PERSON> po <PERSON>, skrytí úspěšných instalací", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "Přidat zdroj do {0}", "Add a timestamp to the backup file names": "Přidat časové razítko (timestamp) do názvu souboru zálohy", "Add a timestamp to the backup files": "Přidat časové razítko (timestamp) do souborů záloh", "Add packages or open an existing bundle": "P<PERSON><PERSON>t balíčky nebo otevřít již stávající sadu", "Add packages or open an existing package bundle": "P<PERSON><PERSON>t balíčky nebo otevřít stávající sadu bal<PERSON>", "Add packages to bundle": "Přidat balíčky do sady", "Add packages to start": "Př<PERSON><PERSON><PERSON>", "Add selection to bundle": "Přidat výběr do sady", "Add source": "Přidat zdroj", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Přidat aktualizace, k<PERSON><PERSON> s hlášením „nebyla nalezena žádná použitelná aktualizace“, do seznamu ignorovaných aktualizací.", "Adding source {source}": "Přidávání zdroje {source}", "Adding source {source} to {manager}": "Přidáván<PERSON> zdroje {source} do {manager}", "Addition succeeded": "Úspěšně p<PERSON>id<PERSON>", "Administrator privileges": "Oprávnění správce", "Administrator privileges preferences": "Volby oprávnění správce", "Administrator rights": "Opravnění správce", "Administrator rights and other dangerous settings": "Oprávnění správce a další nebezpečná nastavení", "Advanced options": "Pokročil<PERSON> m<PERSON>", "All files": "Všechny soubory", "All versions": "Všechny verze", "Allow changing the paths for package manager executables": "Povolit změnu cest pro spustitelné soubory sprá<PERSON>ce balí<PERSON>ů", "Allow custom command-line arguments": "Povolit vlastní argumenty příkazového řádku", "Allow importing custom command-line arguments when importing packages from a bundle": "Povolit import vlastních argumentů příkazového řádku při importování balíčků ze sady", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Povolit import vlastních předinstalačních a poinstalačních příkazů při importu balíčků ze sady", "Allow package operations to be performed in parallel": "Umožnit paralelní provádění operací s balíčky", "Allow parallel installs (NOT RECOMMENDED)": "Povolit paralelní instalování (NEDOPORUČUJE SE)", "Allow pre-release versions": "Povolit předběžné verze", "Allow {pm} operations to be performed in parallel": "Povolit paralelní běh operací {pm}", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativně můžete také nainstalovat {0} spuštěním následujícího příkazu v příkazovém řádku prostředí Windows PowerShell:", "Always elevate {pm} installations by default": "<PERSON><PERSON><PERSON> s<PERSON> {pm} instalaci s oprávněním správce", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {pm} s oprávněn<PERSON><PERSON> správce", "An error occurred": "<PERSON><PERSON><PERSON>ba", "An error occurred when adding the source: ": "Nastala chyba při přidávání zdroje:", "An error occurred when attempting to show the package with Id {0}": "Při pokusu o zobrazení balíčku s Id {0}, do<PERSON><PERSON> k ch<PERSON>bě.", "An error occurred when checking for updates: ": "Nastala chyba při kontrole aktualizací:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "Při načítání zálohy došlo k chybě:", "An error occurred while logging in: ": "Při přihlašování došlo k chybě:", "An error occurred while processing this package": "Při zpracování tohoto balíčku došlo k chybě", "An error occurred:": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>:", "An interal error occurred. Please view the log for further details.": "Došlo k interní chybě. Další podrobnosti naleznete v protokolu.", "An unexpected error occurred:": "<PERSON><PERSON><PERSON>ek<PERSON>vaná chyba:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Při pokusu o opravu WinGet došlo k neočekávanému problému. Zkuste to prosím později", "An update was found!": "Byla nalezena aktualizace!", "Android Subsystem": "Subsystém Android", "Another source": "<PERSON><PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Všechny nové zkratky vytvořené během instalace nebo aktualizace budou automaticky odstraněny, místo aby se při jejich prvním zjištění zobrazil potvrzovací dotaz.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Zkratky vytvořené nebo upravené mimo rozhraní UniGetUI budou ignorovány. Budete je moci přidat pomocí tlačítka {0}.", "Any unsaved changes will be lost": "Neuložené změny budou ztraceny", "App Name": "Název aplikace", "Appearance": "Vzhled", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vymazání úspěšných instalací", "Application theme:": "Motiv aplikace:", "Apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Architecture to install:": "Architektura:", "Are these screenshots wron or blurry?": "J<PERSON>u tyto screenshoty špatné či roz<PERSON>zané?", "Are you really sure you want to enable this feature?": "Opravdu chcete tuto funkci povolit?", "Are you sure you want to create a new package bundle? ": "Opravdu chcete vytvořit novou sadu balíč<PERSON>ů?", "Are you sure you want to delete all shortcuts?": "Opravdu chcete odstranit všechny zkratky?", "Are you sure?": "Jste si jistí?", "Ascendant": "Vzestupně", "Ask for administrator privileges once for each batch of operations": "Požádat o oprávnění správce pro každou operaci", "Ask for administrator rights when required": "Požádat o opravnění spr<PERSON>vce, jen k<PERSON> je to potřeba", "Ask once or always for administrator rights, elevate installations by default": "Jakým způsobem vyžadovat oprávnění správce", "Ask only once for administrator privileges": "Požádat o oprávnění správce pouze jednou", "Ask only once for administrator privileges (not recommended)": "Požádat o oprávnění správce pouze jednou (nedoporučuje se)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Zeptat se na smazání zástupců na ploše vytvořených během instalace nebo aktualizace", "Attention required": "Nutná pozornost", "Authenticate to the proxy with an user and a password": "Ověření k proxy serveru pomocí uživatele a hesla", "Author": "Autor", "Automatic desktop shortcut remover": "Automatické mazání zástupců z plochy", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Automatické uložení seznamu všech nainstalovaných balíčků pro jejich snadné obnovení", "Automatically save a list of your installed packages on your computer.": "Automatické uložení seznamu nainstalovaných balíčků do počítače.", "Autostart WingetUI in the notifications area": "Automatické spuštění UniGetUI do notifikační oblasti", "Available Updates": "Dostupné aktualizace", "Available updates: {0}": "Dostupných aktualizací: {0}", "Available updates: {0}, not finished yet...": "Dostupných aktualizací: {0}, je<PERSON><PERSON><PERSON> nedo<PERSON>...", "Backing up packages to GitHub Gist...": "Zálohování balíčků na GitHub Gist...", "Backup": "Zálohovat", "Backup Failed": "Zálohován<PERSON> se<PERSON>", "Backup Successful": "Zálohování proběhlo úspěšně", "Backup and Restore": "Zálohování a obnovení", "Backup installed packages": "Zálohování nainstalovan<PERSON>ch balíčků", "Backup location": "Umístěn<PERSON>", "Become a contributor": "Staňte se přispěvatelem", "Become a translator": "Staňte se překladatelem", "Begin the process to select a cloud backup and review which packages to restore": "Zahájení procesu výběru cloudové zálohy a přezkoumání bal<PERSON>, které mají být obnov<PERSON>.", "Beta features and other options that shouldn't be touched": "Testovací funkce a další vlastnosti, na které byste neměli sahat", "Both": "Oba", "Bundle security report": "Zpráva o zabezpečení sady", "But here are other things you can do to learn about WingetUI even more:": "Ale jsou tu i da<PERSON><PERSON> v<PERSON>, kter<PERSON> mů<PERSON> ud<PERSON>, abyste se o UniGetUI dozvěděli ještě více:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Vypnutím správce balíčků již nebudete moci zobrazit ani aktualizovat jeho bal<PERSON>.", "Cache administrator rights and elevate installers by default": "Pamatovat si oprávnění správce a v<PERSON><PERSON> spouštět instalace s jeho oprávněním", "Cache administrator rights, but elevate installers only when required": "Pamatovat si oprávnění správce, ale vyžádat si je pouze v případě potřeby", "Cache was reset successfully!": "Mezipaměť byla úspěšně vyčištěna!", "Can't {0} {1}": "Nepovedlo se {0} {1}", "Cancel": "Zrušit", "Cancel all operations": "Zrušit všechny operace", "Change backup output directory": "Změnit výstupní adres<PERSON>", "Change default options": "Změnit výchozí možnosti", "Change how UniGetUI checks and installs available updates for your packages": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak UniGetUI kontroluje a instaluje dostupné aktualizace pro vaše balíčky", "Change how UniGetUI handles install, update and uninstall operations.": "<PERSON><PERSON>v, jak UniGetUI zachází s operacemi instalace, aktualizace a odinstalace", "Change how UniGetUI installs packages, and checks and installs available updates": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jak UniGetUI instaluje balíčky a kontroluje a instaluje jejich aktualizace", "Change how operations request administrator rights": "<PERSON><PERSON><PERSON>, jak operace budou vyžadovat oprávnění správce", "Change install location": "Změnit místo instalace", "Change this": "<PERSON><PERSON><PERSON><PERSON><PERSON> toto", "Change this and unlock": "Změnit toto a odemčít", "Check for package updates periodically": "Pravidelně kontrolovat aktualizace", "Check for updates": "Zkontrolovat aktualizace", "Check for updates every:": "Kontrolovat aktualizace každých:", "Check for updates periodically": "Pravidelně kontrolovat aktualizace", "Check for updates regularly, and ask me what to do when updates are found.": "Pravidelně kontroluje aktualizace a po nalezení aktualizací se zeptá, co udělat.", "Check for updates regularly, and automatically install available ones.": "Check for updates regularly, and automatically install available ones.", "Check out my {0} and my {1}!": "<PERSON>uk<PERSON>ěte na můj {0} a můj {1}!", "Check out some WingetUI overviews": "Podívejte se na některé přehledy UniGetUI", "Checking for other running instances...": "Kontroluji další bežící instance...", "Checking for updates...": "Kontroluji aktualizace...", "Checking found instace(s)...": "<PERSON><PERSON><PERSON><PERSON><PERSON> instance...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON><PERSON>, kolik operací se má provádět paralelně", "Clear cache": "Vyčistit mezipaměť", "Clear finished operations": "Vymazat dokončené operace", "Clear selection": "Zrušit výběr", "Clear successful operations": "Smazat úspěšné operace", "Clear successful operations from the operation list after a 5 second delay": "Vymazat úspěšné operace po 5 sekundách ze seznamu operací", "Clear the local icon cache": "Vyčistit místní mezipaměť ikonek", "Clearing Scoop cache - WingetUI": "Ma<PERSON><PERSON><PERSON> - UniGetUI", "Clearing Scoop cache...": "Čistím <PERSON> Scoop...", "Click here for more details": "Klikněte zde pro více informací", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Klepnutím na tlačítko Instalace zahájíte proces instalace. Pokud instalaci přeskočíte, nemusí UniGetUI fungovat dle očekávání.", "Close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Zavírat UniGetUI do systémové lišty", "Close WingetUI to the notification area": "Skrýt UniGetUI do notifikační oblasti", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Cloudové zálohování používá soukromý Gist GitHub k uložení seznamu nainstalovaných balíčků.", "Cloud package backup": "Zálohování balíčků v cloudu", "Command-line Output": "Výstup příkazového řádku", "Command-line to run:": "Příkazový řádek pro spuštění:", "Compare query against": "Porovnat dotaz vůči", "Compatible with authentication": "Kompatibilní s ověřením", "Compatible with proxy": "Kompatibilní s proxy", "Component Information": "Informace o komponentách", "Concurrency and execution": "Souběžnost a provádění", "Connect the internet using a custom proxy": "Připojit se na internet pomocí vlastní proxy", "Continue": "Po<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Přispět do repozitáře ikonek a screenshotů", "Contributors": "Přispěvatelé", "Copy": "Zkopírovat", "Copy to clipboard": "Zkopírovat do schránky", "Could not add source": "Nepodařilo se přidat zdroj", "Could not add source {source} to {manager}": "Nepodařilo se přidat zdroj {source} do {manager}", "Could not back up packages to GitHub Gist: ": "Nepodařilo se zálohovat balíčky na GitHub Gist:", "Could not create bundle": "Sadu se nepodařilo vytvořit", "Could not load announcements - ": "Nepodařilo se načíst oznámení -", "Could not load announcements - HTTP status code is $CODE": "Nepodařilo se načíst oznámení - HTTP status kód $CODE", "Could not remove source": "Nepodařilo se odstranit zdroj", "Could not remove source {source} from {manager}": "Nepodařilo se odebrat zdroj {source} z {manager}", "Could not remove {source} from {manager}": "Nepodařilo se odebrat {source} z {manager}", "Create .ps1 script": null, "Credentials": "Přihlašovací údaje", "Current Version": "Aktuální verze", "Current status: Not logged in": "Současný stav: N<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Current user": "Aktuální uživatel", "Custom arguments:": "Vlastn<PERSON> argumenty:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Vlastní argumenty příkazového řádku mohou změnit způsob, jakým jsou programy instalovány, aktualizovány nebo odinstalovány, způsobem, který UniGetUI nemůže kontrolovat. Použití vlastních příkazových řádků může poškodit balíčky. Postupujte opatrně.", "Custom command-line arguments:": "Vlastní parametry příkazov<PERSON>:", "Custom install arguments:": "Vlastní argumenty pro instalaci:", "Custom uninstall arguments:": "Vlastní argumenty pro odinstalaci:", "Custom update arguments:": "Vlastní argumenty pro aktualizaci:", "Customize WingetUI - for hackers and advanced users only": "Přizpůsobení UniGetUI - pouze pro hackery a pokročilé uživatele", "DEBUG BUILD": "DEBUG BUILD", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ODMÍTNUTÍ ODPOVĚDNOSTI: ZA STAŽENÉ BALÍČKY NENESEME ODPOVĚDNOST. DBEJTE PROSÍM NA TO, ABYSTE INSTALOVALI POUZE DŮVĚRYHODNÝ SOFTWARE.", "Dark": "Tmavý", "Decline": "Odmítnout", "Default": "Výchozí", "Default installation options for {0} packages": "Výchozí možnosti instalace {0} balíčků", "Default preferences - suitable for regular users": "Výchozí předvolby - vhodné pro běžné uživatele", "Default vcpkg triplet": "Výchozí vcpkg triplet", "Delete?": "<PERSON><PERSON><PERSON><PERSON>?", "Dependencies:": "<PERSON><PERSON><PERSON><PERSON>ti:", "Descendant": "Sestupně", "Description:": "Popis:", "Desktop shortcut created": "Vytvoření zástupce na ploše", "Details of the report:": "Podrobnosti zprávy:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Vývoj je náročný a tato aplikace je zdarma, ale pokud se vám aplikace líbí, tak mi můžete kdykoliv <b>k<PERSON><PERSON> kafe</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Přimá instalace po dvojkliku na položku v záložce „{discoveryTab}“ (místo zobrazení informací o balíčku)", "Disable new share API (port 7058)": "Vypnout nové API sdílení (port 7058)", "Disable the 1-minute timeout for package-related operations": "Vypnutí minutového limitu pro operace související s balíčky", "Disclaimer": "Upozornění", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discover packages": "Obje<PERSON><PERSON><PERSON>", "Distinguish between\nuppercase and lowercase": "Rozlišovat velikost znaků", "Distinguish between uppercase and lowercase": "<PERSON><PERSON><PERSON><PERSON>ovat velké a malé písmena", "Do NOT check for updates": "Nekontrolovat aktualizace", "Do an interactive install for the selected packages": "Provést interaktivní instalaci vybraných balíčků", "Do an interactive uninstall for the selected packages": "Provést interaktivní odinstalaci vybraných balíčků", "Do an interactive update for the selected packages": "Provést interaktivní aktualizaci vybraných balíčků", "Do not automatically install updates when the battery saver is on": "Neinstalovat aktualizace <PERSON>ky, pokud je zapnutý spořič energie", "Do not automatically install updates when the network connection is metered": "Neinstalovat aktualizace automaticky, pokud je síťové připojení <PERSON>o po objemu dat.", "Do not download new app translations from GitHub automatically": "Neaktualizovat automaticky jazykové soubory (překlady)", "Do not ignore updates for this package anymore": "Neignorovat aktualizace tohoto balíčku", "Do not remove successful operations from the list automatically": "Neodstraňovat automaticky úspěšné operace ze seznamu", "Do not show this dialog again for {0}": "Nezobrazujte znovu tento dialog pro {0}", "Do not update package indexes on launch": "Neaktualizovat indexy balíčků po spuštění", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Souhlasíte s tím, že UniGetUI shromažďuje a odesílá anonymní statistiky o používání, a to výhradně za účelem pochopení a zlepšení uživatelských zkušeností?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Připadá ti UniGetUI užitečný? <PERSON><PERSON><PERSON> ano, m<PERSON><PERSON><PERSON><PERSON> podpořit mou práci, abych mohl pokračovat ve vývoji UniGetUI, dokonalého rozhraní pro správu balíčků.\n", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Přijde vám UniGetUI užitečný a chtěli byste podpořit vývojáře? Pokud ano, tak mi můžete {0}, moc to pomáhá!", "Do you really want to reset this list? This action cannot be reverted.": "Opravdu chcete tento seznam obnovit? <PERSON>to akci nelze vrátit zpět.", "Do you really want to uninstall the following {0} packages?": "Opravdu ch<PERSON><PERSON> odin<PERSON>ovat následujících {0} bal<PERSON>čků?", "Do you really want to uninstall {0} packages?": "Opravdu chcete odinstalovat {0} balíčků?", "Do you really want to uninstall {0}?": "Opravdu chcete odinstalovat {0}?", "Do you want to restart your computer now?": "Chcete nyní restartovat počítač?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Ch<PERSON><PERSON><PERSON> byste přeložit UniGetUI do vašeho jazyka? Podivejtě se jak př<PERSON>, <a style=\"color:{0}\" href=\"{1}\"a>zde!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Necítíš se na darování peněz? Sdílej UniGetUI s kamarády a podpoř tak projekt! Šiř informace o UniGetUI.\n", "Donate": "<PERSON><PERSON><PERSON>", "Done!": "Hotovo!", "Download failed": "Stažení se nezdařilo", "Download installer": "Stáhnout instalátor", "Download operations are not affected by this setting": "Toto nastavení nemá vliv na operace stahování", "Download selected installers": "Stáhnout vybrané instalační programy", "Download succeeded": "Úspěšně sta<PERSON>", "Download updated language files from GitHub automatically": "Stáhnout aktualizované soubory s překlady z GitHubu automaticky", "Downloading": "Stahování", "Downloading backup...": "Stahování z<PERSON>lohy...", "Downloading installer for {package}": "Stahování instalátoru pro {package}", "Downloading package metadata...": "Stahuji metadata balíčku...", "Enable Scoop cleanup on launch": "Zapnout pročištění Scoopu po spuštění", "Enable WingetUI notifications": "Zapnout oznámení UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Povolit [experiment<PERSON>ln<PERSON>ho] vylepšeného nástroje pro řešení potíží WinGet", "Enable and disable package managers, change default install options, etc.": "Povolení a zakázání správ<PERSON><PERSON> bal<PERSON>, změna výchozích možností instalace atd.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Povolení optimalizace využití procesoru na pozadí (viz Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Povolit api na pozadí (Widgets pro UniGetUI a Sdílení, port 7058)", "Enable it to install packages from {pm}.": "Povolte jej pro instalaci balíčků z {pm}.", "Enable the automatic WinGet troubleshooter": "Zapnout automatické řešení problémů WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Povolit nový UAC Elevator pod aplikací UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Povolení nové obsluhy vstupu procesu (StdIn automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Níže uvedená nastavení povolte, POKUD A POUZE POKUD plně chápete, k čemu slouží a jaké mohou mít dů<PERSON>d<PERSON> a nebezpečí.", "Enable {pm}": "Zapnout {pm}", "Enter proxy URL here": "Zde zadej URL proxy serveru", "Entries that show in RED will be IMPORTED.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se zobrazí červeně, budou IMPORTOVÁNY.", "Entries that show in YELLOW will be IGNORED.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se zobrazí žlutě, budou IGNOROVÁNY.", "Error": "Chyba", "Everything is up to date": "Vše je aktuální", "Exact match": "Přesná shoda", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Stávající zástupci na ploše budou prohledáni a budete muset vybrat, které z nich chcete zachovat a které odstranit.", "Expand version": "Rozbalit verze", "Experimental settings and developer options": "Experimentální nastavení a vývojářské možnosti", "Export": "Exportovat", "Export log as a file": "Exportovat protokol do souboru", "Export packages": "Export b<PERSON><PERSON><PERSON>", "Export selected packages to a file": "Exportovat označené balíčky do souboru", "Export settings to a local file": "Exportovat nastavení do souboru", "Export to a file": "Exportovat do souboru", "Failed": "Selhání", "Fetching available backups...": "Načítání dos<PERSON>ný<PERSON> záloh...", "Fetching latest announcements, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prosím v<PERSON>č<PERSON>j<PERSON>...", "Filters": "Filtry", "Finish": "Dokončit", "Follow system color scheme": "<PERSON><PERSON>", "Follow the default options when installing, upgrading or uninstalling this package": "<PERSON><PERSON><PERSON>, aktualizaci nebo odinstalaci tohoto balíčku postupujte podle výchozích možností.", "For security reasons, changing the executable file is disabled by default": "Změna s<PERSON>titelného souboru je ve výchozím nastavení z bezpečnostních důvodů zakázána.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Z bezpečnostních důvo<PERSON>ů jsou vlastní argumenty příkazového řádku ve výchozím nastavení zakázány. Chcete-li to změnit, přejděte do nastavení zabezpečení UniGetUI.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Z bezpečnostních dů<PERSON><PERSON>ů jsou předoperační a pooperační skripty ve výchozím nastavení zakázány. Chcete-li to změnit, přejděte do nastavení zabezpečení UniGetUI.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Použít ARM verzi winget (POUZE PRO ARM64 SYSTÉMY)", "Formerly known as WingetUI": "Dříve známý jako Winget<PERSON>", "Found": "<PERSON><PERSON><PERSON><PERSON>", "Found packages: ": "<PERSON><PERSON><PERSON><PERSON>:", "Found packages: {0}": "<PERSON><PERSON><PERSON><PERSON>: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON><PERSON>: {0}, je<PERSON><PERSON><PERSON> nedo<PERSON>...", "General preferences": "Obecné <PERSON>", "GitHub profile": "GitHub profil", "Global": "Globální", "Go to UniGetUI security settings": "Přejděte do nastavení zabezpečení UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Obsáhlý repozitá<PERSON>, ale přesto užitečných nástrojů a dalš<PERSON>ch zajímavých balíčků.<br>Obsahuje: <b><PERSON><PERSON><PERSON><PERSON>, programy příkazové řádky a obecný software (nuné extra repozitáře)</b>", "Great! You are on the latest version.": "Skvělé! Máte nejnovější verzi.", "Grid": "Mřížka", "Help": "Nápověda", "Help and documentation": "Nápověda a dokumentace", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Zde můžete změnit chování rozhraní UniGetUI, pokud jde o následující zkratky. Zaškrtnutí zástupce způsobí, že jej UniGetUI odstraní, pokud bude vytvořen při budoucí aktualizaci. Zrušením zaškrtnutí zůstane zástupce nedotčen", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON>, já jsem Martí a jsem <i>vývojář</i> UniGetUI. Aplikace UniGetUI byla celá vytvořena v mém volném čase!", "Hide details": "<PERSON>k<PERSON><PERSON><PERSON> podro<PERSON>", "Homepage": "Domovská stránka", "Hooray! No updates were found.": "Juchů! Nejsou žádné aktualizace!", "How should installations that require administrator privileges be treated?": "Jak by se mě<PERSON>t s instalacemi, kter<PERSON> v<PERSON>žadují oprávnění správce?", "How to add packages to a bundle": "Jak přidat balíčky do sady", "I understand": "Rozumím", "Icons": "<PERSON><PERSON><PERSON>", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Pokud máte povoleno zálohování do cloudu, bude na tomto úč<PERSON> uložen jako GitHub G<PERSON>.", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignorovat vlastní předinstalační a poinstalační příkazy při importu balíčků ze sady", "Ignore future updates for this package": "Ignorovat budoucí aktualizace tohoto balíčku", "Ignore packages from {pm} when showing a notification about updates": "Ignorovat balíčky z {pm} při zobrazení oznámení o aktualizacích", "Ignore selected packages": "Ignorovat vybrané b<PERSON>", "Ignore special characters": "Ignorovat speciální znaky", "Ignore updates for the selected packages": "Ignorovat aktualizace pro vybrané b<PERSON>", "Ignore updates for this package": "Ignorovat aktualizace pro tento balíček", "Ignored updates": "Ignorované aktualizace", "Ignored version": "Ignorovan<PERSON> verze", "Import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import packages": "<PERSON><PERSON>rt b<PERSON><PERSON><PERSON>", "Import packages from a file": "Importovat balíčky ze souboru", "Import settings from a local file": "Importovat nastavení ze souboru", "In order to add packages to a bundle, you will need to: ": "Chcete-li přidat balíčky do sady, musíte: ", "Initializing WingetUI...": "Inicializace UniGetUI...", "Install": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install Scoop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install and more": "Instalace a další", "Install and update preferences": "Volby instalace a aktualizace", "Install as administrator": "Instalovat jako s<PERSON>", "Install available updates automatically": "Automaticky instalovat dostupné aktualizace", "Install location can't be changed for {0} packages": "Umístění instalace nelze změnit pro {0} b<PERSON><PERSON><PERSON><PERSON>ů", "Install location:": "Umístění instalace:", "Install options": "Možnosti instalace", "Install packages from a file": "Instalovat balíčky ze souboru", "Install prerelease versions of UniGetUI": "Instalovat předběžné verze UniGetUI", "Install script": null, "Install selected packages": "Nainstalovat v<PERSON><PERSON><PERSON><PERSON>", "Install selected packages with administrator privileges": "Nainstalovat vybrané balíčky s oprávněním správce", "Install selection": "Nains<PERSON>ovat vybrané", "Install the latest prerelease version": "Instalovat nejnovější předběžnou verzi", "Install updates automatically": "Automaticky instalovat aktualizace", "Install {0}": "<PERSON>ins<PERSON>ovat {0}", "Installation canceled by the user!": "Instalace byla přerušena uživatelem!", "Installation failed": "Instalace selhala", "Installation options": "Volby instalace", "Installation scope:": "<PERSON><PERSON><PERSON><PERSON> instalace:", "Installation succeeded": "Úspěšně <PERSON>", "Installed Packages": "Míst<PERSON><PERSON>", "Installed Version": "Nainstalovaná verze", "Installed packages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Installer SHA256": "SHA256", "Installer SHA512": "SHA512", "Installer Type": "Typ instalátoru", "Installer URL": "URL", "Installer not available": "Instalační program nen<PERSON>", "Instance {0} responded, quitting...": "Instance {0} o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ukončuji...", "Instant search": "<PERSON><PERSON><PERSON>", "Integrity checks can be disabled from the Experimental Settings": "Kontrolu integrity lze zakázat v Experimentálním nastavení", "Integrity checks skipped": "Kontrola integrity přeskočena", "Integrity checks will not be performed during this operation": "Kontrola integrity se při této operaci neprovádí.", "Interactive installation": "Interaktivní instalace", "Interactive operation": "Interaktivní operace", "Interactive uninstall": "Interaktivní odinstalace", "Interactive update": "Interaktivní aktualizace", "Internet connection settings": "Nastavení připojení k internetu", "Is this package missing the icon?": "Chybí tomuto balíčku ikonka?", "Is your language missing or incomplete?": "Chybí váš jazyk nebo není <PERSON>ý?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "<PERSON><PERSON><PERSON>, že posky<PERSON><PERSON><PERSON> přihlašovací údaje budou bezpeč<PERSON>ě <PERSON>, takže nepoužívej přihlašovací údaje k vašemu bankovnímu účtu.", "It is recommended to restart UniGetUI after WinGet has been repaired": "Po opravě WinGet se doporučuje restartovat aplikaci UniGetUI", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Důrazně se doporučuje přeinstalovat UniGetUI k vyřešení této situace.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "<PERSON><PERSON><PERSON><PERSON> to, že program WinGet nefunguje správně. Chcete se pokusit WinGet opravit?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON><PERSON><PERSON> to, že jste UniGetUI spustili jako spr<PERSON>v<PERSON>, což se nedoporučuje. Program můžete používat i nadále, ale důrazně doporučujeme nespouštět UniGetUI s právy správce. Kliknutím na \"{showDetails}\" zjistíte proč.", "Language": "Jazyk", "Language, theme and other miscellaneous preferences": "Lok<PERSON><PERSON>e, motivy a další různé vlastnosti", "Last updated:": "Poslední aktualizace:", "Latest": "Poslední", "Latest Version": "Poslední verze", "Latest Version:": "Poslední verze:", "Latest details...": "Poslední podrobnosti...", "Launching subprocess...": "Spouštění podprocesu...", "Leave empty for default": "Nechat prázdné pro výchozí", "License": "Licence", "Licenses": "Licence", "Light": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "List": "Seznam", "Live command-line output": "Podrobný výpis z konzole", "Live output": "Živý výstup", "Loading UI components...": "Načítání UI komponent...", "Loading WingetUI...": "Načítání UniGetUI...", "Loading packages": "Nač<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Loading packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prosím v<PERSON>čkejte...", "Loading...": "Načítání...", "Local": "Místní", "Local PC": "Místní <PERSON>č", "Local backup advanced options": "Pokročilé možnosti místního zálohování", "Local machine": "Místní <PERSON>č", "Local package backup": "Místní <PERSON> b<PERSON>", "Locating {pm}...": "<PERSON>yhledávám {pm}...", "Log in": "Přihlásit se", "Log in failed: ": "Příhlašování se<PERSON>.", "Log in to enable cloud backup": "Přihlaš se k povolení zálohování do cloudu", "Log in with GitHub": "Přihlásit se pomocí GitHubu", "Log in with GitHub to enable cloud package backup.": "Přihlaš se pomocí GitHub k povolení zálohování balíčků do cloudu.", "Log level:": "Úroveň protokolu:", "Log out": "Odhlásit se", "Log out failed: ": "Odhlášení se nezdařilo:", "Log out from GitHub": "Odhlásit se z GitHubu", "Looking for packages...": "Hledají se b<PERSON>...", "Machine | Global": "Zařízení | Globálně", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Nesprávně formátované argumenty příkazového řádku mohou poškodit balíčky nebo dokonce umožnit útočníkovi získat privilegované spouštění. Proto je import vlastních argumentů příkazového řádku ve výchozím nastavení zakázán.", "Manage": "Správa", "Manage UniGetUI settings": "Správa nastavení UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Správa spouštění UniGetUI v aplikaci Nastavení", "Manage ignored packages": "Spravovat ignorov<PERSON><PERSON>", "Manage ignored updates": "Spravovat ignorované aktualizace", "Manage shortcuts": "Spravovat zástupce", "Manage telemetry settings": "Spravovat nastavení telemetrie", "Manage {0} sources": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "Manifest": "Manifest", "Manifests": "Manifesty", "Manual scan": "Manuální skenování", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Oficiální sprá<PERSON>ce balí<PERSON>ů od <PERSON>, plný dobře známých a oveřených programů<br>Obsahuje: <b>Obecný software a aplikace z Microsoft Store</b>", "Missing dependency": "Chybějí<PERSON>í <PERSON>", "More": "V<PERSON>ce", "More details": "Více informací", "More details about the shared data and how it will be processed": "Více podrobnosti o sdílených údajích a způsobu jejich zpracování", "More info": "Více informací", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "Poznámka: Toto řešení problémů může být vypnuto z nastavení UnigetUI v sekci WinGet", "Name": "<PERSON><PERSON><PERSON><PERSON>", "New": "Nové", "New Version": "Nová verze", "New bundle": "<PERSON><PERSON> sada", "New version": "Nová verze", "Nice! Backups will be uploaded to a private gist on your account": "Hezky! Zálohy budou nahrány do soukromého gistu na vašem účtu.", "No": "Ne", "No applicable installer was found for the package {0}": "Pro balíček {0} nebyl nalezen žádný použitelný instalační program.", "No dependencies specified": "Nejsou uvedeny žádné z<PERSON>ti", "No new shortcuts were found during the scan.": "Při kontrole nebyly nalezeny žádné nové zkratky.", "No packages found": "Balíčky nebyly nalezeny", "No packages found matching the input criteria": "<PERSON><PERSON> hledaných kritérií ne<PERSON> na<PERSON>zen<PERSON> žá<PERSON>", "No packages have been added yet": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> nebyly přidány", "No packages selected": "Nebyly vybrány ž<PERSON>", "No packages were found": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>ky nebyly nalezeny", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Nejsou shromažďovány ani odesílány osobní údaje a shromážděné údaje jsou anonymizovány, tak<PERSON>e je nelze zpětně vysledovat.", "No results were found matching the input criteria": "Nebyl nalezen žádný výsledek splňující kritéria", "No sources found": "Žádné zdroje <PERSON>", "No sources were found": "<PERSON><PERSON><PERSON><PERSON>", "No updates are available": "Žádné dostupné aktualizace", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "S<PERSON>r<PERSON><PERSON><PERSON> balíčků Node.js, je plný knihoven a da<PERSON><PERSON><PERSON><PERSON> n<PERSON>, k<PERSON><PERSON> týkají světa javascriptu. <br>Obsahuje: <b>Knihovny a další související nástroje pro Node.js</b>", "Not available": "Nedostupné", "Not finding the file you are looking for? Make sure it has been added to path.": "Nenašli jste hledaný soubor? Zkontrolujte, zda byl přidán do cesty.", "Not found": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Not right now": "<PERSON><PERSON>", "Notes:": "Poznámky:", "Notification preferences": "Předvolby oznámení", "Notification tray options": "Volby notifikační lišty", "Notification types": "<PERSON><PERSON>", "NuPkg (zipped manifest)": "NuPkg (zazipovaný manifest)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Open UniGetUI": "Otevřít UniGetUI", "Open UniGetUI security settings": "Otevřete nastavení zabezpečení UniGetUI", "Open WingetUI": "Otevřít UniGetUI", "Open backup location": "Otev<PERSON><PERSON><PERSON>", "Open existing bundle": "Otevř<PERSON>t <PERSON> sadu", "Open install location": "Otev<PERSON><PERSON>t <PERSON>í instalace", "Open the welcome wizard": "Otevřít průvodce spuštěním", "Operation canceled by user": "Operace zrušena uživatelem", "Operation cancelled": "Operace zrušena", "Operation history": "Historie operací", "Operation in progress": "Probíhají operace", "Operation on queue (position {0})...": "Operace v pořadí (pozice {0})...", "Operation profile:": "Profil operace:", "Options saved": "Volby uloženy", "Order by:": "<PERSON><PERSON><PERSON><PERSON>:", "Other": "Ostatní", "Other settings": "Ostatní nastavení", "Package": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package Bundles": "<PERSON><PERSON>", "Package ID": "ID balíčku", "Package Manager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>í<PERSON>ů", "Package Manager logs": "Protokoly spr<PERSON> b<PERSON>í<PERSON>ů", "Package Managers": "Správci balíčků", "Package Name": "Název balíčku", "Package backup": "<PERSON><PERSON><PERSON><PERSON> bal<PERSON>", "Package backup settings": "Nastavení zálohování balíčku", "Package bundle": "<PERSON><PERSON> b<PERSON>", "Package details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Package lists": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>íč<PERSON>ů", "Package management made easy": "S<PERSON><PERSON><PERSON><PERSON> bal<PERSON>ů je snadná", "Package manager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>í<PERSON>ů", "Package manager preferences": "Nastavení správce balíč<PERSON>ů", "Package managers": "Správci balíčků", "Package not found": "Balíček nebyl na<PERSON>zen", "Package operation preferences": "Předvolby operací s balí<PERSON>ky", "Package update preferences": "Předvolby aktualizace b<PERSON>ů", "Package {name} from {manager}": "<PERSON><PERSON><PERSON><PERSON><PERSON> {name} z {manager}", "Package's default": "Výchozí nastavení balíčku", "Packages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Packages found: {0}": "<PERSON><PERSON><PERSON><PERSON>: {0}", "Partially": "Částečně", "Password": "He<PERSON><PERSON>", "Paste a valid URL to the database": "Vložit platnou URL do databáze", "Pause updates for": "Pozastavit aktualizace na", "Perform a backup now": "<PERSON>v<PERSON><PERSON>", "Perform a cloud backup now": "Provést zálohování do cloudu nyní", "Perform a local backup now": "Provést místní zálohování nyní", "Perform integrity checks at startup": "Provést kontrolu integrity při spuštění", "Performing backup, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prosí<PERSON> v<PERSON>č<PERSON>j<PERSON>...", "Periodically perform a backup of the installed packages": "Pravideln<PERSON> provád<PERSON><PERSON> z<PERSON><PERSON> na<PERSON>talova<PERSON><PERSON><PERSON> bal<PERSON>ů", "Periodically perform a cloud backup of the installed packages": "Pravideln<PERSON> provád<PERSON>t <PERSON>u z<PERSON>lohu nainstalovan<PERSON><PERSON> bal<PERSON>ů", "Periodically perform a local backup of the installed packages": "Pravidelně provádět místní zálohu nainstalovan<PERSON>ch bal<PERSON>ů", "Please check the installation options for this package and try again": "Zkontrolujte prosím možnosti instalace tohoto balíčku a zkuste to znovu", "Please click on \"Continue\" to continue": "Pro pokračování klikněte na \"Pokračovat\"", "Please enter at least 3 characters": "Prosím zadej aspoň 3 znaky", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že někter<PERSON> balíčky není možné nainstalovat kvůli správcům balíčků, které jsou v tomto počítači povoleny.", "Please note that not all package managers may fully support this feature": "<PERSON>r na vědomí, že ne všechny správci balíčků tuto funkci plně podporují.", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že balíčky z některých zdrojů není možné exportovat. <PERSON><PERSON><PERSON> balíčky jsou označeny šedou barvou.", "Please run UniGetUI as a regular user and try again.": "Spusťte UniGetUI jako běžný uživatel a zkuste to znovu.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Další informace o problému naleznete ve výstupu příkazového řádku nebo v Historii operací.", "Please select how you want to configure WingetUI": "<PERSON><PERSON><PERSON><PERSON>, jak si chcete nastavit UniGetUI", "Please try again later": "Zkuste to prosím později", "Please type at least two characters": "Napište prosím alespoň dva znaky", "Please wait": "Prosím vyčkejte", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Poč<PERSON><PERSON><PERSON> prosím, než se nainstaluje {0}. M<PERSON>že se zobrazit černé (nebo modré) okno. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dokud se nezavře.", "Please wait...": "Prosím vyčkejte...", "Portable": "Portable", "Portable mode": "Portable režim", "Post-install command:": "Příkaz po instalaci:", "Post-uninstall command:": "Příkaz po odinstalaci:", "Post-update command:": "Příkaz po aktualizaci:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell správce balíčků. Hledání knihoven a skriptů k rozšíření PowerShell schopností<br><PERSON>bs<PERSON><PERSON>: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>\n", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Příkazy před a po instalaci mohou způsobit velmi nepříjemné věci vašemu zařízení, pokud jsou k tomu navrženy. Může být velmi nebezpečné importovat příkazy ze sady, pokud nedůvěřujete zdroji této sady balíčků.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Příkazy před a po instalaci budou spuštěny před a po instalaci, aktualizaci nebo odinstalaci balíčku. Uv<PERSON>domte si, že mohou věci poškodit, pokud nebudou použity opatrně.", "Pre-install command:": "Příkaz před instalací:", "Pre-uninstall command:": "Příkaz před o<PERSON>:", "Pre-update command:": "Příkaz před aktualizací:", "PreRelease": "PreRelease", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, prosím vyčkejte...", "Proceed at your own risk.": "Pokračujte na vlastní nebezpečí.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Zákaz jakéhokoli povýšení pomocí UniGetUI Elevator nebo GSudo", "Proxy URL": "Proxy URL", "Proxy compatibility table": "Tabulka kompatibility proxy serverů", "Proxy settings": "Nastavení proxy serveru", "Proxy settings, etc.": "Nastavení proxy serveru atd.", "Publication date:": "<PERSON>tum v<PERSON>:", "Publisher": "Vydavatel", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Správce knihoven Pythonu a dalš<PERSON>ch nástrojů souvisejících s Pythonem. <br>Obsahu<PERSON>: <b>Knihovny Pythonu a související nástroje</b>", "Quit": "Ukončit", "Quit WingetUI": "Ukončit UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Omezení výzev UAC, zvýšení úrovně výchozí instalace, odemčení některých nebezpečných funkcí atd.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Podívejte se do protokolů UniGetUI pro získání více podrobností o postižených souborech", "Reinstall": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Reinstall package": "Přein<PERSON><PERSON><PERSON> b<PERSON>íč<PERSON>", "Related settings": "Související nastavení", "Release notes": "Poznámky k vydání", "Release notes URL": "URL Poznámek k vydání", "Release notes URL:": "URL seznamu změn:", "Release notes:": "Seznam změn:", "Reload": "Obnovit", "Reload log": "Znovu načíst protokol", "Removal failed": "Odstranění sel<PERSON>o", "Removal succeeded": "Úspěšné odstranění", "Remove from list": "Odstranit ze seznamu", "Remove permanent data": "Odstranit data aplikace", "Remove selection from bundle": "Odstranit výběr ze sady", "Remove successful installs/uninstalls/updates from the installation list": "Odstranit úspěšné (od)instalace/aktualizace ze seznamu instalací", "Removing source {source}": "Odstraňování zdroje {source}", "Removing source {source} from {manager}": "Odebír<PERSON><PERSON> zdroje {source} z {manager}", "Repair UniGetUI": "Opravit UniGetUI", "Repair WinGet": "Opravit WinGet", "Report an issue or submit a feature request": "Nahlásit problém nebo odešli požadavek na funkci", "Repository": "Repozit<PERSON><PERSON>", "Reset": "Obnovit", "Reset Scoop's global app cache": "Obnovit globální mezipaměť Scoopu", "Reset UniGetUI": "Obnovit UniGetUI", "Reset WinGet": "Obnovit WinGet", "Reset Winget sources (might help if no packages are listed)": "Obnovit zdr<PERSON>je <PERSON> (m<PERSON><PERSON><PERSON> p<PERSON>, k<PERSON><PERSON> se nezobrazují balíčky)", "Reset WingetUI": "Obnovit UniGetUI", "Reset WingetUI and its preferences": "Obnovit UniGetUI do původního nastavení", "Reset WingetUI icon and screenshot cache": "Vyčistit mezipaměť UniGetUI ikonek a screenshotů", "Reset list": "Obnovit seznam", "Resetting Winget sources - WingetUI": "Resetování zdrojů Winget - UniGetUI", "Restart": "<PERSON><PERSON><PERSON><PERSON>", "Restart UniGetUI": "Restartovat UniGetUI", "Restart WingetUI": "Restartovat UniGetUI", "Restart WingetUI to fully apply changes": "Pro aplikování změn restartujte UniGetUI", "Restart later": "Restartovat později", "Restart now": "<PERSON><PERSON><PERSON>", "Restart required": "Vyžadován restart", "Restart your PC to finish installation": "Restartujte počítač k dokončení instalace", "Restart your computer to finish the installation": "Aby bylo mo<PERSON><PERSON> instalaci, je nutn<PERSON> p<PERSON>", "Restore a backup from the cloud": "<PERSON><PERSON><PERSON> zálohy z <PERSON>u", "Restrictions on package managers": "Omezení s<PERSON>r<PERSON><PERSON> bal<PERSON>", "Restrictions on package operations": "Omezení operací s balíčky", "Restrictions when importing package bundles": "Omezení při importu balí<PERSON>ů", "Retry": "Zkusit znovu", "Retry as administrator": "Zkusit znovu jako spr<PERSON>ce", "Retry failed operations": "Zkusit znovu neúspěšné operace", "Retry interactively": "Zkusit znovu interaktivně", "Retry skipping integrity checks": "Zkusit znovu bez kontroly integrity", "Retrying, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON> pokus, prosím vyčkejte...", "Return to top": "<PERSON><PERSON><PERSON><PERSON><PERSON> se nahoru", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Run as admin": "<PERSON><PERSON><PERSON><PERSON> jako s<PERSON>", "Run cleanup and clear cache": "Spustit čištění a vymazat mezipaměť", "Run last": "Spustit jako poslední", "Run next": "Spus<PERSON><PERSON> jako <PERSON>", "Run now": "Spustit hned", "Running the installer...": "Spuštění instalačního programu...", "Running the uninstaller...": "Spuštění odinstalačního programu...", "Running the updater...": "Spuštění aktualizačního programu...", "Save": "Uložit", "Save File": "<PERSON><PERSON><PERSON><PERSON> soub<PERSON>", "Save and close": "Uložit a zavřít", "Save as": "Uložit jako", "Save bundle as": "Uložit sadu jako", "Save now": "Uložit", "Saving packages, please wait...": "<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>, prosím v<PERSON>čkejte...", "Scoop Installer - WingetUI": "Scoop instalátor - UniGetUI", "Scoop Uninstaller - WingetUI": "<PERSON><PERSON> odinstalátor - UniGetUI", "Scoop package": "<PERSON><PERSON>", "Search": "Vyhledat", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Vyhledávání softwaru pro počítače, upozornění na dostupné aktualizace a nedělání nerdských věcí. Nechci UniGetUI příliš komplikovaný, chci jen j<PERSON> <b>obchod se <PERSON>m</b>.", "Search for packages": "Vyhledá<PERSON><PERSON> b<PERSON>", "Search for packages to start": "Pro výpis balíčků začněte vyhledávat", "Search mode": "<PERSON><PERSON><PERSON>", "Search on available updates": "Hledání dos<PERSON>ných aktualizací", "Search on your software": "Hledání v nainstalovaných", "Searching for installed packages...": "Vyhledávání na<PERSON>ovaných balíčků...", "Searching for packages...": "Vyhledáván<PERSON> bal<PERSON>ů...", "Searching for updates...": "Vyhledávání aktualizací...", "Select": "<PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Vybert<PERSON> \"{item}\" pro přidání vlastního repozitáře", "Select a folder": "Vybrat složku", "Select all": "Vybrat vše", "Select all packages": "Vybrat všechny balíčky", "Select backup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, <b>pokud op<PERSON><PERSON><PERSON> v<PERSON><PERSON>, co d<PERSON><PERSON><PERSON></b>.", "Select package file": "<PERSON><PERSON><PERSON><PERSON> soubor", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> chcete otevřít. Později budete moci zkontrolovat, k<PERSON><PERSON>/programy chcete obnovit.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "<PERSON><PERSON><PERSON><PERSON> pro<PERSON>, k<PERSON><PERSON> by m<PERSON><PERSON> b<PERSON><PERSON><PERSON> před instal<PERSON>, aktualizací nebo odinstalací tohoto balíč<PERSON>.", "Select the source you want to add:": "Vyber zdroj, k<PERSON><PERSON>š přidat:", "Select upgradable packages by default": "Vždy vybrat aktualizovatelné balíčky", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <b>sp<PERSON><PERSON><PERSON><PERSON> b<PERSON></b> se ma<PERSON><PERSON> ({0}), nakonfigurujte způsob instalace bal<PERSON>ů, spravujte způsob nakládání s právy správce atd.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake odeslán. Čekám na odpověď... ({0}%)", "Set a custom backup file name": "Vlastní název pro soubor z<PERSON>lohy", "Set custom backup file name": "Nastavení vlastního názvu souboru pro zálohu", "Settings": "Nastavení", "Share": "Sdílet", "Share WingetUI": "Sdílet UniGetUI", "Share anonymous usage data": "Sdílení anonymních dat o používání", "Share this package": "Sdílet", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Pokud změníte nastavení zabe<PERSON>pe<PERSON>ení, budete muset sadu znovu otevří<PERSON>, aby se změny projevily.", "Show UniGetUI on the system tray": "Zobrazit UniGetUI na hlavním panelu systému", "Show UniGetUI's version and build number on the titlebar.": "Zobrazit verzi UniGetUI v záhlaví okna", "Show WingetUI": "Zobrazit UniGetUI", "Show a notification when an installation fails": "Zobrazení notif<PERSON>, <PERSON><PERSON><PERSON> instalace selže", "Show a notification when an installation finishes successfully": "Zob<PERSON>n<PERSON> notif<PERSON>, <PERSON><PERSON><PERSON> instalace skončí úspěšně", "Show a notification when an operation fails": "Zobrazit oznámení po selhání operace", "Show a notification when an operation finishes successfully": "Zobrazit oznámení po úspěšném dokončení operace", "Show a notification when there are available updates": "Zobrazit oznámení, pokud jsou dostupné aktualizace", "Show a silent notification when an operation is running": "Zobrazit tichá oznámení při běžící operaci", "Show details": "Zobrazit podrobnosti", "Show in explorer": "Zobrazit v průzkumníkovi", "Show info about the package on the Updates tab": "Zobrazit informace o balíčku v záložce „Aktualizace“", "Show missing translation strings": "Zobrazit chybějící překlady", "Show notifications on different events": "Zobrazit oznámení o různých událostech", "Show package details": "Zobrazit podrobnosti balíčku", "Show package icons on package lists": "Zobrazit ikonky balíčků na seznamu balíčků", "Show similar packages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show the live output": "Zobrazit podrobný výpis", "Size": "Velikost", "Skip": "Přeskočit", "Skip hash check": "Přeskočit kontrolní součet", "Skip hash checks": "Přeskočit kontrolní součet", "Skip integrity checks": "Přeskočit kontrolu integrity", "Skip minor updates for this package": "Přeskočení drobných aktualizací tohoto balíčku", "Skip the hash check when installing the selected packages": "Přeskočit kontrolní součet při instalaci vybraných balíčků", "Skip the hash check when updating the selected packages": "Přeskočit kontrolní součet při aktualizaci vybraných balíčků", "Skip this version": "Přeskočit tuto verzi", "Software Updates": "Aktualizace softwaru", "Something went wrong": "Něco se pokazilo", "Something went wrong while launching the updater.": "<PERSON>ři spouštění aktualizace se něco pokazilo.", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Source URL:": "URL zdroje:", "Source added successfully": "<PERSON><PERSON><PERSON><PERSON> <PERSON>l ú<PERSON>š<PERSON> př<PERSON>", "Source addition failed": "Přidání zdroje se<PERSON>o", "Source name:": "Název zdroje:", "Source removal failed": "Odebrání zdroje se<PERSON>o", "Source removed successfully": "<PERSON><PERSON><PERSON><PERSON> <PERSON>l úsp<PERSON>šně odebrán", "Source:": "Zdroj:", "Sources": "<PERSON><PERSON><PERSON><PERSON>", "Start": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Starting daemons...": "Spouštím daemons...", "Starting operation...": "Spouštění operace...", "Startup options": "<PERSON><PERSON> s<PERSON>štění", "Status": "Stav", "Stuck here? Skip initialization": "Aplikace se zasekla? Přeskočit inicializaci", "Success!": null, "Suport the developer": "Podpořte vývojáře", "Support me": "Podpoř mě", "Support the developer": "Podpoř vývojáře", "Systems are now ready to go!": "Systémy jsou př<PERSON>raveny!", "Telemetry": "Telemetrie", "Text": "Text", "Text file": "unigetui_log", "Thank you ❤": "Dě<PERSON><PERSON> ❤", "Thank you 😉": "Dě<PERSON><PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Správce balíčků Rust.<br>Obsahuje: Knihovny a programy napsané v Rust", "The backup will NOT include any binary file nor any program's saved data.": "Záloha NEBUDE obsahovat binární soubory ani uložená data programů.", "The backup will be performed after login.": "Zálohování se provede po přihlášení.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Záloha bude obsahovat kompletní seznam nainstalovaných balíčků a možnosti jejich instalace. Uloženy budou také ignorované aktualizace a přeskočené verze.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON><PERSON>, k<PERSON>u se snažíte načíst, se zd<PERSON> být neplatná. Zkontrolujte prosím soubor a zkuste to znovu.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Kontrolní součet instalačního programu se neshoduje s očekávanou hodnotou a pravost instalačního programu nelze ověřit. Pokud vydavateli důvěřujete, opětovná {0} balíčku přeskočí kontrolní součet.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Klasický správce balíčků pro Windows. Najdete v něm vše. <br>Obsahuje: <b>Obecný software</b>", "The cloud backup completed successfully.": "Zálohování do cloudu bylo úspěšně dokon<PERSON>eno.", "The cloud backup has been loaded successfully.": "Cloudová záloha byla úspěšně načtena.", "The current bundle has no packages. Add some packages to get started": "Aktuální sada neobsahuje žádn<PERSON> balí<PERSON>. Přidejte nějaké balíčky a začněte", "The executable file for {0} was not found": "Spustitelný soubor pro {0} nebyl na<PERSON>zen", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "<PERSON><PERSON><PERSON> instal<PERSON>, aktualizaci nebo odinstalaci balíčku {0} se ve výchozím nastavení použijí následující možnosti.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Následující balíčky budou exportovány do souboru JSON. Nebudou uložena žádná uživatelská data ani binární soubory.", "The following packages are going to be installed on your system.": "Do systému budou nainstalovány následující b<PERSON>.", "The following settings may pose a security risk, hence they are disabled by default.": "Následující nastavení mohou představovat bezpečnostní riziko, proto jsou ve výchozím nastavení zakázána.", "The following settings will be applied each time this package is installed, updated or removed.": "Následující nastavení se použijí při kaž<PERSON>é instalaci, aktualizaci nebo odebrání tohoto balíčku.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Následující nastavení se použijí při kaž<PERSON>é instalaci, aktualizaci nebo odebrání tohoto balíčku. Uloží se <PERSON>ky.", "The icons and screenshots are maintained by users like you!": "Ikonky a screenshoty jsou udržování uživateli jako jste vy!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "Pravost instalačního programu nebylo možné ověřit.", "The installer has an invalid checksum": "Instalátor má neplatný kontrolní součet", "The installer hash does not match the expected value.": "Hash instalačního programu neodpovídá očekávané hodnot<PERSON>.", "The local icon cache currently takes {0} MB": "Mezipaměť ikonek aktuálně zabírá {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Hlavním cílem toho projektu je vytvořit intuitivní UI k ovládání nejčastěji použiváných CLI správců balíčků pro Windows jako je Winget č<PERSON>.", "The package \"{0}\" was not found on the package manager \"{1}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}“ nebyl nalezen ve správci balí<PERSON>ů „{1}“", "The package bundle could not be created due to an error.": "Sad<PERSON> balíčků se nepodařilo vytvořit z důvodu chyby.", "The package bundle is not valid": "<PERSON><PERSON> bal<PERSON>č<PERSON> není p<PERSON>", "The package manager \"{0}\" is disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{0}\" je vyp<PERSON><PERSON>", "The package manager \"{0}\" was not found": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{0}\" nebyl nalezen", "The package {0} from {1} was not found.": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0} z {1} nebyl nalezen.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Uvedené balíčky nebudou při kontrole aktualizací brány v úvahu. Poklepejte na ně nebo klikněte na tlačítko vpravo, abyste přestali ignorovat jejich aktualizace.", "The selected packages have been blacklisted": "Označené bal<PERSON>č<PERSON> byly zařazeny do blacklistu", "The settings will list, in their descriptions, the potential security issues they may have.": "V popisu nastavení budou uvedeny potenciální bezpečnostní problémy, kter<PERSON> mohou mít.", "The size of the backup is estimated to be less than 1MB.": "Velikost zálohy se odhaduje na méně než 1 MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> {source} by<PERSON> <PERSON><PERSON> do {manager}", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> {source} by<PERSON><PERSON>ě odeb<PERSON><PERSON> z {manager}", "The system tray icon must be enabled in order for notifications to work": "<PERSON><PERSON> <PERSON><PERSON>, musí b<PERSON>t povolena ikona na systémové liště.", "The update process has been aborted.": "Aktualizační proces byl p<PERSON><PERSON>.", "The update process will start after closing UniGetUI": "Aktualizace začne po zavření UniGetUI", "The update will be installed upon closing WingetUI": "Aktualizace bude nainstalována po zavření UniGetUI", "The update will not continue.": "Aktualizace nebude pokračovat.", "The user has canceled {0}, that was a requirement for {1} to be run": "Už<PERSON>l zrušil {0}, což bylo podmínkou pro spuštění {1}.", "There are no new UniGetUI versions to be installed": "Neexistují <PERSON>dné nové verze UniGetUI, kter<PERSON> by by<PERSON> tř<PERSON><PERSON>t", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Stále se provádí operace. Ukončení UniGetUI může způsobit jejich selhání. Chcete i přesto pokračovat?\n", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Na serveru YouTube je několik skvěl<PERSON>ch videí, která ukazují UniGetUI a jeho možnosti. Můžete se naučit užitečné triky a tipy!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Existují dva hlav<PERSON>, pro<PERSON>štět UniGetUI jako správce:\\n První je ten, že správce balíčk<PERSON> Scoop může způsobit problémy s někter<PERSON>mi příkazy, k<PERSON><PERSON> je spouštěn s právy správce.\\n Druhým je, že spuštění WingetUI jako správce znamená, že jakýkoli balíček který stáhnete, bude spuštěn jako správce (a to nen<PERSON> bezpečné).\\n Pamatujte, že pokud potřebujete nainstalovat konkrétní balíček jako správce, můžete vždy kliknout pravým tlačítkem na položku -> Instalovat/Aktualizovat/Odinstalovat jako správce.", "There is an error with the configuration of the package manager \"{0}\"": "Došlo k chybě v konfiguraci správce balíčků „{0}“", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Právě probíhá instalace. Pokud zavřete UniGetUI, instalace může selhat a mít neočekávané následky. Opravdu chcete ukončit UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Jed<PERSON> se o <PERSON>y, kter<PERSON> mají na starosti instalaci, aktualizaci a odebírání balí<PERSON>ů.", "Third-party licenses": "Licence třet<PERSON>ch stran", "This could represent a <b>security risk</b>.": "<PERSON><PERSON> m<PERSON> b<PERSON><PERSON> <b>bezpečnostní riziko</b>.", "This is not recommended.": "Toto se nedoporučuje.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Toto je prav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON><PERSON><PERSON><PERSON> t<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>, kter<PERSON> vám byl <PERSON>, byl odstraněn nebo zveřejněn ve správci balí<PERSON>ů, který nemáte povolen. Přijaté ID je {0}.", "This is the <b>default choice</b>.": "<PERSON><PERSON> je <b>v<PERSON><PERSON><PERSON><PERSON> vol<PERSON></b>.", "This may help if WinGet packages are not shown": "<PERSON><PERSON> m<PERSON>, k<PERSON><PERSON> se WinGet balíčky nezobrazují", "This may help if no packages are listed": "<PERSON><PERSON> m<PERSON> p<PERSON>, k<PERSON><PERSON> se balíčky nezobrazují", "This may take a minute or two": "<PERSON><PERSON><PERSON><PERSON> to trvat minutu nebo dvě.", "This operation is running interactively.": "Tento operace je spuštěna interaktivně.", "This operation is running with administrator privileges.": "Tato operace je spuštěna s oprávněním správce.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Tato mo<PERSON>t bude způsobovat problémy. Jakákoli operace, která se nedokáže sama povýšit, sel<PERSON><PERSON>. Instalace/aktualizace/odinstalace jako správce NEBUDE FUNGOVAT.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "<PERSON><PERSON> sada b<PERSON><PERSON><PERSON><PERSON><PERSON> obsahoval některá potenciálně nebezpečná nastavení, kter<PERSON> mohou být ve výchozím nastavení ignorována.", "This package can be updated": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> může být aktualizován", "This package can be updated to version {0}": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> může být aktualizován na verzi {0}", "This package can be upgraded to version {0}": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> může být aktualizován na verzi {0}", "This package cannot be installed from an elevated context.": "<PERSON>to balíček nelze nainstalovat z kontextu výše.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Chybí tomuto balíčku snímky obrazovky nebo ikonka? Přispějte do UniGetUI přidáním chybějících ikonek a snímků obrazovky do naší otevřené, veřejné databáze.", "This package is already installed": "<PERSON><PERSON> b<PERSON><PERSON> je již na<PERSON>n", "This package is being processed": "<PERSON><PERSON> balíč<PERSON> se zpracovává", "This package is not available": "<PERSON><PERSON> b<PERSON><PERSON> je nedostupný", "This package is on the queue": "<PERSON><PERSON> b<PERSON><PERSON> je ve <PERSON>", "This process is running with administrator privileges": "Tento proces je spuštěný s oprávněním správce", "This project has no connection with the official {0} project — it's completely unofficial.": "Tento projekt nemá žádnou spojitost s oficiálním projektem {0} - je zcela neoficiální.", "This setting is disabled": "Toto nastavení je vypnuto", "This wizard will help you configure and customize WingetUI!": "Tento průvodce Vám pomůže s konfigurací a úpravou UniGetUI!", "Toggle search filters pane": "Přepne panel vyhledávacích filtrů", "Translators": "Překladatelé", "Try to kill the processes that refuse to close when requested to": "Pokuste se ukončit procesy, kter<PERSON> se odmítají zavř<PERSON>, k<PERSON><PERSON> je to požadováno.", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Zapnutí této funkce umožňuje změnit spustitelný soubor používaný pro interakci se správci balíčků. To sice umožňuje jemnější přizpůsobení instalačních procesů, ale může to být také nebezpečné", "Type here the name and the URL of the source you want to add, separed by a space.": "Zde zadejte název a adresu URL zdroje, k<PERSON><PERSON> p<PERSON>, <PERSON><PERSON><PERSON><PERSON>.", "Unable to find package": "<PERSON><PERSON><PERSON> na<PERSON><PERSON><PERSON>", "Unable to load informarion": "Nelze načíst informace", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI shromažďuje anonymní údaje o používání za účelem zlepšení uživatelského komfortu.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI shromažďuje anonymní údaje o používání pouze za účelem pochopení a zlepšení uživatelských zkušeností.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI zjistil nového zástupce na ploše, který může být automaticky odstraněn.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI zjistil následující zástupce na ploše, které lze při budoucích aktualizacích automaticky odstranit", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI zjistil {0} nových zástupců na ploše, které lze automaticky odstranit.", "UniGetUI is being updated...": "UniGetUI je aktualizován...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI nesouvisí s žádným z kompatibilních správců balíčků. UniGetUI je nezávislý projekt.", "UniGetUI on the background and system tray": "UniGetUI v pozadí a systémové liště", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI nebo některé z jeho komponent chybí nebo jsou poškozené.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI vyžaduje ke své činnosti {0}, ale ve vašem systému nebyl na<PERSON>zen.", "UniGetUI startup page:": "Při spuštění UniGetUI zobrazit:", "UniGetUI updater": "Aktualizace UniGetUI", "UniGetUI version {0} is being downloaded.": "Stahuje se verze UniGetUI {0}", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} je připraven k instalaci.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (a jeho b<PERSON>í<PERSON>)", "Uninstall and more": "Odinstalace a další", "Uninstall and remove data": "Odinstalovat a odstranit data", "Uninstall as administrator": "Odinstalovat jako s<PERSON>", "Uninstall canceled by the user!": "Odinstalace byla přerušena uživatelem!", "Uninstall failed": "Odinstalace selhala", "Uninstall options": "Možnosti odinstalace", "Uninstall package": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Uninstall package, then reinstall it": "Odinstalovat balíček a poté znovu nainstalovat", "Uninstall package, then update it": "Odinstalovat balíček a poté jej aktualizovat", "Uninstall previous versions when updated": "Odinstalování předchozích verzí po aktualizaci", "Uninstall selected packages": "Odinstalovat vybrané <PERSON>", "Uninstall selection": "Odinstalovat vybrané", "Uninstall succeeded": "Úspěš<PERSON><PERSON> o<PERSON>", "Uninstall the selected packages with administrator privileges": "Odinstalovat vybrané balíčky s oprávněním správce", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Odinstalovatelné balíčky s původem uvedeným jako \"{0}\" nejsou zveřejněny v žádném správci balíčků, takže o nich nejsou k dispozici žádné informace.", "Unknown": "<PERSON><PERSON><PERSON><PERSON>", "Unknown size": "Neznámá velikost", "Unset or unknown": "Nenastaveno nebo neznámo", "Up to date": "Aktuální", "Update": "Aktualizovat", "Update WingetUI automatically": "Automaticky aktualizovat UniGetUI", "Update all": "Aktualizovat vše", "Update and more": "Aktualizace a další", "Update as administrator": "Aktualizovat jako s<PERSON>", "Update check frequency, automatically install updates, etc.": "Frekvence kontroly aktualizací, automatická instalace aktualizací atd.", "Update checking": null, "Update date": "Aktualizováno", "Update failed": "Aktualizace sel<PERSON>", "Update found!": "Nalezena aktualizace!", "Update now": "Aktualizovat nyní", "Update options": "Možnosti aktualizace", "Update package indexes on launch": "Aktualizovat indexy balíčků při spuštění", "Update packages automatically": "Automaticky aktualizovat balíčky", "Update selected packages": "Aktualizovat vybrané <PERSON>", "Update selected packages with administrator privileges": "Aktualizovat vybrané balíčky s oprávněním správce", "Update selection": "Aktualizovat vybrané", "Update succeeded": "Úspěšně aktualizováno", "Update to version {0}": "Aktualizovat na verzi {0}", "Update to {0} available": "Je dostupná aktualizace na {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Automatická aktualizace vcpkg Git portfiles (vyžaduje nainstalovaný Git)", "Updates": "Aktualizace", "Updates available!": "Dostupné aktualizace!", "Updates for this package are ignored": "Aktulizace tohoto balíčku jsou ignorovány", "Updates found!": "Nalezeny aktualizace!", "Updates preferences": "Předvolby aktualizací", "Updating WingetUI": "Aktualizace UniGetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Použ<PERSON>t přibalený starší WinGet místo PowerShell cmdlets", "Use a custom icon and screenshot database URL": "Použít vlastní URL databáze pro ikonky a screenshoty", "Use bundled WinGet instead of PowerShell CMDlets": "Použít přibalený WinGet místo PowerShell cmdlets", "Use bundled WinGet instead of system WinGet": "Použít přibalený WinGet místo systémového WinGet", "Use installed GSudo instead of UniGetUI Elevator": "Použit nainstalovaný GSudo místo UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Použít nainstalované GSudo místo př<PERSON>ho (vyžaduje restart aplikace)", "Use system Chocolatey": "Použít systémový Chocolatey", "Use system Chocolatey (Needs a restart)": "Použít systémový Chocolatey (vyžaduje restart aplikace)", "Use system Winget (Needs a restart)": "Použít systémový Winget (vyžaduje restart aplikace)", "Use system Winget (System language must be set to english)": "Použít systémový Winget (Systémový jazyk musí být nastavený na angličtinu)", "Use the WinGet COM API to fetch packages": "Použít WinGet COM API k získání balíčků", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Použít WinGet PowerShell modul místo Winget COM API", "Useful links": "Užitečné od<PERSON>zy", "User": "<PERSON>živatel", "User interface preferences": "Vlastnosti uživatelského rozhraní", "User | Local": "Uživatel | Lokální", "Username": "Uživatelské jméno", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Používání UniGetUI znamená souhlas s licencí GNU Lesser General Public License v2.1.", "Using WingetUI implies the acceptation of the MIT License": "Používání UniGetUI znamená souhlas s licencí MIT.", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg nebyl nalezen. Definujte prosím proměnnou prostředí %VCPKG_ROOT% nebo ji definujte v nastavení UniGetUI.", "Vcpkg was not found on your system.": "Vcpkg nebyl ve vašem systému nalezen.", "Verbose": "Podrobný výstup", "Version": "Verze", "Version to install:": "Verze:", "Version:": "Verze:", "View GitHub Profile": "Zobrazit GitHub profil", "View WingetUI on GitHub": "Prohlédněte si UniGetUI na GitHubu", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Zobrazit zdrojový kód UniGetUI. Odtud můžete hlásit chyby, navrhovat funkce nebo dokonce přímo přispívat do projektu UniGetUI.", "View mode:": "Zobrazit jako:", "View on UniGetUI": "Zobrazit v UniGetUI", "View page on browser": "Zobrazit stránku v prohlížeči", "View {0} logs": "Zobrazit {0} protokoly", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "<PERSON><PERSON><PERSON> prov<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> v<PERSON>aduj<PERSON> připojení k internetu, p<PERSON><PERSON><PERSON><PERSON><PERSON>, až bude zařízení připojeno k internetu.", "Waiting for other installations to finish...": "Čekám na dokončení ostatních instalací...", "Waiting for {0} to complete...": "Čekání na dokončení {0}...", "Warning": "Upozornění", "Warning!": "Varování!", "We are checking for updates.": "Kontrolujeme aktualizace.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Podrobné informace o tomto balíčku se nám nepodař<PERSON>, protože nebyl nalezen v žádném z vašich zdro<PERSON><PERSON> balí<PERSON>ů.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Podrobné informace o tomto balíčku se nám nepodař<PERSON>, protože nebyl nainstalován z dostupného správce balíčků.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON><PERSON><PERSON><PERSON> jsme {action} {package}. Zkuste to prosím později. Kliknutím na \"{showDetails}\" získáte protokoly z instalačního programu.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON><PERSON><PERSON><PERSON> jsme {action} {package}. Zkuste to prosím později. Kliknutím na \\\"{showDetails}\\\" získáte protokoly z odinstalačního programu.", "We couldn't find any package": "<PERSON><PERSON><PERSON><PERSON> jsme na<PERSON><PERSON>", "Welcome to WingetUI": "Vítejte v UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "<PERSON><PERSON><PERSON> dáv<PERSON> instalaci balíčků ze sady nainstalovat i balíčky, kter<PERSON> jsou již nainstal<PERSON>.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON><PERSON><PERSON> j<PERSON><PERSON> nov<PERSON>, <PERSON><PERSON> je s<PERSON>t, místo aby se zobrazovalo toto dialogov<PERSON> okno.", "Which backup do you want to open?": "<PERSON><PERSON><PERSON> z<PERSON>hu ch<PERSON>te otevřít?", "Which package managers do you want to use?": "<PERSON><PERSON><PERSON> správce balíčku chcete používat?", "Which source do you want to add?": "<PERSON><PERSON><PERSON> zdroj chcete přidat?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Zatímco Winget lze používat v rámci UniGetUI, UniGetUI lze používat i s jinými správci balíčků, což může být matoucí. V minulosti byl UniGetUI navržen tak, aby pracoval pouze s Wingetem, ale to ji<PERSON>, a proto UniGetUI nepředstavuje to, čím se tento projekt chce stát.", "WinGet could not be repaired": "WinGet se nepodařilo <PERSON>vit", "WinGet malfunction detected": "Zjištěna porucha WinGet", "WinGet was repaired successfully": "WinGet byl úspěšně opraven", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Všechno je aktuální", "WingetUI - {0} updates are available": "UniGetUI - dostupné aktualizace: {0}", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "Domovská stránka UniGetUI", "WingetUI Homepage - Share this link!": "Domovská stránka UniGetUI - Sdílej tento okdaz!", "WingetUI License": "UniGetUI Licence", "WingetUI Log": "UniGetUI protokol", "WingetUI Repository": "UniGetUI repozitář", "WingetUI Settings": "Nastavení UniGetUI", "WingetUI Settings File": "Soubor nastavení UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI používá následující knihovny. Bez nich by UniGetUI nebyl možný.", "WingetUI Version {0}": "UniGetUI verze {0}", "WingetUI autostart behaviour, application launch settings": "Chování automatického spuštění UniGetUI a nastavení spouštění aplikací", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI může zkontrolovat, zda má váš software dostupné aktualizace, a podle potřeby je automaticky nainstalovat.", "WingetUI display language:": "Jazyk UniGetUI", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI byl spuštěn jako sprá<PERSON>, což se nedoporučuje. Pokud je UniGetUI spuštěn jako správce, bude mít KAŽDÁ operace spuštěná z UniGetUI práva správce. Program můžete používat i nadále, ale důrazně nedoporučujeme spouštět UniGetUI s právy správce.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI byl díky dobrovolným překladatelům přeložen do více než 40 jazyků. Děkujeme 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI nebylo strojově přeloženo. Následující uživatelé měli na starosti překlady:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI je aplikace, k<PERSON><PERSON> usnadňuje správu softwaru tím, že poskytuje grafické rozhraní pro správce balíčků příkazového řádku.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI se přejmenovává, aby se zdůraznil rozdíl mezi UniGetUI (rozhraní, které právě používáte) a Winget (správce balíčků vyvinutý společností Microsoft, se kterým nejsem nijak spojen).", "WingetUI is being updated. When finished, WingetUI will restart itself": "Probíhá aktualizace UniGetUI, j<PERSON><PERSON> bude <PERSON>, aplikace se restart<PERSON>je", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI je zdarma a vždycky bude. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> kreditní karta, ž<PERSON>dná premium verze. 100% zdarma, navždy.", "WingetUI log": "Protokol UniGetUI", "WingetUI tray application preferences": "Vlastnosti UniGetUI aplikace v liště", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI používá následující knihovny. Bez nich by UniGetUI nebyl možný.", "WingetUI version {0} is being downloaded.": "Stahuje se verze UniGetUI {0}", "WingetUI will become {newname} soon!": "WingetUI se brzy stane {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI nebude pravidelně kontrolovat aktualizace. (Při spuštění budou st<PERSON><PERSON> kont<PERSON>, ale nebudete na ně upozorněni)", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI zobrazí dialog \"Řízení uživatelských účtů\" poka<PERSON><PERSON><PERSON>, když si jej balíček vyžádá.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI se bude brzy jmenovat {newname}. To nebude představovat žádnou změnu v aplikaci. <PERSON><PERSON> (vývojář) budu pokračovat ve vývoji tohoto projektu stejně jako do teď, ale pod jiným názvem.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI by nik<PERSON> ne<PERSON> vzniknout za podpory našich drahých přispěvovatelů. Koukněte na jejich GitHub profily, UniGetUI by bez nich nikdy nevznikl!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI by ne<PERSON><PERSON> mo<PERSON> vytvořit bez pomoci přispěvatelů. Děkuji Vám všem 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} je připraven k instalaci.", "Write here the process names here, separated by commas (,)": "Zde napište názvy procesů oddě<PERSON> (,).", "Yes": "<PERSON><PERSON>", "You are logged in as {0} (@{1})": "j<PERSON> přihlá<PERSON><PERSON> jako {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Toto chování můžete změnit v nastavení zabezpečení UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Můžete definovat příkazy, kter<PERSON> budou spuštěny před nebo po instalaci, aktualizaci nebo odinstalaci tohoto balíčku. Budou spuštěny v příkazovém řádku, takže zde budou fungovat skripty CMD.", "You have currently version {0} installed": "<PERSON><PERSON><PERSON><PERSON> máš nainstalovanou verzi {0}", "You have installed WingetUI Version {0}": "Je nainstalován UniGetUI verze {0}", "You may lose unsaved data": "Může dojít ke ztrátě neuložených dat", "You may need to install {pm} in order to use it with WingetUI.": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t nutné na<PERSON>t {pm} pro použití s UniGetUI.", "You may restart your computer later if you wish": "Po<PERSON>d <PERSON>, můžete počítač restartovat později", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Budete vyzváni pouze jednou a oprávnění správce budou ud<PERSON><PERSON> pouze bal<PERSON>, které o ně požádají.", "You will be prompted only once, and every future installation will be elevated automatically.": "Budete vyzváni pouze jednou a každá další instalace bude automaticky spuštěná s oprávněním správce.", "You will likely need to interact with the installer.": "Pravděpodobně budete muset s instalátorem interagovat.", "[RAN AS ADMINISTRATOR]": "SPUŠTĚNO JAKO SPRÁVCE", "buy me a coffee": "k<PERSON><PERSON> kafe", "extracted": "<PERSON>hov<PERSON><PERSON>", "feature": "funkce", "formerly WingetUI": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "<PERSON><PERSON><PERSON> str<PERSON>", "install": "instalovat", "installation": "instalace", "installed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "installing": "instaluji", "library": "knihovna", "mandatory": "pov<PERSON><PERSON>", "option": "možnost", "optional": "volitelné", "uninstall": "o<PERSON><PERSON><PERSON><PERSON>", "uninstallation": "odinstalace", "uninstalled": "o<PERSON>stalováno", "uninstalling": "o<PERSON><PERSON><PERSON><PERSON>", "update(noun)": "aktualizace", "update(verb)": "aktualizovat", "updated": "aktualizováno", "updating": "aktual<PERSON><PERSON><PERSON>", "version {0}": "verze {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} Možnosti instalace jsou v současné době uzamčeny, protože {0} se řídí výchozími možnostmi instalace.", "{0} Uninstallation": "Odinstalace {0}", "{0} aborted": "{0} selhala", "{0} can be updated": "{0} m<PERSON><PERSON><PERSON> být aktualizováno", "{0} can be updated to version {1}": "{0} m<PERSON><PERSON><PERSON> být aktualizován na verzi {1}", "{0} days": "{0} dnů", "{0} desktop shortcuts created": "{0} vytvořených zástupců na ploše", "{0} failed": "{0} selhala", "{0} has been installed successfully.": "{0} by<PERSON>.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} by<PERSON> na<PERSON>. Je doporučeno restartovat UniGetUI pro dokončení instalace.", "{0} has failed, that was a requirement for {1} to be run": "{0} <PERSON><PERSON><PERSON><PERSON>, což bylo podmínkou pro spuštění {1}", "{0} homepage": "{0} domovská stránka", "{0} hours": "{0} hodin", "{0} installation": "Instalace {0}", "{0} installation options": "{0} možnosti instalace", "{0} installer is being downloaded": "Stahuje se instalační program {0}", "{0} is being installed": "{0} je ins<PERSON><PERSON>", "{0} is being uninstalled": "{0} je o<PERSON><PERSON><PERSON>", "{0} is being updated": "{0} je a<PERSON><PERSON><PERSON><PERSON>", "{0} is being updated to version {1}": "{0} se aktualizuje na verzi {1}", "{0} is disabled": "{0} je vyp<PERSON>o", "{0} minutes": "{0} minut", "{0} months": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON>(ů)", "{0} packages are being updated": "{0} b<PERSON><PERSON><PERSON><PERSON><PERSON> jsou aktualizovány", "{0} packages can be updated": "{0} b<PERSON><PERSON><PERSON><PERSON><PERSON> může být aktualizováno", "{0} packages found": "<PERSON><PERSON><PERSON><PERSON> {0} b<PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} packages were found": "{0} b<PERSON><PERSON><PERSON><PERSON><PERSON>o", "{0} packages were found, {1} of which match the specified filters.": "{0} b<PERSON><PERSON><PERSON><PERSON><PERSON>, z nichž {1} vyhovuje zadaným filtrům.", "{0} selected": null, "{0} settings": "{0} nastavení", "{0} status": "{0} stav", "{0} succeeded": "{0} úspěšná", "{0} update": "Aktualizace {0}", "{0} updates are available": "<PERSON> {0} aktualizací.", "{0} was {1} successfully!": "{0} by<PERSON> {1}!", "{0} weeks": "{0} <PERSON><PERSON><PERSON><PERSON>(ů)", "{0} years": "{0} r<PERSON>y(ů)", "{0} {1} failed": "{0} {1} selhala", "{package} Installation": "Instalace {package}", "{package} Uninstall": "Odinstalace {package}", "{package} Update": "Aktualizace {package}", "{package} could not be installed": "{package} ne<PERSON><PERSON> b<PERSON><PERSON>n", "{package} could not be uninstalled": "{package} ne<PERSON><PERSON> b<PERSON><PERSON>", "{package} could not be updated": "{package} ne<PERSON><PERSON> být aktualizován", "{package} installation failed": "Instalace {package} selhala", "{package} installer could not be downloaded": "Instalační program {package} se nepodařilo stáhnout", "{package} installer download": "Stáhnout instalační program {package}", "{package} installer was downloaded successfully": "Instalační program {package} byl úspěšně sta<PERSON>en", "{package} uninstall failed": "Odinstalace {package} selhala", "{package} update failed": "Aktualizace {package} selhala", "{package} update failed. Click here for more details.": "Aktualizace {package} selhala. Klikni zde pro více podrobností.", "{package} was installed successfully": "{package} byl <PERSON>", "{package} was uninstalled successfully": "{package} by<PERSON><PERSON>", "{package} was updated successfully": "{package} byl ú<PERSON>šně aktualizován", "{pcName} installed packages": "{pcName} na<PERSON><PERSON><PERSON><PERSON><PERSON>", "{pm} could not be found": "{pm} se nepo<PERSON><PERSON><PERSON> na<PERSON>", "{pm} found: {state}": "{pm} nalezen: {state}", "{pm} is disabled": "{pm} je vyp<PERSON>o", "{pm} is enabled and ready to go": "{pm} je povolen a připraven k použití", "{pm} package manager specific preferences": "Specific<PERSON><PERSON> v<PERSON>ti správce balíč<PERSON>ů {pm}", "{pm} preferences": "Vlastnosti {pm}", "{pm} version:": "{pm} verze:", "{pm} was not found!": "{pm} nebyl nalezen!"}