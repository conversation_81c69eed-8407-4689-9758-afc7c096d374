{"\"{0}\" is a local package and can't be shared": "\"{0}\" є локальним пакетом: їм не можна поділитися", "\"{0}\" is a local package and does not have available details": "\"{0}\" є локальним пакетом: про нього немає доступної інформації", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" є локальним пакетом і несумісний з цією функціональністю", "(Last checked: {0})": "(Востаннє перевірено: {0})", "(Number {0} in the queue)": "({0} позиція в черзі)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "Operator404, <PERSON><PERSON>, @Taron-art, <PERSON>", "0 packages found": "Знайдено 0 пакетів", "0 updates found": "Знайдено 0 оновлень", "1 - Errors": "1 - <PERSON>о<PERSON><PERSON>л<PERSON>и", "1 day": "1 день", "1 hour": "1 година", "1 month": "1 місяць", "1 package was found": "Знайдено 1 пакет", "1 update is available": "1 оновлення доступне", "1 week": "1 тиждень", "1 year": "1 рік", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Перейти на сторінку \"{0}\" чи \"{1}\".", "2 - Warnings": "2 - Зас<PERSON><PERSON><PERSON><PERSON>ж<PERSON>ння", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Знайти пакети, які ви хочете додати до колекції, та позначити їх прапорцем у найлівішій колонці.", "3 - Information (less)": "3 - Інформація (менше)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON><PERSON><PERSON> пакети, які ви хочете додати, вибран<PERSON>, знайдіть та натисніть \"{0}\" на панелі інструментів.", "4 - Information (more)": "4 - Ін<PERSON>ор<PERSON>а<PERSON><PERSON><PERSON> (більше)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Вибрані пакети будуть додані до колекції. Ви можете продовжити додавати пакети або експортувати колекцію.", "5 - information (debug)": "5 - Інформація (налагодження)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Популярний менеджер бібліотек для C/C++. Заповнений бібліотеками для C/C++ та зв'язаними утилітами.<br>Містить: <b>C/C++ бібліотеки та зв'язані утиліти</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Репозиторій повний утиліт та застосунків, спроектованих для екосистеми .NET від Microsoft.<br>Містить: <b>Утиліти, зв'язані з .NET, та скрипти</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Репозиторій повний утиліт, спроектованих для екосистеми .NET від Microsoft.<br>Містить: <b>Утиліти, пов'язані з .NET</b>", "A restart is required": "Потрібен перезапуск", "Abort install if pre-install command fails": "Відмінити встановлення, якщо команда перед встановленням завершиться невдало", "Abort uninstall if pre-uninstall command fails": "Відмінити видалення, якщо команда перед видаленням завершиться невдало", "Abort update if pre-update command fails": "Відмінити оновлення, якщо команда перед оновленням завершиться невдало", "About": "Про програму", "About Qt6": "Про Qt6", "About WingetUI": "Про UniGetUI", "About WingetUI version {0}": "Про UniGetUI версії {0}", "About the dev": "Про розробника", "Accept": "Погоджуюсь", "Action when double-clicking packages, hide successful installations": "Дія при подвійному клацанні пакетів, приховати успішні інсталяції", "Add": "Додати", "Add a source to {0}": "Додати джерело до {0}", "Add a timestamp to the backup file names": "Додавати мітку часу до імен файлів резервної копії", "Add a timestamp to the backup files": "Додавати мітку часу до файлів резервної копії", "Add packages or open an existing bundle": "Додайте пакети чи відкрийте існуючу колекцію", "Add packages or open an existing package bundle": "Додайте пакети чи відкрийте існуючу колекцію пакетів", "Add packages to bundle": "Додати пакети до колекції", "Add packages to start": "Додайте пакети, щоби почати", "Add selection to bundle": "Додати вибране в колекцію", "Add source": "Додати джерело", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Додати оновлення, що не вдалися з помилкою \"не знайдено оновлень, що можна застосувати\", до списку ігнорованих оновлень", "Adding source {source}": "Додавання джерела {source}", "Adding source {source} to {manager}": "Додаємо джерело {source} до {manager}", "Addition succeeded": "Додавання закінчилось успіхом", "Administrator privileges": "Права адміністратора", "Administrator privileges preferences": "Параметри прав адміністратора", "Administrator rights": "Права адміністратора", "Administrator rights and other dangerous settings": "Права адміністратора та інші небезпечні налаштування", "Advanced options": "Розширені налаштування", "All files": "Всі файли", "All versions": "Всі версії", "Allow changing the paths for package manager executables": "Дозволити зміну шляхів до виконавчих файлів менеджерів пакетів", "Allow custom command-line arguments": "Дозволити користувацькі аргументи командного рядка", "Allow importing custom command-line arguments when importing packages from a bundle": "Дозволити імпортувати користувацькі аргументи командного рядка при імпорті пакетів з колекції", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Дозволити імпортувати користувацькі перед- і після-операційні скрипти при імпорті пакетів з колекції", "Allow package operations to be performed in parallel": "Дозволити виконувати операції з пакетами паралельно.", "Allow parallel installs (NOT RECOMMENDED)": "Дозволити паралельне встановлення (НЕ РЕКОМЕНДОВАНО)", "Allow pre-release versions": "Дозволити підготовчі версії", "Allow {pm} operations to be performed in parallel": "Дозволити виконувати операції з {pm} паралельно", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Також, ви можете встановити {0}, запустивши наступну команду у стрічці Windows PowerShell:", "Always elevate {pm} installations by default": "Завжди підвищувати права установки {pm} за замовчуванням", "Always run {pm} operations with administrator rights": "Завжди запускати операції з {pm} від імені адміністратора.", "An error occurred": "Виникла помилка", "An error occurred when adding the source: ": "Сталася помилка при додаванні джерела:", "An error occurred when attempting to show the package with Id {0}": "Виникла помилка при намаганні відобразити пакет з Id {0}", "An error occurred when checking for updates: ": "Виникла помилка при перевірці оновлень:", "An error occurred while attempting to create an installation script:": "При спробі створити скрипт для встановлення виникла помилка:", "An error occurred while loading a backup: ": "Виникла помилка при завантаженні резервної копії:", "An error occurred while logging in: ": "Виникла помилка при вході:", "An error occurred while processing this package": "Виникла помилка при обробці цього пакету", "An error occurred:": "Виникла помилка:", "An interal error occurred. Please view the log for further details.": "Виникла внутрішня помилка. Будь ласка, продивіться журнал для більш детальної інформації.", "An unexpected error occurred:": "Сталася неочікувана помилка:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Виникла неочікувана проблема при намаганні відновити WinGet. Будь ласка, спробуйте пізніше.", "An update was found!": "Знайдено оновлення!", "Android Subsystem": "Android Subsystem", "Another source": "Інше джерело", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Для новий ярликів, створених під час операцій встановлення та оновлення, не буде показано діалогу підтвердження: замість цього вони будуть видалені автоматично.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Всі ярлики, створені або змінені не через UniGetUI, будуть проігноровані. Ви зможете додати їх за допомогою кнопки \"{0}\".", "Any unsaved changes will be lost": "Всі незбережені зміни буде втрачено", "App Name": "Назва програми", "Appearance": "Зовнішній вигляд", "Application theme, startup page, package icons, clear successful installs automatically": "Тема програми, початковий екран, піктограми пакетів, автоматичне прибирання успішних операцій", "Application theme:": "Тема програми:", "Apply": "Застосувати", "Architecture to install:": "Архітектура для встановлення:", "Are these screenshots wron or blurry?": "Ці скріншоти неправильні або розмиті?", "Are you really sure you want to enable this feature?": "Ви точно впевнені що хочете увімкнути цю функцію?", "Are you sure you want to create a new package bundle? ": "Ви впевнені, що хочете створити нову колекцію?", "Are you sure you want to delete all shortcuts?": "Ви впевнені що хочете видаляти всі ярлики?", "Are you sure?": "Ви впевнені?", "Ascendant": "Зростанням", "Ask for administrator privileges once for each batch of operations": "Запитувати права адміністратора один раз для кожної групи операцій ", "Ask for administrator rights when required": "Запитувати права адміністратора за необхідності", "Ask once or always for administrator rights, elevate installations by default": "Запитувати права адміністратора один раз або завжди, підвищувати права встановлення за замовчуванням", "Ask only once for administrator privileges": "Одноразово запитувати права адміністратора", "Ask only once for administrator privileges (not recommended)": "Запитувати права адміністратора один раз (не рекомендується)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Спробувати видалити ярлики з робочого стола, створені під час встановлення або оновлення.", "Attention required": "Потр<PERSON><PERSON>на ваша увага", "Authenticate to the proxy with an user and a password": "Автентифікуватися в проксі за допомогою користувача та пароля", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "Автоматичне видалення ярликів з робочого столу", "Automatic updates": "Автоматичні оновлення", "Automatically save a list of all your installed packages to easily restore them.": "Автоматично зберігати список усіх встановлених вами пакетів, щоб мати змогу їх легко відновити.", "Automatically save a list of your installed packages on your computer.": "Автоматично зберігати список встановлених пакетів на вашому комп'ютері.", "Autostart WingetUI in the notifications area": "Автозапуск WingetUI в області сповіщень", "Available Updates": "Доступні оновлення", "Available updates: {0}": "Доступні оновлення: {0}", "Available updates: {0}, not finished yet...": "Доступні оновлення: {0}, ще не завершено...", "Backing up packages to GitHub Gist...": "Виконую резервне копіювання пакетів до GitHub Gist...", "Backup": "Зробити копію", "Backup Failed": "Резервне копіювання не вдалося", "Backup Successful": "Резервне копіювання успішно виконано", "Backup and Restore": "Резервне копіювання і відновлення", "Backup installed packages": "Резервне копіювання встановлених пакетів", "Backup location": "Розташування резервної копії", "Become a contributor": "Стати учасником", "Become a translator": "Станьте перекладачем", "Begin the process to select a cloud backup and review which packages to restore": "Почніть процес з вибору резервної копії й пакетів для відновлення", "Beta features and other options that shouldn't be touched": "Бета-функції та інші параметри, які не варто чіпати", "Both": "ІD або ім'я пакета", "Bundle security report": "Звіт про безпеку колекції", "But here are other things you can do to learn about WingetUI even more:": "Але ось що ще можна зробити, щоб дізнатися про WingetUI ще більше:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Після відключення менеджера пакетів ви більше не зможете бачити чи оновлювати пакети з нього.", "Cache administrator rights and elevate installers by default": "Запам'ятовувати права адміністратора і підвищувати права установників за замовчуванням", "Cache administrator rights, but elevate installers only when required": "Запам'ятовувати права адміністратора, але підвищувати права установників тільки за необхідності", "Cache was reset successfully!": "Кеш успішно скинуто!", "Can't {0} {1}": "Не можу {0} {1}", "Cancel": "Скасувати", "Cancel all operations": "Відмінити всі операції", "Change backup output directory": "Змінити вихідну теку для резервного копіювання", "Change default options": "Змінити налаштування за замовченням", "Change how UniGetUI checks and installs available updates for your packages": "Змінити як UniGetUI перевіряє та встановлює оновлення для ваших пакетів", "Change how UniGetUI handles install, update and uninstall operations.": "Змінити як UniGetUI керує операціями встановлення, оновлення й видалення.", "Change how UniGetUI installs packages, and checks and installs available updates": "Змінити як UniGetUI встановлює пакети, та як перевіряє і встановлює доступні оновлення", "Change how operations request administrator rights": "Змінити як операції запитають права адміністратора", "Change install location": "Змінити місце встановлення", "Change this": "Змінити", "Change this and unlock": "Змінити це й розблокувати", "Check for package updates periodically": "Періодично перевіряти оновлення пакетів", "Check for updates": "Перевірити зараз", "Check for updates every:": "Перевіряти наявність оновлень кожні:", "Check for updates periodically": "Перевіряти оновлення періодично", "Check for updates regularly, and ask me what to do when updates are found.": "Перевіряти оновлення регулярно і питати мене, що робити в разі їх виявлення.", "Check for updates regularly, and automatically install available ones.": "Регулярно перевіряйте наявність оновлень і автоматично встановлюйте доступні.", "Check out my {0} and my {1}!": "Перевірити мій {0} та мій {1}!", "Check out some WingetUI overviews": "Ознайомтеся з деякими оглядами WingetUI", "Checking for other running instances...": "Перевірка інших запущених екземплярів...", "Checking for updates...": "Перевірка оновлень...", "Checking found instace(s)...": "Перевірка знайдених екземплярів...", "Choose how many operations shouls be performed in parallel": "Виберіть кількість операцій, що можуть виконуватися одночасно", "Clear cache": "Очистити кеш", "Clear finished operations": "Очистити завершені операції", "Clear selection": "Зняти виділення", "Clear successful operations": "Прибрати успішні операції", "Clear successful operations from the operation list after a 5 second delay": "Прибирати успішні операції зі списку операцій після 5 секунд очікування", "Clear the local icon cache": "Очистити кеш піктограм", "Clearing Scoop cache - WingetUI": "Очищаємо кеш Scoop — UniGetUI", "Clearing Scoop cache...": "Очищення кешу Scoop...", "Click here for more details": "Натисніть тут для подробиць", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Натисніть \"Встановити\" щоби почати процес встановлення. Якщо ви пропустите цей крок, UniGetUI може працювати не правильно.", "Close": "Закрити", "Close UniGetUI to the system tray": "Закривати UniGetUI у системний лоток", "Close WingetUI to the notification area": "Закрити UniGetUI в області сповіщень", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Резервне копіювання в хмару використовує GitHub Gist для зберігання списку встановлених пакетів", "Cloud package backup": "Резервне копіювання в хмару", "Command-line Output": "Вивід з командного рядка", "Command-line to run:": "Команда для запуску:", "Compare query against": "Порівняти запит з ", "Compatible with authentication": "Сумісність з автентифікацією", "Compatible with proxy": "Сумісність з проксі", "Component Information": "Інформація про компоненти", "Concurrency and execution": "Паралелізм та виконання  ", "Connect the internet using a custom proxy": "Підключатися до інтернету, використовуючи вказаний проксі", "Continue": "Продовжити", "Contribute to the icon and screenshot repository": "Додайте свій внесок у сховище значків і скріншотів", "Contributors": "Учасники", "Copy": "Скопіювати", "Copy to clipboard": "Копіювати в буфер обміну", "Could not add source": "Неможливо додати джерело", "Could not add source {source} to {manager}": "Неможливо додати джерело {source} до {manager}", "Could not back up packages to GitHub Gist: ": "Не можу завантажити резервну копію пакетів до GitHub Gist:", "Could not create bundle": "Неможливо створити колекцію", "Could not load announcements - ": "Неможливо завантажити анонсування - ", "Could not load announcements - HTTP status code is $CODE": "Неможливо завантажити анонсування: статус код HTTP — $CODE", "Could not remove source": "Неможливо видалити джерело", "Could not remove source {source} from {manager}": "Неможливо видалити джерело {source} з {manager}", "Could not remove {source} from {manager}": "Неможливо видалити {source} з {manager}", "Create .ps1 script": "Створити .ps1 скрипт", "Credentials": "Облікові данні", "Current Version": "Поточна версія", "Current status: Not logged in": "Статус: вхід не виконано", "Current user": "Для поточного користувача", "Custom arguments:": "Користувацькі аргументи:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Аргументи командного рядка, задані користувачем, можуть змінювати як програми встановлюються, оновлюються чи видаляються. UniGetUI не має можливості це контролювати. Також такі аргументи можуть псувати пакети. Дійте обережно.", "Custom command-line arguments:": "Користувацькі аргументи командного рядка:", "Custom install arguments:": "Користувацькі аргументи встановлення:", "Custom uninstall arguments:": "Користувацькі аргументи видалення:", "Custom update arguments:": "Користувацькі аргументи оновлення:", "Customize WingetUI - for hackers and advanced users only": "Налаштування WingetUI - лише для хакерів та досвідчених користувачів", "DEBUG BUILD": "ЗБІРКА ДЛЯ ВІДЛАДЖУВАННЯ", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ВІДМОВА ВІД ВІДПОВІДАЛЬНОСТІ: МИ НЕ НЕСЕМО ВІДПОВІДАЛЬНОСТІ ЗА ЗАВАНТАЖЕНІ ПАКУНКИ. БУДЬ ЛАСКА, ВСТАНОВЛЮЙТЕ ТІЛЬКИ ПЕРЕВІРЕНЕ ПРОГРАМНЕ ЗАБЕЗПЕЧЕННЯ.", "Dark": "Темна", "Decline": "Не погоджуюсь", "Default": "За замовчуванням", "Default installation options for {0} packages": "Варіанти встановлення за замовчуванням для пакетів з {0}", "Default preferences - suitable for regular users": "Налаштування за замовчуванням - підходить для звичайних користувачів", "Default vcpkg triplet": "Триплет vcpk за замовчуванням", "Delete?": "Видалити?", "Dependencies:": "Залежності:", "Descendant": "Спаданням", "Description:": "Опис:", "Desktop shortcut created": "Створено ярлик на робочому столі", "Details of the report:": "Деталі звіту:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Розробка складна, і цей додаток безкоштовний. Але якщо вам сподобався застосунок, ви завжди можете <b>купити мені кави</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Встановлювати пакет по подвійному клацанню на ньому на \"{discoveryTab}\" (замість демонстрації інформації про пакет)", "Disable new share API (port 7058)": "Вимкнути нове API для функції \"поділитися\" (порт 7058)", "Disable the 1-minute timeout for package-related operations": "Відключити 1-хвилинний тайм-аут для операцій, пов'язаних з пакетами", "Disclaimer": "Відмова від відповідальності", "Discover Packages": "Доступні пакети", "Discover packages": "Доступні пакети", "Distinguish between\nuppercase and lowercase": "Враховувати регістр", "Distinguish between uppercase and lowercase": "Враховувати регістр", "Do NOT check for updates": "НЕ перевіряти наявність оновлень", "Do an interactive install for the selected packages": "Виконати інтерактивне встановлення для обраних пакетів", "Do an interactive uninstall for the selected packages": "Виконати інтерактивне видалення для обраних пакетів", "Do an interactive update for the selected packages": "Виконати інтерактивне оновлення для обраних пакетів", "Do not automatically install updates when the battery saver is on": "Не встановлювати оновлення автоматично, якщо увімкнутий режим економії енергії", "Do not automatically install updates when the network connection is metered": "Не встановлювати оновлення автоматично, якщо підключення до мережі є лімітним", "Do not download new app translations from GitHub automatically": "Не завантажувати нові переклади з GitHub автоматично", "Do not ignore updates for this package anymore": "Більше не ігнорувати оновлення цього пакету", "Do not remove successful operations from the list automatically": "Не видаляти успішні операції зі списку автоматично", "Do not show this dialog again for {0}": "Більше не показувати цей діалог для {0}", "Do not update package indexes on launch": "Не оновлювати індекси пакетів при запуску", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Чи ви погоджуєтесь з тим, що UniGetUI збиратиме та відправлятиме анонімну статистику використання, єдина ціль якої - краще зрозуміти та покращити користувацький досвід?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Чи вважаєте ви UniGetUI корисним? Якщо так, то ви можете підтримати мою роботу, щоби я продовжив перетворювати UniGetUI на найкращій графічний інтерфейс для менеджерів пакетів.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Чи вважаєте ви WingetUI корисним? Бажаєте підтримати розробника? Якщо це так, ви можете {0}, це дуже допоможе!", "Do you really want to reset this list? This action cannot be reverted.": "Ви дійсно хочете скинути цей список? Ці зміни буде неможливо відмінити.", "Do you really want to uninstall the following {0} packages?": "Ви дійсно хочете видалити наступні {0} пакетів?", "Do you really want to uninstall {0} packages?": "Ви дійсно хочете видалити {0} пакетів?", "Do you really want to uninstall {0}?": "Ви дійсно хочете видалити {0}?", "Do you want to restart your computer now?": "Ви хочете перезавантажити комп'ютер зараз?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Ви хочете перекласти UniGetUI своєю мовою? Дізнайтеся, як зробити свій внесок <a style=\"color:{0}\" href=\"{1}\"a>ТУТ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Не маєте бажання жертвувати? Не переймайтеся, ви завжди можете показати UniGetUI своїм друзям. Поширте UniGetUI.", "Donate": "Пожертвувати", "Done!": "Виконано!", "Download failed": "Завантаження не вдалося", "Download installer": "Завантажити інсталятор", "Download operations are not affected by this setting": "Це налаштування не впливає на операції завантаження", "Download selected installers": "Завантажити вибрані інсталятори", "Download succeeded": "Завантаження завершено успішно", "Download updated language files from GitHub automatically": "Автоматично завантажувати оновлені файли перекладу з GitHub", "Downloading": "Завантаження", "Downloading backup...": "Завантажуємо резервну копію…", "Downloading installer for {package}": "Завантаження інсталятора для {package}", "Downloading package metadata...": "Завантаження метаданих пакета...", "Enable Scoop cleanup on launch": "Увімкнути очищення Scoop під час запуску", "Enable WingetUI notifications": "Увімкнути сповіщення UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Увімкнути [експериментальний] покращений засіб усунення неполадок для WinGet", "Enable and disable package managers, change default install options, etc.": "Увімкнути та вимкнути менеджери пакетів, змінити налаштування встановлення за замовчуванням і т.д.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Увімкнути оптимізацію використання ЦП у фоновому режимі (див. Pull Request №3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Увімкнути фоновий API (Віджети UniGetUI та можливість ділитися, порт 7058).", "Enable it to install packages from {pm}.": "Увімкніть, щоб встановлювати пакети з {pm}.", "Enable the automatic WinGet troubleshooter": "Увімкнути автоматичний засіб усунення неполадок для WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Увімкнути новий модуль підвищення прав під брендом UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Увімкнути новий обробник вхідних даних з процесів (StdIn automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Вмикайте налаштування нижче ТОДІ Й ЛИШЕ ТОДІ, коли ви повністю розумієте що вони роблять і які наслідки та небезпеки вони можуть нести.", "Enable {pm}": "Увімкнути {pm}", "Enter proxy URL here": "Введіть URL проксі тут", "Entries that show in RED will be IMPORTED.": "Записи, які показані ЧЕРВОНИМ, будуть ІМПОРТОВАНІ.", "Entries that show in YELLOW will be IGNORED.": "Записи, які показані ЖОВТИМ, будуть ПРОІГНОРОВАНІ.", "Error": "Помилка", "Everything is up to date": "Все оновлено", "Exact match": "Точна відповідність", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Існуючи ярлики на вашому робочому столі будуть проскановані, вам буде необхідно вибрати які залишити, а які видалити.", "Expand version": "Розгорнути версію", "Experimental settings and developer options": "Експериментальні налаштування та опції розробника", "Export": "Експорт", "Export log as a file": "Експортувати журнал як файл", "Export packages": "Експортувати пакети", "Export selected packages to a file": "Експортувати вибрані пакети у файл", "Export settings to a local file": "Експортувати налаштування у локальний файл", "Export to a file": "Експорт у файл", "Failed": "Не вдалося", "Fetching available backups...": "Завантажуємо список резервних копій…", "Fetching latest announcements, please wait...": "Збираємо останні анонсування, будь ласка, зачекайте…", "Filters": "Фільтри", "Finish": "Кінець", "Follow system color scheme": "Дотримуватися колірної схеми системи", "Follow the default options when installing, upgrading or uninstalling this package": "Дотримуватися налаштувань за замовченням при встановленні, оновленні й видаленні цього пакета ", "For security reasons, changing the executable file is disabled by default": "З міркувань безпеки зміна виконавчого файлу відключена за замовчуванням.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "З міркувань безпеки, користувацькі аргументи командного рядка відключені за замовчуванням. Ви можете змінити це в налаштуваннях безпеки UniGetUI.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "З міркувань безпеки, перед- і після-операційні скрипти відключені за замовчуванням. Ви можете змінити це в налаштуваннях безпеки UniGetUI.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Примусово використовувати winget, скомпільований під ARM (ТІЛЬКИ ДЛЯ СИСТЕМ НА ARM64)", "Formerly known as WingetUI": "Раніше відомий як WingetUI", "Found": "Знайдено", "Found packages: ": "Знайдено пакети:", "Found packages: {0}": "Знайдено пакетів: {0}", "Found packages: {0}, not finished yet...": "Знайдено пакети: {0}, ще не завершено...", "General preferences": "Загальні налаштування", "GitHub profile": "<PERSON>ро<PERSON><PERSON><PERSON><PERSON>", "Global": "Глобальний", "Go to UniGetUI security settings": "Перейти в налаштування безпеки UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Чудове сховище невідомих, але корисних утиліт та інших цікавих пакетів.<br>Містить: <b>Утиліти, програми командного рядка, загальне програмне забезпечення (потрібно додатково bucket)</b>", "Great! You are on the latest version.": "Чудово! У вас найновіша версія.", "Grid": "Сітка", "Help": "Допомога", "Help and documentation": "Допомога та документація", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Тут ви можете змінити поведінку UniGetUI стосовно наступних ярликів. Виділення ярлика призведе до його видалення програмою UniGetUI, якщо він буде створений при наступних оновленнях. Якщо прапорець зняти, ярлик залишиться неторкнутим.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Привіт, мене звуть Марті, і я <i>розробник</i> WingetUI. WingetUI був повністю зроблений у мій вільний час!", "Hide details": "Приховати деталі", "Homepage": "Домашня сторінка", "Hooray! No updates were found.": "Ура! Оновлення не знайдено.", "How should installations that require administrator privileges be treated?": "Як слід ставитися до установок, що вимагають прав адміністратора?", "How to add packages to a bundle": "Як додати пакети до колекції", "I understand": "Я розумію", "Icons": "Плитки", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Якщо резервне копіювання в хмару увімкнуто, копія буде збережена як GitHub Gist в цей обліковий запис", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ігнорувати користувацькі перед- і після-операційні скрипти при імпорті пакетів з колекції", "Ignore future updates for this package": "Ігнорувати оновлення цього пакета", "Ignore packages from {pm} when showing a notification about updates": "Ігнорувати пакети з {pm} при показі сповіщень про оновлення", "Ignore selected packages": "Ігнорувати обрані пакети", "Ignore special characters": "Ігнорувати спеціальні символи", "Ignore updates for the selected packages": "Ігнорувати оновлення обраних пакетів", "Ignore updates for this package": "Ігнорувати оновлення цього пакета", "Ignored updates": "Ігноровані оновлення", "Ignored version": "Ігнорована версія", "Import": "Імпорт", "Import packages": "Імпортувати пакети", "Import packages from a file": "Імпортувати пакети з файлу", "Import settings from a local file": "Імпортувати налаштування з локального файлу", "In order to add packages to a bundle, you will need to: ": "Щоб додати пакети до колекції, вам необхідно:", "Initializing WingetUI...": "Запуск UniGetUI...", "Install": "Встановити", "Install Scoop": "Встан<PERSON>и<PERSON><PERSON>", "Install and more": "Встановлення й інше", "Install and update preferences": "Параметри встановлення й оновлення пакетів", "Install as administrator": "Встановити від імені адміністратора", "Install available updates automatically": "Автоматично встановлювати доступні оновлення", "Install location can't be changed for {0} packages": "Не можна змінювати місце встановлення для пакетів з {0}", "Install location:": "Місце встановлення:", "Install options": "Варіанти встановлення", "Install packages from a file": "Встановити пакети з файлу", "Install prerelease versions of UniGetUI": "Встановлювати підготовчі версії UniGetUI", "Install script": "Скрипт для встановлення", "Install selected packages": "Встановити вибрані пакети", "Install selected packages with administrator privileges": "Встановіть вибрані пакети з правами адміністратора", "Install selection": "Встановити вибране", "Install the latest prerelease version": "Встановити найновішу підготовчу версію", "Install updates automatically": "Встановлювати оновлення автоматично", "Install {0}": "Встановити {0}", "Installation canceled by the user!": "Встановлення скасовано користувачем!", "Installation failed": "Встановлення не вдалося", "Installation options": "Варіанти встановлення", "Installation scope:": "Рівень встановлення:", "Installation succeeded": "Встановлення завершено успішно", "Installed Packages": "Встановлені пакети", "Installed Version": "Встановлена версія", "Installed packages": "Встановлені пакети", "Installer SHA256": "SHA256 інсталятора", "Installer SHA512": "Х<PERSON>ш SHA512", "Installer Type": "Тип інсталятора", "Installer URL": "URL інсталятора", "Installer not available": "Інсталятор не доступний", "Instance {0} responded, quitting...": "Отримано відповідь від інстансу {0}, закриття...", "Instant search": "Миттєвий пошук", "Integrity checks can be disabled from the Experimental Settings": "Перевірка цілісності може бути відключена в експериментальних налаштуваннях", "Integrity checks skipped": "Перевірка хешу пропущена", "Integrity checks will not be performed during this operation": "Перевірка хешу не буде виконана під час цієї операції", "Interactive installation": "Інтерактивна інсталяція", "Interactive operation": "Інтерактивна операція", "Interactive uninstall": "Інтерактивне видалення", "Interactive update": "Інтерактивне оновлення", "Internet connection settings": "Налаштування з'єднання з інтернетом", "Is this package missing the icon?": "У цьому пакеті відсутній значок?", "Is your language missing or incomplete?": "Ваша мова відсутня чи неповна?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Не має гарант<PERSON><PERSON>, що облікові данні зберігаються безпечно, тож, по можливості, не використовуйте тут облікові данні вашого банківського рахунку", "It is recommended to restart UniGetUI after WinGet has been repaired": "Рекомендовано перезапустити UniGetUI після того, як WinGet буде відновлено", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Дуже радимо перевстановити UniGetUI щоби виправити ситуацію.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Схо<PERSON><PERSON>, WinGet несправний. Хочете спробувати відновити WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Схоже, що ви запустили UniGetUI від імені адміністратора, що не рекомендується. Ви все ще можете використовувати програму, але ми наполегливо рекомендуємо не запускати UniGetUI з правами адміністратора. Натисніть на \"{showDetails}\", щоб дізнатися чому.", "Language": "Мова", "Language, theme and other miscellaneous preferences": "Мова, тема та інші параметри", "Last updated:": "Останнє оновлення:", "Latest": "Остання", "Latest Version": "Остання версія", "Latest Version:": "Остання версія:", "Latest details...": "Останні подробиці...", "Launching subprocess...": "Запускаємо підпроцес…", "Leave empty for default": "Пусте за замовчуванням", "License": "Ліцензія", "Licenses": "Ліцензії", "Light": "Світла", "List": "Список", "Live command-line output": "Виведення командного рядка", "Live output": "Вивід наживо", "Loading UI components...": "Завантаження компонентів інтерфейсу...", "Loading WingetUI...": "Завантаження UniGetUI...", "Loading packages": "Завантаження пакетів", "Loading packages, please wait...": "Завантаження пакетів, будь ласка, зачекайте…", "Loading...": "Завантаження...", "Local": "Локально", "Local PC": "Цей ПК", "Local backup advanced options": "Розширені налаштування локального резервного копіювання", "Local machine": "Для всіх користувачів", "Local package backup": "Локальне резервне копіювання пакетів", "Locating {pm}...": "Пошук {pm}...", "Log in": "Увійти", "Log in failed: ": "Вхід не вдався:", "Log in to enable cloud backup": "Увійдіть, щоб увімкнути резервне копіювання в хмару", "Log in with GitHub": "Увійти через GitHub", "Log in with GitHub to enable cloud package backup.": "Увійдіть чере<PERSON>, щоб увімкнути резервне копіювання в хмару", "Log level:": "Рівень журналу:", "Log out": "Вийти", "Log out failed: ": "Вихід не вдався:", "Log out from GitHub": "Вийти з GitHub аккаунту", "Looking for packages...": "Шукаю пакети…", "Machine | Global": "Пристрій | Глобально", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Неправильно сформовані аргументи командного рядка можуть псувати пакети, або навіть можуть дозволити зловмиснику запустити код з підвищеними привілеями. Саме тому імпортування користувацьких аргументів командного рядка відключено за замовчуванням", "Manage": "Керувати", "Manage UniGetUI settings": "Керування налаштуваннями UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Налаштувати автозавантаження UniGetUI у застосунку \"Налаштування\"", "Manage ignored packages": "Керування ігнорованими пакетами", "Manage ignored updates": "Керування ігнорованими оновленнями", "Manage shortcuts": "Керування ярликами", "Manage telemetry settings": "Керування налаштуваннями телеметрії", "Manage {0} sources": "Керування джерелами для {0}", "Manifest": "Маніфест", "Manifests": "Маніфести", "Manual scan": "Ручне сканування", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Офіційний менеджер пакетів Microsoft. Повний відомих і перевірених пакетів<br>Містить: <b>Загальне програмне забезпечення, програми Microsoft Store</b>", "Missing dependency": "Відсутня залежність", "More": "Ще", "More details": "Більше деталей", "More details about the shared data and how it will be processed": "Більше деталей про дані, які передаються, та як вони обробляються", "More info": "Додаткова інформація", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "ПРИМІТКА: За<PERSON><PERSON>б усунення неполадок можна відключити у налаштуваннях UniGetUI, у секції WinGet", "Name": "Ім'я", "New": "Створити", "New Version": "Нова версія", "New bundle": "Нова колекція", "New version": "Нова версія", "Nice! Backups will be uploaded to a private gist on your account": "Славно! Резервні копії будуть завантажені у приватний Gist на вашому обліковому запису", "No": "Ні", "No applicable installer was found for the package {0}": "Підходящого інсталятора не знайдено для пакета {0}", "No dependencies specified": "Жодних залежностей не вказано", "No new shortcuts were found during the scan.": "В ході сканування нових ярликів не виявлено.", "No packages found": "Пакети не знайдено", "No packages found matching the input criteria": "Не знайдено жодного результату, що відповідає введеним критеріям", "No packages have been added yet": "Жодного пакету ще не було додано", "No packages selected": "Не вибрано жодного пакету", "No packages were found": "Жодного пакету не знайдено", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Жодна персональна інформація не збирається й не передається, зібрані данні анонімізуються, отже їх не можна зв'язати з вами.", "No results were found matching the input criteria": "Не знайдено жодного результату, що відповідає введеним критеріям", "No sources found": "Д<PERSON><PERSON><PERSON><PERSON>л не знайдено", "No sources were found": "Жодного джерела не знайдено", "No updates are available": "Немає доступних оновлень", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Менеджер пакетів Node JS. Повний бібліотек та інших утиліт, які обертаються навколо світу javascript<br>Містить: <b>Бібліотеки Node javascript та інші пов'язані з ними утиліти</b>", "Not available": "Не доступно", "Not finding the file you are looking for? Make sure it has been added to path.": "Не бачите файл, який ви шукали? Впевнитесь, що він був доданий у змінну path.", "Not found": "Не знайдено", "Not right now": "Не зараз", "Notes:": "Примітки:", "Notification preferences": "Параметри сповіщень", "Notification tray options": "Параметри панелі сповіщень", "Notification types": "Типи сповіщень", "NuPkg (zipped manifest)": "NuPkg (стиснутий маніфест)", "OK": "OK", "Ok": "Ok", "Open": "Відкрити", "Open GitHub": "Відк<PERSON><PERSON><PERSON><PERSON>", "Open UniGetUI": "Відкрити UniGetUI", "Open UniGetUI security settings": "Відкрити налаштування безпеки UniGetUI", "Open WingetUI": "Відкрити UniGetUI", "Open backup location": "Відкрити теку з резервними копіями", "Open existing bundle": "Відкрити колекцію", "Open install location": "Відкрити теку установки", "Open the welcome wizard": "Відкрити майстра початкового налаштування", "Operation canceled by user": "Операція відмінена користувачем", "Operation cancelled": "Операція була відмінена", "Operation history": "Історія операцій", "Operation in progress": "Операція виконується", "Operation on queue (position {0})...": "Операція в черзі (позиція {0})…", "Operation profile:": "Профіль операції:", "Options saved": "Опції збережено", "Order by:": "Сортувати за:", "Other": "Інше", "Other settings": "Інші налаштування", "Package": "Пак<PERSON>т", "Package Bundles": "Колекції пакетів", "Package ID": "ID пакета", "Package Manager": "Менед<PERSON><PERSON>р пакетів", "Package Manager logs": "Журнали пакетних менеджерів", "Package Managers": "Менеджери пакетів", "Package Name": "Ім'я пакета", "Package backup": "Резервне копіювання пакетів", "Package backup settings": "Налаштування резервного копіювання", "Package bundle": "Колекція пакетів", "Package details": "Інформація про пакет", "Package lists": "Списки пакетів", "Package management made easy": "Керування пакетами без зусиль", "Package manager": "Менед<PERSON><PERSON>р пакетів", "Package manager preferences": "Налаштування менеджерів пакетів", "Package managers": "Менеджери пакетів", "Package not found": "Пакет не знайдено", "Package operation preferences": "Налаштування операцій з пакетами", "Package update preferences": "Налаштування оновлень пакетів", "Package {name} from {manager}": "Пакет {name} з {manager}", "Package's default": "За замовчуванням для пакета", "Packages": "Пакети", "Packages found: {0}": "Знайдено пакетів: {0}", "Partially": "Частково", "Password": "Пароль", "Paste a valid URL to the database": "Вставте дійсний URL до бази даних", "Pause updates for": "Призупинити оновлення на", "Perform a backup now": "Виконати резервне копіювання зараз", "Perform a cloud backup now": "Виконати резервне копіювання в хмару зараз", "Perform a local backup now": "Виконати локальне резервне копіювання зараз", "Perform integrity checks at startup": "Проводити перевірку цілісності під час запуску", "Performing backup, please wait...": "Створюється резервна копія, зачекайте, будь ласка…", "Periodically perform a backup of the installed packages": "Періодично виконувати резервне копіювання встановлених пакетів", "Periodically perform a cloud backup of the installed packages": "Періодично виконувати резервне копіювання в хмару встановлених пакетів", "Periodically perform a local backup of the installed packages": "Періодично виконувати локальне резервне копіювання встановлених пакетів", "Please check the installation options for this package and try again": "Будь ласка, перевірте вибраний варіант встановлення для цього пакету, та спробуйте знову", "Please click on \"Continue\" to continue": "Будь ласка, натисніть на \"Продовжити\", щоб продовжити", "Please enter at least 3 characters": "Будь ласка, введіть хоча б 3 символи", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Зверніть увагу, що деякі пакети може бути неможливо встановити через увімкнені на цьому комп'ютері менеджери пакетів.", "Please note that not all package managers may fully support this feature": "Будь ласка, зау<PERSON><PERSON><PERSON><PERSON>е, що не всі менеджери пакетів повністю підтримують цю функціональність ", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Зверніть увагу, що пакети з певних джерел можуть бути недоступні для експорту. Вони позначені сірим кольором і не будуть експортовані.", "Please run UniGetUI as a regular user and try again.": "Будь ласка, перезапустіть UniGetUI від імені звичайного користувача, та спробуйте знову", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Будь ласка, подивіться на вивід з командного рядка, або в історію операцій, щоб отримати більше інформації про проблему.", "Please select how you want to configure WingetUI": "Будь ласка, виб<PERSON>р<PERSON>ть, як ви хочете налаштувати WingetUI", "Please try again later": "Будь ласка, спробуйте ще раз пізніше", "Please type at least two characters": "Будь ласка, введіть хоча б два символи", "Please wait": "Будь ласка, зачекайте", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Будь ласка, зачекайте, поки {0} встановлюється. Ви можете побачити чорне (або блакитне) вікно. Дочекайтесь його закриття.", "Please wait...": "Будь ласка, зачекайте...", "Portable": "Переносна", "Portable mode": "Портативний режим", "Post-install command:": "Команда після встановлення:", "Post-uninstall command:": "Команда після видалення:", "Post-update command:": "Команда після оновлення:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Менеджер пакетів для PowerShell. Знайди бібліотеки та скрипти які розширяють можливості PowerShell<br>Містить: <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Перед- і після-операційні команди можуть зробити кепсько вашому пристрою, якщо вони задумувались з цією метою. Імпортувати команди з колекції може бути дуже небезпечно, окрім випадків, коли ви довіряєте джерелу її походження", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Перед- і після-операційні команди будуть запущені до та після встановлення, оновлення чи видалення пакету. Усвідомлюйте, що вони може щось зламати, якщо ви не будете обачні", "Pre-install command:": "Команда перед встановленням:", "Pre-uninstall command:": "Команда перед видаленням:", "Pre-update command:": "Команда перед оновленням:", "PreRelease": "Підготовча", "Preparing packages, please wait...": "Підготовка пакетів, будь ласка, зачекайте…", "Proceed at your own risk.": "Продовжуйте на власний ризик.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Заборонити будь-яке підвищення прав через модуль підвищення UniGetUI або GSudo", "Proxy URL": "URL-адреса проксі", "Proxy compatibility table": "Таблиця сумісності з проксі", "Proxy settings": "Налаштування проксі", "Proxy settings, etc.": "Налаштування проксі та іншого.", "Publication date:": "Дата публікації:", "Publisher": "Видавець", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Менеджер бібліотек Python. Повний набір бібліотек python та інших утиліт, пов'язаних з python<br>Містить: <b>Бібліотеки python та пов'язані з ними утиліти</b>", "Quit": "Вийти", "Quit WingetUI": "Вийти з UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Зменшити кількість запитів UAC, підвищувати права установки за замовчуванням, розблокувати деякі небезпечні функції і т.д. ", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Зверніться до журналів UniGetUI, щоб отримати більше деталей стосовно пошкоджених файлів", "Reinstall": "Перевстановити", "Reinstall package": "Перевстановити пакет", "Related settings": "Пов'язані налаштування", "Release notes": "Нотатки про випуск", "Release notes URL": "Посилання на нотатки про випуск", "Release notes URL:": "Посилання на нотатки про випуск:", "Release notes:": "Список змін:", "Reload": "Перезавантажити", "Reload log": "Перезава<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> журнал", "Removal failed": "Видалення не вдалося", "Removal succeeded": "Видалення виконано успішно", "Remove from list": "Видалити зі списку", "Remove permanent data": "Видалити постійні дані", "Remove selection from bundle": "Прибрати вибране з колекції", "Remove successful installs/uninstalls/updates from the installation list": "Прибирати успішні встановлення/видалення/оновлення зі списку інсталяцій", "Removing source {source}": "Видалення джерела {source}", "Removing source {source} from {manager}": "Видаляємо джерело {source} з {manager}", "Repair UniGetUI": "Відновити UniGetUI", "Repair WinGet": "Відновити WinGet", "Report an issue or submit a feature request": "Повідомити про проблему чи запропонувати покращення", "Repository": "Репозито<PERSON><PERSON>й", "Reset": "Скинути", "Reset Scoop's global app cache": "Скинути кеш <PERSON> у системі", "Reset UniGetUI": "Скинути UniGetUI", "Reset WinGet": "Скинути WinGet", "Reset Winget sources (might help if no packages are listed)": "Скинути джерела Winget (може допомогти якщо в списку немає пакетів)", "Reset WingetUI": "Скинути UniGetUI", "Reset WingetUI and its preferences": "Скинути UniGetUI та його налаштування", "Reset WingetUI icon and screenshot cache": "Скинути кеш знімків екрана та піктограм UniGetUI", "Reset list": "Скинути список", "Resetting Winget sources - WingetUI": "Скидання дже<PERSON>ел WinGet — UniGetUI", "Restart": "Перезавантажити", "Restart UniGetUI": "Перезапустити UniGetUI", "Restart WingetUI": "Перезапуск UniGetUI", "Restart WingetUI to fully apply changes": "Перезапустіть UniGetUI, щоби повністю застосувати зміни", "Restart later": "Перезавантажити пізніше", "Restart now": "Перезавантажити зараз", "Restart required": "Потрібне перезавантаження", "Restart your PC to finish installation": "Перезавантажте комп'ютер, щоб завершити встановлення", "Restart your computer to finish the installation": "Перезавантажте комп'ютер для завершення встановлення", "Restore a backup from the cloud": "Відновити з резервної копії в хмарі", "Restrictions on package managers": "Обмеження для менеджерів пакетів", "Restrictions on package operations": "Обмеження для операцій з пакетами", "Restrictions when importing package bundles": "Обмеження при імпорті колекцій пакетів", "Retry": "Повторити", "Retry as administrator": "Повторити від імені адміністратора", "Retry failed operations": "Перезапустити невдалі операції", "Retry interactively": "Спробувати знов у інтерактивному режимі", "Retry skipping integrity checks": "Повторити без перевірки хешу", "Retrying, please wait...": "Пробуємо знову, будь ласка, зачекайте…", "Return to top": "Повернутися до початку", "Run": "Запустити", "Run as admin": "У режимі адміна", "Run cleanup and clear cache": "Запустити очищення та видалити кеш", "Run last": "Запустити останнім", "Run next": "Запустити наступним", "Run now": "Запустити зараз", "Running the installer...": "Запуск інсталятора...", "Running the uninstaller...": "Запуск деінсталятора...", "Running the updater...": "Запуск оновлення...", "Save": "Зберегти", "Save File": "Зберегти файл", "Save and close": "Зберегти та закрити", "Save as": "Зберегти як", "Save bundle as": "Зберегти як", "Save now": "Зберегти зараз", "Saving packages, please wait...": "Збереження пакетів, будь ласка, зачекайте…", "Scoop Installer - WingetUI": "Інста<PERSON><PERSON><PERSON><PERSON><PERSON> — UniGetUI", "Scoop Uninstaller - WingetUI": "Програма видалення Scoop – UniGetUI", "Scoop package": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search": "По<PERSON><PERSON>к", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Шукайте програмне забезпечення для настільних комп’ютерів, попереджайте мене, коли доступні оновлення, і не робіть дурниці. Я не хочу, щоб WingetUI був надто складним, я просто хочу мати простий <b>магазин програмного забезпечення</b>", "Search for packages": "Пошук пакетів", "Search for packages to start": "Для початку знайдіть пакети", "Search mode": "Метод пошуку", "Search on available updates": "Пошук доступних оновлень", "Search on your software": "Пошук у ваших програмах", "Searching for installed packages...": "Пошук встановлених пакетів...", "Searching for packages...": "Пошук пакетів...", "Searching for updates...": "Пошук оновлень...", "Select": "Вибрати", "Select \"{item}\" to add your custom bucket": "Виберіть \"{item}\" щоб додати користувацький bucket", "Select a folder": "Вибрати теку", "Select all": "Вибрати все", "Select all packages": "Вибрати всі пакети", "Select backup": "Виберіть копію", "Select only <b>if you know what you are doing</b>.": "Виби<PERSON><PERSON>йте, тільки <b>якщо ви знаєте, що робите</b>.", "Select package file": "Вибрати файл пакета", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Виберіть резервну копію для відкриття. Пізніше ви зможете переглянути, які пакети/програми ви хочете відновити.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Виберіть процеси, які мають бути закрити до того як цей пакет буде встановлено, оновлено чи видалено.", "Select the source you want to add:": "Виберіть джерело для додавання:", "Select upgradable packages by default": "Вибирати пакети, що можна оновити, за замовчуванням", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Виберіть, які <b>менеджери пакетів</b> використовувати ({0}), налаштуйте спосіб установлення пакетів, налаштуйте керування правами адміністратора тощо.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Відправлено рукостискання. Очікування відповіді слухаючого процесу... ({0}%)", "Set a custom backup file name": "Задати особливе ім'я для файла резервної копії", "Set custom backup file name": "Задати особливе ім'я для файла резервної копії", "Settings": "Опції", "Share": "Поділити<PERSON>я", "Share WingetUI": "<PERSON><PERSON><PERSON><PERSON>р UniGetUI", "Share anonymous usage data": "Збір анонімних даних про використання", "Share this package": "Поділитися пакетом", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Після зміни налаштувань безпеки вам буде необхідно перевідкрити колекцію, щоби зміни набули чинності.", "Show UniGetUI on the system tray": "Показувати UniGetUI у системному лотку", "Show UniGetUI's version and build number on the titlebar.": "Показувати версію UniGetUI на панелі заголовка", "Show WingetUI": "Показати UniGetUI", "Show a notification when an installation fails": "Показувати повідомлення в разі збою встановлення", "Show a notification when an installation finishes successfully": "Показувати сповіщення про успішне завершення інсталяції", "Show a notification when an operation fails": "Показувати сповіщення про неуспішні операції", "Show a notification when an operation finishes successfully": "Показувати сповіщення про успішні операції", "Show a notification when there are available updates": "Показувати сповіщення, коли є доступні оновлення", "Show a silent notification when an operation is running": "Показувати беззвучне сповіщення, поки операція проводиться", "Show details": "Показати деталі", "Show in explorer": "Показати в Провіднику", "Show info about the package on the Updates tab": "Показати інформацію про пакет на вкладці Оновлення", "Show missing translation strings": "Показати відсутні рядки перекладу", "Show notifications on different events": "Показувати сповіщення про різні події", "Show package details": "Показати інформацію про пакет", "Show package icons on package lists": "Відображати піктограми пакетів", "Show similar packages": "Показувати схожі пакети", "Show the live output": "Показати виведення консолі", "Size": "Розмір", "Skip": "Пропустити", "Skip hash check": "Не перевіряти хеш", "Skip hash checks": "Пропустити перевірки хешу", "Skip integrity checks": "Пропустити перевірку хешу", "Skip minor updates for this package": "Пропустити мінорні оновлення", "Skip the hash check when installing the selected packages": "Пропустити перевірку хешу під час встановлення вибраних пакетів", "Skip the hash check when updating the selected packages": "Пропустити перевірку хешу під час оновлення вибраних пакетів", "Skip this version": "Пропустити версію", "Software Updates": "Оновлення програм", "Something went wrong": "Щось пішло не так", "Something went wrong while launching the updater.": "Щось пішло не так при запуску програми оновлення.", "Source": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Source URL:": "URL джерела:", "Source added successfully": "Джерело успішно додано", "Source addition failed": "Не вдалося додати джерело", "Source name:": "Назва джерела:", "Source removal failed": "Не вдалося видалити джерело", "Source removed successfully": "Джерело успішно видалено", "Source:": "Дже<PERSON>ело:", "Sources": "Дж<PERSON><PERSON><PERSON><PERSON>а", "Start": "Почати", "Starting daemons...": "Запуск daemons...", "Starting operation...": "Починаємо операцію…", "Startup options": "Параметри запуску", "Status": "Статус", "Stuck here? Skip initialization": "Застрягли тут? Пропустити ініціалізацію", "Success!": "Успіх!", "Suport the developer": "Підтримати розробника", "Support me": "Підтримай мене", "Support the developer": "Підтримати розробника", "Systems are now ready to go!": "Тепер системи готові до роботи!", "Telemetry": "Телеметрія", "Text": "Текст", "Text file": "Текстовий файл", "Thank you ❤": "Дякую ❤", "Thank you 😉": "Дякую 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Пакетний менеджер для Rust.<br>Містить: <b>Бібліотеки та програми, написані мовою Rust</b> ", "The backup will NOT include any binary file nor any program's saved data.": "Резервна копія НЕ буде включати в собі жодних файлів програм або їх збережених даних.", "The backup will be performed after login.": "Резервне копіювання буде виконано після входу у систему.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Резервна копія буде включати в себе повний список усіх встановлених пакетів та вибрані варіанти їх встановлення. Проігноровані оновлення та пропущені версії також будуть збережені.", "The bundle was created successfully on {0}": "Колекція була збережена у файл {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Колекція, яку ви намагаєтеся відкрити, сформована некоректно. Будь ласка, перевірте файл, та спробуйте знову.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Контрольна сума інсталятора не збігається з очікуваним значенням, і автентичність інсталятора не може бути перевірено. Якщо ви довіряєте видавцю, {0} пакет знову пропускає хеш-перевірку.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Класичний менеджер пакетів для Windows. Тут ви знайдете все, що потрібно. <br>Містить: <b>Загальне програмне забезпечення</b>", "The cloud backup completed successfully.": "Резервне копіювання в хмару завершено успішно.", "The cloud backup has been loaded successfully.": "Резервна копія з хмари була успішно завантажена.", "The current bundle has no packages. Add some packages to get started": "У відкритій колекції немає жодного пакету. Додайте кілька, щоби почати.", "The executable file for {0} was not found": "Виконуваний файл для {0} не знайдено", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Наступні налаштування будуть застосовані кожного разу коли пакет з {0} встановлюється, оновлюється чи видаляється.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Наступні пакети будуть експортовані у файл JSON. Жодні користувацькі дані або двійкові файли не буде збережено.", "The following packages are going to be installed on your system.": "Наступні пакети буде встановлено у вашій системі.", "The following settings may pose a security risk, hence they are disabled by default.": "Наступні налаштування можуть становити загрозу безпеці, тож вони відключені за замовчуванням.", "The following settings will be applied each time this package is installed, updated or removed.": "Наступні налаштування будуть застосовані кожного разу коли цей пакет встановлюється, оновлюється чи видаляється.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Наступні налаштування будуть застосовані кожного разу коли цей пакет встановлюється, оновлюється чи видаляється. Вони будуть збережені автоматично.", "The icons and screenshots are maintained by users like you!": "Іконки та скріншоти підтримуються такими ж користувачами, як і Ви!", "The installation script saved to {0}": "Скрипт для встановлення був збережений як {0}", "The installer authenticity could not be verified.": "Автентичність інсталятора неможливо перевірити. ", "The installer has an invalid checksum": "Інсталятор має недійсну контрольну суму", "The installer hash does not match the expected value.": "Хеш інсталятора не збігається з очікуваним значенням.", "The local icon cache currently takes {0} MB": "Локальний кеш піктограм займає {0} МБ", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Основною метою цього проекту є створення інтуїтивно зрозумілого інтерфейсу для управління найпоширенішими CLI-менеджерами пакетів для Windows, такими як Winget та Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Пакет \"{0}\" не знайдено в пакетному менеджері \"{1}\"", "The package bundle could not be created due to an error.": "Колекцію пакетів неможливо створити через помилку.", "The package bundle is not valid": "Ця колекція пакетів сформована не коректно", "The package manager \"{0}\" is disabled": "Пакетний менеджер \"{0}\" відключено", "The package manager \"{0}\" was not found": "Пакетний менеджер \"{0}\" не знайдено", "The package {0} from {1} was not found.": "Пакет {0} з {1} не був знайдений", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Перераховані тут пакети не враховуватимуться під час перевірки оновлень. Двічі клацніть по них або натисніть кнопку праворуч, щоб перестати ігнорувати їхні оновлення.", "The selected packages have been blacklisted": "Вибраний пакет був доданий до чорного списку", "The settings will list, in their descriptions, the potential security issues they may have.": "Кожне налаштування має опис потенційний ризиків, які воно може нести.", "The size of the backup is estimated to be less than 1MB.": "Очікуваний розмір резервної копії — менше 1 МБ.", "The source {source} was added to {manager} successfully": "Дж<PERSON><PERSON><PERSON><PERSON><PERSON> {source} було успішно додано до {manager}", "The source {source} was removed from {manager} successfully": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {source} було успішно видалено з {manager}", "The system tray icon must be enabled in order for notifications to work": "Для функціонування сповіщень іконка у системному лотку має бути увімкнена", "The update process has been aborted.": "Процес оновлення був перерваний", "The update process will start after closing UniGetUI": "Процес оновлення почнеться після закриття UniGetUI", "The update will be installed upon closing WingetUI": "Оновлення буде встановлене після закриття UniGetUI", "The update will not continue.": "Оновлення не буде продовжено", "The user has canceled {0}, that was a requirement for {1} to be run": "Користувач відмінив{0}, що є передумовою для запуску {1}", "There are no new UniGetUI versions to be installed": "Немає більш нової версії UniGetUI для встановлення", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Деякі операції ще не завершені. Вихід з UniGetUI може призвести до їх невдалого завершення. Бажаєте продовжити?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "На YouTube є кілька чудових відеороликів, що демонструють UniGetUI та його можливості. Ви можете дізнатися корисні трюки та поради!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Є дві основні причини не запускати UniGetUI від імені адміністратора:\n Перша полягає в тому, що менеджер пакетів Scoop може викликати проблеми з деякими командами при запуску з правами адміністратора.\n Друга причина полягає в тому, що запуск UniGetUI від імені адміністратора означає, що будь-який пакет, який ви завантажуєте, буде запущений від імені адміністратора (а це небезпечно).\n Пам'ятайте, що якщо вам потрібно встановити певний пакет від імені адміністратора, ви завжди можете натиснути правою кнопкою миші на пункт → Встановити/Оновити/Видалити від імені адміністратора.", "There is an error with the configuration of the package manager \"{0}\"": "Сталася помилка зв'язана з конфігурацією менеджера пакетів \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Йде процес встановлення. Якщо ви закриєте UniGetUI, інсталяція може завершитися невдало і мати неочікувані результати. Ви все ще хочете вийти з UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Це програми, що відповідають за встановлення, оновлення та видалення пакетів.", "Third-party licenses": "Сторонні ліцензії", "This could represent a <b>security risk</b>.": "Це може становити <b>ризик для безпеки</b>.", "This is not recommended.": "Це не рекомендується.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Ймовірно, це пов'язано з тим, що пакет, який вам було надіслано, було видалено або опубліковано у менеджері пакетів, який ви не ввімкнули. Отримано ідентифікатор {0}", "This is the <b>default choice</b>.": "Це <b>вибір за замовчуванням</b>.", "This may help if WinGet packages are not shown": "Це може допомогти, якщо пакети WinGet не відображаються", "This may help if no packages are listed": "Це може допомогти, якщо пакети не відображаються", "This may take a minute or two": "Це може зайняти одну-дві хвилини.", "This operation is running interactively.": "Ця операція запущена інтерактивно.", "This operation is running with administrator privileges.": "Ця операція запущена з правами адміністратора.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Вибір цієї опції СПРИЧИНЯТИМЕ проблеми. Будь які операції, що нездатні підвищити собі права, завершаться НЕВДАЛО. Встановлення/оновлення/видалення від імені адміністратора НЕ ПРАЦЮВАТИМЕ.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Ця колекція пакетів містить деякі потенційно небезпечні налаштування, що можуть ігноруватися за замовчуванням.", "This package can be updated": "Цей пакет можна оновити", "This package can be updated to version {0}": "Пакет було оновлено до версії {0}", "This package can be upgraded to version {0}": "Пакет було оновлено до версії {0}", "This package cannot be installed from an elevated context.": "Цей пакет не можна встановити від імені адміністратора.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "В цьому пакеті не вистачає знімків екрану чи піктограми? Зробіть внесок у UniGetUI, додавши відсутні піктограми та знімки екрану в нашу відкриту та публічну базу даних.", "This package is already installed": "Цей пакет вже встановлено", "This package is being processed": "Цей пакет обробляється", "This package is not available": "Цей пакет не доступний", "This package is on the queue": "Цей пакет в черзі", "This process is running with administrator privileges": "Процес запущено з правами адміністратора", "This project has no connection with the official {0} project — it's completely unofficial.": "Цей проект не зв'язаний з офіційним проектом {0} — він повністю не офіційний.", "This setting is disabled": "Ця опція не доступна", "This wizard will help you configure and customize WingetUI!": "Цей майстер допоможе вам налаштувати WingetUI!", "Toggle search filters pane": "Показати або сховати панель фільтрів", "Translators": "Переклад<PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Спробувати завершити процес, який відмовляється закритися за запитом", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Увімкнення цієї опції дозволить змінювати виконавчій файл, який використовується для взаємодії з менеджерами пакетами. Це дозволить точну кастомізацію вашого процесу встановлення, але може бути небезпечним", "Type here the name and the URL of the source you want to add, separed by a space.": "Введіть тут ім'я та URL джерела, яке ви хочете додати, розділені пробілом", "Unable to find package": "Неможливо знайти пакет", "Unable to load informarion": "Не вдалося завантажити інформацію", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI збирає анонімні дані про використання, щоб покращити користувацький досвід.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI збирає анонімну статистику використання, єдина ціль якої - краще зрозуміти та покращити користувацький досвід.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI виявив новий ярлик на робочому столі, який можна автоматично видалити.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI виявив наступні ярлики робочого стола, які можна автоматично видалити при наступних оновленнях пакетів.", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI виявив {0} нових ярликів на робочому столі, які можна автоматично видалити.", "UniGetUI is being updated...": "UniGetUI оновлюється…", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI не має відношення до жодного з сумісних менеджерів пакетів. UniGetUI — це незалежний проект.", "UniGetUI on the background and system tray": "UniGetUI у фоні та системному лотку", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI чи деякі з його компонентів відсутні або пошкоджені.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI потребує {0} для функціонування, але його не знайдено в вашій системі.", "UniGetUI startup page:": "Початковий екран UniGetUI:", "UniGetUI updater": "Оновлення UniGetUI", "UniGetUI version {0} is being downloaded.": "UniGetUI версії {0} завантажується.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} готовий для встановлення.", "Uninstall": "Видалити", "Uninstall Scoop (and its packages)": "Вида<PERSON><PERSON><PERSON><PERSON> (і його пакети)", "Uninstall and more": "Видалення й інше", "Uninstall and remove data": "Видалити разом з даними", "Uninstall as administrator": "Видаліть як адміністратор", "Uninstall canceled by the user!": "Деінсталяцію скасовано користувачем!", "Uninstall failed": "Видалення не вдалося", "Uninstall options": "Варіанти видалення", "Uninstall package": "Видалити пакет", "Uninstall package, then reinstall it": "Видалити пакет, потім встановити наново", "Uninstall package, then update it": "Видалити пакет, потім оновити його", "Uninstall previous versions when updated": "Видалити попередні версії при оновленні", "Uninstall selected packages": "Видалити вибрані пакети", "Uninstall selection": "Видалити вибране", "Uninstall succeeded": "Видалення закінчилося вдало", "Uninstall the selected packages with administrator privileges": "Видалити вибрані пакети з правами адміністратора", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Пакети з можливістю видалення від джерела \"{0}\" не були опубліковані в жодному з доступних менеджерів пакетів, тому інформація про них відсутня.", "Unknown": "Невідомо", "Unknown size": "Розмір невідомий", "Unset or unknown": "Невідома або невстановлена", "Up to date": "Найновіший", "Update": "Оновити", "Update WingetUI automatically": "Оновлювати UniGetUI автоматично", "Update all": "Оновити все", "Update and more": "Оновлення й інше", "Update as administrator": "Оновити від імені адміністратора", "Update check frequency, automatically install updates, etc.": "Частота перевірки, автоматичне встановлення оновлень та інше.", "Update checking": "Перевірка оновлень", "Update date": "Дата оновлення", "Update failed": "Оновлення не вдалося", "Update found!": "Оновлення знайдено!", "Update now": "Оновити зараз", "Update options": "Варіанти оновлення", "Update package indexes on launch": "Оновлювати індекс пакетів при запуску", "Update packages automatically": "Оновлювати пакети автоматично", "Update selected packages": "Оновити вибрані пакети", "Update selected packages with administrator privileges": "Оновити вибрані пакети з правами адміністратора", "Update selection": "Оновити вибране", "Update succeeded": "Оновлення виконано успішно", "Update to version {0}": "Оновити до версії {0}", "Update to {0} available": "Доступне оновлення до {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Оновлювати профілі Git для vcpkg автоматично (потребує встановленого Git)", "Updates": "Оновлення", "Updates available!": "Є оновлення!", "Updates for this package are ignored": "Оновлення для цього пакета ігноруються", "Updates found!": "Оновлення знайдено!", "Updates preferences": "Налаштування оновлень", "Updating WingetUI": "UniGetUI оновлюється", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Використовувати старий вбудований WinGet замість командлетів на PowerShell", "Use a custom icon and screenshot database URL": "Вкажіть URL до користувацької бази даних значків та знімків екрану", "Use bundled WinGet instead of PowerShell CMDlets": "Використовувати вбудований WinGet замість командлетів на PowerShell", "Use bundled WinGet instead of system WinGet": "Використовувати вбудований WinGet замість системного", "Use installed GSudo instead of UniGetUI Elevator": "Використовувати встановлений GSudo замість вбудованого модуля підвищення UniGetUI", "Use installed GSudo instead of the bundled one": "Використовувати встановлений GSudo замість комплектного (потребує перезапуску програми)", "Use system Chocolatey": "Використовувати Chocolatey, встановлений у системі", "Use system Chocolatey (Needs a restart)": "Використовувати системний Chocolatey (потрібен перезапуск)", "Use system Winget (Needs a restart)": "Використовувати системний Winget (потрібен перезапуск)", "Use system Winget (System language must be set to english)": "Використовувати системний Winget (мова системи має бути встановлена на англійську)", "Use the WinGet COM API to fetch packages": "Використовувати WinGet COM API для отримання пакетів", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Використовувати модуль на PowerShell для WinGet замість WinGet COM API", "Useful links": "Корисні посилання", "User": "Користув<PERSON><PERSON>", "User interface preferences": "Налаштування інтерфейсу користувача", "User | Local": "Користувач | Локально", "Username": "Ім'я користувача", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Використання UniGetUI означає що ви приймаєте GNU Lesser General Public License v2.1 License", "Using WingetUI implies the acceptation of the MIT License": "Використання UniGetUI означає згоду з ліцензією MIT.", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Корнева тека для Vcpkg не знайдена. Будь ласка, встановить змінну середовища %VCPKG_ROOT% чи вкажіть її у налаштуваннях UniGetUI.", "Vcpkg was not found on your system.": "Vcpkg не знайдено в вашій системі", "Verbose": "Детальний", "Version": "Версія", "Version to install:": "Версія для встановлення:", "Version:": "Версія:", "View GitHub Profile": "Профіль на GitHub", "View WingetUI on GitHub": "Відкрити UniGetUI на GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Перегляньте вихідний код UniGetUI. Звідти ви можете повідомляти про помилки, пропонувати функції або, навіть, безпосередньо внести свій внесок у проект UniGetUI", "View mode:": "Подання:", "View on UniGetUI": "Відкрити у UniGetUI", "View page on browser": "Відкрити сторінку в браузері", "View {0} logs": "Продивити<PERSON><PERSON> журнал {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Зачекати наявності підключення до інтернету перед тим, як виконувати задачі, які потребують цього підключення.", "Waiting for other installations to finish...": "Чекаємо, коли закінчаться інші інсталяції...", "Waiting for {0} to complete...": "Очікуємо завершення {0}…", "Warning": "Увага", "Warning!": "Увага!", "We are checking for updates.": "Ми перевіряємо оновлення.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Не вдалося завантажити детальну інформацію про цей пакет, оскільки його не було знайдено в жодному з ваших джерел пакетів.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Не вдалося завантажити детальну інформацію про цей пакет, оскільки його не було встановлено з доступного менеджера пакетів.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Ми не змогли {action} {package}. Будь ласка, спробуйте ще раз пізніше. Натисніть \"{showDetails}\", щоб отримати журнали інсталятора.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Ми не змогли {action} {package}. Будь ласка, спробуйте ще раз пізніше. Натисніть \"{showDetails}\", щоб отримати журнали інсталятора.", "We couldn't find any package": "Ми не змогли знайти жодного пакету", "Welcome to WingetUI": "Ласкаво просимо до UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Встановлювати також вже встановлені пакети при пакетному встановленні з колекції", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Коли виявлені нові ярлики, видаляти їх автоматично замість відображення цього діалогу.", "Which backup do you want to open?": "Яку резервну копію ви бажаєте відкрити?", "Which package managers do you want to use?": "Які менеджери пакетів ви хочете використовувати?", "Which source do you want to add?": "Яке джерело ви бажаєте додати?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Хоча Winget можна використовувати з UniGetUI, UniGetUI можна використовувати з іншими пакетними менеджерами, що може збивати з пантелику. В минулому, UniGetUI був спроектований для роботи тільки з Winget, але це більше не так, і тому WingetUI більше не представляє те, що це проект бажає досягти.", "WinGet could not be repaired": "Неможливо відновити WinGet", "WinGet malfunction detected": "Виявлена проблема з WinGet ", "WinGet was repaired successfully": "WinGet був успішно відновлений", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI — Все оновлено", "WingetUI - {0} updates are available": "UniGetUI — доступні оновлення {0}", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "Домашня сторінка UniGetUI", "WingetUI Homepage - Share this link!": "Домашня сторінка UniGetUI - поширте це посилання!", "WingetUI License": "Ліцензія UniGetUI", "WingetUI Log": "<PERSON><PERSON><PERSON><PERSON>л UniGetUI", "WingetUI Repository": "Репозиторій UniGetUI", "WingetUI Settings": "Налаштування UniGetUI", "WingetUI Settings File": "Файл налаштувань UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI використовує наступні бібліотеки. Без них UniGetUI було б неможливо створити.", "WingetUI Version {0}": "UniGetUI версії {0}", "WingetUI autostart behaviour, application launch settings": "Поведінка автозапуску UniGetUI, налаштування запуску програми", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI може перевіряти, чи є оновлення для вашим програм, та встановлювати їх автоматично (за вашим бажанням)", "WingetUI display language:": "Мова відображення UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI був запущений від імені адміністратора, що не рекомендується. Коли UniGetUI запущений таким чином, КОЖНА операція, яку запускає UniGetUI, буде мати права адміністратора. Ви все ще можете користуватися застосунком, але ми дуже радимо не запускати UniGetUI від імені адміністратора.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI був перекладений на більше ніж 40 мов дякуючи добровільним перекладачам. Дякую 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI не перекладено машинним способом! Наступні користувачі брали участь у перекладах:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI — це застосунок, який робить керування вашим програмним забезпеченням простіше: він надає графічний інтерфейс \"все в одному\" для ваших менеджерів пакетів.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI буде перейменовано, щоб відкреслити різницю між UniGetUI (інтерфейс, який ви використовуєте) та WinGet (менеджер пакетів від Microsoft, з яким я не зв'язаний).", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI оновлюється. Після закінчення UniGetUI перезапуститься самостійно", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI безкоштовний, і він буде безкоштовним завжди. Жодної реклами чи кредитних карт, ніяких преміальних версій. 100% безкоштовний, назавжди. ", "WingetUI log": "<PERSON><PERSON><PERSON><PERSON>л UniGetUI", "WingetUI tray application preferences": "Налаштування WingetUI у треї", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI використовує наступні бібліотеки. Без них UniGetUI було б неможливо створити.", "WingetUI version {0} is being downloaded.": "UniGetUI версії {0} завантажується.", "WingetUI will become {newname} soon!": "UniGetUI скоро стане {newname}", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI не буде періодично перевіряти наявність оновлень. Вони, як і раніше, перевірятимуться під час запуску, але ви не будете попереджені про них.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI відображатиме запит UAC щоразу, коли для встановлення пакета потрібне підвищення прав.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI скоро стане {newname}. Це відповідає змінам в застосунку. Я (розробник) продовжу розробку проекта (як я роблю зараз), але під іншою назвою.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI був би неможливим без допомоги наших дорогих учасників. Подивіться їхній профіль на GitHub, без них WingetUI не існував би!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI було б неможливо створити без допомоги учасників. Дякую вам всім 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} готовий до встановлення.", "Write here the process names here, separated by commas (,)": "Введіть імена процесів тут, розділивши комами (,)", "Yes": "Так", "You are logged in as {0} (@{1})": "Ви увійшли як {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Ви можете змінити цю поведінку в налаштуваннях безпеки UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Ви можете задати команди, які будуть запущені до чи після встановлення, оновлення або видалення цього пакету. Вони будуть запущені з командного рядка, тож тут можна використовувати CMD скрипти.", "You have currently version {0} installed": "У вас встановлено версію {0} ", "You have installed WingetUI Version {0}": "У вас встановлена UniGetUI версії {0}", "You may lose unsaved data": "Ви можете втратити незбережені данні", "You may need to install {pm} in order to use it with WingetUI.": "Можливо, вам необхідно встановити {pm}, щоб використовувати його з UniGetUI.", "You may restart your computer later if you wish": "Ви можете перезавантажити комп'ютер пізніше, якщо хочете", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Права адміністратора будуть запитані тільки один раз, потім вони будуть надані пакетам, які їх запитують.", "You will be prompted only once, and every future installation will be elevated automatically.": "Права адміністратора будуть запитані тільки один раз, і права кожної майбутньої установки будуть підвищені автоматично.", "You will likely need to interact with the installer.": "Вірогідно, вам доведеться взаємодіяти за інсталятором.", "[RAN AS ADMINISTRATOR]": "ЗАПУЩЕНО ВІД ІМЕНІ АДМІНІСТРАТОРА", "buy me a coffee": "купити мені каву", "extracted": "видобуто", "feature": "функція", "formerly WingetUI": "колись WingetUI", "homepage": "Домашня сторінка", "install": "встановити", "installation": "встановлення", "installed": "встановлено", "installing": "встановлення", "library": "бібліотека", "mandatory": "обов'язкова", "option": "опція", "optional": "необов'язкова", "uninstall": "видалити", "uninstallation": "видалення", "uninstalled": "видалено", "uninstalling": "видалення", "update(noun)": "оновлення", "update(verb)": "оновити", "updated": "оновлено", "updating": "оновлення", "version {0}": "версії {0}", "{0} Install options are currently locked because {0} follows the default install options.": "Варіанти встановлення для {0} заблоковані тому, що {0} дотримується налаштувань встановлення за замовчуванням.", "{0} Uninstallation": "{0} Видалення", "{0} aborted": "{0} перервано", "{0} can be updated": "{0} Можна оновити", "{0} can be updated to version {1}": "{0} можна оновити до версії {1}", "{0} days": "{0} дня", "{0} desktop shortcuts created": "Створено {0} ярликів на робочому столі.", "{0} failed": "{0} не вдалося", "{0} has been installed successfully.": "{0} було успішно встановлено.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} було успішно встановлено. Рекомендовано перезапустити UniGetUI для завершення установки.", "{0} has failed, that was a requirement for {1} to be run": "{0} завершилося невдало, що було передумовою для запуску {1}", "{0} homepage": "Cтор<PERSON>н<PERSON><PERSON> {0}", "{0} hours": "{0} години", "{0} installation": "встановлення {0}", "{0} installation options": "Варіанти встановлення {0}", "{0} installer is being downloaded": "Інсталятор для {0} завантажується", "{0} is being installed": "Встановлюється {0}", "{0} is being uninstalled": "Видаляється {0}", "{0} is being updated": "{0} оновлюється", "{0} is being updated to version {1}": "{0} був оновлений до версії {1}", "{0} is disabled": "{0} вимкнено", "{0} minutes": "{0} хвилини", "{0} months": "{0} м<PERSON><PERSON><PERSON><PERSON><PERSON>в", "{0} packages are being updated": "{0} пакетів оновлюється", "{0} packages can be updated": "{0} пакетів можна оновити", "{0} packages found": "Знайдено пакетів: {0}", "{0} packages were found": "Знайдено {0} пакетів.", "{0} packages were found, {1} of which match the specified filters.": "Знайдено {0} пакетів, {1} з яких підходять під вибрані фільтри.", "{0} selected": "{0} вибрано", "{0} settings": "Налаштування {0}", "{0} status": "Статус {0}", "{0} succeeded": "{0} виконано успішно", "{0} update": "{0} Оновлення", "{0} updates are available": "Доступно {0} оновлень", "{0} was {1} successfully!": "{0} був {1} успішно!", "{0} weeks": "{0} тижня", "{0} years": "{0} років", "{0} {1} failed": "{0} {1} не вдалося", "{package} Installation": "Встановлення {package}", "{package} Uninstall": "Видалення {package}", "{package} Update": "Оновлення {package}", "{package} could not be installed": "Неможливо встановити {package}", "{package} could not be uninstalled": "{package} неможливо видалити", "{package} could not be updated": "Неможливо оновити {package}", "{package} installation failed": "Не вдалося встановити {package}", "{package} installer could not be downloaded": "Інсталятор для {package} неможливо завантажити", "{package} installer download": "Завантаження інсталятора для {package}", "{package} installer was downloaded successfully": "Інсталятор для {package} було успішно завантажено", "{package} uninstall failed": "Не вдалося видалити {package}", "{package} update failed": "Не вдалося оновити {package}", "{package} update failed. Click here for more details.": "Не вдалося оновити {package}. Натисніть тут для подробиць.", "{package} was installed successfully": "{package} було успішно встановлено", "{package} was uninstalled successfully": "{package} було успішно видалено", "{package} was updated successfully": "{package} було успішно оновлено", "{pcName} installed packages": "Пакети, встановлені на {pcName}", "{pm} could not be found": "{pm} не знайдено", "{pm} found: {state}": "{pm} знайдено: {state}", "{pm} is disabled": "{pm} вимкнено", "{pm} is enabled and ready to go": "{pm} увімкнений та готовий до використання", "{pm} package manager specific preferences": "Особливі налаштування менеджера пакетів {pm}", "{pm} preferences": "Налаштування {pm}", "{pm} version:": "Версія {pm}:", "{pm} was not found!": "{pm} не знайдено!"}