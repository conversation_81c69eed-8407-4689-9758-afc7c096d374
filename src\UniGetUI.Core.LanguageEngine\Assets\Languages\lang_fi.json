{"\"{0}\" is a local package and can't be shared": "\"{0}\" on p<PERSON><PERSON><PERSON><PERSON> paketti, eikä sitä voi jakaa", "\"{0}\" is a local package and does not have available details": "\"{0}\" on p<PERSON><PERSON><PERSON><PERSON> pake<PERSON>, e<PERSON><PERSON> siinä ole saatavilla tietoja", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" on p<PERSON><PERSON><PERSON><PERSON> paketti, eikä se ole yhteensopiva tämän ominaisuuden kanssa", "(Last checked: {0})": "(<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>: {0})", "(Number {0} in the queue)": "(Numero {0} j<PERSON><PERSON>)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@simakuutio", "0 packages found": "0 pakettia <PERSON>", "0 updates found": "0 päivitystä löytyi", "1 - Errors": "1 - <PERSON>ir<PERSON><PERSON>", "1 day": "1 päivä", "1 hour": "1 tunti", "1 month": "1 kuukausi", "1 package was found": "1 paketti löytyi", "1 update is available": "1 päivitys valmiina", "1 week": "1 viikko", "1 year": "1 vuosi", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON> \"{0}\" tai \"{1}\".", "2 - Warnings": "2 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON>, jotka haluat lis<PERSON> ni<PERSON>, ja valitse niiden vasemmanpuoleisin valintaruutu.", "3 - Information (less)": "3 - <PERSON><PERSON><PERSON> (vähemmän)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON>, jotka haluat lisät<PERSON> nippuun, on valittu, etsi työkalupalk<PERSON> vaihtoehto \"{0}\" ja napsauta sitä.", "4 - Information (more)": "4 - <PERSON><PERSON><PERSON> (en<PERSON><PERSON><PERSON><PERSON>)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON><PERSON> on l<PERSON><PERSON><PERSON>pp<PERSON>. V<PERSON> jatkaa pakettien lisäämistä tai viedä paketin.", "5 - information (debug)": "5 - <PERSON><PERSON><PERSON> (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Suosittu C/C++-kir<PERSON><PERSON>hallinta. Täynnä C/C++-kirjastoja ja muita C/C++:aan liittyviä apuohjelmia<br>Sisältää: <b>C/C++-kirjastot ja niihin liittyvät apuohjelmat</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Arkisto täynnä työkaluja ja suoritettavia tiedost<PERSON>, jotka on suunniteltu Microsoftin .NET-ekosysteemiä silmällä pitäen.<br>Sisältää: <b>.NETiin liittyvät työkalut ja komentosarjat</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "<PERSON>isto täynn<PERSON> työkaluja, jotka on suunniteltu Microsoftin .NET-ekosysteemiä ajatellen.<br>Sisältää: <b>.NET-työkalut</b>", "A restart is required": "Uudelleenkäynnistys vaaditaan", "Abort install if pre-install command fails": "Keske<PERSON><PERSON>, jos es<PERSON> epäonnistuu", "Abort uninstall if pre-uninstall command fails": "Keskeytä asennuksen poisto, jos asennuksen poistoa edeltävä komento epäonnistuu", "Abort update if pre-update command fails": "Keskeytä pä<PERSON>, jos päivitystä edeltävä komento epäonnistuu", "About": "Tiet<PERSON>", "About Qt6": "Tietoja Qt6:sta", "About WingetUI": "Tietoja UniGetUI:sta", "About WingetUI version {0}": "Tietoja UniGetUI versio {0}:sta", "About the dev": "Tietoja kehittäjästä", "Accept": "Hyväksy", "Action when double-clicking packages, hide successful installations": "<PERSON><PERSON><PERSON><PERSON> kun kaksoisnap<PERSON>t pakette<PERSON>, p<PERSON><PERSON><PERSON><PERSON> onnistuneet asennukset", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "Lisää lähde {0}:lle", "Add a timestamp to the backup file names": "Lisää aikaleima varmuuskopioiden nimiin", "Add a timestamp to the backup files": "Lisää aikaleima varmuuskopiotiedostoihin", "Add packages or open an existing bundle": "Lisää paketteja tai avaa olemassa oleva paketti", "Add packages or open an existing package bundle": "Lisää paketteja tai avaa olemassaoleva pakettinippu", "Add packages to bundle": "<PERSON>s<PERSON><PERSON> paketti nippuun", "Add packages to start": "Lisää paketteja aloittaaksesi", "Add selection to bundle": "Lisää valittu pakettiin", "Add source": "Lisää lähde", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Lisää ohitettujen päivitysten luetteloon päivitykset, jotka epäonnistuvat, kun ilmoitus ei lö<PERSON>y", "Adding source {source}": "Lisätään lähde {source}", "Adding source {source} to {manager}": "Lisät<PERSON>ä<PERSON> lähde {source}: {manager}", "Addition succeeded": "<PERSON><PERSON><PERSON><PERSON>", "Administrator privileges": "Järjestelmänvalvojan o<PERSON>t", "Administrator privileges preferences": "Järjestelmänvalvojan o<PERSON>t", "Administrator rights": "Ylläpitäjä<PERSON> o<PERSON>", "Administrator rights and other dangerous settings": "Järjestelmänvalvojan o<PERSON>t ja muut vaaralliset asetukset", "Advanced options": "Lisäasetukset", "All files": "<PERSON><PERSON><PERSON>", "All versions": "<PERSON><PERSON><PERSON> ve<PERSON>", "Allow changing the paths for package manager executables": "<PERSON><PERSON> p<PERSON><PERSON><PERSON> suoritettavien tiedostojen polkujen muuttaminen", "Allow custom command-line arguments": "Salli mukautetut komentoriviargumentit", "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON>n su<PERSON>ttaminen rinn<PERSON>kain", "Allow parallel installs (NOT RECOMMENDED)": "Salli rinnakkaiset asennukset (EI SUOSITELLA)", "Allow pre-release versions": "<PERSON><PERSON>", "Allow {pm} operations to be performed in parallel": "<PERSON><PERSON> to<PERSON> {pm} su<PERSON><PERSON><PERSON>n rinn<PERSON>kain", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Vaihtoehtoisesti voit myös asentaa sovelluksen {0} suorittamalla se<PERSON>avan komennon Windows PowerShell -kehotteessa:", "Always elevate {pm} installations by default": "Nosta aina {pm} as<PERSON><PERSON><PERSON> o<PERSON>oa", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON> aina {pm} operaatiot järjestelmävalvojan <PERSON>.", "An error occurred": "Tapaht<PERSON> virhe", "An error occurred when adding the source: ": "Tapahtui virhe lisätessä lähdettä: ", "An error occurred when attempting to show the package with Id {0}": "<PERSON>irhe yritettäessä näyttää pakettia tunnuksella {0}", "An error occurred when checking for updates: ": "Tapahtui virhe tarkistaessa p<PERSON>ivityksiä:", "An error occurred while loading a backup: ": "Varmuuskopiota ladattaessa tapahtui virhe:", "An error occurred while logging in: ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tap<PERSON>i virhe:", "An error occurred while processing this package": "Tätä pakettia käsiteltäessä tapahtui virhe", "An error occurred:": "Tapaht<PERSON> virhe:", "An interal error occurred. Please view the log for further details.": "Tapahtui sisäinen virhe. Katso l<PERSON>ätietoja lokista.", "An unexpected error occurred:": "Tapahtui odottamaton virhe:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Odottamaton ongelma tapahtui yritettäessä korjata WinGetiä. Yritä myöhem<PERSON> uudelleen", "An update was found!": "Päivitys löytyi!", "Android Subsystem": "Android-alijärjestelmä", "Another source": "<PERSON><PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "<PERSON><PERSON><PERSON> asennuksen tai päivityksen aikana luodut uudet pikakuvakkeet poistetaan automaattisesti sen sijaan, että näkyisi vahvistuskehote, kun ne havaitaan ensimmäisen kerran.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Kaikki UniGetUI:n ulkopuolella luodut tai muokatut pikakuvakkeet ohitetaan. Voit lisätä ne {0}-painikkeella.", "Any unsaved changes will be lost": "<PERSON><PERSON><PERSON> tallentamattomat muutokset menetetään", "App Name": "<PERSON><PERSON><PERSON><PERSON> nimi", "Appearance": "Ulkonäkö", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON><PERSON><PERSON> teema, <PERSON><PERSON><PERSON><PERSON><PERSON>, pakettikuvakkeet, tyhjennä onnistuneet asennukset automaattisesti", "Application theme:": "<PERSON><PERSON><PERSON><PERSON> teema:", "Apply": "Käytä", "Architecture to install:": "<PERSON>en<PERSON>ksen arkkitehtuuri:", "Are these screenshots wron or blurry?": "Ovatko nämä kuvakaappaukset vääriä tai epäselviä?", "Are you really sure you want to enable this feature?": "<PERSON><PERSON><PERSON> var<PERSON>, että haluat ottaa tämän ominaisuuden käyttöön?", "Are you sure you want to create a new package bundle? ": "<PERSON><PERSON><PERSON><PERSON> varmasti luoda uuden paketti<PERSON>pun?", "Are you sure you want to delete all shortcuts?": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>ti poistaa kaikki pika<PERSON>?", "Are you sure?": "<PERSON><PERSON><PERSON>?", "Ascendant": "<PERSON><PERSON><PERSON>", "Ask for administrator privileges once for each batch of operations": "Pyydä järjestelmänvalvojan oikeuksia kerran jokaista toimintosarjaa kohden", "Ask for administrator rights when required": "Pyydä järjestelmävalvojan oikeuksia tarvittaessa", "Ask once or always for administrator rights, elevate installations by default": "Pyydä kerran tai aina järjestelmänvalvojan o<PERSON>, korota asennuks<PERSON> o<PERSON>a", "Ask only once for administrator privileges": "Kysy j<PERSON>stelmänvalvojan oikeuksia vain kerran", "Ask only once for administrator privileges (not recommended)": "Pyydä vain kerran järjestelmänvalvojan <PERSON> (ei suositella)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Pyydä poistamaan asennuksen tai päivityksen aikana luodut työpöydän pikakuvakkeet.", "Attention required": "Huomiota tarvitaan", "Authenticate to the proxy with an user and a password": "Todennus välityspalvelimelle käyttäjällä ja salas<PERSON>lla", "Author": "Tekijä", "Automatic desktop shortcut remover": "Automaattinen työpöydän pikakuvakkeiden poisto", "Automatically save a list of all your installed packages to easily restore them.": "Tallenna automaattisesti luettelo kaikista asennetuista paketeista, jotta voit palauttaa ne helposti.", "Automatically save a list of your installed packages on your computer.": "Tallenna automaattisesti luettelo asennetuista paketeista tietokoneellesi.", "Autostart WingetUI in the notifications area": "Käynnistä UniGetUI  automaattisesti ilmoitusalueella", "Available Updates": "Saatavilla olevat päivitykset", "Available updates: {0}": "Saatavilla olevat päivitykset: {0},", "Available updates: {0}, not finished yet...": "Saatavilla olevat päivitykset: {0}, ei vielä valmis...", "Backing up packages to GitHub Gist...": "Pak<PERSON>en varmuuskopiointi GitHub Gistille...", "Backup": "Varmuuskopioi", "Backup Failed": "Varmuuskopiointi epäonnistui", "Backup Successful": "Varmuuskopiointi onnistui", "Backup and Restore": "Varmuuskopiointi ja palautus", "Backup installed packages": "Varmuuskopioi asennetut paketit", "Backup location": "Varmuuskopiointipaikka", "Become a contributor": "<PERSON><PERSON>", "Become a translator": "Liity kääntäjäksi", "Begin the process to select a cloud backup and review which packages to restore": "Aloita pilvivarmuuskopion valintaprosessi ja tarkista palautettavat paketit", "Beta features and other options that shouldn't be touched": "Beta-o<PERSON><PERSON><PERSON><PERSON>t ja muut v<PERSON><PERSON>, joi<PERSON> ei pidä koskea", "Both": "<PERSON><PERSON><PERSON>", "Bundle security report": "<PERSON><PERSON><PERSON>", "But here are other things you can do to learn about WingetUI even more:": "<PERSON><PERSON> tä<PERSON> on muita asioita, joita voit tehdä saadaksesi lisätietoja UniGetUI :sta:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "<PERSON><PERSON> poistat paketin<PERSON>inn<PERSON> k<PERSON>östä, et voi enää nähdä tai päivittää sen paketteja.", "Cache administrator rights and elevate installers by default": "Tallenna j<PERSON>rjestelmänvalvojan oikeudet välimuistiin ja nosta asentajia o<PERSON>a", "Cache administrator rights, but elevate installers only when required": "<PERSON><PERSON>na j<PERSON>stelmänvalvojan o<PERSON>t v<PERSON><PERSON>uist<PERSON>, mutta lisää asentajia vain tarvittaessa", "Cache was reset successfully!": "<PERSON><PERSON><PERSON>uistin nollaus onnistui!", "Can't {0} {1}": "Ei voi {0} {1}", "Cancel": "Peru", "Cancel all operations": "Peru kaikki to<PERSON>", "Change backup output directory": "<PERSON>uta varmuuskopion tulos<PERSON>", "Change default options": "<PERSON><PERSON>", "Change how UniGetUI checks and installs available updates for your packages": "<PERSON><PERSON>, jolla UniGetUI tarkistaa ja asentaa paketteihisi saatavilla olevat päivitykset", "Change how UniGetUI handles install, update and uninstall operations.": "<PERSON><PERSON>, jolla UniGetUI käsittelee asennus-, pä<PERSON><PERSON>- ja asen<PERSON><PERSON>en poisto<PERSON>.", "Change how UniGetUI installs packages, and checks and installs available updates": "Määrittele miten UniGetUI asentaa paketteja, tarkistaa päivityksiä ja asentaa tarjolla olevat päivitykset ", "Change how operations request administrator rights": "<PERSON><PERSON>, jolla to<PERSON>ot pyytävät järjestelmänvalvojan oike<PERSON>", "Change install location": "<PERSON><PERSON> asen<PERSON> si<PERSON>", "Change this": "<PERSON><PERSON> tä<PERSON>", "Change this and unlock": "<PERSON><PERSON><PERSON>da tämä ja avaa lukitus", "Check for package updates periodically": "Tarkista pakettipäivitykset säännöllisesti", "Check for updates": "Tarkista päivitykset", "Check for updates every:": "Tarkista päivitykset joka:", "Check for updates periodically": "Tarkista päivitykset säännöllisesti", "Check for updates regularly, and ask me what to do when updates are found.": "Tarkista päivitykset säännöllisesti ja kysy, mitä tehdä, kun päivityksiä löyt<PERSON>.", "Check for updates regularly, and automatically install available ones.": "Tarkista päivitykset säännöllisesti ja asenna saatavilla olevat päivitykset automaattisesti.", "Check out my {0} and my {1}!": "<PERSON><PERSON> minun {0} ja {1}!", "Check out some WingetUI overviews": "<PERSON><PERSON> j<PERSON>ain UniGetUI -katsauksia", "Checking for other running instances...": "Tarkistetaan muita käynnissä olevia ilmentymiä...", "Checking for updates...": "Tarkistetaan päivityksiä...", "Checking found instace(s)...": "Tarkistetaan löydettyjä ilmentymiä...", "Choose how many operations shouls be performed in parallel": "Valitse kuinka monta toimintoa pitäisi suorittaa rinnakkain", "Clear cache": "Tyhjennä välimuisti", "Clear finished operations": "Poista valmiit to<PERSON>", "Clear selection": "Tyhjennä valinta", "Clear successful operations": "Poista onnist<PERSON> to<PERSON>", "Clear successful operations from the operation list after a 5 second delay": "Poista onnistuneet toiminnot toimintoluettelosta 5 sekunnin viiveen jälkeen", "Clear the local icon cache": "Puhdista paikallinen ikoni välimuisti", "Clearing Scoop cache - WingetUI": "Scoop-vä<PERSON>ui<PERSON>in t<PERSON>nnys - UniGetUI ", "Clearing Scoop cache...": "Scoop-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Click here for more details": "Napsauta tätä saadaksesi lisätietoja", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "<PERSON><PERSON><PERSON><PERSON> as<PERSON>p<PERSON>. <PERSON><PERSON>, UniGetUI ei välttämättä toimi odotetulla tavalla.", "Close": "Sulje", "Close UniGetUI to the system tray": "Pienennä UniGetUI ilmoitusalueelle", "Close WingetUI to the notification area": "Sulje UniGetUI ilmoitusalueelle", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Pilvivarmuuskopiointi käyttää yksityistä GitHub Gist -tiedostoa asennettujen pakettien luettelon tallentamiseen.", "Cloud package backup": "Pilvipaket<PERSON>", "Command-line Output": "Komentorivilähtö", "Command-line to run:": "Su<PERSON><PERSON><PERSON><PERSON> komentorivi:", "Compare query against": "V<PERSON><PERSON> kys<PERSON>", "Compatible with authentication": "Yhteensopiva autentikoinnin kanssa", "Compatible with proxy": "Yhteensopiva välityspalvelimen kanssa", "Component Information": "<PERSON><PERSON><PERSON><PERSON> tiedot", "Concurrency and execution": "Samanaikaisuus ja toteutus", "Connect the internet using a custom proxy": "Yhdistä Internetiin mukautetun välityspalvelimen avulla", "Continue": "Jatka", "Contribute to the icon and screenshot repository": "Luo si<PERSON>ältöä ikoni ja kuvakaappaus arkistoon", "Contributors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Copy": "Ko<PERSON>i", "Copy to clipboard": "<PERSON><PERSON><PERSON>yd<PERSON>", "Could not add source": "Lähdettä ei voitu lisätä", "Could not add source {source} to {manager}": "Lähdettä {source} ei voitu lisätä hakemistoon {manager}", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "Nippua ei voitu luoda", "Could not load announcements - ": "Ei voitu ladata il<PERSON> -", "Could not load announcements - HTTP status code is $CODE": "Ilmoituksia ei voitu ladata - HTTP-tilakoodi on $CODE", "Could not remove source": "Lähdettä ei voitu poistaa", "Could not remove source {source} from {manager}": "<PERSON><PERSON><PERSON>det<PERSON>ä {source} ei voitu poistaa {manager}:sta", "Could not remove {source} from {manager}": "{source} ei voitu poistaa {manager}:sta", "Credentials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Current Version": "Nykyinen versio", "Current status: Not logged in": "Nykyinen tila: ei kirja<PERSON>ut", "Current user": "Nykyinen käyttäjä", "Custom arguments:": "Mukautetut argumentit:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Mukautetut komentorivi argumentit:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": "Mukautetut päivitysargumentit:", "Customize WingetUI - for hackers and advanced users only": "Mukauta UniGetUI - vain hakkereille ja kokeneille käyttäjille", "DEBUG BUILD": "DEBUG VERSIO", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "VASTUUVAPAUSLAUSEKE: <PERSON>MM<PERSON> OLE VASTUUSSA LADATUISTA PAKETTEISTA. VARMISTA, ETTÄ ASENNAT VAIN LUOTETTAVIA OHJELMISTOJA.", "Dark": "Tumma", "Decline": "Hylkää", "Default": "<PERSON><PERSON>", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Oletusasetukset - sopii tavallisille käyttäjille", "Default vcpkg triplet": "<PERSON><PERSON> vcpkg tripletti", "Delete?": "<PERSON>ist<PERSON><PERSON><PERSON>?", "Dependencies:": "Riippuvaisuudet:", "Descendant": "<PERSON><PERSON><PERSON>", "Description:": "Kuvaus:", "Desktop shortcut created": "Työpöytä pikakuvake luotu", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on työlästä ja tämä sovellus on ilmainen. <PERSON>tta jos pidit sovel<PERSON>, voit aina <b>ostaa minulle kah<PERSON></b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> kohdet<PERSON> \"{discoveryTab}\"-v<PERSON><PERSON><PERSON>hdellä (pakettitietojen näyttämisen sijaan)", "Disable new share API (port 7058)": "Poista uusi jakosovellusliittymä käytöstä (portti 7058)", "Disable the 1-minute timeout for package-related operations": "Poista käytöstä minuutin aikakatkaisu pakettikohtaisissa toimissa", "Disclaimer": "Vastuuvapauslaus<PERSON>e", "Discover Packages": "<PERSON><PERSON><PERSON>", "Discover packages": "Löydä paketteja", "Distinguish between\nuppercase and lowercase": "Erotetaan isot ja pienet kir<PERSON>t", "Distinguish between uppercase and lowercase": "Erotetaan isot ja pienet kir<PERSON>t", "Do NOT check for updates": "ÄLÄ tarkista päivityksiä", "Do an interactive install for the selected packages": "Suorita interaktiivinen asennus valituille pake<PERSON>ille", "Do an interactive uninstall for the selected packages": "Suorita interaktiivinen asennuksen purku valituille paketeille", "Do an interactive update for the selected packages": "Suorita interaktiivinen päivitys valituille paketeille", "Do not automatically install updates when the battery saver is on": "<PERSON><PERSON><PERSON> asenna päivityksiä automaattisesti, kun virransäästö on päällä", "Do not automatically install updates when the network connection is metered": "<PERSON><PERSON><PERSON> asenna pä<PERSON>ksiä automaattisesti, kun verkkoyhteys on mitattu", "Do not download new app translations from GitHub automatically": "Älä lataa uusia käännöksiä Githubista automaattisesti", "Do not ignore updates for this package anymore": "<PERSON>lä ohita tämän paketin päivityksiä enää", "Do not remove successful operations from the list automatically": "<PERSON><PERSON>ä poista onnistuneita toimintoja luettelosta automaattisesti", "Do not show this dialog again for {0}": "Älä näytä tätä valintaikkunaa uudelleen kohteelle {0}", "Do not update package indexes on launch": "Älä päivitä pakettien indeksejä käynnistyksen yhteydessä", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Hyväksytkö että UniGetUI kerää ja lähettää anonyymejä käyt<PERSON>ötilastoja, joiden ainoa tarkoitus on ymmärtää ja parantaa käyttökokemusta?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Onko UniGetUI mielestäsi käyttökelpoinen? Voit halutetessasi tukea työtäni niin voin jatkaa WingetUI:n kehittämistä.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Onko UniGetUI mielestäsi hyödyllinen? Haluatko tukea kehittäjää? <PERSON><PERSON> nä<PERSON> on, voit {0}, se auttaa paljon!", "Do you really want to reset this list? This action cannot be reverted.": "<PERSON><PERSON><PERSON><PERSON> todella nollata tämän luettelon? Tätä toimintoa ei voi peruuttaa.", "Do you really want to uninstall the following {0} packages?": "<PERSON><PERSON><PERSON><PERSON> todella poistaa se<PERSON>t {0} paketit?", "Do you really want to uninstall {0} packages?": "<PERSON><PERSON><PERSON><PERSON> todella poistaa {0} p<PERSON><PERSON>?", "Do you really want to uninstall {0}?": "<PERSON><PERSON><PERSON><PERSON> todella poistaa so<PERSON> {0}?", "Do you want to restart your computer now?": "Haluatko käynnistää tietokoneesi uudelleen nyt?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Haluatko kääntää UniGetUI:n kielellesi? <PERSON><PERSON>, miten voit osallistua <a style=\"color:{0}\" href=\"{1}\"a>TÄSTÄ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Etk<PERSON> halua la<PERSON>? <PERSON><PERSON><PERSON> huo<PERSON>, voit aina jakaa UniGetUI:n ystäviesi kanssa. Levitä sanaa UniGetUI:sta.", "Donate": "<PERSON><PERSON><PERSON><PERSON>", "Done!": "Valmis!", "Download failed": "<PERSON><PERSON><PERSON> e<PERSON>", "Download installer": "<PERSON><PERSON><PERSON>", "Download operations are not affected by this setting": null, "Download selected installers": "Lataa valitut as<PERSON>", "Download succeeded": "<PERSON><PERSON><PERSON>", "Download updated language files from GitHub automatically": "Lataa päivitetyt käännökset Githubista automaattisesti", "Downloading": "<PERSON><PERSON><PERSON>", "Downloading backup...": "<PERSON><PERSON><PERSON> varmu<PERSON>...", "Downloading installer for {package}": "<PERSON><PERSON><PERSON> asen<PERSON><PERSON><PERSON><PERSON><PERSON> paketille {package}", "Downloading package metadata...": "Ladataan paketin metatietoja...", "Enable Scoop cleanup on launch": "<PERSON><PERSON>-pu<PERSON><PERSON><PERSON> k<PERSON>yttöön käynnistyksen yhteydessä", "Enable WingetUI notifications": "Ota UniGetUI-ilmoitukset käyttöön", "Enable an [experimental] improved WinGet troubleshooter": "<PERSON><PERSON> [k<PERSON><PERSON><PERSON>] parannettu UniGetUI-vianmääritys", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "<PERSON><PERSON> k<PERSON>öön taustaprosessorin käytön optimoinnit (katso Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "<PERSON><PERSON> taust<PERSON>ovellusliittymä kä<PERSON>töön (UniGetUI-widgetit ja jakaminen, portti 7058)", "Enable it to install packages from {pm}.": "<PERSON><PERSON> se k<PERSON>, jos haluat asentaa paketteja kohteesta {pm}.", "Enable the automatic WinGet troubleshooter": "<PERSON><PERSON> k<PERSON>öön automaattinen WinGet-vianmääritys", "Enable the new UniGetUI-Branded UAC Elevator": "O<PERSON>öön uusi UniGetUI-brändätty UAC Elevator", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "<PERSON><PERSON> {pm}", "Enter proxy URL here": "Kirjoita välityspalvelimen URL-osoite tähän", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "<PERSON><PERSON><PERSON>", "Everything is up to date": "<PERSON><PERSON><PERSON> on ajantasalla", "Exact match": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Työpöydälläsi olevat pikakuvakkeet tarkistetaan, ja sinun on valittava, mitkä niistä haluat säilyttää ja mitkä poistaa.", "Expand version": "<PERSON><PERSON><PERSON><PERSON> versio", "Experimental settings and developer options": "Kokeelliset asetukset ja kehittäjävaihtoehdot", "Export": "Vienti", "Export log as a file": "Vie loki tied<PERSON>ona", "Export packages": "<PERSON><PERSON> p<PERSON>", "Export selected packages to a file": "<PERSON>ie valitut paketit tied<PERSON>oon", "Export settings to a local file": "<PERSON><PERSON> p<PERSON>", "Export to a file": "<PERSON><PERSON>", "Failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Fetching available backups...": null, "Fetching latest announcements, please wait...": "Haetaan uusimpia il<PERSON>, odota...", "Filters": "<PERSON><PERSON><PERSON><PERSON>", "Finish": "Lopeta", "Follow system color scheme": "Seuraa järjestelmän värimaailmaa", "Follow the default options when installing, upgrading or uninstalling this package": "<PERSON><PERSON>ta o<PERSON>setuksia tämän pake<PERSON> as<PERSON>, pä<PERSON><PERSON>en tai poiston yhteydessä", "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Force ARM:n käännetty winget-versio (VAIN ARM64-JÄRJESTELMIÄ)", "Formerly known as WingetUI": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>mmin ni<PERSON>ä WinGetUI", "Found": "<PERSON><PERSON><PERSON><PERSON>", "Found packages: ": "Löytyi paketteja:", "Found packages: {0}": "Löytyi paketteja: {0}", "Found packages: {0}, not finished yet...": "Lö<PERSON>yi paketteja: {0}, ei vielä valmis...", "General preferences": "<PERSON><PERSON><PERSON><PERSON>", "GitHub profile": "<PERSON><PERSON><PERSON> profiili", "Global": "<PERSON><PERSON><PERSON>", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "<PERSON><PERSON> k<PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>, mutta hyödyllisiä apuohjelmia ja muita mielenkiintoisia paketteja.<br>Sisältää: <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> oh<PERSON> (vaatii l<PERSON>)</b>", "Great! You are on the latest version.": "Hienoa! Käytössä tuorein versio.", "Grid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "<PERSON><PERSON><PERSON> ja dokumentaatio", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Täällä voit muuttaa UniGetUI:n käyttäytymistä seuraavien pikanäppäinten suhteen. Pikakuvakkeen tarkistaminen saa UniGetUI:n poistamaan sen, jos se luodaan tulevassa päivityksessä. <PERSON><PERSON> pois<PERSON>, pika<PERSON><PERSON><PERSON> pysyy en<PERSON>", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, ni<PERSON><PERSON> on Martí ja olen UniGetUI:n <i>kehittäjä</i>. UniGetUI on tehty kokonaan vapaa-ajallani!", "Hide details": "<PERSON><PERSON><PERSON>", "Homepage": "Ko<PERSON>iv<PERSON>", "Hooray! No updates were found.": "Hurraa! Päivityksiä ei löyt<PERSON>yt.", "How should installations that require administrator privileges be treated?": "<PERSON>ten jär<PERSON>stelmänvalvojan oikeuksia vaativia asennuksia tulee käsitellä?", "How to add packages to a bundle": "Kuinka lisätä paketteja nippuun", "I understand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Icons": "Ikonit", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Jätä tulevaisuuden päivitykset tälle paketille huomioimatta", "Ignore packages from {pm} when showing a notification about updates": "<PERSON><PERSON> paketit alkaen {pm}, kun näet ilmoituksen päivityksistä", "Ignore selected packages": "Jätä valitut paketit huomio<PERSON>tta", "Ignore special characters": "Jätä erikoismerkit huomioimatta", "Ignore updates for the selected packages": "<PERSON><PERSON> valittujen pakettien päivityks<PERSON>", "Ignore updates for this package": "Jät<PERSON> tämän paketin päivitykset huomio<PERSON>tta", "Ignored updates": "Ohitetut päivitykset", "Ignored version": "Ohitetut versiot", "Import": "<PERSON><PERSON><PERSON>", "Import packages": "<PERSON><PERSON>", "Import packages from a file": "<PERSON><PERSON> p<PERSON>", "Import settings from a local file": "<PERSON><PERSON> p<PERSON>esta tiedostosta", "In order to add packages to a bundle, you will need to: ": "<PERSON><PERSON> voit lisätä paketteja nippuun, sinun pitää:", "Initializing WingetUI...": "Alustetaan UniGetUI...", "Install": "<PERSON><PERSON><PERSON>", "Install Scoop": "<PERSON><PERSON><PERSON>", "Install and more": "Asennusvaihtoehdot", "Install and update preferences": "<PERSON><PERSON><PERSON>ksen ja pä<PERSON>ksen asetukset", "Install as administrator": "<PERSON><PERSON><PERSON>lmä<PERSON>valvo<PERSON>", "Install available updates automatically": "Asenna saatavilla olevat päivitykset automaattisesti", "Install location can't be changed for {0} packages": null, "Install location:": "<PERSON><PERSON><PERSON> si<PERSON>:", "Install options": "<PERSON><PERSON><PERSON> v<PERSON>", "Install packages from a file": "<PERSON><PERSON><PERSON> p<PERSON>", "Install prerelease versions of UniGetUI": "Asenna UniGetUI:n esiversioita", "Install selected packages": "<PERSON><PERSON><PERSON> valitut paketit", "Install selected packages with administrator privileges": "<PERSON><PERSON><PERSON> valitut paketit järjestelmänvalvo<PERSON>", "Install selection": "<PERSON><PERSON><PERSON> v<PERSON>u", "Install the latest prerelease version": "<PERSON><PERSON><PERSON><PERSON>", "Install updates automatically": "Asenna päivitykset automaattisesti", "Install {0}": "<PERSON><PERSON><PERSON> {0}", "Installation canceled by the user!": "Käyttäjä peruutti asennuksen!", "Installation failed": "<PERSON><PERSON><PERSON> e<PERSON>", "Installation options": "Asennusvaihtoehdot", "Installation scope:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "Installation succeeded": "<PERSON><PERSON><PERSON>", "Installed Packages": "Asennetut Paketit", "Installed Version": "<PERSON><PERSON><PERSON><PERSON>", "Installed packages": "<PERSON><PERSON><PERSON><PERSON> paketit", "Installer SHA256": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SHA256", "Installer SHA512": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SHA512", "Installer Type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Installer URL": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> URL", "Installer not available": "<PERSON><PERSON><PERSON><PERSON><PERSON>lmaa ei saatavilla", "Instance {0} responded, quitting...": "E<PERSON>int<PERSON><PERSON> {0} <PERSON><PERSON>, lopeteta<PERSON>...", "Instant search": "<PERSON><PERSON><PERSON><PERSON><PERSON> haku", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Eheystarkastukset ohitettiin", "Integrity checks will not be performed during this operation": "Eheystarkastuksia ei suoriteta tämän toim<PERSON>on aikana", "Interactive installation": "Interaktiivinen asennus", "Interactive operation": "Interaktiivinen toiminta", "Interactive uninstall": "Interaktiivinen asennuksen purku", "Interactive update": "Interaktiivinen päivitys", "Internet connection settings": "Internet-yhteyden asetukset", "Is this package missing the icon?": "<PERSON><PERSON><PERSON><PERSON><PERSON>o tältä paketilta ikoni?", "Is your language missing or incomplete?": "<PERSON><PERSON><PERSON><PERSON><PERSON>o k<PERSON>ks<PERSON> vai onko se puutteellinen?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "<PERSON><PERSON> <PERSON>le <PERSON>, että toimitetut tunnistetiedot säilytetään turvallisesti, joten et voi yhtä hyvin olla käyttämättä pankkitilisi tunnuksia", "It is recommended to restart UniGetUI after WinGet has been repaired": "On suositeltavaa käynnistää UniGetUI uudelleen WinGetin korjauksen jälkeen", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "WinGet ei näyttäisi toimivan oikein Haluatko yrittää Wingetin korjausta?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Näyttää siltä, ​​että käytit UniGetUI:ta järjestelmänvalvojana, mikä ei ole suositeltavaa. Voit edelleen käyttää ohjelma<PERSON>, mutta suosittelemme, että et käytä UniGetUI:ta järjestelmänvalvojan oikeuksilla. Napsauta \"{showDetails}\" nähdäksesi syyn.", "Language": "<PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON>, teema ja muista sekalaisia aset<PERSON>", "Last updated:": "Viimeisin päivitys:", "Latest": "<PERSON><PERSON><PERSON><PERSON>", "Latest Version": "<PERSON><PERSON><PERSON><PERSON>", "Latest Version:": "<PERSON><PERSON><PERSON><PERSON> versio:", "Latest details...": "Viimeisimmät tiedot...", "Launching subprocess...": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>...", "Leave empty for default": "Jätä tyhjäksi oletuksena", "License": "<PERSON><PERSON><PERSON>", "Licenses": "Lisenssit", "Light": "Vaalea", "List": "Lista", "Live command-line output": "Live-kome<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Live output": "Live-tulostus", "Loading UI components...": "Ladataan UI komponentteja...", "Loading WingetUI...": "Käynnistetään UniGetUI...", "Loading packages": "<PERSON><PERSON><PERSON> paketteja", "Loading packages, please wait...": "<PERSON><PERSON><PERSON> p<PERSON>, odota hetki...", "Loading...": "Ladataan...", "Local": "<PERSON><PERSON><PERSON><PERSON>", "Local PC": "<PERSON><PERSON><PERSON><PERSON> PC", "Local backup advanced options": "Paikallisen varmuuskopion laajemmat asetukset", "Local machine": "<PERSON><PERSON><PERSON><PERSON> kone", "Local package backup": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Locating {pm}...": "<PERSON><PERSON><PERSON> {pm}...", "Log in": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Log in failed: ": "Kirjautuminen epäonnistui:", "Log in to enable cloud backup": null, "Log in with GitHub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Log in with GitHub to enable cloud package backup.": null, "Log level:": "<PERSON><PERSON> taso:", "Log out": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Log out failed: ": "Uloskirjautuminen epäonnistui:", "Log out from GitHub": "Ulos<PERSON>r<PERSON><PERSON><PERSON>", "Looking for packages...": "Etsitään paketteja...", "Machine | Global": "Kone | Yleinen", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "Hallitse", "Manage UniGetUI settings": "Hallinnoi UniGetUI-asetuksia", "Manage WingetUI autostart behaviour from the Settings app": "Hallitse UniGetUI:n automaattista käynnistystä Asetukset-sovelluksesta", "Manage ignored packages": "Hallitse huomiotta j<PERSON>ettyjä paketteja", "Manage ignored updates": "Hallinnoi ohitettuja päivityksiä", "Manage shortcuts": "Hallitse pikanäppäimiä", "Manage telemetry settings": "Hallitse telemetria asetuksia", "Manage {0} sources": "Hallinnoi {0} lähteitä", "Manifest": "Ilmentymä", "Manifests": "Ilmentymät", "Manual scan": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoftin virallinen paketinhallinta. Täynnä tunnettuja ja vahvistettuja paketteja<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON><PERSON>, Microsoft Store -sovellukset</b>", "Missing dependency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> puuttuu", "More": "Lisää", "More details": "Lisää yksityiskohtia", "More details about the shared data and how it will be processed": "Lisätietoja jaetuista tiedoista ja niiden käsittelystä", "More info": "Lisää tietoa", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "HUOMAUTUS: <PERSON><PERSON><PERSON>ä vianmääritys voidaan poistaa käytöstä UniGetUI-asetuksista WinGet-osiossa", "Name": "<PERSON><PERSON>", "New": "<PERSON>us<PERSON>", "New Version": "<PERSON><PERSON><PERSON> V<PERSON>", "New bundle": "<PERSON>usi nippu", "New version": "<PERSON><PERSON><PERSON> versio", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "<PERSON>i", "No applicable installer was found for the package {0}": "Paketille {0} ei löyt<PERSON>yt sopivaa asen<PERSON>a", "No dependencies specified": "Riippuvaisuuksia ei määritelty", "No new shortcuts were found during the scan.": "Tarkistuksen aikana ei löytynyt uusia pikakuvakkeita.", "No packages found": "Paketteja ei löytynyt", "No packages found matching the input criteria": "Syöttöehtoja vastaavia paketteja ei löytynyt", "No packages have been added yet": "Ei vielä lisättyjä paketteja", "No packages selected": "<PERSON>i valittuja paketteja", "No packages were found": "Paketteja ei löytynyt", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Henkilökohtaisia ​​tietoja ei kerätä eikä lähetetä, ja kerätyt tiedot anonymisoidaan, joten niitä ei voida palauttaa sinulle.", "No results were found matching the input criteria": "Syöttöehtoja vastaavia tuloksia ei löytynyt", "No sources found": "Lähteitä ei löytynyt", "No sources were found": "Lähteitä ei löytynyt", "No updates are available": "Päivityksiä ei saatavilla", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS:n paketinhallinta. Täynnä kirjastoja ja muita apuohjelmia, jotka kiertävät JavaScript-maailmaa.<br>Sisältää: <b>Node JS ja muut niihin liittyvät apuohjelmat</b>", "Not available": "Ei sa<PERSON>villa", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "<PERSON><PERSON>", "Not right now": "Ei juuri nyt", "Notes:": "Huomautuksia:", "Notification preferences": "Ilmoitusasetukset", "Notification tray options": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Notification types": "Ilmoitustyypit", "NuPkg (zipped manifest)": "NuPkg (pakattu luettelo)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON>", "Open UniGetUI": "Avaa UniGetUI", "Open UniGetUI security settings": "Avaa UniGetUI turvallisuus asetukset", "Open WingetUI": "Avaa UniGetUI", "Open backup location": "<PERSON><PERSON> var<PERSON> si<PERSON>i", "Open existing bundle": "<PERSON><PERSON> nippu", "Open install location": "<PERSON><PERSON>", "Open the welcome wizard": "<PERSON><PERSON> ohjattu terve<PERSON>lotoi<PERSON>to", "Operation canceled by user": "<PERSON><PERSON><PERSON><PERSON> keskey<PERSON>tty käyttäjä<PERSON> to<PERSON>", "Operation cancelled": "<PERSON><PERSON><PERSON><PERSON>", "Operation history": "Toimintojen historia", "Operation in progress": "<PERSON><PERSON><PERSON><PERSON>", "Operation on queue (position {0})...": "<PERSON><PERSON><PERSON><PERSON> (järjestysnumero {0})...", "Operation profile:": null, "Options saved": "Vaihtoe<PERSON><PERSON><PERSON>u", "Order by:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mukaan:", "Other": "<PERSON><PERSON>", "Other settings": "<PERSON><PERSON>", "Package": "<PERSON><PERSON>", "Package Bundles": "Pakettiniput", "Package ID": "Paketin ID", "Package Manager": "Paketinhallinta", "Package Manager logs": "Paketinhallinta logit", "Package Managers": "Paketinhallinnat", "Package Name": "<PERSON><PERSON><PERSON> nimi", "Package backup": "<PERSON><PERSON><PERSON>", "Package backup settings": null, "Package bundle": "Pakettinippu", "Package details": "Paketin yksityiskohdat", "Package lists": "Pakettilista", "Package management made easy": "<PERSON><PERSON><PERSON> hallinta tehty <PERSON>", "Package manager": "<PERSON><PERSON><PERSON> hallinta", "Package manager preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Package managers": "Paketinhallinnat", "Package not found": "Pakettia ei lö<PERSON>yt", "Package operation preferences": "<PERSON><PERSON><PERSON> to<PERSON>", "Package update preferences": "Paketin pä<PERSON>ksen asetuk<PERSON>", "Package {name} from {manager}": "<PERSON><PERSON> {name} {manager}:sta", "Package's default": null, "Packages": "Paketit", "Packages found: {0}": "Löytyneet paketit: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON>", "Password": "<PERSON><PERSON><PERSON>", "Paste a valid URL to the database": "Liitä kelvollinen URL-osoite tietokantaan", "Pause updates for": "Keskeytä päivitykset", "Perform a backup now": "<PERSON><PERSON><PERSON> var<PERSON> nyt", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "Suoritetaan var<PERSON>, odota het<PERSON>...", "Periodically perform a backup of the installed packages": "<PERSON><PERSON><PERSON> ajo<PERSON><PERSON> varmuusk<PERSON>io asennetuista paketeista", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "Tarkista tämän paketin asen<PERSON>eh<PERSON>t ja yritä uudelleen", "Please click on \"Continue\" to continue": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" j<PERSON><PERSON><PERSON><PERSON>", "Please enter at least 3 characters": "Syötä vähintään 3 kirjainta", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON>, että tietyt paketit eivät ehkä ole asennetta<PERSON>, koska paketinhallintaohjelmat ovat käytössä tällä koneella.", "Please note that not all package managers may fully support this feature": "<PERSON><PERSON><PERSON>, että kaikki pake<PERSON>aohjelmat eivät välttämättä tue tätä ominaisuutta täysin", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "<PERSON><PERSON><PERSON>, että tietyistä lähteistä peräisin olevia paketteja ei välttämättä voi viedä. Ne on harmaana, eikä niitä voi viedä vientiin.", "Please run UniGetUI as a regular user and try again.": "Suorita UniGetUI tavallisena käyttäjänä ja yritä uudelleen.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Katso komentorivilähdöstä tai käyttöhistoriasta saadaksesi lisätietoja ongelmasta.", "Please select how you want to configure WingetUI": "Valitse kuinka haluat määrittää UniGetUI:n", "Please try again later": "<PERSON><PERSON><PERSON>", "Please type at least two characters": "Syötä vähintään kaksi kirjainta", "Please wait": "Ole hyvä ja odota", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "<PERSON><PERSON><PERSON>, kun {0} as<PERSON><PERSON><PERSON>. Näkyviin saattaa tulla musta ikkuna. O<PERSON>ta, kunnes se sulkeu<PERSON>u.", "Please wait...": "Ole hyvä ja odota...", "Portable": "<PERSON><PERSON><PERSON><PERSON>", "Portable mode": "Itsenäinen tila", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": "Päivityksen jälkeinen komento:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShellin paketinhallinta. Etsi kirjastoja ja komentosarjoja laajentaaksesi PowerShell-ominaisuuksia.<br>Sisältää: <b><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": "Päivitystä edeltävä komento:", "PreRelease": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, odota hetki...", "Proceed at your own risk.": "<PERSON><PERSON><PERSON> o<PERSON>.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "Välityspalvelimen URL-osoite", "Proxy compatibility table": "Välityspalvelimen yhteensopivuustaulukko", "Proxy settings": "Välityspalvelimen asetukset", "Proxy settings, etc.": "Välityspalvelimen asetukset jne", "Publication date:": "Julkaisupäivä:", "Publisher": "Julkaisija", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "<PERSON>in kirjaston johtaja. Täynnä python-kirjastoja ja muita python-apuohjelm<PERSON><br>Sisältää:<b>Python-kirjastot ja niihin liittyvät apuohjelmat</b>", "Quit": "<PERSON><PERSON><PERSON>", "Quit WingetUI": "Lopeta UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "<PERSON><PERSON><PERSON><PERSON> as<PERSON>na", "Reinstall package": "<PERSON><PERSON><PERSON>", "Related settings": "Aiheeseen liittyvät asetukset", "Release notes": "Julkaisutiedot", "Release notes URL": "Julkaisutiedot URL", "Release notes URL:": "Julkaisutiedot URL:", "Release notes:": "Julkaisutiedot:", "Reload": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>a", "Reload log": "Lataa loki u<PERSON>en", "Removal failed": "<PERSON><PERSON><PERSON>", "Removal succeeded": "<PERSON><PERSON><PERSON>", "Remove from list": "Poista listalta", "Remove permanent data": "Poista pysyvää tietoa", "Remove selection from bundle": "Poista valinta nipusta", "Remove successful installs/uninstalls/updates from the installation list": "Poista onnistuneet asennukset/poistot/päivitykset asennusluettelosta", "Removing source {source}": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>de {source}", "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON><PERSON> {source} {manager}:sta", "Repair UniGetUI": "Ko<PERSON>jaa UniGetUI", "Repair WinGet": "<PERSON><PERSON><PERSON><PERSON>", "Report an issue or submit a feature request": "<PERSON><PERSON><PERSON> on<PERSON>ta tai lähetä ominaisuuspyyntö", "Repository": "Arkisto", "Reset": "<PERSON><PERSON><PERSON>", "Reset Scoop's global app cache": "<PERSON><PERSON><PERSON> glo<PERSON> so<PERSON>", "Reset UniGetUI": "Nollaa UniGetUI", "Reset WinGet": "Nollaa UniGetUI", "Reset Winget sources (might help if no packages are listed)": "<PERSON><PERSON><PERSON>l<PERSON><PERSON><PERSON><PERSON> (voi auttaa, jos paketteja ei ole l<PERSON>)", "Reset WingetUI": "Nollaa UniGetUI", "Reset WingetUI and its preferences": "Nollaa UniGetUI ja sen asetukset", "Reset WingetUI icon and screenshot cache": "Palauta UniGetUI-kuvake ja kuvakaappauksen välimuisti", "Reset list": "Nollaa lista", "Resetting Winget sources - WingetUI": "WinGet-lähteiden nollaaminen - UniGetUI", "Restart": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Restart UniGetUI": "Uudelleenkäynnistä UniGetUI", "Restart WingetUI": "Uudelleen käynnistä UniGetUI", "Restart WingetUI to fully apply changes": "Ota muutokset käyttöön kokonaan käynnistämällä UniGetUI uudelleen", "Restart later": "Uudelleenk<PERSON><PERSON><PERSON><PERSON>", "Restart now": "Uudelleenkäynistä nyt", "Restart required": "Uudelleenkäynnistys vaaditaan", "Restart your PC to finish installation": "Viimeistele asennus k<PERSON>ynnistämällä tietokoneesi uudelleen", "Restart your computer to finish the installation": "Viimeistele asennus k<PERSON>ynnistämällä tietokoneesi uudelleen", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "<PERSON><PERSON><PERSON><PERSON>", "Retry as administrator": "Yritä uudelleen järjestelmänvalvojana", "Retry failed operations": "<PERSON><PERSON><PERSON> ep<PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>", "Retry interactively": "Yritä uudelleen interaktiivisesti", "Retry skipping integrity checks": "Yritä uudelleen ohittaa eheystarkistukset", "Retrying, please wait...": "Yritetä<PERSON><PERSON>, odota...", "Return to top": "<PERSON><PERSON><PERSON>", "Run": "<PERSON><PERSON><PERSON>", "Run as admin": "Suorita ylläpitäjänä", "Run cleanup and clear cache": "<PERSON><PERSON><PERSON> puhdistus ja tyhjennä välimuisti", "Run last": "Suorita viimeinen", "Run next": "<PERSON><PERSON><PERSON>", "Run now": "<PERSON><PERSON><PERSON> nyt", "Running the installer...": "<PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>...", "Running the uninstaller...": "<PERSON><PERSON><PERSON><PERSON><PERSON> poisto-<PERSON><PERSON><PERSON>...", "Running the updater...": "Pä<PERSON>s käynnissä...", "Save": "<PERSON><PERSON><PERSON>", "Save File": "<PERSON><PERSON><PERSON>", "Save and close": "<PERSON><PERSON>na ja sulje", "Save as": "<PERSON><PERSON><PERSON>", "Save bundle as": "<PERSON><PERSON><PERSON> pake<PERSON>", "Save now": "<PERSON><PERSON><PERSON> nyt", "Saving packages, please wait...": "<PERSON>enne<PERSON><PERSON> p<PERSON>, odota hetki...", "Scoop Installer - WingetUI": "<PERSON><PERSON> - UniGetUI", "Scoop Uninstaller - WingetUI": "<PERSON><PERSON> asennuksen poisto - UniGetUI", "Scoop package": "<PERSON><PERSON> paketti", "Search": "Hae", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Hae työpö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kerro kun päivityksiä on saatavilla, älä tee mitään ylimääräistä. En halua UniGetUI:n monimutkaistavan liikaa, haluan vain yksinkertaisen <b>oh<PERSON><PERSON><PERSON><PERSON> hake<PERSON></b>", "Search for packages": "<PERSON><PERSON> paketteja", "Search for packages to start": "<PERSON><PERSON> pakette<PERSON> al<PERSON>", "Search mode": "<PERSON><PERSON><PERSON><PERSON>", "Search on available updates": "Hae saatavilla olevista päivityksistä", "Search on your software": "<PERSON><PERSON> ohjelmistostasi", "Searching for installed packages...": "<PERSON><PERSON><PERSON> as<PERSON> paketteja...", "Searching for packages...": "<PERSON><PERSON><PERSON> paketteja...", "Searching for updates...": "Tarkistetaan päivityksiä...", "Select": "Valitse", "Select \"{item}\" to add your custom bucket": "Lisää mukautettu ryhmä valitsemalla {item}", "Select a folder": "<PERSON><PERSON><PERSON> hake<PERSON>o", "Select all": "<PERSON><PERSON><PERSON> kaikki", "Select all packages": "Valitse kaikki pake<PERSON>t", "Select backup": "Valitse varmuuskopio", "Select only <b>if you know what you are doing</b>.": "Valitse vain <b>jos <PERSON>ät mitä olet tekemässä</b>.", "Select package file": "<PERSON><PERSON><PERSON> p<PERSON>", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "<PERSON><PERSON><PERSON> lähde, jonka haluat lisät<PERSON>:", "Select upgradable packages by default": "Valitse oletuksena päivitettävät paketit", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Valitse käytettävät <b>paketinhallinnat</b> ({0}), määritä kuinka paketit as<PERSON>, hallinnoidaan järjestelmänvalvojan oikeuksien käsittelyä jne.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Lähetetty kättely. Odotetaan esimerkiksi vastapuolen vastausta... ({0}%)", "Set a custom backup file name": "<PERSON><PERSON> mukautettu varmuuskopiotiedoston nimi", "Set custom backup file name": "<PERSON><PERSON> mukautettu varmuuskopiotiedoston nimi", "Settings": "Asetukset", "Share": "Jaa", "Share WingetUI": "Jaa UniGetUI", "Share anonymous usage data": "Jaa anonyymiä k<PERSON>yttötietoa", "Share this package": "Jaa tämä paketti", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Näytä UniGetUI ilmoituspalkissa", "Show UniGetUI's version and build number on the titlebar.": "Näytä UniGetUI:n versio ja koontiversion numero otsikkorivillä.", "Show WingetUI": "Näytä UniGetUI", "Show a notification when an installation fails": "<PERSON><PERSON><PERSON><PERSON>, kun asennus epäonnistuu", "Show a notification when an installation finishes successfully": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>, kun asennus on valmis", "Show a notification when an operation fails": "<PERSON><PERSON><PERSON><PERSON>, kun toiminto epäon<PERSON>u", "Show a notification when an operation finishes successfully": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>, kun toiminto pä<PERSON>tyy onnistuneesti", "Show a notification when there are available updates": "<PERSON><PERSON><PERSON><PERSON> il<PERSON>, kun päivityksi<PERSON> on saatavilla", "Show a silent notification when an operation is running": "Näytä äänetön ilmoitus, kun toiminto on käynnissä", "Show details": "<PERSON><PERSON><PERSON><PERSON>", "Show in explorer": "Näytä explorerissa", "Show info about the package on the Updates tab": "Näytä paketin tiedot Päivitykset-välilehdessä", "Show missing translation strings": "Näytä puuttuvat käännökset", "Show notifications on different events": "Näytä ilmoitukset eri tap<PERSON>", "Show package details": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON> tiedot", "Show package icons on package lists": "Näytä paketin ikonit pakettilistassa", "Show similar packages": "Näytä samankaltaiset paketit", "Show the live output": "Näytä Live-tulostus", "Size": "<PERSON><PERSON>", "Skip": "<PERSON><PERSON>", "Skip hash check": "<PERSON><PERSON> hash-tark<PERSON><PERSON>", "Skip hash checks": "<PERSON><PERSON> has<PERSON>-tark<PERSON><PERSON>", "Skip integrity checks": "<PERSON><PERSON>kastuks<PERSON>", "Skip minor updates for this package": "<PERSON><PERSON> tämän paketin pienet muutokset", "Skip the hash check when installing the selected packages": "<PERSON><PERSON> hash-<PERSON><PERSON><PERSON><PERSON>, kun asennat valittuja paketteja", "Skip the hash check when updating the selected packages": "<PERSON><PERSON> hash-tark<PERSON><PERSON>, kun päivität valittuja paketteja", "Skip this version": "<PERSON><PERSON> tämä versio", "Software Updates": "Ohjelmistopäivitykset", "Something went wrong": "<PERSON><PERSON> meni pieleen", "Something went wrong while launching the updater.": "Jo<PERSON> meni pieleen p<PERSON> k<PERSON>ynnistettäessä.", "Source": "Lä<PERSON><PERSON>", "Source URL:": "Lähde URL:", "Source added successfully": "Lähde lisätty onnistuneesti", "Source addition failed": "<PERSON>ä<PERSON><PERSON> l<PERSON> ep<PERSON>", "Source name:": "Lähteen nimi:", "Source removal failed": "<PERSON><PERSON><PERSON><PERSON> poisto ep<PERSON>", "Source removed successfully": "<PERSON><PERSON><PERSON><PERSON> poistettu onnistuneesti", "Source:": "Lähde:", "Sources": "Lä<PERSON><PERSON><PERSON>", "Start": "Aloita", "Starting daemons...": "Käynnistetään prosesseja...", "Starting operation...": "Aloitetaan toiminto...", "Startup options": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Status": "Tila", "Stuck here? Skip initialization": "<PERSON><PERSON><PERSON>? <PERSON><PERSON> alustus", "Suport the developer": "<PERSON><PERSON> kehitt<PERSON>", "Support me": "<PERSON><PERSON> minua", "Support the developer": "<PERSON><PERSON> kehitt<PERSON>", "Systems are now ready to go!": "Järjestelmät ovat nyt valmis!", "Telemetry": "Telemetria", "Text": "<PERSON><PERSON><PERSON>", "Text file": "Tekstitiedosto", "Thank you ❤": "Kiitos ❤", "Thank you 😉": "Kiitos 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust-pake<PERSON><PERSON><PERSON>a.<br>Sisältää: <b>Rust-kirjastot ja Rust-kielellä kirjoitetut oh<PERSON>lmat</b>", "The backup will NOT include any binary file nor any program's saved data.": "Varmuuskopio EI sisällä binääritiedostoja eikä minkään oh<PERSON>lman tallennettuja tietoja.", "The backup will be performed after login.": "Varmuuskopiointi suoritetaan kirjaut<PERSON> jälk<PERSON>.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Varmuuskopio sisältää täyd<PERSON>sen luettelon asennetuista paketeista ja niiden asennusvaihtoehdoista. Myös ohitetut päivitykset ja ohitetut versiot tallenne<PERSON>.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON><PERSON>, jota yrität ladata, näyttä<PERSON> olevan vir<PERSON>. Tarkista tiedosto ja yritä uudelleen.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarkistussumma ei ole sama kuin odotettu arvo, e<PERSON><PERSON> ohjelman aitoutta voida varmistaa. <PERSON><PERSON> l<PERSON>, {0} paketti ohittaa uudelleen hash-tark<PERSON><PERSON>sen.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Klassinen pakettien hallinta <PERSON>ille. Sieltä löydät kaiken. <br>Sisältää: <b><PERSON><PERSON><PERSON><PERSON></b>\n", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Nykyinen nippu ei sisällä paketteja. Aloita lisäämällä paketteja", "The executable file for {0} was not found": "Suoritettavaa tied<PERSON> k<PERSON> {0} ei l<PERSON>yt", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "<PERSON><PERSON><PERSON><PERSON> asetukset otetaan käyttöön oletusarvoisesti aina, kun {0}-p<PERSON><PERSON> as<PERSON>, päivitetään tai poisteta<PERSON>.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Se<PERSON>avat paketit viedään JSON-tiedostoon. Käyttäjätietoja tai binaaritiedostoja ei tallenneta.", "The following packages are going to be installed on your system.": "<PERSON><PERSON><PERSON><PERSON> paketit asennetaan järjestelmääsi.", "The following settings may pose a security risk, hence they are disabled by default.": "<PERSON><PERSON><PERSON><PERSON> asetukset voivat aiheuttaa tietoturvariskin, joten ne ovat oletusarvoisesti poissa k<PERSON>ytöstä.", "The following settings will be applied each time this package is installed, updated or removed.": "<PERSON><PERSON><PERSON><PERSON> asetukset otetaan käyt<PERSON>öön aina, kun tämä pake<PERSON> as<PERSON>, päivitetään tai poisteta<PERSON>.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "<PERSON><PERSON><PERSON><PERSON> asetukset otetaan käyt<PERSON>öön aina, kun tämä paketti asenne<PERSON>, päivitetään tai poistetaan. Ne tallennetaan automaattisesti.", "The icons and screenshots are maintained by users like you!": "Kaltaisesi käyttäjät ylläpitävät kuvakkeita ja kuvakaappauk<PERSON>!", "The installer authenticity could not be verified.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aitoutta ei voitu varmistaa.", "The installer has an invalid checksum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on virheellinen tarkistussumma", "The installer hash does not match the expected value.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hash ei vastaa odotettua arvoa.", "The local icon cache currently takes {0} MB": "Paikallinen ikoni välimuisti vie tilaa {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Tämän projektin päätavoitteena on luoda intuitiivinen käyttöliittymä yleisimpien Windowsin CLI-pake<PERSON><PERSON> hallint<PERSON><PERSON>, kuten Wing<PERSON> ja <PERSON>, hall<PERSON><PERSON>.", "The package \"{0}\" was not found on the package manager \"{1}\"": "<PERSON><PERSON><PERSON> \"{0}\" ei löytynyt pake<PERSON> \"{1}\"", "The package bundle could not be created due to an error.": "Pakettinippua ei voitu luoda virheen takia.", "The package bundle is not valid": "Pakettinippu ei kelpaa", "The package manager \"{0}\" is disabled": "Paketinhallinta \"{0}\" on poistettu käytöstä", "The package manager \"{0}\" was not found": "Paketinhallintaa \"{0}\" ei löytynyt", "The package {0} from {1} was not found.": "Pakettia {0} lähettäjältä {1} ei l<PERSON>.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Tässä lueteltuja paketteja ei oteta huomioon päivityksiä tarkistettaessa. Kaksoisnapsauta niitä tai napsauta niiden oikealla puolella olevaa painiketta lopettaaksesi niiden päivitysten huomioimisen.", "The selected packages have been blacklisted": "<PERSON><PERSON><PERSON> paketit on asetettu mustalle listalle", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Varmuuskopion koon arvioidaan olevan alle 1 Mt.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> {source} l<PERSON><PERSON><PERSON><PERSON> {manager} onnistuneesti", "The source {source} was removed from {manager} successfully": "Lähde {source} p<PERSON><PERSON><PERSON><PERSON> {manager} onnistuneesti", "The system tray icon must be enabled in order for notifications to work": "Ilmaisinalueen kuvakkeen on oltava käytössä, jotta il<PERSON> toimivat", "The update process has been aborted.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on keskeytetty.", "The update process will start after closing UniGetUI": "Päivitysprosessi alkaa UniGetUI:n sulkemisen jälkeen", "The update will be installed upon closing WingetUI": "<PERSON><PERSON><PERSON><PERSON>, kun UniGetUI suljetaan", "The update will not continue.": "Päivitys ei jatku.", "The user has canceled {0}, that was a requirement for {1} to be run": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on peru<PERSON><PERSON>t kohteen {0}, mik<PERSON> oli vaatimus {1}:n suorittamiselle", "There are no new UniGetUI versions to be installed": "Uusia UniGetUI-versioita ei ole asennettavissa", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Toimintaa on meneillään. UniGetUI:n sulkeminen voi aiheuttaa niiden epäonnistumisen. Haluatko jatkaa?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "YouTubessa on hienoja videoita, jotka esittelevät UniGetUI:ta ja sen ominaisuuksia. Voit oppia hyödyllisiä temppuja ja vinkkejä!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "On kaksi tärkeintä syytä olla suorittamatta UniGetUI:ta järjestelmänvalvojana: Ensimmäinen syy on se, että Scoop-paketinhallinta saattaa aiheuttaa ongelmia joidenkin komentojen kanssa, kun se suoritetaan järjestelmänvalvojan oikeuksilla. Toinen on se, että UniGetUI:n käyttäminen järjestelmänvalvojana tarkoittaa, että kaikki lataamasi paketit ajetaan järjestelmänvalvojana (ja tämä ei ole turvallista). <PERSON><PERSON>, että jos sinun on asennettava tietty paketti järjestelmänvalvojana, voit aina napsauttaa kohdetta hiiren kakkospainikkeella -> Asenna/Päivitä/Poista asennus järjestelmänvalvojana.", "There is an error with the configuration of the package manager \"{0}\"": "<PERSON><PERSON><PERSON><PERSON>innan \"{0}\" määrityksessä on virhe", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Asennus on meneillään. Jos suljet UniGetUI:n, asennus saattaa epäonnistua ja aiheuttaa odottamattomia tuloksia. Haluatko silti lopettaa UniGetUI:n?", "They are the programs in charge of installing, updating and removing packages.": "<PERSON><PERSON> ovat <PERSON>, jotka vastaavat pakettien asent<PERSON>, päivittämisestä ja poistam<PERSON>ta.", "Third-party licenses": "Kolmannen osapuolen lisenssit", "This could represent a <b>security risk</b>.": "Tämä voi olla <b>turvar<PERSON><PERSON></b>.", "This is not recommended.": "Tätä ei suositella", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Tä<PERSON>ä johtuu todenn<PERSON>isesti siitä, että sinulle lähetetty paketti poistettiin tai julkaistiin paketinhallinnassa, jota et ole ottanut käyt<PERSON>öö<PERSON>. Vastaanotettu tunnus on {0}", "This is the <b>default choice</b>.": "<PERSON><PERSON><PERSON><PERSON> on <b>o<PERSON><PERSON><PERSON><PERSON><PERSON></b>.", "This may help if WinGet packages are not shown": "Tämä voi auttaa, j<PERSON>-paketteja ei näytetä", "This may help if no packages are listed": "Tämä voi auttaa, jos lue<PERSON> ei ole paketteja", "This may take a minute or two": "Tämä voi kestää muutaman minuutin", "This operation is running interactively.": "Tämä toiminto ajetaan interaktiivisesti.", "This operation is running with administrator privileges.": "<PERSON><PERSON><PERSON><PERSON> toiminto on käynnissä järjestelmänvalvojan oikeuksilla.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "T<PERSON><PERSON>ä<PERSON> paketin voi päivittää", "This package can be updated to version {0}": "<PERSON><PERSON><PERSON><PERSON> paketti voidaan päivittää versioon {0}", "This package can be upgraded to version {0}": "<PERSON><PERSON><PERSON><PERSON> paketti voidaan päivittää versioon {0}", "This package cannot be installed from an elevated context.": "Tätä pakettia ei voi asentaa korotetusta sisällöstä.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "T<PERSON>ssä paketissa ei ole kuvakaappauksia tai siitä puuttuu kuvake? Auta UniGetUI:ta lisäämällä puuttuvat kuvakkeet ja kuvakaappaukset avoimeen julkiseen tietokantaamme.", "This package is already installed": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON> on jo as<PERSON>u", "This package is being processed": "<PERSON><PERSON><PERSON> pakettia käsitellään", "This package is not available": "Tämä paketti ei ole sa<PERSON>", "This package is on the queue": "<PERSON><PERSON><PERSON><PERSON> paketti on jonossa", "This process is running with administrator privileges": "<PERSON><PERSON><PERSON><PERSON> prosessi on käynnissä järjestelmänvalvojan oikeuk<PERSON>lla", "This project has no connection with the official {0} project — it's completely unofficial.": "Tällä projektilla ei ole yhteyttä viralliseen {0}-projektiin – se on tä<PERSON>in epävirallinen.", "This setting is disabled": "<PERSON><PERSON><PERSON><PERSON> asetus on poistettu käytöstä", "This wizard will help you configure and customize WingetUI!": "Tämä ohjattu toiminto auttaa sinua määrittämään ja mukauttamaan UniGetUI:n!", "Toggle search filters pane": "<PERSON><PERSON><PERSON><PERSON> hakusuodattimien ruutua", "Translators": "Kääntäjät", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON><PERSON><PERSON> tähän lisättävän lähteen nimi ja URL-osoite välilyönnillä erotettuna.", "Unable to find package": "Pakettia ei lö<PERSON>y", "Unable to load informarion": "Tietoja ei voi ladata", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI kerää anonyymejä käyttötietoja parantaakseen käyttökokemusta.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI kerää anonyymejä k<PERSON>ötietoja, joiden ainoa tarkoit<PERSON> on ymmärtää ja parantaa käyttökokemusta.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI on havainnut uuden työpöydän pika<PERSON>en, joka voidaan poistaa automaattisesti.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI on hava<PERSON>ut seuraavat työpöydän pikakuvakkeet, jotka voidaan poistaa automaattisesti tulevien päivitysten yhteydessä", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI on havainnut {0} uutta työpöydän p<PERSON>, jotka voidaan poistaa automaattisesti.", "UniGetUI is being updated...": "UniGetUI päivitetään...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI ei liity yhteensopiviin paketinhallintaohjelmiin. UniGetUI on itsenäinen projekti.", "UniGetUI on the background and system tray": "UniGetUI taustalla ja ilmaisinalueella", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI vaatii {0} <PERSON><PERSON><PERSON><PERSON><PERSON>, mutta sitä ei löytynyt järjestelmästäsi.", "UniGetUI startup page:": "UniGetUI aloitussivu:", "UniGetUI updater": "UniGetUI päivitysohjelma", "UniGetUI version {0} is being downloaded.": "UniGetUI-versiota {0} ladataan.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} on valmis asennettavaksi.", "Uninstall": "Po<PERSON> asennus", "Uninstall Scoop (and its packages)": "<PERSON><PERSON> (ja sen paketit)", "Uninstall and more": null, "Uninstall and remove data": "Poista asennus ja poista tietoja", "Uninstall as administrator": "Poista asennus järjestelmänvalvojana", "Uninstall canceled by the user!": "K<PERSON><PERSON>täjä on peru<PERSON>anut asennuksen poiston!", "Uninstall failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> poisto ep<PERSON>", "Uninstall options": null, "Uninstall package": "<PERSON><PERSON> paketin asennus", "Uninstall package, then reinstall it": "<PERSON><PERSON> paketin asennus ja uudelleen asenna se", "Uninstall package, then update it": "<PERSON><PERSON> paketti ja päivitä se", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Poista valitut paketit", "Uninstall selection": null, "Uninstall succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> poisto on<PERSON>ui", "Uninstall the selected packages with administrator privileges": "Poista valitut paketit järjestelmänvalvojan <PERSON>", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON>, j<PERSON><PERSON> on \"{0}\", ei julkaista missään pake<PERSON>, joten niistä ei ole saatavilla tietoja.", "Unknown": "Tuntematon", "Unknown size": "Tu<PERSON>mat<PERSON> koko", "Unset or unknown": "Ei asetettu tai tuntematon", "Up to date": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update": "Päivitä", "Update WingetUI automatically": "Päivitä UniGetUI automaattisesti", "Update all": "Päivitä kaikki", "Update and more": "Päivitysvaihtoehdot", "Update as administrator": "Päivitä järjestelmänvalvojana", "Update check frequency, automatically install updates, etc.": "<PERSON>ä<PERSON><PERSON> tark<PERSON>ust<PERSON>, päivitysten automaattinen asennus jne.", "Update date": "Päivityksen päivämäärä", "Update failed": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "Update found!": "Päivitys löytyi!", "Update now": "Päivitä nyt", "Update options": "<PERSON><PERSON><PERSON><PERSON> vali<PERSON>", "Update package indexes on launch": "Päivitä pakettihakemistot käynnistyksen yhteydessä", "Update packages automatically": "Päivitä paketit automaattisesti", "Update selected packages": "Päivitä valitut paketit", "Update selected packages with administrator privileges": "Päivitä valitut paketit järjestelmänvalvojan o<PERSON>lla", "Update selection": "Päivitä valitut", "Update succeeded": "<PERSON><PERSON><PERSON><PERSON>", "Update to version {0}": "P<PERSON><PERSON><PERSON>tty versioon {0}", "Update to {0} available": "{0} p<PERSON><PERSON><PERSON> sa<PERSON>", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Päivitä vcpkg:n Git-porttitiedostot automaattisesti (vaatii Gitin asennettuna)", "Updates": "Päivitykset", "Updates available!": "Päivityksiä saatavilla!", "Updates for this package are ignored": "<PERSON><PERSON><PERSON>ä<PERSON> paketin päivitykset ohitetaan", "Updates found!": "Päivityksiä löytyi!", "Updates preferences": "<PERSON><PERSON><PERSON><PERSON>", "Updating WingetUI": "Päivitetään UniGetUI", "Url": "<PERSON><PERSON><PERSON>", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Käytä vanhaa WinGetiä PowerShell CMDLettien sijaan", "Use a custom icon and screenshot database URL": "Käytä mukautettua kuvaketta ja kuvakaappausten tietokannan URL-osoitetta", "Use bundled WinGet instead of PowerShell CMDlets": "Käytä niputettua WinGetiä PowerShell CMDletien sijaan", "Use bundled WinGet instead of system WinGet": "Käytä paketoitua WinGetiä järjestelmä WinGetin sijaan", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "<PERSON><PERSON><PERSON><PERSON> asennettua G<PERSON> p<PERSON>an", "Use system Chocolatey": "Käytä järjestelmän Chocolateytä", "Use system Chocolatey (Needs a restart)": "Käytä järjestelmän Chocolateytä (vaati<PERSON> oh<PERSON><PERSON> uudelleen käynnistämisen)", "Use system Winget (Needs a restart)": "Käytä järjestelmän Wingetiä (vaati<PERSON> oh<PERSON><PERSON> uudelleen käynnistämisen)", "Use system Winget (System language must be set to english)": "Käytä järjestelmän Wingetiä (järjestelmän kielen tulee olla englanti)", "Use the WinGet COM API to fetch packages": "Käytä WinGet COM -sovellusliittymää pakettien hakemiseen", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Käytä WinGet PowerShell -moduulia WinGet COM API:n sijasta", "Useful links": "Hyödyllisiä <PERSON>jä", "User": "Käyttäjä", "User interface preferences": "Käyttöliittymä asetukset", "User | Local": "Käyttäjä | Paikallinen", "Username": "K<PERSON>yttäjät<PERSON>nus", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "UniGetUI:n käyttö edellyttää GNU Lesser General Public License v2.1 -lisenssin hyväksymistä", "Using WingetUI implies the acceptation of the MIT License": "UniGetUI:n käyttö edellyttää MIT-lisenssin hyväksymistä", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg-juurta ei löytynyt. Määritä %VCPKG_ROOT% ympäristömuuttuja tai määritä se UniGetUI-asetuksista", "Vcpkg was not found on your system.": "Vcpkg:ta ei löytynyt järjestelmästäsi.", "Verbose": "<PERSON><PERSON><PERSON><PERSON>", "Version": "Versio", "Version to install:": "Asennettava versio:", "Version:": "Versio:", "View GitHub Profile": "Näytä GitHub profiili", "View WingetUI on GitHub": "Näytä UniGetUI GitHubissa", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Näytä UniGetUI-lähdekoodi. Sieltä voit ilmoittaa virheistä tai ehdottaa ominaisuuksia tai jopa osallistua suoraan UniGetUI-projektiin", "View mode:": "Katselutila:", "View on UniGetUI": "Näytä UniGetUI:ssa", "View page on browser": "Näytä sivu selaimessa", "View {0} logs": "Näytä {0} lokit", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "<PERSON><PERSON><PERSON>, että laite muodostaa yhteyden Internetiin, ennen kuin yrität tehdä tehtäviä, jotka edellyttävät Internet-yhteyttä.", "Waiting for other installations to finish...": "Odotetaan muiden asennusten valmistumista...", "Waiting for {0} to complete...": "<PERSON><PERSON><PERSON><PERSON>, että {0} valmistuu...", "Warning": "Varo<PERSON><PERSON>", "Warning!": "Varoitus!", "We are checking for updates.": "Tarkistamme päivityksiä.", "We could not load detailed information about this package, because it was not found in any of your package sources": "<PERSON><PERSON> voineet ladata yksityiskohtaisia ​​tietoja tästä paketista, koska niitä ei löytynyt mistään pakettilähteistäsi.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "<PERSON><PERSON> voineet ladata yksityiskohtaisia ​​tietoja tästä paketista, koska sitä ei asennettu saatavilla olevasta paketinhallinnasta", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON><PERSON> voineet {action} {package}. Yritä myöhemmin uudelleen. Napsauta \"{showDetails}\" saadaksesi lokit asennusoh<PERSON>lmasta.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.", "We couldn't find any package": "<PERSON><PERSON> pake<PERSON>", "Welcome to WingetUI": "Tervetuloa UniGetUI:n", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Kun uusia pika<PERSON><PERSON><PERSON> ha<PERSON>, poista ne automaattisesti tämän valintaikkunan näyttämisen sijaan.", "Which backup do you want to open?": null, "Which package managers do you want to use?": "Mitä paketin<PERSON>intaohjelmia haluat käyttää?", "Which source do you want to add?": "Minkä lähteen haluat lisätä?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Vaikka Wingetiä voidaan käyttää UniGetUI:ssa, UniGetUI:ta voidaan käyttää muiden paketinhallintaohjelmien kanssa, mikä voi olla hämmentävää. Aiemmin UniGetUI oli suunniteltu toimimaan vain Wingetin kanssa, mutta tämä ei ole enää totta, joten UniGetUI ei edusta sitä, mitä tällä projektilla on tarkoitus olla.", "WinGet could not be repaired": "WinGetiä ei voitu korjata", "WinGet malfunction detected": "WinGet-vika hava<PERSON>u", "WinGet was repaired successfully": "Win<PERSON>et kor<PERSON><PERSON> onnist<PERSON>i", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Kai<PERSON><PERSON> on ajantasalla", "WingetUI - {0} updates are available": "UniGetUI - {0} päivityksiä on saatavilla", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "UniGetUI Kotisivu", "WingetUI Homepage - Share this link!": "UniGetUI Kotisivu - jaa tämä linkki!", "WingetUI License": "UniGetUI Lisenssi", "WingetUI Log": "UniGetUI loki", "WingetUI Repository": "UniGetUI Arkisto", "WingetUI Settings": "UniGetUI Asetukset", "WingetUI Settings File": "UniGetUI asetustiedosto", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI käyttää seuraavia kirjastoja. Ilman niitä UniGetUI ei olisi ollut mahdollinen.", "WingetUI Version {0}": "UniGetUI Versio {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI:n automaattinen käynnistys, sovelluksen käynnistysasetukset", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI voi tarkistaa, onko ohjelmistossasi päivityksiä, ja asentaa ne automaattisesti, jos haluat", "WingetUI display language:": "UniGetUI näytettävä kieli:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI on a<PERSON>tu jär<PERSON>stelmänvalvojana, mikä ei ole suositeltavaa. Käytettäessä UniGetUI:ta järjestelmänvalvojana, KAIKKI UniGetUI:sta käynnistetyt toiminnot saavat järjestelmänvalvojan oikeudet. Voit edelleen käyttää ohje<PERSON>, mutta suosittelem<PERSON>, että et käytä UniGetUI:ta järjestelmänvalvojan oikeuksilla.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI on käännetty yli 40 kielelle vapaaehtoisten kääntäjien ansiosta. Kiitos 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI:ta ei ole konekäännetty! Seuraavat käyttäjät ovat vastanneet käännöksistä:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI on sovellus, joka helpottaa ohjelmistojen hallintaa tarjoamalla all-in-one graafisen käyttöliittymän komentorivipakettien hallintaa varten.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI nimetään uudelleen korostaaksemme eroa UniGetUI:n (käyttämäsi käyttöliittymän) ja WinGetin (Microsoftin kehittämä pake<PERSON>inta, johon en liity) välillä.", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI päivitetään. Kun valmis, UniGetUI käynnistyy uudelleen", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUi on ilmainen ja myös pysyy aina ilmaisena. Ei <PERSON>, ei luottokortteja, ei preemium versioita. 100% ilmanen, aina.", "WingetUI log": "UniGetUI logi", "WingetUI tray application preferences": "UniGetUI-alustasovelluksen asetukset", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI käyttää seuraavia kirjastoja. Ilman niitä UniGetUI ei olisi ollut mahdollinen.", "WingetUI version {0} is being downloaded.": "UniGetUI versio {0} ladataan.", "WingetUI will become {newname} soon!": "UniGetUI:sta tulee pian {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI ei tarkista päivityksiä säännöllisesti. Ne tarkistetaan edelleen käynnistyksen yhteydessä, mutta sinua ei varoiteta niistä.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI näyttää UAC-kehotteen joka kerta, kun paketti vaatii kohotetun käyttöoikeuden.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "UniGetUI nimetään pian {newname};ksi. Tämä ei tarkoita muutoksia sovellukseen. Minä (kehittäjä) jatkan tämän projektin kehittämistä kuten nytkin, mutta toisella nimell<PERSON>.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI ei olisi ollut mahdollinen ilman rakkaiden avustajien apua. <PERSON><PERSON><PERSON> he<PERSON>-profi<PERSON><PERSON><PERSON>, UniGetUI ei olisi mahdollista ilman niitä!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI ei olisi ollut mahdollinen ilman avustajien apua. Kiitos kaikille 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} on valmis asennettavaksi.", "Write here the process names here, separated by commas (,)": null, "Yes": "K<PERSON><PERSON>ä", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "<PERSON><PERSON><PERSON> on tällä hetkellä asennettuna versio {0}", "You have installed WingetUI Version {0}": "Asennettu UniGetUI versio: {0}", "You may lose unsaved data": "Tallentamaton tieto voi kadota", "You may need to install {pm} in order to use it with WingetUI.": "<PERSON>un on ehk<PERSON> asennettava {pm} käyttääksesi sitä UniGetUI:n kanssa.", "You may restart your computer later if you wish": "Voit uudelleen käynnistää tietokoneesi halutessasi", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "<PERSON>ulta pyydetään vain kerran lupaa pakettien asennukselle ylläpitäjän o<PERSON>.", "You will be prompted only once, and every future installation will be elevated automatically.": "<PERSON>ulta pyydetään vain kerran lupaa pakettien asennuks<PERSON> korotetuilla o<PERSON>.", "You will likely need to interact with the installer.": "<PERSON><PERSON> on todennäköisesti oltava vuorovaikutuksessa as<PERSON><PERSON><PERSON><PERSON><PERSON> kanssa.", "[RAN AS ADMINISTRATOR]": "AJETTU JÄRJESTELMÄNVALVOJANA", "buy me a coffee": "tarjoa minulle kup<PERSON> kahvia", "extracted": "purettu", "feature": "ominaisuus", "formerly WingetUI": "entinen WingetUI", "homepage": "kotisivu", "install": "as<PERSON>na", "installation": "as<PERSON><PERSON>", "installed": "<PERSON><PERSON><PERSON><PERSON>", "installing": "<PERSON><PERSON><PERSON><PERSON>", "library": "k<PERSON><PERSON><PERSON>", "mandatory": "<PERSON><PERSON><PERSON><PERSON>", "option": "vai<PERSON><PERSON>hto", "optional": "<PERSON><PERSON><PERSON><PERSON>", "uninstall": "poista asennus", "uninstallation": "as<PERSON><PERSON><PERSON><PERSON> pois<PERSON>n", "uninstalled": "as<PERSON><PERSON> poiste<PERSON>u", "uninstalling": "pois<PERSON><PERSON><PERSON> asennusta", "update(noun)": "pä<PERSON>s", "update(verb)": "päivitä", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updating": "päivitetään", "version {0}": "versio {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} Asennusvaihtoehdot ovat tällä hetkellä lukittuja, koska {0} noudattaa oletusasennusvaihtoehtoja.", "{0} Uninstallation": "{0} as<PERSON><PERSON><PERSON><PERSON> poisto", "{0} aborted": "{0} per<PERSON>u", "{0} can be updated": "{0} <PERSON><PERSON> p<PERSON>", "{0} can be updated to version {1}": "{0} <PERSON>aan päivittää versioon {1}", "{0} days": "{0} päivää", "{0} desktop shortcuts created": "{0} työpöydän pika<PERSON>vaketta luotu", "{0} failed": "{0} epä<PERSON>nist<PERSON>ut", "{0} has been installed successfully.": "{0} on asennettu onnistuneesti.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} on asennettu onnistuneesti. On suositeltavaa käynnistää UniGetUI uudelleen asennuksen viimeistelemiseksi", "{0} has failed, that was a requirement for {1} to be run": "{0} ep<PERSON><PERSON>nist<PERSON> ja se vaaditaan {1} a<PERSON><PERSON><PERSON>i", "{0} homepage": "{0} kot<PERSON><PERSON>", "{0} hours": "{0} tuntia", "{0} installation": "{0} as<PERSON><PERSON>", "{0} installation options": "{0} as<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "{0} installer is being downloaded": "{0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{0} is being installed": "{0} <PERSON><PERSON><PERSON>", "{0} is being uninstalled": "{0} as<PERSON><PERSON><PERSON> p<PERSON>", "{0} is being updated": "{0} o<PERSON><PERSON>", "{0} is being updated to version {1}": "{0} p<PERSON><PERSON><PERSON>tty versioon {1}", "{0} is disabled": "{0} on poistettu kä<PERSON>ä", "{0} minutes": "{0} minu<PERSON>ia", "{0} months": "{0} k<PERSON><PERSON><PERSON>a", "{0} packages are being updated": "{0} pake<PERSON>t päivitetään", "{0} packages can be updated": "{0} pake<PERSON>t voidaan päivittää", "{0} packages found": "{0} p<PERSON><PERSON><PERSON>", "{0} packages were found": "{0} p<PERSON><PERSON><PERSON>", "{0} packages were found, {1} of which match the specified filters.": "<PERSON><PERSON><PERSON><PERSON> {0} p<PERSON><PERSON><PERSON>, joista {1} vastaa määritettyj<PERSON> suodatti<PERSON>.", "{0} settings": "{0} asetukset", "{0} status": "{0} tila", "{0} succeeded": "{0} on<PERSON><PERSON>", "{0} update": "{0} päivitys", "{0} updates are available": "{0} p<PERSON><PERSON><PERSON><PERSON> on saatavilla", "{0} was {1} successfully!": "{0} {1} onnist<PERSON>esti!", "{0} weeks": "{0} viikkoa", "{0} years": "{0} vuotta", "{0} {1} failed": "{0} {1} e<PERSON><PERSON><PERSON><PERSON><PERSON>", "{package} Installation": "{package} <PERSON><PERSON><PERSON>", "{package} Uninstall": "{package} <PERSON><PERSON><PERSON><PERSON><PERSON> poisto", "{package} Update": "{package} Päivitys", "{package} could not be installed": "{package} ei voitu asentaa", "{package} could not be uninstalled": "{package} asen<PERSON>ta ei voitu poistaa", "{package} could not be updated": "{package} pä<PERSON>s ei onnistu", "{package} installation failed": "{package} as<PERSON><PERSON> ep<PERSON>", "{package} installer could not be downloaded": "{package} as<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ei voitu ladata", "{package} installer download": "{package} as<PERSON><PERSON><PERSON><PERSON><PERSON>", "{package} installer was downloaded successfully": "{package} as<PERSON><PERSON><PERSON><PERSON><PERSON> onnistuneesti", "{package} uninstall failed": "{package} as<PERSON><PERSON><PERSON><PERSON> poisto ep<PERSON>ui", "{package} update failed": "{package} p<PERSON><PERSON><PERSON> epäon<PERSON>ui", "{package} update failed. Click here for more details.": "{package} päivitys epäonnistui. Klikkaa tästä saadaksesi lisätietoja.", "{package} was installed successfully": "{package} on asennettu onnistuneesti", "{package} was uninstalled successfully": "{package} on poistettu onnistuneesti", "{package} was updated successfully": "{package} p<PERSON><PERSON><PERSON><PERSON><PERSON> onnistuneesti", "{pcName} installed packages": "{pcName} as<PERSON>i pake<PERSON>ja", "{pm} could not be found": "{pm} ei l<PERSON>yt", "{pm} found: {state}": "{pm} l<PERSON>ytyi: {state}", "{pm} is disabled": "{pm} ei ole k<PERSON>ä", "{pm} is enabled and ready to go": "{pm} on käytössä ja valmis käyttöön", "{pm} package manager specific preferences": "{pm} pake<PERSON><PERSON><PERSON>an erityiset asetukset", "{pm} preferences": "{pm} asetukset", "{pm} version:": "{pm} versio:", "{pm} was not found!": "{pm} ei löyt<PERSON>yt!"}