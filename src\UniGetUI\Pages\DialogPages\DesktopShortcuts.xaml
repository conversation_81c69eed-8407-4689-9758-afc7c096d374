<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Interface.DesktopShortcutsManager"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Interface"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    MaxWidth="950"
    MaxHeight="500"
    mc:Ignorable="d">

    <Grid
        HorizontalAlignment="Stretch"
        ColumnSpacing="8"
        RowSpacing="8">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="200" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition
                Height="*"
                MinHeight="150"
                MaxHeight="600" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>


        <StackPanel Orientation="Vertical" Spacing="4">
            <widgets:TranslatedTextBlock FontWeight="Bold" Text="UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades" />
            <widgets:TranslatedTextBlock Text="Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact" />
        </StackPanel>
        <StackPanel
            Grid.Column="1"
            VerticalAlignment="Bottom"
            Orientation="Vertical"
            Spacing="4">
            <!--Button Margin="10,0,0,0" HorizontalAlignment="Stretch">
                        <widgets:TranslatedTextBlock Text="Manual scan" />
                        <Button.Flyout>
                            <Flyout
                                x:Name="ManualScanFlyout"
                                LightDismissOverlayMode="Off"
                                Placement="Bottom">
                                <Grid
                                    Width="300"
                                    ColumnSpacing="8"
                                    RowSpacing="16">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <widgets:TranslatedTextBlock
                                        Grid.ColumnSpan="2"
                                        Margin="0,0,0,0"
                                        Text="Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove."
                                        WrappingMode="WrapWholeWords" />
                                    <Button
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        HorizontalAlignment="Stretch"
                                        Click="ManualScanButton_Click"
                                        Style="{ThemeResource AccentButtonStyle}">
                                        <widgets:TranslatedTextBlock Text="Continue" />
                                    </Button>
                                </Grid>
                            </Flyout>
                        </Button.Flyout>
                    </Button-->
            <Button Margin="10,0,0,0" HorizontalAlignment="Stretch">
                <widgets:TranslatedTextBlock Text="Reset list" />
                <Button.Flyout>
                    <widgets:BetterFlyout
                        x:Name="ConfirmResetFlyout"
                        LightDismissOverlayMode="Off"
                        Placement="Bottom">
                        <Grid
                            Width="300"
                            ColumnSpacing="8"
                            RowSpacing="16">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <widgets:TranslatedTextBlock
                                Grid.ColumnSpan="2"
                                Margin="0,0,0,0"
                                Text="Do you really want to reset this list? This action cannot be reverted."
                                WrappingMode="WrapWholeWords" />
                            <Button
                                Grid.Row="1"
                                Grid.Column="1"
                                HorizontalAlignment="Stretch"
                                Click="NoResetButton_Click"
                                Style="{ThemeResource AccentButtonStyle}">
                                <widgets:TranslatedTextBlock Text="No" />
                            </Button>
                            <Button
                                Grid.Row="1"
                                Grid.Column="0"
                                HorizontalAlignment="Stretch"
                                Click="YesResetButton_Click">
                                <widgets:TranslatedTextBlock Text="Yes" />
                            </Button>
                        </Grid>
                    </widgets:BetterFlyout>
                </Button.Flyout>
            </Button>
        </StackPanel>
        <ItemsView
            Name="DeletableDesktopShortcutsList"
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Padding="0,4,2,4"
            HorizontalAlignment="Stretch"
            Background="{ThemeResource ApplicationPageBackgroundThemeBrush}"
            CornerRadius="6"
            ItemsSource="{x:Bind Shortcuts}"
            SelectionMode="None">
            <ItemsView.Layout>
                <StackLayout Orientation="Vertical" Spacing="4" />
            </ItemsView.Layout>
            <ItemsView.ItemTemplate>
                <DataTemplate x:DataType="local:ShortcutEntry">
                    <ItemContainer>
                        <Grid Margin="8,0" ColumnSpacing="4">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="2" />
                                <ColumnDefinition Width="24" />
                                <ColumnDefinition Width="*" MaxWidth="200" />
                                <ColumnDefinition Width="24" />
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Border
                                Grid.Column="0"
                                Width="20"
                                HorizontalAlignment="Center">
                                <CheckBox
                                    MaxWidth="10"
                                    Margin="0,8,0,0"
                                    IsChecked="{x:Bind IsDeletable, Mode=TwoWay}" />
                            </Border>
                            <widgets:TranslatedTextBlock
                                Grid.Column="0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Top"
                                FontSize="10"
                                Text="Delete?" />
                            <FontIcon
                                Grid.Column="2"
                                Width="24"
                                Height="24"
                                Glyph="&#xECAA;" />
                            <TextBlock
                                Grid.Column="3"
                                VerticalAlignment="Center"
                                Text="{x:Bind Name}"
                                ToolTipService.ToolTip="{x:Bind Name}" />
                            <FontIcon
                                Grid.Column="4"
                                Width="24"
                                Height="24"
                                Glyph="&#xE71B;" />
                            <TextBlock
                                Grid.Column="5"
                                VerticalAlignment="Center"
                                Text="{x:Bind Path}"
                                ToolTipService.ToolTip="{x:Bind Path}" />
                            <Button
                                Grid.Column="6"
                                Width="32"
                                Height="32"
                                Padding="0"
                                Click="{x:Bind OpenShortcutPath}"
                                IsEnabled="{x:Bind ExistsOnDisk}">
                                <widgets:LocalIcon
                                    Width="24"
                                    Height="24"
                                    Icon="launch" />
                            </Button>
                            <Button
                                Grid.Column="7"
                                Width="32"
                                Height="32"
                                Padding="0"
                                Click="{x:Bind ResetShortcut}">
                                <FontIcon FontSize="16" Glyph="&#xE74D;" />
                            </Button>
                        </Grid>
                    </ItemContainer>
                </DataTemplate>
            </ItemsView.ItemTemplate>
        </ItemsView>
        <CheckBox Name="AutoDeleteShortcutsCheckbox" Grid.Row="2">
            <widgets:TranslatedTextBlock Text="When new shortcuts are detected, delete them automatically instead of showing this dialog." />
        </CheckBox>
        <Button
            Grid.Row="2"
            Grid.Column="2"
            HorizontalAlignment="Stretch"
            Click="CloseSaveButton_Click"
            Style="{ThemeResource AccentButtonStyle}">
            <widgets:TranslatedTextBlock Text="Save and close" />
        </Button>
        <widgets:DialogCloseButton
            Grid.Column="1"
            Margin="0,-63,-24,0"
            Click="CloseButton_Click" />
    </Grid>
</Page>
