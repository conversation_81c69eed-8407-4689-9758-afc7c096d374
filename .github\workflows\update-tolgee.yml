name: Tolgee CI

on:
  schedule:
    - cron: 0 0 * * *
  workflow_dispatch:
jobs:
  load-translations:
    if: github.repository == 'marticliment/UniGetUI'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        
      - name: Download Tolgee translations
        env:
          TOLGEE_KEY: "${{ secrets.TOLGEE_TOKEN }}"
        run: |
          cd scripts
          python download_translations.py --autocommit
      
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v7
        with:
          delete-branch: true
          base: main
          token: "${{ secrets.REPO_SCOPED_TOKEN }}"
          labels: tolgee-ci
          title: "Load translations from Tolgee"
          body: ""
          assignees: marticliment
          author: "<PERSON><PERSON> <<EMAIL>>"
          commit-message:  |
            Load updated translations from Tolgee
            Co-authored-by: <PERSON><PERSON> from the multiverse <<EMAIL>>
          branch: pull-request/update-translation
