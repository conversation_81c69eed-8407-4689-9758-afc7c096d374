{"\"{0}\" is a local package and can't be shared": "\"{0}\" is een lokaal pakket en kan niet worden gedeeld", "\"{0}\" is a local package and does not have available details": "\"{0}\" is een lokaal pakket en heeft geen beschikbare details", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" is een lokaal pakket en is niet compatibel met deze functie", "(Last checked: {0})": "(Laatste controle: {0})", "(Number {0} in the queue)": "(Nummer {0} in de wachtrij)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@abbydiode, @CateyeNL, @mia-r<PERSON><PERSON><PERSON>, @Stephan-<PERSON>", "0 packages found": "0 pakketten gevonden", "0 updates found": "0 updates gevonden", "1 - Errors": "1 - Fout", "1 day": "1 dag", "1 hour": "1 uur", "1 month": "1 maand", "1 package was found": "1 pakket gevonden", "1 update is available": "1 update be<PERSON><PERSON><PERSON><PERSON>", "1 week": "1 week", "1 year": "1 jaar", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON><PERSON> naar de pagina \"{0}\" of \"{1}\".", "2 - Warnings": "2 - Waarschuwingen", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON>(en) die je aan de bundel wilt toevo<PERSON>n, en selecteer hun meest linkse selectievakje.", "3 - Information (less)": "3 - Informatie (beperkt)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON> je de pakketten die je aan de bundel wilt toevoegen hebt geselecteerd,  klik dan op de optie \"{0}\" op de werkbalk.", "4 - Information (more)": "4 - Informatie (meer)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. De pakketten zijn dan aan de bundel zijn toegevo<PERSON>d. <PERSON> kunt doorgaan met het toe<PERSON><PERSON><PERSON> van pakketten of de bundel exporteren.", "5 - information (debug)": "5 - Informatie (foutopsporing)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Een populaire C/C++ bibliotheekmanager. Vol met C/C++-bibliotheken en andere C/C++-gerelateerde hulpprogramma's<br>Bevat: <b>C/C++-bibliotheken en gerelateerde hulpprogramma's</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Een opslagplaats vol hulpmiddelen en uitvoerbare bestanden die zijn ontworpen met het .NET-ecosysteem van Microsoft in gedachten. <br>Bevat: <b>.NET-gerelateerde hulpmiddelen  en scripts</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Een opslagplaats vol hulpmiddelen ontworpen voor het .NET-ecosysteem van Microsoft.<br>Bevat: <b>.NET-gerelateerde hulpmiddelen</b>", "A restart is required": "<PERSON><PERSON> herstart is vereist", "Abort install if pre-install command fails": "Installatie afbreken als pre-installatie opdracht mislukt", "Abort uninstall if pre-uninstall command fails": "Verwijdering afbreken als pre-verwijder opdracht mislukt", "Abort update if pre-update command fails": "Update afbreken als pre-update opdracht mislukt", "About": "Over", "About Qt6": "Over Qt6", "About WingetUI": "Over UniGetUI", "About WingetUI version {0}": "Over UniGetUI versie {0}", "About the dev": "Over de ontwikkelaar", "Accept": "Accept<PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "Actie bij dubbelklikken op pakketten, succesvolle installaties verbergen", "Add": "Toevoegen", "Add a source to {0}": "Voeg een bron toe aan {0}", "Add a timestamp to the backup file names": "<PERSON><PERSON> tijdstempel toevoegen aan de namen van de back-upbestanden", "Add a timestamp to the backup files": "Voeg een tijdstempel toe aan de back-upbestanden", "Add packages or open an existing bundle": "<PERSON><PERSON><PERSON> toe<PERSON>n of een bestaande bundel openen", "Add packages or open an existing package bundle": "<PERSON><PERSON><PERSON> pakketten toe of open een bestaande pakketbundel", "Add packages to bundle": "Pak<PERSON><PERSON> toe<PERSON>egen aan bundel", "Add packages to start": "Voeg pakketten toe om te starten", "Add selection to bundle": "<PERSON><PERSON> toe<PERSON>n aan bundel", "Add source": "Bron toe<PERSON>n", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Updates die mis<PERSON><PERSON> met '<PERSON><PERSON> toe<PERSON> update gevonden' toevoegen aan de lij<PERSON> met genegeerde updates", "Adding source {source}": "<PERSON><PERSON> toe<PERSON> {source}", "Adding source {source} to {manager}": "<PERSON><PERSON> {source} toe<PERSON><PERSON>n aan {manager}", "Addition succeeded": "Toevoeging voltooid", "Administrator privileges": "<PERSON><PERSON><PERSON>", "Administrator privileges preferences": "Voorkeuren voor administratorrechten", "Administrator rights": "<PERSON><PERSON><PERSON>", "Administrator rights and other dangerous settings": "Administratorrechten en andere risicovolle instellingen", "Advanced options": "Geavanceerde opties", "All files": "Alle bestanden", "All versions": "Alle versies", "Allow changing the paths for package manager executables": "Het wij<PERSON>en van paden van pakketbeheerders toestaan", "Allow custom command-line arguments": "Aangepaste opdrachtregelargumenten toestaan", "Allow importing custom command-line arguments when importing packages from a bundle": "Het importeren van aangepaste opdrachtregelargumenten toestaan bij het importeren van pakketten uit een bundel", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Het importeren van aangepaste pre-installatie- en post-installatieopdrachten toestaan bij het importeren van pakketten uit een bundel", "Allow package operations to be performed in parallel": "<PERSON><PERSON><PERSON> u<PERSON> van pakketbewerkingen toestaan", "Allow parallel installs (NOT RECOMMENDED)": "<PERSON><PERSON><PERSON> install<PERSON> (NIET AANBEVOLEN)", "Allow pre-release versions": "Pre-release versies <PERSON><PERSON><PERSON>", "Allow {pm} operations to be performed in parallel": "<PERSON><PERSON><PERSON> dat {pm}-bewerkingen parallel worden uitgevoerd", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Als alternatief kunt je ook {0} installeren door de volgende opdracht uit te voeren in een Windows PowerShell-opdrachtregel:", "Always elevate {pm} installations by default": "Installaties van {pm} alti<PERSON><PERSON> standaar<PERSON> u<PERSON><PERSON><PERSON> met verho<PERSON><PERSON> gebruikersrechten", "Always run {pm} operations with administrator rights": "Bewerking<PERSON> van {pm} alti<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON>", "An error occurred": "Er is een fout opgetreden", "An error occurred when adding the source: ": "Er is een fout opgetreden bij het toevoegen van de <PERSON>:", "An error occurred when attempting to show the package with Id {0}": "Er is een fout opgetreden bij de poging om het pakket met Id {0} weer te geven", "An error occurred when checking for updates: ": "Er is een fout opgetreden bij het controleren op updates:", "An error occurred while attempting to create an installation script:": "Er is een fout opgetreden bij het aanmaken van een installatiescript:", "An error occurred while loading a backup: ": "Er is een fout opgetreden bij het laden van een back-up:", "An error occurred while logging in: ": "Er is een fout opgetreden bij het inloggen:", "An error occurred while processing this package": "Er is een fout opgetreden bij de verwerking van dit pakket", "An error occurred:": "Er is een fout opgetreden:", "An interal error occurred. Please view the log for further details.": "Er is een interne fout opgetreden. Bekijk het logboek voor meer informatie.", "An unexpected error occurred:": "Er is een onverwachte fout opgetreden:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Er is een onverwacht probleem opgetreden bij het repareren van WinGet. Probeer het later opnieuw", "An update was found!": "Er is een update gevonden!", "Android Subsystem": "Andoid Subsysteem", "Another source": "Andere bron", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Nieuwe snelkoppelingen die tijdens een installatie of een updatebewerking worden aangemaakt, worden automatisch verwijderd, in plaats van een bevestigingsprompt te tonen als ze voor het eerst worden gedetecteerd.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Snelkoppelingen die buiten UniGetUI om zijn aangemaakt of gewijzigd, worden genegeerd. Je kunt ze toevoegen via de knop {0}.", "Any unsaved changes will be lost": "Niet-opgeslagen wijzigingen gaan verloren", "App Name": "Applicatienaam", "Appearance": "Uiterlijk", "Application theme, startup page, package icons, clear successful installs automatically": "App-thema, startpagina, pakketpictogrammen, succesvolle installaties automatisch wissen", "Application theme:": "App-thema:", "Apply": "Toepassen", "Architecture to install:": "Te installeren architectuur:", "Are these screenshots wron or blurry?": "<PERSON><PERSON><PERSON> deze schermopnames fout of on<PERSON><PERSON>?", "Are you really sure you want to enable this feature?": "Weet je echt zeker dat je deze functie wilt inschakelen?", "Are you sure you want to create a new package bundle? ": "Weet je zeker dat je een nieuwe pakketbundel wilt aanmaken?", "Are you sure you want to delete all shortcuts?": "Weet je zeker dat je alle snelkoppelingen wilt verwijderen?", "Are you sure?": "Weet je het zeker?", "Ascendant": "<PERSON><PERSON><PERSON><PERSON>", "Ask for administrator privileges once for each batch of operations": "Vraag <PERSON> keer om administratorrechten voor elke reeks bewerkingen", "Ask for administrator rights when required": "Vraag indien nodig om administratorrechten", "Ask once or always for administrator rights, elevate installations by default": "<PERSON><PERSON><PERSON> k<PERSON> of altijd om administratorrechten, installaties standaard verhogen", "Ask only once for administrator privileges": "<PERSON><PERSON> <PERSON> keer om administratorrechten vragen", "Ask only once for administrator privileges (not recommended)": "<PERSON><PERSON><PERSON> maar <PERSON> keer om administratorrecht<PERSON> (niet aanbevolen)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Vragen om snelkoppelingen te verwijderen die op het bureaublad zijn aangemaakt tijdens een installatie of upgrade.", "Attention required": "Aandacht vereist", "Authenticate to the proxy with an user and a password": "Authenticeer bij de proxy met een gebruikers<PERSON> en een wachtwoord", "Author": "<PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "Bureaubladsnelkoppelingen automatisch verwijderen", "Automatic updates": "Automatische updates", "Automatically save a list of all your installed packages to easily restore them.": "Automatisch een lijst opsla<PERSON> van al je geïnstalleerde pakketten om ze gemakkelijk te herstellen.", "Automatically save a list of your installed packages on your computer.": "Automatisch een lijst op<PERSON><PERSON> van de pakketten die op je computer geïnstalleerd zijn.", "Autostart WingetUI in the notifications area": "UniGetUI automatisch starten in het systeemvak", "Available Updates": "Beschikbare updates", "Available updates: {0}": "Beschikbare updates: {0}", "Available updates: {0}, not finished yet...": "Beschikbare updates: {0}, bijna k<PERSON>ar…", "Backing up packages to GitHub Gist...": "Back-up van pakketten maken op GitHub Gist...", "Backup": "Back-up", "Backup Failed": "Back-up mislukt", "Backup Successful": "Back-up voltooid", "Backup and Restore": "Back-up en <PERSON><PERSON><PERSON>", "Backup installed packages": "Back-up maken van g<PERSON>ïnstalleerde pakketten", "Backup location": "Backuplocatie", "Become a contributor": "Lever een bijdrage", "Become a translator": "Meld je aan als vertaler", "Begin the process to select a cloud backup and review which packages to restore": "Start het proces om een Cloud back-up te selecteren en controleer welke pakketen hersteld moeten worden", "Beta features and other options that shouldn't be touched": "Bètafuncties en andere opties die je beter niet kan gebruiken", "Both": "<PERSON><PERSON>", "Bundle security report": "Veiligheidsrapportage bundelen", "But here are other things you can do to learn about WingetUI even more:": "<PERSON><PERSON> hier zijn nog andere dingen die je kunt doen om nog meer te weten te komen over UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Door een pakketbeheerder uit te schakelen, kun je de pakketten niet meer zien of bijwerken.", "Cache administrator rights and elevate installers by default": "Administrator<PERSON><PERSON> opslaan en rechten van installaties standaard ophogen", "Cache administrator rights, but elevate installers only when required": "Administrator<PERSON><PERSON> opslaan en rechten van installaties alleen verhogen indien vereist", "Cache was reset successfully!": "<PERSON><PERSON><PERSON> met succes gewist!", "Can't {0} {1}": "Kan {1} niet {0}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "Alle bewerkingen annuleren", "Change backup output directory": "De uitvoermap voor back-upbestanden wijzigen", "Change default options": "Standaardopties aanpassen", "Change how UniGetUI checks and installs available updates for your packages": "Wijzig hoe UniGetUI beschikbare updates voor jouw pakketten controleert en installeert", "Change how UniGetUI handles install, update and uninstall operations.": "Wijzigen hoe UniGetUI de installatie-, update- en verwijderingsbewerkingen afhandelt.", "Change how UniGetUI installs packages, and checks and installs available updates": "Wijzigen hoe UniGetUI pakketten installeert en beschikbare updates controleert en installeert", "Change how operations request administrator rights": "Wi<PERSON><PERSON>en hoe bewerkingen administratorrechten aanvragen", "Change install location": "Installatiemap a<PERSON>passen", "Change this": "<PERSON><PERSON> a<PERSON>", "Change this and unlock": "Dit aanpassen en ontgrendelen", "Check for package updates periodically": "Regelmatig controleren op pakketupdates", "Check for updates": "Controleren op updates", "Check for updates every:": "Controleren op updates, elke:", "Check for updates periodically": "Regelmatig controleren op updates", "Check for updates regularly, and ask me what to do when updates are found.": "Controleer regelmatig op updates en vraag wat er gedaan moet worden als deze worden gevonden.", "Check for updates regularly, and automatically install available ones.": "Controleer regelmatig op updates en installeer beschikbare updates automatisch.", "Check out my {0} and my {1}!": "<PERSON><PERSON><PERSON> mijn {0} en mijn {1}!", "Check out some WingetUI overviews": "UniGetUI-overzichten bekijken", "Checking for other running instances...": "Controleren op andere lopende instanties…", "Checking for updates...": "Controleren op updates…", "Checking found instace(s)...": "Controleren op gevonden instantie(s)…", "Choose how many operations shouls be performed in parallel": "Be<PERSON>al hoeveel bewerkingen parallel mogen worden uitgevoerd", "Clear cache": "<PERSON><PERSON><PERSON> wissen", "Clear finished operations": "Voltooide bewerkingen opruimen", "Clear selection": "<PERSON><PERSON>", "Clear successful operations": "Geslaagde bewerkingen wissen", "Clear successful operations from the operation list after a 5 second delay": "Geslaagde bewerkingen na 5 seconden uit de bewerkingslijst wissen", "Clear the local icon cache": "Lokaal pictogrambuffer wissen", "Clearing Scoop cache - WingetUI": "Scoop-buffer wissen - UniGetUI", "Clearing Scoop cache...": "Scoop-buffer wissen…", "Click here for more details": "<PERSON><PERSON> hier voor meer details", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Klik op Installeren om het installatieproces te starten. Als je de installatie overslaat, werkt UniGetUI mogelijk niet zoals verwacht.", "Close": "Sluiten", "Close UniGetUI to the system tray": "UniGetUI sluiten naar het systeemvak", "Close WingetUI to the notification area": "UniGetUI sluiten naar het systeemvak", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Cloud back-up gebru<PERSON>t een privé GitHub Gist om een lijst met geïnstalleerde pakketten op te slaan", "Cloud package backup": "Cloud pakket back-up", "Command-line Output": "Opdrachtregeluitvoer", "Command-line to run:": "Te gebruiken opdrachtregel:", "Compare query against": "Vergelijk zoekopdracht met", "Compatible with authentication": "Compatibel met authenticatie", "Compatible with proxy": "Compatibel met proxy", "Component Information": "Componentinformatie", "Concurrency and execution": "Gelijktijdigheid en uitvoering", "Connect the internet using a custom proxy": "Internetverbinding met be<PERSON><PERSON> van een aangepaste proxy", "Continue": "Doorgaan", "Contribute to the icon and screenshot repository": "Draag bij aan het pictogram- en schermopnamedepot", "Contributors": "Bijdragen", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "<PERSON>ar klembord kopiëren", "Could not add source": "Kan bron niet toe<PERSON>n", "Could not add source {source} to {manager}": "Kan bron {source} niet toe<PERSON><PERSON>n aan {manager}", "Could not back up packages to GitHub Gist: ": "Kon geen back-up van pakketten maken op GitHub Gist:", "Could not create bundle": "Kan bundel niet aan<PERSON>ken", "Could not load announcements - ": "Aankondigingen kunnen niet worden geladen -", "Could not load announcements - HTTP status code is $CODE": "Aankondigingen kunnen niet worden geladen - HTTP-statuscode is $CODE", "Could not remove source": "Kan bron niet verwijderen", "Could not remove source {source} from {manager}": "<PERSON>n bron {source} niet verwijderen uit {manager}", "Could not remove {source} from {manager}": "Kan {source} niet verwijderen uit {manager}", "Create .ps1 script": ".ps1-script aanmaken", "Credentials": "Referenties", "Current Version": "Huidige versie", "Current status: Not logged in": "Huidige status: <PERSON><PERSON>", "Current user": "<PERSON><PERSON><PERSON> g<PERSON>r", "Custom arguments:": "Aangepaste argumenten:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Aangepaste opdrachtregelargumenten kunnen de manier veranderen waarop programma's worden geïnstalleerd, bijgewerkt of verwijderd, op een manier die UniGetUI niet kan controleren. Het gebruik van aangepaste opdrachtregels kan pakketten breken. Ga voorzichtig te werk.", "Custom command-line arguments:": "Aangepaste opdrachtregelopties:", "Custom install arguments:": "Aangepaste installatie-argumenten:", "Custom uninstall arguments:": "Aangepaste verwi<PERSON>derings-argumenten:", "Custom update arguments:": "Aangepaste bijwerkings-argumenten:", "Customize WingetUI - for hackers and advanced users only": "UniGetUI aanpassen - alleen voor avonturiers en gevorderde gebruikers", "DEBUG BUILD": "TESTVERSIE", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "CLAUSULE: W<PERSON><PERSON>IJ<PERSON> NIET AANSPRAKELIJK VOOR DE GEDOWNLOADE PAKKETTEN. VERZEKER U ERVAN DAT ALLEEN VERTROUWDE SOFTWARE WORDT GEINSTALLEERD.", "Dark": "<PERSON><PERSON>", "Decline": "Afwijzen", "Default": "Standaard", "Default installation options for {0} packages": "Standaard installatie-opties voor {0} pakketten", "Default preferences - suitable for regular users": "Standaard voorkeuren - geschikt voor doorsnee-gebruikers", "Default vcpkg triplet": "Standaard vcpk tripel", "Delete?": "Verwijderen?", "Dependencies:": "Afhankelijkheden:", "Descendant": "Nakomeling", "Description:": "Beschrijving:", "Desktop shortcut created": "Desktopsnelkoppeling aangemaakt", "Details of the report:": "Details van het rapport:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Softwareontwikkeling is zwaar en dit programma is gratis. Maar als je deze applicatie leuk vond, kun je mij altijd <b>een kopje koffie</b> doneren :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Direct installeren bij het dubbelklikken van een item op het tabblad \"{discoveryTab}\" (i.p.v. pakketinformatie weergeven)", "Disable new share API (port 7058)": "Nieuwe API voor Delen uitschakelen (poort 7058)", "Disable the 1-minute timeout for package-related operations": "Time-out van 1 minuut voor pakketgerelateerde bewerkingen uitschakelen", "Disclaimer": "Clausule", "Discover Packages": "Pakketten ontdekken", "Discover packages": "Pakketten ontdekken", "Distinguish between\nuppercase and lowercase": "Onderscheid maken tussen hoofdletters en kleine letters", "Distinguish between uppercase and lowercase": "Hoofdlettergevoelig", "Do NOT check for updates": "Controleer NIET op updates", "Do an interactive install for the selected packages": "Voer een interactieve installatie uit voor de geselecteerde pakketten", "Do an interactive uninstall for the selected packages": "Voer een interactieve verwijdering uit voor de geselecteerde pakketten", "Do an interactive update for the selected packages": "<PERSON><PERSON>r een interactieve update uit voor de geselecteerde pakketten", "Do not automatically install updates when the battery saver is on": "Niet automatisch updates installeren wanneer de batterijbeveiliging is ingeschakeld", "Do not automatically install updates when the network connection is metered": "Geen automatisch updates installeren wanneer bij betaalde netwerkverbinding", "Do not download new app translations from GitHub automatically": "Nieuwe applicatievertalingen niet automatisch van GitHub downloaden", "Do not ignore updates for this package anymore": "Updates voor dit pakket niet langer negeren", "Do not remove successful operations from the list automatically": "Geslaagde bewerkingen niet automatisch van de lijst wissen", "Do not show this dialog again for {0}": "Dit dialoogvenster niet meer weergeven voor {0}", "Do not update package indexes on launch": "Pakketindexen niet bijwerken bij de start", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Ga je ermee ak<PERSON> dat UniGetUI anonieme gebruiksstatistieken verzamelt en verzendt, met als enig doel inzicht te krijgen op de gebruikerservaring en deze te verbeteren?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Vind je UniGetUI nuttig? <PERSON><PERSON>t wil je mijn werk steunen, zodat ik door kan gaan met het maken van UniGetUI, de ultieme interface voor het beheer van pakketten.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Vind je UniGetUI nuttig en je wilt de ontwikkelaar steunen? In dat geval kun je {0}. Het helpt echt!", "Do you really want to reset this list? This action cannot be reverted.": "Wil je deze lijst echt opnieuw instellen? Deze actie kan niet worden teruggedraaid.", "Do you really want to uninstall the following {0} packages?": "Wil je de volgende {0} pakketten verwijderen?", "Do you really want to uninstall {0} packages?": "Wil je echt {0} pakketten verwijderen?", "Do you really want to uninstall {0}?": "Wil je {0} echt verwijderen?", "Do you want to restart your computer now?": "Wil je je computer nu opnieuw opstarten?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Wil je UniGetUI vertalen in jouw taal? <PERSON><PERSON> <a style=\"color: {0}\"  href=\"{1}\">HIER</a> hoe je een bijdrage kunt leveren!", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "<PERSON>n zin om te doneren? Maak je geen z<PERSON>gen, je kunt UniGetUI altijd delen met je vrienden. Vertel iedereen over UniGetUI.", "Donate": "<PERSON><PERSON>", "Done!": "<PERSON><PERSON><PERSON>!", "Download failed": "Download mislukt", "Download installer": "Installatieprogramma downloaden", "Download operations are not affected by this setting": "Download-handelingen worden niet beïnvloed door deze instelling", "Download selected installers": "Geselecteerde installatieprogramma's downloaden", "Download succeeded": "Download voltooid", "Download updated language files from GitHub automatically": "Bijgewerkte taalbestanden automatisch downloaden van GitHub", "Downloading": "Downloaden", "Downloading backup...": "Back-up downloaden...", "Downloading installer for {package}": "Installatieprogramma downloaden voor {package}", "Downloading package metadata...": "Pakket-metagegevens downloaden…", "Enable Scoop cleanup on launch": "Scoop opschonen inschakelen bij de start", "Enable WingetUI notifications": "Meldingen van UniGetUI inschakelen", "Enable an [experimental] improved WinGet troubleshooter": "[Experimentele] verbeterde WinGet-probleemoplosser inschakelen", "Enable and disable package managers, change default install options, etc.": "Pakketbeheerder in- of uitschakelen, standaardopties wijzigen, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Optimalisaties van CPU-gebruik op de achtergrond inschakelen (zie Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Achtergrond-API inschakelen (UniGetUI Widgets en Delen, poort 7058)", "Enable it to install packages from {pm}.": "<PERSON><PERSON><PERSON> het in om pakketten van {pm} te installeren.", "Enable the automatic WinGet troubleshooter": "Automatische WinGet-probleemoplosser inschakelen", "Enable the new UniGetUI-Branded UAC Elevator": "De nieuwe UniGetUI-Branded UAC-ophoger inschakelen", "Enable the new process input handler (StdIn automated closer)": "De nieuwe procesinvoerbehandelaar inschakelen (StdIn ge-automatiseerde sluiter)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "<PERSON><PERSON><PERSON> de onderstaande instellingen ENKEL EN ALLEEN in als je volledig begrijpt wat ze doen, en wat voor gevolgen en gevaren ze met zich meebrengen.", "Enable {pm}": "{pm} inschakelen", "Enter proxy URL here": "<PERSON><PERSON><PERSON> hier de proxy-URL in", "Entries that show in RED will be IMPORTED.": "RODE items worden GEÏMPORTEERD.", "Entries that show in YELLOW will be IGNORED.": "GELE items worden GENEGEERD.", "Error": "Fout", "Everything is up to date": "Alles is bijgewerkt", "Exact match": "Exacte overeenkomst", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Bestaande snelkoppelingen op je bureaublad worden gescand en je moet kiezen welke je wilt bewaren en welke je wilt verwijderen.", "Expand version": "Uitgebreide versie-informatie", "Experimental settings and developer options": "Experimentele instellingen en ontwikkelaarsopties", "Export": "Exporteren", "Export log as a file": "Logboek exporteren als bestand", "Export packages": "Pakketten exporteren", "Export selected packages to a file": "Geselecteerde pakketten exporteren naar bestand", "Export settings to a local file": "Instellingen exporteren naar een lokaal bestand", "Export to a file": "Exporteren naar bestand", "Failed": "Mislukt", "Fetching available backups...": "Beschikbare back-ups ophalen...", "Fetching latest announcements, please wait...": "Meest recente aankondigingen ophalen, even geduld…", "Filters": "Filters", "Finish": "<PERSON><PERSON>", "Follow system color scheme": "Volg systeem kleur schema", "Follow the default options when installing, upgrading or uninstalling this package": "Standaardopties aanhouden bij het installeren, upgraden of verwijderen van dit pakket", "For security reasons, changing the executable file is disabled by default": "Om veiligheidsredenen is het wijzigen van het uitvoeringsbestand standaard uitgeschakeld", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Om veiligheidsredenen zijn aangepaste opdrachtregelargumenten standaard uitgeschakeld. Ga naar UniGetUI veiligheidsinstellingen om dit te wijzigen.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Om veiligheidsredenen zijn pre-bewerking en post-bewerking scripts standaard uitgeschakeld. Ga naar UniGetUI veiligheidsinstellingen om dit te wijzigen.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Voor ARM gecompileerde versie van winget gebruiken (ALLEEN VOOR ARM64-SYSTEMEN)", "Formerly known as WingetUI": "V<PERSON><PERSON>n bekend als WingetUI", "Found": "Gevonden", "Found packages: ": "Pakketten gevonden:", "Found packages: {0}": "Pakketten gevonden: {0}", "Found packages: {0}, not finished yet...": "Pakketten gevonden: {0}, bijna k<PERSON>…", "General preferences": "Algemene voorkeuren", "GitHub profile": "GitHub-profiel", "Global": "Globaal", "Go to UniGetUI security settings": "Ga naar UniGetUI veiligheidsinstellingen", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Geweldige opslagplaats van onbekende maar nuttige hulpprogramma's en andere interessante pakketten.<br>Bevat: <b><PERSON><PERSON><PERSON>rogramma's, opdrachtregelprogramma's, algemene software (vereist extra bucket)</b>", "Great! You are on the latest version.": "Geweldig! Je zit op de meest recente versie.", "Grid": "<PERSON><PERSON>", "Help": "<PERSON><PERSON><PERSON> (Engels)", "Help and documentation": "Hulp en documentatie (Engels)", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Hier kun je het ged<PERSON> van UniGetUI met betrekking tot de volgende snelkoppelingen veranderen. Als je een snelkoppeling aanvinkt, wordt deze door UniGetUI verwijderd als deze bij een toekomstige upgrade wordt aangemaakt. Als je dit niet aanvinkt, blijft de snelkoppeling intact", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, mijn naam is <PERSON><PERSON> en ik ben de <i>ontwikkelaar</i> van UniGetUI. UniGetUI is geheel in mijn vrije tijd gemaakt!", "Hide details": "Details verbergen", "Homepage": "Website", "Hooray! No updates were found.": "Top! Alles is bijgewerkt!", "How should installations that require administrator privileges be treated?": "Hoe moeten installaties die administratorrechten vereisen, worden behandeld?", "How to add packages to a bundle": "<PERSON><PERSON><PERSON> toevoegen aan een bundel", "I understand": "<PERSON>k begri<PERSON>p het", "Icons": "Pictogrammen", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Als je cloud back-up hebt ingeschakeld, wordt deze opgeslagen als een GitHub Gist op dit account", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Aangepaste pre-installatie en post-installatie opdrachten negeren bij het importeren van pakketten van een bundel", "Ignore future updates for this package": "Toekomstige updates voor dit pakket negeren", "Ignore packages from {pm} when showing a notification about updates": "<PERSON><PERSON><PERSON> van {pm} negeren bij het tonen van een melding over updates", "Ignore selected packages": "Geselecteerde pakketten negeren", "Ignore special characters": "Speciale tekens negeren", "Ignore updates for the selected packages": "Updates voor de geselecteerde pakketten negeren", "Ignore updates for this package": "Updates voor dit pakket negeren", "Ignored updates": "Genegeerde updates", "Ignored version": "<PERSON><PERSON><PERSON> versie", "Import": "Importeren", "Import packages": "Pakketten importeren", "Import packages from a file": "<PERSON><PERSON><PERSON> uit een bestand importeren", "Import settings from a local file": "Instellingen importeren vanuit een lokaal bestand", "In order to add packages to a bundle, you will need to: ": "Om pakketten aan een bundel toe te voegen, moet je:", "Initializing WingetUI...": "UniGetUI initialiseren…", "Install": "Installeren", "Install Scoop": "Scoop installeren", "Install and more": "Installeren en meer", "Install and update preferences": "Voorkeuren voor installeren en bijwerken", "Install as administrator": "Als administrator install<PERSON><PERSON>", "Install available updates automatically": "Beschikbare updates automatisch installeren", "Install location can't be changed for {0} packages": "Installatielocatie kan voor {0} pakketten niet worden gewijzigd", "Install location:": "Installatielocatie:", "Install options": "Installatie-opties", "Install packages from a file": "<PERSON><PERSON><PERSON> installeren vanuit een bestand", "Install prerelease versions of UniGetUI": "Prerelease-vers<PERSON> van UniGetUI installeren", "Install script": "installatiescript", "Install selected packages": "Geselecteerde pakketten installeren", "Install selected packages with administrator privileges": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON>", "Install selection": "<PERSON><PERSON>", "Install the latest prerelease version": "Installeer de meest recente prerelease-versie", "Install updates automatically": "Updates automatisch installeren", "Install {0}": "{0} installeren", "Installation canceled by the user!": "Installatie door gebruiker geannu<PERSON>!", "Installation failed": "Installatie mislukt", "Installation options": "Installatieopties", "Installation scope:": "Installatiebereik:", "Installation succeeded": "Installatie voltooid", "Installed Packages": "<PERSON><PERSON><PERSON> op dit systeem", "Installed Version": "Geïnstalleerde versie", "Installed packages": "Geïnstalleerde pakketten", "Installer SHA256": "Installatieprogramma-SHA256", "Installer SHA512": "Installatieprogramma-SHA512", "Installer Type": "Type installatieprogramma", "Installer URL": "Installatieprogramma URL", "Installer not available": "Installatieprogramma niet be<PERSON>", "Instance {0} responded, quitting...": "Instantie {0} heeft gere<PERSON>, afsluiten…", "Instant search": "Direct zoeken", "Integrity checks can be disabled from the Experimental Settings": "Integriteitscontroles kunnen worden uitgeschakeld via de experimentele instellingen", "Integrity checks skipped": "Integriteitscontroles overgeslagen", "Integrity checks will not be performed during this operation": "Tijdens deze bewerking worden geen integriteitscontroles uitgevoerd", "Interactive installation": "Interactieve installatie", "Interactive operation": "Interactieve bewerking", "Interactive uninstall": "Interactief verwijderen", "Interactive update": "Interactief bijwerken", "Internet connection settings": "Instellingen voor internetverbinding", "Is this package missing the icon?": "Ontbreekt bij dit pakket het pictogram?", "Is your language missing or incomplete?": "Ontbre<PERSON>t jouw taal of is deze onjuist of incompleet?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Er is geen garantie dat de verstrekte inloggegevens veilig worden opgeslagen, dus de inloggegevens van je bankrekening moest je maar niet gebruiken", "It is recommended to restart UniGetUI after WinGet has been repaired": "<PERSON><PERSON> is raad<PERSON><PERSON> om UniGetUI opnieuw op te starten nadat WinGet is gerepareerd", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Het wordt sterk aanbevolen om UniGetUI opnieuw te installeren om de situatie aan te pakken.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Het lijkt erop dat WinGet niet goed functioneert. Wil je proberen dit te repareren?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Het lijkt erop dat je UniGetUI als administrator hebt uitgevoerd, wat niet wordt aanbevolen. Je kunt het programma nog steeds geb<PERSON>ike<PERSON>, maar we raden je ten zeerste aan UniGetUI niet uit te voeren met administratorrechten. Klik op \"{showDetails}\" om te zien waarom.", "Language": "Taal", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON>, thema en andere diverse voorkeuren", "Last updated:": "Laatst bijgewerkt:", "Latest": "Meest recent", "Latest Version": "Meest recente versie", "Latest Version:": "Meest recente versie:", "Latest details...": "Meest recente details…", "Launching subprocess...": "Onderliggend proces starten…", "Leave empty for default": "<PERSON><PERSON> laten voor standaard", "License": "Licentie", "Licenses": "Licenties", "Light": "Licht", "List": "Lijst", "Live command-line output": "Live opdrachtregeluitvoer", "Live output": "Live output", "Loading UI components...": "UI-componenten laden…", "Loading WingetUI...": "UniGetUI laden…", "Loading packages": "<PERSON><PERSON><PERSON> laden", "Loading packages, please wait...": "Pak<PERSON>ten laden, even geduld…", "Loading...": "<PERSON>den…", "Local": "Lokaal", "Local PC": "Lokale PC", "Local backup advanced options": "Lokale back-up geavanceerde opties", "Local machine": "Lokale machine", "Local package backup": "Lokale pakket back-up", "Locating {pm}...": "{pm} lokaliseren…", "Log in": "Inloggen", "Log in failed: ": "Inloggen mislukt:", "Log in to enable cloud backup": "Log in om cloud back-up in te schakelen", "Log in with GitHub": "Log in met GitHub", "Log in with GitHub to enable cloud package backup.": "Log in bij GitHub om cloud pakket back-ups in te schakelen.", "Log level:": "Logboek-niveau", "Log out": "Uitloggen", "Log out failed: ": "Uitloggen mislukt:", "Log out from GitHub": "Uitloggen van GitHub", "Looking for packages...": "<PERSON><PERSON><PERSON> zoeken…", "Machine | Global": "Machine | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Misvormde opdrachtregelargumenten kunnen pakketten breken of zelfs een kwaadwillende actor in staat stellen een bevoorrechte uitvoering te krijgen. Daarom is het importeren van aangepaste opdrachtregelargumenten standaard uitgeschakeld.", "Manage": "<PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "UniGetUI-instellingen beheren", "Manage WingetUI autostart behaviour from the Settings app": "Het autostart-gedrag van UniGetUI beheren via de app-instellingen", "Manage ignored packages": "<PERSON><PERSON><PERSON> pakketten beheren", "Manage ignored updates": "Genegeerde updates beheren", "Manage shortcuts": "Snelkoppelingen beheren", "Manage telemetry settings": "Telemetrie-instellingen beheren", "Manage {0} sources": "{0}-bronnen beheren", "Manifest": "Manifest", "Manifests": "Manifesten", "Manual scan": "<PERSON><PERSON><PERSON> scannen", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Officiële pakketbeheerder van Microsoft. Vol met bekende en geverifieerde pakketten <br>Bevat: <b> Algemene software, Microsoft Store-apps</b>", "Missing dependency": "Ontbrekende afhankelijkheid", "More": "<PERSON><PERSON>", "More details": "Meer details", "More details about the shared data and how it will be processed": "Meer informatie over de gedeelde gegevens en hoe deze worden verwerkt", "More info": "Meer informatie", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "OPMERKING: <PERSON><PERSON> probleemoplosser kan worden uitgeschakeld via UniGetUI instellingen - Pakketbeheerders - WinGet", "Name": "<PERSON><PERSON>", "New": "<PERSON><PERSON><PERSON>", "New Version": "Nieuwe versie", "New bundle": "Nieuwe bundel", "New version": "Nieuwe versie", "Nice! Backups will be uploaded to a private gist on your account": "Mooi! Back-ups zullen als een privé Gist op je account worden geüpload", "No": "<PERSON><PERSON>", "No applicable installer was found for the package {0}": "<PERSON><PERSON> installatieprogramma gevonden voor het pakket {0}", "No dependencies specified": "<PERSON><PERSON>ijkheden gespecificeerd", "No new shortcuts were found during the scan.": "Tij<PERSON>s de scan zijn geen nieuwe snelkoppelingen gevonden.", "No packages found": "<PERSON>n pakketten gevonden", "No packages found matching the input criteria": "Er zijn geen pakketten gevonden die voldoen aan de opgegeven criteria", "No packages have been added yet": "<PERSON>r zijn nog geen pakketten toegevoegd", "No packages selected": "<PERSON><PERSON> pakketten geselecteerd", "No packages were found": "<PERSON>r zijn geen pakketten gevonden", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Er wordt geen persoonlijke informatie verzameld of verzonden en de verzamelde gegevens worden geanonimiseerd, zodat deze niet naar jou kunnen worden teruggevoerd.", "No results were found matching the input criteria": "<PERSON>r zijn geen resultaten gevonden die voldoen aan de invoercriteria", "No sources found": "<PERSON>n bronnen gevonden", "No sources were found": "<PERSON>r zijn geen bronnen gevonden", "No updates are available": "<PERSON>r zijn geen <PERSON> besch<PERSON>", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS pakketbeheer. Vol met bibliotheken en andere hulpprogramma's die rond de javascript-wereld draaien <br>Bevat: <b>Node javascript-bibliotheken en andere gerelateerde hulpprogramma's</b>", "Not available": "<PERSON><PERSON>", "Not finding the file you are looking for? Make sure it has been added to path.": "Kun j het bestand dat je zoekt niet vinden? Zorg ervoor dat het aan pad is toegevoegd.", "Not found": "<PERSON><PERSON>", "Not right now": "Nu even niet", "Notes:": "Opmerkingen:", "Notification preferences": "Meldingsvoorkeuren", "Notification tray options": "Opties voor het systeemvak", "Notification types": "Typen meldingen", "NuPkg (zipped manifest)": "NuPkg (zipped manifest)", "OK": "OK", "Ok": "OK", "Open": "Openen", "Open GitHub": "GitHub openen", "Open UniGetUI": "UniGetUI openen", "Open UniGetUI security settings": "UniGetUI veiligheidsinstellingen openen", "Open WingetUI": "UniGetUI openen", "Open backup location": "De map met back-upbestanden openen", "Open existing bundle": "Bestaande bundel openen", "Open install location": "Installatiemap openen", "Open the welcome wizard": "Welkomstdialoog openen", "Operation canceled by user": "Bewerking geannuleerd door gebruiker", "Operation cancelled": "Bewerking afgebroken", "Operation history": "Bewerkingsgeschiedenis", "Operation in progress": "Bewerking aan de gang", "Operation on queue (position {0})...": "Bewerking in de wachtrij (positie {0})", "Operation profile:": "Bewerkingsprofiel:", "Options saved": "Opties opgeslagen", "Order by:": "Sortering:", "Other": "<PERSON><PERSON>", "Other settings": "Overige instellingen", "Package": "Pakket", "Package Bundles": "Pakkettenbundels", "Package ID": "Pakket-ID", "Package Manager": "Pakketbeheerder", "Package Manager logs": "Pakketbeheerder-logboeken", "Package Managers": "Pakketbeheerders", "Package Name": "<PERSON><PERSON><PERSON><PERSON>", "Package backup": "Back-up van pakket", "Package backup settings": "Back-up instelling<PERSON> van het pakket", "Package bundle": "Pakkettenbundel", "Package details": "Pakketdetails", "Package lists": "Pakketlijsten", "Package management made easy": "Pakketbeheer een<PERSON><PERSON>g gema<PERSON>t", "Package manager": "Pakketbeheerder", "Package manager preferences": "Voorkeuren voor Pakketbeheerders", "Package managers": "Pakketbeheerders", "Package not found": "Pakket niet gevonden", "Package operation preferences": "Voorkeuren voor pakketbewerkingen", "Package update preferences": "Voorkeuren voor pakketupdates", "Package {name} from {manager}": "Pak<PERSON> {name} van {manager}", "Package's default": "<PERSON><PERSON><PERSON> van het pakket", "Packages": "Pak<PERSON><PERSON>", "Packages found: {0}": "Pakketen gevonden: {0}", "Partially": "Gedeeltelijk", "Password": "Wachtwoord", "Paste a valid URL to the database": "Plak een geldige URL in de database", "Pause updates for": "Updates pauzeren voor", "Perform a backup now": "<PERSON>u een back-up uit<PERSON><PERSON>n", "Perform a cloud backup now": "Nu een cloud back-up uitvoeren", "Perform a local backup now": "Nu een lokale back-up uitvoeren", "Perform integrity checks at startup": "Integriteitscontroles uitvoeren bij het opstarten", "Performing backup, please wait...": "Back-up uitvoeren, even geduld…", "Periodically perform a backup of the installed packages": "Regelmatig een back-up u<PERSON><PERSON><PERSON><PERSON> van de geïnstalleerde pakketten", "Periodically perform a cloud backup of the installed packages": "Regelmatig een cloud back-up uit<PERSON><PERSON><PERSON> van de geïnstalleerde pakketten", "Periodically perform a local backup of the installed packages": "Regelmatig een lokale back-up uit<PERSON><PERSON><PERSON> van de geïnstalleerde pakketten", "Please check the installation options for this package and try again": "Controleer de installatie-opties voor dit pakket en probeer het opnieuw", "Please click on \"Continue\" to continue": "Klik op \"Doorgaan\" om door te gaan", "Please enter at least 3 characters": "Voer tenminste 3 tekens in", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Houd er rekening mee dat bepaalde pakketten mogelijk niet kunnen worden geïnstalleerd vanwege de pakketbeheerders die op deze machine zijn ingeschakeld.", "Please note that not all package managers may fully support this feature": "Houd er rekening mee dat niet alle pakketbeheerders deze functie volledig ondersteunen", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Houd er rekening mee dat pakketten uit bepaalde bronnen mogelijk niet kunnen worden geëxporteerd. Deze zijn weergegeven in grijs en worden overgeslagen.", "Please run UniGetUI as a regular user and try again.": "Voer UniGetUI uit als gewone gebruiker en probeer het opnieuw.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Raadpleeg de opdrachtregeluitvoer of raadpleeg de Bewerkingsgeschiedenis voor meer informatie over het probleem.", "Please select how you want to configure WingetUI": "Selecteer hoe je UniGetUI wilt configureren", "Please try again later": "<PERSON><PERSON><PERSON> het later nog eens", "Please type at least two characters": "<PERSON>p tenminste twee tekens", "Please wait": "Even geduld", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Even geduld terwijl {0} wordt geïnstalleerd. Er kan een zwart venster verschijnen. Wacht tot het sluit.", "Please wait...": "Even geduld…", "Portable": "Portable", "Portable mode": "Portable modus", "Post-install command:": "Post-installatie opdracht:", "Post-uninstall command:": "Post-verwijderings opdracht:", "Post-update command:": "Post-update opdracht:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "De pakketbeheerder van PowerShell. Bibliotheken en scripts zoeken om de PowerShell-mogelijkheden uit te breiden<br>Bevat: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Opdrachten vóór en na de installatie kunnen zeer vervelende dingen doen met je apparaat, als ze daarvoor zijn ontworpen. Het kan erg gevaarlijk zijn om de opdrachten uit een bundel te importeren, tenzij je de bron van die pakketbundel vertrouwt", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Installatieopdrachten worden uitgevoerd vóór of nadat een pakket wordt geïnstalleerd, ge<PERSON><PERSON><PERSON> of verwijderd. Houd er rekening mee dat ze dingen kunnen breken, tenzij ze zorgvuldig worden gebruikt", "Pre-install command:": "Pre-installatie opdracht:", "Pre-uninstall command:": "Pre-verwijderings opdracht:", "Pre-update command:": "Pre-update opdracht:", "PreRelease": "Pre-release", "Preparing packages, please wait...": "Pakketten voorbereiden, even geduld…", "Proceed at your own risk.": "Doorgaan op eigen risico.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "<PERSON><PERSON> vorm van rechtenverheffing met UniGetUI Elevator of GSudo verbieden", "Proxy URL": "Proxy-url", "Proxy compatibility table": "Proxy compatibiliteitstabel", "Proxy settings": "Proxy-instellingen", "Proxy settings, etc.": "Proxy-instellingen, enz.", "Publication date:": "Uitgiftedatum:", "Publisher": "Uitgever", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python bibliotheekbeheer. Vol met python-bibliotheken en andere python-gerelateerde hulpprogramma's <br>Bevat: <b>Python-bibliotheken en gerelateerde hulpprogramma's</b>", "Quit": "Afsluiten", "Quit WingetUI": "UniGetUI afsluiten", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "UAC-prompts ve<PERSON><PERSON><PERSON>, installaties standaard ver<PERSON><PERSON>n, bepaalde risicovolle functies ontgrendelen, etc.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Raadpleeg de UniGetUI logboeken voor meer details met betrekking tot de getroffen bestand(en)", "Reinstall": "Opnieuw installeren", "Reinstall package": "Pakket opnieuw installeren", "Related settings": "Gerelateerde instellingen", "Release notes": "Release-opmerkingen", "Release notes URL": "Release-opmerkingen URL", "Release notes URL:": "Release-opmerkingen URL:", "Release notes:": "Release-opmerkingen:", "Reload": "Opnieuw laden", "Reload log": "Logboek opnieuw laden", "Removal failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> mislukt", "Removal succeeded": "Verwijdering voltooid", "Remove from list": "<PERSON> lijst wissen", "Remove permanent data": "Permanente gegevens verwijderen", "Remove selection from bundle": "<PERSON>ie verwijderen van bundel", "Remove successful installs/uninstalls/updates from the installation list": "Geslaagde installaties/verwijderingen/updates van de installatielijst wissen", "Removing source {source}": "<PERSON>ron verwijderen {source}", "Removing source {source} from {manager}": "Bron {source} verwijderen uit {manager}", "Repair UniGetUI": "UniGetUI repareren", "Repair WinGet": "Win<PERSON>et repareren", "Report an issue or submit a feature request": "<PERSON>en probleem melden of een functieverzoek indienen", "Repository": "Opslagplaats", "Reset": "Opnieuw instellen", "Reset Scoop's global app cache": "<PERSON><PERSON>'s algemene buffer wissen", "Reset UniGetUI": "UniGetUI opnieuw instellen", "Reset WinGet": "WinGet opnieuw instellen", "Reset Winget sources (might help if no packages are listed)": "Winget-bronnen opnieuw instellen (kan helpen als er geen pakketten worden vermeld)", "Reset WingetUI": "UniGetUI opnieuw instellen", "Reset WingetUI and its preferences": "UniGetUI en voorkeuren opnieuw instellen", "Reset WingetUI icon and screenshot cache": "UniGetUI pictogrammen- en schermopname-buffer wissen", "Reset list": "Lijst opnieuw instellen", "Resetting Winget sources - WingetUI": "Winget-bronnen opnieuw instellen - UniGetUI", "Restart": "Opnieuw starten", "Restart UniGetUI": "UniGetUI opnieuw starten", "Restart WingetUI": "UniGetUI opnieuw starten", "Restart WingetUI to fully apply changes": "Start UniGetUI opnieuw om wijzigingen volledig toe te passen", "Restart later": "Later opnieuw starten", "Restart now": "Nu opnieuw starten", "Restart required": "Opnieuw starten vereist", "Restart your PC to finish installation": "Start je PC opnieuw op om de installatie te voltooien", "Restart your computer to finish the installation": "Start de computer opnieuw op om de installatie te voltooien", "Restore a backup from the cloud": "<PERSON><PERSON><PERSON> een back-up van de cloud", "Restrictions on package managers": "Beperkingen op pakketbeheerders", "Restrictions on package operations": "Beperkingen op pakketbewerkingen", "Restrictions when importing package bundles": "Beperkingen bij het importeren van pakketbundels", "Retry": "Opnieuw proberen", "Retry as administrator": "Opnieuw proberen als administrator", "Retry failed operations": "Mislukte bewerkingen opnieuw proberen", "Retry interactively": "Interactief opnieuw proberen", "Retry skipping integrity checks": "Opnieuw proberen zonder integriteitscontroles", "Retrying, please wait...": "Opnieuw proberen, even geduld…", "Return to top": "<PERSON>ar boven", "Run": "Uitvoeren", "Run as admin": "Als administrator u<PERSON><PERSON><PERSON><PERSON>", "Run cleanup and clear cache": "Opruiming uitvoeren en cache wissen", "Run last": "Laatste uitvoeren", "Run next": "Volgende uitvoeren", "Run now": "<PERSON><PERSON> u<PERSON>n", "Running the installer...": "Het installatieprogramma uitvoeren…", "Running the uninstaller...": "Het verwijderingsprogramma uitvoeren…", "Running the updater...": "De update uitvoeren…", "Save": "Opsla<PERSON>", "Save File": "<PERSON><PERSON> op<PERSON>an", "Save and close": "Opslaan en sluiten", "Save as": "<PERSON><PERSON><PERSON> als", "Save bundle as": "Bundel opslaan als", "Save now": "<PERSON><PERSON>", "Saving packages, please wait...": "<PERSON><PERSON><PERSON> opslaan, even geduld…", "Scoop Installer - WingetUI": "Scoop installatie - UniGetUI", "Scoop Uninstaller - WingetUI": "Scoop verwijderen - UniGetUI", "Scoop package": "Scoop-pakket", "Search": "<PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Zoeken naar desktopsoftware, wa<PERSON><PERSON>w mij wanneer updates beschikba<PERSON> zijn en doe geen bijzondere dingen. Ik wil niet dat UniGetUI te ingewikkeld wordt, ik wil gewoon een eenvoudige <b>softwarewinkel</b>", "Search for packages": "Pakketten zoeken", "Search for packages to start": "Zoek om te beginnen naar pakketten", "Search mode": "<PERSON><PERSON><PERSON><PERSON>", "Search on available updates": "Beschikbare updates zoeken", "Search on your software": "Jouw software doorzoeken", "Searching for installed packages...": "Zoeken naar geïnstalleerde pakketten…", "Searching for packages...": "Zoeken naar pakketten…", "Searching for updates...": "<PERSON><PERSON> naar updates…", "Select": "Selecteren", "Select \"{item}\" to add your custom bucket": "Selecteer \"{item}\" om jouw aangepaste bucket toe te voegen", "Select a folder": "Een map selecteren", "Select all": "Alles selecteren", "Select all packages": "Alle pakketten selecteren", "Select backup": "Back-up selecteren", "Select only <b>if you know what you are doing</b>.": "Selecteer alleen <b>als je weet wat je doet</b>.", "Select package file": "Pakketbestand selecteren", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Selecteer welke back-up je wilt openen. Later kun je ook bepalen welke pakketten/programma's je wilt herstellen.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Selecteer welke programma's gesloten moeten worden voordat dit pakket wordt geïnstalleerd, ge<PERSON><PERSON><PERSON><PERSON> of verwijderd.", "Select the source you want to add:": "Selecteer de bron die je wilt toevoegen:", "Select upgradable packages by default": "Standaard upgradebare pakketten selecteren", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Selecteer welke <b>pakketbeheerders</b> ({0}) moeten geb<PERSON>iken, configureer hoe pakketten worden geïnstalleerd, beheer hoe administratorrechten worden afgehandeld, enz.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Handshake verzonden. Wachten op antwoord van de instance listener… ({0}%)", "Set a custom backup file name": "Een aangepaste naam voor een back-upbestand instellen", "Set custom backup file name": "Aangepaste naam van het back-upbestand instellen", "Settings": "Instellingen", "Share": "<PERSON><PERSON>", "Share WingetUI": "UniGetUI delen", "Share anonymous usage data": "Anonieme gebruiksgegevens delen", "Share this package": "Dit pakket delen", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Als je de veiligheidsinstellingen wijzigt, zul je de bundel opnieuw moeten openen om de wijzigingen door te voeren.", "Show UniGetUI on the system tray": "UniGetUI in het systeemvak weergeven", "Show UniGetUI's version and build number on the titlebar.": "UniGetUI's versie en bouwnummer in de titelbalk weergeven.", "Show WingetUI": "UniGetUI openen", "Show a notification when an installation fails": "Toon een melding wanneer een installatie mislukt", "Show a notification when an installation finishes successfully": "Toon een melding wanneer een installatie met succes wordt voltooid", "Show a notification when an operation fails": "Toon een melding wanneer een bewerking mislukt", "Show a notification when an operation finishes successfully": "Toon een melding wanneer een bewerking met succes is voltooid", "Show a notification when there are available updates": "Toon een melding wanneer updates beschik<PERSON><PERSON> zijn", "Show a silent notification when an operation is running": "Toon een stille melding wanneer een bewerking wordt uitgevoerd", "Show details": "Details weergeven", "Show in explorer": "In Verkenner weergeven", "Show info about the package on the Updates tab": "Informatie over de pakketten weergeven in het tabblad Software-updates", "Show missing translation strings": "Ontbrekende vertalingen weergeven", "Show notifications on different events": "Meldingen tonen over verschillende gebeurtenissen", "Show package details": "Pakketdetails weergeven", "Show package icons on package lists": "Pakketpictogrammen weergeven in pakketlijsten", "Show similar packages": "Vergelijkbaar pakket tonen", "Show the live output": "Live output weergeven", "Size": "Grootte", "Skip": "Overslaan", "Skip hash check": "Hashcontrole overslaan", "Skip hash checks": "Hashcon<PERSON><PERSON> overs<PERSON>an", "Skip integrity checks": "Integriteitscontroles overslaan", "Skip minor updates for this package": "Kleine updates voor dit pakket overslaan", "Skip the hash check when installing the selected packages": "Hashcontrole overslaan bij het installeren van de geselecteerde pakketten", "Skip the hash check when updating the selected packages": "Hashcontrole overslaan bij het bijwerken van de geselecteerde pakketten", "Skip this version": "<PERSON><PERSON> versie <PERSON>an", "Software Updates": "Software-updates", "Something went wrong": "Er gings iets mis", "Something went wrong while launching the updater.": "<PERSON>r ging iets mis bij het starten van de updater.", "Source": "<PERSON><PERSON>", "Source URL:": "Bron-URL:", "Source added successfully": "<PERSON><PERSON> met succ<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Source addition failed": "<PERSON><PERSON><PERSON><PERSON> van bron mislukt", "Source name:": "<PERSON><PERSON>:", "Source removal failed": "Verwij<PERSON><PERSON> van bron mislukt", "Source removed successfully": "<PERSON><PERSON> met succ<PERSON> ve<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source:": "Bron:", "Sources": "B<PERSON>nen", "Start": "Starten", "Starting daemons...": "Daemons starten…", "Starting operation...": "Bewerking starten...", "Startup options": "Start-opties", "Status": "Status", "Stuck here? Skip initialization": "Vastgelopen? <PERSON><PERSON><PERSON>", "Success!": "Succes!", "Suport the developer": "Steun de ontwikkelaar", "Support me": "Steun mij", "Support the developer": "Steun de ontwikkelaar", "Systems are now ready to go!": "All<PERSON> is klaar voor de start!", "Telemetry": "Telemetrie", "Text": "Tekst", "Text file": "Tekstbestand", "Thank you ❤": "Dank<PERSON><PERSON><PERSON> ❤", "Thank you 😉": "Dank<PERSON><PERSON><PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "De Rust-pakketbeheerder.<br>Bevat: <b>Rust-bibliotheken en programma's geschreven in Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "De back-up bevat GEEN binair bestand en ook geen opgeslagen gegevens van een programma.", "The backup will be performed after login.": "De back-up wordt uitgevoerd na het inloggen.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "De back-up bevat de volledige lijst met geïnstalleerde pakketten en hun installatieopties. Ook genegeerde updates en overgeslagen versies worden opgeslagen.", "The bundle was created successfully on {0}": "De bundel is met succes gemaakt op {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "De bundel die je probeert te laden, lijkt ongeldig. Controleer het bestand en probeer het opnieuw.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Het controlegetal van het installatieprogramma komt niet overeen met de verwachte waarde en de authenticiteit van het installatieprogramma kan niet worden geverifieerd. Als je de uitgever vertrouwt, {0} het pakket opnieuw zonder hashcontrole.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "De klassieke Windows pakketbeheerder. Hier kun je van alles vinden. <br>Bevat: <b>Algemene software</b>", "The cloud backup completed successfully.": "De cloud back-up is succesvol voltooid.", "The cloud backup has been loaded successfully.": "De cloud back-up is succesvol geladen.", "The current bundle has no packages. Add some packages to get started": "De huidige bundel heeft geen pakketten. Voeg wat pakketten toe om te beginnen", "The executable file for {0} was not found": "Het uit<PERSON>erbare bestand voor {0} is niet gevonden", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "De volgende opties zullen standaard worden toegepast wanneer een {0} pakket wordt geïnstalleerd, geüp<PERSON>t of verwijderd.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "De volgende pakketten worden geëxporteerd naar een JSON-bestand. Er worden geen gebruikersgegevens of binaire bestanden opgeslagen.", "The following packages are going to be installed on your system.": "De volgende pakketten worden op jouw systeem geïnstalleerd.", "The following settings may pose a security risk, hence they are disabled by default.": "De volgende instellingen kunnen een veiligheidsrisico met zich meebrengen. Hierom zijn ze standaard uitgeschakeld.", "The following settings will be applied each time this package is installed, updated or removed.": "De volgende instellingen worden toegepast telkens wanneer dit pakket wordt geïnstalleerd, bijgewerkt of verwijderd.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "<PERSON><PERSON> keer dat dit pakket wordt geïnstalleerd, bijgewerkt of verwijderd, worden de volgende instellingen toegepast. Ze worden automatisch opgeslagen.", "The icons and screenshots are maintained by users like you!": "De pictogrammen en screenshots worden onderhouden door gebruikers zoals jij!", "The installation script saved to {0}": "Het installatiescript is opgeslagen op {0}", "The installer authenticity could not be verified.": "De authenticiteit van het installatieprogramma kon niet worden geverifieerd.", "The installer has an invalid checksum": "Het installatieprogramma bevat een ongeldig controlegetal", "The installer hash does not match the expected value.": "Het controlegetal van het installatieprogramma komt niet overeen met de verwachte waarde.", "The local icon cache currently takes {0} MB": "Het lokale pictogram-buffer gebruikt momenteel {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Het hoof<PERSON><PERSON><PERSON> van dit project is het cre<PERSON><PERSON> van een intuïtieve gebruikersinterface voor het beheer van de meest voorkomende CLI package managers voor Windows, zoals Winget en Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Het pakket \"{0}\" is niet aanget<PERSON>ffen in pakketbeheerder \"{1}\"", "The package bundle could not be created due to an error.": "De pakketbundel kan vanwege een fout niet worden angemaakt.", "The package bundle is not valid": "De pakketbundel is niet geldig", "The package manager \"{0}\" is disabled": "Pakketbeheerder \"{0}\" is uitgeschakeld", "The package manager \"{0}\" was not found": "De pakketbeheerder \"{0}\" is niet aang<PERSON><PERSON><PERSON>n", "The package {0} from {1} was not found.": "Het pakket {0} van {1} is niet gevonden.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "De hier vermelde pakketten worden niet in aanmerking genomen bij het controleren op updates. Dubbelklik erop of klik op de knop aan de rechterkant om te stoppen met het negeren van hun updates.", "The selected packages have been blacklisted": "De geselecteerde pakketten zijn op de zwarte lijst gezet", "The settings will list, in their descriptions, the potential security issues they may have.": "De instellingen zullen in hun omschrijving mogelijke veiligheidsproblemen weergeven.", "The size of the backup is estimated to be less than 1MB.": "<PERSON> g<PERSON><PERSON> van de back-up wordt geschat op minder dan 1 MB", "The source {source} was added to {manager} successfully": "De bron {source} is  met succes toege<PERSON><PERSON>d aan {manager}", "The source {source} was removed from {manager} successfully": "<PERSON> <PERSON>on {source} is met succes ver<PERSON><PERSON><PERSON><PERSON> van {manager}", "The system tray icon must be enabled in order for notifications to work": "Het systeemvakpictogram moet ingeschakeld zijn om meldingen te laten werken", "The update process has been aborted.": "Het updatep<PERSON>ces is afgebroken.", "The update process will start after closing UniGetUI": "Het updateproces start na het sluiten van UniGetUI", "The update will be installed upon closing WingetUI": "De update wordt geïnstalleerd bij het sluiten van UniGetUI", "The update will not continue.": "De update wordt niet uitgevoerd.", "The user has canceled {0}, that was a requirement for {1} to be run": "De gebruiker heeft {0} g<PERSON><PERSON><PERSON><PERSON>, dat was een vereiste voor het uitvoeren van {1}", "There are no new UniGetUI versions to be installed": "Er is geen nieuwe UniGetUI-versie om te installeren", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Er zijn lopende bewerkingen. Als je UniGetUI afsluit, kunnen deze mislukken. Wil je doorgaan?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Er zijn een aantal geweldige video's op YouTube die UniGetUI en zijn mogelijkheden laten zien. <PERSON><PERSON> le<PERSON><PERSON> met nuttige trucs en tips!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Er zijn twee hoofdredenen om UniGetUI niet als administrator uit te voeren: <PERSON> <PERSON><PERSON><PERSON> is dat de Scoop package manager problemen kan ve<PERSON><PERSON><PERSON><PERSON> met sommige commando's wanne<PERSON> deze met administratorrechten worden uitgevoerd. De tweede is dat het uitvoeren van UniGetUI als administrator betekent dat elk pakket dat je downloadt, wordt uitgevoerd als administrator (en dit is niet veilig). Onthoud dat als je een specifiek pakket als administrator moet installeren, je altij<PERSON> met de rechtermuisknop op het item kunt klikken ->  Als administrator installeren/bijwerken/verwijderen.", "There is an error with the configuration of the package manager \"{0}\"": "Er is een fout opgetreden bij de configuratie van pakketbeheerder \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Er wordt gewerkt aan een installatie. Als je UniGetUI sluit, kan de installatie mislukken met mogelijk onverwachte resultaten. Wil je UniGetUI nog steeds stoppen?", "They are the programs in charge of installing, updating and removing packages.": "Het zijn de programma's die verantwoordelijk zijn voor het installeren, bijwerken en verwijderen van pakketten.", "Third-party licenses": "Li<PERSON><PERSON> van <PERSON>", "This could represent a <b>security risk</b>.": "Dit kan een <b>veiligheidsrisico</b> vertegenwoordigen.", "This is not recommended.": "Dit wordt afgeraden.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Dit komt waarschijnlijk doordat het pakket dat u was toegezonden, is verwijderd of gepubliceerd is in een pakketbeheerder die u niet hebt ingeschakeld. De ontvangen ID is {0}", "This is the <b>default choice</b>.": "Dit is de <b>standaard<PERSON><PERSON></b>.", "This may help if WinGet packages are not shown": "Dit kan helpen als WinGet-pakketten niet worden weergegeven", "This may help if no packages are listed": "Dit kan helpen als er geen pakketten worden vermeld", "This may take a minute or two": "<PERSON><PERSON> kan 1-2 minuten duren", "This operation is running interactively.": "Deze bewerking wordt interactief uitgevoerd.", "This operation is running with administrator privileges.": "Deze bewerking wordt uitgevoerd met <PERSON><PERSON><PERSON>.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Deze optie ZAL problemen veroorzaken. Elke handeling die zichzelf niet kan verheffen ZAL MISLUKKEN. Installeren/bijwerken/verwijderen als administrator zal NIET WERKEN.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Deze pakketbundel had een aantal instellingen die mogelijk gevaarlijk zijn, en worden mogelijk standaard genegeerd.", "This package can be updated": "Dit pakket kan worden bijgewerkt", "This package can be updated to version {0}": "Dit pakket kan worden bijgewerkt naar versie {0}", "This package can be upgraded to version {0}": "Dit pakket kan worden bijgewerkt naar versie {0}", "This package cannot be installed from an elevated context.": "Dit pakket kan niet vanuit een verhoogde context worden geïnstalleerd.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Heeft dit pakket geen schermopnames of pictogram? Draag bij aan UniGetUI door ontbrekende pictogrammen en schermopnames toe te voegen aan onze openbare database.", "This package is already installed": "Dit pakket is al geïnstalleerd", "This package is being processed": "Dit pakket wordt verwerkt", "This package is not available": "Dit pakket is niet be<PERSON>ar", "This package is on the queue": "Dit pakket staat in de wachtrij", "This process is running with administrator privileges": "Dit proces wordt uitgevoerd met <PERSON><PERSON><PERSON>", "This project has no connection with the official {0} project — it's completely unofficial.": "Dit project heeft geen verband met het officiële {0} project — het is volledig onofficieel.", "This setting is disabled": "Deze instelling is uitgeschakeld", "This wizard will help you configure and customize WingetUI!": "Deze assistent helpt bij het configureren en aanpassen van UniGetUI!", "Toggle search filters pane": "<PERSON><PERSON> met z<PERSON><PERSON><PERSON><PERSON><PERSON> wisselen", "Translators": "Vertalingen", "Try to kill the processes that refuse to close when requested to": "Probeer programma's te beëindigen als ze weigeren te sluiten op verzoek", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Het inschakelen van deze instelling staat het wijzigen van het uivoeringsbestand wat gebruikt wordt bij <PERSON>ies met pakketbeheerders toe. <PERSON><PERSON><PERSON> dit nauwkeurigere aanpassing van je installatie-proces toestaat, kan dit ook gevaren met zich meebrengen.", "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON><PERSON> hier de naam en de URL in van de bron die je wilt toevoegen, gescheiden door een spatie.", "Unable to find package": "Pakket niet a<PERSON>etro<PERSON>n", "Unable to load informarion": "Kan informatie niet laden", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI verzamelt anonieme gebruiksgegevens om de gebruikerservaring te verbeteren.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI verzamelt anonieme gebruiksgegevens met als enig doel inzicht te verkrijgen in de gebruikerservaring en deze te verbeteren.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI heeft een nieuwe bureaubladsnelkoppeling gedetecteerd die automatisch kan worden verwijderd.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI heeft de volgende snelkoppelingen op het bureaublad gedetecteerd die bij toekomstige upgrades automatisch kunnen worden verwijderd", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI heeft {0} nieuwe bureaubladsnelkoppelingen gedetecteerd die automatisch kunnen worden verwijderd.", "UniGetUI is being updated...": "UniGetUI wordt bijgewerkt...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI is niet gebonden aan een van de compatibele pakketbeheerders. UniGetUI is een onafhankelijk project.", "UniGetUI on the background and system tray": "UniGetUI op de achtergrond en het systeemvak", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI of componenten ervan ontbreken of zijn beschadigd.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI heeft {0} nodig om te functioneren, maar het werd niet aangetroffen op het systeem.", "UniGetUI startup page:": "UniGetUI startpagina:", "UniGetUI updater": "UniGetUI updater", "UniGetUI version {0} is being downloaded.": "UniGetUI versie {0} wordt gedownload.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} is klaar om geïnstalleerd te worden.", "Uninstall": "Verwijderen", "Uninstall Scoop (and its packages)": "<PERSON><PERSON> (en de pakketten) verwijderen", "Uninstall and more": "Verwijderen en meer", "Uninstall and remove data": "Verwijderen, incl. gegevens", "Uninstall as administrator": "Als administrator verwij<PERSON><PERSON>", "Uninstall canceled by the user!": "V<PERSON><PERSON><PERSON><PERSON> geannuleerd door gebruiker!", "Uninstall failed": "Verwijderen mislukt", "Uninstall options": "Verwijderingsopties", "Uninstall package": "Pakket verwijderen", "Uninstall package, then reinstall it": "Verwijder het pakket en installeer het opnieuw", "Uninstall package, then update it": "Pakket verwijderen en daarna bijwerken", "Uninstall previous versions when updated": "Oude versies verwijderen bij het updaten", "Uninstall selected packages": "Geselecteerde pakketten verwijderen", "Uninstall selection": "<PERSON>ie verwijderen", "Uninstall succeeded": "Verwijderen voltooid", "Uninstall the selected packages with administrator privileges": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON> verwi<PERSON>", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Niet-te-installeren pakketten afkomstig van \"{0}\" worden niet met behulp van een pakketbeheerder gepubliceerd, da<PERSON>m kan er geen informatie over worden vertoond.", "Unknown": "Onbekend", "Unknown size": "<PERSON><PERSON><PERSON><PERSON> grootte", "Unset or unknown": "<PERSON><PERSON><PERSON><PERSON> of onbekend", "Up to date": "Bijgewerkt", "Update": "Bijwerken", "Update WingetUI automatically": "UniGetUI automatisch bijwerken", "Update all": "Alles bijwerken", "Update and more": "Bijwerken en meer", "Update as administrator": "Als administrator bijwerken", "Update check frequency, automatically install updates, etc.": "Frequentie van update-controles, automatisch nieuwe versies installeren, enz.", "Update checking": "Controle op updates", "Update date": "Bijwerkingsdatum", "Update failed": "Update mislukt", "Update found!": "Update gevonden!", "Update now": "Nu bijwerken", "Update options": "Update-opties", "Update package indexes on launch": "Pakketindexen bijwerken bij het opstarten", "Update packages automatically": "Pakketten automatisch bijwerken", "Update selected packages": "Geselecteerde pakketten bijwerken", "Update selected packages with administrator privileges": "Geselecteerde pakketten bijwerken met <PERSON><PERSON><PERSON>", "Update selection": "Selectie bijwerken", "Update succeeded": "Update voltooid", "Update to version {0}": "Bijwerken naar versie {0}", "Update to {0} available": "Update naar {0} be<PERSON><PERSON><PERSON><PERSON>", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Git-portbestanden van vcpkg automatisch bijwerken (vereist Git geïnstalleerd)", "Updates": "Updates", "Updates available!": "Updates be<PERSON><PERSON><PERSON><PERSON>!", "Updates for this package are ignored": "Updates voor dit pakket worden genegeerd", "Updates found!": "Updates gevonden!", "Updates preferences": "Voorkeuren bijwerken", "Updating WingetUI": "UniGetUI bijwerken", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Gebundelde verouderde WinGet gebruiken in plaats van PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Aangepast pictogram en schermopname database-URL gebruiken", "Use bundled WinGet instead of PowerShell CMDlets": "Gebundelde WinGet gebruiken in plaats van PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "Gebruik gebundelde WinGet in plaats van systeem WinGet", "Use installed GSudo instead of UniGetUI Elevator": "Geïnstalleerde GSudio gebruiken in plaats van UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "GSudo (extra geïnstalleerd) gebruiken i.p.v. de gebundelde ", "Use system Chocolatey": "Systeem-<PERSON><PERSON> g<PERSON>n", "Use system Chocolatey (Needs a restart)": "Systeem-<PERSON><PERSON> (UniGetUI opnieuw starten)", "Use system Winget (Needs a restart)": "Systeem-<PERSON><PERSON> gebruiken (UniGetUI opnieuw starten)", "Use system Winget (System language must be set to english)": "Systeem-<PERSON><PERSON> g<PERSON>ike<PERSON> (systeemtaal moet zijn ingesteld op Engels)", "Use the WinGet COM API to fetch packages": "WinGet COM API gebruiken om pakketten op te halen", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Gebruik de WinGet PowerShell-module in plaats van de WinGet COM API", "Useful links": "Nuttige links", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "User interface preferences": "Voorkeuren gebruikersinterface", "User | Local": "User | Local", "Username": "Gebruikersnaam", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Bij het gebruik van UniGetUI ga je ak<PERSON><PERSON> met de GNU Lesser General Public License v2.1 licentie", "Using WingetUI implies the acceptation of the MIT License": "Met het g<PERSON><PERSON><PERSON> van UniGetUI ga je ak<PERSON><PERSON> met de MIT-licentie", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg-root werd niet gevonden. Definieer de omgevingsvariabele % VCPKG_ROOT% of definieer deze vanuit UniGetUI-instellingen", "Vcpkg was not found on your system.": "Vcpkg is niet gevonden op dit systeem.", "Verbose": "Uitvoerig", "Version": "<PERSON><PERSON><PERSON>", "Version to install:": "Te installeren versie:", "Version:": "Versie:", "View GitHub Profile": "GitHub-profiel bekijken", "View WingetUI on GitHub": "Bekijk UniGetUI op GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Bekijk de broncode van UniGetUI. <PERSON> kun je fouten melden of functies voorstellen, of zelfs een directe bijdrage leveren aan het UniGetUI-project.", "View mode:": "Weergave:", "View on UniGetUI": "Weergeven op UniGetUI", "View page on browser": "Pagina in browser bekijken", "View {0} logs": "{0} logboeken bekijken", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Taken die een internetverbinding verlangen uitstellen totdat het apparaat met internet is verbonden.", "Waiting for other installations to finish...": "Wachten tot andere installaties klaar zijn…", "Waiting for {0} to complete...": "Wachten tot {0} voltooid is...", "Warning": "Waarschuwing", "Warning!": "Waarschuwing!", "We are checking for updates.": "Controleren op updates.", "We could not load detailed information about this package, because it was not found in any of your package sources": "We konden geen gedetailleerde informatie over dit pakket laden, omdat het niet in een van jouw pakketbronnen werd aangetroffen", "We could not load detailed information about this package, because it was not installed from an available package manager.": "We konden over dit pakket geen gedetailleerde informatie laden, omdat het niet door een ondersteunde pakketbeheerder is geïnstalleerd.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "We konden {package} niet {action}. <PERSON>beer het later opnieuw. Klik op \"{showDetails}\" om het logboek van het installatieprogramma te bekijken.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "We konden {package} niet {action}. <PERSON><PERSON><PERSON> het later opnieuw. Klik op \"{showDetails}\" om het logboek van het verwijderprogramma te bekijken.", "We couldn't find any package": "We konden geen pakket vinden", "Welcome to WingetUI": "Welkom bij UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Bij het reeksgewijs installeren van pakketten van een bundel, installeer ook pakketten die al geïnstalleerd zijn", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Nieuwe snelkoppelingen bij detectie automatisch verwijderen worden gedetecteerd, in plaats van dit dialoogvenster te tonen.", "Which backup do you want to open?": "Welke back-up wil je openen?", "Which package managers do you want to use?": "Welke pakketbeheerders wil je geb<PERSON>iken?", "Which source do you want to add?": "Welke bron wil je toevoegen?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Hoewel Winget binnen UniGetUI kan worden gebruikt, kan UniGetUI worden gebruikt met andere pakketbeheerders, wat verwarrend kan zijn. UniGetUI was initieel ontworpen om alleen met Winget te werken, maar is ondertussen uitgegroeid en daarom vertegenwoordigt de naam UniGetUI niet langer waar dit project voor staat.", "WinGet could not be repaired": "WinGet kan niet worden gerepareerd", "WinGet malfunction detected": "WinGet-storing gedetecteerd", "WinGet was repaired successfully": "WinGet is met succes gerepareerd", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Alles is bijgewerkt", "WingetUI - {0} updates are available": "UniGetUI - {0} updates be<PERSON><PERSON><PERSON><PERSON>", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "UniGetUI website", "WingetUI Homepage - Share this link!": "UniGetUI website - Delen!", "WingetUI License": "UniGetUI-licentie", "WingetUI Log": "UniGetUI-logboek", "WingetUI Repository": "UniGetUI-opslagplaats", "WingetUI Settings": "UniGetUI instellingen", "WingetUI Settings File": "UniGetUI-instellingenbestand", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI maakt gebruik van de volgende bibliotheken, zonder welke UniGetUI niet mogelijk zou zijn geweest.", "WingetUI Version {0}": "UniGetUI-versie {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI startgedrag, instellingen voor het starten van de applicatie", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI kan controleren of jouw software beschikbare updates heeft en deze automatisch installeren als je dat wilt", "WingetUI display language:": "UniGetUI weergavetaal:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI is uitgevoerd als administrator, wat niet wordt aanbevolen. Wanneer UniGetUI als administrator wordt uitgevoerd, heeft ELKE bewerking die vanuit UniGetUI wordt gestart, admninistratorrechten. Je kunt het programma nog steeds geb<PERSON>iken, maar we raden je ten zeerste aan UniGetUI niet uit te voeren met administrator<PERSON><PERSON>.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI is dank<PERSON><PERSON> deze v<PERSON>j<PERSON> in meer dan 40 talen vertaald. Hartelijk bedankt 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI is niet automatisch vertaald! De volgende gebruikers hebben bijgedragen aan de vertaling:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI is een applicatie die het beheer van jouw software eenvoudiger maakt door een alles-in-één grafische interface te bieden voor je opdrachtregelpakketbeheerders.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI krijgt een nieuw naam om het onderscheid te benadrukken tussen UniGetUI (het programma dat je nu gebruikt) en Winget (een pakketbeheerder ontwikkeld door Microsoft waaraan ik niet verbonden ben)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI wordt bijgewerkt. Wanneer dit voltooid is, zal het zelf opnieuw opstarten", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI is gratis en zal dat altijd zijn. Geen advertenties, geen creditcard, geen premium-versie. 100% gratis, voor altijd.", "WingetUI log": "UniGetUI logboek", "WingetUI tray application preferences": "UniGetUI systeemvakvoorkeuren", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI maakt gebruik van de volgende bibliotheken, zonder welke UniGetUI niet mogelijk zou zijn geweest.", "WingetUI version {0} is being downloaded.": "UniGetUI-versie {0} wordt gedownload.", "WingetUI will become {newname} soon!": "WingetUI wordt binnenkort {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI controleert niet regelmatig op updates. Ze worden nog steeds gecontroleerd bij de lancering, maar je wordt er niet voor gewaarschuwd.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI zal elke keer dat een pakket ophoging van rechten vereist, een UAC-prompt tonen.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI krijgt binnenkort de naam {newname}. Dit betekent geen wijziging in de applicatie. Ik (de ontwikkelaar) zal de ontwikkeling van dit project voortzetten zoals ik nu doe, maar het krijgt een andere naam.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI zou niet bestaan zonder de hulp van onze gemeenschap. Bekijk hun GitHub-profielen. Zonder hen zou UniGetUI niet bestaan!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI zou niet mogelijk zijn geweest zonder de bijdragen van deze personen. \nBedankt allemaal 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} is klaar om geïnstalleerd te worden.", "Write here the process names here, separated by commas (,)": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>-namen hier, gescheiden door komma's (,)", "Yes": "<PERSON>a", "You are logged in as {0} (@{1})": "<PERSON> bent ingelogd als {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Je kunt dit gedrag wijzigen op UniGetUI veiligheidsinstellingen.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Je kunt de opdrachten die voor het installeren, updaten of verwijderen van dit pakket worden uitgevoerd bepalen. Deze worden op een opdrachtregel uitgevoerd, dus CMD-scripts zullen hier werken.", "You have currently version {0} installed": "Je hebt momenteel versie {0} geïnstalleerd", "You have installed WingetUI Version {0}": "Je hebt UniGetUI versie {0} geïnstalleerd", "You may lose unsaved data": "Je kunt mogelijk niet-opgeslagen gegevens verliezen", "You may need to install {pm} in order to use it with WingetUI.": "Mogelijk moet je {pm} installeren om het met UniGetUI te kunnen gebruiken.", "You may restart your computer later if you wish": "Je kunt je computer later opnieuw opstarten als je dat wilt", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Het wordt maar <PERSON>én keer gevraagd en administratorrechten worden verleend aan pakketten die daarom vragen.", "You will be prompted only once, and every future installation will be elevated automatically.": "Het wordt maar één keer gevraagd en elke toekomstige installatie wordt automatisch verhoogd.", "You will likely need to interact with the installer.": "Je zult waarschijnlijk moeten communiceren met het installatieprogramma.", "[RAN AS ADMINISTRATOR]": "[UITGEVOERD ALS ADMINISTRATOR]", "buy me a coffee": "mij een kop koffie doneren", "extracted": "uitgepakt", "feature": "functie", "formerly WingetUI": "voorheen WingetUI", "homepage": "website", "install": "installeren", "installation": "installatie", "installed": "geïnstalleerd", "installing": "installeren", "library": "bibliotheek", "mandatory": "ve<PERSON><PERSON>t", "option": "optie", "optional": "optioneel", "uninstall": "verwijderen", "uninstallation": "ver<PERSON><PERSON><PERSON>", "uninstalled": "ver<PERSON><PERSON><PERSON>d", "uninstalling": "verwijderen", "update(noun)": "update", "update(verb)": "bijwerken", "updated": "bijgewerkt", "updating": "bijwerken", "version {0}": "versie {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} Installatie-opties zijn op dit moment vergrendeld omdat {0} de standaard installatie-opties gebruikt.", "{0} Uninstallation": "{0} verwijderen", "{0} aborted": "{0} g<PERSON><PERSON><PERSON><PERSON>", "{0} can be updated": "{0} kan worden bijgewerkt", "{0} can be updated to version {1}": "{0} kan worden bijgewerkt naar versie {1}", "{0} days": "{0} dagen", "{0} desktop shortcuts created": "{0} bureaubladsnelkoppelingen aangemaakt", "{0} failed": "{0} mislukt", "{0} has been installed successfully.": "{0} is met succes geï<PERSON><PERSON>erd.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} is met succes geïnstalleerd. Het wordt aangeraden om UniGetUI opnieuw op te starten om de installatie te voltooien", "{0} has failed, that was a requirement for {1} to be run": "{0} is mislukt, dat was een vereiste voor het uitvoeren van {1}", "{0} homepage": "{0} website", "{0} hours": "{0} uur", "{0} installation": "{0} installatie", "{0} installation options": "{0} installatieopties", "{0} installer is being downloaded": "Installatieprogramma voor {0} wordt gedownload", "{0} is being installed": "{0} wordt geïnstalleerd", "{0} is being uninstalled": "{0} wordt verwijderd", "{0} is being updated": "{0} wordt bijgewerkt", "{0} is being updated to version {1}": "{0} wordt bijgewerkt naar versie {1}", "{0} is disabled": "{0} is uitgeschakeld", "{0} minutes": "{0} minuten", "{0} months": "{0} ma<PERSON>en", "{0} packages are being updated": "{0} pakketten worden bijgewerkt", "{0} packages can be updated": "{0} pakketten kunnen worden bijgewerkt", "{0} packages found": "{0} pakket<PERSON> gevonden", "{0} packages were found": "{0} pakket<PERSON> gevonden", "{0} packages were found, {1} of which match the specified filters.": "<PERSON><PERSON> <PERSON><PERSON><PERSON> {0} p<PERSON><PERSON><PERSON> gevo<PERSON>, wa<PERSON><PERSON> {1} over<PERSON><PERSON><PERSON> met de opgegeven filters.", "{0} selected": "{0} g<PERSON><PERSON><PERSON>d", "{0} settings": "{0} instellingen", "{0} status": "{0} status", "{0} succeeded": "{0} succesvol", "{0} update": "{0} bijwerken", "{0} updates are available": "<PERSON><PERSON> <PERSON><PERSON><PERSON> {0} updates be<PERSON><PERSON><PERSON><PERSON>", "{0} was {1} successfully!": "{0} is met succes {1}!", "{0} weeks": "{0} weken", "{0} years": "{0} jaar", "{0} {1} failed": "{0} {1} mislukt", "{package} Installation": "{package} installeren", "{package} Uninstall": "{package} verwijderen", "{package} Update": "{package} bijwerken", "{package} could not be installed": "{package} kan niet worden geïnstalleerd", "{package} could not be uninstalled": "{package} kan niet worden verwijderd", "{package} could not be updated": "{package} kan niet worden bijgewerkt", "{package} installation failed": "{package} installatie mislukt", "{package} installer could not be downloaded": "Installatieprogramma voor {package} kon niet worden gedownload", "{package} installer download": "Installatieprogramma voor {package} downloaden", "{package} installer was downloaded successfully": "Installatieprogramma voor {package} is met succes gedownload", "{package} uninstall failed": "{package} verwijderen mislukt", "{package} update failed": "{package} bijwerken mislukt", "{package} update failed. Click here for more details.": "{package} update mislukt. Klik hier voor meer informatie.", "{package} was installed successfully": "{package} is met succes geïnstalleerd", "{package} was uninstalled successfully": "{package} is met succes verwijderd", "{package} was updated successfully": "{package} is met succes bijgewerkt", "{pcName} installed packages": "{pcName} geïnstalleerde pakketten", "{pm} could not be found": "{pm} is niet a<PERSON><PERSON><PERSON><PERSON>n", "{pm} found: {state}": "{pm} aangetroffen: {state}", "{pm} is disabled": "{pm} is uitgeschakeld", "{pm} is enabled and ready to go": "{pm} is ingeschakeld en klaar voor gebruik", "{pm} package manager specific preferences": "{pm} pakketbeheer - specifieke voorkeuren", "{pm} preferences": "{pm}-v<PERSON><PERSON><PERSON>", "{pm} version:": "{pm} versie:", "{pm} was not found!": "{pm} niet aang<PERSON><PERSON><PERSON>n!"}