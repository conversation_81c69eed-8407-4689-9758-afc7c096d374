{"\"{0}\" is a local package and can't be shared": "\"{0}\" је локални пакет и дељење није дозвољено", "\"{0}\" is a local package and does not have available details": "\"{0}\" је локални пакет и нема доступних детаља", "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(Последње проверено: {0})", "(Number {0} in the queue)": "(Број {0} у реду)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@daVinci13, @momcilovicluka", "0 packages found": "0 пакета нађено.", "0 updates found": "0 надоградњи нађено.", "1 - Errors": "1 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "1 day": "1 дан", "1 hour": "1 сат", "1 month": "мес<PERSON><PERSON>", "1 package was found": "Пронађен је 1 пакет", "1 update is available": "1 ажурирање доступно", "1 week": "1 недеља", "1 year": "1 година", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Идите на \"{0}\" или \"{1}\" страну.", "2 - Warnings": "2 - Упо<PERSON><PERSON><PERSON><PERSON>ња", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Пронађите пакет(е) које желите да додате у скуп пакета, и означите њихово крајње лево поље за потврду.", "3 - Information (less)": "3 - Ин<PERSON>ор<PERSON>ације (мање)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. Када су пакети које желите да додате у скуп пакета изабрани, пронађите и кликните на опцију \"{0}\" на траци са алатима.", "4 - Information (more)": "4 - Информације (више)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Ваши пакети су додати у скуп пакета. Можете да наставите додавање пакета, или да извезете скуп пакета.", "5 - information (debug)": "5 - информације (отклањање грешака)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Популарни C/C++ управљач библиотекама. Пун C/C++ библиотека и других C/C++-сродних алата<br>Садржи: <b>C/C++ библиотеке и сродне алате</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Репозиторијум пун алата и извршних програма дизајниран имајући у виду Микрософтов .NET екосистем.<br>Садржи: <b>алатке и скрипте везане за .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Складиште пуно алатки дизајниране у складу са Microsoft's .NET екосистемом.<br>Поседује: <b>.NET алате</b>", "A restart is required": "Неопходан је рестарт", "Abort install if pre-install command fails": "Обустави инсталацију ако пре-инсталациона команда не успе", "Abort uninstall if pre-uninstall command fails": "Обустави деинсталацију ако пре-деинсталациона команда не успе", "Abort update if pre-update command fails": null, "About": "Информације", "About Qt6": "О Qt6", "About WingetUI": "О WingetUI", "About WingetUI version {0}": "О WingetUI верзији {0}", "About the dev": "О програмеру", "Accept": null, "Action when double-clicking packages, hide successful installations": "Акција при двоструком клику на пакете, сакриј успешне инсталације", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "Додај извор у {0}", "Add a timestamp to the backup file names": "Додај време називу фајла резервне копије", "Add a timestamp to the backup files": "Додај ознаку времена у резервној копији", "Add packages or open an existing bundle": "Додајте пакете или отворите постојећи скуп", "Add packages or open an existing package bundle": "Додај пакете или отвори постојећи скуп", "Add packages to bundle": null, "Add packages to start": "Додај пакете за почетак", "Add selection to bundle": "Додај одабране у скуп", "Add source": "Додај извор", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": "Додавање извора {source} у {manager}", "Addition succeeded": "Успешно додавање", "Administrator privileges": "Административне привилегије", "Administrator privileges preferences": "Поставке административних привилегија", "Administrator rights": "Администраторска права", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "Сви фајлови", "All versions": "Све верзије", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Дозволи паралелно извршавање операција над пакетима", "Allow parallel installs (NOT RECOMMENDED)": "Дозволи паралелну инсталцију (НИЈЕ ПРЕПОРУЧЕНО)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "Дозволи {pm} операцијама да се извршавају паралелно", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": "Увек подразумевано отвори {pm} у повишеном режиму", "Always run {pm} operations with administrator rights": "Увек извршава<PERSON> {pm} операције са администраторским привилегијама", "An error occurred": "Дошло је до грешке", "An error occurred when adding the source: ": "Дошло је до грешке приликом додавања извора:", "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": "Дошло је до грешке приликом проверавања ажурирања", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "Настала је грешка током израде пакета", "An error occurred:": "Дошло је до грешке:", "An interal error occurred. Please view the log for further details.": "Догодила се унутрашња грешка. Молимо погледајте логове за даље детаље", "An unexpected error occurred:": "Дошло је до неочекиване грешке:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": "Пронађено је ажурирање!", "Android Subsystem": "Андроид Подсистем", "Another source": "Додатни извор", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": null, "App Name": "Име апликације", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "Тема апликације", "Apply": null, "Architecture to install:": "Архитектура за инсталацију:", "Are these screenshots wron or blurry?": "Да ли су ови снимци екрана погрешни или мутни?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "Да ли сте сигурни?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "Затражи администраторске привилегије једном за сваки скуп операција", "Ask for administrator rights when required": "Тражи администраторска права када су потребна", "Ask once or always for administrator rights, elevate installations by default": "Питајте једном или увек за администраторска права, подразумевано повишавајте инсталације", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Питајте само једном за администраторске привилегије (није препоручљиво)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": "Потребна пажња", "Authenticate to the proxy with an user and a password": null, "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": null, "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Аутоматски сачувај листу свих инсталираних пакета због лакшег враћања.", "Automatically save a list of your installed packages on your computer.": "Аутоматски сачувај листу инсталираних пакета на рачунару.", "Autostart WingetUI in the notifications area": "Аутоматски покрени WingetUI међу нотификацијама", "Available Updates": "Доступна Ажурирања", "Available updates: {0}": "Доступне надоградње: {0}", "Available updates: {0}, not finished yet...": "Доспутне надоградње: {0}, није још завршено...", "Backing up packages to GitHub Gist...": null, "Backup": "Резервна копија", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Резервна копија инсталираних пакета", "Backup location": null, "Become a contributor": "Постаните доприносилац", "Become a translator": "Постани преводилац", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Бета функције и друге опције које не би требало да се додирну", "Both": "Оба", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Али овде има још ствари које можете урадити да сазнате више о WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Искључивањем менаџера пакета, више нећете моћи да видите или ажурирате његове пакете", "Cache administrator rights and elevate installers by default": "Кеш администраторских права и подразумевано повећајте инсталатере", "Cache administrator rights, but elevate installers only when required": "Кеш администраторских права, али повећајте инсталатере само када је потребно", "Cache was reset successfully!": "Кеш је успешно ресетован!", "Can't {0} {1}": "Не може {0} {1}", "Cancel": "Откажи", "Cancel all operations": null, "Change backup output directory": "Промени локацију резервне копије", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "Промените како UniGetUI проверава и инсталира доступна ажурирања за ваше пакете", "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": "Промени локацију инсталирања", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Повремено проверавајте ажурирања пакета", "Check for updates": null, "Check for updates every:": "Проверавајте ажурирања сваких:", "Check for updates periodically": "Повремено проверавајте ажурирања", "Check for updates regularly, and ask me what to do when updates are found.": "Редовно проверавајте ажурирања и питајте ме шта да радим када се пронађу ажурирања.", "Check for updates regularly, and automatically install available ones.": "Редовно проверавајте ажурирања и аутоматски инсталирајте доступна ажурирања.", "Check out my {0} and my {1}!": "Погледајте мој {0} и мој {1}!", "Check out some WingetUI overviews": "Погледајте неке прегледе WingetUI", "Checking for other running instances...": "Проверавање других покренутих инстанци...", "Checking for updates...": "Провера ажурирања...", "Checking found instace(s)...": "Провера нађених инстанци...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "Обриши избор", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": "Брис<PERSON><PERSON><PERSON> Scoop кеша - WingetUI", "Clearing Scoop cache...": "Бришење кеша Scoop...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Кликните на Инсталирај да бисте започели процес инсталације. Ако прескочите инсталацију, UniGetUI можда неће радити како се очекује.", "Close": "Затвори", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Затвори WingetUI у обавештење област", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "Излаз командне линије", "Command-line to run:": null, "Compare query against": "Упоредите упит са", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Информације о компоненти", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": "Настави", "Contribute to the icon and screenshot repository": "Допринесите репозиторијуму икона и снимака екрана", "Contributors": "Доприниосци", "Copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "Копирај на клипборд", "Could not add source": null, "Could not add source {source} to {manager}": "Није могуће додати извор {source} у {manager}\n", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": "Није могуће учитати саопштења -", "Could not load announcements - HTTP status code is $CODE": "Није могуће учитати саопштења - ХТТП статусни код је $CODE", "Could not remove source": null, "Could not remove source {source} from {manager}": "Није могуће уклонити извор {source} из {manager}", "Could not remove {source} from {manager}": null, "Create .ps1 script": null, "Credentials": null, "Current Version": "Тренутна верзија", "Current status: Not logged in": null, "Current user": "Тренутни корисник", "Custom arguments:": "Прилагођени аргументи", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Прилагодите аргументе командне линије:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Прилагодите WingetUI - само за хакере и напредне кориснике", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ИСКЉУЧЕЊЕ ОДГОВОРНОСТИ: МИ НИСМО ОДГОВОРНИ ЗА СКИНУТЕ ПАКЕТЕ. МОЛИМО ВАС, УВЕК ИНСТАЛИРАЈТЕ САМО ПОУЗДАН СОФТВЕР.", "Dark": "Тамно", "Decline": null, "Default": "Подразумевано", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Подразумеване поставке - погодне за редовне кориснике", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "Опис:", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Развој је тежак, а ова апликација је бесплатна. Али ако вам се апликација свидела, увек можете <b>купити ми кафу</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Директно инсталирајте када двоструко кликнете на ставку на табли \"{discoveryTab}\" (уместо приказивања информација о пакету)", "Disable new share API (port 7058)": "Онемогућите нови дељени API (порт 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": "Одрицање од одговорности", "Discover Packages": "Откријте пакете", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "Разликујте између\nвеликих и малих слова", "Distinguish between uppercase and lowercase": "Прави разлику између великих и малих слова", "Do NOT check for updates": "НЕ проверавајте ажурирања", "Do an interactive install for the selected packages": "Изведите интерактивну инсталацију за изабране пакете", "Do an interactive uninstall for the selected packages": "Изведите интерактивно деинсталирање за изабране пакете", "Do an interactive update for the selected packages": "Изведите интерактивно ажурирање за изабране пакете", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Не преузимајте нове преводе апликација са GitHub-а аутоматски", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": "Немој аутоматски уклањати успешне операције са листе", "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "Не ажурирајте индексе пакета при покретању", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "\nДа ли сматрате да је WingetUI користан? Ако можете, можда бисте желели да подржите мој рад, тако да могу да наставим да чиним WingetUI врхунским интерфејсом за управљање пакетима.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Да ли вам је WingetUI користан? Желите да подржите развојника? Ако је тако, можете {0}, то много помаже!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": "Да ли заиста желите да деинсталирате следећих {0} пакета?", "Do you really want to uninstall {0} packages?": "Да ли заиста желите да деинсталирате {0} пакета?", "Do you really want to uninstall {0}?": "Да ли заиста желите да деинсталирате {0}?", "Do you want to restart your computer now?": "Да ли желите да поново покренете рачунар сада?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Да ли желите да преведете WingetUI на свој језик? Видите како да допринесете <a style=\"color:{0}\" href=\"{1}\"a>ОВДЕ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "\nНе донира Вам се? Не брините, увек можете да делите WingetUI са својим пријатељима. Проширите вест о WingetUI.", "Donate": "Дони<PERSON><PERSON><PERSON>те", "Done!": null, "Download failed": null, "Download installer": "Преузми инсталер", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Успешно преузимање", "Download updated language files from GitHub automatically": "Аутоматски преузимајте ажуриране језичке датотеке са GitHub-а", "Downloading": "Преузимање", "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "Преузимам податке о пакетима...", "Enable Scoop cleanup on launch": "Омогућите чишћење Scoop-а при покретању", "Enable WingetUI notifications": "Омогућите обавештења за WingetUI", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Омогући позадински апи (WingetUI Widgets and Sharing, порт 7058)", "Enable it to install packages from {pm}.": "Омогућите га да бисте инсталирали пакете са {pm}.", "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Омогућите {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "Грешка", "Everything is up to date": "Све је ажурно", "Exact match": "Тачно подударање", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": "Прошири верзију", "Experimental settings and developer options": "Експерименталне поставке и опције за развој", "Export": "Извоз", "Export log as a file": "Извоз лога као фајла", "Export packages": "Извоз пакета", "Export selected packages to a file": "Извоз изабраних пакета у фајл", "Export settings to a local file": "Извоз поставки у локални фајл", "Export to a file": "Извоз у фајл", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "Прибављање најновијих најава, молимо сачекајте...", "Filters": "Филтери", "Finish": "Заврши", "Follow system color scheme": "Прати шему боја система", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Принудите ARM компајлирану верзију winget-a (САМО ЗА ARM64 СИСТЕМЕ)", "Formerly known as WingetUI": "Раније познат као WingetUI", "Found": "Пронађено", "Found packages: ": "Пронађени пакети:", "Found packages: {0}": "Пронађени пакети: {0}", "Found packages: {0}, not finished yet...": "Пронађени пакети: {0}, још није завршено...", "General preferences": "Опште поставке", "GitHub profile": "Профил на GitHub-у", "Global": "Глобално", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Велики репозиторијум непознатих али корисних алатки и других интересантних пакета.<br>Садржи: <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Програме за командну линију, Општи софтвер (потребна додатна кофа)", "Great! You are on the latest version.": null, "Grid": null, "Help": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Help and documentation": "Помоћ и документација", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Здраво, зовем се Марти, и ја сам <i>развојник</i> WingetUI-а. WingetUI је цео направљен у мом слободном времену!", "Hide details": "Сакриј детаље", "Homepage": "Дома<PERSON>а страна", "Hooray! No updates were found.": "Ура! Нису пронађена ажурирања!", "How should installations that require administrator privileges be treated?": "Како треба третирати инсталације које захтевају администраторске привилегије?", "How to add packages to a bundle": null, "I understand": "Разумем", "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Игнориши будућа ажурирања за овај пакет", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "Игнориши изабране пакете", "Ignore special characters": "Игнориши специјалне карактере", "Ignore updates for the selected packages": "Игнориши ажурирања за изабране пакете", "Ignore updates for this package": "Игнориши ажурирања за овај пакет", "Ignored updates": "Игнорисана ажурирања", "Ignored version": "Игнорисана верзија", "Import": "Увоз", "Import packages": "Увоз пакета", "Import packages from a file": "Увоз пакета из фајла", "Import settings from a local file": "Увоз поставки из локалног фајла", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "Иницијализује се WingetUI...", "Install": "Инста<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install Scoop": "Инстал<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Scoop", "Install and more": null, "Install and update preferences": null, "Install as administrator": "Инсталирајте као администратор", "Install available updates automatically": "Аутоматски инсталирај доступна ажурирања", "Install location can't be changed for {0} packages": null, "Install location:": "Локација инсталације:", "Install options": null, "Install packages from a file": "Инсталирајте пакете из фајла", "Install prerelease versions of UniGetUI": null, "Install script": null, "Install selected packages": "Инсталирајте изабране пакете", "Install selected packages with administrator privileges": "Инсталирајте изабране пакете са администраторским привилегијама", "Install selection": "Инстал<PERSON><PERSON><PERSON><PERSON> одабране", "Install the latest prerelease version": "Инсталир<PERSON>ј најновију верзију пред издања", "Install updates automatically": "Инсталирајте ажурирања аутоматски", "Install {0}": null, "Installation canceled by the user!": "Инсталација је отказана од стране корисника!", "Installation failed": "Неуспешна инсталација", "Installation options": "Опције инсталације", "Installation scope:": "Опсег инсталације:", "Installation succeeded": "Успешна инсталација", "Installed Packages": "Инсталирани пакети", "Installed Version": "Инстали<PERSON><PERSON>на верзија", "Installed packages": null, "Installer SHA256": "SHA256 инсталатера", "Installer SHA512": "SHA512 инсталатера", "Installer Type": "Тип инсталатера", "Installer URL": "URL инсталатера", "Installer not available": "Инсталатер није доступан", "Instance {0} responded, quitting...": "Инстанца {0} одговорила, затворила се...", "Instant search": "Инстант претрага", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "Интера<PERSON>тивна инсталација", "Interactive operation": null, "Interactive uninstall": "Интерактивно деинсталирање", "Interactive update": "Интерактивно ажурирање", "Internet connection settings": null, "Is this package missing the icon?": "Да ли овом пакету недостаје икона?", "Is your language missing or incomplete?": "Да ли је Ваш језик непотпун или недостаје?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Изгледа да сте покренули WingetUI као администратор, што се не препоручује. Ипак, можете користити програм, али вам најтооплепају не покретати WingetUI с администраторским привилегијама. Кликните на \"{showDetails}\" да видите зашто.", "Language": null, "Language, theme and other miscellaneous preferences": "<PERSON>е<PERSON><PERSON><PERSON>, тема и остале опције", "Last updated:": "Последњи пут ажурирано:", "Latest": "Најновије", "Latest Version": "Најновија верзија", "Latest Version:": "Најновија верзија:", "Latest details...": "Најновији детаљи...", "Launching subprocess...": "Покретање подпроцеса...", "Leave empty for default": "Оставите празно за подразумевано", "License": "Лиценца", "Licenses": "Лиценце", "Light": "Светло", "List": null, "Live command-line output": "Уживо излаз командне линије", "Live output": "Излаз уживо", "Loading UI components...": "Учитавање UI компоненти...", "Loading WingetUI...": "Учитавање WingetUI...", "Loading packages": "Учитавање пакета", "Loading packages, please wait...": "Учитавање пакета, молимо сачекајте...", "Loading...": "Учитавање...", "Local": "Локално", "Local PC": "Локални рачунар", "Local backup advanced options": null, "Local machine": "Локална машина", "Local package backup": null, "Locating {pm}...": "Локализује {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "Ниво логовања:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Тражење пакета...", "Machine | Global": "Машина | Глобално", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "Управљајте понашањем аутоматског покретања WingetUI из апликације Подешавања", "Manage ignored packages": "Управљајте игнорисаним пакетима", "Manage ignored updates": "Управљајте игнорисаним ажурирањима", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": "Управљај {0} извора", "Manifest": "Манифест", "Manifests": "Манифести", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Званични пакетни менаџер Microsoft-a. Пун добро познатих и верификованих пакета<br>Садржи: <b>Општи софтвер, апликације из Microsoft Store-a</b>", "Missing dependency": "Недостаје зависност", "More": "Више", "More details": "Више детаља", "More details about the shared data and how it will be processed": null, "More info": "Више информација", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "Име", "New": null, "New Version": "Нова верзија", "New bundle": "Нови скуп", "New version": "Нова верзија", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Не", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "Пакети нису пронађени", "No packages found matching the input criteria": "Нису пронађени пакети који одговарају уносу", "No packages have been added yet": "Још увек није додат ниједан пакет", "No packages selected": "Нису изабрани пакети", "No packages were found": "Није пронађен ниједан пакет", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": "Није пронађен ниједан резултат који одговара унетим критеријумима", "No sources found": "Извори нису пронађени", "No sources were found": "Није пронађен ниједан извор", "No updates are available": "Ажурирања нису доступна", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Пакет менаџер за Node JS. Пун библиотека и других алатки које су у вези са JavaScript-ом<br>Садржи: <b>Библиотеке JavaScript-a и друге релевантне алатке</b>", "Not available": "Није доступно", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Није пронађено", "Not right now": "Не тренутно", "Notes:": "Белешке:", "Notification preferences": "Преференце обавештења", "Notification tray options": "Опције у обавештењима", "Notification types": null, "NuPkg (zipped manifest)": "NuPkg (зиповани манифест)", "OK": "У реду", "Ok": "У реду", "Open": "Отвори", "Open GitHub": "Отв<PERSON><PERSON><PERSON>", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": "Отвори WingetUI", "Open backup location": "Отвори локацију резервне копије", "Open existing bundle": "Отвори постојећи скуп", "Open install location": null, "Open the welcome wizard": "Отвори чаробњак за добродошлицу", "Operation canceled by user": "Корисник је отказао операцију", "Operation cancelled": "Операција је отказана", "Operation history": "Историја операција", "Operation in progress": "Операција у току", "Operation on queue (position {0})...": "Операција је у реду (позиција {0})...", "Operation profile:": null, "Options saved": "Опција сачувана", "Order by:": null, "Other": "Друго", "Other settings": null, "Package": null, "Package Bundles": "Скупови Пакета", "Package ID": "ИД пакета", "Package Manager": "Менаџер Пакета", "Package Manager logs": "Логови пакет менаџера", "Package Managers": "Менаџери Пакета", "Package Name": "Име пакета", "Package backup": null, "Package backup settings": null, "Package bundle": "Скуп пакета", "Package details": "Детаљи пакета", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "Поставке пакет менаџера", "Package managers": null, "Package not found": "Пакет није пронађен", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "Пакети", "Packages found: {0}": "Пронађено пакета: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "Залепите исправан URL у базу података", "Pause updates for": null, "Perform a backup now": "Одмах направи резервну копију", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "Прављење резервне копије, молимо сачекајте...", "Periodically perform a backup of the installed packages": "Повремено направи резервну копију инсталираних пакета", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": "Кликните на „Настави“ да бисте наставили", "Please enter at least 3 characters": "Молимо унесите барем 3 карактера", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Обратите пажњу да одређени пакети можда нису могући за инсталацију због пакет менаџера који су омогућени на овом рачунару.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Обратите пажњу да пакети из одређених извора можда не могу бити извезени. Они су затамњени и неће бити извезени.", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Молимо погледајте излаз командне линије или историју операција за додатне информације о проблему.", "Please select how you want to configure WingetUI": "Одаберите како желите да конфигуришете WingetUI", "Please try again later": null, "Please type at least two characters": "Молимо унесите најмање два знака", "Please wait": "Молимо сачекајте", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "Молимо сачекајте...", "Portable": "Преносиво", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell-ов менаџер пакета. Пронађите библиотеке и скрипте за проширење PowerShell могућности<br>Садржи: <b>Мо<PERSON><PERSON><PERSON><PERSON>, Скрипте, Cmdlet-ове</b>\n", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "ПреИздање", "Preparing packages, please wait...": "Припрема пакета, молимо сачекајте...", "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "Датум објаве:", "Publisher": "Издавач", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Менаџер библиотека за Python. Пун Python библиотека и других Python-релевантних алатки<br>Садржи: <b>Python библиотеке и релевантне алатке</b>", "Quit": "Излаз", "Quit WingetUI": "Затвори WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": "Реинсталирај пакет", "Related settings": null, "Release notes": "Белешке о верзијама", "Release notes URL": "УРЛ белешки о издању", "Release notes URL:": "URL белешки о издању:", "Release notes:": "Белешке о издању:", "Reload": "Поново учитај", "Reload log": "Поново учитај лог", "Removal failed": "Неуспешно уклањање", "Removal succeeded": "Успешно уклањање", "Remove from list": "Уклони са листе", "Remove permanent data": "Уклони постојеће податке", "Remove selection from bundle": "Уклони одабране из скупа", "Remove successful installs/uninstalls/updates from the installation list": "Уклони успешне инсталације/деинсталације/ажурирања са листе инсталација", "Removing source {source}": null, "Removing source {source} from {manager}": "Уклањање извора {source} из {manager}", "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": "Пријавите проблем или пошаљите захтев за функционалност", "Repository": "Репозиторијум", "Reset": "Ресет", "Reset Scoop's global app cache": "Ресетујте глобални кеш апликација за Scoop", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "Ресетујте изворе за Winget (може помоћи ако нема приказаних пакета)", "Reset WingetUI": "Ресетујте WingetUI", "Reset WingetUI and its preferences": "Ресетујте WingetUI и његове поставке", "Reset WingetUI icon and screenshot cache": "Ресетујте WingetUI кеш икона и снимака екрана", "Reset list": null, "Resetting Winget sources - WingetUI": "Ресетовање Winget извора - WingetUI", "Restart": null, "Restart UniGetUI": "Рестартуј UniGetUI", "Restart WingetUI": "Поново покрените WingetUI", "Restart WingetUI to fully apply changes": "Поново покрените WingetUI да бисте у потпуности применили измене", "Restart later": "Поново покрените касније", "Restart now": "Поново покрените сада", "Restart required": "Неопходно је поновно покретање", "Restart your PC to finish installation": "Поново покрените рачунар да бисте завршили инсталацију", "Restart your computer to finish the installation": "Поново покрените рачунар да бисте завршили инсталацију", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Покушај поново", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": "Поновни покушај, молимо сачекајте...", "Return to top": "Врати се на врх", "Run": "Покрени", "Run as admin": "Покрени као администратор", "Run cleanup and clear cache": "Покрените чишћење и обришите кеш", "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "Извршавање инсталатера...", "Running the uninstaller...": "Извршавање деинсталатера...", "Running the updater...": "Извршавање ажурирања...", "Save": null, "Save File": "Сачувај фајл", "Save and close": "Сачувај и затвори", "Save as": null, "Save bundle as": "Сачувај скуп као", "Save now": "Сачувај", "Saving packages, please wait...": "Чување пакета, молимо сачекајте...", "Scoop Installer - WingetUI": "Scoop Инсталер - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop Деинсталер - WingetUI", "Scoop package": "Scoop пакет", "Search": "Претрага", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Претражите десктоп софтвер, обавестите ме када су доступна ажурирања и не радите чудачке ствари. Не желим да WingetUI постане компликован, желим једноставан <b>софтверски магазин</b>", "Search for packages": "Претражите пакете", "Search for packages to start": "Претражите пакете за почетак", "Search mode": "Мод претраге", "Search on available updates": "Претражите доступна ажурирања", "Search on your software": "Претражите свој софтвер", "Searching for installed packages...": "Претрага инсталираних пакета...", "Searching for packages...": "Претрага пакета...", "Searching for updates...": "Претрага ажурирања...", "Select": "Одабери", "Select \"{item}\" to add your custom bucket": "Изаберите \"{item}\" да бисте додали своју прилагођену кофу", "Select a folder": "Одабери фолдер", "Select all": "Изаберите све", "Select all packages": "Изаберите све пакете", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "Изаберите само <b>ако знате шта радите</b>.", "Select package file": "Изаберите фајл пакета", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "Одаберите извор који желите да додате:", "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Изаберите које <b>менаџере пакета</b> користити ({0}), конфигуришите како се пакети инсталирају, управљајте обрадом администраторских права, итд.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Послат ручни поздрав. Чекање одговора од слушаоча инстанце... ({0}%)", "Set a custom backup file name": "Постави другачији назив фајла резервне копије", "Set custom backup file name": "Постави назив резервне копије.", "Settings": "Подешавања", "Share": "Подели", "Share WingetUI": "Подели WingetUI", "Share anonymous usage data": null, "Share this package": "Поделите овај пакет", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Прикажи UniGetUI на system tray", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "Прикажи WingetUI", "Show a notification when an installation fails": "Прикажи обавештење када инсталација не успе", "Show a notification when an installation finishes successfully": "Прикажи обавештење када се успешно заврши инсталација", "Show a notification when an operation fails": "Прикажи обавештење када операција не успе", "Show a notification when an operation finishes successfully": "Прикажи обавештење када операција успе", "Show a notification when there are available updates": "Прикажи обавештење када су доступна ажурирања", "Show a silent notification when an operation is running": "Прикажи тихо обавештење када се операција извршава", "Show details": "Прикажи детаље", "Show in explorer": null, "Show info about the package on the Updates tab": "Прикажи информације о пакету на табу за ажурирања", "Show missing translation strings": "Прикажи недостајуће преводе", "Show notifications on different events": "Прикажи обавештења о различитим догађајима", "Show package details": "Прикажи детаље пакета", "Show package icons on package lists": null, "Show similar packages": "Покажи сличне пакете", "Show the live output": "Прикажи уживо излаз", "Size": "Величина", "Skip": "Прескочи", "Skip hash check": "Прескочи проверу хеша", "Skip hash checks": null, "Skip integrity checks": "Прескочи проверу интегритета", "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "Прескочи проверу хеша при инсталацији изабраних пакета", "Skip the hash check when updating the selected packages": "Прескочи проверу хеша при ажурирању изабраних пакета", "Skip this version": "Прескочи ову верзију", "Software Updates": "Ажурирања софтвера", "Something went wrong": "Нешто је пошло по злу", "Something went wrong while launching the updater.": null, "Source": "Извор", "Source URL:": "Изворни УРЛ:", "Source added successfully": null, "Source addition failed": "Неуспешно додавање извора", "Source name:": "Назив извора:", "Source removal failed": "Неуспешно уклањање извора", "Source removed successfully": null, "Source:": "Извор:", "Sources": "Извори", "Start": "Покрени", "Starting daemons...": "Покрећем демоне...", "Starting operation...": null, "Startup options": "Опције покретања", "Status": "Статус", "Stuck here? Skip initialization": "Заглављени сте овде? Прескочите иницијализацију", "Success!": null, "Suport the developer": "Подржите програмера", "Support me": "Подржи ме", "Support the developer": "Подржи програмера", "Systems are now ready to go!": "Системи су сада спремни за рад!", "Telemetry": null, "Text": "Текст", "Text file": "Текстуални фајл", "Thank you ❤": "Хвала ❤", "Thank you 😉": "Хвала 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": "Резервна копија НЕ укључује бинарни фајл ни сачуване податке програма.", "The backup will be performed after login.": "Резервна копија ће бити направљена на следећем логовању.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Резервна копија ће садржати целу листу инсталираних пакета и њихове опције инсталације. Игнорисана ажурирања и прескочене верзије ће такође бити сачуване.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Хеш инсталатера се не поклапа са очекиваном вредношћу, и аутентичност инсталатера се не може потврдити. Ако верујете издавачу, {0} поново инсталирајте пакет и прескочите проверу хеша.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Класични менаџер пакета за Windows. Ту ћете пронаћи све. <br>Садржи: <b>Општи софтвер</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Тренутни скуп нема пакета. Додајте неке пакете како бисте започели", "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Следећи пакети ће бити извезени у JSON фајл. Неће бити сачувани кориснички подаци или бинарни фајлови.", "The following packages are going to be installed on your system.": "Следећи пакети ће бити инсталирани на ваш систем.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "Следећа подешавања ће бити примењена сваки пут када се овај пакет инсталира, ажурира или уклони.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Следећа подешавања ће бити примењена на сваку инсталацију, ажурирање и брисање овог пакета. Аутоматски ће бити сачуване.", "The icons and screenshots are maintained by users like you!": "Иконе и снимци екрана се одржавају од стране корисника као што сте ви!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "Инсталатер има неважећи хеш", "The installer hash does not match the expected value.": "Хеш инсталера не одговара очекиваној вредности.", "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Главни циљ овог пројекта је стварање интуитивног корисничког интерфејса за управљање најчешћим CLI менаџерима пакета за Windows, као што су Winget и Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": "Пакет {0} од {1} није пронађен.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Пакети наведени овде неће бити узети у обзир при провери ажурирања. Дупликати их или кликните на дугме с десне стране да бисте престали игнорисати њихова ажурирања.", "The selected packages have been blacklisted": "Изабрани пакети су стављени на црну листу", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Процењена величина резервне копије је мања од 1МБ.", "The source {source} was added to {manager} successfully": "Извор {source} је успешно додат у {manager}", "The source {source} was removed from {manager} successfully": "Извор {source} је успешно уклоњен из {manager}", "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": "Ажурирање ће бити инсталирано по затварању WingetUI ", "The update will not continue.": "Ажу<PERSON>ирање се неће наставити.", "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Операције су у току. Напуштање WingetUI-ја може довести до њиховог неуспеха. Да ли желите да наставите?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "На YouTube-у постоје одлични видео записи који приказују WingetUI и његове могућности. Можете научити корисне трикове и савете!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Постоје два главна разлога зашто не бисте покренули WingetUI као администратора:\n Први разлог је да би Scoop менаџер пакета могао изазвати проблеме с неким командама када се извршава с администраторским правима.\n Други разлог је да покретање WingetUI као администратор значи да ће сваки пакет који преузмете бити извршен као администратор (и ово није безбедно).\n Запамтите да ако вам је потребно да инсталирате одређени пакет као администратор, увек можете десним кликом на ставку -> Инсталирај/Ажурирај/Деинсталирај као администратор.", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Инсталација је у току. Ако затворите WingetUI, инсталација може не успети и имати неочекиване резултате. Да ли и даље желите да излазите из WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "Они су програми одговорни за инсталирање, ажурирање и уклањање пакета.", "Third-party licenses": "Лиценце трећих лица", "This could represent a <b>security risk</b>.": "Ово би могло представљати <b>сигурносни ризик</b>.", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Ово је вероватно због тога што је пакет који вам је послат уклоњен или објављен на менаџеру пакета који није активиран на вашем систему. Примљени идентификатор је {0}", "This is the <b>default choice</b>.": "Ово је <b>подразумевани избор</b>.", "This may help if WinGet packages are not shown": "Ово може помоћи ако WinGet пакети нису приказани", "This may help if no packages are listed": null, "This may take a minute or two": "Ово може потрајати минут или два", "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Овај пакет се може ажурирати.", "This package can be updated to version {0}": "Овај пакет може бити ажуриран на верзију {0}", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Овај пакет нема снимак екрана или му недостаје икона? Дајте допринос WingetUI додавањем икона и снимака екрана који недостају у нашу отворену, јавну базу података.", "This package is already installed": "Овај пакет је већ инсталиран", "This package is being processed": "Овај пакет је тренутно у обрати.", "This package is not available": null, "This package is on the queue": "Овај пакет је на листи чекања.", "This process is running with administrator privileges": "Овај процес се извршава с администраторским правима", "This project has no connection with the official {0} project — it's completely unofficial.": "Овај пројекат нема везе с официјалним {0} пројектом — потпуно је неслацићени.", "This setting is disabled": "Ово подешавање је онемогућено", "This wizard will help you configure and customize WingetUI!": "Овај волшебник ће вам помоћи да конфигуришете и прилагодите WingetUI!", "Toggle search filters pane": "Промените приказ панела филтера", "Translators": "Преводиоци", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Овде напиши назив и линк извора који желиш да додаш, одвојен размаком.", "Unable to find package": "Није могуће пронаћи пакет", "Unable to load informarion": "Није могуће учитати информације", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI није повезан ни са једним од компатибилних менаџера пакета. UniGetUI је независан пројекат.", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "Деинста<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Деинстал<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (и његове пакете)", "Uninstall and more": null, "Uninstall and remove data": "Деинсталирај и уклони податке", "Uninstall as administrator": "Деинстали<PERSON><PERSON>ј као администратор", "Uninstall canceled by the user!": "Деинсталација је отказана од стране корисника!", "Uninstall failed": "Неуспешно деинсталирање", "Uninstall options": null, "Uninstall package": "Деинстали<PERSON><PERSON><PERSON> пакет", "Uninstall package, then reinstall it": "Уклони пакет, затим га поново инсталирај", "Uninstall package, then update it": "Уклони пакет, затим га ажурирај", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Деинсталир<PERSON>ј изабране пакете", "Uninstall selection": null, "Uninstall succeeded": "Успешно деинсталирање", "Uninstall the selected packages with administrator privileges": "Деинсталирајте изабране пакете с администраторским привилегијама", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Пакети који се могу деинсталирати са наведеним пореклом \"{0}\" нису објављени на ниједном менаџеру пакета, тако да нема доступних информација о њима.", "Unknown": "Непознато", "Unknown size": "Непозната величина", "Unset or unknown": null, "Up to date": null, "Update": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Аутоматски ажурирајте WingetUI", "Update all": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> све", "Update and more": null, "Update as administrator": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> као администратор", "Update check frequency, automatically install updates, etc.": null, "Update checking": null, "Update date": "Датум ажурирања", "Update failed": "Неуспешно ажурирање", "Update found!": "Ажурирање пронађено!", "Update now": "А<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> одмах", "Update options": null, "Update package indexes on launch": "Ажу<PERSON>и<PERSON>ај индексе пакета при покретању", "Update packages automatically": "Аутоматски ажурирај пакете", "Update selected packages": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> изабране пакете", "Update selected packages with administrator privileges": "Ажурирајте изабране пакете с администраторским привилегијама", "Update selection": null, "Update succeeded": "Успешно ажурирање", "Update to version {0}": null, "Update to {0} available": "Ажу<PERSON><PERSON>р<PERSON>ње на {0} доступно", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "Ажурирања", "Updates available!": "Ажурирања доступна!", "Updates for this package are ignored": "Ажурирања за овај пакет се игноришу", "Updates found!": "Ажурирања пронађена!", "Updates preferences": "Преференце ажурирања", "Updating WingetUI": "Ажурирање WingetUI", "Url": "<PERSON><PERSON><PERSON><PERSON>", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Користи застарели упаковани WinGet уместо PowerShell CMDLeт-ова", "Use a custom icon and screenshot database URL": "Користите URL са прилагођеним иконама и снимцима екрана", "Use bundled WinGet instead of PowerShell CMDlets": "Користи упаковани WinGet уместо системских PowerShell CMDlet-ова", "Use bundled WinGet instead of system WinGet": "Користи упаковани WinGet уместо системског", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Користите инсталирани GSudo уместо привезаног (захтева поновно покретање апликације)", "Use system Chocolatey": "Користи системски Chocolatey", "Use system Chocolatey (Needs a restart)": "Користите системски Chocolatey (Захтева поновно покретање)", "Use system Winget (Needs a restart)": "Користите системски Winget (Захтева поновно покретање)", "Use system Winget (System language must be set to english)": "Користи системски Winget (језик система мора бити подешен на енглески)", "Use the WinGet COM API to fetch packages": "Користи WinGet COM АПИ за добављање пакета", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Користи WinGet PowerShell модул уместо WinGet COM АПИ-ја\n", "Useful links": "Корисни линкови", "User": "Корисник", "User interface preferences": "Поставке корисничког интерфејса", "User | Local": "Корисник | Локално", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Коришћење WingetUI подразумева прихватање GNU Lesser General Public License v2.1 лиценце", "Using WingetUI implies the acceptation of the MIT License": "Коришћење WingetUI подразумева прихватање MIT лиценце", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": "Пуно детаља", "Version": "Верзија", "Version to install:": "Верзија за инсталацију:", "Version:": null, "View GitHub Profile": "Види GitH<PERSON> Профил", "View WingetUI on GitHub": "Погледајте WingetUI на GitHub-у", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Погледајте изворни код WingetUI-а. Одатле можете пријавити грешке или предложити функције, а чак и допринети директно пројекту WingetUI", "View mode:": null, "View on UniGetUI": null, "View page on browser": "Погледај страницу у претраживачу", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "Чекање да друге инсталације заврше...", "Waiting for {0} to complete...": null, "Warning": "Упозорење", "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": "Нисмо могли учитати детаљне информације о овом пакету јер га нисмо пронашли у ниједном од ваших извора пакета", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Нисмо могли учитати детаљне информације о овом пакету јер није инсталиран из доступног менаџера пакета.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Нисмо могли {action} {package}. Покушајте поново касније. Кликните на „{showDetails}“ да бисте добили логове од инсталатера.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Нисмо могли {action} {package}. Покушајте поново касније. Кликните на „{showDetails}“ да бисте добили логове од деинсталатера.", "We couldn't find any package": "Нисмо могли да нађемо ниједан пакет", "Welcome to WingetUI": "Добродошли у WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Које менаџере пакета желите да користите?", "Which source do you want to add?": "Који извор желите да додате?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Док се Winget може користити унутар WingetUI, WingetUI се може користити са другим менаџерима пакета, што може бити збуњујуће. У прошлости, WingetUI је био дизајниран да ради само са Winget-ом, али то више није случај, па стога WingetUI не представља оно што овај пројекат жели да постане.\n", "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - Све је ажурно", "WingetUI - {0} updates are available": "WingetUI - Досту<PERSON>но је {0} ажурирања", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI Почетна страница", "WingetUI Homepage - Share this link!": "WingetUI Почетна страница - Подели овај линк!", "WingetUI License": "WingetUI Лиценца", "WingetUI Log": "WingetUI Логови", "WingetUI Repository": "WingetUI Репозиторијум", "WingetUI Settings": "Поставке WingetUI", "WingetUI Settings File": "Датотека поставки WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI Користи следеће библиотеке. Без њих, WingetUI не би био могућ.", "WingetUI Version {0}": "WingetUI Верзија {0}", "WingetUI autostart behaviour, application launch settings": "Понашање автостарта WingetUI-а, поставке покретања апликације", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI може проверити да ли ваш софтвер има доступна ажурирања и аутоматски их инсталирати ако желите", "WingetUI display language:": "Језик приказивања WingetUI-а:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI  је покренут као администратор, што се не препоручује. Када покренете WingetUI као администратор, СВАКА операција покренута из WingetUI имаће администраторске привилегије. И даље можете да користите програм, али јако препоручујемо да не покрећете WingetUI са администраторским привилегијама.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI је преведен на више од 40 језика захваљујући преводиоцима волонтерима. Хвала вам 🤝\n", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI није преведен машински. Следећи корисници били су одговорни за преводе:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI је апликација која олакшава управљање вашим софтвером, пружајући свеобухватни графички интерфејс за ваше менаџере пакета на командној линији.\n", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI мења име како би се нагласила разлика између WingetUI (интерфејса који тренутно користите) и Winget (менаџер пакета који је развио Микрософт са којим нисам повезан)\n", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI се ажурира. Када заврши, WingetUI ће се сам поново покренути", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI је бесплатан и биће бесплатан заувек. Без реклама, без кредитне картице, без премијум верзије. 100% бесплатно, заувек.", "WingetUI log": "<PERSON><PERSON><PERSON>etUI-а", "WingetUI tray application preferences": "Поставке треј апликације WingetUI-а", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI користи следеће библиотеке. Без њих, WingetUI не би био могућ.", "WingetUI version {0} is being downloaded.": "WingetUI верзија {0} се преузима.", "WingetUI will become {newname} soon!": "WingetUI ће ускоро постати {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI неће периодично проверавати ажурирања. Ипак, провераваће се приликом покретања, али нећете бити упозорени на њих.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI ће приказивати UAC упитник сваки пут када пакет захтева повишени статус за инсталацију.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI ће ускоро добити назив {newname}. Ово неће представљати никакву промену у апликацији. Ја (програмер) ћу наставити развој овог пројекта као што радим и сада, али под другим именом.\n", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI не би био могућ без помоћи наших драгих доприносилаца. Погледајте њихови GitHub профиле, WingetUI не би био могућ без њих!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI не би био могућ без помоћи доприносиоца. Хвала свима 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} је спреман за инсталирање.", "Write here the process names here, separated by commas (,)": null, "Yes": "Да", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "Тренутно имате инсталирану верзију {0}", "You have installed WingetUI Version {0}": "Инсталир<PERSON><PERSON>и сте WingetUI Верзију {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "Можда ћете морати да инсталирате {pm} да бисте га користили са WingetUI.", "You may restart your computer later if you wish": "Можете поново покренути рачунар касније ако желите", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Бићете упитани само једном, и администраторска права биће додељена пакетима који их захтевају.", "You will be prompted only once, and every future installation will be elevated automatically.": "Бићете упитани само једном, и свака будућа инсталација биће аутоматски повишена.", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": "ПОКРЕНУТО КАО АДМИНИСТРАТОР", "buy me a coffee": "купи ми кафу", "extracted": "извучено", "feature": null, "formerly WingetUI": "раније WingetUI", "homepage": "почетна страна", "install": "инстал<PERSON><PERSON><PERSON><PERSON>", "installation": "инсталација", "installed": "инсталирано", "installing": "инсталирање", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "деинста<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstallation": "деинсталација", "uninstalled": "деинсталирано", "uninstalling": "деинстал<PERSON>р<PERSON>ње", "update(noun)": "ажурирање (именица)", "update(verb)": "ажур<PERSON>р<PERSON>ње (глагол)", "updated": "ажу<PERSON><PERSON><PERSON><PERSON>но", "updating": "ажур<PERSON>р<PERSON><PERSON>е", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} Деинсталација", "{0} aborted": "{0} прекинуто", "{0} can be updated": "{0} може бити ажурирано", "{0} can be updated to version {1}": "{0} може бити ажуриран на верзију {1}", "{0} days": "{0} дана", "{0} desktop shortcuts created": null, "{0} failed": "{0} није успјело.", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": "{0} главна страна", "{0} hours": "{0} сати", "{0} installation": "{0} инсталација", "{0} installation options": "{0} опције за инсталацију", "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} се ажурира", "{0} is being updated to version {1}": "{0} се ажурира на верзију {1}", "{0} is disabled": "{0} је онемогућен", "{0} minutes": "{0} минута", "{0} months": null, "{0} packages are being updated": "Ажу<PERSON>ира се {0} пакета", "{0} packages can be updated": "{0} пакета се може ажурирати", "{0} packages found": "Нађено је {0} пакета", "{0} packages were found": "Нађено је {0} пакета", "{0} packages were found, {1} of which match the specified filters.": "{0} пакета је пронађено, {1} од којих одговара наведеним филтерима.", "{0} selected": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} је успјешно", "{0} update": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "{0} updates are available": "{0} ажурирања је доступно", "{0} was {1} successfully!": "{0} је успешно {1}!", "{0} weeks": null, "{0} years": null, "{0} {1} failed": "{0} {1} није успјело", "{package} Installation": "{package} Инсталација", "{package} Uninstall": "{package} Деинсталир<PERSON>ј", "{package} Update": "{package} Ажури<PERSON><PERSON>ј", "{package} could not be installed": "{package} није могуће инсталирати", "{package} could not be uninstalled": "{package} није могуће деинсталирати", "{package} could not be updated": "{package} није могуће ажурирати", "{package} installation failed": "{package} неуспешна инсталација", "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": "{package} деинсталирање није успело", "{package} update failed": "{package} ажурирање није успело", "{package} update failed. Click here for more details.": "{package} ажурирање није успело. Кликните овде за више детаља.", "{package} was installed successfully": "{package} је успешно инсталиран", "{package} was uninstalled successfully": "{package} је успешно деинсталиран", "{package} was updated successfully": "{package} је успешно ажуриран", "{pcName} installed packages": "{pcName} инсталирани пакети", "{pm} could not be found": "{pm} није могао бити пронађен", "{pm} found: {state}": "{pm} пронађено: {state}", "{pm} is disabled": "{pm} је онемогућен", "{pm} is enabled and ready to go": "{pm} је омогућен и спреман за рад", "{pm} package manager specific preferences": "Специфични кориснички предлози за {pm}", "{pm} preferences": "Поставке за {pm}", "{pm} version:": "{pm} верзија:", "{pm} was not found!": "{pm} није пронађен!"}