{"\"{0}\" is a local package and can't be shared": "\"{0}\" является локальным пакетом и не может быть передан", "\"{0}\" is a local package and does not have available details": "\"{0}\" является локальным пакетом, и сведения о нём недоступны", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" является локальным пакетом и не поддерживает эту функцию", "(Last checked: {0})": "(Проверено: {0})", "(Number {0} in the queue)": "({0} позиция в очереди)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON>, sklart, @flatron4eg, @bropines, @ka<PERSON><PERSON>, @<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, @<PERSON><PERSON><PERSON>, @solarscream, @tapnisu", "0 packages found": "Найдено 0 пакетов", "0 updates found": "Найдено 0 обновлений", "1 - Errors": "1 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "1 day": "1 день", "1 hour": "1 час", "1 month": "1 месяц", "1 package was found": "Найден 1 пакет", "1 update is available": "Доступно 1 обновление", "1 week": "1 неделя", "1 year": "1 год", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Перейдите на страницу \"{0}\" или \"{1}\".", "2 - Warnings": "2 - Предупреждения", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Найдите пакет(ы), которые вы хотите добавить в набор, и выберите самый левый флажок.", "3 - Information (less)": "3 - Информация (меньше)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. Когда пакеты, которые вы хотите добавить в набор, выбраны, найдите и нажмите на опцию \"{0}\" на панели инструментов.", "4 - Information (more)": "4 - Информация (больше)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Ваши пакеты будут добавлены в набор. Вы можете продолжить добавление пакетов или экспортировать набор.", "5 - information (debug)": "5 - Информация (отладка)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Популярный менеджер библиотек C/C++. Полный набор библиотек C/C++ и других утилит, связанных с C/C++<br>Содержит: <b>библиотеки C/C++ и связанные с ними утилиты</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Репозиторий, полный инструментов и исполняемых файлов, разработанных с учетом экосистемы Microsoft .NET.<br>Содержит: <b>инструменты и сценарии, связанные с .NET </b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Репозитор<PERSON>, полный инструментов, разработанных с учетом экосистемы Microsoft .NET. <br> Содержит: <b> инструменты связанные с .NET </b>", "A restart is required": "Требуется перезапуск", "Abort install if pre-install command fails": "Отменить установку при ошибке выполнения предустановочной команды", "Abort uninstall if pre-uninstall command fails": "Отменить удаление при ошибке выполнения предварительной команды", "Abort update if pre-update command fails": "Отменить обновление при ошибке выполнения предварительной команды", "About": "О приложении", "About Qt6": "О библиотеке Qt6", "About WingetUI": "О программе UniGetUI", "About WingetUI version {0}": "О программе UniGetUI версии {0}", "About the dev": "О разработчиках", "Accept": "Применить", "Action when double-clicking packages, hide successful installations": "Действие при двойном нажатии, исключая успешные установки", "Add": "Добавить", "Add a source to {0}": "Добавьте источник в {0}", "Add a timestamp to the backup file names": "Добавить временную метку к именам файлов резервных копий", "Add a timestamp to the backup files": "Добавлять временную метку к файлам резервных копий", "Add packages or open an existing bundle": "Добавьте пакеты или откройте существующий набор", "Add packages or open an existing package bundle": "Добавьте пакеты или откройте существующий набор пакетов", "Add packages to bundle": "Добавить пакеты в набор", "Add packages to start": "Добавьте пакеты для начала", "Add selection to bundle": "Добавить выбранное в набор", "Add source": "Добавить источник", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Добавить обновления, которые завершаются ошибкой с сообщением \"не найдено подходящего обновления\", в список игнорируемых обновлений", "Adding source {source}": "Добавление источника {source}", "Adding source {source} to {manager}": "Добавление источника {source} в {manager}", "Addition succeeded": "Добавление выполнено успешно", "Administrator privileges": "Права администратора", "Administrator privileges preferences": "Настройки прав администратора", "Administrator rights": "Права администратора", "Administrator rights and other dangerous settings": "Права администратора и другие чувствительные настройки", "Advanced options": "Расширенные настройки", "All files": "Все файлы", "All versions": "Все версии", "Allow changing the paths for package manager executables": "Разрешить изменять каталоги исполняемых файлов менеджера пакетов", "Allow custom command-line arguments": "Допускать пользовательские аргументы командной строки", "Allow importing custom command-line arguments when importing packages from a bundle": "Разрешить импорт пользовательских аргументов командной строки при импорте пакетов из бандла", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Разрешить импорт пользовательских пред- и постустановочных команд при импорте пакетов из комплекта", "Allow package operations to be performed in parallel": "Позволить выполнять операции с пакетами параллельно", "Allow parallel installs (NOT RECOMMENDED)": "Разрешить параллельные установки (НЕ РЕКОМЕНДУЕТСЯ)", "Allow pre-release versions": "Разрешить обновление до предварительных версий", "Allow {pm} operations to be performed in parallel": "Разрешить {pm} параллельное выполнение операций ", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "В качестве альтернативы Вы также можете установить {0} запустив следующую команду из командной строки PowerShell:", "Always elevate {pm} installations by default": "Всегда повышать права установки {pm} по умолчанию", "Always run {pm} operations with administrator rights": "Всегда выполнять {pm} операции с правами администратора", "An error occurred": "Произошла ошибка", "An error occurred when adding the source: ": "Ошибка при добавлении источника:", "An error occurred when attempting to show the package with Id {0}": "Ошибка при попытке показать пакет с ID {0} ", "An error occurred when checking for updates: ": "Ошибка при проверке обновлений:", "An error occurred while loading a backup: ": "Ошибка при загрузке резервной копии:", "An error occurred while logging in: ": "Ошибка при авторизации:", "An error occurred while processing this package": "Произошла ошибка при обработке этого пакета", "An error occurred:": "Возникла ошибка:", "An interal error occurred. Please view the log for further details.": "Внутренняя ошибка. Пожалуйста, ознакомьтесь с журналом для получения более подробной информации.", "An unexpected error occurred:": "Произошла непредвиденная ошибка:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Неожиданная проблема во время восстановления WinGet. Пожалуйста, попробуйте запустить восстановление позднее", "An update was found!": "Найдено обновление!", "Android Subsystem": "Подсистема Android", "Another source": "Другой источник", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Все новые ярлыки, созданные во время установки или обновления, будут автоматически удалены, вместо того чтобы показывать запрос на подтверждение при их первом обнаружении.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Все ярлыки, созданные или измененные вне UniGetUI, будут проигнорированы. Вы сможете добавить их через кнопку {0}.", "Any unsaved changes will be lost": "Любые несохраненные изменения будут потеряны", "App Name": "Название приложения", "Appearance": "Вн<PERSON><PERSON>ний вид", "Application theme, startup page, package icons, clear successful installs automatically": "Тема приложения, стартовая страница, значки пакетов, автоматическая очистка успешных установок", "Application theme:": "Тема оформления:", "Apply": "Применить", "Architecture to install:": "Архитектура для установки:", "Are these screenshots wron or blurry?": "Эти скриншоты неправильные или размытые?", "Are you really sure you want to enable this feature?": "Вы действительно уверены, что хотите включить эту функцию?", "Are you sure you want to create a new package bundle? ": "Вы уверены, что хотите создать новый набор пакетов?", "Are you sure you want to delete all shortcuts?": "Вы уверены, что хотите удалить все ярлыки?", "Are you sure?": "Вы уверены?", "Ascendant": "По возрастанию", "Ask for administrator privileges once for each batch of operations": "Запрашивать права администратора для каждого пакета операций", "Ask for administrator rights when required": "Запрашивать права администратора при необходимости", "Ask once or always for administrator rights, elevate installations by default": "Запрашивать права администратора единожды или всегда, повышая права установки по умолчанию", "Ask only once for administrator privileges": "Запрашивать права администратора один раз", "Ask only once for administrator privileges (not recommended)": "Запрашивать права администратора единожды (не рекомендуется)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Спрашивать об удалении ярлыков на рабочем столе, созданных во время установки или обновления.", "Attention required": "Требуется внимание пользователя", "Authenticate to the proxy with an user and a password": "Авторизоваться в прокси с помощью имени пользователя и пароля", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": "Автоматическое удаление ярлыков на рабочем столе", "Automatically save a list of all your installed packages to easily restore them.": "Автоматически сохраняйте список всех установленных вами пакетов, чтобы легко восстановить их.", "Automatically save a list of your installed packages on your computer.": "Автоматическое сохранение списка установленных пакетов на вашем компьютере.", "Autostart WingetUI in the notifications area": "Автозапуск UniGetUI в области уведомлений", "Available Updates": "Доступные обновления", "Available updates: {0}": "Доступно обновлений: {0}", "Available updates: {0}, not finished yet...": "Доступно обновлений: {0}, еще не завершено...", "Backing up packages to GitHub Gist...": "Резервное копирование пакетов в сервис GitHub Gist...", "Backup": "Резервная копия", "Backup Failed": "Ошибка резервного копирования", "Backup Successful": "Резервное копирование завершено успешно", "Backup and Restore": "Резервная копия и восстановление", "Backup installed packages": "Резервное копирование установленных пакетов", "Backup location": "Расположение резервной копии", "Become a contributor": "Стать участником", "Become a translator": "Стать переводчиком", "Begin the process to select a cloud backup and review which packages to restore": "Выбрать облачное хранилище и пакеты для восстановления", "Beta features and other options that shouldn't be touched": "Функции бета-версии и другие настройки, которые не стоит изменять", "Both": "Оба", "Bundle security report": "Сводный отчет по безопасности", "But here are other things you can do to learn about WingetUI even more:": "Но вот что еще можно сделать, чтобы узнать о UniGetUI еще больше:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Отключив менеджер пакетов, вы больше не сможете видеть и обновлять его пакеты.", "Cache administrator rights and elevate installers by default": "Запоминать права администратора и повышать права установщиков по умолчанию", "Cache administrator rights, but elevate installers only when required": "Запоминать права администратора, но повышать права установщиков только при необходимости", "Cache was reset successfully!": "Кэш успешно сброшен!", "Can't {0} {1}": "Невозможно {0} {1}", "Cancel": "Отмена", "Cancel all operations": "Отменить все операции", "Change backup output directory": "Изменить каталог для резервных копий", "Change default options": "Изменить настройки по умолчанию", "Change how UniGetUI checks and installs available updates for your packages": "Изменение способа проверки и установки доступных обновлений для пакетов в UniGetUI", "Change how UniGetUI handles install, update and uninstall operations.": "Изменить управление операциями установки, обновления и удаления.", "Change how UniGetUI installs packages, and checks and installs available updates": "Изменение механизма получения пакетов обновлений, проверка и установка доступных обновлений", "Change how operations request administrator rights": "Изменить как операции запрашивают права администратора", "Change install location": "Измените место установки", "Change this": "Изменить это", "Change this and unlock": "Изменить это и разблокировать", "Check for package updates periodically": "Периодически проверять наличие обновлений пакетов", "Check for updates": "Проверка обновлений", "Check for updates every:": "Интервал проверки обновлений:", "Check for updates periodically": "Проверять обновления периодически", "Check for updates regularly, and ask me what to do when updates are found.": "Проверять обновления регулярно и спрашивать меня, что делать при их обнаружении.", "Check for updates regularly, and automatically install available ones.": "Регулярно проверять наличие обновлений и автоматически устанавливать доступные.", "Check out my {0} and my {1}!": "Посетите мой {0} и мой {1}!", "Check out some WingetUI overviews": "Ознакомьтесь с некоторыми обзорами UniGetUI", "Checking for other running instances...": "Проверка других запущенных экземпляров...", "Checking for updates...": "Проверка обновлений...", "Checking found instace(s)...": "Проверка найденных экземпляров...", "Choose how many operations shouls be performed in parallel": "Выбор количества одновременно выполняемых операций", "Clear cache": "Очистить кэш", "Clear finished operations": "Очистить завершенные операции", "Clear selection": "Снять выделение", "Clear successful operations": "Очистить успешно завершенные загрузки", "Clear successful operations from the operation list after a 5 second delay": "Очищать успешные операции из списка операций после 5-секундной задержки", "Clear the local icon cache": "Очистить локальный кэш иконок", "Clearing Scoop cache - WingetUI": "Очистка кэша Scoop - UniGetUI", "Clearing Scoop cache...": "Очистка кеша Scoop...", "Click here for more details": "Нажмите здесь для получения подробностей", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Нажмите \"Установить\" для начала процесса установки. Если Вы пропустите установку, UniGetUI может не работать должным образом ", "Close": "Закрыть", "Close UniGetUI to the system tray": "Закрыть UniGetUI в системный трей", "Close WingetUI to the notification area": "Закрывать UniGetUI в область уведомлений", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Облачное хранилище использует частный сервис GitHub Gist для хранения списка установленных пакетов", "Cloud package backup": "Резервное копирование в облако", "Command-line Output": "Вывод командной строки", "Command-line to run:": "Команда для запуска:", "Compare query against": "Сравнить запрос с", "Compatible with authentication": "Совместимо с аутентификацией", "Compatible with proxy": "Совместимо с прокси", "Component Information": "Информация о компонентах", "Concurrency and execution": "Параллелизм и выполнение", "Connect the internet using a custom proxy": "Подключиться к интернету, используя указанный прокси", "Continue": "Продолжить", "Contribute to the icon and screenshot repository": "Внести свой вклад в репозиторий значков и скриншотов", "Contributors": "Участники", "Copy": "Копировать", "Copy to clipboard": "Копировать в буфер обмена", "Could not add source": "Невозможно добавить источник", "Could not add source {source} to {manager}": "Не удалось добавить источник {source} в {manager}", "Could not back up packages to GitHub Gist: ": "Ошибка резервного копирования пакетов на сервис GitHub Gist:", "Could not create bundle": "Не удалось создать набор", "Could not load announcements - ": "Не удалось загрузить объявления -", "Could not load announcements - HTTP status code is $CODE": "Не удалось загрузить объявления - код состояния HTTP $CODE", "Could not remove source": "Невозможно удалить источник", "Could not remove source {source} from {manager}": "Не удалось удалить источник {source} из {manager}", "Could not remove {source} from {manager}": "Не удалось удалить {source} из {manager} ", "Credentials": "Учетные данные", "Current Version": "Текущая версия", "Current status: Not logged in": "Статус: вход не выполнен", "Current user": "Для текущего пользователя", "Custom arguments:": "Пользовательские аргументы:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Пользовательские аргументы командной строки:", "Custom install arguments:": "Пользовательские аргументы для установки:", "Custom uninstall arguments:": "Пользовательские аргументы для удаления:", "Custom update arguments:": "Пользовательские аргументы для обновления:", "Customize WingetUI - for hackers and advanced users only": "Настроить UniGetUI — для хакеров и экспертов", "DEBUG BUILD": "СБОРКА ДЛЯ ОТЛАДКИ", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ОТКАЗ ОТ ОТВЕТСТВЕННОСТИ: МЫ НЕ НЕСЕМ ОТВЕТСТВЕННОСТИ ЗА ЗАГРУЖАЕМЫЕ ПАКЕТЫ. ПОЖАЛУЙСТА, УСТАНАВЛИВАЙТЕ ТОЛЬКО ПРОВЕРЕННОЕ ПРОГРАММНОЕ ОБЕСПЕЧЕНИЕ.", "Dark": "Темная", "Decline": "Отменить", "Default": "По умолчанию", "Default installation options for {0} packages": "Настройки установки по умолчанию для {0} пакетов", "Default preferences - suitable for regular users": "Настройки по умолчанию - подходят для большинства пользователей", "Default vcpkg triplet": "Триплет vcpkg по умолчанию", "Delete?": "Удалить?", "Dependencies:": "Зависимости:", "Descendant": "По убыванию", "Description:": "Описание:", "Desktop shortcut created": "Ярлык на рабочем столе создан", "Details of the report:": "Детали отчета:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Разработка сложна, и это приложение бесплатное. Но если вам понравилось приложение, вы всегда можете <b>купить мне кофе</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Прямая установка при двойном щелчке по элементу на вкладке \"{discoveryTab}\" (без инф. о пакете)", "Disable new share API (port 7058)": "Отключить новое API для функции \"поделиться\" (порт 7058)", "Disable the 1-minute timeout for package-related operations": "Отключить 1-минутный тайм-аут для операций, связанных с пакетами", "Disclaimer": "Отказ от ответственности", "Discover Packages": "Доступные пакеты", "Discover packages": "Доступные пакеты", "Distinguish between\nuppercase and lowercase": "Различать верхний\nи нижний регистр", "Distinguish between uppercase and lowercase": "Различать прописные и строчные буквы", "Do NOT check for updates": "НЕ проверять наличие обновлений", "Do an interactive install for the selected packages": "Выполнить интерактивную установку для выбранных пакетов", "Do an interactive uninstall for the selected packages": "Выполнить интерактивное удаление для выбранных пакетов", "Do an interactive update for the selected packages": "Выполнить интерактивное обновление для выбранных пакетов", "Do not automatically install updates when the battery saver is on": "Не устанавливать обновления автоматически, когда включён режим экономии заряда батареи", "Do not automatically install updates when the network connection is metered": "Не устанавливать обновления автоматически при использовании лимитированного подключения", "Do not download new app translations from GitHub automatically": "Не скачивать новые переводы с GitHub автоматически", "Do not ignore updates for this package anymore": "Больше не игнорировать обновления для этого пакета", "Do not remove successful operations from the list automatically": "Не удалять успешные операции из списка автоматически\n", "Do not show this dialog again for {0}": "Не показывать диалог снова для {0}", "Do not update package indexes on launch": "Не обновлять индексы пакетов при запуске", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Разрешаете ли Вы UniGetUI сбор и отправку анонимной статистики использования с целью изучения и улучшения пользовательского опыта?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Считаете ли вы UniGetUI полезным? Если, вы захотите поддержать мою работу, чтобы я мог продолжать делать UniGetUI идеальным интерфейсом для управления пакетами.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Считаете ли вы UniGetUI полезным? Хотите поддержать разработчика? Если это так, вы можете {0}, это очень поможет!", "Do you really want to reset this list? This action cannot be reverted.": "Вы действительно хотите сбросить этот список? Это действие нельзя отменить.", "Do you really want to uninstall the following {0} packages?": "Вы действительно хотите удалить {0, plural, one {следующий {0} пакет} few {следующие {0} пакета} other {следующие {0} пакетов}}?", "Do you really want to uninstall {0} packages?": "Вы действительно хотите удалить {0} {0, plural, one {пакет} few {пакета} other {пакетов}}?", "Do you really want to uninstall {0}?": "Вы действительно хотите удалить {0}?", "Do you want to restart your computer now?": "Вы хотите перезагрузить компьютер сейчас?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Хотите перевести UniGetUI на свой язык? Узнайте, как внести свой вклад <a style=\"color:{0}\" href=\"{1}\"a>ЗДЕСЬ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Не хотите делать пожертвование? Не волнуйтесь, вы всегда можете поделиться WingetUI со своими друзьями. Расскажите о WingetUI.", "Donate": "Пожертвовать", "Done!": "Готово!", "Download failed": "Ошибка загрузки", "Download installer": "Загрузить программу установки", "Download operations are not affected by this setting": "Этот параметр не влияет на операции загрузки", "Download selected installers": "Загрузить выбранные установщики", "Download succeeded": "Загрузка прошла успешно", "Download updated language files from GitHub automatically": "Автоматически скачивать обновленные языковые файлы с GitHub", "Downloading": "Загрузка", "Downloading backup...": "Загружаем резервную копию…", "Downloading installer for {package}": "Загрузка установщика для {package}", "Downloading package metadata...": "Загрузка метаданных пакета...", "Enable Scoop cleanup on launch": "Включить очистку Scoop при запуске", "Enable WingetUI notifications": "Включить уведомления WingetUI", "Enable an [experimental] improved WinGet troubleshooter": "Включить [экспериментальное] улучшенное средство устранения неполадок WinGet", "Enable and disable package managers, change default install options, etc.": "Включить и отключить менеджеры пакетов, изменить настройки установки по умолчанию и т.д.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Включить фоновую оптимизацию использования процессора", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Включить фоновый API (WingetUI виджеты и возможность поделиться, порт 7058)", "Enable it to install packages from {pm}.": "Включите его для установки пакетов из {pm}.", "Enable the automatic WinGet troubleshooter": "Включить автоматическое средство устранения неполадок WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Включить новый модуль управления учетными записями под брендом UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Включить новый обработчик входных данных (Std In automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Включайте настройки ниже только в том случае, если вы полностью осознаете их назначение и эффект от включения", "Enable {pm}": "Включить {pm}", "Enter proxy URL here": "Введите URL прокси здесь", "Entries that show in RED will be IMPORTED.": "Позиции, помеченные КРАСНЫМ цветом, будут ИМПОРТИРОВАНЫ", "Entries that show in YELLOW will be IGNORED.": "Записи, показанные ЖЕЛТЫМ цветом, будут ПРОИГНОРИРОВАНЫ.", "Error": "Ошибка", "Everything is up to date": "Все актуально", "Exact match": "Точное совпадение", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Существующие ярлыки на вашем рабочем столе будут отсканированы, и вам нужно будет выбрать, какие из них оставить, а какие удалить.", "Expand version": "Расширенная версия", "Experimental settings and developer options": "Экспериментальные настройки и опции разработчика", "Export": "Экспорт", "Export log as a file": "Экспорт журнала в файл", "Export packages": "Экспортировать пакеты", "Export selected packages to a file": "Экспорт выбранных пакетов в файл", "Export settings to a local file": "Экспорт настроек в файл", "Export to a file": "Экспорт в файл", "Failed": "Не удалось", "Fetching available backups...": "Получение доступных резервных копий...", "Fetching latest announcements, please wait...": "Чтобы получить последние объявления, пожалуйста, подождите...", "Filters": "Фильтры", "Finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Follow system color scheme": "Использовать системную цветовую схему", "Follow the default options when installing, upgrading or uninstalling this package": "Следовать настройкам по умолчанию при установке, обновлении или удалении этого пакета", "For security reasons, changing the executable file is disabled by default": "В целях безопасности изменение исполняемого файла отключено по умолчанию", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "В целях безопасности пользовательские аргументы командной строки отключены по умолчанию. Включить их можно в разделе настроек UniGetUI.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "В целях безопасности предварительные и завершающие скрипты отключены по умолчанию. Включить их можно в разделе настроек UnitGetUI.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Используйте версию winget, скомпилированную для ARM (ТОЛЬКО ДЛЯ СИСТЕМ ARM64)", "Formerly known as WingetUI": "Ранее известный как WingetUI", "Found": "Найден", "Found packages: ": "Найденные пакеты:", "Found packages: {0}": "Найдено пакетов: {0}", "Found packages: {0}, not finished yet...": "Найдено пакетов: {0}, еще не завершено...", "General preferences": "Общие настройки", "GitHub profile": "Про<PERSON><PERSON><PERSON><PERSON>", "Global": "Глобально", "Go to UniGetUI security settings": "Перейти в настройки безопасности UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Отличное хранилище малоизвестных, но полезных утилит и других интересных пакетов.<br>Содержит: <b>Утилиты, программы командной строки, общее программное обеспечение (требуется дополнительный бакет)</b>", "Great! You are on the latest version.": "Отлично! Вы используете последнюю версию.", "Grid": "Сетка", "Help": "Помощь", "Help and documentation": "Справка и документация", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Здесь вы можете изменить поведение UniGetUI в отношении следующих сочетаний клавиш. Проверка ярлыка приведет к тому, что UniGetUI удалит его, если он будет создан при будущем обновлении. Если снять флажок, ярлык останется нетронутым", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Привет, меня зовут Марти́, и я <i>разработчик</i> WingetUI. WingetUI был полностью сделан мной в свободное время!", "Hide details": "Скрыть детали", "Homepage": "Домашняя страница", "Hooray! No updates were found.": "Ура! Обновления не найдены!", "How should installations that require administrator privileges be treated?": "Как следует относиться к установкам, требующим прав администратора?", "How to add packages to a bundle": "Как добавить пакеты в набор", "I understand": "Я понимаю", "Icons": "Иконки", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Если включено резервное копирование в облако, копия будет сохранена как GitHub Gist в этом аккаунте", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Игнорировать пользовательские пред- и постустановочные команды при импорте пакетов из бандла", "Ignore future updates for this package": "Игнорировать будущие обновления этого пакета", "Ignore packages from {pm} when showing a notification about updates": "Игнорировать пакеты из {pm} при показе уведомления о обновлениях", "Ignore selected packages": "Игнорировать выбранные пакеты", "Ignore special characters": "Игнорировать специальные\nсимволы", "Ignore updates for the selected packages": "Игнорировать обновления выбранных пакетов", "Ignore updates for this package": "Игнорировать обновления для этого пакета", "Ignored updates": "Игнорируемые обновления", "Ignored version": "Игнорируемая версия", "Import": "Импорт", "Import packages": "Импортировать пакеты", "Import packages from a file": "Импорт пакетов из файла", "Import settings from a local file": "Импорт настроек из файла", "In order to add packages to a bundle, you will need to: ": "Чтобы добавить пакеты в набор, вам нужно:", "Initializing WingetUI...": "Запуск WingetUI...", "Install": "Установить", "Install Scoop": "Установить Scoop", "Install and more": "Установить и более", "Install and update preferences": "Настройки установки и обновления", "Install as administrator": "Установить от имени администратора", "Install available updates automatically": "Автоматически устанавливать доступные обновления", "Install location can't be changed for {0} packages": "Расположение установки не может быть изменено для {0} пакетов", "Install location:": "Место установки:", "Install options": "Настройки установки", "Install packages from a file": "Установить пакеты из файла", "Install prerelease versions of UniGetUI": "Устанавливать предварительные версии UniGetUI", "Install selected packages": "Установить выбранные пакеты", "Install selected packages with administrator privileges": "Установить выбранные пакеты с правами администратора", "Install selection": "Выбор установки", "Install the latest prerelease version": "Установка последней предварительной версии", "Install updates automatically": "Устанавливать обновления автоматически", "Install {0}": "Установить {0}", "Installation canceled by the user!": "Установка отменена пользователем!", "Installation failed": "Ошибка установки", "Installation options": "Опции установки", "Installation scope:": "Область установки:", "Installation succeeded": "Установка выполнена успешно", "Installed Packages": "Установленные пакеты", "Installed Version": "Установленная версия", "Installed packages": "Установленные пакеты", "Installer SHA256": "SHA256 установщика", "Installer SHA512": "Хэш установщика SHA512", "Installer Type": "Тип установщика", "Installer URL": "URL установщика", "Installer not available": "Установщик недоступен", "Instance {0} responded, quitting...": "Получен ответ от инстанса {0}, закрытие...", "Instant search": "Искать незамедлительно", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "Проверка целостности пропущена", "Integrity checks will not be performed during this operation": "Проверка целостности не будет выполнена во время этой операции", "Interactive installation": "Интерактивная установка", "Interactive operation": "Интерактивное управление", "Interactive uninstall": "Интерактивное удаление", "Interactive update": "Интерактивное обновление", "Internet connection settings": "Настройка подключения к интернету", "Is this package missing the icon?": "В этом пакете отсутствует значок?", "Is your language missing or incomplete?": "Ваш язык отсутствует или является неполным?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Не гарантированно, что предоставленные учётные данные будут храниться в безопасности, поэтому лучше не использовать учётные данные от вашего банковского счёта.", "It is recommended to restart UniGetUI after WinGet has been repaired": "Рекомендуется перезапустить UniGetUI после восстановления WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Похоже, WinGet не работает должным образом. Хотите ли Вы попробовать исправить WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Похоже, вы запустили WingetUI от имени администратора, что не рекомендуется. Вы по-прежнему можете использовать программу, но мы настоятельно рекомендуем не запускать WingetUI с правами администратора. Нажмите \"{showDetails}\", чтобы узнать почему.", "Language": "Язык", "Language, theme and other miscellaneous preferences": "Язык, тема и другие настройки", "Last updated:": "Последнее обновление:", "Latest": "Последний", "Latest Version": "Последняя версия", "Latest Version:": "Последняя версия:", "Latest details...": "Последние детали...", "Launching subprocess...": "Запуск подпроцесса...", "Leave empty for default": "Оставьте пустым по умолчанию", "License": "Лицензия", "Licenses": "Лицензии", "Light": "Светлая", "List": "Список", "Live command-line output": "Вывод командной строки", "Live output": "Вывод в реальном времени", "Loading UI components...": "Загрузка UI-компонентов...", "Loading WingetUI...": "Загрузка WingetUI...", "Loading packages": "Загрузка пакетов", "Loading packages, please wait...": "Загрузка пакетов, пожалуйста, подождите...", "Loading...": "Загрузка...", "Local": "Локально", "Local PC": "Этот ПК", "Local backup advanced options": "Расширенные настройки локального резервного копирования", "Local machine": "Для всех пользователей", "Local package backup": "Резервное копирование локального пакета", "Locating {pm}...": "Поиск {pm}...", "Log in": "Вход", "Log in failed: ": "Ошибка входа:", "Log in to enable cloud backup": "Войдите для включения облачного резервного копирования", "Log in with GitHub": "Войти используя GitHub", "Log in with GitHub to enable cloud package backup.": "Войдите с GitHub для включения облачного резервного копирования пакета.", "Log level:": "Уровень журнала:", "Log out": "Выйти", "Log out failed: ": "Ошибка выхода:", "Log out from GitHub": "Выйти из GitHub", "Looking for packages...": "Ищу...", "Machine | Global": "Устройство | Глобально", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "Управление", "Manage UniGetUI settings": "Управление настройками UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Настроить поведение автозапуска WingetUI из приложения \"Параметры\"", "Manage ignored packages": "Управление игнорируемыми пакетами", "Manage ignored updates": "Управление игнорируемыми обновлениями", "Manage shortcuts": "Управление ярлыками", "Manage telemetry settings": "Управление настройками телеметрии", "Manage {0} sources": "Управление источниками {0}", "Manifest": "Манифест", "Manifests": "Манифесты", "Manual scan": "Ручное сканирование", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Официальный пакетный менеджер Microsoft. Полон хорошо известных и проверенных пакетов<br>Содержит: <b>Общее программное обеспечение, приложения Microsoft Store</b>", "Missing dependency": "Отсутствующие зависимости", "More": "Еще", "More details": "Более подробная информация", "More details about the shared data and how it will be processed": "Больше информации о передаваемых данных и их обработке", "More info": "Дополнительная информация", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "ПРИМЕЧАНИЕ: Это средство устранения неполадок можно отключить в настройках UniGetUI в разделе WinGet", "Name": "Название", "New": "Новое", "New Version": "Новая версия", "New bundle": "Новый набор", "New version": "Новая версия", "Nice! Backups will be uploaded to a private gist on your account": "Резервные копии будут загружены в приватный Gist на вашем аккаунте", "No": "Нет", "No applicable installer was found for the package {0}": "Не найден подходящий установщик для пакета {0}", "No dependencies specified": "Не указаны зависимости", "No new shortcuts were found during the scan.": "Во время сканирования не было обнаружено новых ярлыков.", "No packages found": "Пакеты не найдены", "No packages found matching the input criteria": "Пакеты, соответствующие введенным критериям, не найдены", "No packages have been added yet": "Пакеты еще не добавлены", "No packages selected": "Нет выбранных пакетов", "No packages were found": "Пакеты не найдены", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Сбор и обработка персональных данных не производятся. Собранные данные анонимизированы, поэтому по ним невозможно определить Вашу личность", "No results were found matching the input criteria": "Не найдено результатов, соответствующих критериям ввода", "No sources found": "Источники не найдены", "No sources were found": "Источники не найдены", "No updates are available": "Нет доступных обновлений", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Пакетный менеджер NodeJS. Наполнен библиотеками и другими утилитами, которые вращаются вокруг мира javascript<br>Содержит: <b>Библиотеки Node javascript и другие связанные с ними утилиты</b>", "Not available": "Не доступно", "Not finding the file you are looking for? Make sure it has been added to path.": "Не можете найти файл? Убедитесь, что он был добавлен в путь.", "Not found": "Не найден", "Not right now": "Не сейчас", "Notes:": "Примечания:", "Notification preferences": "Параметры уведомлений", "Notification tray options": "Параметры панели уведомлений", "Notification types": "Типы уведомлений", "NuPkg (zipped manifest)": "NuPkg (заархивированный манифест)", "OK": "OK", "Ok": "Ok", "Open": "Открыть", "Open GitHub": "Отк<PERSON><PERSON><PERSON><PERSON> GitHub", "Open UniGetUI": "Открыть UniGetUI", "Open UniGetUI security settings": "Открыть настройки безопасности UniGetUI", "Open WingetUI": "Открыть WingetUI", "Open backup location": "Открыть хранилище резервных копий", "Open existing bundle": "Открыть существующий набор", "Open install location": "Открыть папку установки", "Open the welcome wizard": "Открыть мастер начальной настройки", "Operation canceled by user": "Операция отменена пользователем", "Operation cancelled": "Операция отменена", "Operation history": "История действий", "Operation in progress": "В процессе...", "Operation on queue (position {0})...": "Операция в очереди (позиция {0})...", "Operation profile:": "Профиль операции:", "Options saved": "Параметры сохранены", "Order by:": "Сортировать по:", "Other": "Друг<PERSON>й", "Other settings": "Другие настройки", "Package": "Пак<PERSON>т", "Package Bundles": "Наборы пакетов", "Package ID": "ID пакета", "Package Manager": "Менед<PERSON><PERSON><PERSON> пакетов", "Package Manager logs": "Журналы событий пакетного менеджера", "Package Managers": "Менеджеры пакетов", "Package Name": "Название пакета", "Package backup": "Резервная копия пакета", "Package backup settings": "Настройки резервного копирования пакета", "Package bundle": "Набор пакетов", "Package details": "Информация о пакете", "Package lists": "Списки пакетов", "Package management made easy": "Управление пакетами без усилий", "Package manager": "Менед<PERSON><PERSON><PERSON> пакетов", "Package manager preferences": "Настройки менеджеров пакетов", "Package managers": "Менеджеры пакетов", "Package not found": "Пакет не найден", "Package operation preferences": "Настройка операций с пакетами", "Package update preferences": "Настройка обновлений пакетов", "Package {name} from {manager}": "<PERSON>а<PERSON><PERSON><PERSON> {name} из {manager} ", "Package's default": "По умолчанию для пакета", "Packages": "Пакеты", "Packages found: {0}": "Найдено пакетов: {0}", "Partially": "Частично", "Password": "Пароль", "Paste a valid URL to the database": "Вставьте действительный URL-адрес в базу данных", "Pause updates for": "Приостановить обновления для", "Perform a backup now": "Выполнить резервное копирование сейчас", "Perform a cloud backup now": "Выполнить резервное копирование в облако сейчас", "Perform a local backup now": "Выполнить локальное резервное копирование сейчас", "Perform integrity checks at startup": null, "Performing backup, please wait...": "Создается резервная копия, пожалуйста, подождите...", "Periodically perform a backup of the installed packages": "Периодически выполнять резервное копирование установленных пакетов", "Periodically perform a cloud backup of the installed packages": "Периодически выполнять облачное резервное копирование установленных пакетов", "Periodically perform a local backup of the installed packages": "Периодически выполнять локальное резервное копирование установленных пакетов", "Please check the installation options for this package and try again": "Пожалуйста, проверьте параметры установки этого пакета и повторите попытку", "Please click on \"Continue\" to continue": "Пожалуйста, нажмите «Продолжить», чтобы продолжить", "Please enter at least 3 characters": "Пожалуйста, введите не менее 3 символов", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Учтите, что некоторые пакеты могут быть недоступны для установки из-за включенных на этом компьютере менеджеров пакетов.", "Please note that not all package managers may fully support this feature": "Обратите внимание, что не все менеджеры пакетов могут полностью поддерживать эту функцию", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Учтите, что пакеты из определенных источников могут быть недоступны для экспорта. Они были выделены серым цветом и не будут экспортированы.", "Please run UniGetUI as a regular user and try again.": "Пожалуйста, запустите UniGetUI как обычный пользователь и повторите попытку.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Пожалуйста, посмотрите вывод командной строки или обратитесь к истории операций для получения дополнительной информации о проблеме.", "Please select how you want to configure WingetUI": "Пожалуйста, выберите желаемые настройки WingetUI", "Please try again later": "Пожалуйста, попробуйте ещё раз позже", "Please type at least two characters": "Пожалуйста, введите не менее двух символов", "Please wait": "Пожалуйста подождите", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Пожалуйста, подождите, пока выполняется установка {0}. Во время установки может появляться черное окно. Ожидайте его закрытия", "Please wait...": "Пожалуйста, подождите...", "Portable": "Портативный", "Portable mode": "Портативный режим", "Post-install command:": "Команда после установки:", "Post-uninstall command:": "Команда после удаления:", "Post-update command:": "Команда после обновления:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Менеджер пакетов PowerShell. Поиск библиотек и сценариев для расширения возможностей PowerShell<br>Содержит: <b>Модули, скрипты, команды</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": "Команда перед установкой:", "Pre-uninstall command:": "Команда перед удалением:", "Pre-update command:": "Команда перед обновлением:", "PreRelease": "Предварительный релиз", "Preparing packages, please wait...": "Подготовка пакетов, пожалуйста, подождите...", "Proceed at your own risk.": "Действуйте на свой страх и риск", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Запрещать любую элевацию через UniGetUI Elevator или GSudo", "Proxy URL": "Прокси URL", "Proxy compatibility table": "Таблица совместимости с прокси", "Proxy settings": "Настройки прокси", "Proxy settings, etc.": "Настройки прокси и т.д.", "Publication date:": "Дата публикации:", "Publisher": "Издатель", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Менеджер библиотек Python. Полон библиотек Python и других утилит, связанных с Python<br>Содержит: <b>Библиотеки Python и связанные с ними утилиты</b>", "Quit": "Выход", "Quit WingetUI": "Выйти из WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "Переустановить", "Reinstall package": "Переустановить пакет", "Related settings": "Связанные настройки", "Release notes": "Примечания к выпуску", "Release notes URL": "URL-адрес примечаний к выпуску", "Release notes URL:": "Ссылка на заметки о выпуске:", "Release notes:": "Список изменений:", "Reload": "Перезагрузить", "Reload log": "Перезагрузи<PERSON><PERSON> журнал", "Removal failed": "Удаление не удалось", "Removal succeeded": "Удаление прошло успешно", "Remove from list": "Удалить из списка", "Remove permanent data": "Удалить постоянные данные", "Remove selection from bundle": "Удалить выбранное из набора", "Remove successful installs/uninstalls/updates from the installation list": "Убирать успешные установки/удаления/обновления из списка установок", "Removing source {source}": "Удаление источника {source}", "Removing source {source} from {manager}": "Удаление источника {source} из {manager}", "Repair UniGetUI": null, "Repair WinGet": "Восстановить WinGet", "Report an issue or submit a feature request": "Сообщить о проблеме или отправить запрос на функцию", "Repository": "Репозиторий", "Reset": "Сбросить", "Reset Scoop's global app cache": "Сброс кэша Scoop в системе", "Reset UniGetUI": "Сброс UniGetUI", "Reset WinGet": "Сброс WinGet", "Reset Winget sources (might help if no packages are listed)": "Сбросить источники Winget (может помочь, если в списке нет пакетов)", "Reset WingetUI": "Сброс настроек WingetUI", "Reset WingetUI and its preferences": "Сбросить WingetUI и его настройки", "Reset WingetUI icon and screenshot cache": "Сбросить значок WingetUI и кэш скриншотов", "Reset list": "Сбросить список", "Resetting Winget sources - WingetUI": "Сброс источников Winget - WingetUI", "Restart": "Рестарт", "Restart UniGetUI": "Перезапустить UniGetUI", "Restart WingetUI": "Перезапустить WingetUI", "Restart WingetUI to fully apply changes": "Перезапустить WingetUI для применения всех настроек", "Restart later": "Перезагрузить позже", "Restart now": "Перезагрузить сейчас", "Restart required": "Требуется перезагрузка", "Restart your PC to finish installation": "Перезагрузите компьютер, чтобы завершить установку", "Restart your computer to finish the installation": "Перезагрузите компьютер для завершения установки", "Restore a backup from the cloud": "Восстановить резервную копию из облака", "Restrictions on package managers": "Ограничения менеджеров пакетов", "Restrictions on package operations": "Ограничения операций с пакетами", "Restrictions when importing package bundles": "Ограничения импорта бандлов пакетов", "Retry": "Повторить", "Retry as administrator": "Перезапустить с правами администратора", "Retry failed operations": "Перезапустить неудачные операции", "Retry interactively": "Перезапустить в интерактивном режиме", "Retry skipping integrity checks": "Повторить пропуск проверки целостности", "Retrying, please wait...": "Повторная попытка, пожалуйста, подождите...", "Return to top": "Вернуться наверх", "Run": "Выполнить", "Run as admin": "Запуск от имени администратора", "Run cleanup and clear cache": "Выполнить очистку и очистить кэш", "Run last": "Запустить последним", "Run next": "Запустить следующим", "Run now": "Запустить сейчас", "Running the installer...": "Запуск установщика...", "Running the uninstaller...": "Запуск деинсталлятора...", "Running the updater...": "Запуск установщика обновления...", "Save": "Сохранить", "Save File": "Сохранить файл", "Save and close": "Сохранить и закрыть", "Save as": "Сохранить как", "Save bundle as": "Сохранить набор как", "Save now": "Сохранить сейчас", "Saving packages, please wait...": "Сохранение пакетов, пожалуйста, подождите...", "Scoop Installer - WingetUI": "Установщик Scoop - WingetUI", "Scoop Uninstaller - WingetUI": "Деинста<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oop - WingetUI", "Scoop package": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search": "Поиск", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Ищите программное обеспечение для ПК, предупреждайте меня, когда доступны обновления, и не делайте глупостей. Я не хочу, чтобы WingetUI был слишком сложным, я лишь хочу простой <b>магазин программного обеспечения</b>.", "Search for packages": "Поиск пакетов", "Search for packages to start": "Начните поиск пакетов", "Search mode": "Режим поиска", "Search on available updates": "Поиск доступных обновлений", "Search on your software": "Поиск в ваших программах", "Searching for installed packages...": "Поиск установленных пакетов...", "Searching for packages...": "Поиск пакетов...", "Searching for updates...": "Поиск обновлений...", "Select": "Выбрать", "Select \"{item}\" to add your custom bucket": "Выберите \"{item}\" чтобы добавить пользовательский бакет", "Select a folder": "Выберите папку", "Select all": "Выбрать все", "Select all packages": "Выбрать все пакеты", "Select backup": "Выбрать резервную копию", "Select only <b>if you know what you are doing</b>.": "Выбирайте <b>с умом</b>.", "Select package file": "Выбрать файл пакета", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Выберите резервную копию для открытия. Позже вы сможете ознакомиться с пакетами для установки.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Выберите процессы для закрытия перед установкой, обновлением или удалением пакета.", "Select the source you want to add:": "Выберите источник, который вы хотите добавить:", "Select upgradable packages by default": "Выбирать пакеты с возможностью обновления по умолчанию", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Выберите, какие <b>менеджеры пакетов</b> использовать ({0}), настройте способ установки пакетов, настройте управление правами администратора и т.д.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Отправлено рукопожатие. Ожидание ответа слушающего процесса... ({0}%)", "Set a custom backup file name": "Задать собственное имя файла резервной копии", "Set custom backup file name": "Задать собственное имя файла резервной копии", "Settings": "Настройки", "Share": "Поделиться", "Share WingetUI": "Поделиться WingetUI", "Share anonymous usage data": "Отправлять анонимные пользовательские данные", "Share this package": "Поделиться пакетом", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Если вы измените настройки безопасности, необходимо будет открыть бандл снова, чтобы изменения вступили в силу.", "Show UniGetUI on the system tray": "Показывать UniGetUI в системном трее", "Show UniGetUI's version and build number on the titlebar.": "Показать версию UniGetUI на панели заголовка", "Show WingetUI": "Показать WingetUI", "Show a notification when an installation fails": "Показывать уведомление при сбое установки", "Show a notification when an installation finishes successfully": "Показать уведомление об успешном завершении установки", "Show a notification when an operation fails": "Показывать уведомление в случае ошибки операции", "Show a notification when an operation finishes successfully": "Показывать уведомление, когда операция завершена успешно", "Show a notification when there are available updates": "Показывать уведомление, когда есть доступные обновления", "Show a silent notification when an operation is running": "Показывать беззвучное уведомление, когда операция запущена", "Show details": "Показать детали", "Show in explorer": "Показать в Проводнике", "Show info about the package on the Updates tab": "Показать информацию о пакете на вкладке «Обновления»", "Show missing translation strings": "Показать отсутствующие строки перевода", "Show notifications on different events": "Показывать уведомления о различных событиях", "Show package details": "Показать информацию о пакете", "Show package icons on package lists": "Показывать иконки в списке пакетов", "Show similar packages": "Показать похожие пакеты", "Show the live output": "Показать вывод консоли", "Size": "Размер", "Skip": "Пропустить", "Skip hash check": "Пропустить проверку хэша", "Skip hash checks": "Пропустить проверки хэша", "Skip integrity checks": "Пропуск проверок целостности", "Skip minor updates for this package": "Пропускать минорные обновления данного пакета", "Skip the hash check when installing the selected packages": "Пропустить проверку хэша при установке выбранных пакетов", "Skip the hash check when updating the selected packages": "Пропустить проверку хэша при обновлении выбранных пакетов", "Skip this version": "Пропустить версию", "Software Updates": "Обновления программ", "Something went wrong": "Что-то пошло не так", "Something went wrong while launching the updater.": "Что-то пошло не так при запуске программы обновления.", "Source": "Источник", "Source URL:": "URL-адрес источника:", "Source added successfully": "Источник добавлен успешно", "Source addition failed": "Не удалось добавить источник", "Source name:": "Название источника:", "Source removal failed": "Не удалось удалить источник", "Source removed successfully": "Источник удален успешно", "Source:": "Источник:", "Sources": "Источники", "Start": "Начать", "Starting daemons...": "Запуск демонов...", "Starting operation...": "Запуск операции...", "Startup options": "Параметры запуска", "Status": "Статус", "Stuck here? Skip initialization": "Застряли? Пропустить инициализацию", "Suport the developer": "Поддержать разработчика", "Support me": "Поддержи меня", "Support the developer": "Поддержите разработчика", "Systems are now ready to go!": "Теперь системы готовы к работе!", "Telemetry": "Телеметрия", "Text": "Текст", "Text file": "Текстовый файл", "Thank you ❤": "Спасибо ❤", "Thank you 😉": "Спасибо 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Пакетный менеджер Rust. <br>Содержит: <b>Библиотеки и программы, написанные на Rust</b> ", "The backup will NOT include any binary file nor any program's saved data.": "Резервная копия НЕ будет включать в себя ни двоичные файлы, ни сохраненные данные какой-либо программы.", "The backup will be performed after login.": "Резервное копирование будет выполнено после входа в систему.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Резервная копия будет включать полный список установленных пакетов и вариантов их установки. Проигнорированные обновления и пропущенные версии также будут сохранены.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Набор, который вы пытаетесь загрузить, кажется недействительным. Пожалуйста, проверьте файл и попробуйте снова.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Контрольная сумма установщика не совпадает с ожидаемым значением, а подлинность установщика проверить невозможно. Если вы доверяете издателю, {0} пакет снова, пропустив проверку хэша.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Классический пакетный менеджер для Windows. Там ты найдешь все. <br>Содержит: <b>General Software</b>", "The cloud backup completed successfully.": "Облачное резервное копирование завершено успешно.", "The cloud backup has been loaded successfully.": "Облачная резервная копия была загружена успешно.", "The current bundle has no packages. Add some packages to get started": "В текущем наборе нет пакетов. Добавьте пакеты, чтобы начать", "The executable file for {0} was not found": "Исполняемый файл для {0} не найден", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Следующие настройки будут применяться по умолчанию каждый раз после установки, обновления или удаления {0} пакетов.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Следующие пакеты будут экспортированы в файл JSON. Никакие пользовательские данные или двоичные файлы не будут сохранены.", "The following packages are going to be installed on your system.": "Следующие пакеты будут установлены в вашей системе.", "The following settings may pose a security risk, hence they are disabled by default.": "Следующие настройки могут привести к риску, поэтому они отключены по умолчанию.", "The following settings will be applied each time this package is installed, updated or removed.": "Следующие настройки будут применяться каждый раз при установке, обновлении или удалении этого пакета.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Следующие настройки будут применяться каждый раз при установке, обновлении или удалении этого пакета. Они будут сохраняться автоматически.", "The icons and screenshots are maintained by users like you!": "Иконки и скриншоты создаются такими же пользователями, как вы!", "The installer authenticity could not be verified.": "Подлинность установщика не удалось проверить.", "The installer has an invalid checksum": "Неверная контрольная сумма установщика", "The installer hash does not match the expected value.": "Хэш установщика не соответствует ожидаемому значению.", "The local icon cache currently takes {0} MB": "Локальный кэш иконок занимает {0} Мб", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Основная цель этого проекта — создать интуитивно понятный пользовательский интерфейс для управления наиболее распространенными менеджерами пакетов CLI для Windows, такими как Winget и Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Пакет \"{0}\" не найден в пакетном менеджере \"{1}\"", "The package bundle could not be created due to an error.": "В связи с ошибкой набор пакетов не может быть создан", "The package bundle is not valid": "Набор пакетов недействителен", "The package manager \"{0}\" is disabled": "Пакетный менеджер \"{0}\" отключен", "The package manager \"{0}\" was not found": "Пакетный менеджер \"{0}\" не найден", "The package {0} from {1} was not found.": "Пакет {0} из {1} не найден.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Перечисленные здесь пакеты не будут учитываться при проверке обновлений. Дважды щелкните по ним или нажмите кнопку справа, чтобы перестать игнорировать их обновления.", "The selected packages have been blacklisted": "Выбранные пакеты были занесены в черный список", "The settings will list, in their descriptions, the potential security issues they may have.": "В примечаниях к настройкам будут перечислены возможные нарушения безопасности, к которым может привести их изменение.", "The size of the backup is estimated to be less than 1MB.": "Предполагаемый размер резервной копии составляет менее 1 МБ.", "The source {source} was added to {manager} successfully": "Источник {source} был успешно добавлен в {manager}", "The source {source} was removed from {manager} successfully": "Источник {source} был успешно удален из {manager}", "The system tray icon must be enabled in order for notifications to work": "Иконка в системном трее должна быть включена, чтобы уведомления работали", "The update process has been aborted.": "Процесс обновления был прерван.", "The update process will start after closing UniGetUI": "Процесс обновления начнется после закрытия UniGetUI", "The update will be installed upon closing WingetUI": "Обновление будет установлено после закрытия WingetUI", "The update will not continue.": "Обновление не будет продолжено.", "The user has canceled {0}, that was a requirement for {1} to be run": "Пользователь отменил {0}, при этом было требование запуска {1}", "There are no new UniGetUI versions to be installed": "Новых версий UniGetUI для установки нет", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Операции продолжаются. Выход из UniGetUI может привести к их сбою. Вы хотите продолжить?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "На YouTube есть несколько отличных видеороликов, демонстрирующих WingetUI и его возможности. Вы можете узнать полезные трюки и советы!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Есть две основные причины не запускать WingetUI от имени администратора: Первая заключается в том, что менеджер пакетов Scoop может вызывать проблемы с некоторыми командами при запуске с правами администратора. Вторая причина заключается в том, что запуск WingetUI от имени администратора означает, что любой пакет который вы загружаете, будет запускаться от имени администратора (и это небезопасно). Помните, что если вам нужно установить конкретный пакет от имени администратора, вы всегда можете щелкнуть правой кнопкой мыши элемент -> Установить/Обновить/Удалить от имени администратора.", "There is an error with the configuration of the package manager \"{0}\"": "Произошла ошибка с конфигурацией менеджера пакетов \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Идет процесс установки. Если вы закроете WingetUI сейчас, установка может прерваться с неожиданным результатом. Вы уверены, что хотите закрыть WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "Это программы, отвечающие за установку, обновление и удаление пакетов.", "Third-party licenses": "Сторонние лицензии", "This could represent a <b>security risk</b>.": "Это может представлять <b>угрозу безопасности</b>.", "This is not recommended.": "Это не рекомендуется", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Возможно, это случилось по причине удаления пакета, который вы отправили, либо он был опубликован в менеджере пакетов, который вы не включили. Полученный ID - {0}", "This is the <b>default choice</b>.": "Это <b>выбор по умолчанию</b>.", "This may help if WinGet packages are not shown": "Это может помочь в случае, если пакеты WinGet не отображаются", "This may help if no packages are listed": "Это может помочь, если в списке нет пакетов", "This may take a minute or two": "Это может занять минуту или две", "This operation is running interactively.": "Данная операция запущена интерактивно", "This operation is running with administrator privileges.": "Данная операция запущена с правами администратора", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Настройка БУДЕТ приводить к неполадкам. Любая операция, неспособная элевации, БУДЕТ ЗАВЕРШЕНА. Установка, обновление или удаление с правами администратора НЕ БУДЕТ работать.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Этот пакет может быть обновлен", "This package can be updated to version {0}": "Этот пакет может быть обновлен до версии {0}", "This package can be upgraded to version {0}": "Пакет может быть обновлен до версии {0}", "This package cannot be installed from an elevated context.": "Этот пакет не может быть установлен из контекста с повышенными правами.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "У этого пакета нет скриншотов или отсутствует иконка? Внесите свой вклад в WingetUI, добавив недостающие иконки и скриншоты в нашу открытую, общедоступную базу данных.", "This package is already installed": "Этот пакет уже установлен", "This package is being processed": "Этот пакет находится в стадии обработки", "This package is not available": "Пакет недоступен", "This package is on the queue": "Этот пакет находится в очереди", "This process is running with administrator privileges": "Процесс запущен с правами администратора", "This project has no connection with the official {0} project — it's completely unofficial.": "Этот проект никак не связан с официальным проектом {0} - он полностью неофициальный.", "This setting is disabled": "Эта настройка отключена", "This wizard will help you configure and customize WingetUI!": "Этот мастер поможет вам настроить WingetUI!", "Toggle search filters pane": "Переключить панель фильтров поиска", "Translators": "Переводчики", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Введите здесь название и URL-адрес источника, который вы хотите добавить, разделив их пробелом.", "Unable to find package": "Не удается найти пакет", "Unable to load informarion": "Не удалось загрузить информацию", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI собирает анонимную статистику использования в целях улучшения опыта использования", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI собирает анонимную статистику использования с единственной целью изучения и улучшения опыта использования", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI обнаружил новый ярлык на рабочем столе, который может быть удален автоматически.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI обнаружил следующие ярлыки на рабочем столе, которые могут быть автоматически удалены при будущих обновлениях", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI обнаружил {0} новые ярлыки на рабочем столе, которые могут быть удалены автоматически.", "UniGetUI is being updated...": "UniGetUI обновляется...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI не связан ни с одним из совместимых менеджеров пакетов. UniGetUI - это независимый проект.", "UniGetUI on the background and system tray": "UniGetUI в фоновом режиме и системном трее", "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI требуется {0} для работы, но он не найден в системе", "UniGetUI startup page:": "Начальная страница UniGetUI:", "UniGetUI updater": "Обновления UniGetUI", "UniGetUI version {0} is being downloaded.": "Идет загрузка UniGetUI версии {0}.", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} готов к установке.", "Uninstall": "Удалить", "Uninstall Scoop (and its packages)": "Уда<PERSON>и<PERSON><PERSON> (и его пакеты)", "Uninstall and more": null, "Uninstall and remove data": "Деинсталляция и удаление данных", "Uninstall as administrator": "Удалить от имени администратора", "Uninstall canceled by the user!": "Удаление отменено пользователем!", "Uninstall failed": "Ошибка удаления", "Uninstall options": null, "Uninstall package": "Удалить пакет", "Uninstall package, then reinstall it": "Удалить пакет, затем установить его заново", "Uninstall package, then update it": "Удалить пакет, затем обновить его", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Удалить выбранные пакеты", "Uninstall selection": null, "Uninstall succeeded": "Удаление прошло успешно", "Uninstall the selected packages with administrator privileges": "Удалить выбранные пакеты с правами администратора", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Пакеты с возможностью удаления от источника \"{0}\" не были опубликованы ни в одном из доступных менеджеров пакетов, поэтому информация о них отсутствует.", "Unknown": "Неизвестно", "Unknown size": "Неизвестный размер", "Unset or unknown": "Отключено или неизвестно", "Up to date": "Актуально", "Update": "Обновить", "Update WingetUI automatically": "Обновлять WingetUI автоматически", "Update all": "Обновить все", "Update and more": null, "Update as administrator": "Обновить от имени администратора", "Update check frequency, automatically install updates, etc.": "Частота проверки, автоматическая установка обновлений и т.д.", "Update date": "Дата обновления", "Update failed": "Ошибка обновления", "Update found!": "Найдено обновление!", "Update now": "Обновить сейчас", "Update options": null, "Update package indexes on launch": "Обновлять индексы пакетов при запуске", "Update packages automatically": "Обновлять пакеты автоматически", "Update selected packages": "Обновить выбранные пакеты", "Update selected packages with administrator privileges": "Обновить выбранные пакеты с правами администратора", "Update selection": null, "Update succeeded": "Обновление прошло успешно", "Update to version {0}": "Обновление до версии {0}", "Update to {0} available": "Обновление для {0} найдено", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Обновлять vcpkg's Git-файлы портов автоматически (требуется установленный Git)", "Updates": "Обновления", "Updates available!": "Доступны обновления!", "Updates for this package are ignored": "Обновления этих пакетов игнорируются", "Updates found!": "Найдены обновления!", "Updates preferences": "Настройки обновлений", "Updating WingetUI": "WingetUI обновляется", "Url": "Ссылка", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Использовать встроенный WinGet вместо CMDLets из PowerShell", "Use a custom icon and screenshot database URL": "Использовать пользовательский значок и URL-адрес базы данных снимков экрана", "Use bundled WinGet instead of PowerShell CMDlets": "Использовать встроенный WinGet вместо CMDLets из PowerShell", "Use bundled WinGet instead of system WinGet": "Использовать встроенный WinGet вместо системного", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Использовать установленный GSudo вместо встроенного (требуется перезапуск приложения)", "Use system Chocolatey": "Использовать системный Chocolatey", "Use system Chocolatey (Needs a restart)": "Использовать системный Chocolatey (требуется перезапуск)", "Use system Winget (Needs a restart)": "Использовать системный Winget (требуется перезапуск)", "Use system Winget (System language must be set to english)": "Использовать системный Winget (язык системы должен быть установлен на английский)", "Use the WinGet COM API to fetch packages": "Использовать WinGet COM API для получения пакетов", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Использовать модуль WinGet в PowerShell вместо WinGet COM API", "Useful links": "Полезные ссылки", "User": "Пользователь", "User interface preferences": "Настройки пользовательского интерфейса", "User | Local": "Пользователь | Локально", "Username": "Имя пользователя", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Использование WingetUI подразумевает принятие лицензии GNU Lesser General Public License версии 2.1", "Using WingetUI implies the acceptation of the MIT License": "Использование WingetUI подразумевает согласие с лицензией MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg root не найден. Пожалуйста, задайте переменную окружения %VCPKG_ROOT% или укажите ее в настройках UniGetUI", "Vcpkg was not found on your system.": "Vcpkg не был найден в вашей системе.", "Verbose": "Подробно", "Version": "Версия", "Version to install:": "Версия для установки:", "Version:": "Версия:", "View GitHub Profile": "Посмотреть профиль GitHub", "View WingetUI on GitHub": "Открыть WingetUI на GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Просмотрите исходный код WingetUI. Оттуда вы можете сообщать об ошибках, предлагать функции или даже напрямую внести свой вклад в проект WingetUI.", "View mode:": "Представление:", "View on UniGetUI": "Смотреть на сайте UniGetUI", "View page on browser": "Просмотреть страницу в браузере", "View {0} logs": "Посмотреть журналы {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Ожидать подключения устройства к сети перед запуском операций, требующих подключения к сети", "Waiting for other installations to finish...": "Ожидание завершения других установок...", "Waiting for {0} to complete...": "Ожидание завершения {0}", "Warning": "Внимание", "Warning!": "Внимание!", "We are checking for updates.": "Мы проверяем наличие обновлений.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Не удалось загрузить подробную информацию об этом пакете, так как он не был найден ни в одном из ваших источников пакетов.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Не удалось загрузить подробную информацию об этом пакете, так как он не был установлен из доступного менеджера пакетов.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Мы не смогли {action} {package}. Пожалуйста, повторите попытку позже. Нажмите \"{showDetails}\", чтобы получить журналы установщика.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Мы не смогли {action} {package}. Пожалуйста, повторите попытку позже. Нажмите \"{showDetails}\", чтобы получить журналы установщика.", "We couldn't find any package": "Мы не смогли найти ни одного пакета.", "Welcome to WingetUI": "Добро пожаловать в WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Когда обнаружены новые ярлыки, удалять их автоматически вместо отображения этого диалога.", "Which backup do you want to open?": null, "Which package managers do you want to use?": "Какие менеджеры пакетов вы хотите использовать?", "Which source do you want to add?": "Какой источник вы хотите добавить?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "В то время как Winget можно использовать в WingetUI, WingetUI можно использовать с другими пакетными менеджерами, что может привести к путанице. В прошлом WingetUI был разработан для работы только с Winget, но это уже не так, и поэтому WingetUI не отражает того, чем стремится стать этот проект.", "WinGet could not be repaired": "WinGet не может быть восстановлен", "WinGet malfunction detected": "Обнаружена неисправность WinGet", "WinGet was repaired successfully": "WinGet восстановлен успешно", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - Все обновлено", "WingetUI - {0} updates are available": "WingetUI - доступно {0} {0, plural, one {обновление} few {обновления} other {обновлений}}", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "Домашняя страница WingetUI", "WingetUI Homepage - Share this link!": "Домашняя страница WingetUI - Поделитесь этой ссылкой!", "WingetUI License": "Лицензия WingetUI", "WingetUI Log": "<PERSON><PERSON><PERSON><PERSON><PERSON> WingetUI", "WingetUI Repository": "Репозиторий WingetUI", "WingetUI Settings": "Настройки WingetUI", "WingetUI Settings File": "Файл настроек WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "В WingetUI используются следующие библиотеки. Без них WingetUI был бы невозможен.", "WingetUI Version {0}": "WingetUI Версия {0}", "WingetUI autostart behaviour, application launch settings": "Поведение автозапуска WingetUI, настройки запуска приложения", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI может проверить, есть ли для вашего программного обеспечения доступные обновления, и установить их автоматически, если вы захотите", "WingetUI display language:": "Язык интерфейса WingetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI был запущен от имени администратора, что не рекомендуется. При запуске WingetUI от имени администратора КАЖДАЯ операция, запущенная из WingetUI, будет иметь привилегии администратора. Вы можете продолжать пользоваться программой, но мы настоятельно рекомендуем не запускать WingetUI с правами администратора.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI был переведен более чем на 40 языков благодаря переводчикам-добровольцам. Спасибо 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI переведен не машинным способом. Эти люди участвовали в переводе:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI - это приложение, которое упрощает управление вашим программным обеспечением, предоставляя универсальный графический интерфейс для ваших менеджеров пакетов командной строки.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI переименовывается, чтобы подчеркнуть разницу между WingetUI (интерфейсом, который вы используете прямо сейчас) и Winget (менеджером пакетов, разработанным Microsoft, с которым я не связан).", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI обновляется. После окончания WingetUI перезапустится сам", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI бесплатен, и он будет бесплатным всегда. Никакой рекламы, никаких кредитных карт, никакой премиум-версии. 100% бесплатно, навсегда.", "WingetUI log": "<PERSON><PERSON><PERSON><PERSON><PERSON> WingetUI", "WingetUI tray application preferences": "Настройки WingetUI в трее ", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "В WingetUI используются следующие библиотеки. Без них WingetUI был бы невозможен.", "WingetUI version {0} is being downloaded.": "Загрузка WingetUI версии {0}.", "WingetUI will become {newname} soon!": "WingetUI скоро станет {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI не будет периодически проверять наличие обновлений. Они по-прежнему будут проверяться при запуске, но вы не будете предупреждены о них.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI будет отображать запрос UAC каждый раз, когда для установки пакета требуется повышение прав.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI скоро получит название {newname}. Это не повлечет за собой никаких изменений в приложении. Я (разработчик) буду продолжать развивать этот проект, как и сейчас, но под другим именем.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI был бы невозможен без помощи наших дорогих участников. Посмотрите их профиль на GitHub, без них WingetUI не существовал бы!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI был бы невозможен без помощи участников. Спасибо вам всем 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} готов к установке.", "Write here the process names here, separated by commas (,)": null, "Yes": "Да", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "На данный момент у вас установлена версия {0}", "You have installed WingetUI Version {0}": "Вы установили WingetUI версию {0}", "You may lose unsaved data": "Вы можете потерять несохраненные данные", "You may need to install {pm} in order to use it with WingetUI.": "Вам может потребоваться установить {pm}, чтобы использовать его с WingetUI.", "You may restart your computer later if you wish": "Вы можете перезагрузить компьютер позже, если хотите", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Права администратора будут запрошены только один раз, затем они будут предоставлены пакетам, которые их запрашивают.", "You will be prompted only once, and every future installation will be elevated automatically.": "Права администратора будут запрошены единожды и для каждой будущей установки будут повышены автоматически.", "You will likely need to interact with the installer.": "Вам скорее всего потребуется взаимодействовать с установщиком", "[RAN AS ADMINISTRATOR]": "ЗАПУЩЕН ОТ ИМЕНИ АДМИНИСТРАТОРА", "buy me a coffee": "купить мне кофе", "extracted": "извлечено", "feature": "функция", "formerly WingetUI": "ранее WingetUI", "homepage": "сайт", "install": "установить", "installation": "установка", "installed": "установлен", "installing": "установка", "library": "библиотека", "mandatory": "обязательно", "option": "опция", "optional": "необязательно", "uninstall": "удалить", "uninstallation": "удаление", "uninstalled": "удалён", "uninstalling": "удаление", "update(noun)": "обновление", "update(verb)": "обновить", "updated": "обновлён", "updating": "обновление", "version {0}": "версия {0}", "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "Удаление {0}", "{0} aborted": "{0} без успеха", "{0} can be updated": "Можно обновить {0}", "{0} can be updated to version {1}": "{0} может быть обновлен до версии {1}", "{0} days": "{0} {0, plural, one {день} few {дня} other {дней}}", "{0} desktop shortcuts created": "Создано {0} ярлыков на рабочем столе", "{0} failed": "{0} не удалось", "{0} has been installed successfully.": "{0} был успешно установлен", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} был успешно установлен. Рекомендуется перезапустить UniGetUI для завершения установки", "{0} has failed, that was a requirement for {1} to be run": "{0} неудачных, при этом было требование запуска {1}", "{0} homepage": "{0} домашняя страница", "{0} hours": "{0} {0, plural, one {час} few {часа} other {часов}}", "{0} installation": "Установка {0}", "{0} installation options": "Параметры установки {0}", "{0} installer is being downloaded": "{0} установщиков было загружено", "{0} is being installed": "{0} был установлен", "{0} is being uninstalled": "{0} был удален", "{0} is being updated": "{0} обновляется", "{0} is being updated to version {1}": "{0} обновляется до версии {1}", "{0} is disabled": "{0} отключён", "{0} minutes": "{0} {0, plural, one {минута} few {минуты} other {минут}}", "{0} months": "{0} месяцев", "{0} packages are being updated": "{0} {0, plural, one {пакет} few {пакета} other {пакетов}} обновляется", "{0} packages can be updated": "{0} {0, plural, one {пакет может быть обновлён} few {пакета может быть обновлено} other {пакетов может быть обновлено}}", "{0} packages found": "{0, plural, one {Найден {0} пакет} few {Найдено {0} пакета} other {Найдено {0} пакетов}} ", "{0} packages were found": "{0, plural, one {Найден {0} пакет} few {Найдено {0} пакета} other {Найдено {0} пакетов}} ", "{0} packages were found, {1} of which match the specified filters.": "{0, plural, one {Найден {0} пакет} few {Найдено {0} пакета} other {Найдено {0} пакетов}}, {1} из которых {1, plural, one {соответствует} other {соответствуют}} указанным фильтрам.", "{0} settings": "Настройки {0}", "{0} status": "{0} статус", "{0} succeeded": "{0} выполнено успешно", "{0} update": "Обновление {0}", "{0} updates are available": "Доступно {0} {0, plural, one {обновление} few {обновления} other {обновлений}}", "{0} was {1} successfully!": "{0} успешно {1}!", "{0} weeks": "{0} недель", "{0} years": "{0} лет", "{0} {1} failed": "{0} {1} не удалось", "{package} Installation": "Установка {package}", "{package} Uninstall": "Удаление {package}", "{package} Update": "Обновление {package}", "{package} could not be installed": "Не удалось установить {package}", "{package} could not be uninstalled": "Не удалось удалить {package}", "{package} could not be updated": "Не удалось обновить {package}", "{package} installation failed": "Установка {package} не удалась", "{package} installer could not be downloaded": "{package} установщик не может быть загружен", "{package} installer download": "{package} установщик загружается", "{package} installer was downloaded successfully": "{package} установщик был успешно загружен", "{package} uninstall failed": "Удаление {package} не удалось", "{package} update failed": "Обновление {package} не удалось", "{package} update failed. Click here for more details.": "Обновление {package} не удалось. Нажмите здесь для получения более подробной информации.", "{package} was installed successfully": "{package} был успешно установлен", "{package} was uninstalled successfully": "{package} был успешно удален", "{package} was updated successfully": "{package} был успешно обновлен", "{pcName} installed packages": "Установленные пакеты {pcName}", "{pm} could not be found": "{pm} не удалось найти", "{pm} found: {state}": "{pm} найдено: {state}", "{pm} is disabled": "{pm} отключен", "{pm} is enabled and ready to go": "{pm} включен и готов к работе", "{pm} package manager specific preferences": "Специфичные настройки менеджера пакетов {pm}", "{pm} preferences": "Настройки {pm}", "{pm} version:": "{pm} версия:", "{pm} was not found!": "{pm} не найден!"}