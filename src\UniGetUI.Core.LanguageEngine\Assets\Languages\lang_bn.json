{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(শেষ পরীক্ষা করা হয়েছেঃ {0})", "(Number {0} in the queue)": "(সারিতে {0} কিউ নম্বর)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON><PERSON><PERSON>, @<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, @itz-rj-here, @samiulislamsharan", "0 packages found": "০ টি প্যাকেজ পাওয়া গেছে", "0 updates found": "০ টি আপডেট পাওয়া গেছে", "1 - Errors": "১ - ত্রুটি", "1 day": "১ দিন", "1 hour": "১ ঘন্টা", "1 month": null, "1 package was found": "১টি প্যাকেজ পাওয়া গেছে", "1 update is available": "১ টি আপডেট উপলব্ধ", "1 week": "১ সপ্তাহ", "1 year": null, "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": "২ - সতর্কতা", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": "৩ - তথ্য (কম)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": "৪ - ত<PERSON><PERSON><PERSON> (আরো)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": "৫ - তথ্য (ডিবাগ)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": null, "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "মাইক্রোসফটের .NET ইকোসিস্টেমকে মাথায় রেখে ডিজাইন করা টুলস এবং এক্সিকিউটেবলে পূর্ণ একটি ভান্ডার।<br>এতে রয়েছে: <b>.NET সম্পর্কিত টুল এবং স্ক্রিপ্ট</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "মাইক্রোসফটের .NET ইকোসিস্টেমকে মাথায় রেখে ডিজাইন করা টুলে পূর্ণ একটি ভান্ডার।<br>এতে রয়েছে: <b>.NET সম্পর্কিত টুলস</b>", "A restart is required": "একটি পুনরায় সক্রিয় করা প্রয়োজন", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "সম্পর্কিত", "About Qt6": "Qt6 সম্পর্কে", "About WingetUI": "WingetUI সম্পর্কে", "About WingetUI version {0}": "WingetUI সম্পর্কে সংস্করণ {0}", "About the dev": "ডেভেলপার সম্বন্ধে", "Accept": null, "Action when double-clicking packages, hide successful installations": "প্যাকেজগুলি ডাবল ক্লিক করার সময়, সফল ইনস্টলেশনগুলো লুকান", "Add": "যুক্ত করুন", "Add a source to {0}": "{0} এ একটি উৎস যোগ করুন", "Add a timestamp to the backup file names": "ব্যাকআপ ফাইলের নামগুলিতে একটি টাইমস্ট্যাম্প যোগ করুন", "Add a timestamp to the backup files": "ব্যাকআপ ফাইলগুলিতে একটি টাইমস্ট্যাম্প যোগ করুন", "Add packages or open an existing bundle": "প্যাকেজ যোগ করুন বা একটি বিদ্যমান বান্ডিল খুলুন", "Add packages or open an existing package bundle": "প্যাকেজ যোগ করুন বা একটি বিদ্যমান প্যাকেজ বান্ডিল খুলুন", "Add packages to bundle": null, "Add packages to start": "শুরু করতে প্যাকেজ যোগ করুন", "Add selection to bundle": "বান্ডেলে নির্বাচন যোগ করুন", "Add source": "উৎস যোগ করুন", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": "{manager}-এ {source} যোগ করা হচ্ছে", "Addition succeeded": "সংযোজন সফল হয়েছে", "Administrator privileges": "প্রশাসকের বিশেষাধিকার", "Administrator privileges preferences": "প্রশাসকের বিশেষাধিকার পছন্দসমূহ", "Administrator rights": "প্রশাসকের অধিকার", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "সকল ফাইল", "All versions": "সকল ভার্সন", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "প্যাকেজ অপারেশন সমান্তরালভাবে সঞ্চালিত করার অনুমতি দিন", "Allow parallel installs (NOT RECOMMENDED)": "সমান্তর<PERSON><PERSON> ইনস্টল করার অনুমতি দিন (প্রস্তাবিত নয়)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "{pm} ক্রিয়<PERSON>কলাপগুলি সমান্তরালভাবে সম্পাদন করার অনুমতি দিন৷", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": "সর্বদা ডিফল্টরূপে {pm} ইনস্টলেশনগুলিকে উন্নত করুন", "Always run {pm} operations with administrator rights": "সর্বদা প্রশাসকের অধিকার সহ {pm} অপারেশন চালান৷", "An error occurred": "একটি ত্রুটি ঘটেছে", "An error occurred when adding the source: ": "উৎস যোগ করার সময় একটি ত্রুটি ঘটেছেঃ", "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": "আপডেটের জন্য পরীক্ষা করার সময় একটি ত্রুটি ঘটেছেঃ", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "এই প্যাকেজটি প্রক্রিয়া করার সময় একটি ত্রুটি ঘটেছে", "An error occurred:": "একটি ত্রুটি ঘটেছেঃ", "An interal error occurred. Please view the log for further details.": "একটি অভ্যন্তরীণ ত্রুটি ঘটেছে. আরো বিস্তারিত জানার জন্য লগ দেখুন.", "An unexpected error occurred:": "একটি অপ্রত্যাশিত ত্রুটি ঘটেছেঃ", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": "একটি আপডেট পাওয়া গেছে!", "Android Subsystem": "অ্যান্ড্রয়েড সাবসিস্টেম", "Another source": "আরেকটি উৎস", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": null, "App Name": "অ্যাপের নাম", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "অ্যাপ্লিকেশন থিমঃ", "Apply": null, "Architecture to install:": "ইনস্টল করার জন্য আর্কিটেকচারঃ", "Are these screenshots wron or blurry?": "এই স্ক্রিনশটগুলি কি ভুল বা অস্পষ্ট লাগছে?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "আপনি কি নিশ্চিত?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "প্রতিটি ব্যাচের অপারেশনের জন্য একবার প্রশাসকের সুবিধার জন্য জিজ্ঞাসা করুন", "Ask for administrator rights when required": "প্রয়োজনে প্রশাসকের অধিকারের জন্য জিজ্ঞাসা করুন", "Ask once or always for administrator rights, elevate installations by default": "প্রশাসকের অধিকারের জন্য একবার বা সর্বদা জিজ্ঞাসা করুন, ডিফল্টরূপে ইনস্টলেশনগুলিকে উন্নত করুন", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "প্রশাসকের বিশেষাধিকারের জন্য শুধুমাত্র একবার জিজ্ঞাসা করুন (প্রস্তাবিত নয়)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": "মনোযোগ প্রয়োজন", "Authenticate to the proxy with an user and a password": null, "Author": "লেখক", "Automatic desktop shortcut remover": null, "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "সহজেই পুনরুদ্ধার করতে আপনার সমস্ত ইনস্টল করা প্যাকেজগুলির একটি তালিকা স্বয়ংক্রিয়ভাবে সংরক্ষণ করুন।", "Automatically save a list of your installed packages on your computer.": "আপনার কম্পিউটারে আপনার ইনস্টল করা প্যাকেজগুলির একটি তালিকা স্বয়ংক্রিয়ভাবে সংরক্ষণ করুন।", "Autostart WingetUI in the notifications area": "বিজ্ঞপ্তি এলাকায় WingetUI অটোস্টার্ট করুন", "Available Updates": "উপলব্ধ আপডেট", "Available updates: {0}": "উপলভ্য আপডেটগুলিঃ {0}", "Available updates: {0}, not finished yet...": "উপলব্ধ আপডেটগুলি: {0}, এখনো শেষ হয়নি...", "Backing up packages to GitHub Gist...": null, "Backup": "ব্যাকআপ", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "ইনস্টল করা প্যাকেজগুলি ব্যাকআপ করুন", "Backup location": null, "Become a contributor": "অবদানকারী হয়ে উঠুন", "Become a translator": "একজন অনুবাদক হন", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "বেটা বৈশিষ্ট্যগুলো এবং অন্যান্য বিকল্পগুলি যা স্পর্শ করা উচিত হবে না।", "Both": "উভয়", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "কিন্তু WingetUI সম্বন্ধে আরও বেশি কিছু জানতে আপনি যা করতে পারেন তা এখানে রয়েছে:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "প্যাকেজ ম্যানেজ<PERSON><PERSON>কে টগল করার মাধ্যমে, আপনি আর এর প্যাকেজ দেখতে বা আপডেট করতে পারবেন না।", "Cache administrator rights and elevate installers by default": "ক্যাশে অ্যাডমিনিস্ট্রেটর অধিকার এবং ডিফল্টরূপে ইনস্টলার উন্নত করুন", "Cache administrator rights, but elevate installers only when required": "ক্যাশে অ্যাডমিনিস্ট্রেটর অধিকার, কিন্তু যখন প্রয়োজন তখনই ইনস্টলারদের উন্নত করুন", "Cache was reset successfully!": "ক্যাশ সফলভাবে পুনরায় সেট করা হয়েছে!", "Can't {0} {1}": "{1} {0} করা যাচ্ছে না", "Cancel": "বাতিল", "Cancel all operations": null, "Change backup output directory": "ব্যাকআপ আউটপুট ডিরেক্টরি পরিবর্তন করুন", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "UniGetUI কীভাবে আপনার প্যাকেজগুলির জন্য উপলব্ধ আপডেটগুলি পরীক্ষা করে এবং ইনস্টল করে তা পরিবর্তন করুন", "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": "ইনস্টল করার অবস্থান পরিবর্তন করুন", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "পর্যায়ক্রমে প্যাকেজ আপডেট চেক করুন", "Check for updates": null, "Check for updates every:": "প্রতিটি আপডেটের জন্য চেক করুনঃ", "Check for updates periodically": "পর্যায়ক্রমে আপডেটের জন্য চেক করুন", "Check for updates regularly, and ask me what to do when updates are found.": "নিয়মিত আপডেটের জন্য চেক করুন, এবং আপডেট পাওয়া গেলে কি করতে হবে তা আমাকে জিজ্ঞাসা করুন।", "Check for updates regularly, and automatically install available ones.": "নিয়মিত আপডেটের জন্য চেক করুন, এবং স্বয়ংক্রিয়ভাবে উপলব্ধগুলি ইনস্টল করুন।", "Check out my {0} and my {1}!": "আমার {0} এবং আমার {1} দেখুন!", "Check out some WingetUI overviews": "কিছু WingetUI ওভারভিউ দেখুন", "Checking for other running instances...": "অন্যান্য চলমান উদাহরণের জন্য পরীক্ষা করা হচ্ছে...", "Checking for updates...": "আপডেট চেক করা হচ্ছে...", "Checking found instace(s)...": "পাওয়া উদাহরণ(গুলি) পরীক্ষা করা হচ্ছে...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "নির্বাচন পরিষ্কার করুন", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": "স্কুপ ক্যাশে সাফ করা হচ্ছে - WingetUI", "Clearing Scoop cache...": "<PERSON><PERSON> ক্যাশে পরিষ্কার করা হচ্ছে...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "ইনস্টলেশন প্রক্রিয়া শুরু করতে Install এ ক্লিক করুন। আপনি ইনস্টলেশন এড়িয়ে গেলে, UniGetUI আশানুরূপ কাজ নাও করতে পারে।", "Close": "বন্ধ", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "বিজ্ঞপ্তি এলাকায় WingetUI বন্ধ করুন", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "কমান্ড-লাইন আউটপুট", "Command-line to run:": null, "Compare query against": "প্রশ্নের সাথে তুলনা করুন", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "উপাদানের তথ্য", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": "চালিয়ে যান", "Contribute to the icon and screenshot repository": "আইকন এবং স্ক্রিনশট সংগ্রহস্থলে অবদান রাখুন", "Contributors": "অবদানকারী", "Copy": "ক<PERSON>ি", "Copy to clipboard": "ক্লিপবোর্ডে ক<PERSON>ি করুন", "Could not add source": null, "Could not add source {source} to {manager}": "{manager}-এ {source} যোগ করা যায়নি", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": "ঘোষণা লোড করা যায়নি -", "Could not load announcements - HTTP status code is $CODE": "ঘোষণা লোড করা যায়নি - HTTP স্ট্যাটাস কোড হল $CODE", "Could not remove source": null, "Could not remove source {source} from {manager}": "{manager} থেকে উৎস {source} সরানো যায়নি", "Could not remove {source} from {manager}": null, "Create .ps1 script": null, "Credentials": null, "Current Version": "বর্তমান সংস্করণ", "Current status: Not logged in": null, "Current user": "বর্<PERSON><PERSON><PERSON><PERSON> ব্যব<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ী", "Custom arguments:": "কাস্টম আর্গুমেন্টঃ", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "কাস্টম কমান্ড লাইন আর্গুমেন্টঃ", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "WingetUI কাস্টমাইজ করুন - শুধুমাত্র হ্যাকার এবং উন্নত ব্যবহারকারীদের জন্য", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "দাবিত্যাগ: আমরা ডাউনলোড করা প্যাকেজগুলির জন্য দায়ী নই। শুধুমাত্র বিশ্বস্ত সফ্টওয়্যার ইনস্টল করার বিষয়ে নিশ্চিত করুন।", "Dark": "অন্ধকার", "Decline": null, "Default": "ডিফল্ট", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "ডিফল্ট পছন্দ - নিয়মিত ব্যবহারকারীদের জন্য উপযুক্ত", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "বর্ণনাঃ", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "বিকাশ করা কঠিন, এবং এই অ্যাপ্লিকেশনটি বিনামূল্যে। কিন্তু আপনি যদি অ্যাপ্লিকেশনটি পছন্দ করেন তবে আপনি সবসময় <b>আমাকে একটি কফি কিনতে পারেন</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "\"{discoveryTab}\" ট্যাবে একটি আইটেমে ডাবল ক্লিক করার সময় সরাসরি ইনস্টল করুন (প্যাকেজের তথ্য দেখানোর পরিবর্তে)", "Disable new share API (port 7058)": "নতুন শেয়ার API (পোর্ট 7058) অক্ষম করুন", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": "দাবিত্যাগ", "Discover Packages": "প্যাকেজ আবিষ্কার করুন", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "বড় হাতের এবং ছোট হাতের অক্ষরের মধ্যে পার্থক্য করুন", "Distinguish between uppercase and lowercase": "বড় হাতের এবং ছোট হাতের অক্ষরের মধ্যে পার্থক্য করুন", "Do NOT check for updates": "আপডেটের জন্য চেক করবেন না", "Do an interactive install for the selected packages": "নির্বাচিত প্যাকেজের জন্য একটি ইন্টারেক্টিভ ইনস্টল করুন", "Do an interactive uninstall for the selected packages": "নির্বাচিত প্যাকেজগুলির জন্য একটি ইন্টারেক্টিভ আনইনস্টল করুন", "Do an interactive update for the selected packages": "নির্বাচিত প্যাকেজের জন্য একটি ইন্টারেক্টিভ আপডেট করুন", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "<PERSON><PERSON><PERSON><PERSON> থেকে স্বয়ংক্রিয়ভাবে নতুন অ্যাপ অনুবাদ ডাউনলোড করবেন না", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": "তালিকা থেকে স্বয়ংক্রিয়ভাবে সফল ক্রিয়াকলাপগুলি সরাবেন না", "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "লঞ্চের সময় প্যাকেজ সূচী আপডেট করবেন না", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "আপনি কি WingetUI দরকারী বলে মনে করেন? আপনি যদি পারেন, আপনি আমার কাজকে সমর্থন করতে  পারেন,যাতে আমি WingetUI-কে চূড়ান্ত প্যাকেজ পরিচালনার ইন্টারফেস তৈরি করা চালিয়ে যেতে পারি।", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "আপনি কি WingetUI দরকারী বলে মনে করেন? আপনি ডেভেলপমেন্ট সমর্থন করতে চান? যদি তাই হয়, আপনি {0} করতে পারেন, এটা অনেক সাহায্য করে!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": "আপনি কি সত্যিই নিম্নলিখিত {0} প্যাকেজগুলি আনইনস্টল করতে চান?", "Do you really want to uninstall {0} packages?": "আপনি কি সত্যিই {0} টি প্যাকেজ আনইনস্টল করতে চান?", "Do you really want to uninstall {0}?": "আপনি কি {0} আনইনষ্টল করতে চান?", "Do you want to restart your computer now?": "আপনি কি এখন আপনার কম্পিউটার পুনরায় চালু করতে চান?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "আপনি কি আপনার ভাষায় UniGetUI অনুবাদ করতে চান? কীভাবে অবদান রাখতে হয় তা দেখুন <a style=\"color:{0}\" href=\"{1}\"a>এখানে!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "দান করতে ভালো লাগছে না? চিন্তা করবেন না, আপনি সবসময় আপনার বন্ধুদের সাথে WingetUI শেয়ার করতে পারেন। WingetUI সম্পর্কে শব্দ ছড়িয়ে দিন।", "Donate": "দা<PERSON> করুন", "Done!": null, "Download failed": null, "Download installer": "ইনস্টলার ডাউনলোড করুন", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "ডাউনলোড সফল হয়েছে", "Download updated language files from GitHub automatically": "GitHub থেকে স্বয়ংক্রিয়ভাবে আপডেট করা ভাষার ফাইল ডাউনলোড করুন", "Downloading": "ডাউনলোড হচ্ছে", "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "প্যাকেজ মেটাডেটা ডাউনলোড করা হচ্ছে...", "Enable Scoop cleanup on launch": "লঞ্চের সময় Scoop ক্লিনআপ সক্ষম করুন", "Enable WingetUI notifications": "WingetUI বিজ্ঞপ্তি সক্ষম করুন", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": "ব্যাকগ্রাউন্ড এপিআই সক্ষম করুন (WingetUI উইজেট এবং শেয়ারিং, পোর্ট 7058)", "Enable it to install packages from {pm}.": "{pm} থেকে প্যাকেজ ইনস্টল করতে এটি সক্ষম করুন।", "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "{pm} চালু করুন", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "ত্রুটি", "Everything is up to date": "Everything is up to date", "Exact match": "খা<PERSON><PERSON> খাপ", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": "সংস্করণ প্রসারিত করুন", "Experimental settings and developer options": "পরীক্ষামূলক সেটিংস এবং ডেভেলপার বিকল্প", "Export": "রপ্ত<PERSON>নি", "Export log as a file": "লগ একটি ফাইল হিসাবে রপ্তানি করুন", "Export packages": "প্যাকে<PERSON> রপ্তানি করুন", "Export selected packages to a file": "নির্বাচিত প্যাকেজ একটি ফাইলে রপ্তানি করুন", "Export settings to a local file": "একটি স্থানীয় ফাইলে সেটিংস রপ্তানি করুন", "Export to a file": "একটি ফাইলে রপ্তানি করুন", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "সাম্প্রতিক ঘোষণা আনা হচ্ছে, অনুগ্রহ করে অপেক্ষা করুন...", "Filters": "ফিল্টার", "Finish": "শেষ করুন", "Follow system color scheme": "সিস্টেমের রঙের স্কিম অনুসরণ করুন", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "ফোর্স এআরএম কম্পাইল করা উইঙ্গেট সংস্করণ (শুধুমাত্র ARM64 সিস্টেমের জন্য)", "Formerly known as WingetUI": "পূর্বে WingetUI নামে পরিচিত", "Found": "পাওয়া গেছে ", "Found packages: ": "প্যাকেজ পাওয়া গেছেঃ", "Found packages: {0}": "পাওয়া গেছে : {0}", "Found packages: {0}, not finished yet...": "পাওয়া গেছে : {0}, এখনও খোজা শেষ হয়নি। ", "General preferences": "সাধারণ পছন্দ", "GitHub profile": "GitHub প্রোফাইল", "Global": "গ্লোবাল", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "অজানা কিন্তু দরকারী ইউটিলিটি এবং অন্যান্য আকর্ষণীয় প্যাকেজগুলির দুর্দান্ত সংগ্রহস্থল৷<br>অন্তর্ভুক্ত: <b>ইউটিলিটি, কমান্ড-লাইন প্রোগ্রাম, সাধারণ সফ্টওয়্যার (অতিরিক্ত বাকেট প্রয়োজন)</b>", "Great! You are on the latest version.": null, "Grid": null, "Help": "সাহায্য", "Help and documentation": "সাহায্য এবং ডকুমেন্টেশন", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "নমস্কার। আমার নাম মার্টি। আম<PERSON> WingetUI এর <i>ডেভেলপার</i>। WingetUI সম্পূর্ণরূপে আমার অবসর সময়ে বানিয়েছি !", "Hide details": "বিস্তারিত আড়াল করুন", "Homepage": "<PERSON><PERSON><PERSON><PERSON><PERSON>জ", "Hooray! No updates were found.": "কোনো নতুন আপডেট পাওয়া যায়নি!", "How should installations that require administrator privileges be treated?": "এডমিনিস্ট্রেটরের বিশেষাধিকার প্রয়োজন এমন ইনস্টলেশনগুলিকে কীভাবে বিবেচনা করা উচিত?", "How to add packages to a bundle": null, "I understand": "আমি বুঝেছি", "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "এই প্যাকেজের জন্য ভবিষ্যতের আপডেটগুলি উপেক্ষা করুন", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "নির্বাচিত প্যাকেজ উপেক্ষা করুন", "Ignore special characters": "বিশেষ অক্ষরগুলো উপেক্ষা করুন", "Ignore updates for the selected packages": "নির্বাচিত প্যাকেজের আপডেট উপেক্ষা করুন", "Ignore updates for this package": "এই প্যাকেজের আপডেট উপেক্ষা করুন", "Ignored updates": "উপেক্ষা করা আপডেট", "Ignored version": "উপেক্ষিত সংস্করণ", "Import": "আম<PERSON><PERSON><PERSON><PERSON>", "Import packages": "প্যাকেজ আম<PERSON><PERSON>নি করুন", "Import packages from a file": "একটি ফাইল থেকে প্যাকেজ আমদানি করুন", "Import settings from a local file": "একটি স্থানীয় ফাইল থেকে সেটিংস আমদানি করুন", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "WingetUI শুরু করা হচ্ছে...", "Install": "ইনস্টল", "Install Scoop": "স্কুপ ইনস্টল করুন", "Install and more": null, "Install and update preferences": null, "Install as administrator": "এডমিনিস্ট্রেটর হিসেবে ইনস্টল করুন", "Install available updates automatically": "স্বয়ংক্রিয়ভাবে উপলব্ধ আপডেট ইনস্টল করুন", "Install location can't be changed for {0} packages": null, "Install location:": "ইন্টলের জায়গাঃ", "Install options": null, "Install packages from a file": "একটি ফাইল থেকে প্যাকেজ ইনস্টল করুন", "Install prerelease versions of UniGetUI": null, "Install script": null, "Install selected packages": "নির্বাচিত প্যাকেজ ইনস্টল করুন", "Install selected packages with administrator privileges": "প্রশাসকের বিশেষাধিকার সহ নির্বাচিত প্যাকেজগুলি ইনস্টল করুন", "Install selection": "ইনস্টল নির্বাচন করুন", "Install the latest prerelease version": "সর্বশেষ প্রি-রিলিজ সংস্করণ ইনস্টল করুন", "Install updates automatically": "স্বয়ংক্রিয়ভাবে আপডেট ইনস্টল করুন", "Install {0}": null, "Installation canceled by the user!": "ব্যবহ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> দ্বারা ইনস্টলেশন বাতিল!", "Installation failed": "ইনস্টলেশন ব্যর্থ হয়েছে", "Installation options": "ইনস্টলেশন বিকল্প", "Installation scope:": "ইনস্টলেশন সুযোগ:", "Installation succeeded": "ইনস্টলেশন সফল হয়েছে", "Installed Packages": "ইনস্টল করা প্যাকেজ", "Installed Version": "ইনস্টল করা সংস্করণ", "Installed packages": null, "Installer SHA256": "ইনস্টলার SHA256", "Installer SHA512": "ইনস্টলার SHA512", "Installer Type": "ইনস্টলার প্রকার", "Installer URL": "ইনস্টলার URL", "Installer not available": "ইনস্টলার উপলব্ধ নয়", "Instance {0} responded, quitting...": "উদাহরণ {0} সাড়া দিয়েছে, প্রস্থান করছে...", "Instant search": "তাৎক্ষণিক অনুসন্ধান", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "ইন্টারেক্টিভ ইনস্টলেশন", "Interactive operation": null, "Interactive uninstall": "ইন্টারেক্টিভ আনইনস্টল", "Interactive update": "ইন্টারেক্টিভ আপডেট", "Internet connection settings": null, "Is this package missing the icon?": "এই প্যাকেজের আইকন অনুপস্থিত?", "Is your language missing or incomplete?": "আপনার ভাষা অনুপস্থিত বা অসম্পূর্ণ?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "দেখে মনে হচ্ছে আপনি প্রশাসক হিসাবে WingetUI চালিয়েছেন, যা সুপারিশ করা হয় না। আপনি এখনও প্রোগ্রামটি ব্যবহার করতে পারেন, তবে আমরা প্রশাসকের বিশেষাধিকারের সাথে WingetUI না চালানোর পরামর্শ দিই। কেন দেখতে \"{showDetails}\"-এ ক্লিক করুন৷", "Language": null, "Language, theme and other miscellaneous preferences": "ভাষা, থিম এবং অন্যান্য বিবিধ পছন্দ", "Last updated:": "সর্বশেষ সংষ্করণঃ", "Latest": "সর্বশেষ", "Latest Version": "সবচেয়ে নতুন সংস্করণ", "Latest Version:": "সবচেয়ে নতুন সংস্করণঃ", "Latest details...": "সর্বশেষ বিবরণ...", "Launching subprocess...": "সাবপ্রসেস চালু করা হচ্ছে...", "Leave empty for default": "ডিফল্টের জন্য খালি ছেড়ে দিন", "License": "লাইসেন্স", "Licenses": "লাইসেন্স", "Light": "আ<PERSON><PERSON>", "List": null, "Live command-line output": "লাইভ কমান্ড-লাইন আউটপুট", "Live output": "লাইভ আউটপুট", "Loading UI components...": "ইউ আই উপাদানগুলো লোড হচ্ছে...", "Loading WingetUI...": "WingetUI লোড হচ্ছে...", "Loading packages": "প্যাকেজ লোড হচ্ছে", "Loading packages, please wait...": "প্যাকেজ লোড হচ্ছে, অনুগ্রহ করে অপেক্ষা করুন...", "Loading...": "লোড হচ্ছে...", "Local": "স্থানীয়", "Local PC": "স্থানীয় পিসি", "Local backup advanced options": null, "Local machine": "স্থান<PERSON><PERSON><PERSON> মেশিন", "Local package backup": null, "Locating {pm}...": "{pm} সনাক্ত করা হচ্ছে...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "লগ লেভেলঃ", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "প্যাকেজ খোঁজা হচ্ছে...", "Machine | Global": "মেশিন | গ্লোবাল", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "সেটিংস অ্যাপ থেকে WingetUI অটোস্টার্ট আচরণ পরিচালনা করুন", "Manage ignored packages": "উপেক্ষা করা প্যাকেজ পরিচালনা করুন", "Manage ignored updates": "উপেক্ষা করা আপডেটগুলি পরিচালনা করুন", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": "{0}টি উৎস পরিচালনা করুন৷", "Manifest": "উদ্ভাসিত", "Manifests": "প্রক<PERSON>শ করে", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "মাইক্রোসফটের অফিসিয়াল প্যাকেজ ম্যানেজার। সুপরিচিত এবং যাচাইকৃত প্যাকেজে পূর্ণ<br>অন্তর্ভুক্ত: <b>সাধারণ সফটওয়্যার, মাইক্রোসফ্ট স্টোর অ্যাপস</b>", "Missing dependency": "অনুপস্থিত নির্ভরতা", "More": "<PERSON><PERSON><PERSON>", "More details": "আর<PERSON> বিস্তারিত", "More details about the shared data and how it will be processed": null, "More info": "আরও তথ্য", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "নাম", "New": null, "New Version": "নতুন সংস্করণ", "New bundle": "নতুন বান্ডিল", "New version": "নতুন সংস্করণ", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "না", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "কোন প্যাকেজ পাওয়া যায়নি", "No packages found matching the input criteria": "ইনপুট মানদণ্ডের সাথে মেলে এমন কোনো প্যাকেজ পাওয়া যায়নি", "No packages have been added yet": "কোন<PERSON> প্যাকেজ এখনো যোগ করা হয়নি", "No packages selected": "কোন<PERSON> প্যাকেজ নির্বাচন করা হয়নি", "No packages were found": "কোন প্যাকেজ পাওয়া যায়নি", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": "ইনপুট মানদণ্ডের সাথে মেলে এমন কোনো ফলাফল পাওয়া যায়নি", "No sources found": "কোনো সূত্র পাওয়া যায়নি", "No sources were found": "কোন<PERSON> প্যাকেজ নির্বাচন করা হয়নি", "No updates are available": "কোন আপডেট উপলব্ধ নেই", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "নোড জেএস এর প্যাকেজ ম্যানেজার। লাইব্রেরি এবং অন্যান্য ইউটিলিটি পূর্ণ যা জাভাস্ক্রিপ্ট বিশ্বকে প্রদক্ষিণ করে<br>এতে রয়েছে: <b>নোড জাভাস্ক্রিপ্ট লাইব্রেরি এবং অন্যান্য সম্পর্কিত ইউটিলিটিগুলি</b>", "Not available": "পাওয়া যায়নি", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "খুঁজে পাওয়া যায়নি", "Not right now": "এখনই না", "Notes:": "মন্তব্যঃ", "Notification preferences": "বিজ্ঞপ্তি পছন্দ", "Notification tray options": "বিজ্ঞপ্তি ট্রে বিকল্প", "Notification types": null, "NuPkg (zipped manifest)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (জিপ করা ম্যানিফেস্ট)", "OK": "OK (ঠিকাছে)", "Ok": "<PERSON> (ঠিকাছে)", "Open": "খোলা", "Open GitHub": "<PERSON><PERSON><PERSON><PERSON> খুলুন", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": "WingetUI খুলুন", "Open backup location": "ব্যাকআপ অবস্থান খুলুন", "Open existing bundle": "বিদ্যমান বান্ডিল খুলুন", "Open install location": null, "Open the welcome wizard": "স্বাগতম উইজার্ড খুলুন", "Operation canceled by user": "ব্যবহারক<PERSON>রী দ্বারা অপারেশন বাতিল করা হয়েছে", "Operation cancelled": "অপারেশন বাতিল করা হয়েছে", "Operation history": "অপারেশন ইতিহাস", "Operation in progress": "অপারেশন চলছে", "Operation on queue (position {0})...": "সারিতে অপারেশন (অবস্থান {0})...", "Operation profile:": null, "Options saved": "বিকল্পগুলি সংরক্ষিত", "Order by:": null, "Other": "অন্যান্য", "Other settings": null, "Package": null, "Package Bundles": "প্যাকেজ বান্ডিল", "Package ID": "প্যাকেজ আইডি", "Package Manager": "প্যাকেজ ম্যানেজার", "Package Manager logs": "প্যাকেজ ম্যানেজার লগ", "Package Managers": "প্যাকেজ ম্যানেজার", "Package Name": "প্যাকেজের নাম", "Package backup": null, "Package backup settings": null, "Package bundle": "প্যাকেজ বান্ডিল", "Package details": "প্যাকেজ বিবরণ", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "প্যাকেজ ম্যানেজার পছন্দ", "Package managers": null, "Package not found": "প্যাকেজ পাওয়া যায়নি", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "প্যাকেজ", "Packages found: {0}": "প্যাকেজ পাওয়া গেছেঃ {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "ডাটাবেসে একটি বৈধ URL পেস্ট করুন", "Pause updates for": null, "Perform a backup now": "এখন একটি ব্যাকআপ সঞ্চালন", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "ব্যাকআপ করা হচ্ছে, অনুগ্রহ করে অপেক্ষা করুন...", "Periodically perform a backup of the installed packages": "পর্যায়ক<PERSON><PERSON><PERSON><PERSON> ইনস্টল করা প্যাকেজগুলির একটি ব্যাকআপ সঞ্চালন করুন", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": "চালিয়ে যেতে অনুগ্রহ করে \"চালিয়ে যান\" এ ক্লিক করুন", "Please enter at least 3 characters": "অনুগ্রহ করে কমপক্ষে ৩টি অক্ষর লিখুন", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "অনুগ্রহ করে মনে রাখবেন যে এই মেশিনে সক্ষম প্যাকেজ পরিচালকদের কারণে কিছু প্যাকেজ ইনস্টলযোগ্য নাও হতে পারে।", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "দয়া করে মনে রাখবেন যে নির্দিষ্ট উত্স থেকে প্যাকেজগুলি রপ্তানিযোগ্য নাও হতে পারে৷ সেগুলিকে ধূসর করা হয়েছে এবং রপ্তানি করা হবে না।", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "অনুগ্রহ করে কমান্ড-লাইন আউটপুট দেখুন বা সমস্যা সম্পর্কে আরও তথ্যের জন্য অপারেশন ইতিহাস পড়ুন।", "Please select how you want to configure WingetUI": "আপনি কিভাবে WingetUI কনফিগার করতে চান দয়া করে নির্বাচন করুন", "Please try again later": null, "Please type at least two characters": "অন্তত দুটি অক্ষর টাইপ করুন", "Please wait": "অনুগ্রহপূর্বক অপেক্ষা করুন", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "একটু অপেক্ষা করুন...", "Portable": "সু<PERSON><PERSON>", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "পাওয়ারশেলের প্যাকেজ ম্যানেজার। PowerShell ক্ষমতা প্রসারিত করতে লাইব্রেরি এবং স্ক্রিপ্ট খুঁজুন<br>অন্তর্ভুক্তঃ <b>মডিউল, স্ক্রিপ্ট, Cmdlets</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "প্রি-রিল<PERSON>জ", "Preparing packages, please wait...": "প্যাকেজ প্রস্তুত করা হচ্ছে, অনুগ্রহ করে অপেক্ষা করুন...", "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "প্রকাশনার তারিখঃ", "Publisher": "প্রকাশক", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "পাইথনের লাইব্রেরি ম্যানেজার। পাইথন লাইব্রেরি এবং অন্যান্য পাইথন-সম্পর্কিত ইউটিলিটিগুলিতে পরিপূর্ণ<br>অন্তর্ভুক্ত: <b>পাইথন লাইব্রেরি এবং সম্পর্কিত ইউটিলিটিগুলি</b>", "Quit": "বন্ধ করুন", "Quit WingetUI": "WingetUI প্রস্থান করুন", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": "প্যাকেজ পুনরায় ইনস্টল করুন", "Related settings": null, "Release notes": "অব্যাহতি পত্র", "Release notes URL": "রিলিজ নোট URL", "Release notes URL:": "রিলিজ নোট URL:", "Release notes:": "অব্যাহতি পত্রঃ", "Reload": "পুনরায় লোড করুন", "Reload log": "লগ পুনরায় লোড করুন", "Removal failed": "সরানো ব্যর্থ হয়েছে", "Removal succeeded": "অপসারণ সফল হয়েছে", "Remove from list": "তালিকা থেকে বাদ দিন", "Remove permanent data": "পার্মানেন্ট ডেটা মুছে দিন", "Remove selection from bundle": "বান্ডেল থেকে নির্বাচন সরান", "Remove successful installs/uninstalls/updates from the installation list": "ইনস্টলেশন তালিকা থেকে সফল ইনস্টল/আনইনস্টল/আপডেটগুলি সরান", "Removing source {source}": null, "Removing source {source} from {manager}": "{manager} থেকে উৎস {source} সরানো হচ্ছে", "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": "একটি সমস্যা রিপোর্ট করুন বা একটি বৈশিষ্ট্য অনুরোধ জমা দিন", "Repository": "ভান্ডার", "Reset": "রিসেট করুন", "Reset Scoop's global app cache": "<PERSON><PERSON> এর গ্লোবাল অ্যাপ ক্যাশ রিসেট করুন", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "উইনগেট সোর্স রিসেট করুন (কোন প্যাকেজ তালিকাভুক্ত না থাকলে সাহায্য করতে পারে)", "Reset WingetUI": "WingetUI রিসেট করুন", "Reset WingetUI and its preferences": "WingetUI এবং এর পছন্দগুলি রিসেট করুন", "Reset WingetUI icon and screenshot cache": "WingetUI আইকন এবং স্ক্রিনশট ক্যাশে রিসেট করুন", "Reset list": null, "Resetting Winget sources - WingetUI": "Winget উৎস রিসেট করা হচ্ছে - WingetUI", "Restart": null, "Restart UniGetUI": "UniGetUI পুনরায় চালু করুন", "Restart WingetUI": "WingetUI পুনরায় চালু করুন", "Restart WingetUI to fully apply changes": "পরিবর্তনগুলি সম্পূর্ণরূপে প্রয়োগ করতে WingetUI পুনরায় চালু করুন", "Restart later": "পরে পুনরায় আরম্ভ করুন", "Restart now": "এখন আবার পুনরায় চালু করুন", "Restart required": "রিস্টার্ট প্রয়োজন", "Restart your PC to finish installation": "ইনস্টলেশন শেষ করতে আপনার পিসি পুনরায় চালু করুন", "Restart your computer to finish the installation": "ইনস্টলেশন শেষ করতে আপনার কম্পিউটার পুনরায় চালু করুন", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "পুনরায় চেষ্টা করা", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": "পুনরায় চেষ্টা করা হচ্ছে, অনুগ্রহ করে অপেক্ষা করুন...", "Return to top": "উপরে ফেরত যান", "Run": "চালান", "Run as admin": "এডমিনিস্ট্রেটর হিসেবে চালান", "Run cleanup and clear cache": "পরিষ্কার চালান এবং ক্যাশে পরিষ্কার করুন", "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "ইনস্টলার চালানো হচ্ছে...", "Running the uninstaller...": "আনইনস্টলার চালানো হচ্ছে...", "Running the updater...": "আপডেটার চালানো হচ্ছে...", "Save": null, "Save File": "ফাইল সংরক্ষণ", "Save and close": "সংরক্ষণ করেন এবং বন্ধ করেন", "Save as": null, "Save bundle as": "এই হিসাবে বান্ডিল সংরক্ষণ করুন", "Save now": "এখনই সংরক্ষণ করুন", "Saving packages, please wait...": "প্যাকেজ সংরক্ষণ করা হচ্ছে, অনুগ্রহ করে অপেক্ষা করুন...", "Scoop Installer - WingetUI": "স্কুপ ইনস্টলার - WingetUI", "Scoop Uninstaller - WingetUI": "স্কুপ আনইনস্টলার - WingetUI", "Scoop package": "স্কুপ প্যাকেজ", "Search": "অনুসন্ধান করুন", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "ডেস্কটপ সফ্টওয়্যার অনুসন্ধান করুন, আপডেটগুলি উপলব্ধ হলে আমাকে সতর্ক করুন এবং অযৌক্তিক জিনিসগুলি করবেন না। আমি চাই না উইনগেটইউআই অতিরিক্ত জটিল হোক, আমি চাই একটি সাধারণ <b>সফ্টওয়্যার স্টোর</b>", "Search for packages": "প্যাকেজ অনুসন্ধান করুন", "Search for packages to start": "শুরু করার জন্য প্যাকেজ অনুসন্ধান করুন", "Search mode": "অনুসন্ধান মোড", "Search on available updates": "উপলব্ধ আপডেট অনুসন্ধান করুন", "Search on your software": "আপনার সফ্টওয়্যার অনুসন্ধান করুন", "Searching for installed packages...": "ইনস্টল করা প্যাকেজগুলির জন্য অনুসন্ধান করা হচ্ছে...", "Searching for packages...": "প্যাকেজ খোঁজা হচ্ছে...", "Searching for updates...": "আপডেটের জন্য অনুসন্ধান করা হচ্ছে...", "Select": "নির্বাচন করুন", "Select \"{item}\" to add your custom bucket": "আপনার কাস্টম বাকেট যোগ করতে \"{item}\" নির্বাচন করুন", "Select a folder": "একটি ফোল্ডার নির্বাচন করুন", "Select all": "সব নির<PERSON><PERSON><PERSON><PERSON>ন করুন", "Select all packages": "সব প্যাকেজ নির্বাচন করুন", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "শুধ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <b>যদি আপনি জানেন যে আপনি কি করছেন</b> নির্বাচন করুন।", "Select package file": "প্যাকেজ ফাইল নির্বাচন করুন", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "আপনি যে উৎসটি যোগ করতে চান তা নির্বাচন করুনঃ", "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "কোন <b>প্যাক<PERSON><PERSON> ম্যানেজার</b> ব্যবহার করবেন ({0}) নির্বাচন করুন, প্যাকেজগুলি কীভাবে ইনস্টল করা হয় তা কনফিগার করুন, প্রশাসকের অধিকারগুলি কীভাবে পরিচালনা করা হয় তা পরিচালনা করুন ইত্যাদি।", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "হ্যান্ডশেক পাঠানো হয়েছে। উদাহরণ শ্রোতার উত্তরের জন্য অপেক্ষা করা হচ্ছে... ({0}%)", "Set a custom backup file name": "একটি কাস্টম ব্যাকআপ ফাইলের নাম সেট করুন", "Set custom backup file name": "Set custom backup file name\n", "Settings": "সেটিংস", "Share": "শেয়<PERSON>র", "Share WingetUI": "WingetUI শেয়ার করুন", "Share anonymous usage data": null, "Share this package": "এই প্যাকেজ শেয়ার করুন", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "সিস্টেম ট্রেতে UniGetUI দেখান", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "WingetUI দেখান", "Show a notification when an installation fails": "ইনস্টলেশন ব্যর্থ হলে একটি বিজ্ঞপ্তি দেখান", "Show a notification when an installation finishes successfully": "একটি ইনস্টলেশন সফলভাবে শেষ হলে একটি বিজ্ঞপ্তি দেখান", "Show a notification when an operation fails": "অপারেশন ব্যর্থ হলে একটি বিজ্ঞপ্তি দেখান", "Show a notification when an operation finishes successfully": "একটি অপারেশন সফলভাবে শেষ হলে একটি বিজ্ঞপ্তি দেখান", "Show a notification when there are available updates": "আপডেট পাওয়া গেলে একটি বিজ্ঞপ্তি দেখান", "Show a silent notification when an operation is running": "অপারেশন চলাকালীন একটি নীরব বিজ্ঞপ্তি দেখান", "Show details": "বিস্তারিত দেখাও", "Show in explorer": null, "Show info about the package on the Updates tab": "আপডেট ট্যাবে প্যাকেজ সম্পর্কে তথ্য দেখান", "Show missing translation strings": "অনুপস্থিত অনুবাদ স্ট্রিং দেখান", "Show notifications on different events": "অপারেশন চলাকালীন একটি নীরব বিজ্ঞপ্তি দেখান", "Show package details": "প্যাকেজের বিবরণ দেখান", "Show package icons on package lists": null, "Show similar packages": "অনুরূপ প্যাকেজ দেখান", "Show the live output": "লাইভ আউটপুট দেখান", "Size": "আ<PERSON><PERSON><PERSON>", "Skip": "এড়িয়ে যান", "Skip hash check": "হ্যাশ চেক করা বাদ দিন", "Skip hash checks": null, "Skip integrity checks": "অখণ্ডতা পরীক্ষা এড়িয়ে যান", "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "নির্বাচিত প্যাকেজ ইনস্টল করার সময় হ্যাশ চেক এড়িয়ে যান", "Skip the hash check when updating the selected packages": "নির্বাচিত প্যাকেজ আপডেট করার সময় হ্যাশ চেক এড়িয়ে যান", "Skip this version": "এই সংস্করণে এড়িয়ে যান", "Software Updates": "সফটওয়্যার আপডেট", "Something went wrong": "কিছু ভুল হয়েছে", "Something went wrong while launching the updater.": null, "Source": "উৎস", "Source URL:": "উৎস URL", "Source added successfully": null, "Source addition failed": "উৎস যোগ ব্যর্থ হয়েছে", "Source name:": "উৎসের নামঃ", "Source removal failed": "উৎস অপসারণ ব্যর্থ হয়েছে", "Source removed successfully": null, "Source:": "উৎসঃ", "Sources": "সূত্র", "Start": "শুর<PERSON> করুন", "Starting daemons...": "ডেমন শুরু হচ্ছে...", "Starting operation...": null, "Startup options": "স্টার্টআপ বিকল্প", "Status": "স্ট্যাটাস", "Stuck here? Skip initialization": "এখানে আটকে? সূচনা এড়িয়ে যান", "Success!": null, "Suport the developer": "ডেভেলপারদের সমর্থন করুন", "Support me": "আমাকে সমর<PERSON><PERSON>ন করুন", "Support the developer": "ডেভেলপারদের সমর্থন করুন", "Systems are now ready to go!": "সিস্টেম এখন যেতে প্রস্তুত!", "Telemetry": null, "Text": "পাঠ্য", "Text file": "লেখার ফাইল", "Thank you ❤": "ধন্যবাদ ❤", "Thank you 😉": "ধন্যবাদ 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": "ব্যাকআপে কোনও বাইনারি ফাইল বা কোনও প্রোগ্রামের সংরক্ষিত ডেটা অন্তর্ভুক্ত থাকবে না।", "The backup will be performed after login.": "ব্যাকআপ লগইন করার পরে সঞ্চালিত হবে।", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "ব্যাকআপ ইনস্টল করা প্যাকেজের সম্পূর্ণ তালিকা এবং তাদের ইনস্টলেশন বিকল্পগুলি অন্তর্ভুক্ত করবে। উপেক্ষিত আপডেট এবং এড়িয়ে যাওয়া সংস্করণগুলিও সংরক্ষণ করা হবে।", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "ইনস্টলারের চেকসাম প্রত্যাশিত মানের সাথে মিলে না এবং ইনস্টলারের সত্যতা যাচাই করা যায় না। আপনি যদি প্রকাশককে বিশ্বাস করেন, তাহলে {0} প্যাকেজটি আবার হ্যাশ চেক এড়িয়ে যাচ্ছে।", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "উইন্ডোজের জন্য ক্লাসিক্যাল প্যাকেজ ম্যানেজার। আপনি সেখানে সবকিছু পাবেন. <br>ধারণ করে: <b>সাধারণ সফ্টওয়্যার</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "বর্তমান বান্ডেলে কোনো প্যাকেজ নেই। শুরু করতে কিছু প্যাকেজ যোগ করুন", "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "নিম্নলিখিত প্যাকেজগুলি একটি JSON ফাইলে রপ্তানি করা হবে৷ কোন ব্যবহারকারীর তথ্য বা বাইনারি সংরক্ষণ করা যাচ্ছে না।", "The following packages are going to be installed on your system.": "নিম্নলিখিত প্যাকেজ আপনার সিস্টেমে ইনস্টল করা যাচ্ছে।", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "এই প্যাকেজটি ইনস্টল করা, আপডেট করা বা সরানো হলে নিম্নলিখিত সেটিংস প্রয়োগ করা হবে।", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "এই প্যাকেজটি ইনস্টল করা, আপডেট করা বা সরানো হলে নিম্নলিখিত সেটিংস প্রয়োগ করা হবে। তারা স্বয়ংক্রিয়ভাবে সংরক্ষণ করা হবে।", "The icons and screenshots are maintained by users like you!": "আইকন এবং স্ক্রিনশট আপনার মত ব্যবহারকারীদের দ্বারা রক্ষণাবেক্ষণ করা হয়!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "ইনস্টলারের একটি অবৈধ চেকসাম আছে", "The installer hash does not match the expected value.": "ইনস্টলার হ্যাশ প্রত্যাশিত মানের সাথে মেলে না।", "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "এই প্রকল্পের মূল লক্ষ্য হল উইন্ডোজ এর জন্য সবচেয়ে সাধারণ সিএলআই প্যাকেজ পরিচালকদের জন্য একটি সজ্ঞান্মুলক ইউআই তৈরি করা, যেমন উইনগেট এবং স্কুপ ৷", "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": "প্যাকেজ {0} {1} থেকে পাওয়া যায়নি৷", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "আপডেটের জন্য চেক করার সময় এখানে তালিকাভুক্ত প্যাকেজগুলি অ্যাকাউন্টে নেওয়া হবে না। তাদের আপডেট উপেক্ষা করা বন্ধ করতে তাদের ডাবল-ক্লিক করুন বা তাদের ডানদিকের বোতামটি ক্লিক করুন।", "The selected packages have been blacklisted": "নির্বাচিত প্যাকেজ কালো তালিকাভুক্ত করা হয়েছে", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "ব্যাকআপের আকার 1MB এর কম বলে অনুমান করা হয়।", "The source {source} was added to {manager} successfully": "উৎস {source} সফলভাবে {manager}-এ যোগ করা হয়েছে", "The source {source} was removed from {manager} successfully": "উৎস {source} সফলভাবে {manager} থেকে সরানো হয়েছে", "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": "WingetUI বন্ধ করার পরে আপডেটটি ইনস্টল করা হবে", "The update will not continue.": "আপডেট চলতে থাকবে না।", "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "চলমান অভিযান চলছে। UniGetUI ত্যাগ করা তাদের ব্যর্থ হতে পারে। আপনি কি চালিয়ে যেতে চান?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "ইউটিউবে কিছু দুর্দান্ত ভিডিও রয়েছে যা WingetUI এবং এর ক্ষমতা প্রদর্শন করে। আপনি দরকারী কৌশল এবং টিপস শিখতে পারে!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "প্রশাসক হিসাবে WingetUI না চালানোর দুটি প্রধান কারণ রয়েছে: প্রথমটি হল যে Scoop প্যাকেজ ম্যানেজার প্রশাসকের অধিকারের সাথে চালানোর সময় কিছু কমান্ডের সাথে সমস্যা সৃষ্টি করতে পারে। দ্বিতীয়টি হল প্রশাসক হিসাবে WingetUI চালানোর অর্থ হল যে আপনি যে প্যাকেজ ডাউনলোড করবেন তা প্রশাসক হিসাবে চালানো হবে (এবং এটি নিরাপদ নয়)। মনে রাখবেন যে যদি আপনাকে প্রশাসক হিসাবে একটি নির্দিষ্ট প্যাকেজ ইনস্টল করতে হয়, আপনি সর্বদা আইটেমটিতে ডান-ক্লিক করতে পারেন -> প্রশাসক হিসাবে ইনস্টল/আপডেট/আনইনস্টল করুন।", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "একটি ইনস্টলেশন প্রক্রিয়াধীন আছে. আপনি যদি WingetUI বন্ধ করেন, তাহলে ইনস্টলেশন ব্যর্থ হতে পারে এবং অপ্রত্যাশিত ফলাফল হতে পারে। আপনি কি এখনও WingetUI ছেড়ে যেতে চান?", "They are the programs in charge of installing, updating and removing packages.": "তারা প্যাকেজ ইনস্টল, আপডেট এবং অপসারণের দায়িত্বে থাকা প্রোগ্রাম।", "Third-party licenses": "তৃতীয় পক্ষের লাইসেন্স", "This could represent a <b>security risk</b>.": "এটি একটি <b>নিরাপত্তা ঝুঁকি</b> প্রতিনিধিত্ব করতে পারে।", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "এটি সম্ভবত এই কারণে যে আপনাকে পাঠানো প্যাকেজটি সরানো হয়েছে, বা প্যাকেজ ম্যানেজারে প্রকাশিত হয়েছে যা আপনি সক্ষম করেননি। প্রাপ্ত ID হল {0}", "This is the <b>default choice</b>.": "এটি <b>ডিফল্ট পছন্দ</b>।", "This may help if WinGet packages are not shown": "UniGetUI প্যাকেজ দেখানো না হলে এটি সাহায্য করতে পারে", "This may help if no packages are listed": null, "This may take a minute or two": "এতে এক বা দুই মিনিট সময় লাগতে পারে", "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "এই প্যাকেজ আপডেট করা যেতে পারে", "This package can be updated to version {0}": "এই প্যাকেজটি {0} সংস্করণে আপডেট করা যেতে পারে", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "এই প্যাকেজের কোন স্ক্রিনশট নেই বা আইকনটি নেই? আমাদের উন্মুক্ত, পাবলিক ডাটাবেসে অনুপস্থিত আইকন এবং স্ক্রিনশট যোগ করে UniGetUI-তে অবদান রাখুন।", "This package is already installed": "এই প্যাকেজ ইতিমধ্যে ইনস্টল করা আছে", "This package is being processed": "এই প্যাকেজ প্রক্রিয়া করা হচ্ছে", "This package is not available": null, "This package is on the queue": "এই প্যাকেজ সারিতে আছে", "This process is running with administrator privileges": "এই প্রক্রিয়াটি প্রশাসকের বিশেষাধিকারের সাথে চলছে", "This project has no connection with the official {0} project — it's completely unofficial.": "এই প্রকল্পের সাথে অফিসিয়াল {0} প্রকল্পের কোনো সংযোগ নেই — এটি সম্পূর্ণরূপে অনানুষ্ঠানিক৷", "This setting is disabled": "এই সেটিং অক্ষম করা আছে", "This wizard will help you configure and customize WingetUI!": "এই উইজার্ড আপনাকে WingetUI কনফিগার এবং কাস্টমাইজ করতে সাহায্য করবে!", "Toggle search filters pane": "অনুসন্ধান ফিল্টার ফলক টগল করুন", "Translators": "অনুবাদক", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "আপনি যে উৎস যোগ করতে চান তার নাম এবং URL এখানে টাইপ করুন, একটি স্থান দ্বারা পৃথক করুন।", "Unable to find package": "প্যাকেজটি খুঁজে পাওয়া যায়নি", "Unable to load informarion": "তথ্য খুঁজে পাওয়া যায়নি", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI কোনো সামঞ্জস্যপূর্ণ প্যাকেজ পরিচালকদের সাথে সম্পর্কিত নয়। UniGetUI একটি স্বাধীন প্রকল্প।", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "আনইনস্টল করুন", "Uninstall Scoop (and its packages)": "স্কুপ আনইনস্টল করুন (এবং এর প্যাকেজ)", "Uninstall and more": null, "Uninstall and remove data": "আনইনস্টল এবং ডেটা অপসারণ", "Uninstall as administrator": "এডমিনিস্ট্রেটর হিসেবে আনইনস্টল করুন ", "Uninstall canceled by the user!": "ব্যব<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> দ্বারা আনইনস্টল বাতিল!", "Uninstall failed": "আনইনস্টল ব্যর্থ হয়েছে৷", "Uninstall options": null, "Uninstall package": "প্যাকেজ আনইনস্টল করুন", "Uninstall package, then reinstall it": "প্যাকেজ আনইনস্টল করুন, তার<PERSON>র এটি পুনরায় ইনস্টল করুন", "Uninstall package, then update it": "প্যাকেজ আনইনস্টল করুন, তারপর এটি আপডেট করুন", "Uninstall previous versions when updated": null, "Uninstall selected packages": "নির্বাচিত প্যাকেজ আনইনস্টল করুন", "Uninstall selection": null, "Uninstall succeeded": "আনইনস্টল সফল হয়েছে৷", "Uninstall the selected packages with administrator privileges": "প্রশাসকের বিশেষাধিকার সহ নির্বাচিত প্যাকেজগুলি আনইনস্টল করুন", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "\"{0}\" হিসাবে তালিকাভুক্ত মূলের সাথে আনইনস্টলযোগ্য প্যাকেজগুলি কোনও প্যাকেজ পরিচালকে প্রকাশিত হয় না, তাই তাদের সম্পর্কে দেখানোর জন্য কোনও তথ্য উপলব্ধ নেই৷", "Unknown": "অজানা", "Unknown size": "অজানা সাইজ", "Unset or unknown": null, "Up to date": null, "Update": "হাল<PERSON><PERSON>গাদ", "Update WingetUI automatically": "স্বয়ংক্রিয়ভাবে WingetUI হালনাগাদ করুন", "Update all": "সব হাল<PERSON><PERSON><PERSON><PERSON><PERSON> করুন", "Update and more": null, "Update as administrator": "এডমিনিস্ট্রেটর হিসেবে আপডেট করুন", "Update check frequency, automatically install updates, etc.": null, "Update checking": null, "Update date": "তারিখ আপডেট করুন", "Update failed": "আপডেট ব্যর্থ হয়েছে", "Update found!": "আপডেট পাওয়া গেছে!", "Update now": "এখনই আপডেট করুন", "Update options": null, "Update package indexes on launch": null, "Update packages automatically": "স্বয়ংক্রিয়ভাবে প্যাকেজ আপডেট করুন", "Update selected packages": "নির্বাচিত প্যাকেজ আপডেট করুন", "Update selected packages with administrator privileges": "প্রশাসকের বিশেষাধিকার সহ নির্বাচিত প্যাকেজ আপডেট করুন", "Update selection": null, "Update succeeded": null, "Update to version {0}": null, "Update to {0} available": "{0} এর আপডেট উপলব্ধ", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "আপডেট", "Updates available!": "আপডেট উপলব্ধ!", "Updates for this package are ignored": "এই প্যাকেজের জন্য আপডেট উপেক্ষা করা হয়", "Updates found!": "আপডেট পাওয়া গেছে!", "Updates preferences": null, "Updating WingetUI": "WingetUI আপডেট করা হচ্ছে", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "PowerShell CMDLets এর পরিবর্তে Legacy bundled WinGet ব্যবহার করুন", "Use a custom icon and screenshot database URL": "একটি কাস্টম আইকন এবং স্ক্রিনশট ডাটাবেস URL ব্যবহার করুন", "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "বান্ডিল এর পরিবর্তে ইনস্টল করা GSudo ব্যবহার করুন (অ্যাপ পুনরায় চালু করতে হবে)", "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": "সিস্টেম চকোলেট ব্যবহার করুন (পুনরায় চালু করতে হবে)", "Use system Winget (Needs a restart)": "সিস্টেম Winget ব্যবহার করুন (পুনঃসূচনা প্রয়োজন)", "Use system Winget (System language must be set to english)": null, "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": null, "User": "ব্<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User interface preferences": "ব্যবহারকারীর ইন্টারফেস পছন্দসমূহ", "User | Local": null, "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": null, "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": "সংস্করণ", "Version to install:": "ইনস্টল করার জন্য সংস্করণঃ", "Version:": null, "View GitHub Profile": null, "View WingetUI on GitHub": "GitHub এ WingetUI দেখুন", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "UniGetUI সোর্স কোড দেখুন। সেখান থেকে, আপনি বাগ রিপোর্ট করতে পারেন বা বৈশিষ্ট্যগুলির পরামর্শ দিতে পারেন, বা ইভেন্টগুলি সরাসরি The WingetUI প্রকল্পে অবদান রাখতে পারেন৷", "View mode:": null, "View on UniGetUI": null, "View page on browser": "ব্রাউজারে পৃষ্ঠা দেখুন", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "অন্যান্য ইনস্টলেশন শেষ হওয়ার জন্য অপেক্ষা করা হচ্ছে...", "Waiting for {0} to complete...": null, "Warning": "সতর্কতা", "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": "আমরা এই প্যাকেজ সম্পর্কে বিস্তারিত তথ্য লোড করতে পারিনি, কারণ এটি আপনার কোনো প্যাকেজের উৎসে পাওয়া যায়নি।", "We could not load detailed information about this package, because it was not installed from an available package manager.": "আমরা এই প্যাকেজ সম্পর্কে বিস্তারিত তথ্য লোড করতে পারিনি, কারণ এটি একটি উপলব্ধ প্যাকেজ ম্যানেজার থেকে ইনস্টল করা হয়নি।", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "আমরা {action} {package} করতে পারিনি। অনুগ্রহ করে একটু পরে আবার চেষ্টা করুন. ইনস্টলার থেকে লগগুলি পেতে \"{showDetails}\" এ ক্লিক করুন৷", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "আমরা {action} {package} করতে পারিনি। অনুগ্রহ করে একটু পরে আবার চেষ্টা করুন. আনইনস্টলার থেকে লগগুলি পেতে \"{showDetails}\" এ ক্লিক করুন৷", "We couldn't find any package": null, "Welcome to WingetUI": "WingetUI তে স্বাগতম", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "আপনি কোন প্যাকেজ পরিচালকদের ব্যবহার করতে চান?", "Which source do you want to add?": "আপনি কোন উৎস যোগ করতে চান?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": null, "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": null, "WingetUI - Everything is up to date": "WingetUI - সবকিছু আপ টু ডেট", "WingetUI - {0} updates are available": "WingetUI - {0}টি আপডেট পাওয়া গেছে ", "WingetUI - {0} {1}": "WingetUI - {0} {1}\n", "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": null, "WingetUI Log": null, "WingetUI Repository": null, "WingetUI Settings": "WingetUI সেটিংস", "WingetUI Settings File": "WingetUI সেটিংস ফাইল", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": null, "WingetUI autostart behaviour, application launch settings": "WingetUI অটোস্টার্ট আচরণ, অ্যাপ্লিকেশন লঞ্চ সেটিংস", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI আপনার সফ্টওয়্যার আপডেটগুলি উপলব্ধ আছে কিনা তা পরীক্ষা করতে পারে এবং আপনি চাইলে স্বয়ংক্রিয়ভাবে সেগুলি ইনস্টল করতে পারেন৷", "WingetUI display language:": "WingetUI ভাষা প্রদর্শন:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": null, "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": null, "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI কোনো যন্ত্র বা কম্পিউটার দিয়ে অনুবাদ করা হয়নি! নিম্নলিখিত ব্যক্তিরা অনুবাদগুলির দায়িত্বে রয়েছেন:\n", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI আপডেট করা হচ্ছে। সমাপ্ত হলে, WingetUI নিজেই পুনরায় চালু হবে", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UnigetUI বিনামূল্য, এবং এটি চিরতরে বিনামূল্যে থাকবে। কোন বিজ্ঞাপন, কোন ক্রেডিট কার্ড, কোন প্রিমিয়াম সংস্করণ. 100% বিনামূল্যে, চিরতরে।", "WingetUI log": "WingetUI লগ", "WingetUI tray application preferences": "WingetUI ট্রে অ্যাপ্লিকেশন পছন্দসমূহ", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": null, "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI পর্যায়ক্রমে আপডেটের জন্য চেক করবে না। সেগুলি এখনও লঞ্চে চেক করা হবে, কিন্তু আপনাকে সেগুলি সম্পর্কে সতর্ক করা হবে না৷", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "উইনগেটইউআই একটি UAC প্রম্পট দেখাবে প্রতিবার যখন একটি প্যাকেজ ইনস্টল করার জন্য উচ্চতার প্রয়োজন হয়।", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "আমাদের প্রিয় অবদানকারীদের সাহায্যে উইনগেটইউআই সম্ভব হতো না। তাদের GitHub প্রোফাইল দেখুন, UniGetUI তাদের ছাড়া সম্ভব হবে না!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "অবদানকারীদের সাহায্য ছাড়া UniGetUI সম্ভব হত না। সবাইকে ধন্যবাদ 🥳", "WingetUI {0} is ready to be installed.": null, "Write here the process names here, separated by commas (,)": null, "Yes": "হ্যাঁ", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": null, "You have installed WingetUI Version {0}": null, "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": "আপনি চাইলে পরে আপনার কম্পিউটার পুনরায় চালু করতে পারেন", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "আপনাকে শুধুমাত্র একবার অনুরোধ করা হবে, এবং প্রশাসকের অধিকার দেওয়া হবে প্যাকেজগুলিকে যা তাদের অনুরোধ করবে।", "You will be prompted only once, and every future installation will be elevated automatically.": "আপনাকে শুধুমাত্র একবার অনুরোধ করা হবে, এবং প্রতিটি ভবিষ্যতের ইনস্টলেশন স্বয়ংক্রিয়ভাবে উন্নত হবে।", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": "একটি কফি কিনে দিয়ে সাহায্য করুন", "extracted": null, "feature": null, "formerly WingetUI": null, "homepage": "ওয়েবসাইট", "install": "ইনস্টল", "installation": "স্থাপন", "installed": "ইনস্টল করা হয়েছে", "installing": "ইনস্টল করা হচ্ছে", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "আনইনস্টল", "uninstallation": "আনইনস্টলেশন", "uninstalled": "আনইনস্টল করা হয়েছে", "uninstalling": "আনইনস্টল করা হচ্ছে", "update(noun)": "আপডেট", "update(verb)": "আপডেট", "updated": "আপডেট করা হয়েছে", "updating": "আপডেট করা হচ্ছে", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} আনইনস্টলেশন", "{0} aborted": "{0} বাতিল করা হয়েছে", "{0} can be updated": "{0} আপডেট করা সম্ভব", "{0} can be updated to version {1}": null, "{0} days": "{0} দিন", "{0} desktop shortcuts created": null, "{0} failed": "{0} ব্যর্থ", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} ঘণ্টা", "{0} installation": "{0} ইন্সটলেশন", "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} আপডেট করা হচ্ছে", "{0} is being updated to version {1}": null, "{0} is disabled": "{0} বন্ধ রয়েছে", "{0} minutes": "{0} মিনিট", "{0} months": null, "{0} packages are being updated": "{0}টি প্যাকেজ আপডেট করা হচ্ছে", "{0} packages can be updated": "{0}টি প্যাকেজ আপডেট করা যেতে পারে", "{0} packages found": "{0}কেজ পাওয়া গিয়েছে", "{0} packages were found": "{0}টি প্যাকেজ পাওয়া গেছে", "{0} packages were found, {1} of which match the specified filters.": null, "{0} selected": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} সফল হয়েছে", "{0} update": "{0} আপডেট", "{0} updates are available": null, "{0} was {1} successfully!": "{0} সফলভাবে {1} হয়েছে!", "{0} weeks": null, "{0} years": null, "{0} {1} failed": "{0} {1} ব্যর্থ", "{package} Installation": null, "{package} Uninstall": null, "{package} Update": null, "{package} could not be installed": null, "{package} could not be uninstalled": null, "{package} could not be updated": null, "{package} installation failed": null, "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": null, "{package} update failed": null, "{package} update failed. Click here for more details.": null, "{package} was installed successfully": null, "{package} was uninstalled successfully": null, "{package} was updated successfully": null, "{pcName} installed packages": null, "{pm} could not be found": "{pm} পাওয়া যায়নি", "{pm} found: {state}": "{pm} পাওয়া গেছে: {state}", "{pm} is disabled": null, "{pm} is enabled and ready to go": null, "{pm} package manager specific preferences": "{pm} প্যাকেজ ম্যানেজার নির্দিষ্ট পছন্দ", "{pm} preferences": "{pm} পছন্দ", "{pm} version:": null, "{pm} was not found!": null}