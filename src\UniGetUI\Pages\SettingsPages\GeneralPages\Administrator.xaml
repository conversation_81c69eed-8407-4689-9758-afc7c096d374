<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.GeneralPages.Administrator"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="using:CommunityToolkit.WinUI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages.GeneralPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    mc:Ignorable="d">

    <Page.Resources>
        <converters:BoolNegationConverter x:Key="BoolNegationConverter" />
    </Page.Resources>

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel>

            <InfoBar
                x:Name="WarningTitlebar"
                CornerRadius="8"
                IsClosable="False"
                IsOpen="True"
                Severity="Warning" />

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Change how operations request administrator rights" />

            <widgets:CheckboxCard
                x:Name="DoCacheAdminRightsForBatches"
                BorderThickness="1,1,1,0"
                CornerRadius="8,8,0,0"
                IsEnabled="{x:Bind ProhibitElevator._checkbox.IsOn, Mode=OneWay, Converter={StaticResource BoolNegationConverter}}"
                SettingName="DoCacheAdminRightsForBatches"
                StateChanged="RestartCache"
                Text="Ask for administrator privileges once for each batch of operations" />

            <widgets:CheckboxCard
                x:Name="DoCacheAdminRights"
                CornerRadius="0,0,8,8"
                SettingName="DoCacheAdminRights"
                IsEnabled="{x:Bind ProhibitElevator._checkbox.IsOn, Mode=OneWay, Converter={StaticResource BoolNegationConverter}}"
                StateChanged="RestartCache"
                Text="Ask only once for administrator privileges" />

            <UserControl Height="16" />

            <widgets:CheckboxCard
                x:Name="ProhibitElevator"
                CornerRadius="8"
                SettingName="ProhibitElevation"
                StateChanged="ShowRestartBanner"
                Text="Prohibit any kind of Elevation via UniGetUI Elevator or GSudo">
                <widgets:CheckboxCard.Description>
                    <widgets:TranslatedTextBlock
                        Foreground="{ThemeResource SystemControlErrorTextForegroundBrush}"
                        Text="This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK."/>
                </widgets:CheckboxCard.Description>
            </widgets:CheckboxCard>
            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Restrictions on package operations" />

            <widgets:SecureCheckboxCard
                x:Name="AllowCLIArguments"
                CornerRadius="8,8,0,0"
                SettingName="AllowCLIArguments"
                Text="Allow custom command-line arguments"
                WarningText="Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution." />

            <widgets:SecureCheckboxCard
                x:Name="AllowPrePostInstallCommands"
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,8,8"
                SettingName="AllowPrePostOpCommand"
                Text="Ignore custom pre-install and post-install commands when importing packages from a bundle"
                WarningText="Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully" />

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Restrictions on package managers" />

            <widgets:SecureCheckboxCard
                x:Name="AllowCustomManagerPaths"
                CornerRadius="8"
                SettingName="AllowCustomManagerPaths"
                Text="Allow changing the paths for package manager executables"
                WarningText="Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous" />

            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Restrictions when importing package bundles" />

            <widgets:SecureCheckboxCard
                x:Name="AllowImportingCLIArguments"
                CornerRadius="8,8,0,0"
                IsEnabled="{x:Bind AllowCLIArguments._checkbox.IsOn, Mode=OneWay}"
                SettingName="AllowImportingCLIArguments"
                Text="Allow importing custom command-line arguments when importing packages from a bundle"
                WarningText="Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default" />

            <widgets:SecureCheckboxCard
                x:Name="AllowImportingPrePostInstallCommands"
                BorderThickness="1,0,1,1"
                CornerRadius="0,0,8,8"
                IsEnabled="{x:Bind AllowPrePostInstallCommands._checkbox.IsOn, Mode=OneWay}"
                SettingName="AllowImportPrePostOpCommands"
                Text="Allow importing custom pre-install and post-install commands when importing packages from a bundle"
                WarningText="Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle" />

        </StackPanel>
    </ScrollViewer>
</Page>
