{"\"{0}\" is a local package and can't be shared": "\"{0}\" është një paketë lokale dhe nuk mund të shpërndahet", "\"{0}\" is a local package and does not have available details": "\"{0}\" është një paketë lokale dhe nuk of<PERSON>hen detaje", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" është një paketë lokale dhe nuk është e përputhshme me këtë veçori", "(Last checked: {0})": "(<PERSON><PERSON><PERSON><PERSON><PERSON> për herë të fundit në: {0})", "(Number {0} in the queue)": "N<PERSON><PERSON> {0} në radhë", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@RDN000", "0 packages found": "Nuk u gjet asnjë paketë", "0 updates found": "Nuk u gjet asnjë përditësim", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 ditë", "1 hour": "1 orë", "1 month": "1 muaj", "1 package was found": "U gjet 1 paketë", "1 update is available": "Ofrohet 1 përditësim", "1 week": "1 javë", "1 year": "1 vit", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON><PERSON> në faqen \"{0}\" ose \"{1}\".", "2 - Warnings": "2 - Paralajmërime", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON> paket<PERSON>n që dëshiron të shtosh në koleksion, dhe të përzgjidhësh kutinë e zgjedhjes më të majtë.", "3 - Information (less)": "3 - Informacion (më pak)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON> pake<PERSON> që dëshiron të shtosh në koleksion janë të pë<PERSON><PERSON><PERSON><PERSON>, gjej dhe kliko rregullimin \"{0}\" në shiritin e veglave.", "4 - Information (more)": "4 - Informacion (më shumë)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Paketat e tua do të jenë shtuar në koleksion. Mund të vazhdosh të shtosh paketa ose të eksportosh koleksionin.", "5 - information (debug)": "5 - informac<PERSON> (korrigjim)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "<PERSON><PERSON><PERSON> men<PERSON> i njohur i librarisë C/C++. Plot me librari C/C++ dhe shërbime të tjera të lidhura me C/C++<br>Përmban: <b>librari C/C++ dhe shërbime të ngjashme</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "<PERSON><PERSON><PERSON> depo plot me vegla dhe programe ekzekutuese të krijuar duke pasur parasysh ekosistemin .NET të Microsoft-it.<br>Përmban: <b>vegla dhe skripte të lidhura me .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "<PERSON><PERSON><PERSON> depo plot me vegla të krijuar duke pasur parasysh e<PERSON>in .NET të Microsoft-it.<br>P<PERSON>rmban: <b>. vegla të lidhura me .NET</b>", "A restart is required": "Kërkohet një rinisje", "Abort install if pre-install command fails": "Anulo instalimin nëse komanda e para-instalimit d<PERSON><PERSON>ton", "Abort uninstall if pre-uninstall command fails": "<PERSON><PERSON> nëse komanda e para-çinstalimit dë<PERSON>ton", "Abort update if pre-update command fails": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> nëse komanda e para-përditësimit d<PERSON><PERSON>ton", "About": "<PERSON><PERSON><PERSON>", "About Qt6": "Rreth Qt6", "About WingetUI": "Rreth UniGetUI", "About WingetUI version {0}": "Rreth versionit {0} i UniGetUI", "About the dev": "<PERSON><PERSON><PERSON>", "Accept": "Prano", "Action when double-clicking packages, hide successful installations": "Veprimi kur klikon dy her<PERSON> paketat, f<PERSON>hen instalimet e suksesshme", "Add": "Shto", "Add a source to {0}": "<PERSON>hto një burim në {0}", "Add a timestamp to the backup file names": "Shto një vulë kohore në emrat e skedarëve rezervë", "Add a timestamp to the backup files": "Shto një vulë kohore në skedarët rezervë", "Add packages or open an existing bundle": "Shto paketa ose hap një koleksikon ekzistues", "Add packages or open an existing package bundle": "Shto paketa ose hap një koleksion paketash ekzistues", "Add packages to bundle": "Shto paketa në koleksion", "Add packages to start": "<PERSON>hto paketa për të filluar", "Add selection to bundle": "Shto përzgjedhjet në koleksion", "Add source": "<PERSON><PERSON><PERSON> burim", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Shto përditësimet që dështojnë me 'nuk u gjet përditësim i përshtatshëm' në listën e përditësimeve të shpërfillura.", "Adding source {source}": "<PERSON> shtuar burimin {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON> burimin {source} në {manager}", "Addition succeeded": "<PERSON><PERSON><PERSON>i pati sukses", "Administrator privileges": "Privilegjet e administratorit", "Administrator privileges preferences": "Parapëlqimet e privilegjëve të administratorit", "Administrator rights": "Të drejtat e administratorit", "Administrator rights and other dangerous settings": "Të drejtat e administratorit dhe cilësimet e tjera të rrezikshme", "Advanced options": "Rregullimet e përparuara", "All files": "<PERSON><PERSON> s<PERSON>", "All versions": "Çdo version", "Allow changing the paths for package manager executables": "Mundëso ndryshimin e shtigjeve për ekzekutuesit e menaxherëve të paketave", "Allow custom command-line arguments": "Lejo argumente të personalizuara të rreshtit të komandës", "Allow importing custom command-line arguments when importing packages from a bundle": "Lejo importimin e argumenteve të personalizuara të rreshtit të komandës kur importon paketa nga një koleksion", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Mundëso importimin e komandave të personalizuara të para-instalimit dhe pas-instalimit kur importohen paketat nga një koleksion", "Allow package operations to be performed in parallel": "Lejo që operacionet e paketës të kryhen paralelisht", "Allow parallel installs (NOT RECOMMENDED)": "Lejo instalime paralele (NUK KËSHILLOHET)", "Allow pre-release versions": "Lejo versionet e botimit paraprak", "Allow {pm} operations to be performed in parallel": "<PERSON>jo që operacionet {pm} të kryhen paralelisht", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "<PERSON><PERSON><PERSON><PERSON>, mund të instalosh {0} duke <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komandën që vijon në një dritare Windows PowerShell:", "Always elevate {pm} installations by default": "Instalo gjithmonë {pm} me privilegje të ngritura", "Always run {pm} operations with administrator rights": "Ekzekuto gjithmonë operacionet {pm} me të drejta administratori", "An error occurred": "Ndodhi një gabim", "An error occurred when adding the source: ": "Ndodhi një gabim gjatë shtimit të burimit:", "An error occurred when attempting to show the package with Id {0}": "Ndodhi një gabim gjatë përpjekjes për të shfaqur paketën me Id {0}", "An error occurred when checking for updates: ": "Ndodhi një gabim gjatë kontrollit për përditësime:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "Ndodhi një gabim gjatë ngarkimit të kopjes rezervë:", "An error occurred while logging in: ": "Ndodhi një gabim gjatë hyrjes:", "An error occurred while processing this package": "Ndodhi një gabim gjatë përpunimit të kësaj pakete", "An error occurred:": "Ndodhi një gabim:", "An interal error occurred. Please view the log for further details.": "Ndodhi një gabim i brendshëm. Të lutem shiko ditarin për detaje të mëtejshme.", "An unexpected error occurred:": "Ndodhi një gabim i papritur:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Ndodhi një gabim i papritur gjatë përpjekjes për të rregulluar WinGet. Të lutem provo përsëri më vonë", "An update was found!": "U gjet një përditësim!", "Android Subsystem": "Nënsistemi Android", "Another source": "<PERSON><PERSON><PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Çdo shkurtore e re e krijuar gjatë një instalimi ose përditësimi do të fshihet automatikisht, në vend se të shfaqet një dritare konfirmimi herën e parë kur zbulohet.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Çdo shkurtore e krijuar ose modifikuar jashtë UniGetUI do të shpërfillet. Do mund t'i shtosh ato përmes butonit {0}", "Any unsaved changes will be lost": "Çdo ndryshim i paruajtur do të humbet", "App Name": "Emri i aplikacionit", "Appearance": "<PERSON><PERSON>", "Application theme, startup page, package icons, clear successful installs automatically": "Motivi i aplikacionit, faqja e nisjes, ikonat e paketave, pastrim automatik i instalimeve të suksesshme", "Application theme:": "Motivi i aplikacionit:", "Apply": "Zbato", "Architecture to install:": "Arkitektura për të instaluar:", "Are these screenshots wron or blurry?": "A janë këto pamje të ekranit të gabuara apo të paqarta?", "Are you really sure you want to enable this feature?": "A je i sigurt që do të aktivizosh këtë veçori?", "Are you sure you want to create a new package bundle? ": "Je i sigurt që do të krijosh një koleksion paketash të ri?", "Are you sure you want to delete all shortcuts?": "A je i sigurt që do të fshish të gjitha shkurtoret?", "Are you sure?": "A je i sigurt?", "Ascendant": "<PERSON><PERSON><PERSON>", "Ask for administrator privileges once for each batch of operations": "Kërko privilegje administratori një herë për secilin grup operacionesh", "Ask for administrator rights when required": "Kërko të drejtat e administratorit kur nevojiten", "Ask once or always for administrator rights, elevate installations by default": "Kërko vetëm një herë ose gjithmonë për të drejtat e administratorit, instalo me privilegje të ngritura si paracaktim", "Ask only once for administrator privileges": "Pyet vetëm një herë për të drejtat e administratorit", "Ask only once for administrator privileges (not recommended)": "Pyet vetëm një herë për privilegjet e administratorit (nuk këshillohet)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Kërko të fshihen shkurtoret e tryezës të krijuara gjatë një instalimi ose përditësimi.", "Attention required": "Kërkohet vëmendje", "Authenticate to the proxy with an user and a password": "Hyr në ndërmjetësin me emrin e përdoruesit dhe fjalëkalimin", "Author": "Autor", "Automatic desktop shortcut remover": "Heqës automatik i shkurtoreve të tryezës", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Ruaj automatikisht një listë të të gjitha paketave të tua të instaluara për t'i rikthyer ato lehtësisht.", "Automatically save a list of your installed packages on your computer.": "Ruaj automatikisht një listë të paketave të tua të instaluara në kompjuterin tënd.", "Autostart WingetUI in the notifications area": "Nis automatikisht UniGetUI në hapësirën e njoftimeve", "Available Updates": "Përditësimet e ofruara", "Available updates: {0}": "Përditësimet e ofruara: {0}", "Available updates: {0}, not finished yet...": "Përditësimet e ofruara: {0}, nuk ka përfunduar ende...", "Backing up packages to GitHub Gist...": "Po bëhet kopje rezervë e paketave në GitHub Gist…", "Backup": "Bëj një kopje rezervë", "Backup Failed": "Krijimi i kopjes rezervë dështoi", "Backup Successful": "Kopja rezervë u krijua me sukses.", "Backup and Restore": "Kopje rezervë dhe Rikthim", "Backup installed packages": "Kopje rezervë e paketave të instaluara", "Backup location": "Vendndodhja e kopjes rezervë", "Become a contributor": "<PERSON><PERSON><PERSON>", "Become a translator": "<PERSON><PERSON><PERSON>", "Begin the process to select a cloud backup and review which packages to restore": "Fillo procesin për të zgjedhur një kopje rezervë në re dhe rishiko paketat që do të rikthehen.", "Beta features and other options that shouldn't be touched": "Veçoritë për testim dhe rregullime të tjera që nuk duhen prekur", "Both": "Të dyja", "Bundle security report": "Raporti i sigurisë për koleksionin", "But here are other things you can do to learn about WingetUI even more:": "Por këtu ka gjëra të tjera që mund të bësh për të mësuar më shumë rreth UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Duke <PERSON><PERSON> një menaxher paketash, nuk do të jesh më në gjendje të shohësh ose përditësosh paketat e tij.", "Cache administrator rights and elevate installers by default": "Vendos në kesh të drejtat e administratorit edhe instalo me privilegje të ngritura gjithmonë", "Cache administrator rights, but elevate installers only when required": "Vendos në kesh të drejtat e administratorit, por instalo me privilegje të ngritura vetëm kur nevojitet", "Cache was reset successfully!": "Keshi u rivendos me sukses!", "Can't {0} {1}": "Nuk {0} dot {1}", "Cancel": "<PERSON><PERSON>", "Cancel all operations": "Anulo të gjitha operacionet", "Change backup output directory": "Ndrysho vendndodhjen e kopjës rezervë", "Change default options": "Ndrysho rregullimet e paracaktuara", "Change how UniGetUI checks and installs available updates for your packages": "Ndrysho mënyrën se si UniGetUI kontrollon dhe instalon përditësimet e ofruara për paketat e tua", "Change how UniGetUI handles install, update and uninstall operations.": "Ndrysho mënyrën se si UniGetUI trajton operacionet e instalimit, përditësimit dhe çinstalimit.", "Change how UniGetUI installs packages, and checks and installs available updates": "Ndrysho mënyrën se si UniGetUI instalon paketat dhe si kontrollon edhe instalon përditësimet e ofruara.", "Change how operations request administrator rights": "Ndrysho mënyrën se si operacionet kërkojnë të drejtat e administratorit", "Change install location": "<PERSON><PERSON>ys<PERSON> vendndodhjen e instalimit", "Change this": "Ndryshoje", "Change this and unlock": "Ndryshoje dhe zhblloko", "Check for package updates periodically": "Kontrollo periodikisht për përditësime të paketave", "Check for updates": "Kontrollo për p<PERSON>rdi<PERSON>ësime", "Check for updates every:": "Kontrollo për përditësime <PERSON>:", "Check for updates periodically": "Kontrollo periodikisht për përditësime.", "Check for updates regularly, and ask me what to do when updates are found.": "Kontrollo rregullisht për përditësime dhe më pyet se çfarë të bëj kur të gjenden përditësimet.", "Check for updates regularly, and automatically install available ones.": "Kontrollo rregullisht për përditësime dhe instalo automatikisht ato që ofrohen.", "Check out my {0} and my {1}!": "<PERSON><PERSON> {0} dhe {1} të mi!", "Check out some WingetUI overviews": "Shiko disa përmbledhje të UniGetUI", "Checking for other running instances...": "Po kontrollohet për raste të tjera në ekzekutim...", "Checking for updates...": "Po kontrollohet për përditësime...", "Checking found instace(s)...": "Po kontrollohen rastet e gjetura...", "Choose how many operations shouls be performed in parallel": "Zgjidh sa operacione duhet të kryhen paralelisht", "Clear cache": "<PERSON><PERSON> kesh-in", "Clear finished operations": "Pastro opracionet e përfunduara", "Clear selection": "Pastro p<PERSON>", "Clear successful operations": "Pastro operacionet e suksesshme", "Clear successful operations from the operation list after a 5 second delay": "Pastro operacionet e suksesshme nga lista e operacioneve pas një vonese prej 5 sekondash", "Clear the local icon cache": "Pastro kesh-in lokal të ikonave", "Clearing Scoop cache - WingetUI": "Po pastrohet keshi i Scoop - UniGetUI", "Clearing Scoop cache...": "Po pastrohet keshi i Scoop...", "Click here for more details": "Kliko këtu për më shumë detaje", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Kliko mbi Instalo për të filluar procesin e instalimit. Nëse e anashkalon instalimin, UniGetUI mund të mos punon siç pritet.", "Close": "<PERSON><PERSON>ll", "Close UniGetUI to the system tray": "Mbyll UniGetUI në hapësirën e sistemit", "Close WingetUI to the notification area": "Mbyll UniGetUI në hapësirën e njoftimeve", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Kopja rezervë në re përdor një Gist privat në GitHub për të ruajtur një listë të paketave të instaluara.", "Cloud package backup": "Kopja reyervë e paketave në re", "Command-line Output": "Dalja e rreshtit të komandës", "Command-line to run:": "Rreshti i komandës për t'u ekzekutuar:", "Compare query against": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>t<PERSON>n me", "Compatible with authentication": "I përputhshëm me kyçjen", "Compatible with proxy": "I përputhshëm me ndërmjet<PERSON>s", "Component Information": "Informacion mbi Përbë<PERSON>sin", "Concurrency and execution": "Njëkohshmëria dhe ekzekutimi", "Connect the internet using a custom proxy": "<PERSON><PERSON>u me internetin duke p<PERSON><PERSON><PERSON><PERSON> një ndërmjetës të personalizuar", "Continue": "Vazhdo", "Contribute to the icon and screenshot repository": "Kontribuo në depon e ikonave dhe pamjeve të ekranit", "Contributors": "Kontribuesit", "Copy": "<PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopjo në letërmbajtëse", "Could not add source": "<PERSON>uk mund të shtohet burimi", "Could not add source {source} to {manager}": "<PERSON>uk mund të shtohej buri<PERSON> {source} në {manager}", "Could not back up packages to GitHub Gist: ": "Nuk mund të bëhej kopje rezervë e paketave në GitHub Gist:", "Could not create bundle": "Nuk mund të krijohej koleksioni i paketave", "Could not load announcements - ": "Njoftimet nuk u ngarkuan dot -", "Could not load announcements - HTTP status code is $CODE": "Njoftimet nuk u ngarkuan dot - Kodi i gjendjes HTTP është $CODE", "Could not remove source": "<PERSON>uk mund të hiqet burimi", "Could not remove source {source} from {manager}": "<PERSON><PERSON><PERSON> {source} nuk mund të hiqej nga {manager}", "Could not remove {source} from {manager}": "<PERSON>uk mund të hiqej {source} nga {manager}", "Create .ps1 script": null, "Credentials": "Kredencialet", "Current Version": "Versioni i tanishëm", "Current status: Not logged in": "Gjendja e tanishme: <PERSON>uk ke hyr", "Current user": "Përdoruesi i tanishëm", "Custom arguments:": "Argumentet e personalizuara:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Argumentet e personalizuara të rreshtit të komandës mund të ndryshojnë mënyrën se si programet instalohen, përditësohen ose çinstalohen, në një mënyrë që UniGetUI nuk mund ta kontrollojë. Përdorimi i rreshtave të personalizuar të komandës mund t’i prishë paketat. Vazhdo me kujdes.", "Custom command-line arguments:": "Argumentet e personalizuara të rreshtit të komandës:", "Custom install arguments:": "Argumente të instalimit të personalizuara:", "Custom uninstall arguments:": "Argumente të çinstalimit të personalizuara:", "Custom update arguments:": "Argumente të përditësimit të personalizuara:", "Customize WingetUI - for hackers and advanced users only": "Personalizo UniGetUI - vetëm për hakerat dhe përdoruesit e përparuar", "DEBUG BUILD": "KONSTRUKT PËR KORRIGJIM", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "MOSPRANIM PËRGJEGJËSIE: NE NUK JEMI PËRGJEGJËS PËR PAKETAT E SHKARKUARA. TË LUTEM SIGUROHU TË INSTALOSH VETËM PROGRAME TË BESUESHËME.", "Dark": "<PERSON><PERSON><PERSON><PERSON>", "Decline": "Refuzo", "Default": "Paracaktuar", "Default installation options for {0} packages": "Rregullimet e paracaktuara të instalimit për {0} paketa.", "Default preferences - suitable for regular users": "Parapëlqimet e paracaktuara - të përshtatshme për përdoruesit e thjeshtë", "Default vcpkg triplet": "Treshja vcpkg e paracaktuar", "Delete?": "Do të fshish?", "Dependencies:": "Varësitë:", "Descendant": "Ren<PERSON><PERSON>", "Description:": "Përshkrim:", "Desktop shortcut created": "U krijua shkurtoja e tryezës", "Details of the report:": "Detajet e raportit:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Zhvillimi është i vështirë dhe ky aplikacion është falas. Por nëse të pëlqeu aplikacioni, gjithm<PERSON><PERSON> mund të <b>më blesh një kafe</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Instalo drejtpërdrejt kur klikon dy herë një artikull në skedën \"{discoveryTab}\" (në vend që të shfaqësh informacionin e paketës)", "Disable new share API (port 7058)": "Çaktivizo API-në e re të shpërndarjes (porta 7058)", "Disable the 1-minute timeout for package-related operations": "Çaktivizo afatin 1 minutësh për operacionet që lidhen me paketat", "Disclaimer": "Mospranim përgjegjë<PERSON>", "Discover Packages": "Zbulo paketat", "Discover packages": "<PERSON><PERSON><PERSON> pake<PERSON>", "Distinguish between\nuppercase and lowercase": "<PERSON><PERSON> midis shkronjave \ntë mëdhaja dhe të vogla", "Distinguish between uppercase and lowercase": "<PERSON><PERSON> midis shkronjave të mëdhaja dhe të vogla", "Do NOT check for updates": "MOS kontrollo për përditësime", "Do an interactive install for the selected packages": "Bëj një instalim ndërveprues për paketat e përzgjedhura", "Do an interactive uninstall for the selected packages": "Bëj një çinstalim ndërveprues për paketat e përzgjedhura", "Do an interactive update for the selected packages": "Bëj një përditësim ndërveprues për paketat e përzgjedhura", "Do not automatically install updates when the battery saver is on": "Mos i instalo automatikisht përditësimet kur kursyesi i baterisë është aktiv", "Do not automatically install updates when the network connection is metered": "Mos i instalo automatikisht përditësimet kur lidhja e rrjetit është me kufizime të dozës", "Do not download new app translations from GitHub automatically": "Mos shkarko automatikisht përkthimet e reja të aplikacionit nga GitHub", "Do not ignore updates for this package anymore": "<PERSON><PERSON> s<PERSON> përditësimet për këtë paketë", "Do not remove successful operations from the list automatically": "Mos i hiq automatikisht operacionet e suksesshme nga lista", "Do not show this dialog again for {0}": "<PERSON><PERSON> shfaq më këtë dialog për {0}", "Do not update package indexes on launch": "Mos p<PERSON>rditëso treguesit e paketave në nisje", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "A pranon që UniGetUI të mbledhë dhe të dërgojë statistika të përdorimit anonime, me qëllim të vetëm kuptimin dhe përmirësimin të përvojës së përdoruesit?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "A të duket i dobishëm UniGetUI? Nëse e ke mundësinë, mbështet punën time, kështu që unë të vazhdoj ta bëj UniGetUI ndërfaqen përfundimtare të menaxhimit të paketave.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "A të duket i dobishëm UniGetUI? Dëshiron të mbështesësh zhvilluesin? Nëse po, mund të {0}, ndi<PERSON>on shumë!", "Do you really want to reset this list? This action cannot be reverted.": "A je i sigurt që do të rivendosësh këtë listë? Ky veprim nuk mund të rikthehet.", "Do you really want to uninstall the following {0} packages?": "Dëshiron vërtet të çinstalosh {0, plural, one {paketën} other {{0} paketat}} në vijim?", "Do you really want to uninstall {0} packages?": "Dëshiron vërtet të çinstalosh {0, plural, one {paketën} other {{0} paketa}}?", "Do you really want to uninstall {0}?": "Dëshiron vërtet të ç<PERSON>talosh {0}?", "Do you want to restart your computer now?": "Dëshiron të rinisësh kompjuterin tënd tani?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Dëshiron të përkthesh UniGetUI në gjuhën tënde? Shiko se si të kontribuosh <a style=\"color:{0}\" href=\"{1}\"a>KËTU!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Nuk do të dhurosh? Mos u shqetëso, mund të shpërndash UniGetUI me miqtë e tu. Shpërndaj fjalën për UniGetUI.", "Donate": "<PERSON><PERSON><PERSON>", "Done!": "U krye!", "Download failed": "<PERSON>h<PERSON><PERSON><PERSON>", "Download installer": "<PERSON><PERSON><PERSON><PERSON> instal<PERSON>", "Download operations are not affected by this setting": "Operacionet e shkarkimit nuk preken nga ky cilësim", "Download selected installers": "Shkarko instaluesit e përzgjedhur", "Download succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> pati sukses", "Download updated language files from GitHub automatically": "Shkarko automatikisht skedarët ë gjuhës te përditësuar nga GitHub", "Downloading": "Po shkarkohet", "Downloading backup...": "Duke s<PERSON><PERSON><PERSON><PERSON> kopjen rezervë...", "Downloading installer for {package}": "Po shkarkohet instaluesi për {package}", "Downloading package metadata...": "Po shkarkohen meta të dhënat të paketës...", "Enable Scoop cleanup on launch": "Aktivizo pastrimin e Scoop në nisje", "Enable WingetUI notifications": "Aktivizo njoftimet e UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Aktivizo zgjidhësin e problemeve të përmirësuar [eksperimental] të WinGet", "Enable and disable package managers, change default install options, etc.": "Aktivizo dhe çaktivizo menaxherët e paketave, ndrysho rregullimet e paracaktuara të instalimit, etj.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Aktivizo përmirësimet e përdorimit të CPU-së në sfond (s<PERSON>h Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Aktivizo API-në e sfondit (Widgets for UniGetUI and Sharing, porta 7058)", "Enable it to install packages from {pm}.": "Aktivizoje për të instaluar paketa nga {pm}.", "Enable the automatic WinGet troubleshooter": "Akitvizo zgjidhësin automatik të problemeve të WinGet-it", "Enable the new UniGetUI-Branded UAC Elevator": "Aktivizo ngritësin e ri të privilegjëve të administratorit të UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Aktivizo trajtuesin e ri të hyrjes së procesit (mbyllës automatik i StdIn)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Aktivizo cilësimet më poshtë VETËM NË QOFTË SE kupton plotësisht se çfarë bëjnë ato, si dhe implikimet dhe rreziqet që mund të sjellin.", "Enable {pm}": "Aktivizo {pm}", "Enter proxy URL here": "Shkruaj këtu URL-në e ndërmjetësit", "Entries that show in RED will be IMPORTED.": "Shënimet që shfaqen me të KUQE do të IMPORTOHEN.", "Entries that show in YELLOW will be IGNORED.": "Shënimet që shfaqen me të VERDHË do të ANASHKALOHEN.", "Error": "<PERSON><PERSON><PERSON>", "Everything is up to date": "Gjithçka është e përditësuar", "Exact match": "Përputhje e saktë", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Shkurtoret ekzistuese në tryezën tënde do të skanohen, dhe do të duhet të zgjedhësh se cilat do mbash dhe cilat do fshish.", "Expand version": "Shfaq versionin", "Experimental settings and developer options": "Cilësimet eksperimentale dhe rregullimet për zhvilluesit", "Export": "Eksporto", "Export log as a file": "Eksporto ditarin si skedar", "Export packages": "Eksporto paketat", "Export selected packages to a file": "Eksporto paketat e përzgjedhura në një skedar", "Export settings to a local file": "Eksporto cilësimet në një skedar lokal", "Export to a file": "Eksporto në një skedar", "Failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fetching available backups...": "Po merren kopjet rezervë…", "Fetching latest announcements, please wait...": "Po merren njoftimet më të fundit, të lutem prit...", "Filters": "Filtrat", "Finish": "Mbar<PERSON>", "Follow system color scheme": "Ndiq skemën e ngjyrave të sistemit", "Follow the default options when installing, upgrading or uninstalling this package": "Ndjek rregullimet e paracaktuara gjatë instalimit, përditësimit ose çinstalimit të kësaj pakete.", "For security reasons, changing the executable file is disabled by default": "Për ars<PERSON> sigu<PERSON>, ndryshimi i skedarit të ekzekutimit është i çaktivizuar si paracaktim", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "<PERSON><PERSON><PERSON> a<PERSON><PERSON> sigu<PERSON>, argumentet e personalizuara të rreshtit të komandës janë të çaktivizuara si paracaktim. Shko te cilësimet e sigurisë të UniGetUI për ta ndryshuar këtë.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "<PERSON><PERSON><PERSON> a<PERSON> sigu<PERSON>, skriptet para dhe pas operacionit janë të çaktivizuara si paracaktim. Shko te cilësimet e sigurisë të UniGetUI për ta ndryshuar këtë.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Detyro përdorimin e versionit të winget të përpiluar për ARM (VETËM PËR SISTEMET ARM64)", "Formerly known as WingetUI": "I njohur më parë si <PERSON>", "Found": "<PERSON><PERSON><PERSON>", "Found packages: ": "Paketat e gjetura:", "Found packages: {0}": "Paketat e gjetura: {0}", "Found packages: {0}, not finished yet...": "Paketat e gjetura: {0}, nuk ka përfunduar ende...", "General preferences": "Parapëlqime të përgjit<PERSON>hme", "GitHub profile": "<PERSON><PERSON>", "Global": "Globale", "Go to UniGetUI security settings": "Shko te cilësimet e sigurisë të UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Depo e shkëlqyeshme me shërbime të panjohura por të dobishme dhe paketa të tjera interesante.<br>Përmban: <b><PERSON><PERSON><PERSON><PERSON><PERSON>, Programe të rreshtit së komandës, Programe të përgjithshme (kërkohet kova e shtesave)</b>", "Great! You are on the latest version.": "Shkëlqyeshëm! Je në versionin më të fundit.", "Grid": "Rrjetë", "Help": "Ndihmë", "Help and documentation": "Ndihmë dhe dokumentacion", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Këtu mund të ndryshosh sjelljen e UniGetUI në lidhje me shkurtoret që vijojnë. Duke p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> një shkurtore, UniGetUI do ta fshijë atë nëse krijohet gjatë një përditësimi të ardhshëm. Nëse e heq përzg<PERSON>jen, shkurtesa do të mbetet e pandryshuar", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t<PERSON>, emri im është Martí dhe jam <i>zh<PERSON>luesi</i> i UniGetUI. UniGetUI është bërë tërësisht në kohën time të lirë!", "Hide details": "Fshih detajet", "Homepage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hooray! No updates were found.": "Ec aty! Nuk u gjetën përditësime.", "How should installations that require administrator privileges be treated?": "Si duhet të trajtohen instalimet që kërkojnë privilegje administratori?", "How to add packages to a bundle": "Si të shtosh paketa në një koleksion", "I understand": "<PERSON><PERSON><PERSON><PERSON>", "Icons": "<PERSON><PERSON><PERSON>", "Id": "ID-ja", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Nëse ke aktivizuar kopjen rezervë në re, ajo do të ruhet si një GitHub Gist në këtë llogari", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Shpërfill komandat e personalizuara para dhe pas instalimit kur importon paketa nga një koleksion", "Ignore future updates for this package": "Shpërfill përditësimet e ardhshme për këtë paketë", "Ignore packages from {pm} when showing a notification about updates": "Shpërfill paketat nga {pm} kur shfaqet një njoftim për përditësime", "Ignore selected packages": "Shpërfill paketat e përzgjedhura", "Ignore special characters": "Shpërfill shkronjat e veçanta", "Ignore updates for the selected packages": "Shpërfill përditësimet për paketën e përzgjedhur", "Ignore updates for this package": "Shpërfill përditësimet për këtë paketë", "Ignored updates": "Përditësimet e shpërfillura", "Ignored version": "Versionet e shpërfillura", "Import": "Importo", "Import packages": "I<PERSON>rto pake<PERSON>", "Import packages from a file": "Importo paketat nga një skedar", "Import settings from a local file": "Importo cilësimet nga një skedar lokal", "In order to add packages to a bundle, you will need to: ": "<PERSON><PERSON>r të shtuar paketa në një koleksion, do të duhet të:", "Initializing WingetUI...": "Po niset UniGetUI...", "Install": "Instalo", "Install Scoop": "<PERSON><PERSON><PERSON>", "Install and more": "Instalimi dhe të tjera", "Install and update preferences": "Parapëlqimet e instalimit dhe përditësimit", "Install as administrator": "Instalo si administrator", "Install available updates automatically": "Instalo automatikisht përditësimet e ofruara", "Install location can't be changed for {0} packages": "Vendndodhja e instalimit nuk mund të ndryshohet për {0} paketa", "Install location:": "Vendndodhja e instalimit:", "Install options": "Rregullimet e instalimit", "Install packages from a file": "Instalo paketat nga një skedar", "Install prerelease versions of UniGetUI": "Instalo versionet paraprake të UniGetUI", "Install script": null, "Install selected packages": "Instalo paketat e përzgjedhura", "Install selected packages with administrator privileges": "Instalo paketat e përzgjedhura me privilegje administratori", "Install selection": "Instalo përzgjedhjet", "Install the latest prerelease version": "Instalo versionin e botimit paraprak më të fundit", "Install updates automatically": "Instalo përditësimet automatikisht", "Install {0}": "Instalo {0}", "Installation canceled by the user!": "Instalimi u anulua nga përdoruesi!", "Installation failed": "<PERSON><PERSON><PERSON><PERSON>", "Installation options": "Rregullimet e instalimit", "Installation scope:": "Shtrirja e instalimit:", "Installation succeeded": "In<PERSON><PERSON>i pati sukses", "Installed Packages": "Paketat e instaluara", "Installed Version": "Versioni i instaluar", "Installed packages": "Paketat e instaluara", "Installer SHA256": "SHA256 i instaluesit", "Installer SHA512": "SHA512 i instaluesit", "Installer Type": "Lloji i instaluesit", "Installer URL": "URL i instaluesit", "Installer not available": "Instaluesi nuk ofrohet", "Instance {0} responded, quitting...": "<PERSON><PERSON><PERSON> {0} u p<PERSON><PERSON><PERSON>g<PERSON>, po ndalohet...", "Instant search": "Kërkim i menjëhershëm", "Integrity checks can be disabled from the Experimental Settings": "Kontrollet e integritet mund të çaktivizohen nga Cilësimet Eksperimentale", "Integrity checks skipped": "Kontrollet e integritetit u anashkaluan", "Integrity checks will not be performed during this operation": "Kontrollet e integritetit nuk do të kryhen gjatë këtij operacioni", "Interactive installation": "Instalim ndërveprues", "Interactive operation": "Operacion ndërveprues", "Interactive uninstall": "Çinstalim ndërveprues", "Interactive update": "Përditësim ndërveprues", "Internet connection settings": "Cilësimet e lidhjes së Internetit", "Is this package missing the icon?": "A i mungon ikona kësaj pakete?", "Is your language missing or incomplete?": "Gjuha jote mungon apo është e paplotë?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Nuk është e garantuar që kredencialet do të ruhen në mënyrë të sigurt, prandaj mund të shmangësh përdorimin e kredencialeve të llogarisë tënde bankare", "It is recommended to restart UniGetUI after WinGet has been repaired": "Këshillohet të riniset UniGetUI mbasi që rregullohet WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Rekomandohet me ngulm të instalosh përsëri UniGetUI për të zgjidhur situatën.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Duket sikur WinGet nuk po punon siç duhet. Dëshiron të përpiqesh të rregullosh WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Duket sikur ke nisur UniGetUI si administrator, gjë që nuk këshillohet. Ti sidoqoftë mund ta përdorsh programin, por këshillohet të mos ekzekutohet UniGetUI me privilegje administratori. Kliko në \"{showDetails}\" për të parë pse.", "Language": "<PERSON><PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON>, motivi dhe parapëlqime të tjera të ndryshme", "Last updated:": "Përditësimi i fundit:", "Latest": "I fundit", "Latest Version": "Versioni i fundit", "Latest Version:": "Versioni i fundit:", "Latest details...": "Detajet e fundit...", "Launching subprocess...": "Duke ni<PERSON><PERSON> nënprocesin...", "Leave empty for default": "<PERSON><PERSON><PERSON> bosh për të ndjekur paracaktimin", "License": "<PERSON><PERSON>", "Licenses": "Le<PERSON>", "Light": "Çelët", "List": "Listë", "Live command-line output": "Dalje e rreshtit të komandës e drejtpërdrejtë ", "Live output": "Dalja e drejtpërdrejtë", "Loading UI components...": "Po ngarkohen përbërësit e ndërfaqes së përdoruesit...", "Loading WingetUI...": "Po ngarkohet UniGetUI...", "Loading packages": "<PERSON> ngark<PERSON>en paketat", "Loading packages, please wait...": "<PERSON> ng<PERSON>en pake<PERSON>, të lutem prit...", "Loading...": "Po ngarkohet...", "Local": "<PERSON><PERSON>", "Local PC": "Kompjuteri lokal", "Local backup advanced options": "Rregullimet e përparuara për kopjen rezervë lokale", "Local machine": "Makina lokale", "Local package backup": "Kopje rezervë lokale e paketës", "Locating {pm}...": "<PERSON> gjendet {pm}...", "Log in": "Hyr", "Log in failed: ": "<PERSON><PERSON><PERSON>:", "Log in to enable cloud backup": "Hyr për të aktivizuar kopjen rezervë në re", "Log in with GitHub": "<PERSON>yr me <PERSON>", "Log in with GitHub to enable cloud package backup.": "Hyr me GitHub për të aktivizuar kopjen rezervë në re të paketave.", "Log level:": "Niveli i ditarit:", "Log out": "Dil", "Log out failed: ": "<PERSON><PERSON>:", "Log out from GitHub": "Dil nga GitHub", "Looking for packages...": "Po gjenden paketat...", "Machine | Global": "Makinë | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Argumentet e pasakta të rreshtit të komandës mund të prishin paketat, ose madje t’i lejojnë një aktori keqdashës të marrë ekzekutimin me privilegje. <PERSON>randaj, importimi i argumenteve të personalizuara të rreshtit të komandës është i çaktivizuar si paracaktim.", "Manage": "<PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "Menaxho cilësimet e UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Menaxho sjelljen e nisjes automatike të UniGetUI nga aplikacioni Cilësimet", "Manage ignored packages": "Menaxho paketat e shpërfillura", "Manage ignored updates": "Menaxho përditësimet e shpërfillura", "Manage shortcuts": "<PERSON><PERSON><PERSON>", "Manage telemetry settings": "Menaxho cilësimet e matjes nga larg (telemetrisë)", "Manage {0} sources": "<PERSON><PERSON><PERSON> burimet {0}", "Manifest": "Manifest", "Manifests": "Manifestet", "Manual scan": "Skanim manual", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Menaxheri zyrtar i paketave të Microsoft. Plot me paketa të njohura dhe të verifikuara<br>Përmban: <b>Programe të përgjithshme, aplikacione të Microsoft Store</b>", "Missing dependency": "Mungon <PERSON>", "More": "<PERSON><PERSON> s<PERSON>", "More details": "<PERSON><PERSON> shumë detaje", "More details about the shared data and how it will be processed": "<PERSON>ë shumë detaje rreth të dhënave të ndara dhe mënyrës se si do të përpunohen", "More info": "Më shumë informacione", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "SHËNIM: <PERSON><PERSON> z<PERSON>jidh<PERSON> i problemeve mund të çaktivizohet nga Cilësimet UniGetUI, në seksionin WinGet", "Name": "<PERSON><PERSON>", "New": "I ri", "New Version": "Version i Ri", "New bundle": "Koleksion i ri", "New version": "Verisoni i ri", "Nice! Backups will be uploaded to a private gist on your account": "Bukur! Kopjet rezervë do të ngarkohen në një Gist privat në llogarinë tënde", "No": "<PERSON>", "No applicable installer was found for the package {0}": "Nuk u gjet asnjë instalues i përshtatshëm për paketën {0}", "No dependencies specified": "Nuk janë përcaktuar <PERSON>", "No new shortcuts were found during the scan.": "Nuk u gjetën shkurtore të reja gjatë skanimit.", "No packages found": "Nuk u gjet asnjë paketë", "No packages found matching the input criteria": "Nuk u gjet asnjë paketë që përputhet me kriteret hyrëse", "No packages have been added yet": "Ende nuk është shtuar asnjë paketë", "No packages selected": "Nuk është përzgjedhur asnjë paketë", "No packages were found": "Nuk u gjet asnjë paketë", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Asnjë informacion personal nuk mblid<PERSON>t as nuk dë<PERSON><PERSON><PERSON>, dhe të dhënat e mbledhura janë të anonimizuara, kë<PERSON>tu që nuk mund të gjurmohen mbrapsht te ty.", "No results were found matching the input criteria": "Nuk u gjet asnjë rezultat që përputhet me kriteret hyrëse", "No sources found": "Nuk u gjet asnjë burim", "No sources were found": "Nuk u gjet asnjë burim", "No updates are available": "Nuk ka përditësime të ofruara", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Menaxheri i paketave të Node JS-it. Plot me librari dhe shërbime të tjera që kanë të bëjnë me botën e javascript-it<br>Përmban: <b>Libraritë Node javascript dhe shërbime të tjera të lidhura me to</b>", "Not available": "<PERSON><PERSON>", "Not finding the file you are looking for? Make sure it has been added to path.": "Nuk po gjen skedarin që po kërkon? Sigurohu që është shtuar në shteg.", "Not found": "Nuk u gjet", "Not right now": "<PERSON>i", "Notes:": "Shënime:", "Notification preferences": "Parapëlqimet e njoftimit", "Notification tray options": "Rregullimet e hapësirës së njoftimeve", "Notification types": "Llojet e njoftimeve", "NuPkg (zipped manifest)": "NuPkg (zipped manifest)", "OK": "OK", "Ok": "Ok", "Open": "<PERSON>p", "Open GitHub": "<PERSON><PERSON>", "Open UniGetUI": "Hap UniGetUI", "Open UniGetUI security settings": "Hap cilësimet e sigurisë të UniGetUI", "Open WingetUI": "Hap UniGetUI", "Open backup location": "Hap vendndodhjen e kopjes rezervë", "Open existing bundle": "Hap koleksion ekzistues", "Open install location": "Hap vendndodhjen e instalimit", "Open the welcome wizard": "Hap drejtuesin e mirëseardhjes", "Operation canceled by user": "Operacioni u anulua nga përdoruesi", "Operation cancelled": "Operacioni u anulua", "Operation history": "Historiku i operacionëve", "Operation in progress": "Operacion në vazhdim", "Operation on queue (position {0})...": "Operacion në radhë (vendi {0})...", "Operation profile:": "Profili i operacionit:", "Options saved": "Rregullimet u ruajtën", "Order by:": "Rëndit sipas:", "Other": "Tjerë", "Other settings": "Cilësimet e tjera", "Package": "Paketë", "Package Bundles": "Koleksione të paketave", "Package ID": "ID i paketës", "Package Manager": "Menaxheri i paketave", "Package Manager logs": "Ditari i menaxherit të paketave", "Package Managers": "Menaxherët e paketave", "Package Name": "<PERSON><PERSON> <PERSON> paketës", "Package backup": "Kopje rezervë e paketës", "Package backup settings": "Cilësimet e kopjes rezervë të paketës", "Package bundle": "Koleksion i paketave", "Package details": "Detajet e paketës", "Package lists": "Listat e paketave", "Package management made easy": "Menaxhimi i paketave i lehtësuar", "Package manager": "Menaxheri i paketave", "Package manager preferences": "Parapëlqimet e menaxherit të paketave", "Package managers": "Menaxherët e paketave", "Package not found": "Paketa nuk u gjet", "Package operation preferences": "Parapëlqimet e operacioneve të paketave", "Package update preferences": "Parapëlqimet e përditësimit të paketave", "Package {name} from {manager}": "<PERSON><PERSON> {name} nga {manager}", "Package's default": "Paracaktimet e paketës", "Packages": "Paketat", "Packages found: {0}": "Paketat e gjetura: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Password": "Fjalëkalim", "Paste a valid URL to the database": "Ngjit një URL të vlefshme për bazën e të dhënave", "Pause updates for": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>si<PERSON> për", "Perform a backup now": "Bëj një kopje rezervë tani", "Perform a cloud backup now": "Bëj një kopje rezervë në re tani", "Perform a local backup now": "Bëj një kopje rezervë lokale tani", "Perform integrity checks at startup": "Bëj kontrollet e integritet gjatë nisjes", "Performing backup, please wait...": "<PERSON> kryhet rezervimi, të lutem prit...", "Periodically perform a backup of the installed packages": "Bëj periodikisht një kopje rezervë të paketave të instaluara", "Periodically perform a cloud backup of the installed packages": "Bëj periodikisht një kopje rezervë në re të paketave të instaluara", "Periodically perform a local backup of the installed packages": "Bëj periodikisht një kopje rezervë lokale të paketave të instaluara", "Please check the installation options for this package and try again": "Kontrollo rregullimet e instalimit për këtë paketë dhe provo përsëri.", "Please click on \"Continue\" to continue": "Të lutem kliko \"Vazhdo\" për të vazhduar", "Please enter at least 3 characters": "Të lutem shkruaj të paktën 3 shkronja", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Të lutem vër re se disa paketa mund të mos instalohen, për shkak të menaxherëve të paketave që janë aktivizuar në këtë makinë.", "Please note that not all package managers may fully support this feature": "Të lutem vë re që jo të gjithë menaxherët e paketave mund ta mbështesin plotësisht këtë veçori", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Të lutem vër re se paketat nga ca burime mund të mos jenë të eksportueshme. Janë të hijëzuara dhe nuk do të eksportohen.", "Please run UniGetUI as a regular user and try again.": "Të lutem hap UniGetUI si përdorues i zakonshëm dhe provo përsëri.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Të lutem shiko daljen e rreshtit të komandës ose referoju Historikut të Operacioneve për informacione të mëtejshme rreth problemit.", "Please select how you want to configure WingetUI": "Të lutem zgjidh se si dëshiron të konfigurosh UniGetUI", "Please try again later": "Të lutem provo përs<PERSON>ri më vonë", "Please type at least two characters": "Të lutem shkruaj të paktën dy shkronja", "Please wait": "Të lutem prit", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Të lutem prit derisa të instalohet {0}. Mund të shfaqet një dritare e zezë (ose blu). Të lutem prit që të mbyllet.", "Please wait...": "Të lutem prit...", "Portable": "Portativ", "Portable mode": "Mënyra portative", "Post-install command:": "Ko<PERSON>a pas instalimit:", "Post-uninstall command:": "Komanda pas çinstalimit:", "Post-update command:": "Komanda pas përditësimit:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Menaxheri i paketave të PowerShell-it. Gjen librari dhe skripta për të zgjeruar aftësitë e PowerShell-it<br><PERSON><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Cmdlet-a</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Komandat para dhe pas instalimit mund të shkaktojnë dëme serioze në pajisjen tënde, nëse janë krijuar për këtë qëllim. Mund të jetë shumë e rrezikshme të importosh këto komanda nga një koleksion, përveç nëse e beson burimin e atij koleksioni paketash.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Komandat para dhe pas instalimit do të ekzekutohen përpara dhe pas instalimit, përditësimit ose çinstalimit të një pakete. Ki parasysh që ato mund të prishin gjëra nëse nuk përdoren me kujdes", "Pre-install command:": "Komanda para instalimit:", "Pre-uninstall command:": "Komanda para çinstalimit:", "Pre-update command:": "Komanda para përditësimit:", "PreRelease": "<PERSON><PERSON><PERSON> paraprak", "Preparing packages, please wait...": "<PERSON> përgatiten paketat, të lutem prit...", "Proceed at your own risk.": "<PERSON><PERSON><PERSON><PERSON>, por çdo rrezik është në përgjegjësinë tënde.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Ndalo çdo lloj ngritjeje të privilegjeve përmes UniGetUI Elevator ose GSudo", "Proxy URL": "URL-ja e ndërmjetësit", "Proxy compatibility table": "Tabela e përputhshmërisë me ndërmjetësit", "Proxy settings": "Cilësimet e ndërmjetësit", "Proxy settings, etc.": "Cilësimet e ndërmjetësit, etj.", "Publication date:": "Data e botimit:", "Publisher": "<PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Menaxheri i librarisë të Python-it. Plot me librari python dhe shërbime të tjera të lidhura me Python-in<br><PERSON><PERSON><PERSON><PERSON>: <b>Librari të Python-it dhe shërbime të ngjashme</b>", "Quit": "Ndal", "Quit WingetUI": "Mbyll UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Ul paralajmërimet e UAC, ngri instalimet si paracaktim, zhblloko disa veçori të rrezikshme, etj.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Referohu te Ditarët e UniGetUI për të marrë më shumë detaje rreth skedarëve të prekur", "Reinstall": "<PERSON><PERSON><PERSON>", "Reinstall package": "Instalo paketën përeseri", "Related settings": "Cilësimet përkatëse", "Release notes": "Shënimet e botimit", "Release notes URL": "URL-ja e shënimeve të botimit", "Release notes URL:": "URL-ja e shënimeve të botimit:", "Release notes:": "Shënimet e botimit:", "Reload": "<PERSON><PERSON><PERSON>", "Reload log": "<PERSON><PERSON><PERSON>", "Removal failed": "<PERSON><PERSON><PERSON>", "Removal succeeded": "<PERSON><PERSON><PERSON> pati sukses", "Remove from list": "Hiq nga lista", "Remove permanent data": "Hiq të dhënat e përhershme", "Remove selection from bundle": "Hiq përzgjedhjet nga koleksioni", "Remove successful installs/uninstalls/updates from the installation list": "Hiq instalimet/çinstalimet/përditësimet e suksesshme nga lista e instalimeve", "Removing source {source}": "<PERSON> <PERSON><PERSON><PERSON> b<PERSON> {source}", "Removing source {source} from {manager}": "<PERSON> hiqet burimi {source} nga {manager}", "Repair UniGetUI": "Ndreq UniGetUI", "Repair WinGet": "<PERSON><PERSON><PERSON><PERSON> WinGet", "Report an issue or submit a feature request": "Njofto një problem ose paraqit një kërkesë për veçori", "Repository": "<PERSON><PERSON>", "Reset": "Rivendos", "Reset Scoop's global app cache": "Rivendos keshin global të aplikacionëve të Scoop-it", "Reset UniGetUI": "Rivendos UniGetUI", "Reset WinGet": "Rivendos WinGet", "Reset Winget sources (might help if no packages are listed)": "Rivendos burimet Winget (mund të ndihmojë nëse nuk paraqiten paketa)", "Reset WingetUI": "Rivendos UniGetUI", "Reset WingetUI and its preferences": "Rivendos UniGetUI dhe parapëlqimet e tija", "Reset WingetUI icon and screenshot cache": "Rivendos ikonën e UniGetUI dhe keshin e pamjes së ekranit", "Reset list": "Rivendos listën", "Resetting Winget sources - WingetUI": "Po rivendosen burimet e Winget - UniGetUI", "Restart": "<PERSON><PERSON><PERSON>", "Restart UniGetUI": "Rinis UniGetUI", "Restart WingetUI": "Rinis UniGetUI", "Restart WingetUI to fully apply changes": "Rinisni UniGetUI për të zbatuar plotësisht ndryshimet", "Restart later": "<PERSON><PERSON><PERSON>", "Restart now": "<PERSON><PERSON><PERSON> tani", "Restart required": "Kërkohet rini<PERSON>", "Restart your PC to finish installation": "<PERSON><PERSON><PERSON> komp<PERSON>in tënd për të përfunduar instalimin", "Restart your computer to finish the installation": "<PERSON><PERSON><PERSON> komp<PERSON>in tënd për të përfunduar instalimin", "Restore a backup from the cloud": "Rikthe një kopje rezervë nga reja", "Restrictions on package managers": "Ku<PERSON>zime mbi men<PERSON>herët e paketave", "Restrictions on package operations": "Kufizime mbi operacionet e paketave", "Restrictions when importing package bundles": "Kufizime për importimin e koleksioneve të paketave", "Retry": "<PERSON><PERSON>", "Retry as administrator": "Provo përsëri si administrator", "Retry failed operations": "Provo përsëri operacionet e dështuara", "Retry interactively": "Provo përsëri në mënyrë ndërvepruese", "Retry skipping integrity checks": "Provo përsëri duke an<PERSON><PERSON><PERSON><PERSON> kontrollet e integritetit", "Retrying, please wait...": "Po provohet s<PERSON>, të lutem prit...", "Return to top": "<PERSON><PERSON><PERSON> në fillim", "Run": "<PERSON>k<PERSON><PERSON><PERSON>", "Run as admin": "Ekze<PERSON><PERSON> si <PERSON>", "Run cleanup and clear cache": "<PERSON><PERSON><PERSON> pastrimin dhe pastro keshin", "Run last": "Ekzekuto në fund", "Run next": "Ekzekuto pas operacionit të tanishëm", "Run now": "<PERSON><PERSON><PERSON><PERSON><PERSON> tani", "Running the installer...": "Po ekzekutohet instaluesi...", "Running the uninstaller...": "Po ekzekutohet çinstaluesi...", "Running the updater...": "Po ekzekutohet përditësuesi...", "Save": "<PERSON><PERSON><PERSON>", "Save File": "<PERSON><PERSON><PERSON>", "Save and close": "<PERSON><PERSON><PERSON> dhe mbyll", "Save as": "<PERSON><PERSON><PERSON> si", "Save bundle as": "<PERSON><PERSON><PERSON>", "Save now": "<PERSON><PERSON><PERSON> tani", "Saving packages, please wait...": "<PERSON> ruhen paketat, të lutem prit...", "Scoop Installer - WingetUI": "Instaluesi Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Çinstaluesi Scoop - UniGetUI", "Scoop package": "<PERSON><PERSON><PERSON>", "Search": "K<PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Kërko për programe për tryezë, më paralajmëro kur përditësimet janë të ofruara dhe mos bëj gjëra të çuditshme. Unë nuk dua që UniGetUI të ndërlikohet tepër, dua vetëm një <b>dyqan programesh të thjeshtë</b>", "Search for packages": "<PERSON><PERSON><PERSON><PERSON>", "Search for packages to start": "<PERSON><PERSON><PERSON> të nisur kërko për paketa", "Search mode": "Mënyra e kërkimit", "Search on available updates": "Kërko në përditësimet e ofruara", "Search on your software": "Kërko në programet e tua", "Searching for installed packages...": "Po kërkohen paketat e instaluara...", "Searching for packages...": "Po kërkohen paketa...", "Searching for updates...": "Po kërkohen përditësime...", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "P<PERSON><PERSON><PERSON><PERSON><PERSON> \"{item}\" për të shtuar kovën tënde të personalizuar", "Select a folder": "Përzgjidh një <PERSON>", "Select all": "Përzgjidh të gjitha", "Select all packages": "Përzgjidh të gjitha paketat", "Select backup": "Zgjidh kopjen rezervë", "Select only <b>if you know what you are doing</b>.": "Plrzgjidh vetëm <b>nëse e di se çfarë po bën</b>.", "Select package file": "Përzgjidh skedarin e paketës", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Zgjidh kopjen rezervë që dëshiron të hapësh. <PERSON><PERSON> von<PERSON>, do të mund të rishikosh cilat paketa/programe dëshiron të rikthesh.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Zgjidh proceset që duhet të mbyllen para se kjo paketë të instalohet, p<PERSON>rditësohet ose çinstalohet.", "Select the source you want to add:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> burimin që do të shtosh:", "Select upgradable packages by default": "Përzgjidh paketat e përditësueshme si paracaktim", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "P<PERSON>rz<PERSON><PERSON><PERSON> cilët <b>menaxher<PERSON> të paketave</b> të pë<PERSON> ({0}), konfiguro se si instalohen paketat, menaxho se si trajtohen të drejtat e administratorit, etj.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "U dërgua duarshtrëngimi . Në pritje të përgjigjes së rastit të dëgjuesit... ({0}%)", "Set a custom backup file name": "Vendos një emër skedari rezervë të personalizuar", "Set custom backup file name": "Vendos emër skedari rezervë të personalizuar", "Settings": "Cilësimet", "Share": "Shpërndaj", "Share WingetUI": "Shpërndaj UniGetUI", "Share anonymous usage data": "Ndaj të dhëna anonime të përdorimit", "Share this package": "Shpërndaj këtë paketë", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Nëse modifikon cilësimet e sigurisë, do të duhet të hapësh përsëri koleksionin që ndryshimet të hyjnë në fuqi.", "Show UniGetUI on the system tray": "Shfaq UniGetUI në hapësirën e sistemit", "Show UniGetUI's version and build number on the titlebar.": "Shfaq versionin dhe numrin e ndërtimit të UniGetUI në shiritin e titullit.", "Show WingetUI": "Shfaq UniGetUI", "Show a notification when an installation fails": "Shfaq një njoftim kur një instalim dështon", "Show a notification when an installation finishes successfully": "Shfaq një njoftim kur një instalim përfundon me sukses", "Show a notification when an operation fails": "Shfaq një njoftim kur një operacion dështon", "Show a notification when an operation finishes successfully": "Shfaq një njoftim kur një operacion përfundon me sukses", "Show a notification when there are available updates": "Shfaq një njoftim kur ofrohen përditësime", "Show a silent notification when an operation is running": "Shfaq një njoftim të heshtur kur një operacion po ekzekutohet", "Show details": "<PERSON><PERSON><PERSON><PERSON> detajet", "Show in explorer": "Shfaq në Eksploruesin", "Show info about the package on the Updates tab": "Shfaq informacione rreth paketës në skedën e Përditësimeve.", "Show missing translation strings": "Shfaq fjalitë që nuk kanë përkthime", "Show notifications on different events": "<PERSON><PERSON><PERSON><PERSON> njoftimet për ngjarje të ndryshme", "Show package details": "Shfaq detajet e paketës", "Show package icons on package lists": "Shfaq ikonat e paketave në listat e paketave", "Show similar packages": "<PERSON><PERSON><PERSON><PERSON> paketa të ngjashme", "Show the live output": "<PERSON><PERSON><PERSON><PERSON> daljen dre<PERSON>përdrejtë", "Size": "<PERSON><PERSON><PERSON><PERSON>", "Skip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Skip hash check": "Ka<PERSON><PERSON><PERSON><PERSON> kontrollin e hash-it", "Skip hash checks": "Kapërce kontrollet e hash-it", "Skip integrity checks": "Kapërce kontrollet e integritetit", "Skip minor updates for this package": "Anashkalo përditësimet e vogla për këtë paketë", "Skip the hash check when installing the selected packages": "Ka<PERSON><PERSON>rce kontrollin e hash-it kur instalohen paketat e përzgjedhura", "Skip the hash check when updating the selected packages": "Ka<PERSON>ërce kontrollin e hash-it kur përditësohen paketat e përzgjedhura", "Skip this version": "Ka<PERSON><PERSON><PERSON>e këtë version", "Software Updates": "Përditësimet e Programeve", "Something went wrong": "Diçka shkoi keq", "Something went wrong while launching the updater.": "Diçka shkoi gabim gjatë nisjes së përditësuesit.", "Source": "<PERSON><PERSON><PERSON>", "Source URL:": "URL-ja e burimit:", "Source added successfully": "<PERSON><PERSON><PERSON> u shtua me sukses", "Source addition failed": "Sht<PERSON>i i burimit d<PERSON>i", "Source name:": "<PERSON><PERSON> <PERSON> burimit:", "Source removal failed": "<PERSON><PERSON><PERSON> e buri<PERSON>", "Source removed successfully": "<PERSON><PERSON><PERSON> u hoq me sukses", "Source:": "<PERSON><PERSON><PERSON>:", "Sources": "<PERSON><PERSON><PERSON>", "Start": "<PERSON><PERSON>", "Starting daemons...": "Duke ni<PERSON><PERSON>...", "Starting operation...": "Duke nisur operacionin...", "Startup options": "Rregullimet e nisjes", "Status": "<PERSON><PERSON><PERSON><PERSON>", "Stuck here? Skip initialization": "I b<PERSON>kuar këtu? Kapërce <PERSON>n", "Success!": null, "Suport the developer": "Mbështet zhvilluesin", "Support me": "<PERSON>ë m<PERSON>ështet", "Support the developer": "Mbështet zhvilluesin", "Systems are now ready to go!": "Sistemet tani janë gati!", "Telemetry": "<PERSON><PERSON>a la<PERSON> (Telemetria)", "Text": "Tekst", "Text file": "<PERSON><PERSON><PERSON> te<PERSON>t", "Thank you ❤": "Faleminderit ❤", "Thank you 😉": "Faleminderit 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Menaxheri i paketave Rust.<br><PERSON><PERSON><PERSON><PERSON>: <b>Libraritë dhe programet Rust të shkruara në Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Rezervimi NUK do të përfshijë asnjë skedar binar dhe as të dhëna të ruajtura të ndonjë programi.", "The backup will be performed after login.": "Rezervimi do të bëhet pas hyrjes.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Rezervimi do të përfshijë listën e plotë të paketave të instaluara dhe rregullimet e instalimit të tyre. Gjithashtu do të ruhen përditësimet e shpërfillura dhe versionet e anashkaluara.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Koleksioni që po përpiqesh të ngarkosh duket i pavlefshëm. Të lutem kontrollo skedarin dhe provo përsëri.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Shuma e vërtetësimit të instaluesit nuk përkon me vlerën e pritur dhe vërtetësia e instaluesit nuk mund të verifikohet. Nëse i beson botuesit, {0} paketën duke ka<PERSON><PERSON><PERSON><PERSON> pë<PERSON><PERSON><PERSON> kontrollin e hash-it.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Menaxheri klasik i paketave për Windows. Do të gjesh gjithçka atje. <br>Përmban: <b>Programe të përgjithshme</b>", "The cloud backup completed successfully.": "Kopja rezervë në re u përfundua me sukses.", "The cloud backup has been loaded successfully.": "Kopja rezervë në re u ngarkua me sukses.", "The current bundle has no packages. Add some packages to get started": "Koleksioni i tanishëm nuk ka paketa. Shto disa paketa për të filluar", "The executable file for {0} was not found": "<PERSON><PERSON><PERSON> e<PERSON><PERSON><PERSON> për {0} nuk u gjet", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Rregullimet e mëposhtme do të aplikohen si paracaktim sa herë që një paketë {0} instalohet, përditësohet ose ç<PERSON>talohet.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Paketat që vijojnë do të eksportohen në një skedar JSON. Nuk do të ruhen të dhëna të përdoruesit ose binarët.", "The following packages are going to be installed on your system.": "Paketat që vijojnë do të instalohen në sistemin tënd.", "The following settings may pose a security risk, hence they are disabled by default.": "Cilësimet e mëposhtme mund të paraqesin një rrezik sigurie, prandaj ato janë të çaktivizuara si paracaktim.", "The following settings will be applied each time this package is installed, updated or removed.": "Cilësimet që vijojnë do të zbatohen sa herë që kjo paketë instalohet, përditësohet ose hiqet.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Cilësimet që vijojnë do të zbatohen sa herë që kjo paketë instalohet, përditësohet ose hiqet. Ato do të ruhen automatikisht.", "The icons and screenshots are maintained by users like you!": "Ikonat dhe pamjet e ekranit mirëmbahen nga përdo<PERSON> si ti!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "Vërtetësia e instaluesit nuk mund të vërtetohej.", "The installer has an invalid checksum": "Instaluesi ka një shumë të vërtetësimit të pavlefshme", "The installer hash does not match the expected value.": "Hash-i i instaluesit nuk përputhet me vlerën e pritur.", "The local icon cache currently takes {0} MB": "Kesh-i lokal i ikonave aktualisht merr {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Qëllimi kryesor i këtij projekti është të krijojë një ndërfaqe të përdoruesit intuitive për të drejtuar menaxherët më të zakonshëm të paketave CLI për Windows, si Winget dhe Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "<PERSON><PERSON> \"{0}\" nuk u gjet në men<PERSON>herin e paketave \"{1}\"", "The package bundle could not be created due to an error.": "Koleksioni i paketave nuk mund të krijohej për shkak të një gabimi.", "The package bundle is not valid": "Koleksioni i paketave është i pavlefshëm", "The package manager \"{0}\" is disabled": "Menaxheri i paketave \"{0}\" është i çaktivizuar", "The package manager \"{0}\" was not found": "Menaxheri i paketave \"{0}\" nuk u gjet", "The package {0} from {1} was not found.": "Paketa {0} nga {1} nuk u gjet.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Paketat e paraqitura këtu do të shpërfillen kur kontrollohen përditësimet. Kliko dy herë mbi to ose kliko butonin në të djathtën e tyre për të ndaluar shpërfilljen e përditësimeve të tyre.", "The selected packages have been blacklisted": "Paketat e përzgjedhura janë në listën e zezë", "The settings will list, in their descriptions, the potential security issues they may have.": "Cilësimet do të tregojnë, në përshkrimet e tyre, problemet e mundshme të sigurisë që mund të shkaktojnë.", "The size of the backup is estimated to be less than 1MB.": "Madhësia e kopjes rezervë vlerësohet të jetë më pak se 1MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON> {source} u shtua te {manager} me sukses", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON> {source} u hoq nga {manager} me sukses", "The system tray icon must be enabled in order for notifications to work": "Ikona e hapësirës së sistemit duhet të jetë aktivizuar që njoftimet të funksionojnë", "The update process has been aborted.": "Procesi i përditësimit është ndërprerë.", "The update process will start after closing UniGetUI": "Procesi i përditësimit do të fillojë pasi të mbyllet UniGetUI", "The update will be installed upon closing WingetUI": "Përditësimi do të instalohet pas mbylljes së UniGetUI", "The update will not continue.": "Përditësimi nuk do të vazhdojë.", "The user has canceled {0}, that was a requirement for {1} to be run": "Përdoruesi ka anuluar {0}, që ishte një domosdoshmëri që {1} të ekzekutohet", "There are no new UniGetUI versions to be installed": "Nuk ka versione të reja të UniGetUI për t'u instaluar", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Ka operacione në vazhdim. Mbyllja e UniGetUI mund të shkaktojë deshtimin e tyre. Do të vazhdosh?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Ka disa video të shkëlqyera në YouTube që shfaqin UniGetUI dhe aftësitë e tij. Mund të mësosh aftësi dhe këshilla të dobishme!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Ka dy arsye kryesore për të mos ekzekutuar UniGetUI si administrator:\n  E para është se menaxheri i paketave Scoop mund të shkaktojë probleme me disa komanda kur ekzekutohet me të drejtat e administratorit.\n  E dyta është se ekzekutimi i UniGetUI si administrator do të thotë që çdo paketë që shkarkon do të ekzekutohet si administrator (dhe kjo nuk është gjë e sigurt).\n  Mos harro se nëse ke nevojë të instalosh një paketë specifike si administrator, gjithmonë mund të klikosh butonin e djathtë mbi artikullin -> Instalo/Përditëso/Çinstalo si administrator.", "There is an error with the configuration of the package manager \"{0}\"": "Ka një gabim me konfigurimin e menaxherit të paketave \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Ka një instalim në vazdhim. Nëse mbyll UniGetUI, instalimi mund të dështojë dhe të ketë rezultate të papritura. Dëshiron ende të mbyllësh UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Ato janë programet e ngarkuara për instalimin, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dhe heqjen e paketave.", "Third-party licenses": "Leje të palëve të treta", "This could represent a <b>security risk</b>.": "<PERSON><PERSON> mund të përfaqësojë një <b>r<PERSON><PERSON> sigurie</b>.", "This is not recommended.": "Nuk këshillohet.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "<PERSON>jo është ndoshta për shkak të faktit se paketa që të dërguan është hequr, ose është botuar në një menaxher paketash që nuk është aktivizuar. ID-ja e marrë është {0}", "This is the <b>default choice</b>.": "<PERSON><PERSON> <b>përzgjedhja e paracaktuar</b>.", "This may help if WinGet packages are not shown": "<PERSON>jo mund të ndihmojë nëse paketat WinGet nuk shfaqen", "This may help if no packages are listed": "<PERSON>jo mund të ndihmojë nëse nuk ka paketa në listë", "This may take a minute or two": "<PERSON><PERSON> mund të zgjasë një ose dy minuta", "This operation is running interactively.": "Ky operacion po ekzekutohet në mënyrë ndërvepruese.", "This operation is running with administrator privileges.": "Ky operacion po ekzekutohet me privilegje administratori.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Ky rregullim do të shkaktojë probleme. Çdo veprim që nuk mund të ngrihet vetë me privilegje do të DËSHTOJË. Instalimi/përditësimi/çinstalimi si administrator NUK DO TË PUNOJË.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Ky koleksion paketash kishte disa cilësime që mund të jenë të rrezikshme dhe mund të injorohen si paracaktim.", "This package can be updated": "<PERSON><PERSON> paketë mund të përditëso<PERSON>t", "This package can be updated to version {0}": "<PERSON><PERSON> paketë mund të përditësohet në versionin {0}", "This package can be upgraded to version {0}": "<PERSON><PERSON> paketë mund të përditësohet në versionin {0}", "This package cannot be installed from an elevated context.": "<PERSON><PERSON> paketë nuk mund të instalohet me privilegje të ngritura.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Kjo paketë nuk ka pamje nga ekrani apo i mungon ikona? Kontribuo në UniGetUI duke shtuar ikonat dhe pamjet e ekranit që mungojnë në bazën tonë të të dhënave të hapur publike.", "This package is already installed": "Kjo paketë është instaluar tashmë", "This package is being processed": "<PERSON>jo paketë është duke u përp<PERSON>uar", "This package is not available": "<PERSON><PERSON> paketë nuk ofrohet", "This package is on the queue": "Kjo paketë është në radhë", "This process is running with administrator privileges": "Ky proces po ekzekutohet me privilegje administratori", "This project has no connection with the official {0} project — it's completely unofficial.": "Ky projekt nuk ka asnjë lidhje me projektin zyrtar të {0} — është krejtësisht jozyrtar.", "This setting is disabled": "Ky cilësim është i çaktivizuar", "This wizard will help you configure and customize WingetUI!": "Ky drejtues do të ndihmojë të konfigurosh dhe personalizosh UniGetUI!", "Toggle search filters pane": "Ndrysho gje<PERSON>je<PERSON> e panelit të filtrave të kërkimit", "Translators": "P<PERSON><PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Përpiqu të mbyllësh proceset që refuzojnë të mbyllen kur u kërkohet", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Aktivizimi i këtij rregullimi lejon ndryshimin e skedarit ekzekutues që përdoret për të ndërvepruar me menaxherët e paketave. Ndërsa kjo mundëson personalizim më të hollësishëm të proceseve të instalimit, gjithashtu mund të jetë e rrezikshme", "Type here the name and the URL of the source you want to add, separed by a space.": "Shkruaj këtu emrin dhe URL-në e burimit që dëshiron të shtosh, të ndarë me një hapësirë.", "Unable to find package": "Paketa nuk mund të gjendet", "Unable to load informarion": "Informacioni nuk mund të ngarkohet", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI mbledh të dhëna anonime të përdorimit për të përmirësuar përvojën e përdoruesit.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI mbledh të dhëna anonime të përdorimit me qëllim të vetëm kuptimin dhe përmirësimin e përvojës të përdoruesit.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI ka zbuluar një shkurtore të re të tryezës që mund të fshihet automatikisht.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI ka zbuluar shkurtoret të tryezës që vijojnë, të cilat mund të fshihen automatikisht gjatë përditësimeve të ardhshme.", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI ka zbuluar {0, plural, one {një shkurtore të re të tryezës që mund të fshihet} other {{0} shkurtore të reja të tryezës që mund të fshihen}} automatikisht.", "UniGetUI is being updated...": "UniGetUI po përditësohet...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI nuk është i lidhur me asnjë nga menaxherët e paketave të përputhur. UniGetUI është një projekt i pavarur.", "UniGetUI on the background and system tray": "UniGetUI në sfond dhe në hapësirën e sistemit", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI ose disa nga përbërësit e tij mungojnë ose janë të dëmtuar.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI kërkon {0} për të punuar, por nuk u gjet në sistemin tënd.", "UniGetUI startup page:": "Faqja e nisjes së UniGetUI:", "UniGetUI updater": "Përditësuesi e UniGetUI", "UniGetUI version {0} is being downloaded.": "Versioni {0} i UniGetUI po shkarkohet.", "UniGetUI {0} is ready to be installed.": "Versioni {0} i UniGetUI është gati për t'u instaluar.", "Uninstall": "Çinstalo", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON> (dhe paketat e tija)", "Uninstall and more": "Çinstalimi dhe të tjera", "Uninstall and remove data": "<PERSON><PERSON><PERSON><PERSON> dhe hiq të dhënat", "Uninstall as administrator": "Çinstalo si administrator", "Uninstall canceled by the user!": "Çinstalimi u anulua nga përdoruesi!", "Uninstall failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall options": "Rregullimet e çinstalimit", "Uninstall package": "Çinstalo paketën", "Uninstall package, then reinstall it": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON>n, më pas instaloje përsëri", "Uninstall package, then update it": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON>n, më pas përditësoje", "Uninstall previous versions when updated": "Çinstalo versionet e mëparshme gjatë përditësimit", "Uninstall selected packages": "Çinstalo paketat e përzgjedhura", "Uninstall selection": "Çinstalo përzgjedhjet", "Uninstall succeeded": "Çinstalimi pati sukses", "Uninstall the selected packages with administrator privileges": "Çinstalo paketat e përzgjedhura me privilegje administratori", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Paketat e çinstalueshme me origjinë të paraqitur si \"{0}\" nuk botohen në asnjë menaxher paketash, kështu që nuk ka asnjë informacion të ofruar për të treguar rreth tyre.", "Unknown": "<PERSON><PERSON><PERSON>", "Unknown size": "Madhësi e panjohur", "Unset or unknown": "I papërcaktuar ose i panjohur", "Up to date": "I përditësuar", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Përditëso automatikisht UniGetUI", "Update all": "Përditëso të gjitha", "Update and more": "Përditësimi dhe të tjera", "Update as administrator": "Përditëso si <PERSON>", "Update check frequency, automatically install updates, etc.": "Shpeshtësia e kontrollit për përditësime, instalimi automatik i përditësimeve, etj.", "Update checking": null, "Update date": "Data e përditësimit", "Update failed": "Përditë<PERSON><PERSON>", "Update found!": "Përditësimi u gjet!", "Update now": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tani", "Update options": "Rregullimet e përditësimit", "Update package indexes on launch": "Përditëso treguesit e paketave në nisje", "Update packages automatically": "Përditëso paketat automatikisht", "Update selected packages": "Përditëso paketat e përzgjedhura", "Update selected packages with administrator privileges": "Përditëso paketat e përzgjedhura me privilegjet e administratorit", "Update selection": "Përditëso p<PERSON>", "Update succeeded": "Përditësimi pati sukses", "Update to version {0}": "Përditëso në versionin {0}", "Update to {0} available": "Ofrohet përditësim për {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Përditëso portfiles Git të vcpkg automatikisht (kërkon që Git të jetë i instaluar).", "Updates": "Përditesime", "Updates available!": "Ofrohen përditësime!", "Updates for this package are ignored": "Përditësimet për këtë paketë do të shpërfillen", "Updates found!": "Përditësimet u gjetën!", "Updates preferences": "Parapëlqimet e përditësimeve", "Updating WingetUI": "Po përditësohet UniGetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Përdor WinGet-in e vjetër të përfshirë në vend të CMDLet-ave PowerShell", "Use a custom icon and screenshot database URL": "Përdor një URL të bazës së të dhënave për ikona dhe pamje të ekranit të personalizuar", "Use bundled WinGet instead of PowerShell CMDlets": "Përdor WinGet-in të përfshirë në vend të CMDLet-ave PowerShell", "Use bundled WinGet instead of system WinGet": "Përdor WinGet-in e instaluar në vend të WinGet-it të sistemit", "Use installed GSudo instead of UniGetUI Elevator": "Përdor GSudo të instaluar në vend të UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Përdor GSudo të instaluar në vend të atij të paracaktuar", "Use system Chocolatey": "Përdor Chocolatey të sistemit", "Use system Chocolatey (Needs a restart)": "Përdor Chocolatey të sistemit (Kërkon rinisje)", "Use system Winget (Needs a restart)": "Përdor Winget-in e sistemit (kërkon rinisje)", "Use system Winget (System language must be set to english)": "Përdor Winget-in e sistemit (Gjuha e sistemit duhet të jetë në anglisht)", "Use the WinGet COM API to fetch packages": "Përdor COM API-në e WinGet për të marrë paketat", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Përdor modulin PowerShell të WinGet në vend të COM API-t të WinGet", "Useful links": "Lidhje të dobishme", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "User interface preferences": "Parapëlqimet e ndërfaqes së përdoruesit", "User | Local": "Përdorues | Lokal", "Username": "Emri i përdoruesit", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Përdorimi i UniGetUI nënkupton pranimin e Lejes GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Përdorimi i UniGetUI nënkupton pranimin e Lejes MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Rrënja e Vcpkg nuk u gjet. Përcakto ndryshoren e mjedisit %VCPKG_ROOT% ose përcaktoje nga Cilësimet e UniGetUI", "Vcpkg was not found on your system.": "Vcpkg nuk u gjet në sistemin tënd.", "Verbose": "Fjalëshumë", "Version": "Versioni", "Version to install:": "Versioni për t'u instaluar:", "Version:": "Versioni:", "View GitHub Profile": "<PERSON><PERSON> profi<PERSON>", "View WingetUI on GitHub": "Shiko UniGetUI në GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "<PERSON><PERSON> kodin burimor të UniGetUI. <PERSON><PERSON> atje, mund të njoftosh defekte ose të sugjerosh veçori ose edhe të kontribuosh drejtpërdrejt në Projektin UniGetUI", "View mode:": "Mënyra e shikimit:", "View on UniGetUI": "Shiko në UniGetUI", "View page on browser": "<PERSON><PERSON> faqen në shfletues", "View {0} logs": "<PERSON><PERSON><PERSON><PERSON> ditarin e {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Prit derisa pajisja të lidhet me internetin para se të përpiqesh të kryesh detyra që kërkojnë lidhje me internet.", "Waiting for other installations to finish...": "Në pritje të përfundimit të instalimeve të tjera...", "Waiting for {0} to complete...": "<PERSON> pritur që {0} të përfundojë...", "Warning": "Paralajmërim", "Warning!": "Paralajmërim!", "We are checking for updates.": "Po kontrollojmë për përditësime.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Nuk mund të ngarkohen informacione të detajuara për këtë paketë, sepse nuk u gjet në asnjë nga burimet e paketave të tua", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Nuk mund të ngarkohen informacione të detajuara për këtë paketë, sepse nuk është instaluar nga një nga menaxherët e paketave të ofruar.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON>uk mund të {action} {package}. Të lutem provo përsëri më vonë. Kliko në \"{showDetails}\" për të marrë ditarin nga instaluesi.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON>uk mund të {action} {package}. Të lutem provo përsëri më vonë. Kliko në \"{showDetails}\" për të marrë ditarin nga çinstaluesi.", "We couldn't find any package": "Nuk u gjet asnjë paketë", "Welcome to WingetUI": "Mirë se erdhe në UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Gjatë instalimit në grup të paketave nga një koleksion, instalo edhe paketat që janë tashmë të instaluara", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON><PERSON> z<PERSON>en shkurtore të reja, fshiji automatikisht në vend që të shfaqet ky dialog.", "Which backup do you want to open?": "Cilën kopje rezervë dëshiron të hapësh?", "Which package managers do you want to use?": "<PERSON><PERSON><PERSON><PERSON> menaxher<PERSON> paketash dëshiron të përdor<PERSON>sh?", "Which source do you want to add?": "<PERSON><PERSON><PERSON> burim dë<PERSON>ron të shtosh?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Megjithëse Winget mund të përdoret brenda WingetUI, WingetUI mund të përdoret me menaxherët e tjerë të paketave, gjë që mund të jetë e ngatërruar. Në të kaluar<PERSON>n, WingetUI ishte projektuar për të punuar vetëm me Winget, por tani jo më, dhe për këtë arsye WingetUI nuk përfaqëson atë që synon të bëhet ky projekt.", "WinGet could not be repaired": "WinGet nuk mund të rregullohej", "WinGet malfunction detected": "U zbulua një gabim i WinGet", "WinGet was repaired successfully": "WinGet u rregullua me sukses", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Gjithçka është e përditësuar", "WingetUI - {0} updates are available": "UniGetUI - {0, plural, one {ofrohet {0} përditësim} other {ofrohen {0} përditësime}}", "WingetUI - {0} {1}": "UniGetUI - {0} {1}", "WingetUI Homepage": "Kryefaqja e UniGetUI", "WingetUI Homepage - Share this link!": "Kryefaqja e UniGetUI - Shpërndaj këtë lidhje!", "WingetUI License": "Leja e UniGetUI", "WingetUI Log": "Ditari i UniGetUI", "WingetUI Repository": "Depo e UniGetUI", "WingetUI Settings": "Cilësimet e UniGetUI", "WingetUI Settings File": "Skedari i cilësimeve i UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI përdor libraritë që vijojnë. Pa to, UniGetUI nuk do të ishte i mundur.", "WingetUI Version {0}": "Versioni {0} i UniGetUI", "WingetUI autostart behaviour, application launch settings": "Sjellja e nisjes automatike të UniGetUI, cilësimet e nisjes së aplikacionit", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI mund të kontrollojë nëse programet e tua kanë përditësime dhe t'i instalojë automatikisht, nëse dëshiron", "WingetUI display language:": "Gjuha e UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI është ekzekutuar si administrator, gjë që nuk këshillohet. Kur përdor UniGetUI si administrator, ÇDO operacion i nisur nga UniGetUI do të ketë privilegje administratori. Ti mund ta përdorësh programin gjithsesi, por këshillohet të mos ekzekutosh UniGetUI me privilegje administratori.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI është përkthyer në më shumë se 40 gjuhë falë përkthyesve vullnetarë. Faleminderit 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI nuk është përkthyer me makineri. Përdoruesit e mëposhtëm janë përgjegjës për përkthimet:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI është një aplikacion që e bën më të lehtë menaxhimin e programeve të tua, duke ofruar një ndërfaqe grafike të plotë për menaxherët e paketave të rreshtit të komandës të tu.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI po riemërtohet për të theksuar ndryshimin midis WingetUI (ndërfaqja që po përdor tani) dhe Winget (një menaxher paketash i zhvilluar nga Microsoft me të cilin nuk jam i lidhur)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI po përditësohet. Kur të përfundojë, UniGetUI do të riniset vetë", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI është falas dhe do të jetë falas përgjithmonë. Pa reklama, pa kartë krediti, pa version premium. 100% falas, përgjithmonë.", "WingetUI log": "Ditari i UniGetUI", "WingetUI tray application preferences": "Parapëlqimet e aplikacionit UniGetUI në hapësirën e njoftimeve", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI përdor libraritë që vijojnë. Pa to, UniGetUI nuk do të ishte i mundur.", "WingetUI version {0} is being downloaded.": "Versioni {0} i UniGetUI po shkarkohet.", "WingetUI will become {newname} soon!": "WingetUI do të bëhet {newname} së shpejti!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI nuk do të kontrollojë periodikisht për përditësime. Ato do të kontrollohen sidoqoftë në fillim, por nuk do të paralajmërohesh për to.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI do të shfaqë një kërkesë UAC sa herë që një paketë kërkon që të instalohet me privilegje të ngritura.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI së shpejti do të quhet {newname}. Kjo nuk do të përfaqësojë ndonjë ndryshim në aplikacion. Unë (zhvilluesi) do të vazhdoj zhvillimin e këtij projekti siç po bëj tani, por me një emër tjetër.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI nuk do të ishte i mundur pa ndihmën e kontribuesve tanë të dashur. Shiko profilin e tyre GitHub, UniGetUI nuk do të ishte i mundur pa ta!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI nuk do të ishte i mundur pa ndihmën e kontribuesve. Faleminderit të gjithëve 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} është gati për t'u instaluar.", "Write here the process names here, separated by commas (,)": "Shkruaj këtu emrat e proceseve, të ndarë me presje (,)", "Yes": "Po", "You are logged in as {0} (@{1})": "Ke hyr si {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Mund ta ndryshosh këtë sjellje te cilësimet e sigurisë së UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Mund të përcaktosh komandat që do të ekzekutohen para ose pas instalimit, përditësimit ose çinstalimit të kësaj pakete. Ato do të ekzekutohen në një dritare komandash, kështu që skriptet CMD do të funksionojnë këtu.", "You have currently version {0} installed": "<PERSON>i ke të instaluar versionin {0}", "You have installed WingetUI Version {0}": "Ke instaluar UniGetUI në versionin {0}", "You may lose unsaved data": "Mund të humbasësh të dhënat e pa ruajtura.", "You may need to install {pm} in order to use it with WingetUI.": "Mund të të duhet të instalosh {pm} në mënyrë që ta përdorësh me UniGetUI.", "You may restart your computer later if you wish": "<PERSON><PERSON><PERSON>, mund ta rinisësh kompjuterin më vonë", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Do të kërkohet vetëm një herë dhe të drejtat e administratorit do të jepen paketave që i kërkojnë ato.", "You will be prompted only once, and every future installation will be elevated automatically.": "Do të kërkohet vetëm një herë dhe çdo instalim i ardhshëm do të instalohet me privilegje të ngritura automatikisht.", "You will likely need to interact with the installer.": "Ka shumë gjasa që do të duhet të ndërveprosh me instaluesin.", "[RAN AS ADMINISTRATOR]": "EKZEKUTUAR SI ADMINISTRATOR", "buy me a coffee": "më bli një kafe", "extracted": "nxjerrë", "feature": "<PERSON><PERSON><PERSON>", "formerly WingetUI": "m<PERSON> par<PERSON> WingetUI", "homepage": "k<PERSON>efaqe", "install": "instalo", "installation": "instalim", "installed": "instaluar", "installing": "duke instaluar", "library": "librari", "mandatory": "i detyrueshëm", "option": "rregullim", "optional": "fakultativ", "uninstall": "çinstalo", "uninstallation": "çinstalim", "uninstalled": "<PERSON><PERSON><PERSON><PERSON>r", "uninstalling": "duke <PERSON><PERSON><PERSON><PERSON><PERSON>", "update(noun)": "përditësim", "update(verb)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updating": "duke <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version {0}": "versioni {0}", "{0} Install options are currently locked because {0} follows the default install options.": "Rregullimet e instalimit të {0} janë të bllokuara për momentin sepse {0} ndjek rregullimet e paracaktuara të instalimit.", "{0} Uninstallation": "{0} Ç<PERSON><PERSON><PERSON>", "{0} aborted": "{0} u ndërpre", "{0} can be updated": "{0} mund të pë<PERSON>", "{0} can be updated to version {1}": "{0} mund të përditësohet në versionin {1}", "{0} days": "{0} ditë", "{0} desktop shortcuts created": "{0, plural, one {U krijua një shkurtore në tryezë} other {U krijuan {0} shkurtore në tryezë}}", "{0} failed": "{0} d<PERSON><PERSON><PERSON><PERSON>", "{0} has been installed successfully.": "{0} u instalua me sukses.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} u instalua me sukses. Këshillohet të riniset UniGetUI për ta mbaruar instalimin.", "{0} has failed, that was a requirement for {1} to be run": "{0} d<PERSON><PERSON><PERSON><PERSON>, që ishte një domosdoshmëri që {1} të ekzekuto<PERSON>t", "{0} homepage": "<PERSON><PERSON><PERSON><PERSON><PERSON> e {0}", "{0} hours": "{0} orë", "{0} installation": "{0} instalim", "{0} installation options": "{0} rregullimet e instalimit", "{0} installer is being downloaded": "Instaluesi i {0} po sh<PERSON><PERSON><PERSON>t", "{0} is being installed": "{0} po instalohet", "{0} is being uninstalled": "{0} po <PERSON><PERSON><PERSON><PERSON>et", "{0} is being updated": "{0} po p<PERSON>rditësohet", "{0} is being updated to version {1}": "{0} po përditësohet në versionin {1}", "{0} is disabled": "{0} është çaktivizuar", "{0} minutes": "{0, plural, one {{0} minutë} other {{0} minuta}}", "{0} months": "{0} muaj", "{0} packages are being updated": "Po {0, plural, one {përditësohet {0} paketë} other {përditësohen {0} paketa}}", "{0} packages can be updated": "Mund të {0, plural, one {përditësohet {0} paketë} other {përditësohen {0} paketa}}", "{0} packages found": "U {0, plural, one {gjet {0} paketë} other {gjetën {0} paketa}}", "{0} packages were found": "{0, plural, one {Është gjetur {0} pake<PERSON><PERSON>} other {<PERSON><PERSON> gjetur {0} paketa}}", "{0} packages were found, {1} of which match the specified filters.": "U {0, plural, one {gjet {0} paketë} other {gjetën {0} paketa}}, {1, plural, one {dhe {1} përputhet} other {nga të cilat {1} pë<PERSON><PERSON>en}} me filtrat e specifikuar.", "{0} selected": null, "{0} settings": "Cilësimet e {0}", "{0} status": "<PERSON><PERSON><PERSON><PERSON> e {0}", "{0} succeeded": "{0} pati sukses", "{0} update": "{0} përditësim", "{0} updates are available": "{0, plural, one {Of<PERSON>het {0} përditësim} other {<PERSON><PERSON>hen {0} përditësime}}", "{0} was {1} successfully!": "{0} ësh<PERSON>ë {1} me sukses!", "{0} weeks": "{0} jav<PERSON>", "{0} years": "{0} vite", "{0} {1} failed": "{1} i {0} d<PERSON><PERSON><PERSON><PERSON>", "{package} Installation": "{package} Instalim", "{package} Uninstall": "{package} Çinstalim", "{package} Update": "Përditësim i {package}", "{package} could not be installed": "{package} nuk mund të instalohej", "{package} could not be uninstalled": "{package} nuk mund të <PERSON>", "{package} could not be updated": "{package} nuk mund të përditësohej", "{package} installation failed": "Instalimi i {package} d<PERSON>sh<PERSON>i", "{package} installer could not be downloaded": "Instaluesi i {package} nuk mund të shkarkohej", "{package} installer download": "Shkarkim i instaluesit të {package}", "{package} installer was downloaded successfully": "Instaluesi i {package} u shkarkua me sukses", "{package} uninstall failed": "Çinstalimi i {package} dësh<PERSON>i", "{package} update failed": "Përditësimi i {package} dësh<PERSON>i", "{package} update failed. Click here for more details.": "Përditësimi i {package} dështoi. Kliko këtu për më shumë detaje.", "{package} was installed successfully": "{package} u instalua me sukses", "{package} was uninstalled successfully": "{package} u çinstalua me sukses", "{package} was updated successfully": "{package} u përditësua me sukses", "{pcName} installed packages": "Paketat e instaluara në {pcName}", "{pm} could not be found": "{pm} nuk mund të gjendej", "{pm} found: {state}": "{pm} u gjet: {state}", "{pm} is disabled": "{pm} ësh<PERSON>ë çaktivizuar", "{pm} is enabled and ready to go": "{pm} është aktivizuar dhe është gati", "{pm} package manager specific preferences": "Parapëlqimet specifike të menaxherit të paketave {pm}", "{pm} preferences": "Parapëlqimet e {pm}", "{pm} version:": "Versioni i {pm}:", "{pm} was not found!": "{pm} nuk u gjet!"}