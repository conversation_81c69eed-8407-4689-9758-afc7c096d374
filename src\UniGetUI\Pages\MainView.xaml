<?xml version="1.0" encoding="utf-8" ?>
<UserControl
    x:Class="UniGetUI.Interface.MainView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:animatedvisuals="using:Microsoft.UI.Xaml.Controls.AnimatedVisuals"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
    xmlns:controls1="using:UniGetUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:interface="using:UniGetUI.Interface"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:operationWidgets="using:UniGetUI.Controls.OperationWidgets"
    xmlns:operations="using:UniGetUI.PackageOperations"
    xmlns:pages="using:UniGetUI.Interface"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    MinWidth="200"
    MinHeight="200"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary>
            <widgets:BetterMenu x:Name="MoreNavButtonMenu" Placement="Right">
                <widgets:BetterMenuItem
                    Name="WingetUILogs"
                    Click="UniGetUILogs_Click"
                    IconName="buggy"
                    Text="WingetUI Log" />
                <widgets:BetterMenuItem
                    Name="ManagerLogsMenu"
                    Click="ManagerLogsMenu_Click"
                    IconName="console"
                    Text="Package Manager logs" />
                <widgets:BetterMenuItem
                    Name="OperationHistoryMenu"
                    Click="OperationHistoryMenu_Click"
                    IconName="history"
                    Text="Operation history" />
                <MenuFlyoutSeparator Height="5" />
                <widgets:BetterMenuItem
                    x:Name="VersionMenuItem"
                    IconName="info_round"
                    IsEnabled="False" />
                <widgets:BetterMenuItem
                    Name="ReleaseNotesMenu"
                    Click="ReleaseNotesMenu_Click"
                    IconName="megaphone"
                    Text="Release notes" />
                <MenuFlyoutSeparator Height="5" />
                <widgets:BetterMenuItem
                    Name="HelpMenu"
                    Click="HelpMenu_Click"
                    IconName="help"
                    Text="Help" />
                <widgets:BetterMenuItem
                    Name="InfoMenu"
                    Click="AboutNavButton_Click"
                    IconName="Info_Round"
                    Text="About" />
                <MenuFlyoutSeparator Height="5" />
                <widgets:BetterMenuItem
                    Name="QuitWingetUI"
                    Click="QuitUniGetUI_Click"
                    IconName="close_round"
                    Text="Quit WingetUI" />
            </widgets:BetterMenu>

            <widgets:BetterMenu x:Name="OperationListMenu" Placement="Bottom">
                <widgets:BetterMenuItem
                    Name="RetryFailedOps"
                    Click="RetryFailedOps_Click"
                    IconName="Reload"
                    Text="Retry failed operations" />
                <MenuFlyoutSeparator />
                <widgets:BetterMenuItem
                    Name="ClearSuccessful"
                    Click="ClearSuccessfulOps_Click"
                    IconName="ClipboardList"
                    Text="Clear successful operations" />
                <widgets:BetterMenuItem
                    Name="ClearAllFinished"
                    Click="ClearAllFinished_OnClick"
                    IconName="ClipboardList"
                    Text="Clear finished operations" />
                <MenuFlyoutSeparator />
                <widgets:BetterMenuItem
                    Name="CancellAllOps"
                    Click="CancellAllOps_Click"
                    IconName="Cross"
                    Text="Cancel all operations" />
            </widgets:BetterMenu>

            <DataTemplate x:Key="OperationBadgeTemplate" x:DataType="operationWidgets:OperationBadge">
                <Button
                    Width="32"
                    Height="32"
                    Margin="6,0,0,0"
                    Padding="0"
                    CornerRadius="6"
                    ToolTipService.ToolTip="{x:Bind Tooltip}">
                    <widgets:LocalIcon Icon="{x:Bind Icon}" />
                    <Button.Flyout>
                        <widgets:BetterFlyout>
                            <StackPanel Orientation="Horizontal" Spacing="12">
                                <widgets:LocalIcon Icon="{x:Bind Icon}" />
                                <StackPanel Orientation="Vertical" Spacing="6">
                                    <TextBlock
                                        MaxWidth="200"
                                        Text="{x:Bind PrimaryBanner}"
                                        TextWrapping="WrapWholeWords" />
                                    <TextBlock
                                        MaxWidth="200"
                                        Opacity="0.6"
                                        Text="{x:Bind SecondaryBanner}"
                                        TextWrapping="WrapWholeWords"
                                        Visibility="{x:Bind SecondaryBannerVisible}" />
                                </StackPanel>
                            </StackPanel>
                        </widgets:BetterFlyout>
                    </Button.Flyout>
                </Button>
            </DataTemplate>

            <DataTemplate x:Key="OperationTemplate" x:DataType="operationWidgets:OperationControl">
                <ItemContainer AutomationProperties.Name="{x:Bind Title}">
                    <Grid
                        Padding="8"
                        Background="{x:Bind Background, Mode=OneWay}"
                        ColumnSpacing="0"
                        RowSpacing="6">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Setter Property="Background" Value="{ThemeResource SystemFillColorNeutralBackgroundBrush}" />
                                <Setter Property="BorderBrush" Value="{StaticResource ExpanderContentBorderBrush}" />
                                <Setter Property="BorderThickness" Value="1" />
                                <Setter Property="CornerRadius" Value="8" />
                                <Setter Property="Margin" Value="0" />
                                <Setter Property="Padding" Value="0" />
                            </Style>
                        </Grid.Style>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="40" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="110" />
                            <ColumnDefinition Width="36" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.Resources>
                            <ResourceDictionary>
                                <Style BasedOn="{StaticResource DefaultButtonStyle}" TargetType="Button">
                                    <Setter Property="Height" Value="32" />
                                    <Setter Property="CornerRadius" Value="2" />
                                    <Setter Property="BorderThickness" Value="0" />
                                </Style>
                            </ResourceDictionary>
                        </Grid.Resources>
                        <Image
                            Grid.Column="0"
                            Width="32"
                            Height="24"
                            Margin="0"
                            VerticalAlignment="Center">
                            <Image.Source>
                                <BitmapImage UriSource="{x:Bind Icon, Mode=OneWay}" />
                            </Image.Source>
                        </Image>
                        <TextBlock
                            Grid.Column="1"
                            Margin="6,0,0,0"
                            VerticalAlignment="Center"
                            Text="{x:Bind Title}" />
                        <Button
                            Name="OutputViewewBlock"
                            Grid.Column="2"
                            Margin="12,0,0,0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            HorizontalContentAlignment="Left"
                            Click="{x:Bind LiveLineClick}"
                            Content="{x:Bind LiveLine, Mode=OneWay}"
                            CornerRadius="5"
                            FontFamily="Consolas" />
                        <Border
                            Grid.Column="2"
                            Height="12"
                            Margin="12,0,0,0"
                            VerticalAlignment="Bottom"
                            CornerRadius="6">
                            <ProgressBar
                                Height="6"
                                Margin="0,0,0,-2"
                                VerticalAlignment="Bottom"
                                CornerRadius="0,0,6,6"
                                Foreground="{x:Bind ProgressForeground, Mode=OneWay}"
                                IsIndeterminate="{x:Bind ProgressIndeterminate, Mode=OneWay}"
                                Maximum="100"
                                Value="{x:Bind ProgressValue, Mode=OneWay}" />
                        </Border>
                        <Border
                            Grid.Column="3"
                            Margin="0,0,0,0"
                            CornerRadius="6">
                            <ItemsRepeater ItemTemplate="{StaticResource OperationBadgeTemplate}" ItemsSource="{x:Bind Badges}">
                                <ItemsRepeater.Layout>
                                    <StackLayout Orientation="Horizontal" Spacing="0" />
                                </ItemsRepeater.Layout>
                            </ItemsRepeater>
                        </Border>
                        <Button
                            Grid.Column="4"
                            Margin="6,0,1,0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            Click="{x:Bind ButtonClick}"
                            Content="{x:Bind ButtonText, Mode=OneWay}"
                            CornerRadius="6,0,0,6" />
                        <Button
                            Grid.Column="5"
                            Height="32"
                            Margin="0,0,0,0"
                            Padding="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Center"
                            BorderThickness="0"
                            CornerRadius="0,6,6,0"
                            Flyout="{x:Bind OpMenu}"
                            GotFocus="{x:Bind LoadMenu}"
                            PointerEntered="{x:Bind LoadMenu}">
                            <SymbolIcon Symbol="More" />
                        </Button>
                    </Grid>
                </ItemContainer>
            </DataTemplate>

        </ResourceDictionary>
    </UserControl.Resources>

    <NavigationView
        Name="NavView"
        Grid.Row="2"
        Margin="0,-4,0,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        x:FieldModifier="public"
        CompactModeThresholdWidth="800"
        CompactPaneLength="68"
        ExpandedModeThresholdWidth="1600"
        IsBackButtonVisible="Collapsed"
        IsPaneOpen="False"
        IsPaneToggleButtonVisible="False"
        IsSettingsVisible="False"
        OpenPaneLength="250"
        SelectionChanged="NavigationView_SelectionChanged">
        <NavigationView.Resources>
            <SolidColorBrush x:Key="NavigationViewContentBackground" Color="Transparent" />
            <Thickness x:Key="NavigationViewContentGridBorderThickness">0,0,0,0</Thickness>
            <Thickness x:Key="NavigationViewMinimalContentGridBorderThickness">0,0,0,0</Thickness>
            <Thickness x:Key="TopNavigationViewContentGridBorderThickness">0,0,0,0</Thickness>
        </NavigationView.Resources>

        <NavigationView.MenuItems>
            <controls1:CustomNavViewItem
                x:Name="DiscoverNavBtn"
                FontSize="16"
                FontWeight="SemiBold"
                GlyphIcon="&#xF6FA;"
                Tag="Discover"
                Text="Discover Packages" />

            <controls1:CustomNavViewItem
                x:Name="UpdatesNavBtn"
                FontSize="16"
                FontWeight="SemiBold"
                GlyphIcon="&#xE895;"
                Tag="Updates"
                Text="Software Updates">
                <controls1:CustomNavViewItem.InfoBadge>
                    <InfoBadge
                        Name="UpdatesBadge"
                        Visibility="Collapsed"
                        Value="0" />
                </controls1:CustomNavViewItem.InfoBadge>
            </controls1:CustomNavViewItem>
            <controls1:CustomNavViewItem
                x:Name="InstalledNavBtn"
                FontSize="16"
                FontWeight="SemiBold"
                GlyphIcon="&#xE977;"
                Tag="Installed"
                Text="Installed Packages" />
            <controls1:CustomNavViewItem
                x:Name="BundlesNavBtn"
                FontSize="16"
                FontWeight="SemiBold"
                GlyphIcon="&#xF133;"
                Tag="Bundles"
                Text="Package Bundles">
                <controls1:CustomNavViewItem.InfoBadge>
                    <InfoBadge
                        x:Name="BundlesBadge"
                        Width="16"
                        Height="16"
                        Background="{ThemeResource SystemFillColorCautionBrush}"
                        Visibility="Collapsed">
                        <InfoBadge.IconSource>
                            <FontIconSource Glyph="&#xEDB1;" />
                        </InfoBadge.IconSource>
                    </InfoBadge>
                </controls1:CustomNavViewItem.InfoBadge>
            </controls1:CustomNavViewItem>
        </NavigationView.MenuItems>
        <NavigationView.FooterMenuItems>
            <controls1:CustomNavViewItem
                x:Name="SettingsNavBtn"
                IconSize="20"
                Tag="Settings"
                Text="Settings">
                <controls1:CustomNavViewItem.Icon>
                    <AnimatedIcon>
                        <AnimatedIcon.Source>
                            <animatedvisuals:AnimatedSettingsVisualSource />
                        </AnimatedIcon.Source>
                        <AnimatedIcon.FallbackIconSource>
                            <FontIconSource Glyph="&#xE713;" />
                        </AnimatedIcon.FallbackIconSource>
                    </AnimatedIcon>
                </controls1:CustomNavViewItem.Icon>
            </controls1:CustomNavViewItem>
            <controls1:CustomNavViewItem
                x:Name="ManagersNavBtn"
                GlyphIcon="&#xE835;"
                IconSize="20"
                Tag="Managers"
                Text="Package managers" />
            <controls1:CustomNavViewItem
                x:Name="MoreNavBtn"
                Icon="More"
                IconSize="20"
                Tag="Null"
                Tapped="MoreNavBtn_Tapped"
                Text="More" />
        </NavigationView.FooterMenuItems>

        <Grid
            Name="ContentGrid"
            Grid.Row="0"
            Grid.Column="1"
            Margin="8,8,8,8"
            RowSpacing="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="16" />
                <RowDefinition
                    Height="Auto"
                    MinHeight="0"
                    MaxHeight="200" />
            </Grid.RowDefinitions>
            <Frame
                Name="ContentFrame"
                Grid.Row="0"
                Grid.Column="0" />

            <controls:GridSplitter
                x:Name="OperationSplitter"
                Grid.Row="1"
                Grid.Column="0"
                Height="12"
                Margin="1,0,1,0"
                Padding="0,0,0,0"
                CornerRadius="4"
                Orientation="Horizontal" />
            <Grid Grid.Row="1" Grid.Column="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="15" />
                </Grid.ColumnDefinitions>
                <HyperlinkButton
                    Name="ExpandCollapseOpList"
                    Grid.Column="1"
                    Height="12"
                    Padding="12,0,12,0"
                    Click="ExpandCollapseOpList_Click"
                    CornerRadius="4"
                    Foreground="{ThemeResource ScrollBarButtonPointerOverBackgroundThemeBrush}">
                    <FontIcon FontSize="14" Glyph="&#xE96E;" />
                </HyperlinkButton>
                <HyperlinkButton
                    Name="OperationSplitterMenuButton"
                    Grid.Column="2"
                    Height="12"
                    Padding="12,0,12,0"
                    Click="OperationSplitterMenuButton_Click"
                    CornerRadius="4"
                    Foreground="{ThemeResource ScrollBarButtonPointerOverBackgroundThemeBrush}">
                    <FontIcon FontSize="24" Glyph="&#xE712;" />
                </HyperlinkButton>
            </Grid>
            <ListView
                Name="OperationList"
                Grid.Row="2"
                Grid.Column="0"
                x:FieldModifier="public"
                ItemTemplate="{StaticResource OperationTemplate}"
                SelectionMode="None"
                SizeChanged="OperationScrollView_SizeChanged">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel
                            Margin="-16,0,-12,0"
                            Orientation="Vertical"
                            Spacing="8" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ListView.Resources>
                    <SolidColorBrush x:Key="ItemContainerPointerOverBackground" Color="Transparent" />
                    <SolidColorBrush x:Key="ItemContainerPressedBackground" Color="Transparent" />
                </ListView.Resources>
            </ListView>
        </Grid>

    </NavigationView>
</UserControl>
