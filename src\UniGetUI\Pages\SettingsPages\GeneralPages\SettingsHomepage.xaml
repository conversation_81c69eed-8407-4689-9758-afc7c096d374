<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.SettingsHomepage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    NavigationCacheMode="Required"
    mc:Ignorable="d">

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel Name="SettingsEntries" Orientation="Vertical">
            <Border
                Name="AnnouncerBorder"
                Padding="24,16,24,16"
                Background="{ThemeResource ButtonBackground}"
                CornerRadius="8">
                <widgets:Announcer x:Name="SettingsAnnouncer" Url="https://marticliment.com/resources/annoucements/unigetui" />
            </Border>

            <UserControl Height="16" />

            <widgets:SettingsPageButton
                x:Name="GeneralSettingsExpander"
                Click="General"
                Icon="settings"
                Text="General preferences"
                UnderText="Language, theme and other miscellaneous preferences" />

            <UserControl Height="16" />

            <widgets:SettingsPageButton
                x:Name="InterfaceSettingsExpander"
                Click="Interface"
                CornerRadius="8,8,0,0"
                Icon="interactive"
                Text="User interface preferences"
                UnderText="Application theme, startup page, package icons, clear successful installs automatically" />

            <widgets:SettingsPageButton
                x:Name="NotificationSettingsEntry"
                BorderThickness="1,0,1,1"
                Click="Notifications"
                CornerRadius="0,0,8,8"
                Icon="megaphone"
                Text="Notification preferences"
                UnderText="Show notifications on different events" />

            <UserControl Height="16" />
            <widgets:SettingsPageButton
                x:Name="AdminSettingsExpander"
                Click="Administrator"
                CornerRadius="8"
                Icon="uac"
                Text="Administrator rights and other dangerous settings"
                UnderText="Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc." />

            <UserControl Height="16" />

            <widgets:SettingsPageButton
                x:Name="UpdatesOptionsExpander"
                Click="Operations"
                CornerRadius="8,8,0,0"
                Icon="Package"
                Text="Package operation preferences"
                UnderText="Change how UniGetUI handles install, update and uninstall operations." />

            <widgets:SettingsPageButton
                x:Name="StartupOptionsExpander"
                BorderThickness="1,0,1,1"
                Click="Startup"
                CornerRadius="0,0,8,8"
                Icon="update"
                Text="Package update preferences"
                UnderText="Update check frequency, automatically install updates, etc." />

            <UserControl Height="16" />

            <widgets:SettingsPageButton
                x:Name="BackupOptionsExpander"
                Click="Backup"
                CornerRadius="8"
                Icon="disk"
                Text="Package backup"
                UnderText="Automatically save a list of all your installed packages to easily restore them." />

            <UserControl Height="16" />

            <widgets:SettingsPageButton
                x:Name="ManagersSettingsShortcut"
                Click="ManagersShortcut"
                CornerRadius="8"
                Icon="Search"
                Text="Package manager preferences"
                UnderText="Enable and disable package managers, change default install options, etc.">
                <widgets:SettingsPageButton.HeaderIcon>
                    <FontIcon FontSize="12" Glyph="&#xE835;" />
                </widgets:SettingsPageButton.HeaderIcon>
            </widgets:SettingsPageButton>

            <UserControl Height="16" />

            <widgets:SettingsPageButton
                x:Name="InternetSettings"
                Click="Internet"
                CornerRadius="8,8,0,0"
                Icon="Search"
                Text="Internet connection settings"
                UnderText="Proxy settings, etc.">
                <widgets:SettingsPageButton.HeaderIcon>
                    <FontIcon FontSize="12" Glyph="&#xE839;" />
                </widgets:SettingsPageButton.HeaderIcon>
            </widgets:SettingsPageButton>

            <widgets:SettingsPageButton
                x:Name="ExperimentalSettingsExpander"
                BorderThickness="1,0,1,1"
                Click="Experimental"
                CornerRadius="0,0,8,8"
                Icon="experimental"
                Text="Experimental settings and developer options"
                UnderText="Beta features and other options that shouldn't be touched" />

        </StackPanel>
    </ScrollViewer>
</Page>
