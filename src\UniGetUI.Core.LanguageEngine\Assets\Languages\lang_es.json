{"\"{0}\" is a local package and can't be shared": "\"{0}\" es un paquete local y no puede compartirse", "\"{0}\" is a local package and does not have available details": "\"{0}\" es un paquete local y no tiene detalles disponibles", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" es un paquete local y no es compatible con esta característica", "(Last checked: {0})": "(Comprobado por última vez: {0})", "(Number {0} in the queue)": "(Número {0} en la cola)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@rubnium, @JMoreno97, @dalbitresb12, @marticliment, @apazga, @evaneliasyoung, @guplem, @uKER, @P10Designs", "0 packages found": "0 paquetes encontrados", "0 updates found": "0 actualizaciones encontradas", "1 - Errors": "1 - Errores", "1 day": "1 día", "1 hour": "1 hora", "1 month": "1 mes", "1 package was found": "Se encontró 1 paquete", "1 update is available": "Hay 1 actualización disponible", "1 week": "1 semana", "1 year": "1 año", "1. Navigate to the \"{0}\" or \"{1}\" page.": "Navega a la página \"{0}\" o \"{1}\".", "2 - Warnings": "2 - Ad<PERSON><PERSON><PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "Localiza el/los paquete(s) que quiere agregar al grupo y selecciona su casilla a la izquierda.", "3 - Information (less)": "3 - Información (menos)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "<PERSON>uando los paquetes que quieras agregar al grupo estén seleccionados, encuentra y clickea la opción \"{0}\" en la barra de herramientas.", "4 - Information (more)": "4 - Información (más)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "<PERSON><PERSON> paquetes se han añadido al grupo. Puedes seguir añadiendo paquetes o exportar el grupo.", "5 - information (debug)": "5 - Información (depuración)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Un gestor popular de librerías de C/C++. Lleno de liberías de C/C++ y otras utilidades relacionadas con C/C++<br>Contiene: <b>Librerías de C/C++ y utilidades relacionadas.</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Un repositorio lleno de herramientas y ejecutables diseñados con el ecosistema .NET de Microsoft en mente.<br>Contiene: <b>Herramientas y scripts relacionados con .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Un repositorio lleno de herramientas diseñadas con el ecosistema .NET de Microsoft en mente.<br>Contiene: <b>Herramientas relacionadas con .NET</b>", "A restart is required": "Es necesario reiniciar", "Abort install if pre-install command fails": "Cancelar la instalación si falla el comando previo.", "Abort uninstall if pre-uninstall command fails": "Cancelar la desinstalación si falla el comando previo.", "Abort update if pre-update command fails": "Cancelar la actualización si falla el comando previo.", "About": "Acerca de", "About Qt6": "Acerca de Qt6", "About WingetUI": "Acerca de UniGetUI", "About WingetUI version {0}": "Acerca de UniGetUI versión {0}", "About the dev": "Acerca del desarrollador", "Accept": "Aceptar", "Action when double-clicking packages, hide successful installations": "Acción al hacer doble clic en los paquetes, ocultar las instalaciones exitosas", "Add": "Agregar", "Add a source to {0}": "Agregar un origen a {0}", "Add a timestamp to the backup file names": "Añadir una marca de tiempo a los nombres de los archivos de copia de seguridad", "Add a timestamp to the backup files": "Añadir una marca de tiempo a los archivos de copia de seguridad", "Add packages or open an existing bundle": "<PERSON><PERSON><PERSON> p<PERSON> o abrir un grupo existente", "Add packages or open an existing package bundle": "<PERSON><PERSON><PERSON> paque<PERSON> o abrir un grupo de paquetes existente", "Add packages to bundle": "<PERSON><PERSON><PERSON> p<PERSON> al conjunto", "Add packages to start": "<PERSON><PERSON><PERSON> al<PERSON> paquetes para empezar", "Add selection to bundle": "Añadir la selección al grupo", "Add source": "<PERSON><PERSON><PERSON> origen", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Añadir las actualizaciones que fallen con \"no se encontró una actualización aplicable\" a la lista de actualizaciones ignoradas.", "Adding source {source}": "Añadiendo fuente {source}", "Adding source {source} to {manager}": "Agregando origen {source} a {manager}", "Addition succeeded": "Se agregó exitosamente", "Administrator privileges": "Privilegios de administrador", "Administrator privileges preferences": "Preferencias de privilegios de administrador", "Administrator rights": "Privilegios de administrador", "Administrator rights and other dangerous settings": "Permisos de administrador y otras configuraciones peligrosas.", "Advanced options": "Opciones avanzadas", "All files": "Todos los archivos", "All versions": "Todas las versiones", "Allow changing the paths for package manager executables": "<PERSON><PERSON><PERSON> cambiar las rutas de los ejecutables del gestor de paquetes.", "Allow custom command-line arguments": "Admitir argumentos de línea de comandos personalizados", "Allow importing custom command-line arguments when importing packages from a bundle": "Permitir importar argumentos de línea de comandos personalizados al importar paquetes de un grupo", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Permitir importar comandos personalizados antes y después de la instalación al importar paquetes desde un lote.", "Allow package operations to be performed in parallel": "Permitir que las operaciones se ejecuten en paralelo", "Allow parallel installs (NOT RECOMMENDED)": "Permitir instalaciones en paralelo (NO RECOMENDADO)", "Allow pre-release versions": "Permitir versiones preliminares.", "Allow {pm} operations to be performed in parallel": "Permitir que las operaciones de {pm} se ejecuten en paralelo", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativamente, también puedes instalar  {0} ejecutando el siguiente comando en una ventana de comandos de Windows Powershell:", "Always elevate {pm} installations by default": "Elevar siempre las instalaciones de {pm} por defecto", "Always run {pm} operations with administrator rights": "Siempre ejecutar las operaciones de {pm} con privilegios de administrador", "An error occurred": "Ocurrió un error", "An error occurred when adding the source: ": "Ocurrió un error al agregar el origen", "An error occurred when attempting to show the package with Id {0}": "Ocurrió un error al intentar mostrar el paquete con ID {0} ", "An error occurred when checking for updates: ": "Ocurrió un error al buscar actualizaciones:", "An error occurred while attempting to create an installation script:": null, "An error occurred while loading a backup: ": "Ocurrió un error al cargar una copia de seguridad: ", "An error occurred while logging in: ": "Ocurrió un error al iniciar sesión: ", "An error occurred while processing this package": "Ocurrió un error mientras se procesaba este paquete", "An error occurred:": "Ha ocurrido un error:", "An interal error occurred. Please view the log for further details.": "Se ha producido un error interno. Por favor, consulta el registro para obtener más detalles.", "An unexpected error occurred:": "Ocurrió un error inesperado:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Se ha producido un problema inesperado al intentar reparar WinGet. Por favor, inténtalo de nuevo más tarde", "An update was found!": "¡Se encontró una actualización!", "Android Subsystem": "Subsistema de Android", "Another source": "<PERSON><PERSON> origen", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Cualquier acceso directo nuevo creado durante una instalación o actualización se eliminará automáticamente, en lugar de mostrar un aviso de confirmación la primera vez que se detecte.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Cualquier acceso directo creado o modificado fuera de UniGetUI será ignorado. Podrás añadirlos manualmente usando el botón {0}.", "Any unsaved changes will be lost": "Cualquier cambió no guardado se perderá", "App Name": "Nombre de la Aplicación", "Appearance": "Apariencia", "Application theme, startup page, package icons, clear successful installs automatically": "Tema de la aplicación, página de inicio, iconos en los paquetes, quita las operaciones exitosas automáticamente", "Application theme:": "Tema de la aplicación:", "Apply": "Aplicar", "Architecture to install:": "Arquitectura a instalar:", "Are these screenshots wron or blurry?": "¿Estas capturas de pantalla son incorrectas o borrosas?", "Are you really sure you want to enable this feature?": "¿Estás seguro de que quieres habilitar esta característica?", "Are you sure you want to create a new package bundle? ": "¿Estás seguro de que quieres crear un nuevo grupo de paquetes?", "Are you sure you want to delete all shortcuts?": "¿Estás seguro de que quieres eliminar todos los accesos directos?", "Are you sure?": "¿Estás seguro?", "Ascendant": "Ascendente", "Ask for administrator privileges once for each batch of operations": "Pedir privilegios de administrador una vez por cada lote de operaciones", "Ask for administrator rights when required": "Solicitar privilegios de administrador cuando se requiera", "Ask once or always for administrator rights, elevate installations by default": "Pedir una vez o siempre permiso para ejecutar con privilegios de administrador, elevar las instalaciones por defecto.", "Ask only once for administrator privileges": "Solicitar privilegios de administrador solo una vez.", "Ask only once for administrator privileges (not recommended)": "Solicitar sólo una vez los privilegios de administrador (no recomendado)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Pregunta para eliminar accesos directos del escritorio creados durante la instalación o actualización", "Attention required": "Se requiere atención", "Authenticate to the proxy with an user and a password": "Autenticarse en el proxy con un usuario y una contraseña", "Author": "Autor", "Automatic desktop shortcut remover": "Eliminador del acceso directo del escritorio automático", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Salvar automáticamente una lista de todos tus paquetes instalados para fácilmente restaurarlos.", "Automatically save a list of your installed packages on your computer.": "Salvar automáticamente una lista de tus paquetes instalados en tu computador.", "Autostart WingetUI in the notifications area": "Iniciar UniGetUI automáticamente en el área de notificaciones", "Available Updates": "Actualizaciones disponibles", "Available updates: {0}": "Actualizaciones disponibles: {0}", "Available updates: {0}, not finished yet...": "Actualizaciones disponibles: {0}, no ha acabado todavía...", "Backing up packages to GitHub Gist...": "Haciendo copia de seguridad de paquetes en GitHub Gist...", "Backup": "<PERSON><PERSON><PERSON><PERSON>", "Backup Failed": "Error en la copia de seguridad.", "Backup Successful": "Copia de seguridad realizada con éxito.", "Backup and Restore": "Copia de seguridad y restauración.", "Backup installed packages": "<PERSON><PERSON><PERSON><PERSON> paquetes instalados", "Backup location": "Ubicación del respaldo", "Become a contributor": "Conviértete en contribuidor", "Become a translator": "Conviértete en traductor", "Begin the process to select a cloud backup and review which packages to restore": "Inicia el proceso para seleccionar una copia de seguridad en la nube y revisar qué paquetes restaurar.", "Beta features and other options that shouldn't be touched": "Funciones beta y otras opciones que no deberían tocarse", "Both": "Ambos", "Bundle security report": "Informe de seguridad del paquete.", "But here are other things you can do to learn about WingetUI even more:": "Pero aquí hay otras cosas que puedes hacer para aprender aún más de UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Desactivando un administrador de paquetes, no se mostrarán ni sus paquetes ni sus actualizaciones disponibles", "Cache administrator rights and elevate installers by default": "Almacenar privilegios de administrador en caché y elevar instaladores por defecto", "Cache administrator rights, but elevate installers only when required": "Almacenar privilegios de administrador en caché, pero elevar instaladores sólo cuando se requiera", "Cache was reset successfully!": "¡La caché se restableció con éxito!", "Can't {0} {1}": "No es posible {0} {1}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "Cancelar todas las operaciones", "Change backup output directory": "Cambiar carpeta de salida", "Change default options": "Cambiar opciones predeterminadas.", "Change how UniGetUI checks and installs available updates for your packages": "Cambia cómo UniGetUI comprueba e instala las actualizaciones de tus paquetes", "Change how UniGetUI handles install, update and uninstall operations.": "Cambiar cómo UniGetUI maneja las operaciones de instalación, actualización y desinstalación.", "Change how UniGetUI installs packages, and checks and installs available updates": "Cambia cómo UniGetUI instala paquetes y comprueba y instala las actualizaciones disponibles", "Change how operations request administrator rights": "Cambiar cómo las operaciones solicitan derechos de administrador.", "Change install location": "Cambiar ubicación para la instalación", "Change this": "Cambiar esto.", "Change this and unlock": "Cambiar esto y desbloquear.", "Check for package updates periodically": "Comprobar actualizaciones de paquetes periódicamente", "Check for updates": "Buscar actualizaciones", "Check for updates every:": "Buscar actualizaciones cada:", "Check for updates periodically": "Comprobar actualizaciones periódicamente.", "Check for updates regularly, and ask me what to do when updates are found.": "Buscar actualizaciones regularmente y preguntarme qué hacer cuando se encuentren.", "Check for updates regularly, and automatically install available ones.": "Comprobar actualizaciones diariamente, e instalar automáticamente las disponibles.", "Check out my {0} and my {1}!": "¡Echa un vistazo a mi {0} y a mi {1}!", "Check out some WingetUI overviews": "Échales un vistazo a algunos análisis de UniGetUI", "Checking for other running instances...": "Comprobando otras instancias en ejecución...", "Checking for updates...": "Buscando actualizaciones...", "Checking found instace(s)...": "Comprobando la(s) instancia(s) encontrada(s)...", "Choose how many operations shouls be performed in parallel": "Selecciona cuántas operaciones deben ejecutarse en paralelo", "Clear cache": "<PERSON><PERSON><PERSON> caché", "Clear finished operations": "Limpiar operaciones finalizadas.", "Clear selection": "Limpiar la selección", "Clear successful operations": "Quita las operaciones exitosas", "Clear successful operations from the operation list after a 5 second delay": "Eliminar las operaciones exitosas de la lista de operaciones después de 5 segundos", "Clear the local icon cache": "Borrar el caché de iconos local", "Clearing Scoop cache - WingetUI": "Limpiando caché de <PERSON> - UniGetUI", "Clearing Scoop cache...": "Limpiando caché de <PERSON>...", "Click here for more details": "Haga click aquí para más detalles", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Haz click en Instalar para iniciar el proceso de instalación. Si te saltas el proceso de instalación, puede que UniGetUI no funcione como se espera", "Close": "<PERSON><PERSON><PERSON>", "Close UniGetUI to the system tray": "Cerrar UniGetUI a la bandeja del sistema", "Close WingetUI to the notification area": "Cerrar UniGetUI al área de notificaciones", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "La copia de seguridad en la nube usa un Gist privado de GitHub para almacenar la lista de paquetes instalados.", "Cloud package backup": "Copia de seguridad de paquetes en la nube.", "Command-line Output": "Salida de Línea de Comandos", "Command-line to run:": "Línea de comandos a ejecutar:", "Compare query against": "Comparar consulta contra", "Compatible with authentication": "Compatible con autenticación", "Compatible with proxy": "Compatible con proxy", "Component Information": "Información del Componente", "Concurrency and execution": "Concurrencia y ejecución", "Connect the internet using a custom proxy": "Conectar a internet usando un proxy personalizado", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Contribuir al repositorio de íconos y capturas de pantalla", "Contributors": "Contribuidores", "Copy": "Copiar", "Copy to clipboard": "Copiar al portapapeles", "Could not add source": "No se ha podido añadir la fuente", "Could not add source {source} to {manager}": "No se pudo agregar el origen {source} a {manager}", "Could not back up packages to GitHub Gist: ": "No se pudieron guardar los paquetes en GitHub Gist: ", "Could not create bundle": "No se pudo crear el grupo de paquetes", "Could not load announcements - ": "No se pudieron cargar los anuncios -", "Could not load announcements - HTTP status code is $CODE": "No se pudieron cargar los anuncios - El código de estado de HTTP es $CODE", "Could not remove source": "No se ha podido eliminar la fuente", "Could not remove source {source} from {manager}": "No se pudo eliminar el origen {source} de {manager}", "Could not remove {source} from {manager}": "No se pudo eliminar {source} de {manager}", "Create .ps1 script": null, "Credentials": "Credenciales", "Current Version": "Versión actual", "Current status: Not logged in": "Estado actual: No has iniciado sesión.", "Current user": "Usuario actual", "Custom arguments:": "Argumentos personalizados:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Los argumentos de línea de comandos personalizados puede cambia la forma en que los programas se instalan, actualizan o desinstalan, de una forma que UniGetUI no puede controlar. Usar líneas de comandos personalizadas puede romper paquetes. Procede con precaución.", "Custom command-line arguments:": "Argumentos de línea de comandos personalizados", "Custom install arguments:": "Argumentos de instalación personalizados:", "Custom uninstall arguments:": "Argumentos personalizados para desinstalación:", "Custom update arguments:": "Argumentos personalizados para actualización:", "Customize WingetUI - for hackers and advanced users only": "Personalizar UniGetUI - solo para hackers y usuarios avanzados", "DEBUG BUILD": "COMPILACIÓN DE DEPURACIÓN", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ADVERTENCIA: NO NOS H<PERSON>EMOS RESPONSABLES DE LOS PAQUETES DESCARGADOS. POR FAVOR ASEGÚRATE DE INSTALAR SÓLO SOFTWARE CONFIADO.", "Dark": "Oscuro", "Decline": "Dec<PERSON><PERSON>", "Default": "Por defecto", "Default installation options for {0} packages": "Opciones predeterminadas de instalación para {0} paquetes.", "Default preferences - suitable for regular users": "Preferencias por defecto - aptas para usuarios regulares", "Default vcpkg triplet": "Tripleta predeterminada de vcpkg", "Delete?": "¿Eliminar?", "Dependencies:": "Dependencias:", "Descendant": "Descendente", "Description:": "Descripción:", "Desktop shortcut created": "Acceso directo de escritorio creado", "Details of the report:": "Detalles del informe:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Programar es difícil, y esta aplicación es gratuita. Pero si te gustó la aplicación, siempre puedes <b>comprarme un café</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Instalar directamente al hacer doble clic en un elemento de la pestaña \"{discoveryTab}\" (en lugar de mostrar la información del paquete)", "Disable new share API (port 7058)": "Desactivar la nueva API de compartición (puerto 7058)", "Disable the 1-minute timeout for package-related operations": "Desactiva el tiempo de espera de 1-minuto para operaciones de paquete relacionadas", "Disclaimer": "Descargo de responsabilidad", "Discover Packages": "<PERSON><PERSON><PERSON><PERSON>", "Discover packages": "<PERSON><PERSON><PERSON><PERSON> paque<PERSON>", "Distinguish between\nuppercase and lowercase": "Distinguir entre\nmayúsculas y minúsculas", "Distinguish between uppercase and lowercase": "Distinguir entre mayúsculas y minúsculas", "Do NOT check for updates": "NO buscar actualizaciones", "Do an interactive install for the selected packages": "Realizar una instalación interactiva para los paquetes seleccionados", "Do an interactive uninstall for the selected packages": "Realizar una desinstalación interactiva para los paquetes seleccionados", "Do an interactive update for the selected packages": "Realizar una actualización interactiva para los paquetes seleccionados", "Do not automatically install updates when the battery saver is on": "No instalar actualizaciones automáticamente cuando está activo el ahorro de batería", "Do not automatically install updates when the network connection is metered": "No instalar actualizaciones automáticamente cuando se esté en una conexión de red medida", "Do not download new app translations from GitHub automatically": "No descargar nuevas traducciones de la aplicación desde GitHub automáticamente", "Do not ignore updates for this package anymore": "No ignores actualizaciones de este paquete", "Do not remove successful operations from the list automatically": "No remover operaciones exitosas de la lista automáticamente", "Do not show this dialog again for {0}": "No volver a mostrar este diálogo para {0}", "Do not update package indexes on launch": "No actualizar los índices de paquetes al iniciar", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Aceptas que UniGetUI recoja y envie estadísticas anónimas de uso, con el único propósito de entender y mejorar la experiencia del usuario?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "¿Encuentras UniGetUI útil? Si puedes, tal vez quieras apoyar mi trabajo, para que pueda seguir haciendo a UniGetUI la interfaz definitiva de administración de paquetes.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "¿UniGetUI te parece útil? ¿Te gustaría apoyar al desarrollador? Si es así, puedes {0}. ¡Se agradece mucho!", "Do you really want to reset this list? This action cannot be reverted.": "¿Realmente quieres restablecer esta lista? Esta acción no puede revertirse.", "Do you really want to uninstall the following {0} packages?": "¿Realmente quiere desinstalar los siguientes {0} paque<PERSON>?", "Do you really want to uninstall {0} packages?": "¿Realmente quieres desinstalar {0} paque<PERSON>?", "Do you really want to uninstall {0}?": "¿Realmente quieres desinstalar {0}?", "Do you want to restart your computer now?": "¿Quieres reiniciar tu equipo ahora?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "¿Quieres traducir UniGetUI a tu idioma? Mira como contribuir <a style=\"color:{0}\" href=\"{1}\"a>AQUÍ</a>.", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "¿No quieres donar? No te preocupes. Siempre puedes compartir UniGetUI con tus amigos. Corre la voz acerca de UniGetUI.", "Donate": "Donar", "Done!": "¡Hecho!", "Download failed": "La descarga ha fallado", "Download installer": "<PERSON><PERSON><PERSON> instalador", "Download operations are not affected by this setting": "Las operaciones de descarga no se ven afectadas por esta configuración.", "Download selected installers": "Descargar instaladores seleccionados.", "Download succeeded": "<PERSON><PERSON><PERSON> exitosa", "Download updated language files from GitHub automatically": "Descargar archivos de idioma actualizados de GitHub automáticamente", "Downloading": "Descargando", "Downloading backup...": "Descargando copia de seguridad...", "Downloading installer for {package}": "Descargando el instalador para {package}", "Downloading package metadata...": "Descargando metadatos del paquete...", "Enable Scoop cleanup on launch": "Habilitar la limpieza de Scoop al iniciar", "Enable WingetUI notifications": "Activar las notificaciones de UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Habilitar una versión [experimental] mejorada del solucionador de problemas de WinGet", "Enable and disable package managers, change default install options, etc.": "Habilitar y deshabilitar gestores de paquetes, cambiar opciones de instalación predeterminadas, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Activar las optimizaciones de CPU en fondo (ver Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Habilitar API en segundo plano (Widgets de UniGetUI y Compartir, puerto 7058)", "Enable it to install packages from {pm}.": "Habilítelo para instalar paquetes de {pm}.", "Enable the automatic WinGet troubleshooter": "Activar el solucionador de problemas automático de WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Activar el nuevo elevador UAC marca UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Activar el nuevo manejador de entrada de procesos (cierre automático de StdIn).", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Activa las siguientes opciones solo si comprendes completamente lo que hacen y sus implicaciones.", "Enable {pm}": "Habilitar {pm}", "Enter proxy URL here": "Entre la URL del proxy aquí", "Entries that show in RED will be IMPORTED.": "Las entradas en ROJO serán IMPORTADAS.", "Entries that show in YELLOW will be IGNORED.": "Las entradas en AMARILLO serán IGNORADAS.", "Error": "Error", "Everything is up to date": "Todo está actualizado", "Exact match": "Coincidencia exacta", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Los accesos directos existentes de tu escritorio serán analizados, y necesitarás elegir cuáles mantener y cuáles eliminar.", "Expand version": "Expandir versión", "Experimental settings and developer options": "Configuraciones y opciones de desarrollador experimentales", "Export": "Exportar", "Export log as a file": "Exportar el registro como un archivo", "Export packages": "Exportar paquetes", "Export selected packages to a file": "Exportar los paquetes seleccionados a un archivo", "Export settings to a local file": "Exportar la configuración a un archivo local", "Export to a file": "Exportar a un archivo", "Failed": "Fallido.", "Fetching available backups...": "Buscando copias de seguridad disponibles...", "Fetching latest announcements, please wait...": "Obteniendo los últimos anuncios. Por favor espere...", "Filters": "<PERSON><PERSON><PERSON>", "Finish": "Finalizar", "Follow system color scheme": "Seguir el esquema de colores del sistema", "Follow the default options when installing, upgrading or uninstalling this package": "Seguir las opciones predeterminadas al instalar, actualizar o desinstalar este paquete.", "For security reasons, changing the executable file is disabled by default": "Por motivos de seguridad, cambiar el ejecutable está deshabilitado por defecto.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Por seguridad, los argumentos personalizados en la línea de comandos están desactivados. Ve a la configuración de seguridad de UniGetUI para cambiarlo. ", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Por seguridad, los scripts previos y posteriores a las operaciones están desactivados. Ve a la configuración de seguridad de UniGetUI para activarlos. ", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Utilizar la versión winget compilada para ARM (SOLO PARA SISTEMAS ARM64)", "Formerly known as WingetUI": "Antes conocido como WingetUI", "Found": "Encontrado", "Found packages: ": "Paquetes encontrados:", "Found packages: {0}": "Paquetes encontrados: {0}", "Found packages: {0}, not finished yet...": "Paquetes encontrados: {0}. No se ha terminado aún...", "General preferences": "Preferencias generales", "GitHub profile": "<PERSON><PERSON><PERSON> de GitHub", "Global": "Global", "Go to UniGetUI security settings": "Ir a la configuración de seguridad de UniGetUI.", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Gran repositorio lleno de utilidades desconocidas pero útiles y otros paquetes interesantes. <br>Contiene: <b>Utilidades, Programas de línea de comandos, Software general (se requiere el bucket de extras)</b>", "Great! You are on the latest version.": "¡Perfecto! Estás en la ultima versión.", "Grid": "Grilla", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Ayuda y documentación", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Aquí puedes cambiar cómo UniGetUI se comporta respecto a los siguientes accesos directos. Si marcas uno, UniGetUI lo eliminará si se crea en una futura actualización. Si lo desmarcas, se mantendrá intacto", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, mi nombre es Martí y soy el <i>desarrollador</i> de UniGetUI. ¡WingetUI fue hecho enteramente en mi tiempo libre!", "Hide details": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON>", "Homepage": "Sitio web", "Hooray! No updates were found.": "¡Hurra! ¡No se han encontrado actualizaciones!", "How should installations that require administrator privileges be treated?": "¿Cómo deberían tratarse las instalaciones que requieren privilegios de administrador?", "How to add packages to a bundle": "Cómo agregar paquetes a un grupo", "I understand": "Entiendo", "Icons": "Íconos", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Si tienes activada la copia en la nube, se guardará como un Gist de GitHub en esta cuenta.", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignorar comandos personalizados antes y después de instalar al importar desde un lote.", "Ignore future updates for this package": "Ignorar futuras actualizaciones de este paquete", "Ignore packages from {pm} when showing a notification about updates": "Ignorar los paquetes de {pm} al mostrar una notificación sobre actualizaciones", "Ignore selected packages": "Ignorar los paquetes seleccionados", "Ignore special characters": "Ignorar caracteres especiales", "Ignore updates for the selected packages": "Ignorar actualizaciones para los paquetes seleccionados", "Ignore updates for this package": "Ignorar actualizaciones para este paquete", "Ignored updates": "Actualizaciones ignoradas", "Ignored version": "Versión Ignorada", "Import": "Importar", "Import packages": "Importar paquetes", "Import packages from a file": "Importar paquetes desde un archivo", "Import settings from a local file": "Importar la configuración desde un archivo local", "In order to add packages to a bundle, you will need to: ": "Para agregar paquetes a este grupo, necesitarás:", "Initializing WingetUI...": "Inicializando UniGetUI...", "Install": "Instalar", "Install Scoop": "<PERSON><PERSON><PERSON>", "Install and more": "Instalar y más.", "Install and update preferences": "Preferencias de instalación y actualización", "Install as administrator": "Instalar como administrador", "Install available updates automatically": "Instala las actualizaciones disponibles automáticamente", "Install location can't be changed for {0} packages": "No se puede cambiar la ubicación de instalación para {0} paquetes.", "Install location:": "Ubicación de instalación:", "Install options": "Opciones de instalación.", "Install packages from a file": "In<PERSON>ar paquetes desde un archivo", "Install prerelease versions of UniGetUI": "Instalar versiones prerelease de UniGetUI", "Install script": null, "Install selected packages": "Instalar los paquetes seleccionados", "Install selected packages with administrator privileges": "Instalar los paquetes seleccionados con privilegios de administrador", "Install selection": "Instalar se<PERSON>", "Install the latest prerelease version": "Instalar la última versión preliminar", "Install updates automatically": "Instalar actualizaciones automáticamente", "Install {0}": "Instalar {0}", "Installation canceled by the user!": "¡Instalación cancelada por el usuario!", "Installation failed": "La instalación falló", "Installation options": "Opciones de instalación", "Installation scope:": "Entorno de instalación:", "Installation succeeded": "Instalación exitosa", "Installed Packages": "<PERSON><PERSON><PERSON>", "Installed Version": "Versión Instalada", "Installed packages": "Paquetes instalados", "Installer SHA256": "SHA256 del instalador", "Installer SHA512": "SHA512 del instalador", "Installer Type": "Tipo de instalador", "Installer URL": "URL del instalador", "Installer not available": "Instalador no disponible", "Instance {0} responded, quitting...": "La instancia {0} ha respondido, saliendo...", "Instant search": "Búsqueda instantánea", "Integrity checks can be disabled from the Experimental Settings": "Los chequeos de integridad se pueden deshabilitar desde las Configuraciones Experimentals.", "Integrity checks skipped": "Comprobaciones de integridad omitidas", "Integrity checks will not be performed during this operation": "No se hará ningún tipo de comprovación de integridad durante esta operación", "Interactive installation": "Instalación interactiva", "Interactive operation": "Operación interactiva", "Interactive uninstall": "Desinstalación interactiva", "Interactive update": "Actualización interactiva", "Internet connection settings": "Configuración de conexión a Internet", "Is this package missing the icon?": "¿A este paquete le falta el ícono?", "Is your language missing or incomplete?": "¿Falta tu idioma o está incompleto?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "No se garantiza que las credenciales provistas se almacenen seguramente, por lo que tal vez prefiera no usar las credenciales de su cuenta bancaria", "It is recommended to restart UniGetUI after WinGet has been repaired": "Se recomienda reiniciar UniGetUI después de que WinGet haya sido reparado", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Se recomienda encarecidamente reinstalar UniGetUI para solucionar la situación.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Parece que WinGet no está funcionando correctamente. ¿Quieres intentar reparar WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON><PERSON> que has ejecutado UniGetUI como administrador, lo cual no es recomendado. <PERSON><PERSON><PERSON> seguir usando el programa, pero recomendamos fuertemente no ejecutar UniGetUI como administrador. Haz click en \"{showDetails}\" para ver el porqué.", "Language": "Idioma", "Language, theme and other miscellaneous preferences": "I<PERSON>ma, tema y otras preferencias varias", "Last updated:": "Actualizado por última vez:", "Latest": "Última", "Latest Version": "Última Versión", "Latest Version:": "Última Versión:", "Latest details...": "Últimos detalles...", "Launching subprocess...": "Iniciando subproceso...", "Leave empty for default": "Dejar vacío por defecto", "License": "Licencia", "Licenses": "Licencias", "Light": "<PERSON><PERSON><PERSON>", "List": "Lista", "Live command-line output": "Salida de línea de comandos en tiempo real", "Live output": "Salida en tiempo real", "Loading UI components...": "Cargando componentes de la interfaz...", "Loading WingetUI...": "Cargando UniGetUI...", "Loading packages": "<PERSON><PERSON><PERSON> paque<PERSON>", "Loading packages, please wait...": "Cargando paquetes. Por favor espere...", "Loading...": "Cargando...", "Local": "Local", "Local PC": "PC Local", "Local backup advanced options": "Opciones avanzadas de copia de seguridad local.", "Local machine": "Equipo local", "Local package backup": "Copia de seguridad de paquetes local.", "Locating {pm}...": "Buscando {pm}...", "Log in": "Iniciar <PERSON>.", "Log in failed: ": "Error al iniciar sesión: ", "Log in to enable cloud backup": "Inicia sesión para habilitar la copia de seguridad en la nube.", "Log in with GitHub": "Inicia sesión con GitHub.", "Log in with GitHub to enable cloud package backup.": "Inicia sesión con GitHub para habilitar la copia de seguridad de paquetes en la nube.", "Log level:": "Nivel del registro:", "Log out": "<PERSON><PERSON><PERSON>.", "Log out failed: ": "Error al cerrar sesión: ", "Log out from GitHub": "<PERSON><PERSON><PERSON> GitHub.", "Looking for packages...": "<PERSON><PERSON><PERSON> paque<PERSON>...", "Machine | Global": "Máquina | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Argumentos de línea de comandos malformados pueden romper paquetes, o incluso permitir que un actor malicioso obtenga ejecución privilegiada. <PERSON><PERSON> tanto, importar líneas de comandos personalizadas está deshabilitado por defecto.", "Manage": "Administrar", "Manage UniGetUI settings": "Administrar la configuración de UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Administrar el comportamiento de inicio automático de UniGetUI desde la aplicación de Configuración", "Manage ignored packages": "Admini<PERSON><PERSON> paque<PERSON> ignorados", "Manage ignored updates": "Administrar actualizaciones ignoradas", "Manage shortcuts": "Gestionar atajos", "Manage telemetry settings": "Administrar las preferencias de la telemetria", "Manage {0} sources": "Admini<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> de {0}", "Manifest": "Manifiesto", "Manifests": "Manifiestos", "Manual scan": "Escaneo manual", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Administrador de paquetes oficial de Microsoft. Lleno de paquetes conocidos y verificados<br>Contiene: <b>Software en general, aplicaciones de Microsoft Store</b>", "Missing dependency": "Falta una dependencia", "More": "Más", "More details": "<PERSON><PERSON>", "More details about the shared data and how it will be processed": "Más detalles sobre qué datos se comparten y sobre cómo se procesa la información compartida", "More info": "Más información", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOTA: Este solucionador de problemas puede ser desactivado desde los ajustes de UniGetUI, en la sección de WinGet", "Name": "Nombre", "New": "Nuevo.", "New Version": "Versión Nueva", "New bundle": "Nuevo grupo", "New version": "Nueva versión", "Nice! Backups will be uploaded to a private gist on your account": "¡Genial! Las copias de seguridad se subirán como un Gist privado en tu cuenta.", "No": "No", "No applicable installer was found for the package {0}": "No se ha encontrado ningún instalador para el paquete {0}", "No dependencies specified": "No se especificaron dependencias.", "No new shortcuts were found during the scan.": "No se han encontrado nuevos atajos durante el escaneo.", "No packages found": "No se han encontrado paquetes", "No packages found matching the input criteria": "No se han encontrado paquetes con los criterios ingresados", "No packages have been added yet": "No se han agregado paquetes", "No packages selected": "No hay paquetes seleccionados", "No packages were found": "No se encontraron paquetes", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "No se recogen datos personales, y los datos enviados están anonimizados, de forma que no se pueden relacionar con ud.", "No results were found matching the input criteria": "No se encontraron resultados para el criterio ingresado", "No sources found": "No se encontraron orígenes", "No sources were found": "No se encontraron orígenes", "No updates are available": "No hay actualizaciones disponibles", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Administrador de paquetes de Node JS. Lleno de bibliotecas y otras utilidades del mundo de Javascript<br><PERSON>tien<PERSON>: <b>Bibliotecas de Javascript de Node y otras utilidades relacionadas</b>", "Not available": "No disponible", "Not finding the file you are looking for? Make sure it has been added to path.": "¿No encuentras el archivo? Asegúrate de que esté en la variable PATH.", "Not found": "No encontrado", "Not right now": "<PERSON>ora no", "Notes:": "Notas:", "Notification preferences": "Preferencias de las notificaciones", "Notification tray options": "Opciones de la bandeja de notificaciones", "Notification types": "Tipos de notificación", "NuPkg (zipped manifest)": "NuPkg (manifiesto comprimido con zip)", "OK": "Aceptar", "Ok": "Aceptar", "Open": "Abrir", "Open GitHub": "<PERSON><PERSON><PERSON>", "Open UniGetUI": "Abrir UniGetUI", "Open UniGetUI security settings": "Abrir configuración de seguridad de UniGetUI.", "Open WingetUI": "Abrir UniGetUI", "Open backup location": "Abrir ubicación de respaldo", "Open existing bundle": "Abrir grupo existente", "Open install location": "Abrir directorio de instalación", "Open the welcome wizard": "Abrir el asistente de bienvenida", "Operation canceled by user": "Operación cancelada por el usuario", "Operation cancelled": "Operación cancelada", "Operation history": "Historial de operaciones", "Operation in progress": "Operación en curso", "Operation on queue (position {0})...": "Operación en cola (posición {0})...", "Operation profile:": "Perfil de operación:", "Options saved": "Opciones guardadas", "Order by:": "Ordenar por:", "Other": "<PERSON><PERSON>", "Other settings": "Otras configuraciones", "Package": "<PERSON><PERSON><PERSON>", "Package Bundles": "Grupos de Paquetes", "Package ID": "ID de Paquete", "Package Manager": "Administrador <PERSON>", "Package Manager logs": "Registros del administrador de paquetes", "Package Managers": "Admin<PERSON>", "Package Name": "Nombre de Paquete", "Package backup": "<PERSON><PERSON><PERSON>", "Package backup settings": "Configuración de copia de seguridad del paquete.", "Package bundle": "Grupo de paquetes", "Package details": "Detalles del paquete", "Package lists": "Listas de paquetes", "Package management made easy": "Administración de paquetes hecha fácil", "Package manager": "Administrador <PERSON>", "Package manager preferences": "Preferencias de los gestores de paquetes", "Package managers": "Admin. de <PERSON>", "Package not found": "Paquete no encontrado", "Package operation preferences": "Preferencias de operaciones de paquetes", "Package update preferences": "Preferencias de actualización de paquetes", "Package {name} from {manager}": "<PERSON><PERSON><PERSON> {name} de {manager} ", "Package's default": "Predeterminado del paquete.", "Packages": "<PERSON><PERSON><PERSON>", "Packages found: {0}": "Paquetes encontrados: {0}", "Partially": "<PERSON><PERSON><PERSON><PERSON>", "Password": "Contraseña", "Paste a valid URL to the database": "Pegue una URL válida de la base de datos", "Pause updates for": "Pausar actualizaciones por", "Perform a backup now": "Hacer una copia de seguridad ahora", "Perform a cloud backup now": "Realizar copia de seguridad en la nube ahora.", "Perform a local backup now": "Realizar copia de seguridad local ahora.", "Perform integrity checks at startup": "Llevar a cabo chequeos de integridad al inicio", "Performing backup, please wait...": "Haciendo copia de seguridad. Por favor espere...", "Periodically perform a backup of the installed packages": "Hacer periódicamente una copia de seguridad de los paquetes instalados", "Periodically perform a cloud backup of the installed packages": "Realizar periódicamente copia de seguridad en la nube de los paquetes instalados.", "Periodically perform a local backup of the installed packages": "Realizar periódicamente copia de seguridad local de los paquetes instalados.", "Please check the installation options for this package and try again": "Por favor, verifique las opciones de instalación de este paquete e inténtelo de nuevo", "Please click on \"Continue\" to continue": "Seleccione \"Continuar\" para continuar", "Please enter at least 3 characters": "Por favor ingrese al menos 3 caracteres", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Por favor ten en cuenta que algunos paquetes pueden no ser instalables, debido a los administradores de paquetes habilitados en este sistema.", "Please note that not all package managers may fully support this feature": "Por favor note que no todos los administradores de paquetes soportan esta característica", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Por favor ten en cuenta que los paquetes de determinadas fuentes pueden no ser exportables. Se han marcado en gris y no se exportarán.", "Please run UniGetUI as a regular user and try again.": "Por favor, ejecuta UniGetUI como un usuario normal e inténtelo de nuevo", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Por favor vea la Salida de Línea de Comandos o diríjase a la Historia de Operaciones para más información sobre el problema.", "Please select how you want to configure WingetUI": "Por favor selecciona como quieres configurar UniGetUI", "Please try again later": "Por favor, inténtelo de nuevo después", "Please type at least two characters": "Por favor escribe al menos dos caracteres", "Please wait": "Por favor espere", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Por favor, espera mientras {0} se instala. <PERSON>uede que se abra una ventana negra. Por favor, espera hasta que se cierre.", "Please wait...": "Por favor espere...", "Portable": "Portable", "Portable mode": "<PERSON><PERSON>", "Post-install command:": "Comando posterior a la instalación:", "Post-uninstall command:": "Comando posterior a la desinstalación:", "Post-update command:": "Comando posterior a la actualización:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "El administrador de paquetes de PowerShell. Encuentre librerías y scripts para expandir las capacidades de PowerShell<br><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Los comandos pre y post instalación puede hacer cosas muy feas a tu dispositivo, si se las diseña para eso. Puede ser muy peligroso importar los comandos de un grupo, a menos que confíe en el origen de ese grupo de paquetes.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Los comandos pre y post instalación se ejecutarán antes y después de que un paquete se instale, actualice o desinstale. Tenga presente que pueden romper cosas a menos que se los use con cuidado", "Pre-install command:": "Comando previo a la instalación:", "Pre-uninstall command:": "Comando previo a la desinstalación:", "Pre-update command:": "Comando previo a la actualización:", "PreRelease": "PreLanzamiento", "Preparing packages, please wait...": "Preparando paque<PERSON>. Por favor espere...", "Proceed at your own risk.": "Proceda bajo su responsabilidad", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Prohibir cualquier tipo de elevación mediante UniGetUI Elevator o GSudo.", "Proxy URL": "URL del proxy", "Proxy compatibility table": "Tabla de compatibilidad de proxy", "Proxy settings": "Configuración de proxy", "Proxy settings, etc.": "Configuración de proxy, etc.", "Publication date:": "Fecha de publicación:", "Publisher": "Publicador", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Administrador de librerías de Python. Lleno de bibliotecas python y otras utilidades relacionadas con python.<br>Contiene: <b>Librerías python y utilidades relacionadas<b>", "Quit": "Salir", "Quit WingetUI": "Salir de UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Reducir avisos de UAC, elevar instalaciones por defecto, desbloquear funciones peligrosas, etc.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Refiérase a los Registros de UniGetUI para tener más detalles acerca de el/los archivos afectado(s)", "Reinstall": "Reins<PERSON>ar", "Reinstall package": "<PERSON><PERSON><PERSON><PERSON> paquete", "Related settings": "Configuraciones relacionadas", "Release notes": "Notas de liberación", "Release notes URL": "URL de notas de lanzamiento", "Release notes URL:": "URL de las notas de publicación:", "Release notes:": "Notas de publicación:", "Reload": "Recargar", "Reload log": "Recargar el registro", "Removal failed": "Falló la eliminación", "Removal succeeded": "Eliminación exitosa", "Remove from list": "Eliminar de la lista", "Remove permanent data": "<PERSON><PERSON> da<PERSON>", "Remove selection from bundle": "Eliminar selección de grupo", "Remove successful installs/uninstalls/updates from the installation list": "Quitar las instalaciones/desinstalaciones/actualizaciones exitosas de la lista de instalaciones", "Removing source {source}": "Eliminando la fuente {source}", "Removing source {source} from {manager}": "Eliminando origen {source} from {manager}", "Repair UniGetUI": "Reparar UniGetUI", "Repair WinGet": "<PERSON><PERSON><PERSON>", "Report an issue or submit a feature request": "Reportar un problema o enviar una solicitud de característica", "Repository": "Repositorio", "Reset": "Restablecer", "Reset Scoop's global app cache": "Restablecer la caché global de aplicaciones de Scoop", "Reset UniGetUI": "Reiniciar UniGetUI", "Reset WinGet": "Restablecer WinGet", "Reset Winget sources (might help if no packages are listed)": "Restablecer los orígenes de WinGet (puede ayudar si no se muestran paquetes)", "Reset WingetUI": "Restablecer UniGetUI", "Reset WingetUI and its preferences": "Restablecer UniGetUI y sus preferencias", "Reset WingetUI icon and screenshot cache": "Restablecer la caché de los íconos y capturas de pantalla de UniGetUI", "Reset list": "Reiniciar lista", "Resetting Winget sources - WingetUI": "Restableciendo los orígenes de WinGet - UniGetUI", "Restart": "Reiniciar", "Restart UniGetUI": "Reiniciar UniGetUI", "Restart WingetUI": "Reiniciar UniGetUI", "Restart WingetUI to fully apply changes": "Reiniciar UniGetUI para aplicar completamente los cambios", "Restart later": "Reiniciar más tarde", "Restart now": "<PERSON><PERSON><PERSON><PERSON> ahora", "Restart required": "Se requiere un reinicio", "Restart your PC to finish installation": "Reinicia tu PC para finalizar la instalación", "Restart your computer to finish the installation": "Reinicia tu equipo para finalizar la instalación", "Restore a backup from the cloud": "Restaurar copia de seguridad desde la nube.", "Restrictions on package managers": "Restricciones sobre los gestores de paquetes.", "Restrictions on package operations": "Restricciones sobre operaciones de paquetes.", "Restrictions when importing package bundles": "Restricciones al importar paquetes agrupados.", "Retry": "Reintentar", "Retry as administrator": "Intentar otra vez como administrador", "Retry failed operations": "Intentar otra vez las operaciones que han fallado", "Retry interactively": "Intentar otra vez de forma interactive", "Retry skipping integrity checks": "Intentar otra vez omitiendo las comprobaciones de integridad", "Retrying, please wait...": "Reintentando. Por favor espere...", "Return to top": "Volver arriba", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Run as admin": "Ejecutar como admin", "Run cleanup and clear cache": "Ejecutar limpieza y limpiar caché", "Run last": "Ejecutar la última", "Run next": "Ejecutar la siguiente", "Run now": "<PERSON><PERSON><PERSON><PERSON> ahora", "Running the installer...": "Corriendo el instalador...", "Running the uninstaller...": "Corriendo el desinstalador...", "Running the updater...": "Corriendo el instalador de actualización...", "Save": "Guardar", "Save File": "Guardar Archivo", "Save and close": "<PERSON><PERSON> y salir", "Save as": "Guardar como", "Save bundle as": "Guardar grupo como", "Save now": "Guardar ahora", "Saving packages, please wait...": "Sal<PERSON>do <PERSON> paquetes. Por favor espere...", "Scoop Installer - WingetUI": "Instalador de Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Desinstalador de Scoop - UniGetUI", "Scoop package": "<PERSON><PERSON><PERSON>", "Search": "Buscar", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Buscar aplicaciones, avisarme de actualizaciones cuando estén disponibles y no hacer cosas de nerds. No quiero que UniGetUI me complique demasiado, s<PERSON>lo quiero una simple <b>tienda de aplicaciones</b>", "Search for packages": "Buscar paquetes", "Search for packages to start": "Busque paquetes para empezar", "Search mode": "<PERSON><PERSON>", "Search on available updates": "Buscar en las actualizaciones encontradas", "Search on your software": "Buscar en tus programas", "Searching for installed packages...": "<PERSON><PERSON><PERSON> paquetes instalados...", "Searching for packages...": "<PERSON><PERSON><PERSON> paque<PERSON>...", "Searching for updates...": "Buscando actualizaciones...", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Seleccione \"{item}\" para añadir tu bucket personalizado", "Select a folder": "Se<PERSON>ccionar una carpeta", "Select all": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Select all packages": "Seleccionar todos los paquetes", "Select backup": "Seleccionar copia de seguridad.", "Select only <b>if you know what you are doing</b>.": "Seleccionar s<PERSON>lo <b>si sabes lo que estás haciendo</b>.", "Select package file": "Selecciona el archivo de paquetes", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Selecciona la copia de seguridad que quieres abrir. <PERSON><PERSON> pod<PERSON> revisar qué paquetes instalar.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Selecciona los procesos que deben cerrarse antes de instalar, actualizar o desinstalar este paquete.", "Select the source you want to add:": "Seleccione el origen que desea agregar:", "Select upgradable packages by default": "Seleccione los paquetes actualizables por defecto", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Selecciona qué <b>gestores de paquetes</b> usar ({0}), configura cómo se instalan los paquetes, gestiona cómo se manejan los privilegios de administrador, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Se ha enviado el handshake. Esperando respuesta de la instancia... ({0}%)", "Set a custom backup file name": "Establecer un nombre personalizado para el archivo de respaldo", "Set custom backup file name": "Establecer nombre personalizado para el respaldo", "Settings": "Configuración", "Share": "Compartir", "Share WingetUI": "Compartir UniGetUI", "Share anonymous usage data": "Compartir datos de uso anónimos", "Share this package": "Compartir este paquete", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Si modificas la configuración de seguridad, deberás abrir el paquete de nuevo para aplicar los cambios.", "Show UniGetUI on the system tray": "Mostrar UniGetUI en la bandeja del sistema", "Show UniGetUI's version and build number on the titlebar.": "Mostrar la versión de UniGetUI en la barra de título", "Show WingetUI": "Mostrar UniGetUI", "Show a notification when an installation fails": "Mostrar una notificación cuando una instalación falle", "Show a notification when an installation finishes successfully": "Mostrar una notificación cuando una instalación se complete exitosamente", "Show a notification when an operation fails": "Mostrar una notificación cuando una operación falla", "Show a notification when an operation finishes successfully": "Mostrar una notificación cuando una operación se completa exitosamente", "Show a notification when there are available updates": "Mostrar una notificación cuando haya actualizaciones disponibles", "Show a silent notification when an operation is running": "Mostrar una notificación silenciosa cuando una operación está en ejecución", "Show details": "<PERSON><PERSON> de<PERSON>les", "Show in explorer": "Mostrar en el explorador", "Show info about the package on the Updates tab": "Mostrar información del paquete en la pestaña de Actualizaciones", "Show missing translation strings": "Mostrar cadenas de traducción faltantes", "Show notifications on different events": "Muestra notificaciones en diferentes situaciones", "Show package details": "Mostrar detalles del paquete", "Show package icons on package lists": "Mostrar iconos de paquete en las listas de paquete", "Show similar packages": "Mostrar paquetes similares", "Show the live output": "Muestra la salida en tiempo real", "Size": "<PERSON><PERSON><PERSON>", "Skip": "Saltar", "Skip hash check": "Omitir comproba<PERSON> de hash", "Skip hash checks": "Saltar las verificaciones de hash", "Skip integrity checks": "Saltear chequeos de integridad", "Skip minor updates for this package": "Saltarse las actualizaciones menores para este paquete", "Skip the hash check when installing the selected packages": "Omitir la comprobación del hash al instalar los paquetes seleccionados", "Skip the hash check when updating the selected packages": "Omitir la comprobación del hash al actualizar los paquetes seleccionados", "Skip this version": "Omitir esta versión", "Software Updates": "Actualizaciones de Software", "Something went wrong": "Algo salió mal", "Something went wrong while launching the updater.": "Algo salió mal mientras se iniciaba el actualizador.", "Source": "Origen", "Source URL:": "URL de Fuente:", "Source added successfully": "Fuente añadida exitosamente", "Source addition failed": "Error al agregar la fuente", "Source name:": "Nombre de la fuente:", "Source removal failed": "Error al eliminar la fuente", "Source removed successfully": "Fuente eliminada exitosamente", "Source:": "Origen:", "Sources": "Orígenes", "Start": "Comenzar", "Starting daemons...": "Iniciando daemons...", "Starting operation...": "Empezando operación...", "Startup options": "Opciones de inicio", "Status": "Estado", "Stuck here? Skip initialization": "¿Atascado aquí? Saltar la inicialización", "Success!": null, "Suport the developer": "Apoyar al desarrollador", "Support me": "Apóyame", "Support the developer": "Apoya al desarrollador", "Systems are now ready to go!": "¡Los sistemas están ahora listos para empezar!", "Telemetry": "Telemetría", "Text": "Texto", "Text file": "Archivo de texto", "Thank you ❤": "<PERSON><PERSON><PERSON> ❤️", "Thank you 😉": "<PERSON><PERSON><PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "El administrador de paquetes de Rust. <br> <PERSON><PERSON><PERSON>: <b>Librerías de Rust y programas escritos en Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "El respaldo NO incluirá ningún archivo binario ni los datos guardados de ningún programa.", "The backup will be performed after login.": "El respaldo se llevará a cabo después de iniciar sesión.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "El respaldo incluirá la lista completa de los paquetes instalados y sus opciones de instalación. Las actualizaciones ignoradas y salteadas también se salvarán.", "The bundle was created successfully on {0}": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "El grupo de paquetes que intentas cargar parece ser inválido. Por favor, comprueba el archivo e inténtalo de nuevo.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "El hash del instalador no coincide con el valor esperado, por lo que no se puede garantizar la autenticidad del instalador. Si realmente confías en el publicador, {0} el paquete otra vez, omitiendo la comprobación del hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "El gestor de paquetes clásico para Windows. Encontrarás de todo ahí. <br><PERSON>tien<PERSON>: <b>Software general</b>", "The cloud backup completed successfully.": "La copia de seguridad en la nube se completó correctamente.", "The cloud backup has been loaded successfully.": "La copia de seguridad en la nube se ha cargado correctamente.", "The current bundle has no packages. Add some packages to get started": "El grupo actual no contiene paquetes. Añade algunos para comenzar", "The executable file for {0} was not found": "No se encontró el archivo ejecutable para {0}", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Las siguientes opciones se aplicarán por defecto cada vez que se instale, actualice o desinstale un paquete {0}.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Los siguientes paquetes van a ser exportados a un archivo JSON. No se guardarán datos de usuario ni binarios.", "The following packages are going to be installed on your system.": "Los siguientes paquetes van a ser instalados en tu sistema.", "The following settings may pose a security risk, hence they are disabled by default.": "Las siguientes configuraciones pueden suponer un riesgo de seguridad, por lo que están desactivadas por defecto.", "The following settings will be applied each time this package is installed, updated or removed.": "Las siguientes configuraciones se aplicarán cada vez que este paquete sea instalado, actualizado o removido.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Lsa siguientes configuraciones se aplicarán cada vez que este paquete se instale, actualice o elimine. Serán salvadas automáticamente.", "The icons and screenshots are maintained by users like you!": "¡Los íconos y las capturas de pantalla son mantenidos por usuarios como tú!", "The installation script saved to {0}": null, "The installer authenticity could not be verified.": "La autenticidad del instalador no se ha podido verificar", "The installer has an invalid checksum": "El instalador tiene un hash inválido", "The installer hash does not match the expected value.": "El hash del instalador no coincide con el valor esperado.", "The local icon cache currently takes {0} MB": "El caché de iconos local ocupa {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "El objetivo principal de este proyecto es proveer al usuario de una forma rápida de administrar los instaladores de paquetes de línea de comandos para Windows, como Winget o Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "No se encontró el paquete \"{0}\" en el administrador de paquetes \"{1}\"", "The package bundle could not be created due to an error.": "El grupo de paquetes no se pudo crear debido a error.", "The package bundle is not valid": "El grupo de paquetes no es válido", "The package manager \"{0}\" is disabled": "El administrador de paquetes \"{0}\" está desactivado", "The package manager \"{0}\" was not found": "No se encontró el administrador de paquetes \"{0}\"", "The package {0} from {1} was not found.": "El paquete {0} de {1} no se encontró.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Los paquetes listados aquí no se tendrán en cuenta cuando se compruebe si hay actualizaciones disponibles. Haga doble click en ellos o pulse el botón a su derecha para dejar de ignorar sus actualizaciones.", "The selected packages have been blacklisted": "Los paquetes seleccionados se han excluido", "The settings will list, in their descriptions, the potential security issues they may have.": "Las configuraciones indicarán, en su descripción, los posibles riesgos de seguridad que puedan tener.", "The size of the backup is estimated to be less than 1MB.": "El tamaño de este respaldo está estimado en menos de 1 MB.", "The source {source} was added to {manager} successfully": "El origen {source} se agregó a {manager} exitosamente.", "The source {source} was removed from {manager} successfully": "El origen {source} fue eliminado de {manager} exitosamente.", "The system tray icon must be enabled in order for notifications to work": "El ícono de la bandeja de sistema debe estar habilitado para que funcionen las notificaciones", "The update process has been aborted.": "El proceso de actualización ha sido abortado.", "The update process will start after closing UniGetUI": "El proceso de actualización comenzará cuando se cierre UniGetUI", "The update will be installed upon closing WingetUI": "La actualización se instalará al cerrar UniGetUI.", "The update will not continue.": "La actualización no continuará.", "The user has canceled {0}, that was a requirement for {1} to be run": "El usuario ha cancelado la {0}, que era un requerimiento para la ejecución de la {1}", "There are no new UniGetUI versions to be installed": "No hay nuevas versiones de UniGetUI para instalar", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Hay operaciones en curso. Salir de UniGetUI puede causar que fallen. ¿Quiere continuar?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Hay algunos videos geniales en YouTube que muestran UniGetUI y sus capacidades. ¡Podrías aprender trucos y consejos útiles!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Hay dos razones principales para no ejecutar UniGetUI como administrador:\nLa primera es que el gestor de paquetes Scoop puede causar problemas con algunos comandos si se ejecutan con privilegios de administrador.\nLa segunda es que ejecutar UniGetUI como administrador implica que cualquier paquete que descargues a través de UniGetUI se ejecutará también como administrador (y esto no es seguro).\nRecuerda que si necesitas instalar un paquete concreto como administrador, siempre puedes hacer clic derecho en él -> Instalar/Actualizar/Desinstalar como administrador.", "There is an error with the configuration of the package manager \"{0}\"": "Hay un error en la configuración del administrador de paquetes \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Hay una inatalación en progreso. Si cierras UniGetUI, la instalación podría fallar y tener resultados inesperados. ¿Todavía quieres cerrar UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Son los programas encargados de instalar, actualizar y eliminar paquetes.", "Third-party licenses": "Licencias de terceros", "This could represent a <b>security risk</b>.": "Esto podría representar un <b>riesgo de seguridad</b>.", "This is not recommended.": "Esto no se recomienda.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Esto probablemente se deba al hecho de que el paquete que te enviaron se eliminó o se publicó en un administrador de paquetes que no tienes habilitado. El ID recibido es {0}\n", "This is the <b>default choice</b>.": "Esta es la <b>opción por defecto</b>.", "This may help if WinGet packages are not shown": "Esto puede ayudar si no se muestran los paquetes de WinGet", "This may help if no packages are listed": "Esto puede ayudar si no se lista ningún paquete", "This may take a minute or two": "Esto puede tomar un minuto o dos", "This operation is running interactively.": "Esta operación se está ejecutando de forma interactiva.", "This operation is running with administrator privileges.": "Esta operación se está ejecutando con derechos de administrador.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Esta opción CAUSARÁ problemas. Cualquier operación que no pueda elevar privilegios FALLARÁ. Instalar/actualizar/desinstalar como administrador NO FUNCIONARÁ.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Este paquete tiene configuraciones potencialmente peligrosas que podrían ser ignoradas por defecto.", "This package can be updated": "<PERSON>ste paquete puede actualizarse", "This package can be updated to version {0}": "Este paquete puede actualizarse a la versión {0}.", "This package can be upgraded to version {0}": "Este paquete se puede actualizar a la versión \"{0}\"", "This package cannot be installed from an elevated context.": "Este paquete no ser puede instalar desde un contexto elevado", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "¿Este paquete no tiene capturas de pantalla o le falta el icono? Contribuye a UniGetUI agregando los iconos y capturas de pantalla que faltan a nuestra base de datos abierta y pública.", "This package is already installed": "Este paquete ya está instalado", "This package is being processed": "Este paquete está siendo procesado", "This package is not available": "Este paquete no está disponible", "This package is on the queue": "Este paquete está en la cola", "This process is running with administrator privileges": "Este proceso se está ejecutando con privilegios de administrador", "This project has no connection with the official {0} project — it's completely unofficial.": "Este proyecto no tiene conexión con el proyecto oficial de {0} — es completamente extraoficial.", "This setting is disabled": "Esta configuración está deshabilitada", "This wizard will help you configure and customize WingetUI!": "¡Este asistente te ayudará a configurar y personalizar UniGetUI!", "Toggle search filters pane": "Activar o desactivar el panel de filtros de búsqueda", "Translators": "Traductores", "Try to kill the processes that refuse to close when requested to": "Intentar cerrar los procesos que no responden.", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Activar esto permite cambiar el ejecutable usado para interactuar con gestores de paquetes. Aunque ofrece más personalización, puede ser peligroso.", "Type here the name and the URL of the source you want to add, separed by a space.": "Escribe aquí el nombre y la URL del origen que quieras añadir, separados por un espacio.", "Unable to find package": "No se pudo encontrar el paquete", "Unable to load informarion": "No se pudo cargar la información", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI recoge datos de uso anónimos con el único fin de mejorar la experiencia del usuario.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI recoge datos de uso anónimos con el único fin de entender y mejorar la experiencia del usuario.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI ha detectado un nuevo atajo de escritorio que se puede eliminar automaticamente", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI ha detectado los siguientes accesos directos en el  escritorio  que se pueden eliminar automáticamente en las siguientes actualizaciones", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI ha detectado {0} un nuevo atajo del escritorio que se puede eliminar automaticamente.", "UniGetUI is being updated...": "UniGetUI se está actualizando...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI no está relacionado con los administradores de paquetes compatibles. UniGetUI es un proyecto independiente.", "UniGetUI on the background and system tray": "UniGetUI en segundo plano y en la bandeja del sistema", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI o alguno de sus componentes faltan o están dañados", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI necesita {0} para funcionar correctamente, pero no se encontró en tu sistema.", "UniGetUI startup page:": "Página principal de UniGetUI:", "UniGetUI updater": "Actualizador de UniGetUI", "UniGetUI version {0} is being downloaded.": "La versión {0} de UniGetUI se está descargando", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} esta listo para ser instalado", "Uninstall": "<PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON> (y sus paquetes)", "Uninstall and more": "Desinstalar y más.", "Uninstall and remove data": "Desinstalar y eliminar datos", "Uninstall as administrator": "Desinstalar como administrador", "Uninstall canceled by the user!": "¡Desinstalación cancelada por el usuario!", "Uninstall failed": "La desinstalación falló", "Uninstall options": "Opciones de desinstalación.", "Uninstall package": "<PERSON><PERSON><PERSON><PERSON> paquete", "Uninstall package, then reinstall it": "<PERSON><PERSON><PERSON><PERSON> paque<PERSON>, y luego reinstalarlo", "Uninstall package, then update it": "<PERSON><PERSON><PERSON><PERSON> paque<PERSON>, y luego actualizarlo", "Uninstall previous versions when updated": "Desinstalar versiones anteriores al actualizar.", "Uninstall selected packages": "Desinstalar los paquetes seleccionados", "Uninstall selection": "Des<PERSON><PERSON><PERSON>", "Uninstall succeeded": "Desinstalación exitosa", "Uninstall the selected packages with administrator privileges": "Desinstalar los paquetes seleccionados con privilegios de administrador", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Los paquetes desinstalables que provienen de la fuente \"{0}\" no han sido publicados en ningún administrador de paquetes, por lo que no hay información disponible sobre ellos.", "Unknown": "Desconocido", "Unknown size": "Tamaño desconocido", "Unset or unknown": "No especificado o desconocido", "Up to date": "Ya está actualizado", "Update": "Actualizar", "Update WingetUI automatically": "Actualizar UniGetUI automáticamente", "Update all": "<PERSON>ual<PERSON><PERSON> todos", "Update and more": "Actualizar y más.", "Update as administrator": "Actualizar como administrador", "Update check frequency, automatically install updates, etc.": "Frecuencia del chequeo de actualizaciones, instalar actualizaciones automáticamente, etc.", "Update checking": null, "Update date": "Fecha de actualización", "Update failed": "La actualización falló", "Update found!": "Actualización disponible", "Update now": "<PERSON><PERSON><PERSON><PERSON>ora", "Update options": "Opciones de actualización.", "Update package indexes on launch": "Actualizar los índices de paquetes al inicio", "Update packages automatically": "Actualizar los paquetes automáticamente", "Update selected packages": "Actualizar los paquetes seleccionados", "Update selected packages with administrator privileges": "Actualizar los paquetes seleccionados con privilegios de administrador", "Update selection": "Actualizar <PERSON>", "Update succeeded": "Actualización exitosa", "Update to version {0}": "Actualizar a la versión \"{0}\"", "Update to {0} available": "Actualización a {0} disponible", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Actualizar automáticamente los portfiles de Git de vcpkg (requiere tener Git instalado)", "Updates": "Actualizaciones", "Updates available!": "¡Actualizaciones disponibles!", "Updates for this package are ignored": "Las actualizaciones de este paquete se ignoran", "Updates found!": "¡Actualizaciones disponibles!", "Updates preferences": "Preferencias de las actualizaciones", "Updating WingetUI": "Actualizando UniGetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Usar el WinGet legado incorporado en vez de los CMDLets de PowerShell", "Use a custom icon and screenshot database URL": "Usar una URL personalizada de base de datos de íconos y capturas de pantalla", "Use bundled WinGet instead of PowerShell CMDlets": "Usa el WinGet incorporado en vez de los CMDlets de PowerShell", "Use bundled WinGet instead of system WinGet": "Usar el WinGet incorporado en lugar del WinGet del sistema", "Use installed GSudo instead of UniGetUI Elevator": "Usar GSudo instalado en lugar de UniGetUI Elevator.", "Use installed GSudo instead of the bundled one": "Utilizar el GSudo instalado en lugar del incorporado", "Use system Chocolatey": "Usar el Chocolatey del sistema", "Use system Chocolatey (Needs a restart)": "Usar el Chocolatey del sistema (Requiere un reinicio)", "Use system Winget (Needs a restart)": "Utilizar el Winget del sistema (Requiere un reinicio)", "Use system Winget (System language must be set to english)": "Usar el WinGet del sistema (el idioma del sistema debe estar configurado en inglés)", "Use the WinGet COM API to fetch packages": "Usa la API COM de WinGet para cargar los paquetes", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Usar el Módulo de Powershell de WinGet en lugar de la API COM de WinGet", "Useful links": "Links útiles", "User": "Usuario", "User interface preferences": "Preferencias de interfaz de usuario", "User | Local": "Usuario | Local", "Username": "Nombre de usuario", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Usar UniGetUI implica la aceptación de la Licencia Pública Menor de GNU v2.1", "Using WingetUI implies the acceptation of the MIT License": "Usar UniGetUI implica la aceptación de la Licencia MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg root no se ha encontrado. Por favor, defina la variable de entorno %VCPKG_ROOT% o defínelo en la configuración de UniGetUI", "Vcpkg was not found on your system.": "Vcpkg no se ha encontrado en el sistema", "Verbose": "Verboso", "Version": "Versión", "Version to install:": "Versión a instalar:", "Version:": "Versión:", "View GitHub Profile": "<PERSON><PERSON> Perfil G<PERSON>", "View WingetUI on GitHub": "Ver UniGetUI en GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "er el código fuente de UniGetUI. Desde allí, puedes informar de errores, sugerir características o incluso contribuir directamente al proyecto UniGetUI.", "View mode:": "Modo de visualización:", "View on UniGetUI": "Ver en UniGetUI", "View page on browser": "Ver página en navegador", "View {0} logs": "Ver registros de {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Esperar a que el dispositivo esté conectado a internet antes de ejecutar qualquier tarea que requiera conexión a internet.", "Waiting for other installations to finish...": "Esperando que terminen otras instalaciones...", "Waiting for {0} to complete...": "Esperando a que se acabe la {0}...", "Warning": "Advertencia", "Warning!": "¡Atención!", "We are checking for updates.": "Estamos buscando atualizaciones", "We could not load detailed information about this package, because it was not found in any of your package sources": "No pudimos cargar información detallada sobre este paquete, porque no se lo encontró en ninguno de sus orígenes de paquetes.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "No pudimos cargar información detallada sobre este paquete, porque no fue instalado desde un administrador de paquetes disponible.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "No pudimos {action} {package}. Inténtelo más tarde. Haga click en \"{showDetails}\" para ver el registro del instalador.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "No pudimos {action} {package}. Inténtelo más tarde. Haga click en \"{showDetails}\" para ver el registro del desinstalador.", "We couldn't find any package": "No pudimos encontrar ning<PERSON> paquete", "Welcome to WingetUI": "Bienvenido a UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Al instalar paquetes en lote desde un paquete, tambi<PERSON> instalar los que ya están presentes.", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON>uando se <PERSON>an nuevos accesos directos, eliminarlos automáticamente en lugar de mostrar este diálogo.", "Which backup do you want to open?": "¿Qué copia de seguridad deseas abrir?", "Which package managers do you want to use?": "¿Qué gestores de paquetes deseas utilizar?", "Which source do you want to add?": "¿Qué orígenes deseas agregar?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Aunque WinGet puede utilizarse dentro de UniGetUI, UniGetUI también puede usarse con otros gestores de paquetes, lo que puede resultar confuso. En el pasado, UniGetUI se diseñó para funcionar solo con WinGet, pero esto ya no es así, por lo que UniGetUI ya no refleja lo que este proyecto aspira a ser.", "WinGet could not be repaired": "No se pudo reparar WinGet", "WinGet malfunction detected": "Malfunción de Winget detectada", "WinGet was repaired successfully": "Se reparó WinGet exitosamente", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "WingetUI - Todo está actualizado", "WingetUI - {0} updates are available": "UniGetUI - hay {0} actualizaciones disponibles", "WingetUI - {0} {1}": "UniGetUI - {1} de {0}", "WingetUI Homepage": "Página Oficial de UniGetUI", "WingetUI Homepage - Share this link!": "Página Oficial de UniGetUI - ¡Comparte este link!", "WingetUI License": "Licencia de UniGetUI", "WingetUI Log": "Registro de UniGetUI", "WingetUI Repository": "Repositorio de UniGetUI", "WingetUI Settings": "Configuración de UniGetUI", "WingetUI Settings File": "Archivo del configuración de WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI usa las siguientes librerías. Sin ellas, UniGetUI no habría sido posible.", "WingetUI Version {0}": "UniGetUI Versión {0}", "WingetUI autostart behaviour, application launch settings": "Comportamiento de inicio automático de UniGetUI, configuración de inicio de la aplicación", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI puede comprobar si tu software tiene actualizaciones disponibles, e instalarlas automáticamente si quieres", "WingetUI display language:": "Idioma de presentación de UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI ha sido ejecutado como administrador, lo cual no es recomendado. Al ejecutar UniGetUI como administrador, TODAS las operaciones lanzadas desde UniGetUI tendrán privilegios de administrador. Puede usar el programa de todas formas, pero recomendamos altamente no ejecutar UniGetUI con privilegios de administrador.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI ha sido traducido a más de 40 idiomas gracias a traductores voluntarios. Gracias 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI no ha sido traducido automáticamente. Los siguientes usuarios se han encargado de las traducciones:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI es una aplicación que hace administrar tu software más fácil, proporcionando una interfaz gráfica unificada para todos tus administradores de paquetes de línea de comandos.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI está siendo renombrado para enfatizar la diferencia entre WingetUI (la interfaz que estás usando en este momento) y Winget (un administrador de paquetes desarrollado por Microsoft con el cual no estoy relacionado)", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI está siendo actualizado. Al finalizar, WingetUI se reiniciará automáticamente", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI es gratis, y será gratis para siempre. Sin anuncios, sin tarjeta de crédito, sin versión premium. 100% gratis, para siempre.", "WingetUI log": "Registro de UniGetUI", "WingetUI tray application preferences": "Preferencias de la bandeja de notificaciones de UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI usa las siguientes librerías. Sin ellas, UniGetUI no habría sido posible.", "WingetUI version {0} is being downloaded.": "Se está descargando WingetUI versión {0}.", "WingetUI will become {newname} soon!": "¡UniGetUI se convertirá en {newname} pronto!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI no buscará actualizaciones periódicamente. Seguirán siendo comprobadas al iniciarse, pero no se te advertirá de ellas.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI mostrará un aviso de UAC cada vez que un paquete requiera elevación para instalarse.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI pronto se llamará {newname}. Esto no representa ningún cambio en la aplicación. Yo (el desarrollador) continuaré el desarrollo de este proyecto de la misma forma que ahora, pero bajo un nombre diferente.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI no sería posible sin la ayuda de nuestros queridos contribuidores. Revisa sus perfiles de GitHub ¡WingetUI no sería posible sin ellos!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI no habría sido posible sin la ayuda de los contribuidores. Gracias a todos 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} está listo para instalarse.", "Write here the process names here, separated by commas (,)": "Escribe aquí los nombres de procesos, separados por comas (,)", "Yes": "Sí", "You are logged in as {0} (@{1})": "Has iniciado sesión como {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Puedes cambiar este comportamiento en la configuración de seguridad de UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Puedes definir los comandos que se ejecutarán antes o después de instalar, actualizar o desinstalar este paquete. Se ejecutarán en una consola CMD, por lo que los scripts CMD funcionarán.", "You have currently version {0} installed": "Actualmente tienes instalada la versión {0}", "You have installed WingetUI Version {0}": "Tienes instalado UniGetUI versión {0}", "You may lose unsaved data": "Podrías perder datos no guardados.", "You may need to install {pm} in order to use it with WingetUI.": "Tal vez necesites instalar {pm} para usarlo con UniGetUI.", "You may restart your computer later if you wish": "<PERSON><PERSON><PERSON> reiniciar tu computadora más tarde si lo deseas", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "<PERSON><PERSON>lo se te preguntará una vez, y se concederán privilegios de administrador a los paquetes que lo soliciten.", "You will be prompted only once, and every future installation will be elevated automatically.": "Sólo se te preguntará una vez, y cada instalación futura se elevará automáticamente.", "You will likely need to interact with the installer.": "Es muy probable que sea necesario interactuar con el instalador", "[RAN AS ADMINISTRATOR]": "EJECUTADO COMO ADMINISTRADOR", "buy me a coffee": "comprarme un café", "extracted": "<PERSON><PERSON><PERSON>", "feature": "característica", "formerly WingetUI": "antes <PERSON>", "homepage": "sitio web", "install": "instalar", "installation": "instalación", "installed": "instalado", "installing": "instalando", "library": "librería", "mandatory": "obligatorio", "option": "opción", "optional": "opcional", "uninstall": "desinstalar", "uninstallation": "desinstalación", "uninstalled": "desinstalado", "uninstalling": "desinstalando", "update(noun)": "actualización", "update(verb)": "<PERSON><PERSON><PERSON>", "updated": "actualizado", "updating": "actualizando", "version {0}": "versión {0}", "{0} Install options are currently locked because {0} follows the default install options.": "Las opciones de instalación de {0} están bloqueadas porque {0} sigue las opciones predeterminadas.", "{0} Uninstallation": "Desinstalación de {0}", "{0} aborted": "{0} cancelada", "{0} can be updated": "{0} puede actualizarse", "{0} can be updated to version {1}": "{0} puede actualizarse a la versión {1}", "{0} days": "{0} días", "{0} desktop shortcuts created": "Se han creado {0} accesos directos en el escritorio", "{0} failed": "{0} ha fallado", "{0} has been installed successfully.": "{0} se instaló exitosamente.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} se instaló exitosamente. Se recomienda reiniciar UniGetUI para finalizar con la instalación", "{0} has failed, that was a requirement for {1} to be run": "La {0} ha fallado, y era un requerimiento para que se ejecutase la {1}", "{0} homepage": "Página oficial de {0}", "{0} hours": "{0} horas", "{0} installation": "Instalación de {0}", "{0} installation options": "Opciones de instalación de {0}", "{0} installer is being downloaded": "Se está descargando el instalador de {0}", "{0} is being installed": "{0} está siendo instalado", "{0} is being uninstalled": "{0} está siendo desinstalado", "{0} is being updated": "{0} se está actualizando", "{0} is being updated to version {1}": "{0} está siendo actualizado a la versión {1}", "{0} is disabled": "{0} <PERSON><PERSON> deshabilitado", "{0} minutes": "{0} minutos", "{0} months": "{0} meses", "{0} packages are being updated": "{0} paque<PERSON> estan siendo actualizados", "{0} packages can be updated": "{0} paquetes pueden ser actualizados", "{0} packages found": "{0} paque<PERSON> encontrados", "{0} packages were found": "Se han encontrado {0} paquetes", "{0} packages were found, {1} of which match the specified filters.": "Se encontraron {0} paque<PERSON>, {1} de los cuales coinciden con los filtros especificados.", "{0} selected": null, "{0} settings": "Configuración del {0}", "{0} status": "estado de {0}", "{0} succeeded": "{0} realizado correctamente", "{0} update": "Actualización de {0}", "{0} updates are available": "{0} paquetes disponibles", "{0} was {1} successfully!": "{0} se ha {1} correctamente.", "{0} weeks": "{0} semanas", "{0} years": "{0} a<PERSON>s", "{0} {1} failed": "{0} {1} ha fallado", "{package} Installation": "Instalación de {package}", "{package} Uninstall": "Desinstalación de {package}", "{package} Update": "Actualización de {package}", "{package} could not be installed": "{package} no se pudo instalar", "{package} could not be uninstalled": "{package} no se pudo desinstalar", "{package} could not be updated": "{package} no se pudo actualizar", "{package} installation failed": "Falló la instalación de {package}", "{package} installer could not be downloaded": "No se pudo descargar el instalador de {package} ", "{package} installer download": "Descarga del instalador de {package}", "{package} installer was downloaded successfully": "El instalador de {package} se ha descargado correctamente", "{package} uninstall failed": "Falló la desinstalación de {package}", "{package} update failed": "Falló la actualización de {package}", "{package} update failed. Click here for more details.": "La actualización de {package} falló. Haca click aquí por más detalles.", "{package} was installed successfully": "{package} se instaló correctamente", "{package} was uninstalled successfully": "{package} se desinstaló correctamente", "{package} was updated successfully": "{package} se actualizó correctamente", "{pcName} installed packages": "Paquetes instalados en {pcName}", "{pm} could not be found": "{pm} no se encontró", "{pm} found: {state}": "{pm} encontrado: {state}", "{pm} is disabled": "{pm} est<PERSON> deshabilitado", "{pm} is enabled and ready to go": "{pm} está habilitado y listo para usarse", "{pm} package manager specific preferences": "Preferencias específicas de administrador de paquetes {pm}", "{pm} preferences": "Preferencias de {pm}", "{pm} version:": "Versión de {pm}:", "{pm} was not found!": "¡Se encontró {pm}!"}