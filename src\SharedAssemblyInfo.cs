﻿using System.Reflection;
using System.Runtime.Versioning;

[assembly: AssemblyProduct("UniGetUI")]
[assembly: AssemblyDescription("UniGetUI")]
[assembly: AssemblyTitle("UniGetUI")]
[assembly: AssemblyDefaultAlias("UniGetUI")]
[assembly: AssemblyCopyright("2025, Martí <PERSON>")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyInformationalVersion("3.3.2")]
[assembly: SupportedOSPlatform("windows10.0.19041")]
