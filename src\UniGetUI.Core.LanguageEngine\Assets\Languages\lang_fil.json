{"\"{0}\" is a local package and can't be shared": "\"{0}\" ay isang lokal na package at hindi pwedeng ibahagi", "\"{0}\" is a local package and does not have available details": "\"{0}\" ay isang lokal na package at ay walang magagamit na mga detalye", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" ay isang lokal na package at hindi compatible sa feature na ito", "(Last checked: {0})": "(<PERSON><PERSON> sinuri: {0})", "(Number {0} in the queue)": "(Numero {0} sa queue)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@infyProductions", "0 packages found": "0 package ang nahanap", "0 updates found": "0 update ang nahanap", "1 - Errors": "1 - <PERSON><PERSON>rro<PERSON>", "1 day": "1 araw", "1 hour": "1 oras", "1 month": "1 buwan", "1 package was found": "1 package ang nahanap", "1 update is available": "1 update ang available", "1 week": "1 linggo", "1 year": "1 taon", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Mag-navigate sa \"{0}\" o \"{1}\" na pahina.", "2 - Warnings": "2 - <PERSON><PERSON>", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON> ang (mga) package na gusto mong idagdag sa bundle, at piliin ang kanilang pinakakaliwang checkbox.", "3 - Information (less)": "3 - <PERSON><PERSON><PERSON><PERSON><PERSON> (mas kaunti)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. Kapag napili ang mga package na gusto mong idagdag sa bundle, hanapin at i-click ang opsyong \"{0}\" sa toolbar.", "4 - Information (more)": "4 - <PERSON><PERSON><PERSON><PERSON><PERSON> (higit pa)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Ang iyong mga package ay naidagdag na sa bundle. <PERSON><PERSON><PERSON> kang magpatuloy sa pagdaragdag ng mga package, o i-export ang bundle.", "5 - information (debug)": "5 - <PERSON><PERSON><PERSON><PERSON><PERSON> (debug)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Isang sikat na C/C++ library manager. Puno ng mga library ng C/C++ at iba pang mga utility nauugnay sa C/C++<br>Naglalaman ng: <b>Mga library ng C/C++ at mga nauugnay na utility</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Isang repository na puno ng mga tool at executable na idinisenyo nang nasa isip ang .NET ecosystem ng Microsoft.<br>Naglalaman ng: <b>Mga tool at script na nauugnay sa .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Isang repository na puno ng mga tool na idinisenyo na nasa isip ang .NET ecosystem ng Microsoft.<br>Naglalaman ng: <b>Mga Tool na nauugnay sa .NET</b>", "A restart is required": "<PERSON><PERSON>kailangan ang pag-restart", "Abort install if pre-install command fails": "<PERSON><PERSON><PERSON> ang pag-install kapag ang command ng pre-install ay nabigo", "Abort uninstall if pre-uninstall command fails": "<PERSON><PERSON>to ang pag-uninstall kapag ang command ng pre-uninstall ay nabigo", "Abort update if pre-update command fails": "Ihinto ang pag-update kapag ang command ng pre-update ay nabigo", "About": "Patungkol", "About Qt6": "Tungkol sa QT6", "About WingetUI": "Tungkol sa UniGetUI", "About WingetUI version {0}": "Tungkol sa bersyon ng UniGetUI {0}", "About the dev": "Tungkol sa dev", "Accept": "Tanggapin", "Action when double-clicking packages, hide successful installations": "Pagkilos kapag nag-double click sa mga package, itago ang matagumpay na mga pag-install", "Add": "Idagdag", "Add a source to {0}": "Magdagdag ng source sa {0}", "Add a timestamp to the backup file names": "Magdagdag ng timestamp sa mga pangalan ng mga backup file", "Add a timestamp to the backup files": "Magdagdag ng timestamp sa mga backup na file", "Add packages or open an existing bundle": "Magdagdag ng mga package o magbukas ng umiiral nang bundle", "Add packages or open an existing package bundle": "Magdagdag ng mga package o magbukas ng umiiral nang package bundle", "Add packages to bundle": "Magdagdag ng mga package sa bundle", "Add packages to start": "Magdagdag ng mga package upang magsimula", "Add selection to bundle": "Magdagdag ng seleksyon sa bundle", "Add source": "Magdagdag ng source", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Magdagdag ng mga update na nabigo nang may 'walang nahanap na naaangkop na update' sa listahan ng mga hindi pinansin na update", "Adding source {source}": "Pagdaragdag ng source {source}", "Adding source {source} to {manager}": "Pagdaragdag ng source {source} sa {manager}", "Addition succeeded": "Nagtagumpay ang pagdaragdag", "Administrator privileges": "Mga pribilehiyo ng administrator", "Administrator privileges preferences": "Mga kagustuhan sa mga pribilehiyo ng administrator", "Administrator rights": "Mga karapatan ng administrator", "Administrator rights and other dangerous settings": "Mga karapatan ng administrator at iba pang mga mapanganib na setting", "Advanced options": "Mga advanced na opsyon", "All files": "Lahat ng mga file", "All versions": "Lahat ng bersyon", "Allow changing the paths for package manager executables": "Payagan ang pagpapalit ng mga path para sa mga executable ng package manager", "Allow custom command-line arguments": "Payagan ang mga custom na argumento sa command-line", "Allow importing custom command-line arguments when importing packages from a bundle": "Payagan ang pag-import ng mga custom na arguemento ng command-line kapag nag-i-import ng mga package mula sa bundle", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Payagan ang pag-import ng custom na pre-install at post-install na command kapag nag-i-import ng mga package mula sa bundle", "Allow package operations to be performed in parallel": "Payagan ang mga pagpapatakbo ng package na gumanap nang sabay-sabay", "Allow parallel installs (NOT RECOMMENDED)": "Payagan ang mga pagsabay-sabay na pag-install (HINDI INIREREKOMENDA)", "Allow pre-release versions": "Payagan ang mga pre-release na bersyon", "Allow {pm} operations to be performed in parallel": "Pahintulutan ang mga operasyon ng {pm} na gumanap nang sabay-sabay", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "<PERSON><PERSON><PERSON> ka<PERSON>, ma<PERSON>i mo ring i-install ang {0} sa pamamagitan ng pagpapatakbo ng sumusunod na command sa isang prompt ng Windows PowerShell:", "Always elevate {pm} installations by default": "Palaging i-elevate ang mga pag-install ng {pm} bilang default", "Always run {pm} operations with administrator rights": "Palaging magpatakbo ng {pm} na mga pagpapatakbo na may mga karapatan ng administrator", "An error occurred": "May naganap na error", "An error occurred when adding the source: ": "Nagkaroon ng error nang idagdag ang source:", "An error occurred when attempting to show the package with Id {0}": "Nagkaroon ng error noong sinusubukang ipakita ang package na may Id {0}", "An error occurred when checking for updates: ": "Nagkaroon ng error habang tumitingin ng mga update:", "An error occurred while attempting to create an installation script:": "May naganap na error habang sinusubukang ginagawa ang script ng pag-install:", "An error occurred while loading a backup: ": "Nagkaroon ng error habang niloload ang backup:", "An error occurred while logging in: ": "<PERSON><PERSON><PERSON> ng error habang naglalog-in:", "An error occurred while processing this package": "Nagkaroon ng error habang pinoproseso ang package na ito", "An error occurred:": "May naganap na error", "An interal error occurred. Please view the log for further details.": "May naganap na internal na error. Pakitingnan ang log para sa karagdagang detalye.", "An unexpected error occurred:": "May naganap na hindi in<PERSON><PERSON><PERSON> error:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Isang hindi inaa<PERSON>hang isyu ang naganap habang sinusubukang ayusin ang WinGet. Pakisubukang muli mamaya", "An update was found!": "May nahanap na update!", "Android Subsystem": null, "Another source": "Isa pang source", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Awtomatikong made-delete ang anumang mga bagong shortcut na ginawa sa panahon ng pag-install o pagpapatakbo ng pag-update, sa halip na magpakita ng prompt ng kumpirmasyon sa unang pagkakataong matukoy ang mga ito.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Ang anumang mga shortcut na ginawa o binago sa labas ng UniGetUI ay hindi papansinin. Magagawa mong idagdag ang mga ito sa pamamagitan ng button na {0}.", "Any unsaved changes will be lost": "<PERSON><PERSON><PERSON> ang anumang hindi na-save na pagbabago", "App Name": "Pangalan ng App", "Appearance": "<PERSON><PERSON>", "Application theme, startup page, package icons, clear successful installs automatically": "Tema ng aplikasyon, startup page, mga icon ng package, awtomatikong i-clear ang matagumpay na pag-install", "Application theme:": "Tema ng aplikasyon:", "Apply": "Ilapat", "Architecture to install:": "Arkitekturang i-install:", "Are these screenshots wron or blurry?": "Mali ba o malabo ang mga screenshot na ito?", "Are you really sure you want to enable this feature?": "<PERSON><PERSON><PERSON> ka bang gusto mong paganahin ang feature na ito?", "Are you sure you want to create a new package bundle? ": "Sigu<PERSON> ka bang gusto mong gumawa ng bagong package bundle?", "Are you sure you want to delete all shortcuts?": "Si<PERSON><PERSON> ka bang gusto mong tanggalin ang lahat ng mga shortcut?", "Are you sure?": "<PERSON><PERSON><PERSON> ka ba?", "Ascendant": "Ayos na pataas", "Ask for administrator privileges once for each batch of operations": "Humingi ng mga pribilehiyo ng administrator nang isang beses para sa bawat batch ng mga operasyon", "Ask for administrator rights when required": "Humingi ng mga karapatan ng administrator kung kinakailangan", "Ask once or always for administrator rights, elevate installations by default": "Humingi ng isang beses o palaging para sa mga karapatan ng administrator, i-elevate ang mga pag-install bilang default", "Ask only once for administrator privileges": "Isang beses lang humingi ng mga pribilehiyo ng administrator", "Ask only once for administrator privileges (not recommended)": "<PERSON>ng beses lang humingi ng mga pribilehiyo ng administrator (hindi inirerekomenda)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Hilingin na tanggalin ang mga desktop shortcut na ginawa sa panahon ng pag-install o pag-upgrade.", "Attention required": "Kailangan ng atensyon", "Authenticate to the proxy with an user and a password": "I-authenticate sa proxy gamit ang isang user at isang password", "Author": "May-akda", "Automatic desktop shortcut remover": "Awtomatikong pagtanggal ng desktop shortcut", "Automatic updates": null, "Automatically save a list of all your installed packages to easily restore them.": "Awtomatikong i-save ang isang listahan ng lahat ng iyong naka-install na mga package upang madaling maibalik ang mga ito.", "Automatically save a list of your installed packages on your computer.": "Awtomatikong i-save ang isang listahan ng iyong mga naka-install na package sa iyong computer.", "Autostart WingetUI in the notifications area": "Awtomatikong simulan ang UniGetUI sa lugar ng mga notification", "Available Updates": "Available na Mga Update", "Available updates: {0}": "Available na Mga Update: {0}", "Available updates: {0}, not finished yet...": "Available na Mga Update: {0}, hindi pa natatapos...", "Backing up packages to GitHub Gist...": "Bina-backup ang mga package sa GitHub Gist...", "Backup": "I-backup", "Backup Failed": "Nabigo ang <PERSON>-<PERSON>up", "Backup Successful": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>-<PERSON><PERSON>", "Backup and Restore": "Pag-backup at Pag-restore", "Backup installed packages": "I-backup ang mga naka-install na backup", "Backup location": "Lokasyon ng backup", "Become a contributor": "Maging isang kontribyutor", "Become a translator": "Maging isang tagasalin", "Begin the process to select a cloud backup and review which packages to restore": "Simulan ang proseso na pumili ng cloud backup at i-review anong package ang i-restore", "Beta features and other options that shouldn't be touched": "Mga beta feature at iba pang opsyon na hindi dapat pakialaman", "Both": "<PERSON><PERSON><PERSON>", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Ngunit narito ang iba pang mga bagay na maaari mong gawin upang matuto nang higit pa tungkol sa UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Sa pamamagitan ng pag-toggle sa isang manager ng package, hindi mo na makikita o maa-update ang mga package nito.", "Cache administrator rights and elevate installers by default": "I-cache ang mga karapatan ng administrator at i-elevate ang mga installer sa pamamagitan ng default", "Cache administrator rights, but elevate installers only when required": "Mga karapatan ng administrator ng cache, ngunit i-elevate lamang ang mga installer ka<PERSON>g kin<PERSON>n", "Cache was reset successfully!": "Matagumpay na na-reset ang cache!", "Can't {0} {1}": "Hindi maka {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": "<PERSON><PERSON><PERSON><PERSON> lahat ng operasyon", "Change backup output directory": "Baguhin ang backup na directory ng output", "Change default options": "Baguhin ang mga default na opsyon", "Change how UniGetUI checks and installs available updates for your packages": "Baguhin kung paano sinusuri at ini-install ng UniGetUI ang mga available na update para sa iyong mga package", "Change how UniGetUI handles install, update and uninstall operations.": "Baguhin kung paano pinangangasiwaan ng UniGetUI ang pag-install, pag-update at pag-uninstall ng mga operasyon.", "Change how UniGetUI installs packages, and checks and installs available updates": "Baguhin kung paano nag-i-install ang UniGetUI ng mga package, at sinusuri at ini-install ang mga available na update", "Change how operations request administrator rights": "Baguhin kung paano humihiling ang mga pagpapatakbo ng mga karapatan ng administrator", "Change install location": "Baguhin ang lokasyon ng pag-install", "Change this": "<PERSON><PERSON><PERSON> ito", "Change this and unlock": "<PERSON><PERSON><PERSON> ito at i-unlock", "Check for package updates periodically": "Suriin ang mga pag-update ng package sa pana-panahon", "Check for updates": "Tingnan ang mga update", "Check for updates every:": "Tingnan ang mga update kada:", "Check for updates periodically": "Suriin ang mga pag-update ng package sa pana-panahon.", "Check for updates regularly, and ask me what to do when updates are found.": "Regular na suriin ang mga update, at tanungin ako kung ano ang gagawin kapag may nakitang mga update.", "Check for updates regularly, and automatically install available ones.": "Regular na suriin ang mga update, at awtomatikong i-install kung available.", "Check out my {0} and my {1}!": "Tingnan ang aking {0} at ang aking {1}!", "Check out some WingetUI overviews": "Tingnan ang ilang mga review sa UniGetUI", "Checking for other running instances...": "Sinusuri ang iba pang tumatakbong mga instance...", "Checking for updates...": "Tumitingin ng mga update...", "Checking found instace(s)...": "Sin<PERSON>uri ang (mga) nahanap na instance...", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON><PERSON> kung gaano karaming mga operasyon ang dapat isagawa nang sabay-sabay", "Clear cache": "Linisin ang cache", "Clear finished operations": "I-clear ang natapos na mga operasyon", "Clear selection": "I-clear ang mga napili", "Clear successful operations": "I-clear ang mga nagtagumpay na operasyon", "Clear successful operations from the operation list after a 5 second delay": "Linisin ang matagumpay na operasyon mula sa listahan ng operasyon pagkatapos ng 5 segundong pagkaantala", "Clear the local icon cache": "Linisin ang cache ng lokal na icon", "Clearing Scoop cache - WingetUI": "Linilinis ang Scoop cache - UniGetUI", "Clearing Scoop cache...": "<PERSON><PERSON><PERSON> ang <PERSON> cache...", "Click here for more details": "Mag-click dito para sa higit pang mga detalye", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Mag-click sa I-install upang simulan ang proseso ng pag-install. Kung lalaktawan mo ang pag-install, maaaring hindi gumana ang UniGetUI gaya ng inaasahan.", "Close": "Isara", "Close UniGetUI to the system tray": "Isara ang UniGetUI sa system tray", "Close WingetUI to the notification area": "Isara ang UniGetUI sa lugar ng notification", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Ang Cloud backup ay gumagamit ng pribadong GitHub Gist para makatago ng listahan ng mga na-install na package", "Cloud package backup": null, "Command-line Output": "Output ng command-line", "Command-line to run:": "Command-line na patakbuhin:", "Compare query against": "Ihambing ang query kontra sa", "Compatible with authentication": "Compatible na may authentication", "Compatible with proxy": "Compatible sa proxy", "Component Information": "Impormasyon ng Component", "Concurrency and execution": "Concurrency at pag-execute", "Connect the internet using a custom proxy": "Kumonekta sa internet gamit ang isang custom na proxy", "Continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Mag-ambag sa icon at imbakan ng screenshot", "Contributors": "Mga kontribyutor", "Copy": "<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "<PERSON><PERSON><PERSON><PERSON> sa clipboard", "Could not add source": "Hindi maidagdag ang source", "Could not add source {source} to {manager}": "Hindi maidagdag ang source {source} sa {manager}", "Could not back up packages to GitHub Gist: ": "Hindi ma-back up ang mga package sa GitHub Gist:", "Could not create bundle": "Hindi makagawa ng bundle", "Could not load announcements - ": "Hindi ma-load ang mga anunsyo -", "Could not load announcements - HTTP status code is $CODE": "Hindi ma-load ang mga anunsyo - HTTP status code ay $CODE", "Could not remove source": "Hindi maalis ang source", "Could not remove source {source} from {manager}": "Hindi maalis ang source {source} mula sa {manager}", "Could not remove {source} from {manager}": "Hindi maalis ang source {source} mula sa {manager}", "Create .ps1 script": "Gumawa ng .ps1 script", "Credentials": "<PERSON>ga k<PERSON>", "Current Version": "Kasalukuyang Bersyon", "Current status: Not logged in": "Kasalukuyang katayuan: Hindi naka-log in", "Current user": "<PERSON><PERSON><PERSON><PERSON>", "Custom arguments:": "Mga custom na argumento:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Maaaring baguhin ng mga custom na argumento sa command-line ang paraan ng pag-install, pag-upgrade o pag-uninstall ng mga program, sa paraang hindi makontrol ng UniGetUI. Ang paggamit ng mga custom na command-line ay maaaring masira ang mga package. Magpatuloy nang may pag-iingat.", "Custom command-line arguments:": "Mga custom na argumento sa command-line:", "Custom install arguments:": "Custom na argumento sa pag-install:", "Custom uninstall arguments:": "Custom na argumento sa pag-uninstall:", "Custom update arguments:": "Custom na argumento sa pag-update:", "Customize WingetUI - for hackers and advanced users only": "I-customize ang UniGetUI - para sa mga hacker at advanced na user lamang", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "DISCLAIMER: <PERSON><PERSON><PERSON> RESPONSABLE PARA SA NA-DOWNLOAD NA MGA PACKAGE. MANGYARING SIGURADO NA I-INSTALL LAMANG PINAG<PERSON><PERSON><PERSON><PERSON><PERSON> SOFTWARE.", "Dark": "<PERSON><PERSON><PERSON>", "Decline": "<PERSON><PERSON><PERSON>", "Default": null, "Default installation options for {0} packages": "Default na opyson sa pag-install para sa {0} na package", "Default preferences - suitable for regular users": "Mga default na kagustuhan - angkop para sa mga regular na gumagamit", "Default vcpkg triplet": "Default na vcpkg triplet", "Delete?": "Tanggalin?", "Dependencies:": "<PERSON>ga kinakailangan:", "Descendant": "Ayos na pababa", "Description:": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "Desktop shortcut created": "Nagawa ang desktop shortcut", "Details of the report:": "Mga detalye ng report:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Ang pag-develop ay mahirap, at ang aplikasyon na ito ay libre. Ngunit kung nagustuhan mo ang application, maaari mo akong palaging <b>bumili ng kape</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Direktang i-install kapag nag-double click sa isang item sa tab na \"{discoveryTab}\" (sa halip na ipakita ang impormasyon ng package)", "Disable new share API (port 7058)": "I-disable ang bagong share API (port 7058)", "Disable the 1-minute timeout for package-related operations": "I-disable ang 1 minutong timeout para sa mga operasyong nauugnay sa package", "Disclaimer": "<PERSON><PERSON><PERSON>", "Discover Packages": "Tumuklas ng mga package", "Discover packages": "Tumuklas ng mga package", "Distinguish between\nuppercase and lowercase": "Magkaiba sa pagitan ng\nuppercase at lowercase", "Distinguish between uppercase and lowercase": "Magkaiba sa pagitan ng\nuppercase at lowercase", "Do NOT check for updates": "HUWAG tingnan ang mga update", "Do an interactive install for the selected packages": "Gumawa ng interactive na pag-install para sa mga napiling package", "Do an interactive uninstall for the selected packages": "Gumawa ng interactive na pag-uninstall para sa mga napiling package", "Do an interactive update for the selected packages": "Gumawa ng interactive na update para sa mga napiling package", "Do not automatically install updates when the battery saver is on": "Huwag awtomatikong mag-install ng mga update kapag naka-on ang battery saver", "Do not automatically install updates when the network connection is metered": "Huwag awtomatikong mag-install ng mga update kapag ang koneksyon sa network ay nakametro", "Do not download new app translations from GitHub automatically": "Huwag awtomatikong mag-download ng mga bagong pagsasalin ng app mula sa GitHub", "Do not ignore updates for this package anymore": "Huwag nang balewalain ang mga update para sa package na ito", "Do not remove successful operations from the list automatically": "Huwag awtomatikong alisin ang mga matagumpay na operasyon sa listahan", "Do not show this dialog again for {0}": "Huwag ipakita muli ang dialog na ito para sa {0}", "Do not update package indexes on launch": "Huwag i-update ang mga package index sa paglulunsad", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Tinatanggap mo ba na ang UniGetUI ay nangongolekta at nagpapadala ng mga hindi kilalang istatistika ng paggamit, na may tanging layunin na maunawaan at mapabuti ang karanasan ng user?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Nakikita mo bang kapaki-pakinabang ang UniGetUI? Kung magagawa mo, maaaring gusto mong suportahan ang aking trabaho, para maipagpatuloy ko ang paggawa ng UniGetUI na ultimate package sa pamamahala ng interface.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Naki<PERSON>ta mo bang kapaki-pakinabang ang UniGetUI? Gusto mo bang suportahan ang developer? <PERSON> gayon, ma<PERSON>i mong {0}, nakaka<PERSON><PERSON> ito nang malaki!", "Do you really want to reset this list? This action cannot be reverted.": "<PERSON>to mo ba talagang i-reset ang listahang ito? Hindi na maibabalik ang pagkilos na ito.", "Do you really want to uninstall the following {0} packages?": "<PERSON>to mo ba talagang i-uninstall ang mga sumusunod na {0} na package?", "Do you really want to uninstall {0} packages?": "Gusto mo ba talagang i-uninstall ang {0} na mga package?", "Do you really want to uninstall {0}?": "<PERSON>to mo ba talagang i-uninstall ang {0}?", "Do you want to restart your computer now?": "Gusto mo bang i-restart ang iyong computer ngayon?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "<PERSON>to mo bang isalin ang UniGetUI sa iyong wika? Tingnan kung paano mag-ambag <a style=\"color:{0}\" href=\"{1}\"a>DITO!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Ayaw mag-donate? <PERSON><PERSON><PERSON> mag-alala, ma<PERSON>i mong ibahagi ang UniGetUI anumang oras sa iyong mga kaibigan. Ikalat ang balita tungkol sa UniGetUI.", "Donate": "Mag-donate", "Done!": "Tapos na!", "Download failed": "Nabigo ang pag-download", "Download installer": "I-download ang installer", "Download operations are not affected by this setting": "Ang mga operasyon sa pag-download ay hindi apektado ng setting na ito", "Download selected installers": "I-download ang mga napiling installer", "Download succeeded": "Nagtagumpay ang pag-download", "Download updated language files from GitHub automatically": "Awtomatikong mag-download ng mga na-update na file ng wika mula sa GitHub", "Downloading": "Nagda-download", "Downloading backup...": "Dina-download ang backup...", "Downloading installer for {package}": "Dina-download ang installer para sa {package}", "Downloading package metadata...": "Dina-download ang metadata ng package...", "Enable Scoop cleanup on launch": "Paganahin ang paglilinis ng Scoop sa pagbubukas", "Enable WingetUI notifications": "Paganahin ang mga notification ng UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "<PERSON><PERSON><PERSON><PERSON> ang isang [pang-eksperimentong] pinahusay na troubleshooter ng WinGet", "Enable and disable package managers, change default install options, etc.": "<PERSON><PERSON><PERSON><PERSON> at di paganahin ang mga package manager, palitan ang default na opsyon sa pag-install, atbp.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Paganahin ang mga pag-optimize sa paggamit ng CPU sa background (tingnan ang Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Paganahin ang background API (Mga Widget para sa UniGetUI at Pagbabahagi, port 7058)", "Enable it to install packages from {pm}.": "Paganahin itong mag-install ng mga package mula sa {pm}.", "Enable the automatic WinGet troubleshooter": "<PERSON><PERSON><PERSON><PERSON> ang awtomatikong WinGet troubleshooter", "Enable the new UniGetUI-Branded UAC Elevator": "<PERSON><PERSON><PERSON><PERSON> ang bagong UniGetUI-Branded UAC Elevator", "Enable the new process input handler (StdIn automated closer)": "<PERSON><PERSON><PERSON><PERSON> ang bagong process input handler (StdIn automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Paganahin ang mga setting sa ibaba KUNG AT LAMANG KUNG lubusan mong nauunawaan ang kanilang ginagawa, at ang mga implikasyon at panganib na maaaring kasangkot sa kanila.", "Enable {pm}": "<PERSON><PERSON><PERSON><PERSON> {pm}", "Enter proxy URL here": "Ilagay ang proxy URL dito", "Entries that show in RED will be IMPORTED.": "Ang mga entry na makikita sa PULA ay I-IMPORT.", "Entries that show in YELLOW will be IGNORED.": "Ang mga entry na makikita sa DILAW ay HINDI PAPANSININ.", "Error": null, "Everything is up to date": "<PERSON> lahat ay napapa<PERSON>on", "Exact match": "Eksaktong tugma", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Ii-scan ang mga kasalukuyang shortcut sa iyong desktop, at kakailanganin mong pumili kung alin ang dapat panatilihin at kung alin ang aalisin.", "Expand version": "<PERSON><PERSON><PERSON><PERSON> ang bersyon", "Experimental settings and developer options": "Mga pang-eksperimentong setting at mga opsyon sa developer", "Export": "I-export", "Export log as a file": "I-export ang log bilang isang file", "Export packages": "I-export ang mga package", "Export selected packages to a file": "I-export ang mga napiling package sa isang file", "Export settings to a local file": "I-export ang mga setting sa isang lokal na file", "Export to a file": "I-export sa isang file", "Failed": "Nabigo", "Fetching available backups...": "Kinukuha ang mga available na backup...", "Fetching latest announcements, please wait...": "<PERSON><PERSON><PERSON><PERSON> ang mga pinakabagong anunsyo, mangyaring maghintay...", "Filters": "Mga filter", "Finish": "Tapo<PERSON>", "Follow system color scheme": "Sundin ang scheme ng kulay ng system", "Follow the default options when installing, upgrading or uninstalling this package": "Sundan ang mga default na opsyon sa pag-install, pag-upgrade o pag-uninstall ng package na ito", "For security reasons, changing the executable file is disabled by default": "Dahil sa mga kadahilanang pangseguridad, ang pagpapalit ng executable file ay hindi pinapagana bilang default.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Dahil sa mga kadahilanang pangseguridad, ang mga custom na argumento sa command-line ay di-pinagana ng default. Pumunta sa mga setting ng seguridad sa UniGetUI para palitan ito.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Dahil sa mga kadahilanang pangseguridad, ang mga script ng pre-operation at post-operation ay di-pinagana ng default. Pumunta sa mga setting ng seguridad sa UniGetUI para palitan ito.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Pilitin ang ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)", "Formerly known as WingetUI": "Dating kilala bilang WingetUI", "Found": "<PERSON><PERSON><PERSON>", "Found packages: ": "Nahanap (na) mga package:", "Found packages: {0}": "Nahanap (na) mga package: {0}", "Found packages: {0}, not finished yet...": "Nahanap (na) mga package: {0}, hindi pa natatapos...", "General preferences": "Pangkalahatang kagustuhan", "GitHub profile": "Profile sa GitHub", "Global": null, "Go to UniGetUI security settings": "Pumunta sa setting ng seguridad ng UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Mahusay na imbakan ng hindi alam ngunit kapaki-pakinabang na mga utility at iba pang kawili-wiling mga package.<br>Naglalaman ng: <b>Mga Utility, Command-line program, Pangkalahatang Software (kinakailangan ng extras na bucket)</b>", "Great! You are on the latest version.": "Mahusay! Ikaw ay nasa pinakabagong bersyon.", "Grid": null, "Help": "<PERSON><PERSON>", "Help and documentation": "Tu<PERSON> at dokumentasyon", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Dito maaari mong baguhin ang pag-uugali ng UniGetUI tungkol sa mga sumusunod na shortcut. Ang pagsuri sa isang shortcut ay gagawing tatanggalin ito ng UniGetUI kung gagawin ito sa isang pag-upgrade sa hinaharap. Ang pag-alis ng check dito ay magpapanatili ang shortcut", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON>, ang pangalan ko ay Martí, at ako ang <i>developer</i> ng UniGetUI. Ang UniGetUI ay ganap na ginawa sa aking libreng oras!", "Hide details": "Itago ang mga detalye", "Homepage": null, "Hooray! No updates were found.": "Hooray! Walang nahanap (na) mga update.", "How should installations that require administrator privileges be treated?": "<PERSON><PERSON> dapat tratuhin ang mga pag-install na nanganga<PERSON>ngan ng mga pribilehiyo ng administrator?", "How to add packages to a bundle": "Paano magdagdag ng mga package sa isang bundle", "I understand": "<PERSON><PERSON><PERSON><PERSON><PERSON> ko", "Icons": "Mga icon", "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Kung napagana ang cloud backup, ito ay ise-save bilang isang GitHub Gist sa account na ito", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "<PERSON><PERSON><PERSON> pansinin ang mga custom na pre-install at post-install na command kapag nag-i-import ng mga package mula sa isang bundle", "Ignore future updates for this package": "Huwag pansinin ang mga update sa hinaharap para sa package na ito", "Ignore packages from {pm} when showing a notification about updates": "Huwag pansinin ang mga package mula {pm} kapag nagpapakita ng notification tungkol sa mga update", "Ignore selected packages": "Huwag pansinin ang mga napiling package", "Ignore special characters": "Huwag pansinin ang mga espesyal na character", "Ignore updates for the selected packages": "Huwag pansinin ang mga update para sa mga napiling package", "Ignore updates for this package": "Huwag pansinin ang mga update para sa mga napiling package", "Ignored updates": "Binalewala ang mga update", "Ignored version": "Binalewala ang bersyon", "Import": "I-import", "Import packages": "I-import ang mga package", "Import packages from a file": "Mag-import ng mga package mula sa isang file", "Import settings from a local file": "Mag-import ng mga setting mula sa isang lokal na file", "In order to add packages to a bundle, you will need to: ": "Upang magdagdag ng mga package sa isang bundle, kakailanganin mong:", "Initializing WingetUI...": "Sinisimulan ang UniGetUI...", "Install": "I-install", "Install Scoop": "I-install ang <PERSON>", "Install and more": "I-install at iba pa", "Install and update preferences": "Kagustuhan ng pag-install at pag-update", "Install as administrator": "I-install bilang administator", "Install available updates automatically": "Awtomatikong i-install ang mga available na update", "Install location can't be changed for {0} packages": "Hindi mababago ang lokasyon ng pag-install para sa {0} na package", "Install location:": "Lokasyon ng pag-install:", "Install options": "Mga opsyon sa install", "Install packages from a file": "Mag-install ng mga package mula sa isang file", "Install prerelease versions of UniGetUI": "Mag-install ng mga prerelease na bersyon ng UniGetUI", "Install script": "Script ng pag-install", "Install selected packages": "I-install ang mga napiling package", "Install selected packages with administrator privileges": "I-install ang mga napiling package na may mga pribilehiyo ng administrator", "Install selection": "I-install ang mga napili", "Install the latest prerelease version": "I-install ang pinakabagong bersyon ng prerelease", "Install updates automatically": "Awtomatikong i-install ang mga update", "Install {0}": "I-install ang {0}", "Installation canceled by the user!": "Kinansela ng user ang pag-install!", "Installation failed": "Nabigo ang pag-install", "Installation options": "Mga opsyon sa pag-install", "Installation scope:": "Saklaw ng pag-install:", "Installation succeeded": "Nagtagumpay ang pag-install", "Installed Packages": "Mga Naka-install na Package", "Installed Version": "Naka-install na <PERSON><PERSON>", "Installed packages": "Mga naka-install na package", "Installer SHA256": "SHA256 ng installer", "Installer SHA512": "SHA512 ng installer", "Installer Type": "Uri ng Installer", "Installer URL": "Uri ng Installer", "Installer not available": "Hindi available ang installer", "Instance {0} responded, quitting...": "Instance {0} ay tumugon, huminto...", "Instant search": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Integrity checks can be disabled from the Experimental Settings": "Maaaring i-disable ang mga pagsusuri sa integridad mula sa Mga Pang-eksperimento Setting", "Integrity checks skipped": "Nilaktawan ang mga pagsusuri sa integridad", "Integrity checks will not be performed during this operation": "Ang mga pagsusuri sa integridad ay hindi isasagawa sa panahon ng operasyong ito", "Interactive installation": "Interactive na pag-install", "Interactive operation": "Interactive na operasyon", "Interactive uninstall": "Interactive na pag-uninstall", "Interactive update": "Interactive na pag-update", "Internet connection settings": "Mga setting ng koneksyon sa internet", "Is this package missing the icon?": "<PERSON><PERSON><PERSON> ba ang icon ng package na ito?", "Is your language missing or incomplete?": "<PERSON><PERSON><PERSON> ba o hindi kumpleto ang iyong wika?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Hindi garantisadong ligtas na maiimbak ang mga ibinigay na kredensyal, kaya maaari mo ring hindi gamitin ang mga kredensyal ng iyong bank account", "It is recommended to restart UniGetUI after WinGet has been repaired": "Inirerekomenda na i-restart ang UniGetUI pagkatapos ayusin ang WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Lubos na inirerekomendang ang pag-reinstall ng UniGetUI upang matugunan ang sitwasyon.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "<PERSON>khang hindi gumagana nang maayos ang WinGet. <PERSON><PERSON> mo bang subukang ayusin ang WinGet?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON>ng pinatakbo mo ang UniGetUI bilang administrator, na hindi inirerekomenda. Magagamit mo pa rin ang program, ngunit lubos naming inirerekomenda na huwag patakbuhin ang UniGetUI na may mga pribilehiyo ng administrator. Mag-click sa \"{showDetails}\" para makita kung bakit.", "Language": "<PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON>, tema at iba pang mga kagustuhan", "Last updated:": "Huling na-update:", "Latest": "Pinakabago", "Latest Version": "Pinakabagong Bersyon", "Latest Version:": "Pinakabagong Bersyon:", "Latest details...": "Pinakabagong detalye...", "Launching subprocess...": "Inilunsad ang subprocess...", "Leave empty for default": "<PERSON><PERSON>ng walang laman para sa default", "License": "<PERSON><PERSON><PERSON><PERSON>", "Licenses": "Mga lisensya", "Light": "Malinawag", "List": "<PERSON><PERSON>", "Live command-line output": "Live na command-line na output", "Live output": "Live na output", "Loading UI components...": "Nilo-load ang mga bahagi ng UI...", "Loading WingetUI...": "Nilo-load ang UniGetUI...", "Loading packages": "Naglo-load ng mga package", "Loading packages, please wait...": "Naglo-load ng mga package, mangyaring maghintay...", "Loading...": "Naglo-load...", "Local": "<PERSON><PERSON>", "Local PC": "Lokal na PC", "Local backup advanced options": "Mga advanced na opsyon sa Lokal na backup", "Local machine": "Lokal na machine", "Local package backup": "Pag-backup ng lokal na package", "Locating {pm}...": "<PERSON><PERSON><PERSON><PERSON> ang {pm}...", "Log in": "Mag-log in", "Log in failed: ": "Bigong mag-log in:", "Log in to enable cloud backup": "Mag-log in para mapagana ang cloud backup", "Log in with GitHub": "Mag-log in gamit ang GitHub", "Log in with GitHub to enable cloud package backup.": "Mag-log in gamit ang GitHub para mapagana ang pag-backup ng cloud package", "Log level:": "Antas ng log:", "Log out": "Mag-log out", "Log out failed: ": "Bigong mag-log out:", "Log out from GitHub": "Mag-log out sa <PERSON><PERSON><PERSON>", "Looking for packages...": "Naghahanap ng mga package...", "Machine | Global": null, "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Maaaring masira ng mga maling argumento sa command-line ang mga package, o payagan ang isang malisyosong aktor na makakuha ng privileged execution. Samakatuwid, ang pag-import ng mga custom na argumento sa command-line ay hindi pinagana bilang default", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage UniGetUI settings": "Pamahalaan ang mga setting ng UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Pamahalaan ang UniGetUI autostart na gawi mula sa Mga Setting na app", "Manage ignored packages": "<PERSON><PERSON>alaan ang mga hindi pinapansin na mga package", "Manage ignored updates": "<PERSON><PERSON><PERSON>an ang mga hindi pinapansin na update", "Manage shortcuts": "<PERSON><PERSON><PERSON>an ang mga shortcut", "Manage telemetry settings": "Pamahalaan ang mga setting ng telemetry", "Manage {0} sources": "<PERSON><PERSON><PERSON><PERSON> ang {0} mga source", "Manifest": null, "Manifests": "Mga manifest", "Manual scan": "Mano-manong i-scan", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Opisyal na manager ng package ng Microsoft. Puno ng mga kilalang at na-verify na package<br>Naglalaman ng: <b>Pangkalahatang Software, Mga app ng Microsoft Store</b>", "Missing dependency": "Nawawalang kinaka<PERSON>ngan", "More": "Higit pa", "More details": "Higit pang mga detalye", "More details about the shared data and how it will be processed": "Higit pang mga detalye tungkol sa nakabahaging data at kung paano ito ipoproseso", "More info": "Higit pang impormasyon", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "TANDAAN: <PERSON><PERSON><PERSON> hindi paganahin ang troubleshooter na ito mula sa Mga Setting ng UniGetUI, sa seksyong WinGet", "Name": "<PERSON><PERSON><PERSON>", "New": "Bago", "New Version": "Bagong Bersyon", "New bundle": "Bagong bundle", "New version": "Bagong Bersyon", "Nice! Backups will be uploaded to a private gist on your account": "Ayos! Ang mga backup ay ia-upload sa pribadong gist sa iyong account", "No": "Hindi", "No applicable installer was found for the package {0}": "Walang nahanap na naaangkop na installer para sa package na {0}", "No dependencies specified": "Walang tinukoy na kinakailangan", "No new shortcuts were found during the scan.": null, "No packages found": "Walang nahanap (na) mga package", "No packages found matching the input criteria": "Walang nahanap (na) mga package na tumutugma sa pamantayan sa pag-input", "No packages have been added yet": "Wala pang mga package na naidagdag", "No packages selected": "Walang napiling mga package", "No packages were found": "Walang nahanap na mga package", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Walang personal na impormasyon ang kinokolekta o ipinadala, at ang nakolektang data ay hindi nagpapakilala, kaya hindi ito maibabalik sa iyo.", "No results were found matching the input criteria": "Walang nahanap (na) mga resulta na tumutugma sa pamantayan sa pag-input", "No sources found": "Walang nahanap (na) source", "No sources were found": "Walang nahanap na source", "No updates are available": "Walang available na mga update", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Ang manager ng package ng Node JS. Puno ng mga library at iba pang mga utility na umiikot sa mundo ng javascript<br>Naglalaman ng: <b>Mga library ng javascript ng node at iba pang nauugnay na mga utility</b>", "Not available": "Hindi available", "Not finding the file you are looking for? Make sure it has been added to path.": "Hindi mahanap ang file na hinahanap mo? Tiyaking naidagdag ito sa path.", "Not found": "Hindi mahanap", "Not right now": "Hindi sa ngayon", "Notes:": "Mga Note:", "Notification preferences": "Mga kagustuhan sa notification", "Notification tray options": "Mga opsyon sa tray ng notification", "Notification types": "Mga uri ng notification", "NuPkg (zipped manifest)": "NuPkg (naka-zip na manifest)", "OK": null, "Ok": null, "Open": "<PERSON><PERSON><PERSON>", "Open GitHub": "<PERSON><PERSON><PERSON>itHub", "Open UniGetUI": "B<PERSON><PERSON> ang UniGetUI", "Open UniGetUI security settings": "Buksan ang mga setting ng seguridad ng UniGetUI", "Open WingetUI": "B<PERSON><PERSON> ang UniGetUI", "Open backup location": "<PERSON><PERSON><PERSON> ang lokasyon ng backup", "Open existing bundle": "<PERSON><PERSON><PERSON> ang umiiral na bundle", "Open install location": "<PERSON><PERSON><PERSON> ang lokasyon ng pag-install", "Open the welcome wizard": "<PERSON><PERSON><PERSON> ang welcome wizard", "Operation canceled by user": "Kinansela ng user ang operasyon", "Operation cancelled": "<PERSON><PERSON><PERSON><PERSON> ang <PERSON>", "Operation history": "Kasaysayan ng operasyon", "Operation in progress": "<PERSON><PERSON><PERSON><PERSON> isinasagawa ang <PERSON>yon", "Operation on queue (position {0})...": "Operasyon sa queue (posisyon {0})...", "Operation profile:": null, "Options saved": "Nai-save ang mga opsyon", "Order by:": "Mag-order sa pamamagitan ng:", "Other": "Iba pa", "Other settings": "Iba pang mga setting", "Package": null, "Package Bundles": "Mga Bundle ng Package", "Package ID": "ID ng Package", "Package Manager": null, "Package Manager logs": "Mga log ng Package Manager", "Package Managers": "Mga Package Manager", "Package Name": "Pangalan ng Package", "Package backup": "I-backup ang package", "Package backup settings": "Mga setting ng pag-backup ng package", "Package bundle": null, "Package details": "Mga detalye ng package", "Package lists": "Mga listahan ng package", "Package management made easy": "Pinadali ang pamamahala ng package", "Package manager": null, "Package manager preferences": "Mga kagustuhan sa package manager", "Package managers": "Mga package manager", "Package not found": "Hindi mahanap ang package", "Package operation preferences": "Mga kagustuhan sa pagpapatakbo ng package", "Package update preferences": "Mga kagustuhan sa pag-update ng package", "Package {name} from {manager}": "Package {name} mula sa {manager}", "Package's default": "Default ng package", "Packages": "Mga package", "Packages found: {0}": "Nahanap (na) mga package: {0}", "Partially": "Bahagyang", "Password": null, "Paste a valid URL to the database": "Mag-paste ng wastong URL sa database", "Pause updates for": "I-pause ang mga update hanggang", "Perform a backup now": "Magsagawa ng backup ngayon", "Perform a cloud backup now": "Magsagawa ng cloud backup ngayon", "Perform a local backup now": "Magsagawa ng lokal na backup ngayon", "Perform integrity checks at startup": "Magsagawa ng mga pagsusuri sa integridad sa pagsisimula", "Performing backup, please wait...": "Nagsasagawa ng backup, mangyaring maghintay...", "Periodically perform a backup of the installed packages": "Pana-panahong magsagawa ng backup ng mga naka-install na package", "Periodically perform a cloud backup of the installed packages": "Pana-panahong magsagawa ng cloud backup ng mga naka-install na package", "Periodically perform a local backup of the installed packages": "Pana-panahong magsagawa ng lokal na backup ng mga naka-install na package", "Please check the installation options for this package and try again": "Pakisuri ang mga opsyon sa pag-install para sa package na ito at subukang muli", "Please click on \"Continue\" to continue": "Mangyaring mag-click sa \"Magpatuloy\" upang magpatuloy", "Please enter at least 3 characters": "Mangyaring maglagay ng hindi bababa sa 3 character", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON><PERSON><PERSON><PERSON> na maaaring hindi mai-install ang ilang partikular na package, dahil sa mga package manager na naka-enable sa machine na ito.", "Please note that not all package managers may fully support this feature": "Pakitandaan na hindi lahat ng package manager ay maaaring ganap na suportahan ang feature na ito", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Pakitandaan na ang mga package mula sa ilang partikular na source ay maaaring hindi ma-export. Ang mga ito ay na-grey out at hindi na i-export.", "Please run UniGetUI as a regular user and try again.": "Mangyaring patakbuhin ang UniGetUI bilang isang regular na user at subukang muli.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Pakitingnan ang Output ng Command-line o sumangguni sa Kasaysayan ng Operasyon para sa karagdagang impormasyon tungkol sa isyu.", "Please select how you want to configure WingetUI": "Mangyaring piliin kung paano mo gustong i-configure ang UniGetUI", "Please try again later": "<PERSON><PERSON><PERSON><PERSON><PERSON> muli mamaya", "Please type at least two characters": "Mangyaring mag-type ng hindi bababa sa dalawang character", "Please wait": "Man<PERSON><PERSON> maghintay", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Mangyaring maghintay habang ini-install ang {0}. Ma<PERSON>ing lumabas ang isang itim (o asul) na window. Pakihintay hanggang sa magsara ito.", "Please wait...": "Mangyaring maghintay...", "Portable": null, "Portable mode": null, "Post-install command:": "Command pagkatapos ng pag-install:", "Post-uninstall command:": "Command pagkatapos ng pag-uninstall:", "Post-update command:": "Command pagkatapos ng pag-update:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Package manager ng PowerShell. Maghanap ng mga library at script para palawakin ang mga kakayahan ng PowerShell<br>Naglalaman ng: <b><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Ang mga command bago at pagkatapos ng pag-install ay maaaring gumawa ng mga napakasamang bagay sa iyong device, kung idinisenyo upang gawin ito. Maaaring lubhang mapanganib ang pag-import ng mga command mula sa isang bundle, maliban kung pinagkakatiwalaan mo ang pinagmulan ng bundle ng package na iyon", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Ang mga command bago at pagkatapos ay patatakbuhin bago at pagkatapos ma-install, ma-upgrade o ma-uninstall ang isang package. Magkaroon ng kamalayan na maaari silang masira ang mga bagay maliban kung ginamit nang maingat", "Pre-install command:": "Command bago mag-install:", "Pre-uninstall command:": "Command bago mag-uninstall:", "Pre-update command:": "Command bago ang pag-update:", "PreRelease": null, "Preparing packages, please wait...": "Inihahanda ang mga package, mangyaring maghintay...", "Proceed at your own risk.": "Magpatuloy sa sarili mong pananagutan.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Ipagbawal ang anumang uri ng Elevation sa pamamagitan ng UniGetUI Elevator o GSudo", "Proxy URL": "URL ng proxy", "Proxy compatibility table": "Compatability table ng proxy", "Proxy settings": "Mga setting ng proxy", "Proxy settings, etc.": "Mga setting ng proxy, atbp.", "Publication date:": "Petsa ng publikasyon:", "Publisher": "Tagapaglathala:", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Tagapamahala ng library ng Python. Puno ng mga library ng python at iba pang mga kagamitang nauugnay sa python<br>Naglalaman ng: <b>Mga library ng Python at mga nauugnay na kagamitan</b>", "Quit": "Lu<PERSON><PERSON>", "Quit WingetUI": "Lumabas ng UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "<PERSON><PERSON>an ang mga UAC prompt, itaas ang mga pag-install bilang default, i-unlock ang ilang partikular na mapanganib na feature, atbp.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Sumangguni sa mga log ng UniGetUI para makakuha ng higit pang mga detalye tungkol sa (mga) apektadong file", "Reinstall": "I-reinstall", "Reinstall package": "I-reinstall ang package", "Related settings": "<PERSON>ga kaugnay na setting", "Release notes": "Mga release note", "Release notes URL": "URL ng mga release note", "Release notes URL:": "URL ng mga release note:", "Release notes:": "Mga release note:", "Reload": "I-reload", "Reload log": "I-reload ang log", "Removal failed": "Nabigo ang pag-alis", "Removal succeeded": "Nagtagumpay ang pagtanggal", "Remove from list": "<PERSON><PERSON>", "Remove permanent data": "<PERSON><PERSON> ang permanenteng data", "Remove selection from bundle": "<PERSON><PERSON> ang napili sa <PERSON>", "Remove successful installs/uninstalls/updates from the installation list": "<PERSON><PERSON> ang matagumpay na pag-install/pag-uninstall/pag-update mula sa listahan ng pag-install", "Removing source {source}": "Inaalis ang source {source}", "Removing source {source} from {manager}": "Inaalis ang source {source} mula sa {manager}", "Repair UniGetUI": "<PERSON><PERSON><PERSON> ang UniGetUI", "Repair WinGet": "<PERSON><PERSON><PERSON>", "Report an issue or submit a feature request": "Mag-ulat ng isyu o magsumite ng kahilingan sa feature", "Repository": null, "Reset": "I-reset", "Reset Scoop's global app cache": "I-reset ang global app cache ng Scoop", "Reset UniGetUI": "I-reset ang UniGetUI", "Reset WinGet": "I-reset ang Win<PERSON>et", "Reset Winget sources (might help if no packages are listed)": "I-reset ang mga source ng Winget (maaaring makatulong kung walang nakalistang mga package)", "Reset WingetUI": "I-reset ang UniGetUI", "Reset WingetUI and its preferences": "I-reset ang UniGetUI at ang mga kagustuhan nito", "Reset WingetUI icon and screenshot cache": "I-reset ang icon ng UniGetUI at cache ng screenshot", "Reset list": "I-reset ang listahan", "Resetting Winget sources - WingetUI": "Rine-reset ang mga source ng WinGet - UniGetUI", "Restart": "I-reset", "Restart UniGetUI": "I-restart ang UniGetUI", "Restart WingetUI": "I-restart ang UniGetUI", "Restart WingetUI to fully apply changes": "I-restart ang UniGetUI upang ganap na mailapat ang mga pagbabago", "Restart later": "I-restart mamaya", "Restart now": "I-restart ngayon", "Restart required": "Kinakailangang i-restart", "Restart your PC to finish installation": "I-restart ang iyong PC upang matapos ang pag-install", "Restart your computer to finish the installation": "I-restart ang iyong computer upang matapos ang pag-install", "Restore a backup from the cloud": "I-restore ang backup mula sa cloud", "Restrictions on package managers": "Mga restriksyon sa mga package manager", "Restrictions on package operations": "Mga restriksyon sa mga operasyon ng package", "Restrictions when importing package bundles": "Mga restriksyon kung nag-iimport ng mga package bundle", "Retry": "<PERSON><PERSON><PERSON> muli", "Retry as administrator": "<PERSON><PERSON>ng muli bilang administrator", "Retry failed operations": "Subukang muli ang mga nabigong operasyon", "Retry interactively": "Subukang muli nang interactive", "Retry skipping integrity checks": "Subukang laktawan ang mga pagsusuri sa integridad", "Retrying, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON> muli, mangyaring maghintay...", "Return to top": "Bumalik sa itaas", "Run": "Magpatakbo", "Run as admin": "Magpatakbo bilang admin", "Run cleanup and clear cache": "Patak<PERSON><PERSON> ang paglilinis at linisin ang cache", "Run last": "<PERSON><PERSON>", "Run next": "<PERSON><PERSON><PERSON><PERSON> su<PERSON>", "Run now": "<PERSON><PERSON><PERSON><PERSON> ngayon", "Running the installer...": "Pinapatakbo ang installer...", "Running the uninstaller...": "Pinapatakbo ang uninstaller...", "Running the updater...": "Pinapatakbo ng updater...", "Save": "I-save", "Save File": "I-save ang File", "Save and close": "I-save at isara", "Save as": "I-save bilang", "Save bundle as": "I-save ang bundle bilang", "Save now": "I-save ngayon", "Saving packages, please wait...": "Sine-save ang mga package, mangyaring maghintay...", "Scoop Installer - WingetUI": "Installer ng Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Uninstaller ng Scoop - UniGetUI", "Scoop package": "Package ng scoop", "Search": "Ma<PERSON>ap", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Maghanap ng desktop software, balaan ako kapag may available na mga update at huwag gumawa ng mga nerdy na bagay. Ayokong maging kumplikado ang UniGetUI, gusto ko lang ng simpleng <b>software store</b>", "Search for packages": "Maghanap ng mga package", "Search for packages to start": "Maghanap ng mga package upang magsimula", "Search mode": "Mode ng paghahanap", "Search on available updates": "Maghanap sa mga available na update", "Search on your software": "Maghanap sa iyong software", "Searching for installed packages...": "Naghahanap ng mga naka-install na package...", "Searching for packages...": "Naghahanap ng mga package...", "Searching for updates...": "Naghahanap ng mga update...", "Select": "<PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "<PERSON><PERSON><PERSON> ang \"{item}\" para idagdag ang iyong custom na bucket", "Select a folder": "P<PERSON>li ng folder", "Select all": "<PERSON><PERSON><PERSON>", "Select all packages": "Piliin ang lahat ng mga package", "Select backup": "Pumili ng backup", "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON> lamang ang <b>kung alam mo ang iyong ginagawa</b>.", "Select package file": "Pumili ng package file", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "<PERSON><PERSON><PERSON> ang backup na gusto mong buksan. Sa ibang pagka<PERSON>on, magagawa mong suriin kung aling mga package/program ang gusto mong i-restore.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Piliin ang mga prosesong dapat isara bago i-install, i-update o i-uninstall ang package na ito.", "Select the source you want to add:": "Piliin ang source na gusto mong idagdag:", "Select upgradable packages by default": "Pumili ng mga naa-upgrade na package bilang default", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON> kung aling <b>mga package manager</b> ang gagamitin ({0}), i-configure kung paano naka-install ang mga package, pamahalaan kung paano pinangangasiwaan ang mga karapatan ng administrator, atbp.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Nagpadala ng handshake. Naghihintay ng halimbawa ng sagot ng listener... ({0}%)", "Set a custom backup file name": "Magtakda ng custom na backup na pangalan ng file", "Set custom backup file name": "Itakda ang custom na backup na pangalan ng file", "Settings": "Mga Setting", "Share": "<PERSON><PERSON><PERSON>", "Share WingetUI": "Ibahagi ang UniGetUI", "Share anonymous usage data": "Ibahagi ang hindi kilalang data ng paggamit", "Share this package": "Ibahagi ang package na ito", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Kung babaguhin mo ang mga setting ng seguridad, kakailanganin mong buksan muli ang bundle para magkabisa ang mga pagbabago.", "Show UniGetUI on the system tray": "Ipakita ang UniGetUI sa system tray", "Show UniGetUI's version and build number on the titlebar.": "Ipakita ang bersyon ng UniGetUI sa title bar", "Show WingetUI": "Ipakita ng UniGetUI", "Show a notification when an installation fails": "Magpakita ng notification kapag nabigo ang pag-install", "Show a notification when an installation finishes successfully": "Magpakita ng notification kapag matagumpay na natapos ang pag-install", "Show a notification when an operation fails": "Magpakita ng notification kapag nabigo ang isang operasyon", "Show a notification when an operation finishes successfully": "Magpakita ng notification kapag matagumpay na natapos ang isang operasyon", "Show a notification when there are available updates": "Magpakita ng notification kapag may mga available na update", "Show a silent notification when an operation is running": "Magpakita ng tahimik na notification kapag tumatakbo ang isang operasyon", "Show details": "Ipakita ang mga detalye", "Show in explorer": "Ipakita sa <PERSON>", "Show info about the package on the Updates tab": "Ipakita ang impormasyon tungkol sa package sa tab ng Mga Update", "Show missing translation strings": "Ipakita ang mga nawawalang string ng pagsasalin", "Show notifications on different events": "Ipakita ang mga abiso sa iba't ibang mga kaganapan", "Show package details": "Ipakita ang mga detalye ng package", "Show package icons on package lists": "Ipakita ang mga icon ng package sa mga listahan ng package", "Show similar packages": "Magkatulad na mga package", "Show the live output": "Ipakita ang live na output", "Size": "<PERSON><PERSON>", "Skip": "Laktawan", "Skip hash check": "Laktawan ang pagsusuri ng hash", "Skip hash checks": "Laktawan ang mga pagsusuri ng hash", "Skip integrity checks": "Laktawan ang mga pagsusuri sa integridad", "Skip minor updates for this package": "Laktawan ang mga minor na update para sa package na ito", "Skip the hash check when installing the selected packages": "Laktawan ang pagsusuri ng hash kapag ini-install ang mga napiling package", "Skip the hash check when updating the selected packages": "Laktawan ang pagsusuri ng hash kapag ina-update ang mga napiling package", "Skip this version": "Laktawan ang bersyon na ito", "Software Updates": "Mga Software Update", "Something went wrong": "Nagkaroon ng problema", "Something went wrong while launching the updater.": "Nagkaroon ng problema habang pinapatakbo ang updater.", "Source": null, "Source URL:": "URL ng Source:", "Source added successfully": "Matagumpay na naidagdag ang source", "Source addition failed": "Nabigo ang pagdaragdag ng source", "Source name:": "Pangalan ng source:", "Source removal failed": "Nabigo ang pag-alis ng source", "Source removed successfully": "Matagumpay na naalis ang source", "Source:": null, "Sources": "Mga Source", "Start": "<PERSON><PERSON><PERSON><PERSON>", "Starting daemons...": "Nags<PERSON><PERSON>la ang mga daemon...", "Starting operation...": "Sinisimulan ang operasyon...", "Startup options": "Mga pagpipilian sa startup", "Status": "Katayuan", "Stuck here? Skip initialization": "Natigil dito? Laktawan ang pagsisimula", "Success!": "Matagumpay!", "Suport the developer": "Suportahan ang developer", "Support me": "Suportahan ako", "Support the developer": "Suportahan ang developer", "Systems are now ready to go!": "Ang mga system ay nakahanda na!", "Telemetry": null, "Text": null, "Text file": "File ng text", "Thank you ❤": "Salamat ❤", "Thank you 😉": "Salamat 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Ang Rust package manager.<br>Na<PERSON>lal<PERSON> ng: <b>Mga Rust na library at program na nakasulat sa Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "HINDI isasama sa backup ang anumang binary file o anumang naka-save na data ng program.", "The backup will be performed after login.": "Ang backup ay is<PERSON><PERSON> pagkatapos mag-login.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Kasama sa backup ang kumpletong listahan ng mga naka-install na package at ang kanilang mga opsyon sa pag-install. Mase-save din ang mga binalewalang update at nilaktawan na bersyon.", "The bundle was created successfully on {0}": "Matagumpay na nalikha ang bundle noong {0}", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON><PERSON><PERSON> di-wasto ang bundle na sinusubukan mong i-load. Pakisuri ang file at subukang muli.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Ang checksum ng installer ay hindi tumutugma sa inaasahang halaga, at ang pagiging tunay ng installer ay hindi mabe-verify. Kung pinagkakatiwalaan mo ang tagapaglathala, {0} muli laktawan ng package ang pagsusuri ng hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Ang klasikong manager ng package para sa Windows. Makikita mo ang lahat doon. <br>Naglalaman ng: <b>Pangkalahatang Software</b>", "The cloud backup completed successfully.": "Matagumpay na nakumpleto ang cloud backup.", "The cloud backup has been loaded successfully.": "Matagumpay na na-load ang cloud backup.", "The current bundle has no packages. Add some packages to get started": "Ang kasalukuyang bundle ay walang mga package. Magdagdag ng ilang mga package upang makapagsimula", "The executable file for {0} was not found": "Ang executable na file para sa {0} ay hindi nahanap", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Ang mga sumusunod na opsyon ay ilalapat bilang default sa tuwing ang isang {0} na package ay na-install, na-upgrade o na-uninstall.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Ang mga sumusunod na package ay ie-export sa isang JSON file. Walang data ng user o binary ang mase-save.", "The following packages are going to be installed on your system.": "Ang mga sumusunod na package ay mai-install sa iyong system.", "The following settings may pose a security risk, hence they are disabled by default.": "Ang mga sumusunod na setting ay maaaring magdulot ng panganib sa seguridad, kaya hindi pinagana ang mga ito bilang default.", "The following settings will be applied each time this package is installed, updated or removed.": "Ilalapat ang mga sumusunod na setting sa tuwing mai-install, maa-update o maalis ang package na ito.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Ilalapat ang mga sumusunod na setting sa tuwing mai-install, maa-update o maalis ang package na ito. Awtomatiko silang mase-save.", "The icons and screenshots are maintained by users like you!": "Ang mga icon at screenshot ay pinapanatili ng mga user na katulad mo!", "The installation script saved to {0}": "Ang script ng pag-install ay na-save sa {0}", "The installer authenticity could not be verified.": "Hindi ma-verify ang pagiging tunay ng installer.", "The installer has an invalid checksum": "Ang installer ay may di-wastong checksum", "The installer hash does not match the expected value.": "Ang hash ng installer ay hindi tumutugma sa inaasahang halaga.", "The local icon cache currently takes {0} MB": "Ang cache ng lokal na icon ay kasalukuyang nasa {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Ang pangunahing layunin ng proyektong ito ay lumikha ng isang madaling gamitin na UI para sa pinakakaraniwang CLI package manager para sa Windows, gaya ng Winget at Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Ang package na \"{0}\" ay hindi nahanap sa package manager na \"{1}\"", "The package bundle could not be created due to an error.": "Hindi magawa ang package bundle dahil sa isang error.", "The package bundle is not valid": "Ang package bundle ay hindi wasto", "The package manager \"{0}\" is disabled": "Ang manager ng package na \"{0}\" ay naka-disable", "The package manager \"{0}\" was not found": "Ang manager ng package na \"{0}\" ay hindi nahanap", "The package {0} from {1} was not found.": "Ang package na {0} mula sa {1} ay hindi nahanap.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Ang mga package na nakalista dito ay hindi isasaalang-alang kapag tumitingin ng mga update. I-double-click ang mga ito o i-click ang button sa kanilang kanan upang ihinto ang pagbalewala sa kanilang mga update.", "The selected packages have been blacklisted": "Ang mga napiling package ay nai-blacklist", "The settings will list, in their descriptions, the potential security issues they may have.": "Ililista ng mga setting, sa kanilang mga paglalarawan, ang mga potensyal na isyu sa seguridad na maaaring mayroon sila.", "The size of the backup is estimated to be less than 1MB.": "Ang laki ng backup ay tinatantya na mas mababa sa 1MB.", "The source {source} was added to {manager} successfully": "Ang source {source} ay matagumpay na naidagdag sa {manager}.", "The source {source} was removed from {manager} successfully": "Matagumpay na naalis ang source {source} mula sa {manager}.", "The system tray icon must be enabled in order for notifications to work": "Dapat na pinagana ang icon ng system tray upang gumana ang mga notification", "The update process has been aborted.": "Na-abort ang proseso ng pag-update.", "The update process will start after closing UniGetUI": "Magsisimula ang proseso ng pag-update pagkatapos isara ang UniGetUI", "The update will be installed upon closing WingetUI": "Ang update ay mai-install sa pagsasara ng UniGetUI", "The update will not continue.": "Hindi matutuloy ang update.", "The user has canceled {0}, that was a requirement for {1} to be run": "<PERSON><PERSON><PERSON><PERSON> ng user ang {0}, iyon ay kinakailangan para sa {1} na patakbuhin", "There are no new UniGetUI versions to be installed": "Walang mga bagong bersyon ng UniGetUI na mai-install", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "May mga patuloy na operasyon. Ang paghinto sa UniGetUI ay maaaring maging sanhi ng pagkabigo sa kanila. Gusto mo bang magpatuloy?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "<PERSON><PERSON>g ilang magagandang video sa YouTube na nagpapakita ng UniGetUI at mga kakayahan nito. Ma<PERSON>i kang matuto ng mga kapaki-pakinabang na trick at tip!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "<PERSON><PERSON><PERSON> dalawang pangunahing dahilan upang hindi patakbuhin ang UniGetUI bilang administrator: \n<PERSON> una ay ang Scoop package manager ay maaaring magdulot ng mga problema sa ilang mga command kapag tumakbo nang may mga karapatan ng administrator. \n<PERSON> pangalawa ay ang pagpapatakbo ng UniGetUI bilang administrator ay nangangahulugan na ang anumang package na iyong ida-download ay tatakbo bilang administrator (at ito ay hindi ligtas). \n<PERSON>daan na kung kailangan mong mag-install ng isang partikular na package bilang administrator, maaari mong palaging i-right-click ang item -> I-install/I-update/I-uninstall bilang administrator.", "There is an error with the configuration of the package manager \"{0}\"": "Mayroong error sa configuration ng package manager na \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "<PERSON><PERSON><PERSON> ka<PERSON> pag-install. Kung isasara mo ang UniGetUI, maaaring mabigo ang pag-install at magkaroon ng mga hindi inaasahang resulta. <PERSON>to mo pa bang umalis sa UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "Ito ang mga program na namamahala sa pag-install, pag-update at pag-alis ng mga package.", "Third-party licenses": "Mga lisensya ng third-party", "This could represent a <b>security risk</b>.": "Ito ay maaaring kuma<PERSON>wan sa isang <b>panganib sa seguridad</b>.", "This is not recommended.": "Hindi ito inirerekomenda.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Ito ay malamang na dahil sa ang katunayan na ang package na ipinadala sa iyo ay inalis, o na-publish sa isang package manager na hindi mo pinagana. Ang natanggap na ID ay {0}", "This is the <b>default choice</b>.": "Ito ang <b>default na pagpipilian</b>.", "This may help if WinGet packages are not shown": "Maaaring makatulong ito kung hindi ipinapakita ang mga package ng WinGet", "This may help if no packages are listed": "Maaaring makatulong ito kung walang nakalistang mga package", "This may take a minute or two": "Maaaring tumagal ito ng isang minuto o dalawa", "This operation is running interactively.": "Interactive na tumatakbo ang operasyong ito.", "This operation is running with administrator privileges.": "Ang operasyong ito ay tumatakbo nang may mga pribilehiyo ng administrator.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Ang opsyon na ito ay MAGDUDULOT ng mga isyu. Ang anumang operasyon na hindi kayang i-elevate ang sarili ay MABIGO. Ang pag-install/pag-update/pag-uninstall bilang administrator ay HINDI GAGANA.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Ang package bundle na ito ay may ilang mga setting na potensyal na mapanganib, at maaaring balewalain bilang default.", "This package can be updated": "Maaaring ma-update ang package na ito", "This package can be updated to version {0}": "Maaaring ma-update ang package na ito sa bersyon {0}", "This package can be upgraded to version {0}": "Maaaring i-upgrade ang package na ito sa bersyon {0}", "This package cannot be installed from an elevated context.": "Hindi ma-install ang package na ito mula sa isang na-elevate na konteksto.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Ang package na ito ay walang mga screenshot o nawawala ang icon? Mag-ambag sa UniGetUI sa pamamagitan ng pagdaragdag ng mga nawawalang icon at screenshot sa aming bukas, pampublikong database.", "This package is already installed": "Naka-install na ang package na ito", "This package is being processed": "Pinoproseso ang package na ito", "This package is not available": "Hindi available ang package na ito", "This package is on the queue": "Ang package na ito ay nasa queue", "This process is running with administrator privileges": "Ang prosesong ito ay tumatakbo na may mga pribilehiyo ng administrator", "This project has no connection with the official {0} project — it's completely unofficial.": "Ang proyektong ito ay walang koneksyon sa opisyal na {0} na proyekto — ito ay ganap na hindi opisyal.", "This setting is disabled": "Naka-disable ang setting na ito", "This wizard will help you configure and customize WingetUI!": "<PERSON><PERSON>lungan ka ng wizard na ito na i-configure at i-customize ang UniGetUI!", "Toggle search filters pane": "I-toggle ang pane ng mga filter sa paghahanap", "Translators": "<PERSON>ga tag<PERSON>", "Try to kill the processes that refuse to close when requested to": "<PERSON><PERSON><PERSON> patayin ang mga prosesong tumatangging magsara kapag hiniling", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Ang pag-on nito ay nagbibigay-daan sa pagbabago ng executable file na ginagamit para makipag-ugnayan sa mga package manager. Bagama't pinapayagan nito ang mas pinong pag-customize ng iyong mga proseso sa pag-install, maaari rin itong mapanganib", "Type here the name and the URL of the source you want to add, separed by a space.": "I-type dito ang pangalan at ang URL ng source na gusto mong idagdag, na pinaghihiwalay ng espasyo.", "Unable to find package": "Hindi mahanap ang package", "Unable to load informarion": "Hindi ma-load ang impormasyon", "UniGetUI collects anonymous usage data in order to improve the user experience.": "Kinokolekta ng UniGetUI ang hindi kilalang data ng paggamit upang mapabuti ang karanasan ng user.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "Kinokolekta ng UniGetUI ang hindi kilalang data ng paggamit na may tanging layunin ng pag-unawa at pagpapabuti ng karanasan ng user.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "Nakakita ang UniGetUI ng bagong desktop shortcut na maaaring awtomatikong tanggalin.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "Natukoy ng UniGetUI ang mga sumusunod na desktop shortcut na maaaring awtomatikong alisin sa mga pag-upgrade sa hinaharap", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "Nakakita ang UniGetUI ng {0} bagong desktop shortcut na maaaring awtomatikong tanggalin.", "UniGetUI is being updated...": "Ina-update ang UniGetUI...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "Ang UniGetUI ay hindi nauugnay sa alinman sa mga compatible na package manager. Ang UniGetUI ay isang independiyenteng proyekto.", "UniGetUI on the background and system tray": "UniGetUI sa background at system tray", "UniGetUI or some of its components are missing or corrupt.": "Ang UniGetUI o ang ilan sa mga bahagi nito ay nawawala o na-corrupt.", "UniGetUI requires {0} to operate, but it was not found on your system.": "Kinakailangan ng UniGetUI ang {0} upang gumana, ngunit hindi ito nahanap sa iyong system.", "UniGetUI startup page:": "Startup page ng UniGetUI:", "UniGetUI updater": "Taga-update ng UniGetUI", "UniGetUI version {0} is being downloaded.": "Dina-download ang bersyon ng UniGetUI {0}.", "UniGetUI {0} is ready to be installed.": "Handa nang i-install ang UniGetUI {0}.", "Uninstall": "I-uninstall", "Uninstall Scoop (and its packages)": "I-uninstall ang <PERSON>oop (at ang mga package nito)", "Uninstall and more": "I-uninstall at iba pa", "Uninstall and remove data": "I-uninstall at alisin ang data", "Uninstall as administrator": "I-uninstall bilang administrator", "Uninstall canceled by the user!": "Kinansela ng user ang pag-uninstall!", "Uninstall failed": "Nabigo ang pag-uninstall", "Uninstall options": "Mga opsyon sa pag-uninstall", "Uninstall package": "I-uninstall ang package", "Uninstall package, then reinstall it": "I-uninstall ang package, pagkatapos ay i-reinstall ito", "Uninstall package, then update it": "I-uninstall ang package, pagkatapos ay i-update ito", "Uninstall previous versions when updated": "I-uninstall ang mga nakaraang bersyon kapag na-update", "Uninstall selected packages": "I-uninstall ang mga napiling package", "Uninstall selection": "Napiling i-uninstall", "Uninstall succeeded": "Nagtagumpay ang pag-uninstall", "Uninstall the selected packages with administrator privileges": "I-uninstall ang mga napiling package na may mga pribilehiyo ng administrator", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Ang mga naa-uninstall na package na may pinagmulang nakalista bilang \"{0}\" ay hindi na-publish sa anumang package manager, kaya walang impormasyong magagamit upang ipakita ang tungkol sa mga ito.", "Unknown": "Hindi kilala", "Unknown size": "Hindi kilalang laki", "Unset or unknown": "Hindi nakatakda o hindi alam", "Up to date": "Napapanah<PERSON>", "Update": "I-update", "Update WingetUI automatically": "Awtomatikong i-update ang UniGetUI", "Update all": "I-update lahat", "Update and more": "Update at iba pa", "Update as administrator": "I-update bilang administrator", "Update check frequency, automatically install updates, etc.": "I-update ang dalas ng pagsusuri, awtomatikong mag-install ng mga update, atbp.", "Update checking": null, "Update date": "Petsa ng update", "Update failed": "Nabigo ang pag-update", "Update found!": "Nahanap ang update!", "Update now": "I-update ngayon", "Update options": "Mga opsyon sa pag-update", "Update package indexes on launch": "I-update ang mga package index sa paglulunsad", "Update packages automatically": "Awtomatikong i-update ang mga package", "Update selected packages": "I-update ang mga napiling package", "Update selected packages with administrator privileges": "I-update ang mga napiling package na may mga pribilehiyo ng administrator", "Update selection": "Napiling i-update", "Update succeeded": "Nagtagumpay ang pag-update", "Update to version {0}": "Update sa bersyon {0}", "Update to {0} available": "Available ang update sa {0}.", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Awtomatikong i-update ang Git portfiles ng vcpkg (nangangailangan ng naka-install ang Git)", "Updates": "Mga update", "Updates available!": "Available ang Mga Update!", "Updates for this package are ignored": "Binabalewala ang mga update para sa package na ito", "Updates found!": "Nahanap ang mga update!", "Updates preferences": "Mga kagustuhan sa pag-update", "Updating WingetUI": "Ina-update ang UniGetUI", "Url": null, "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Gamitin ang Legacy bundle na WinGet sa halip na PowerShell CMDLets", "Use a custom icon and screenshot database URL": "Gumamit ng custom na icon at URL ng database ng screenshot", "Use bundled WinGet instead of PowerShell CMDlets": "Gumamit ng naka-bundle na WinGet sa halip na PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "Gumamit ng naka-bundle na WinGet sa halip na system WinGet", "Use installed GSudo instead of UniGetUI Elevator": "Gamitin ang na-install na GSudo sa halip ng UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "<PERSON><PERSON><PERSON> ang naka-install na GSudo sa halip na ang naka-bundle", "Use system Chocolatey": "Gamitin ang system na Chocolatey", "Use system Chocolatey (Needs a restart)": "Gamitin ang system na Chocolatey (Kailangan ng restart)", "Use system Winget (Needs a restart)": "Gamitin ang system Winget (Kailangan ng restart)", "Use system Winget (System language must be set to english)": "<PERSON><PERSON><PERSON> ang WinGet (Dapat itakda ang wika ng system sa Ingles)", "Use the WinGet COM API to fetch packages": "Gamitin ang WinGet COM API para kumuha ng mga package", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Gamitin ang WinGet PowerShell Module sa halip na ang WinGet COM API", "Useful links": "Mga kapaki-pakinabang na link", "User": null, "User interface preferences": "Mga kagustuhan sa interface ng user", "User | Local": "User | Lokal", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Ang paggamit ng UniGetUI ay nagpapahiwatig ng pagtanggap ng GNU Lesser General Public License v2.1 License", "Using WingetUI implies the acceptation of the MIT License": "Ang paggamit ng UniGetUI ay nagpapahiwatig ng pagtanggap ng MIT License", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Hindi nahanap ang root ng vcpkg. Mangyaring tukuyin ang %VCPKG_ROOT% environment variable o tukuyin ito mula sa Mga Setting ng UniGetUI", "Vcpkg was not found on your system.": "Hindi nahanap ang Vcpkg sa iyong system.", "Verbose": null, "Version": "<PERSON><PERSON><PERSON>", "Version to install:": "Bersyon na i-install:", "Version:": "Bersyon:", "View GitHub Profile": "Tingnan ang GitHub Profile", "View WingetUI on GitHub": "Tingnan ang UniGetUI sa GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Tingnan ang source code ng UniGetUI. <PERSON><PERSON> doon, maaari kang mag-ulat ng mga bug o magmungkahi ng mga feature, o kahit na direktang mag-ambag sa proyekto ng UniGetUI", "View mode:": "Mode sa pag-view:", "View on UniGetUI": "Tignan sa UniGetUI", "View page on browser": "Tingnan ang page sa browser", "View {0} logs": "Tingnan ang {0} (na) log", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Hintaying makakonekta ang device sa internet bago subukang gawin ang mga gawain na nangangailangan ng koneksyon sa internet.", "Waiting for other installations to finish...": "Naghihintay para matapos ang iba pang pag-install...", "Waiting for {0} to complete...": "Hinihintay na makumpleto ang {0}...", "Warning": "<PERSON><PERSON>", "Warning!": "<PERSON><PERSON>!", "We are checking for updates.": "Sinusuri namin ang mga update.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Hindi namin mai-load ang detalyadong impormasyon tungkol sa package na ito, dahil hindi ito nakita sa alinman sa iyong mga source ng package.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Hindi namin mai-load ang detalyadong impormasyon tungkol sa package na ito, dahil hindi ito na-install mula sa isang available na package manager.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Hindi namin magawa ang {action} sa {package}. Pakisubukang muli mamaya. I-click ang \"{showDetails}\" upang makuha ang mga log mula sa installer.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Hindi namin magawa ang {action} sa {package}. Pakisubukang muli mamaya. I-click ang \"{showDetails}\" upang makuha ang mga log mula sa uninstaller.", "We couldn't find any package": "Wala kaming mahanap na kahit anong package", "Welcome to WingetUI": "Maligayang pagdating sa UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Kapag batch ang pag-install ng mga package mula sa isang bundle, i-install din ang mga na-install na mga package", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON><PERSON><PERSON> may nakitang mga bagong shortcut, awtomatikong tanggalin ang mga ito sa halip na ipakita ang dialog na ito.", "Which backup do you want to open?": "Anong backup ang gusto mong buksan?", "Which package managers do you want to use?": "Aling mga package manager ang gusto mong gamitin?", "Which source do you want to add?": "Aling source ang gusto mong idagdag?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Habang ang Winget ay maaaring gamitin sa loob ng UniGetUI, ang UniGetUI ay maaaring gamitin sa iba pang mga package manager, na maaaring nakalilito. <PERSON><PERSON>, ang UniGetUI ay idinisenyo upang gumana lamang sa Winget, ngunit ito ay hindi na totoo, at samakatuwid ang UniGetUI ay hindi kumakatawan sa kung ano ang layunin ng proyektong ito.", "WinGet could not be repaired": "Hindi ma-repair ang <PERSON>et", "WinGet malfunction detected": "May nakitang malfunction ng WinGet", "WinGet was repaired successfully": "Matagumpay na naayos ang WinGet", "WingetUI": null, "WingetUI - Everything is up to date": "UniGetI - <PERSON> la<PERSON> ay napapanahon", "WingetUI - {0} updates are available": "UniGetUI - {0} (na) update ay available", "WingetUI - {0} {1}": null, "WingetUI Homepage": "Homepage ng UniGetUI", "WingetUI Homepage - Share this link!": "Homepage ng UniGetUI - Ibahagi ang link na ito!", "WingetUI License": "Lisensya ng UniGetUI", "WingetUI Log": "Log ng UniGetUI", "WingetUI Repository": "Repository ng UniGetUI", "WingetUI Settings": "Mga Setting ng UniGetUI", "WingetUI Settings File": "Settings File ng UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "Ginagamit ng UniGetUI ang mga sumusunod na library. Kung wala sila, hindi magiging posible ang UniGetUI.", "WingetUI Version {0}": "Bersyon ng UniGetUI {0}", "WingetUI autostart behaviour, application launch settings": "Gawain ng UniGetUI sa pag-autostart, mga setting ng paglunsad ng aplikasyon", "WingetUI can check if your software has available updates, and install them automatically if you want to": "Maaaring suriin ng UniGetUI kung may available na mga update ang iyong software, at awtomatikong i-install ang mga ito kung gusto mo", "WingetUI display language:": "Ipinapakitang wika ng UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "Ang UniGetUI ay pinatakbo bilang administrator, na hindi inirerekomenda. Kapag nagpapatakbo ng UniGetUI bilang administrator, BAWAT operasyon na inilunsad mula sa UniGetUI ay magkakaroon ng mga pribilehiyo ng administrator. Magagamit mo pa rin ang program, ngunit lubos naming inirerekomenda na huwag patakbuhin ang UniGetUI na may mga pribilehiyo ng administrator.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "Ang UniGetUI ay isinalin sa higit sa 40 mga wika salamat sa mga boluntaryong tagasalin. Salamat 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "Ang UniGetUI ay hindi naisalin gamit ang machine! Ang mga sumusunod na user ay namamahala sa mga pagsasalin:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "Ang UniGetUI ay isang aplikasyon na nagpapadali sa pamamahala ng iyong software, sa pamamagitan ng pagbibigay ng isahang graphical na interface para sa iyong mga command-line package manager.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "Ang UniGetUI ay pinalitan ng pangalan upang bigyang-diin ang pagkakaiba sa pagitan ng UniGetUI (ang interface na ginagamit mo ngayon) at WinGet (isang package manager na binuo ng Microsoft kung saan hindi ako nauugnay)", "WingetUI is being updated. When finished, WingetUI will restart itself": "Ina-update ang UniGetUI. Kapag natapos na, ang UniGetUI ay magre-restart ", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "Ang UniGetUI ay libre, at ito ay magiging libre magpakailanman. Walang mga ad, walang credit card, walang premium na bersyon. 100% libre, magpakailanman.", "WingetUI log": "Log ng UniGetUI", "WingetUI tray application preferences": "Mga kagustuhan sa application ng tray ng UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "Ginagamit ng UniGetUI ang mga sumusunod na library. Kung wala sila, hindi magiging posible ang UniGetUI.", "WingetUI version {0} is being downloaded.": "Dina-download ang bersyon ng UniGetUI {0}.", "WingetUI will become {newname} soon!": "Ang WingetUI ay magiging {newname} sa susunod!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "Hindi titingnan ng UniGetUI ang mga update sa pana-panahon. <PERSON><PERSON><PERSON><PERSON> pa rin ang mga ito sa paglulunsad, ngunit hindi ka babalaan tungkol sa mga ito.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "Magpapakita ang UniGetUI ng UAC prompt sa tuwing nangangailangan ang isang package ng elevation upang mai-install.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "Malapit nang pangalanan ang WingetUI na {newname}. Hindi ito kumakatawan sa anumang pagbabago sa aplikasyon. Ako (ang developer) ay magpapatuloy sa pagbuo ng proyektong ito tulad ng ginagawa ko ngayon, ngunit sa ilalim ng ibang pangalan.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "Hindi magiging posible ang UniGetUI kung wala ang tulong ng aming mahal na mga kontribyutor. Tingnan ang kanilang mga profile sa GitHub, hindi magiging posible ang UniGetUI kung wala sila!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "Hindi magiging posible ang UniGetUI kung wala ang tulong ng mga kontribyutor. Salamat sa inyong lahat 🥳", "WingetUI {0} is ready to be installed.": "Handa nang i-install ang UniGetUI {0}.", "Write here the process names here, separated by commas (,)": "<PERSON><PERSON><PERSON> dito ang mga pangalan ng mga proseso dito, pinaghihiwalay ng kuwit (,)", "Yes": "Oo", "You are logged in as {0} (@{1})": "Naka-log in ka bilang {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "<PERSON><PERSON><PERSON> mong baguhin ang gawi na ito sa mga setting ng seguridad ng UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "<PERSON><PERSON><PERSON> mong tukuyin ang mga command na patatakbuhin bago o pagkatapos ma-install, ma-update o ma-uninstall ang package na ito. Tatakbo ang mga ito sa command prompt, kaya gagana rito ang mga CMD script.", "You have currently version {0} installed": "<PERSON><PERSON><PERSON><PERSON> kang naka-install na bersyon {0}.", "You have installed WingetUI Version {0}": "Naka-install ang Bersyon ng UniGetUI {0}", "You may lose unsaved data": "<PERSON><PERSON><PERSON> kang mawalan ng hindi na-save na data", "You may need to install {pm} in order to use it with WingetUI.": "Maaaring kailanganin mong i-install ang {pm} upang magamit ito sa UniGetUI.", "You may restart your computer later if you wish": "<PERSON><PERSON><PERSON> mong i-restart ang iyong computer sa ibang pagkakataon kung gusto mo", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "<PERSON>ng beses ka lang ipo-prompt, at ibibigay ang mga karapatan ng administrator sa mga package na humihiling sa kanila.", "You will be prompted only once, and every future installation will be elevated automatically.": "Isang beses ka lang ipo-prompt, at ang bawat pag-install sa hinaharap ay awtomatikong i-elevate.", "You will likely need to interact with the installer.": "Malamang na kakailanganin mong makipag-iteract sa installer.", "[RAN AS ADMINISTRATOR]": "PINATAKBO BILANG ADMINISTRATOR", "buy me a coffee": "bilhan mo ako ng kape", "extracted": "na-extract na", "feature": "katangian", "formerly WingetUI": "dating WingetUI", "homepage": null, "install": "i-install", "installation": "pag-install", "installed": "na-install na", "installing": "nag-iinstall", "library": null, "mandatory": "kinakailangan", "option": "opsyon", "optional": "opsyonal", "uninstall": "i-uninstall", "uninstallation": "nag-uninstall", "uninstalled": "na-uninstall na", "uninstalling": "nag-uninstall", "update(noun)": "mag-update", "update(verb)": "i-update", "updated": "na-update na", "updating": "nag-a-update", "version {0}": "be<PERSON><PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": "<PERSON><PERSON><PERSON><PERSON> naka-lock ang mga opsyon sa pag-install ng {0} dahil sinusunod ng {0} ang mga default na opsyon sa pag-install.", "{0} Uninstallation": "Nag-uninstall ang {0}", "{0} aborted": "Na-abort ang {0}", "{0} can be updated": "Maaaring ma-update ang {0}.", "{0} can be updated to version {1}": "Maaaring ma-update ang {0} sa bersyon {1}", "{0} days": "{0} na araw", "{0} desktop shortcuts created": "{0} (na) desktop shortcut ang nagawa", "{0} failed": "Nabigo ang {0}", "{0} has been installed successfully.": "Matagumpay na na-install ang {0}.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "Matagumpay na na-install ang {0}. Inirerekomenda na i-restart ang UniGetUI upang matapos ang pag-install", "{0} has failed, that was a requirement for {1} to be run": "Ang {0} ay nabigo, iyon ay isang kinakailangan para sa {1} na patakbuhin", "{0} homepage": "Homepage ng {0}", "{0} hours": "{0} (na) oras", "{0} installation": "Pag-install ng {0}", "{0} installation options": "Mga opsyon sa pag-install ng {0}", "{0} installer is being downloaded": "Ang {0} installer ay dina-download", "{0} is being installed": "Ini-install ang {0}", "{0} is being uninstalled": "Ina-uninstall ang {0}", "{0} is being updated": "Ang {0} ay ina-update", "{0} is being updated to version {1}": "Ang {0} ay ina-update sa bersyon {1}", "{0} is disabled": "{0} ay naka-disable", "{0} minutes": "{0} (na) minuto", "{0} months": "{0} (na) buwan", "{0} packages are being updated": "{0} (na) package ay ina-update", "{0} packages can be updated": "Maaaring ma-update ang {0} (na) mga package", "{0} packages found": "{0} (na) package ang nahanap", "{0} packages were found": "{0} (na) package ang nahanap", "{0} packages were found, {1} of which match the specified filters.": "{0} (na) package ang nahanap, {1} kung saan tumutugma sa tinukoy na mga filter.", "{0} selected": "{0} na napili", "{0} settings": "mga setting ng {0}", "{0} status": "<PERSON><PERSON><PERSON> ng {0}", "{0} succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON> ang {0}.", "{0} update": "{0} na update", "{0} updates are available": "{0} (na) mga update ay available", "{0} was {1} successfully!": "Matagumpay na naging {1} ang {0}!", "{0} weeks": "{0} (na) linggo", "{0} years": "{0} (na) taon", "{0} {1} failed": "Nabigo ang {0} {1}", "{package} Installation": "Pag-install ng {package}", "{package} Uninstall": "Pag-uninstall ng {package}", "{package} Update": "Pag-update ng {package}", "{package} could not be installed": "Hindi ma-install ang {package}", "{package} could not be uninstalled": "Hindi ma-uninstall ang {package}", "{package} could not be updated": "Hindi ma-update ang {package}", "{package} installation failed": "Nabigo ang pag-install ng {package}", "{package} installer could not be downloaded": "Hindi ma-download ang installer ng {package}", "{package} installer download": "Pag-download ng installer ng {package}", "{package} installer was downloaded successfully": "Matagumpay na na-download ang installer ng {package}", "{package} uninstall failed": "Nabigo ang pag-uninstall ng {package}", "{package} update failed": "Nabigo ang pag-update ng {package}", "{package} update failed. Click here for more details.": "Nabigo ang pag-update ng {package}. Mag-click dito para sa higit pang mga detalye.", "{package} was installed successfully": "Matagumpay na na-install ang {package}", "{package} was uninstalled successfully": "Matagumpay na na-uninstall ang {package}", "{package} was updated successfully": "Matagumpay na na-update ang {package}", "{pcName} installed packages": "Mga naka-install na package ng {pcName}", "{pm} could not be found": "Hindi mahanap ang {pm}", "{pm} found: {state}": "{pm} natagpuan: {state}", "{pm} is disabled": "{pm} ay naka-disable", "{pm} is enabled and ready to go": "{pm} ay naka-enable at handa na", "{pm} package manager specific preferences": "Mga partikular na kagustuhan sa package manager ng {pm}", "{pm} preferences": "<PERSON><PERSON> kagustuhan ng {pm}", "{pm} version:": "<PERSON><PERSON><PERSON> ng {pm}:", "{pm} was not found!": "Hindi mahanap ang {pm}!"}