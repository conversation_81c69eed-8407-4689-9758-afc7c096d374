<?xml version="1.0" encoding="utf-8" ?>
<Window
    x:Class="UniGetUI.Interface.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:animations="using:CommunityToolkit.WinUI.Animations"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:services="using:UniGetUI.Services"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    xmlns:winex="using:WinUIEx"
    Title="UniGetUI"
    mc:Ignorable="d">

    <Window.SystemBackdrop>
        <MicaBackdrop Kind="Base" />
    </Window.SystemBackdrop>
    <Grid
        x:Name="MainContentGrid"
        x:FieldModifier="public"
        Visibility="Visible">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TitleBar
            x:Name="TitleBar"
            Title="UniGetUI"
            Grid.Row="0"
            Margin="0,0,0,-4"
            HorizontalContentAlignment="Center"
            BackRequested="TitleBar_OnBackRequested"
            IsBackButtonVisible="False"
            IsPaneToggleButtonVisible="True"
            PaneToggleRequested="TitleBar_PaneToggleRequested"
            SizeChanged="TitleBar_SizeChanged"
            Visibility="Collapsed">
            <!--TitleBar.IconSource>
                <ImageIconSource ImageSource="ms-appx:///Assets/Images/icon.png" />
            </-TitleBar.IconSource-->
            <TitleBar.Content>
                <AutoSuggestBox
                    x:Name="GlobalSearchBox"
                    Width="400"
                    Height="32"
                    BorderBrush="{ThemeResource ButtonBorderBrush}"
                    HorizontalAlignment="Stretch"
                    x:FieldModifier="public"
                    QueryIcon="Find" />
            </TitleBar.Content>
            <TitleBar.RightHeader>
                <services:UserAvatar Margin="0,0,0,0" />
            </TitleBar.RightHeader>
        </TitleBar>
        <StackPanel
            Grid.Row="1"
            Margin="0"
            Padding="0"
            BorderThickness="0"
            Spacing="0">
            <InfoBar
                Name="UpdatesBanner"
                Margin="0,0,0,4"
                x:FieldModifier="public"
                BorderThickness="0,1,0,1"
                CornerRadius="0"
                IsOpen="False"
                Visibility="{x:Bind UpdatesBanner.IsOpen, Mode=OneWay}" />
            <InfoBar
                Name="ErrorBanner"
                Margin="0,0,0,4"
                x:FieldModifier="public"
                BorderThickness="0,1,0,1"
                CornerRadius="0"
                IsOpen="False"
                Severity="Error"
                Visibility="{x:Bind ErrorBanner.IsOpen, Mode=OneWay}" />
            <InfoBar
                Name="WinGetWarningBanner"
                Margin="0,0,0,4"
                x:FieldModifier="public"
                BorderThickness="0,1,0,1"
                CornerRadius="0"
                IsOpen="False"
                Severity="Warning"
                Visibility="{x:Bind WinGetWarningBanner.IsOpen, Mode=OneWay}" />
            <InfoBar
                Name="TelemetryWarner"
                Margin="0,0,0,4"
                x:FieldModifier="public"
                BorderThickness="0,1,0,1"
                CornerRadius="0"
                IsOpen="False"
                Severity="Informational"
                Visibility="Collapsed" />
        </StackPanel>
        <Frame Name="MainContentFrame" Grid.Row="2">
            <Grid Background="{StaticResource ProgressBarBorderThemeBrush}" Visibility="Visible">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Canvas
                    x:Name="LaunchBackground"
                    Grid.Row="0"
                    Grid.RowSpan="5"
                    Grid.Column="0"
                    Grid.ColumnSpan="3"
                    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}" />
                <StackPanel
                    Grid.Row="1"
                    Grid.Column="1"
                    Padding="0,20,0,20"
                    Orientation="Horizontal">
                    <Border
                        Margin="-500,0,20,0"
                        BorderBrush="{ThemeResource ApplicationPageBackgroundThemeBrush}"
                        BorderThickness="500,0,0,0"
                        Canvas.ZIndex="1">
                        <animations:Explicit.Animations>
                            <animations:AnimationSet x:Name="InAnimation_Border">
                                <animations:TranslationAnimation
                                    EasingMode="EaseOut"
                                    From="225,0"
                                    To="0,0"
                                    Duration="0:0:0.7" />
                            </animations:AnimationSet>
                        </animations:Explicit.Animations>
                        <Image Width="96">
                            <Image.Source>
                                <BitmapImage UriSource="ms-appx:///Assets/Images/icon_unsquare.png" />
                            </Image.Source>
                        </Image>
                    </Border>
                    <Border
                        Margin="0"
                        Padding="0,10"
                        HorizontalAlignment="Right">
                        <animations:Explicit.Animations>
                            <animations:AnimationSet x:Name="InAnimation_Text">
                                <animations:TranslationAnimation
                                    EasingMode="EaseOut"
                                    From="-225,0"
                                    To="0,0"
                                    Duration="0:0:0.7" />
                                <animations:OpacityAnimation
                                    EasingMode="EaseOut"
                                    From="0"
                                    To="1"
                                    Duration="0:0:0.7" />
                            </animations:AnimationSet>
                        </animations:Explicit.Animations>
                        <StackPanel VerticalAlignment="Center" Orientation="Vertical">
                            <TextBlock
                                Name="UniGetUITitle"
                                Margin="0,-10,0,-20"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                FontFamily="Segoe UI Variable Display"
                                FontSize="90"
                                FontWeight="ExtraBlack"
                                HorizontalTextAlignment="Right"
                                Text="UniGetUI" />
                            <widgets:TranslatedTextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Bottom"
                                FontFamily="Segoe UI Variable"
                                FontSize="20"
                                Text="Package management made easy" />
                        </StackPanel>
                    </Border>
                </StackPanel>
                <ProgressBar
                    Name="LoadingIndicator"
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="0,0,5,0"
                    VerticalAlignment="Bottom"
                    Foreground="#08a9c3"
                    IsIndeterminate="True"
                    Visibility="Visible" />
            </Grid>
        </Frame>

        <TeachingTip
            x:Name="DismissableNotification"
            x:FieldModifier="public"
            PlacementMargin="20"
            PreferredPlacement="Auto" />

    </Grid>
</Window>
