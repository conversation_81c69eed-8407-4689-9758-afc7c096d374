<?xml version="1.0" encoding="utf-8" ?>
<Page
    x:Class="UniGetUI.Pages.SettingsPages.GeneralPages.Updates"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:CommunityToolkit.WinUI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="using:UniGetUI.Pages.SettingsPages.GeneralPages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:widgets="using:UniGetUI.Interface.Widgets"
    Background="Transparent"
    mc:Ignorable="d">

    <ScrollViewer
        x:Name="Scroller"
        Margin="0,0,-8,0"
        Padding="0,0,8,0"
        HorizontalAlignment="Stretch"
        VerticalAlignment="Stretch"
        HorizontalContentAlignment="Center"
        VerticalContentAlignment="Center">
        <StackPanel Orientation="Vertical" Spacing="0">
            <TextBlock
                Padding="4,32,4,8"
                Style="{ThemeResource NavigationViewItemHeaderTextStyle}"
                Text="Update checking" />

            <widgets:CheckboxCard
                x:Name="DisableAutoCheckForUpdates"
                CornerRadius="8,8,0,0"
                SettingName="DisableAutoCheckforUpdates"
                Text="Check for package updates periodically" />

            <widgets:ComboboxCard
                x:Name="UpdatesCheckIntervalSelector"
                BorderThickness="1,0"
                CornerRadius="0,0,8,8"
                IsEnabled="{x:Bind DisableAutoCheckForUpdates._checkbox.IsOn, Mode=OneWay}"
                SettingName="UpdatesCheckInterval"
                Text="Check for updates every:"
                ValueChanged="ShowRestartBanner" />

            <TextBlock
                Padding="4,32,4,8"
                Style="{ThemeResource NavigationViewItemHeaderTextStyle}"
                Text="Automatic updates" />

            <widgets:CheckboxCard
                x:Name="AutomaticallyUpdatePackages"
                CornerRadius="8,8,0,0"
                IsEnabled="{x:Bind DisableAutoCheckForUpdates._checkbox.IsOn, Mode=OneWay}"
                SettingName="AutomaticallyUpdatePackages"
                Text="Install available updates automatically" />

            <widgets:CheckboxCard
                BorderThickness="1,0"
                CornerRadius="0"
                ForceInversion="True"
                IsEnabled="{x:Bind AutomaticallyUpdatePackages._checkbox.IsOn, Mode=OneWay}"
                SettingName="DisableAUPOnMeteredConnections"
                Text="Do not automatically install updates when the network connection is metered" />

            <widgets:CheckboxCard
                CornerRadius="0,0,8,8"
                ForceInversion="True"
                IsEnabled="{x:Bind AutomaticallyUpdatePackages._checkbox.IsOn, Mode=OneWay}"
                SettingName="DisableAUPOnBatterySaver"
                Text="Do not automatically install updates when the battery saver is on" />


            <widgets:TranslatedTextBlock
                Margin="4,32,4,8"
                FontWeight="SemiBold"
                Text="Related settings" />

            <controls:SettingsCard
                BorderThickness="1,1,1,0"
                Click="OperationsSettingsButton_Click"
                CornerRadius="8,8,0,0"
                IsClickEnabled="True">
                <controls:SettingsCard.Header>
                    <widgets:TranslatedTextBlock Text="Package operation preferences" />
                </controls:SettingsCard.Header>
                <controls:SettingsCard.Description>
                    <widgets:TranslatedTextBlock Text="Change how UniGetUI handles install, update and uninstall operations." />
                </controls:SettingsCard.Description>
                <controls:SettingsCard.HeaderIcon>
                    <widgets:LocalIcon Icon="Update" />
                </controls:SettingsCard.HeaderIcon>
            </controls:SettingsCard>

            <controls:SettingsCard
                Click="AdminButton_Click"
                CornerRadius="0,0,8,8"
                IsClickEnabled="True">
                <controls:SettingsCard.Header>
                    <widgets:TranslatedTextBlock Text="Administrator rights and other dangerous settings" />
                </controls:SettingsCard.Header>
                <controls:SettingsCard.Description>
                    <widgets:TranslatedTextBlock Text="Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc." />
                </controls:SettingsCard.Description>
                <controls:SettingsCard.HeaderIcon>
                    <widgets:LocalIcon Icon="UAC" />
                </controls:SettingsCard.HeaderIcon>
            </controls:SettingsCard>

        </StackPanel>
    </ScrollViewer>
</Page>
