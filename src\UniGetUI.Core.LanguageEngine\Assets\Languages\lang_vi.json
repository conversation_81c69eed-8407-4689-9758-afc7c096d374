{"\"{0}\" is a local package and can't be shared": "\"{0}\" là một gói cục bộ và không thể chia sẻ", "\"{0}\" is a local package and does not have available details": "\"{0}\" là một gói cục bộ và không có chi tiết sẵn có", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" là một gói cục bộ và không tương thích với tính năng này", "(Last checked: {0})": "(<PERSON><PERSON><PERSON> kiểm tra cuối: {0})", "(Number {0} in the queue)": "(Số {0} trong hàng đợi)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@txavlog, @legendsjoon, @van<PERSON><PERSON><PERSON>y, @aethervn2309", "0 packages found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói nào", "0 updates found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bản cập nhật nào", "1 - Errors": "1 - Lỗi", "1 day": "1 ngày", "1 hour": "1 giờ", "1 month": "1 tháng", "1 package was found": "Đã tìm thấy 1 gói", "1 update is available": "Có 1 bản cập nh<PERSON>t có sẵn", "1 week": "1 tuần", "1 year": "1 năm", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. <PERSON><PERSON><PERSON><PERSON> hướng đến trang \"{0}\" hoặc \"{1}\".", "2 - Warnings": "2 - <PERSON><PERSON><PERSON> b<PERSON>o", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON> gói hàng bạn muốn thêm vào bộ sản phẩm và chọn hộp kiểm ở vị trí ngoài cùng bên trái của chúng.", "3 - Information (less)": "3 - <PERSON><PERSON><PERSON><PERSON> tin (<PERSON><PERSON> <PERSON>)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON><PERSON> <PERSON><PERSON><PERSON> gói hàng bạn muốn thêm vào bộ sản phẩm đã đư<PERSON><PERSON> chọn, hãy tìm và nhấp vào tùy chọn \"{0}\" trên thanh công cụ.", "4 - Information (more)": "4 - T<PERSON><PERSON><PERSON> tin (thêm)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON><PERSON><PERSON> gói hàng của bạn sẽ được thêm vào bộ sản phẩm. Bạn có thể tiếp tục thêm các gói hàng khác hoặc xuất bộ sản phẩm.", "5 - information (debug)": "5 - <PERSON><PERSON><PERSON><PERSON> tin (gỡ lỗi)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "<PERSON><PERSON>t trình quản lý thư viện C/C++ phổ biến. Đ<PERSON>y đủ các thư viện C/C++ và các tiện ích liên quan đến C/C++ kh<PERSON><PERSON>. <br><PERSON><PERSON> gồ<PERSON>: <b><PERSON><PERSON><PERSON> thư viện C/C++ và các tiện ích liên quan.</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "<PERSON><PERSON><PERSON> kho chứa đầy đủ các công cụ và tệp thực thi được thiết kế với hệ sinh thái .NET của Microsoft. <br><PERSON><PERSON> gồ<PERSON>: <b> <PERSON><PERSON><PERSON> công cụ và tập lệnh liên quan đến .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "<PERSON><PERSON><PERSON> kho lưu trữ gồm đầy đủ các công cụ được thiết kế dành cho hệ sinh thái .NET của Microsoft. <br><PERSON><PERSON> gồ<PERSON>: <b>.NET related Tools</b>", "A restart is required": "<PERSON><PERSON><PERSON> cầu khởi động lại", "Abort install if pre-install command fails": "<PERSON><PERSON>y cài đặt nếu lệnh tiền cài đặt thất bại", "Abort uninstall if pre-uninstall command fails": "Hủy gỡ cài đặt nếu lệnh tiền gỡ cài đặt thất bại", "Abort update if pre-update command fails": "<PERSON><PERSON><PERSON> cập nhật nếu lệnh tiền cập nhật thất bại", "About": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "About Qt6": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> về Qt6", "About WingetUI": "G<PERSON>ớ<PERSON> thiệu về UniGetUI", "About WingetUI version {0}": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u về UniGetUI phiên bản {0}", "About the dev": "<PERSON><PERSON><PERSON><PERSON> thiệu về nhà phát triển", "Accept": "<PERSON><PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "<PERSON><PERSON><PERSON> động khi nhấn đúp vào các <PERSON>, <PERSON><PERSON> các cài đặt thành công", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "<PERSON><PERSON><PERSON><PERSON> nguồn vào {0}", "Add a timestamp to the backup file names": "<PERSON>hê<PERSON> dấu thời gian vào tên tệp sao lưu", "Add a timestamp to the backup files": "<PERSON><PERSON><PERSON><PERSON> dấu thời gian vào các tệp sao lưu", "Add packages or open an existing bundle": "<PERSON>hê<PERSON> các gói hoặc mở một gói đã tồn tại", "Add packages or open an existing package bundle": "Thêm gói hoặc mở một nhóm có sẵn", "Add packages to bundle": "<PERSON>h<PERSON><PERSON> gói hàng vào bộ sản phẩm", "Add packages to start": "<PERSON><PERSON><PERSON><PERSON> gói để bắt đầu", "Add selection to bundle": "Thêm gói lựa chọn vào nhóm", "Add source": "<PERSON><PERSON><PERSON><PERSON>", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "<PERSON>h<PERSON><PERSON> các bản cập nhật bị lỗi với thông báo \"không tìm thấy bản cập nhật phù hợp\" vào danh sách cập nhật bị bỏ qua", "Adding source {source}": "<PERSON><PERSON> thêm ngu<PERSON> {source}", "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON><PERSON> {source} v<PERSON><PERSON> {manager}", "Addition succeeded": "<PERSON><PERSON><PERSON><PERSON> thành công", "Administrator privileges": "<PERSON><PERSON><PERSON><PERSON> quản trị viên", "Administrator privileges preferences": "<PERSON><PERSON><PERSON> chọn quyền quản trị viên", "Administrator rights": "<PERSON><PERSON><PERSON><PERSON> quản trị viên", "Administrator rights and other dangerous settings": "<PERSON><PERSON><PERSON><PERSON> quản trị và các thiết lập nguy hiểm kh<PERSON>c", "Advanced options": "<PERSON><PERSON><PERSON> ch<PERSON>n nâng cao", "All files": "<PERSON><PERSON><PERSON> cả tệp tin", "All versions": "<PERSON><PERSON><PERSON> cả phi<PERSON>n bản", "Allow changing the paths for package manager executables": "<PERSON> phép thay đổi đường dẫn của các tệp thực thi trình quản lý gói", "Allow custom command-line arguments": "Cho phép sử dụng đối số dòng lệnh tùy chỉnh", "Allow importing custom command-line arguments when importing packages from a bundle": "<PERSON> phép nhập các đối số dòng lệnh tùy chỉnh khi nhập gói từ một gói tổng hợp", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "<PERSON> phép nhập các lệnh tùy chỉnh trước và sau khi cài đặt khi nhập gói phần mềm từ một bộ", "Allow package operations to be performed in parallel": "<PERSON> phép thực hiện các hoạt động gói song song", "Allow parallel installs (NOT RECOMMENDED)": "Cho phép cài đặt song song (KHÔNG KHUYẾN KHÍCH)", "Allow pre-release versions": "<PERSON> phép phiên bản phát hành trước", "Allow {pm} operations to be performed in parallel": "Cho phép {pm} thao tác đ<PERSON>c thực hiện song song", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "<PERSON><PERSON><PERSON><PERSON> ra, bạ<PERSON> cũng có thể cài đặt {0} bằng cách chạy lệnh sau trong lời nhắc Windows PowerShell:", "Always elevate {pm} installations by default": "Luôn sử dụng trình cài đặt {pm} nâng cao theo mặc định", "Always run {pm} operations with administrator rights": "<PERSON><PERSON><PERSON> chạy {pm} thao tác dưới quyền quản trị viên", "An error occurred": "Oops! Có lỗi xảy ra", "An error occurred when adding the source: ": "<PERSON><PERSON> lỗi khi thêm nguồn", "An error occurred when attempting to show the package with Id {0}": "C<PERSON> lỗi khi cố gắng hiển thị gói có Id {0}", "An error occurred when checking for updates: ": "<PERSON><PERSON> lỗi khi kiểm tra cập nhật", "An error occurred while loading a backup: ": "<PERSON><PERSON> xảy ra lỗi khi tải bản sao lưu: ", "An error occurred while logging in: ": "<PERSON><PERSON> xảy ra lỗi khi đăng nhập: ", "An error occurred while processing this package": "<PERSON><PERSON> lỗi xảy ra khi xử lý gói này", "An error occurred:": "Đã xảy ra lỗi:", "An interal error occurred. Please view the log for further details.": "<PERSON><PERSON> xảy ra lỗi nội bộ. <PERSON><PERSON> lòng xem nhật ký để biết thêm chi tiết.", "An unexpected error occurred:": "<PERSON><PERSON> xảy ra lỗi không mong muốn:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "<PERSON><PERSON> xảy ra sự cố không mong muốn khi cố gắng sửa chữa WinGet. <PERSON><PERSON> lòng thử lại sau", "An update was found!": "<PERSON><PERSON><PERSON> bản cập nhật đã đư<PERSON><PERSON> tìm thấy!", "Android Subsystem": "<PERSON><PERSON> th<PERSON>ng <PERSON>", "Another source": "Một ng<PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Bất kỳ lối tắt mới nào được tạo trong quá trình cài đặt hoặc cập nhật sẽ tự động bị xóa, thay vì hiển thị lời nhắc xác nhận lần đầu tiên chúng được phát hiện.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Bất kỳ lối tắt nào được tạo hoặc chỉnh sửa ngoài UniGetUI sẽ bị bỏ qua. Bạn có thể thêm chúng thông qua nút {0}.", "Any unsaved changes will be lost": "<PERSON><PERSON><PERSON> thay đổi chưa lưu sẽ bị mất", "App Name": "<PERSON><PERSON><PERSON> dụng", "Appearance": "<PERSON><PERSON><PERSON>", "Application theme, startup page, package icons, clear successful installs automatically": "Chủ đề ứng dụng, trang khởi động, biể<PERSON> tượ<PERSON> gó<PERSON>, tự động xóa các cài đặt thành công", "Application theme:": "<PERSON><PERSON><PERSON> dụng", "Apply": "<PERSON><PERSON>", "Architecture to install:": "Kiến trúc cài đặt: ", "Are these screenshots wron or blurry?": "<PERSON><PERSON><PERSON> ảnh chụp màn hình này có sai hoặc mờ không?", "Are you really sure you want to enable this feature?": "Bạn có thật sự chắc chắn muốn bật tính năng này không?", "Are you sure you want to create a new package bundle? ": "Bạn có chắc chắn muốn tạo một nhóm gói mới không?", "Are you sure you want to delete all shortcuts?": "Bạn có chắc chắn muốn xóa tất cả các lối tắt không?", "Are you sure?": "Bạn có chắc không?", "Ascendant": "<PERSON><PERSON><PERSON><PERSON> tiến", "Ask for administrator privileges once for each batch of operations": "<PERSON><PERSON><PERSON> cầu đặc quyền của quản trị viên một lần cho mỗi nhóm hành động", "Ask for administrator rights when required": "<PERSON><PERSON><PERSON> cầu quyền quản trị viên khi cần thiết", "Ask once or always for administrator rights, elevate installations by default": "<PERSON><PERSON><PERSON> cầu một lần hoặc luôn luôn quyền quản trị viên, cài đặt nâng cao theo mặc định", "Ask only once for administrator privileges": "Chỉ yêu cầu quyền quản trị một lần", "Ask only once for administrator privileges (not recommended)": "Chỉ yêu cầu một lần đối với các đặc quyền của quản trị viên (không được khuyến nghị)", "Ask to delete desktop shortcuts created during an install or upgrade.": "<PERSON><PERSON><PERSON> cầu x<PERSON>a các lối tắt trên màn hình được tạo ra trong quá trình cài đặt hoặc nâng cấp.", "Attention required": "<PERSON><PERSON><PERSON> ch<PERSON>", "Authenticate to the proxy with an user and a password": "<PERSON><PERSON><PERSON> thực với proxy bằng tên người dùng và mật khẩu", "Author": "Tác g<PERSON>", "Automatic desktop shortcut remover": "Trình gỡ lối tắt trên màn hình tự động", "Automatically save a list of all your installed packages to easily restore them.": "Tự động lưu một danh sách tất cả các gói bạn đã cài đặt để dễ dàng khôi phục chúng sau này.", "Automatically save a list of your installed packages on your computer.": "Tự động lưu danh sách những gói đã cài đặt trên máy tính của bạn.", "Autostart WingetUI in the notifications area": "Tự động chạy WingetUI trong khu vực thông báo", "Available Updates": "<PERSON><PERSON><PERSON> nhật có sẵn", "Available updates: {0}": "<PERSON><PERSON><PERSON> nh<PERSON>t khả dụng: {0}", "Available updates: {0}, not finished yet...": "<PERSON><PERSON><PERSON> nh<PERSON>t kh<PERSON> dụng: {0}, ch<PERSON><PERSON> hoàn tất...", "Backing up packages to GitHub Gist...": "Đang sao lưu c<PERSON>c gói lên GitHub Gist...", "Backup": "<PERSON><PERSON> lư<PERSON>", "Backup Failed": "<PERSON><PERSON> <PERSON><PERSON><PERSON> thất b<PERSON>i", "Backup Successful": "<PERSON><PERSON> l<PERSON><PERSON> thành công", "Backup and Restore": "Sao lư<PERSON> và Khôi phục", "Backup installed packages": "<PERSON>o l<PERSON><PERSON> các gói đã cài đặt", "Backup location": "Vị trí sao lưu", "Become a contributor": "Trở thành người đóng góp", "Become a translator": "Trở thành người phiên dịch", "Begin the process to select a cloud backup and review which packages to restore": "B<PERSON>t đầu quá trình chọn bản sao lưu trên đám mây và xem xét các gói cần khôi phục", "Beta features and other options that shouldn't be touched": "<PERSON><PERSON><PERSON> t<PERSON>h năng beta và lựa chọn khác không nên điều chỉnh", "Both": "<PERSON><PERSON> hai", "Bundle security report": "<PERSON><PERSON><PERSON> hợ<PERSON> b<PERSON>o c<PERSON>o b<PERSON>o mật", "But here are other things you can do to learn about WingetUI even more:": "<PERSON><PERSON> nhiên đây là những điều khác bạn có thể làm để tìm hiểu thêm về WingetUI: ", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Bằng cách tắt trình quản lý gói, bạn sẽ không thể xem hoặc cập nhật các gói của nó nữa.", "Cache administrator rights and elevate installers by default": "<PERSON><PERSON><PERSON> trữ quyền quản trị viên và trình cài đặt nâng cao theo mặc định", "Cache administrator rights, but elevate installers only when required": "<PERSON><PERSON><PERSON> trữ quyền quản trị viên, nh<PERSON><PERSON> chỉ cấp quyền cho trình cài đặt nâng cao khi được yêu cầu", "Cache was reset successfully!": "Cache đã đư<PERSON><PERSON> xóa thành công!", "Can't {0} {1}": "<PERSON><PERSON><PERSON><PERSON> thể {0} {1}", "Cancel": "<PERSON><PERSON><PERSON>", "Cancel all operations": "<PERSON><PERSON><PERSON> tất cả các hoạt động", "Change backup output directory": "<PERSON><PERSON> đ<PERSON>i thư mục sao lưu", "Change default options": "Thay đổi tùy chọn mặc định", "Change how UniGetUI checks and installs available updates for your packages": "Thay đổi cách UniGetUI kiểm tra và cài đặt các bản cập nhật có sẵn cho gói của bạn", "Change how UniGetUI handles install, update and uninstall operations.": "Thay đổi cách UniGetUI xử lý các thao tác cài đặt, cập nhật và gỡ cài đặt.", "Change how UniGetUI installs packages, and checks and installs available updates": "Thay đổi cách UniGetUI cài đặt các gói và kiểm tra, cài đặt các bản cập nhật có sẵn.", "Change how operations request administrator rights": "<PERSON>hay đổi cách các thao tác yêu cầu quyền quản trị viên", "Change install location": "Thay đổi vị trí cài đặt", "Change this": "<PERSON><PERSON> đ<PERSON>i mục này", "Change this and unlock": "Thay đổi mục này và mở khóa", "Check for package updates periodically": "<PERSON><PERSON><PERSON> tra cập nhật g<PERSON><PERSON> đ<PERSON>nh k<PERSON>", "Check for updates": "<PERSON><PERSON><PERSON> tra bản cập nh<PERSON>t", "Check for updates every:": "<PERSON><PERSON><PERSON> tra cập nhật mỗi: ", "Check for updates periodically": "<PERSON><PERSON><PERSON> tra cập nh<PERSON>t đ<PERSON><PERSON>", "Check for updates regularly, and ask me what to do when updates are found.": "Th<PERSON><PERSON><PERSON> xuyên kiểm tra các bản cập nhật, và hỏi tôi làm gì với từng bản cập nhật", "Check for updates regularly, and automatically install available ones.": "Thườ<PERSON> xuyên kiểm tra cập nhật và tự động cài đặt những bản có sẵn!", "Check out my {0} and my {1}!": "<PERSON><PERSON><PERSON> tra {0} và {1} của tôi!", "Check out some WingetUI overviews": "<PERSON><PERSON><PERSON> tra một số đánh giá UniGetUI", "Checking for other running instances...": "<PERSON><PERSON> kiểm tra các trường hợp đang chạy khác...", "Checking for updates...": "<PERSON><PERSON> kiểm tra cập nhật...", "Checking found instace(s)...": "<PERSON><PERSON> kiểm tra (các) phiên bản được tìm thấy....", "Choose how many operations shouls be performed in parallel": "<PERSON><PERSON>n số lượng hoạt động cần đ<PERSON><PERSON><PERSON> thực hiện song song", "Clear cache": "<PERSON><PERSON><PERSON> bộ nhớ đệm", "Clear finished operations": "<PERSON><PERSON><PERSON> các thao tác đã hoàn tất", "Clear selection": "<PERSON>óa l<PERSON>a ch<PERSON>n", "Clear successful operations": "Dọn dẹp các hoạt động thành công", "Clear successful operations from the operation list after a 5 second delay": "<PERSON><PERSON><PERSON> các thao tác thành công khỏi danh sách thao tác sau khi trễ 5 giây", "Clear the local icon cache": "<PERSON><PERSON><PERSON> bộ đệm biểu tượ<PERSON> cục bộ", "Clearing Scoop cache - WingetUI": "Dọn dẹp bộ nhớ đệm <PERSON> - WingetU", "Clearing Scoop cache...": "<PERSON><PERSON> x<PERSON>a bộ nhớ Scoop....", "Click here for more details": "<PERSON><PERSON>m vào đây để biết thêm chi tiết", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Bấm vào <PERSON>à<PERSON> đặt để bắt đầu quá trình cài đặt. Nếu bạn bỏ qua quá trình cài đặt, UniGetUI có thể không hoạt động như mong đợi.", "Close": "Đ<PERSON><PERSON>", "Close UniGetUI to the system tray": "Đóng UniGetUI vào khay hệ thống.", "Close WingetUI to the notification area": "Đóng UniGetUI vào khu vực thông báo", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "<PERSON><PERSON> lưu đám mây sử dụng một GitHub Gist riêng tư để lưu trữ danh sách các gói đã cài đặt", "Cloud package backup": "<PERSON><PERSON> l<PERSON><PERSON> gói lên đám mây", "Command-line Output": "<PERSON><PERSON><PERSON> ra dòng lệnh", "Command-line to run:": "<PERSON>òng lệnh để chạy:", "Compare query against": "So s<PERSON>h truy vấn với", "Compatible with authentication": "<PERSON><PERSON><PERSON><PERSON> thích với x<PERSON>c thực", "Compatible with proxy": "<PERSON><PERSON><PERSON><PERSON> thích với proxy", "Component Information": "<PERSON>h<PERSON>ng tin thành phần", "Concurrency and execution": "<PERSON><PERSON><PERSON> thời và thực thi", "Connect the internet using a custom proxy": "<PERSON><PERSON><PERSON> internet bằng proxy tùy chỉnh", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Đóng góp vào kho biểu tượng và ảnh chụp màn hình", "Contributors": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> g<PERSON>p", "Copy": "Sao chép", "Copy to clipboard": "Sao chép vào bảng nhớ tạm", "Could not add source": "<PERSON><PERSON><PERSON><PERSON> thể thêm nguồn", "Could not add source {source} to {manager}": "<PERSON><PERSON><PERSON><PERSON> thể thêm nguồn {source} v<PERSON><PERSON> {manager}", "Could not back up packages to GitHub Gist: ": "<PERSON><PERSON><PERSON><PERSON> thể sao lưu các gói lên <PERSON> Gist:", "Could not create bundle": "<PERSON><PERSON><PERSON><PERSON> thể tạo gói", "Could not load announcements - ": "<PERSON><PERSON><PERSON><PERSON> thể tải thông báo -", "Could not load announcements - HTTP status code is $CODE": "<PERSON>hông thể tải thông báo - mã trạng thái HTTP là $CODE", "Could not remove source": "<PERSON><PERSON><PERSON><PERSON> thể xóa nguồn", "Could not remove source {source} from {manager}": "<PERSON><PERSON><PERSON><PERSON> thể xóa nguồn {source} khỏi {manager}", "Could not remove {source} from {manager}": "<PERSON><PERSON><PERSON><PERSON> thể xóa {source} khỏi {manager}", "Credentials": "Thông tin xác thực", "Current Version": "<PERSON><PERSON><PERSON> bản hiện tại", "Current status: Not logged in": "Tr<PERSON>ng thái hiện tại: <PERSON><PERSON><PERSON> đăng nhập", "Current user": "<PERSON><PERSON><PERSON><PERSON> dùng hiện tại", "Custom arguments:": "<PERSON><PERSON><PERSON> số tùy chỉnh:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "<PERSON><PERSON><PERSON> đối số dòng lệnh tùy chỉnh có thể thay đổi cách chương trình được cài đặt, nâng cấp hoặc gỡ bỏ, theo cách mà UniGetUI không thể kiểm soát. Việc sử dụng dòng lệnh tùy chỉnh có thể làm hỏng các gói phần mềm. Hãy tiến hành thận trọng.", "Custom command-line arguments:": "Tham số dòng lệnh tùy chỉnh:", "Custom install arguments:": "<PERSON>ham số cài đặt tùy chỉnh:", "Custom uninstall arguments:": "<PERSON>ham số gỡ cài đặt tùy chỉnh:", "Custom update arguments:": "<PERSON><PERSON> số cập nhật tùy chỉnh:", "Customize WingetUI - for hackers and advanced users only": "Tùy chỉnh WingetUI - chỉ dành cho hacker và người dùng nâng cao", "DEBUG BUILD": "BẢN DỰNG GỠ LỖI", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "TUYÊN BỐ TỪ CHỐI: CHÚNG TÔI KHÔNG CHỊU TRÁCH NHIỆM VỀ CÁC GÓI ĐÃ TẢI XUỐNG. VUI LÒNG ĐẢM BẢO CHỈ CÀI ĐẶT PHẦN MỀM ĐÁNG TIN CẬY.", "Dark": "<PERSON><PERSON><PERSON>", "Decline": "<PERSON><PERSON> chối", "Default": "Mặc định", "Default installation options for {0} packages": "<PERSON><PERSON><PERSON> chọn cài đặt mặc định cho {0} gói", "Default preferences - suitable for regular users": "<PERSON><PERSON><PERSON> chọn mặc định - phù hợp với người dùng bình thường", "Default vcpkg triplet": "Bộ ba vcpkg mặc định", "Delete?": "Xóa?", "Dependencies:": "<PERSON><PERSON><PERSON> thành phần phụ thuộc:", "Descendant": "<PERSON><PERSON>n tử con", "Description:": "<PERSON><PERSON> tả: ", "Desktop shortcut created": "<PERSON><PERSON><PERSON> tắt trên màn hình đã đ<PERSON><PERSON><PERSON> tạo", "Details of the report:": "<PERSON> tiết của báo cáo:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "<PERSON><PERSON><PERSON> trình rất khó và ứng dụng này miễn phí. Nhưng nếu bạn thích ứng dụng này, bạn luôn có thể <b>mua cà phê cho tôi</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Cài đặt trực tiếp khi nhấp đúp vào một mục trên \"{discoveryTab}\" (thay vì hiển thị thông tin gói)", "Disable new share API (port 7058)": "<PERSON><PERSON> hiệu hóa API chia sẻ(cổng 7058)", "Disable the 1-minute timeout for package-related operations": "<PERSON><PERSON> hiệu hóa thời gian chờ 1 phút cho các hoạt động liên quan đến gói", "Disclaimer": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> miễn trừ trách n<PERSON>m", "Discover Packages": "Khám phá các gói", "Discover packages": "Khám phá các gói", "Distinguish between\nuppercase and lowercase": "<PERSON>ân biệt giữa chữ hoa và chữ thường", "Distinguish between uppercase and lowercase": "Phân biệt chữ hoa và chữ thường", "Do NOT check for updates": "ĐỪNG kiểm tra cập nhật", "Do an interactive install for the selected packages": "<PERSON>h<PERSON><PERSON> hiện cài đặt tương tác cho các gói đã chọn", "Do an interactive uninstall for the selected packages": "<PERSON>h<PERSON>c hiện gỡ cài đặt tương tác cho các gói đã chọn", "Do an interactive update for the selected packages": "<PERSON><PERSON><PERSON><PERSON> hiện cập nhật tương tác cho các gói đã chọn", "Do not automatically install updates when the battery saver is on": "<PERSON>hông tự động cài đặt cập nhật khi chế độ tiết kiệm pin đang bật", "Do not automatically install updates when the network connection is metered": "<PERSON><PERSON><PERSON><PERSON> tự động cài đặt cập nhật khi kết nối mạng được đo lường", "Do not download new app translations from GitHub automatically": "Không tự động tải xuống bản dịch ứng dụng mới từ GitHub", "Do not ignore updates for this package anymore": "<PERSON>h<PERSON>ng bỏ qua các cập nhật cho gói này nữa", "Do not remove successful operations from the list automatically": "Không tự động xóa các thao tác thành công khỏi danh sách", "Do not show this dialog again for {0}": "<PERSON><PERSON><PERSON><PERSON> hiển thị lại hộp tho<PERSON>i này cho {0}", "Do not update package indexes on launch": "<PERSON><PERSON><PERSON><PERSON> cập nhật chỉ mục gói khi khởi chạy", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Bạn có chấp nhận rằng UniGetUI thu thập và gửi các số liệu thống kê sử dụng ẩn danh, với mục đích duy nhất là hiểu và cải thiện trải nghiệm người dùng không?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Bạn có thấy UniGetUI hữu ích không? <PERSON><PERSON><PERSON> có thể, bạn có thể muốn hỗ trợ công việc của tôi, để tôi có thể tiếp tục làm cho UniGetUI trở thành giao diện quản lý gói tối ưu.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Bạn có thấy WingetUI hữu ích không? Bạn muốn hỗ trợ nhà phát triển? Nếu vậy, bạn có thể {0}, nó sẽ giúp ích rất nhiều!", "Do you really want to reset this list? This action cannot be reverted.": "Bạn có thật sự muốn đặt lại danh sách này không? Hành động này không thể hoàn tác.", "Do you really want to uninstall the following {0} packages?": "Bạn có thực sự muốn gỡ cài đặt các gói {0} sau không?", "Do you really want to uninstall {0} packages?": "Bạn có thực sự muốn gỡ cài đặt gói {0} không?", "Do you really want to uninstall {0}?": "<PERSON>ạn thực sự muốn gỡ bỏ {0} chứ?", "Do you want to restart your computer now?": "Bạn muốn khởi động lại máy ngay giờ không?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Bạn có muốn dịch WingetUI sang ngôn ngữ của mình không? Xem cách đóng góp <a style=\"color:{0}\" href=\"{1}\"a>TẠI ĐÂY!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "<PERSON><PERSON><PERSON><PERSON> cảm thấy muốn quyên góp? <PERSON><PERSON><PERSON> lo lắng, bạn luôn có thể chia sẻ UniGetUI với bạn bè của mình. Hãy lan truyền thông tin về UniGetUI.", "Donate": "Ủng hộ", "Done!": "Xong!", "Download failed": "<PERSON><PERSON><PERSON> xuống thất bại", "Download installer": "<PERSON><PERSON><PERSON> xuống trình cài đặt", "Download operations are not affected by this setting": "<PERSON><PERSON><PERSON> thao tác tải xuống không bị ảnh hưởng bởi thiết lập này", "Download selected installers": "<PERSON><PERSON><PERSON> xuống các trình cài đặt đã chọn", "Download succeeded": "<PERSON><PERSON><PERSON> xu<PERSON>ng thành công", "Download updated language files from GitHub automatically": "Tự động tải xuống các tệp ngôn ngữ cập nhật từ GitHub", "Downloading": "<PERSON><PERSON> tả<PERSON> xu<PERSON>", "Downloading backup...": "<PERSON><PERSON> tải bản sao lư<PERSON>...", "Downloading installer for {package}": "<PERSON><PERSON> tải xuống trình cài đặt cho {package}", "Downloading package metadata...": "<PERSON><PERSON> tải xuống siêu tài nguyên....", "Enable Scoop cleanup on launch": "<PERSON><PERSON><PERSON> t<PERSON>h năng dọn dẹp Scoop khi khởi chạy", "Enable WingetUI notifications": "<PERSON><PERSON><PERSON> thông b<PERSON>o của WingetUI", "Enable an [experimental] improved WinGet troubleshooter": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON> k<PERSON> phục sự cố WinGet đư<PERSON><PERSON> cải tiến [thử nghiệm]", "Enable and disable package managers, change default install options, etc.": "Bật và tắt trình quản lý gói, thay đổi tùy chọn cài đặt mặc định, v.v.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> tối ưu hóa sử dụng CPU nền (xem <PERSON><PERSON><PERSON> c<PERSON><PERSON> Kéo #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "<PERSON><PERSON><PERSON> ho<PERSON> a<PERSON> (Tiện ích và chia sẻ WingetUI, cổng 7058)", "Enable it to install packages from {pm}.": "<PERSON><PERSON><PERSON> ho<PERSON>t nó để cài đặt các gói từ {pm}.", "Enable the automatic WinGet troubleshooter": "<PERSON><PERSON><PERSON> ho<PERSON> trình kh<PERSON>c phục sự cố WinGet tự động", "Enable the new UniGetUI-Branded UAC Elevator": "Bật UAC Elevator <PERSON><PERSON><PERSON><PERSON> thương hiệu hóa của UniGetUI mới.", "Enable the new process input handler (StdIn automated closer)": "<PERSON><PERSON>t trình xử lý đầu vào tiến trình mới (tự động đóng StdIn)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Chỉ bật các thiết lập bên dưới NẾU VÀ CHỈ NẾU bạn thực sự hiểu rõ chức năng của chúng, cũ<PERSON> nh<PERSON> các hệ quả và rủi ro tiềm ẩn.", "Enable {pm}": "Bật {pm}", "Enter proxy URL here": "Nhập URL proxy v<PERSON><PERSON><PERSON>", "Entries that show in RED will be IMPORTED.": "<PERSON><PERSON><PERSON> mục hiển thị bằng màu ĐỎ sẽ được NHẬP vào.", "Entries that show in YELLOW will be IGNORED.": "<PERSON><PERSON><PERSON> mục hiển thị bằng màu VÀNG sẽ bị BỎ QUA.", "Error": "Lỗi", "Everything is up to date": "<PERSON><PERSON><PERSON> thứ đều đ<PERSON><PERSON><PERSON> cậ<PERSON> nh<PERSON>t", "Exact match": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>h <PERSON>c", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "<PERSON><PERSON><PERSON> lối tắt hiện có trên màn hình của bạn sẽ được quét, và bạn sẽ cần chọn những lối tắt nào để giữ lại và những lối tắt nào để xóa.", "Expand version": "<PERSON><PERSON><PERSON> bản mở rộng", "Experimental settings and developer options": "<PERSON><PERSON><PERSON> đặt thử nghiệm và tùy chọn nhà phát triển", "Export": "<PERSON><PERSON><PERSON>", "Export log as a file": "<PERSON><PERSON><PERSON> nhật ký ra một tệp tin", "Export packages": "<PERSON><PERSON><PERSON> g<PERSON>", "Export selected packages to a file": "<PERSON><PERSON><PERSON> các gói đã lựa chọn thành một tập tin", "Export settings to a local file": "<PERSON><PERSON><PERSON> cài đặt ra tệp tin cục bộ", "Export to a file": "<PERSON><PERSON><PERSON> thành tệp", "Failed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "Fetching available backups...": "<PERSON><PERSON> tìm các bản sao lưu có sẵn...", "Fetching latest announcements, please wait...": "<PERSON><PERSON> tải thông báo mới nhất, vui lòng đợi...", "Filters": "<PERSON><PERSON> lọc", "Finish": "<PERSON><PERSON><PERSON> th<PERSON>", "Follow system color scheme": "<PERSON> bảng màu hệ thống", "Follow the default options when installing, upgrading or uninstalling this package": "<PERSON><PERSON><PERSON> theo tùy chọn mặc định khi cài đặt, nâng cấp hoặc gỡ bỏ gói này", "For security reasons, changing the executable file is disabled by default": "<PERSON><PERSON> lý do bả<PERSON> mậ<PERSON>, vi<PERSON><PERSON> thay đổi tệp thực thi bị vô hiệu hóa theo mặc định", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "<PERSON><PERSON> lý do bảo mật, các tham số dòng lệnh tùy chỉnh bị vô hiệu hóa theo mặc định. Hãy vào cài đặt bảo mật của UniGetUI để thay đổi.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "<PERSON><PERSON> lý do bảo mật, c<PERSON><PERSON> tập lệnh trước và sau thao tác bị vô hiệu hóa theo mặc định. Hãy vào cài đặt bảo mật của UniGetUI để thay đổi.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Sử dụng phiên bản winget do ARM biên dịch (CHỈ DÀNH CHO HỆ THỐNG ARM64)", "Formerly known as WingetUI": "Trước đây là WingetUI", "Found": "<PERSON><PERSON> tìm thấy", "Found packages: ": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> đ<PERSON><PERSON> tìm thấy:", "Found packages: {0}": "<PERSON><PERSON><PERSON> tìm thấy:  {0} ", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON> tìm thấy: {0}, ch<PERSON><PERSON> hoàn thành...", "General preferences": "<PERSON><PERSON><PERSON> chỉnh chung", "GitHub profile": "<PERSON><PERSON> s<PERSON>", "Global": "<PERSON><PERSON><PERSON> c<PERSON>", "Go to UniGetUI security settings": "<PERSON>i tới cài đặt bảo mật của UniGetUI.", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "<PERSON><PERSON> lưu trữ lớn chưa biết nhưng có các tiện ích hữu ích và các gói thú vị khác.<br><PERSON><PERSON> gồm: <b><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> dò<PERSON>nh, <PERSON><PERSON><PERSON> mề<PERSON> chung (<PERSON><PERSON><PERSON> c<PERSON><PERSON> bộ chứ<PERSON> bổ sung)</b>", "Great! You are on the latest version.": "Tuyệt vời! Bạn đang sử dụng phiên bản mới nhất.", "Grid": "Lưới", "Help": "<PERSON><PERSON><PERSON> g<PERSON>", "Help and documentation": "<PERSON>rợ giúp và tài liệu", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "<PERSON><PERSON><PERSON> đ<PERSON>, bạn có thể thay đổi hành vi của UniGetUI liên quan đến các phím tắt sau. <PERSON><PERSON><PERSON> tra một phím tắt sẽ làm cho UniGetUI xóa nó nếu nó được tạo ra trong lần nâng cấp tương lai. Bỏ chọn nó sẽ giữ nguyên phím tắt.", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, tô<PERSON> là <PERSON>, và tôi là <i>nhà phát triển</i>của UniGetUI. UniGetUI đã được tạo ra hoàn toàn trong thời gian rảnh của tôi!", "Hide details": "Ẩn chi tiết", "Homepage": "Trang chủ", "Hooray! No updates were found.": "Yeah! <PERSON><PERSON><PERSON><PERSON> có bản cập nhật nào đư<PERSON>c tìm thấy!", "How should installations that require administrator privileges be treated?": "<PERSON><PERSON><PERSON> cài đặt yêu cầu quyền quản trị viên nên được xử lý như thế nào?", "How to add packages to a bundle": "<PERSON><PERSON><PERSON> thêm các gói vào bộ sản phẩm", "I understand": "<PERSON><PERSON><PERSON>", "Icons": "<PERSON><PERSON><PERSON> bi<PERSON> t<PERSON>", "Id": "<PERSON><PERSON> đ<PERSON>nh danh", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "<PERSON><PERSON>u bạn đã bật t<PERSON>h năng sao lưu đám mây, bản sao lưu sẽ được lưu dưới dạng GitHub Gist trong tài khoản này.", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Bỏ qua các lệnh tùy chỉnh trước và sau khi cài đặt khi nhập gói phần mềm từ một bộ", "Ignore future updates for this package": "Bỏ qua các bản cập nhật tương lai cho gói này", "Ignore packages from {pm} when showing a notification about updates": "Bỏ qua các gói từ {pm} khi hiển thị thông báo về các bản cập nhật", "Ignore selected packages": "Bỏ qua các gói đã chọn", "Ignore special characters": "Bỏ qua các ký tự đặc biệt", "Ignore updates for the selected packages": "Bỏ qua các bản cập nhật cho các gói đã chọn", "Ignore updates for this package": "Bỏ qua các bản cập nhật cho gói này", "Ignored updates": "<PERSON><PERSON><PERSON> bản cập nhật bị bỏ qua", "Ignored version": "<PERSON><PERSON><PERSON> bản bị bỏ qua", "Import": "<PERSON><PERSON><PERSON><PERSON>", "Import packages": "<PERSON><PERSON><PERSON><PERSON>", "Import packages from a file": "<PERSON><PERSON><PERSON><PERSON> gói từ tệp tin", "Import settings from a local file": "<PERSON><PERSON><PERSON><PERSON> cài đặt từ tệp tin cục bộ", "In order to add packages to a bundle, you will need to: ": "<PERSON><PERSON> thêm các gói vào bộ sản phẩm, bạn cần:", "Initializing WingetUI...": "<PERSON>ang khởi tạo WingetUI...", "Install": "Cài đặt", "Install Scoop": "Cài đặt Scoop", "Install and more": "Cài đặt và các tùy chọn khác", "Install and update preferences": "<PERSON>ài đặt và cập nhật các tùy chọn", "Install as administrator": "Cài đặt với quyền quản trị viên", "Install available updates automatically": "Tự động cài đặt các bản cập nhật có sẵn", "Install location can't be changed for {0} packages": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi vị trí cài đặt cho {0} gói.", "Install location:": "Vị trí cài đặt:", "Install options": "<PERSON><PERSON><PERSON> ch<PERSON>n cài đặt", "Install packages from a file": "Cài đặt gói từ tệp tin", "Install prerelease versions of UniGetUI": "Cài đặt các phiên bản phát hành trước của UniGetUI", "Install selected packages": "Cài đặt các gói đã chọn", "Install selected packages with administrator privileges": "Cài đặt các gói đã chọn với quyền quản trị viên", "Install selection": "Cài đặt những lựa chọn", "Install the latest prerelease version": "Cài đặt phiên bản phát hành trước mới nhất", "Install updates automatically": "<PERSON><PERSON><PERSON> đặt bản cập nhật tự động", "Install {0}": "Cài đặt {0}", "Installation canceled by the user!": "Cài đặt bị hủy bởi người dùng!", "Installation failed": "Cài đặt thất bại", "Installation options": "L<PERSON>a chọn cài đặt", "Installation scope:": "Phạm vi cài đặt:", "Installation succeeded": "<PERSON>ài đặt thành công", "Installed Packages": "G<PERSON>i đã cài đặt", "Installed Version": "<PERSON><PERSON><PERSON> bản đã cài", "Installed packages": "<PERSON><PERSON><PERSON> gói đã được cài đặt", "Installer SHA256": "Trình cài đặt SHA256", "Installer SHA512": "Trình cài đặt SHA512", "Installer Type": "Loại cài đặt", "Installer URL": "URL trình cài đặt", "Installer not available": "Trình cài đặt không có sẵn", "Instance {0} responded, quitting...": "<PERSON>r<PERSON><PERSON><PERSON> hợ<PERSON> {0} đ<PERSON> <PERSON><PERSON><PERSON><PERSON> hồ<PERSON>, đang tho<PERSON>...", "Instant search": "<PERSON><PERSON><PERSON> kiếm tức thì", "Integrity checks can be disabled from the Experimental Settings": "<PERSON><PERSON><PERSON> tra tính toàn vẹn có thể được vô hiệu hóa từ phần Cài đặt Thử nghiệm", "Integrity checks skipped": "Bỏ qua kiểm tra tính hoàn hảo", "Integrity checks will not be performed during this operation": "<PERSON><PERSON><PERSON> tra tính hoàn hảo sẽ không được thực hiện trong quá trình hoạt động này", "Interactive installation": "Cài đặt tương tác", "Interactive operation": "<PERSON><PERSON><PERSON> động tư<PERSON>ng tác", "Interactive uninstall": "Gỡ cài đặt tương tác", "Interactive update": "<PERSON><PERSON><PERSON> nh<PERSON>t tư<PERSON> tác", "Internet connection settings": "Cài đặt kết nối Internet", "Is this package missing the icon?": "<PERSON><PERSON><PERSON> này có bị thiếu biểu tượng không?", "Is your language missing or incomplete?": "Ngôn ngữ của bạn bị thiếu hoặc không đầy đủ?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Không đảm bảo rằng thông tin xác thực được cung cấp sẽ được lưu trữ an toàn, vì vậy bạn không nên sử dụng thông tin xác thực của tài khoản ngân hàng của mình", "It is recommended to restart UniGetUI after WinGet has been repaired": "Nên khởi động lại UniGetUI sau khi WinGet đã được sửa chữa", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "<PERSON><PERSON><PERSON> khu<PERSON>ến nghị bạn nên cài đặt lại UniGetUI để xử lý tình huống hiện tại.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Có vẻ như WinGet không hoạt động bình thường. Bạn có muốn thử sửa chữa WinGet không?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON> vẻ như bạn đã chạy WingetUI với tư cách quản trị viên, điều này không đượ<PERSON> khuyến khích. Bạn vẫn có thể sử dụng chương trình nhưng chúng tôi khuyên bạn không nên chạy WingetUI với quyền quản trị viên. Nhấp vào \"{showDetails}\" để xem lý do.", "Language": "<PERSON><PERSON><PERSON>", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON>, chủ đề và các cài đặt khác", "Last updated:": "<PERSON><PERSON><PERSON> cu<PERSON> cập nhật: ", "Latest": "<PERSON><PERSON><PERSON>", "Latest Version": "<PERSON><PERSON><PERSON> bản mới nh<PERSON>t", "Latest Version:": "<PERSON><PERSON><PERSON> bản mới nhất: ", "Latest details...": "<PERSON> tiết mới nhất...", "Launching subprocess...": "<PERSON><PERSON> khởi chạy quy trình con...", "Leave empty for default": "<PERSON><PERSON> trống theo mặc định", "License": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>p", "Licenses": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>(s)", "Light": "<PERSON><PERSON><PERSON>", "List": "<PERSON><PERSON>", "Live command-line output": "<PERSON><PERSON><PERSON> ra dòng lệnh tr<PERSON><PERSON> tiếp", "Live output": "<PERSON><PERSON><PERSON> ra tr<PERSON><PERSON> tiếp", "Loading UI components...": "<PERSON><PERSON> tải các thành phần của giao diện người dùng...", "Loading WingetUI...": "WingetUI đang đư<PERSON> tải....", "Loading packages": "<PERSON><PERSON> t<PERSON> g<PERSON>i", "Loading packages, please wait...": "<PERSON><PERSON> t<PERSON>, vui lòng đợi...", "Loading...": "<PERSON><PERSON> tả<PERSON>....", "Local": "<PERSON><PERSON><PERSON> bộ", "Local PC": "<PERSON> c<PERSON><PERSON> bộ", "Local backup advanced options": "<PERSON><PERSON><PERSON> chọn nâng cao cho sao lưu c<PERSON> bộ.", "Local machine": "<PERSON><PERSON><PERSON> chủ cục bộ", "Local package backup": "<PERSON><PERSON> l<PERSON><PERSON> g<PERSON> c<PERSON> bộ", "Locating {pm}...": "<PERSON><PERSON> đ<PERSON>nh vị {pm}...", "Log in": "<PERSON><PERSON><PERSON>", "Log in failed: ": "<PERSON><PERSON><PERSON> nhập thất bại:", "Log in to enable cloud backup": "<PERSON><PERSON><PERSON> nhập để bật tính năng sao lưu lên đám mây", "Log in with GitHub": "<PERSON><PERSON><PERSON> nhập bằng GitHub", "Log in with GitHub to enable cloud package backup.": "<PERSON><PERSON><PERSON> nhập bằng GitHub để bật tính năng sao lưu gói lên đám mây.", "Log level:": "<PERSON><PERSON><PERSON> độ ghi log:", "Log out": "<PERSON><PERSON><PERSON> xu<PERSON>", "Log out failed: ": "<PERSON><PERSON><PERSON> xuất thất bại:", "Log out from GitHub": "<PERSON><PERSON><PERSON> xuất khỏi GitHub", "Looking for packages...": "<PERSON><PERSON> tìm gói...", "Machine | Global": "<PERSON><PERSON>y | <PERSON><PERSON><PERSON> c<PERSON>u", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "<PERSON><PERSON><PERSON> đối số dòng lệnh sai định dạng có thể làm hỏng các gói phần mềm, hoặc thậm chí tạo điều kiện cho kẻ tấn công xấu chiếm quyền thực thi đặc biệt. <PERSON><PERSON> vậy, việc nhập các đối số dòng lệnh tùy chỉnh bị vô hiệu hóa theo mặc định", "Manage": "<PERSON><PERSON><PERSON><PERSON> lý", "Manage UniGetUI settings": "<PERSON><PERSON><PERSON>n lý cài đặt UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "<PERSON><PERSON><PERSON><PERSON> lý hành vi tự khởi động WingetUI từ ứng dụng Cài đặt", "Manage ignored packages": "<PERSON><PERSON><PERSON><PERSON> lý các gói bị bỏ qua", "Manage ignored updates": "<PERSON><PERSON><PERSON><PERSON> lý các cập nhật bị bỏ qua", "Manage shortcuts": "<PERSON><PERSON><PERSON><PERSON> lý l<PERSON> t<PERSON>t", "Manage telemetry settings": "<PERSON><PERSON><PERSON><PERSON> lý cài đặt thu thập thông tin", "Manage {0} sources": "<PERSON><PERSON><PERSON><PERSON> lý {0} ngu<PERSON>n", "Manifest": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "Manifests": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> c<PERSON>u hình", "Manual scan": "<PERSON><PERSON><PERSON> thủ công", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "<PERSON><PERSON><PERSON><PERSON> quản lý gói chính thức của Microsoft. Đ<PERSON>y đủ các gói nổi tiếng và đã được xác minh<br><PERSON><PERSON> gồ<PERSON>: <b><PERSON><PERSON><PERSON> mềm chung, <PERSON>ng dụng Microsoft Store</b>", "Missing dependency": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>n ph<PERSON> thuộc", "More": "<PERSON><PERSON><PERSON><PERSON>", "More details": "<PERSON> ti<PERSON><PERSON> h<PERSON>n", "More details about the shared data and how it will be processed": "Thông tin chi tiết hơn về dữ liệu được chia sẻ và cách nó sẽ được xử lý", "More info": "<PERSON><PERSON><PERSON><PERSON> thông tin", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "LƯU Ý: <PERSON><PERSON><PERSON><PERSON> khắc phục sự cố này có thể bị tắt từ Cài đặt UniGetUI, trên phần WinGet", "Name": "<PERSON><PERSON><PERSON>", "New": "<PERSON><PERSON><PERSON>", "New Version": "<PERSON><PERSON><PERSON> bản mới", "New bundle": "<PERSON>h<PERSON><PERSON> mới", "New version": "<PERSON><PERSON><PERSON> bản mới", "Nice! Backups will be uploaded to a private gist on your account": "Tuyệt! <PERSON><PERSON><PERSON> bản sao lưu sẽ được tải lên một Gist riêng tư trong tài khoản của bạn", "No": "K<PERSON>ô<PERSON>", "No applicable installer was found for the package {0}": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trình cài đặt phù hợp cho gói {0}", "No dependencies specified": "<PERSON><PERSON><PERSON><PERSON> có thành phần phụ thuộc nào được chỉ định", "No new shortcuts were found during the scan.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lối tắt mới nào trong quá trình quét.", "No packages found": "<PERSON><PERSON><PERSON><PERSON> có gói nào đ<PERSON><PERSON><PERSON> tìm thấy", "No packages found matching the input criteria": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói nào phù hợp với yêu cầu nhập vào", "No packages have been added yet": "<PERSON><PERSON><PERSON> có gói nào đ<PERSON><PERSON><PERSON> thêm", "No packages selected": "<PERSON><PERSON><PERSON><PERSON> có gói nào đ<PERSON><PERSON><PERSON> ch<PERSON>n", "No packages were found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói nào", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "<PERSON><PERSON><PERSON>ng có thông tin cá nhân nào được thu thập hoặc gửi đi, và dữ liệu thu thập đư<PERSON><PERSON> l<PERSON> danh, vì vậy không thể truy ngược lại bạn.", "No results were found matching the input criteria": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả nào phù hợp với tiêu chí đầu vào", "No sources found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nguồn nào", "No sources were found": "<PERSON><PERSON><PERSON><PERSON> có nguồn nào đ<PERSON><PERSON><PERSON> tìm thấy", "No updates are available": "<PERSON><PERSON><PERSON><PERSON> có bản cập nhật nào khả dụng vào lúc này", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "<PERSON><PERSON><PERSON><PERSON> quản lý gói của Node JS. <PERSON><PERSON><PERSON> đủ các thư viện và các tiện ích khác xoay quanh thế giới JavaScript<br><PERSON><PERSON> gồm: <b><PERSON><PERSON><PERSON> thư viện JavaScript của Node và các tiện ích liên quan khác</b>", "Not available": "<PERSON><PERSON><PERSON><PERSON> khả dụng", "Not finding the file you are looking for? Make sure it has been added to path.": "Không tìm thấy tệp bạn cần? Hãy đảm bảo rằng nó đã được thêm vào biến đường dẫn.", "Not found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy", "Not right now": "Không phải bây giờ", "Notes:": "<PERSON><PERSON> chú: ", "Notification preferences": "<PERSON><PERSON><PERSON> chọn thông báo", "Notification tray options": "<PERSON><PERSON><PERSON> chọn khay thông báo", "Notification types": "<PERSON><PERSON><PERSON> lo<PERSON>i thông báo", "NuPkg (zipped manifest)": "NuPkg (tệ<PERSON> c<PERSON>u hình n<PERSON>)", "OK": "OK ", "Ok": "Ok", "Open": "Mở", "Open GitHub": "Mở GitHub", "Open UniGetUI": "Mở UniGetUI", "Open UniGetUI security settings": "Mở cài đặt bảo mật của UniGetUI", "Open WingetUI": "Mở UniGetUI", "Open backup location": "Mở vị trí sao lưu", "Open existing bundle": "Mở nhóm hiện có", "Open install location": "Mở vị trí cài đặt", "Open the welcome wizard": "Mở trình hướng dẫn chào mừng", "Operation canceled by user": "<PERSON><PERSON><PERSON><PERSON> dùng đã hủy thao tác", "Operation cancelled": "<PERSON><PERSON> hủy thao tác", "Operation history": "<PERSON><PERSON><PERSON> sử hoạt động", "Operation in progress": "<PERSON><PERSON><PERSON> động đang tiến hành", "Operation on queue (position {0})...": "<PERSON><PERSON><PERSON> động trên hàng đợi (vị trí {0})...", "Operation profile:": "<PERSON><PERSON> sơ thao tác:", "Options saved": "<PERSON><PERSON><PERSON> chọn đã đ<PERSON><PERSON><PERSON> l<PERSON>u", "Order by:": "<PERSON><PERSON><PERSON> xếp theo:", "Other": "K<PERSON><PERSON><PERSON>", "Other settings": "Cài đặt khác", "Package": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> mềm", "Package Bundles": "Nhóm gói", "Package ID": "ID gói", "Package Manager": "<PERSON><PERSON><PERSON><PERSON> qu<PERSON>n lý gói", "Package Manager logs": "<PERSON><PERSON><PERSON><PERSON> ký <PERSON>ình quản lý gói", "Package Managers": "<PERSON><PERSON><PERSON> trình quản lý gói", "Package Name": "<PERSON><PERSON><PERSON>", "Package backup": "<PERSON>o lư<PERSON> g<PERSON>i", "Package backup settings": "Cài đặt sao lưu gói", "Package bundle": "Nhóm gói", "Package details": "<PERSON> tiết gói", "Package lists": "<PERSON><PERSON> s<PERSON> g<PERSON>i", "Package management made easy": "<PERSON><PERSON><PERSON><PERSON> lý gói trở nên dễ dàng", "Package manager": "<PERSON><PERSON><PERSON><PERSON> qu<PERSON>n lý gói", "Package manager preferences": "<PERSON><PERSON><PERSON> chọn trình quản lý gói", "Package managers": "<PERSON><PERSON><PERSON><PERSON> qu<PERSON>n lý gói", "Package not found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói", "Package operation preferences": "<PERSON><PERSON><PERSON> ch<PERSON>n thao tác gói", "Package update preferences": "<PERSON><PERSON><PERSON> ch<PERSON>n cập nh<PERSON>t g<PERSON>i", "Package {name} from {manager}": "<PERSON><PERSON><PERSON> {name} từ {manager}", "Package's default": "<PERSON><PERSON><PERSON> phần mềm mặc định", "Packages": "<PERSON><PERSON><PERSON>", "Packages found: {0}": "<PERSON><PERSON><PERSON> gói tìm thấy: {0}", "Partially": "<PERSON><PERSON><PERSON>", "Password": "<PERSON><PERSON><PERSON>", "Paste a valid URL to the database": "Dán URL hợp lệ vào cơ sở dữ liệu", "Pause updates for": "<PERSON><PERSON><PERSON> dừng cập nhật trong vòng", "Perform a backup now": "<PERSON><PERSON><PERSON><PERSON> hiện sao lưu ngay bây giờ", "Perform a cloud backup now": "<PERSON><PERSON><PERSON><PERSON> hiện sao lưu lên đám mây ngay bây giờ", "Perform a local backup now": "<PERSON><PERSON><PERSON><PERSON> hiện sao lưu cục bộ ngay bây giờ", "Perform integrity checks at startup": "Thực hiện kiểm tra tính toàn vẹn khi khởi động", "Performing backup, please wait...": "<PERSON><PERSON> thực hiện sao lư<PERSON>, vui lòng đợi...", "Periodically perform a backup of the installed packages": "<PERSON>h<PERSON>c hiện sao lưu định kỳ các gói đã cài đặt", "Periodically perform a cloud backup of the installed packages": "Thực hiện sao lưu định kỳ lên đám mây cho các gói đã cài đặt", "Periodically perform a local backup of the installed packages": "<PERSON>hực hiện sao lưu cục bộ định kỳ cho các gói đã cài đặt", "Please check the installation options for this package and try again": "<PERSON>ui lòng kiểm tra các tùy chọn cài đặt cho gói này và thử lại", "Please click on \"Continue\" to continue": "<PERSON><PERSON> lòng click vào \"Ti<PERSON><PERSON> tục\" để tiếp tục", "Please enter at least 3 characters": "<PERSON><PERSON> lòng nhập ít nhất 3 ký tự", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "<PERSON>n lưu ý rằng một số gói nhất định có thể không cài đặt được do trình quản lý gói được bật trên thiết bị này.", "Please note that not all package managers may fully support this feature": "Xin lưu ý rằng không phải tất cả các trình quản lý gói đều có thể hỗ trợ đầy đủ tính năng này", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "<PERSON>n lưu ý rằng các gói từ một số nguồn nhất định có thể không xuất được. Chúng đã chuyển sang màu xám và sẽ không được xuất.", "Please run UniGetUI as a regular user and try again.": "<PERSON><PERSON> lòng chạy UniGetUI với quyền người dùng thông thường và thử lại.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "<PERSON><PERSON> lòng xem Đầu ra dòng lệnh hoặc tham kh<PERSON><PERSON>ch sử hoạt động để biết thêm thông tin về sự cố.", "Please select how you want to configure WingetUI": "<PERSON><PERSON> lòng chọn cách bạn muốn thiế<PERSON> lập <PERSON>etUI", "Please try again later": "<PERSON><PERSON> lòng thử lại sau", "Please type at least two characters": "<PERSON><PERSON> lòng nhập ít nhất hai kí tự", "Please wait": "<PERSON><PERSON> lòng chờ", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Vui lòng đợi trong khi {0} đang được cài đặt. <PERSON><PERSON>t cửa sổ màu đen có thể xuất hiện. Vui lòng đợi cho đến khi nó đóng lại.", "Please wait...": "<PERSON><PERSON> lòng chờ.....", "Portable": "<PERSON>", "Portable mode": "<PERSON><PERSON> độ di động", "Post-install command:": "<PERSON><PERSON><PERSON> sau khi cài đặt:", "Post-uninstall command:": "<PERSON><PERSON><PERSON> sau khi gỡ cài đặt:", "Post-update command:": "<PERSON><PERSON><PERSON> sau khi cập nhật:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Tr<PERSON><PERSON> quản lý gói của PowerShell. T<PERSON><PERSON> thư viện và tập lệnh để mở rộng khả năng của PowerShell<br><PERSON><PERSON> gồ<PERSON>: <b><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ghép ngắn</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "<PERSON><PERSON><PERSON> lệnh trước và sau khi cài đặt có thể gây hại nghiêm trọng cho thiết bị của bạn nếu được thiết kế với mục đích xấu. Vi<PERSON><PERSON> nhập các lệnh này từ một gói tổng hợp có thể rất nguy hiểm, trừ khi bạn tin tưởng nguồn của gói phần mềm đó", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "<PERSON><PERSON><PERSON> lệnh trước và sau khi cài đặt sẽ được thực thi trước và sau khi một gói phần mềm được cài đặt, nâng cấp hoặc gỡ bỏ. H<PERSON>y lưu ý rằng chúng có thể gây hỏng hóc nếu không được sử dụng cẩn thận", "Pre-install command:": "<PERSON><PERSON><PERSON> trước khi cài đặt:", "Pre-uninstall command:": "<PERSON><PERSON><PERSON> trước khi gỡ cài đặt:", "Pre-update command:": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> khi cập nhật:", "PreRelease": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi phát hành", "Preparing packages, please wait...": "<PERSON><PERSON> chu<PERSON>n bị gó<PERSON>, vui lòng đợi...", "Proceed at your own risk.": "<PERSON><PERSON><PERSON><PERSON> hành tự chịu rủi ro", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "<PERSON><PERSON><PERSON> mọi hình thức nâng quyền thông qua UniGetUI Elevator hoặc GSudo", "Proxy URL": "URL proxy", "Proxy compatibility table": "<PERSON><PERSON><PERSON> tư<PERSON><PERSON> thích proxy", "Proxy settings": "Cài đặt proxy", "Proxy settings, etc.": "Cài đặt proxy, v.v.", "Publication date:": "<PERSON><PERSON><PERSON> c<PERSON>ng khai: ", "Publisher": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON> b<PERSON>n", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "<PERSON><PERSON><PERSON><PERSON> quản lý thư viện của Python. <PERSON><PERSON><PERSON> đủ các thư viện Python và các tiện ích liên quan đến Python khác<br><PERSON><PERSON> gồ<PERSON>: <b><PERSON><PERSON><PERSON> thư viện Python và các tiện ích liên quan</b>", "Quit": "<PERSON><PERSON><PERSON><PERSON>", "Quit WingetUI": "T<PERSON><PERSON>t WingetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "<PERSON><PERSON><PERSON>m số lần hiển thị thông b<PERSON>o <PERSON>, tự động nâng quyền khi cài đặt, mở kh<PERSON>a một số tính năng nguy hiểm, v.v.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "<PERSON><PERSON> kh<PERSON>o nhật ký UniGetUI để biết thêm chi tiết về (các) tệp bị ảnh hưởng", "Reinstall": "Cài đặt lại", "Reinstall package": "Cài đặt lại gói", "Related settings": "Cài đặt liên quan", "Release notes": "<PERSON><PERSON> chú phát hành", "Release notes URL": "URL ghi chú phát hành", "Release notes URL:": "URL ghi chú phát hành:", "Release notes:": "<PERSON><PERSON> chú phát hành:", "Reload": "<PERSON><PERSON><PERSON> l<PERSON>i", "Reload log": "<PERSON><PERSON><PERSON> lại n<PERSON>t ký", "Removal failed": "<PERSON><PERSON><PERSON> không thành công", "Removal succeeded": "<PERSON><PERSON><PERSON> thành công", "Remove from list": "Xóa khỏi danh sách", "Remove permanent data": "<PERSON><PERSON>a dữ liệu v<PERSON><PERSON> v<PERSON><PERSON>n", "Remove selection from bundle": "<PERSON>óa lựa chọn khỏi nhóm", "Remove successful installs/uninstalls/updates from the installation list": "<PERSON><PERSON><PERSON> các cài đặt/gỡ cài đặt/cập nhật thành công khỏi danh sách cài đặt", "Removing source {source}": "<PERSON><PERSON> gỡ bỏ nguồn {source}", "Removing source {source} from {manager}": "<PERSON><PERSON> x<PERSON>a nguồn {source} khỏi {manager}", "Repair UniGetUI": "Sửa chữa UniGetUI", "Repair WinGet": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>", "Report an issue or submit a feature request": "<PERSON><PERSON>o cáo một vấn đề hoặc yêu cầu tính năng nào đó", "Repository": "<PERSON><PERSON>", "Reset": "Đặt lại", "Reset Scoop's global app cache": "Đặt lại cache c<PERSON><PERSON> cục bộ", "Reset UniGetUI": "Đặt lại UniGetUI", "Reset WinGet": "Đặt lại WinGet", "Reset Winget sources (might help if no packages are listed)": "Đặt lại nguồn Winget (có thể hữu ích nếu không có gói nào được liệt kê)", "Reset WingetUI": "Đặt lại WingetUI", "Reset WingetUI and its preferences": "Đặt lại WingetUI và các tùy chọn của nó", "Reset WingetUI icon and screenshot cache": "Đặt lại biểu tượng WingetUI và bộ đệm ảnh chụp màn hình", "Reset list": "Đặt lại danh sách", "Resetting Winget sources - WingetUI": "Đặt lại nguồn Winget - WingetUI", "Restart": "Khởi động lại", "Restart UniGetUI": "Khởi động lại UniGetUI", "Restart WingetUI": "Khởi động lại WingetUI", "Restart WingetUI to fully apply changes": "Khởi động lại WingetUI để áp dụng đầy đủ tất cả các thay đổi", "Restart later": "Khởi động lại sau ", "Restart now": "Khởi động lại ngay", "Restart required": "<PERSON><PERSON><PERSON> cầu khởi động lại", "Restart your PC to finish installation": "Khởi động lại PC của bạn để hoàn tất cài đặt", "Restart your computer to finish the installation": "Khởi động lại máy tính của bạn để hoàn tất cài đặt", "Restore a backup from the cloud": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> bản sao lưu từ đám mây", "Restrictions on package managers": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>i hạn áp dụng cho trình quản lý gói", "Restrictions on package operations": "<PERSON><PERSON><PERSON> gi<PERSON>i hạn áp dụng cho thao tác xử lý gói", "Restrictions when importing package bundles": "<PERSON><PERSON><PERSON> chế khi nhập các gói phần mềm theo bộ", "Retry": "<PERSON><PERSON><PERSON> lại", "Retry as administrator": "Th<PERSON> lại với quyền quản trị viên", "Retry failed operations": "Th<PERSON> lại các thao tác đã thất bại", "Retry interactively": "<PERSON><PERSON><PERSON> lại một cách tương tác", "Retry skipping integrity checks": "Thử lại bỏ qua các kiểm tra tính toàn vẹn", "Retrying, please wait...": "<PERSON><PERSON> thử lại, vui lòng đợi...", "Return to top": "Quay lại đầu trang", "Run": "Chạy", "Run as admin": "<PERSON><PERSON><PERSON> với quyền quản trị", "Run cleanup and clear cache": "Dọn dẹp và xóa bộ nhớ tạm", "Run last": "<PERSON><PERSON><PERSON> cu<PERSON>i c<PERSON>ng", "Run next": "<PERSON><PERSON><PERSON> ti<PERSON> theo", "Run now": "<PERSON><PERSON><PERSON> ngay bây giờ", "Running the installer...": "<PERSON><PERSON> chạy trình cài đặt...", "Running the uninstaller...": "<PERSON><PERSON> chạy trình gỡ cài đặt...", "Running the updater...": "<PERSON><PERSON> chạy trình cập nhật...", "Save": "<PERSON><PERSON><PERSON>", "Save File": "<PERSON><PERSON><PERSON>", "Save and close": "<PERSON><PERSON><PERSON> và đóng", "Save as": "<PERSON><PERSON><PERSON> th<PERSON>...", "Save bundle as": "<PERSON><PERSON><PERSON> v<PERSON>i", "Save now": "<PERSON><PERSON><PERSON> ngay", "Saving packages, please wait...": "<PERSON><PERSON> l<PERSON>, vui lòng đợi...", "Scoop Installer - WingetUI": "<PERSON><PERSON><PERSON><PERSON> cài đặt Scoop - WingetUI", "Scoop Uninstaller - WingetUI": "Trình gỡ cài đặt Scoop - WingetUI", "Scoop package": "<PERSON><PERSON><PERSON>", "Search": "<PERSON><PERSON><PERSON>", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "T<PERSON><PERSON> kiếm phần mềm dành cho máy tính để bàn, thông báo cho tôi khi có bản cập nhật và không làm những việc ngớ ngẩn. Tôi không muốn WingetUI quá phức tạp, tô<PERSON> chỉ muốn một <b>cửa hàng phần mềm</b> đơn giản thôi", "Search for packages": "<PERSON><PERSON><PERSON> k<PERSON> g<PERSON>i", "Search for packages to start": "<PERSON><PERSON><PERSON> kiếm các gói để bắt đầu", "Search mode": "<PERSON><PERSON> độ tìm kiếm", "Search on available updates": "<PERSON><PERSON><PERSON> kiếm trên các bản cập nhật có sẵn", "Search on your software": "<PERSON><PERSON><PERSON> kiếm trên phần mềm của bạn", "Searching for installed packages...": "<PERSON><PERSON> tìm kiếm các gói đã cài đặt...", "Searching for packages...": "<PERSON><PERSON> tìm c<PERSON>c g<PERSON>...", "Searching for updates...": "<PERSON><PERSON> tìm kiếm các bản cập nhật...", "Select": "<PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "Chọn \"{item}\" để thêm nhóm tùy chỉnh của bạn", "Select a folder": "<PERSON><PERSON><PERSON> một thư mục", "Select all": "<PERSON><PERSON><PERSON> tất cả", "Select all packages": "<PERSON><PERSON><PERSON> tất cả các gói", "Select backup": "<PERSON><PERSON><PERSON> bản sao lưu", "Select only <b>if you know what you are doing</b>.": "Chỉ chọn nó <b>nếu như bạn biết mình đang làm gì</b>.", "Select package file": "<PERSON><PERSON><PERSON> tập tin gói", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Chọn bản sao lưu bạn muốn mở. <PERSON><PERSON> đ<PERSON>, bạn sẽ có thể xem lại và chọn các gói/chư<PERSON><PERSON> trình muốn khôi phục.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "<PERSON><PERSON><PERSON> các tiến trình cần đóng trước khi gói phần mềm này được cài đặt, cập nhật hoặc gỡ bỏ.", "Select the source you want to add:": "<PERSON><PERSON><PERSON> nguồn bạn muốn thêm", "Select upgradable packages by default": "<PERSON><PERSON><PERSON> các gói có thể nâng cấp theo mặc định", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON> <b>trì<PERSON> quản lý gói</b> nào để sử dụng ({0}), đ<PERSON><PERSON> cấu hình cách cài đặt gói, quản lý cách xử lý quyền quản trị viên, v.v.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "<PERSON><PERSON><PERSON> b<PERSON>t tay. <PERSON><PERSON> chờ câu trả lời của người nghe... ({0}%)", "Set a custom backup file name": "Đặt tên tệp sao lưu tùy chỉnh", "Set custom backup file name": "Đặt tên tệp tin sao lưu tùy chỉnh", "Settings": "Cài đặt", "Share": "<PERSON><PERSON> sẻ", "Share WingetUI": "Chia sẻ WingetUI", "Share anonymous usage data": "<PERSON>a sẻ dữ liệu sử dụng ẩn danh", "Share this package": "Chia sẻ gói", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "<PERSON><PERSON>u bạn thay đổi cài đặt bảo mật, bạn sẽ cần mở lại bộ gói để các thay đổi có hiệu lực.", "Show UniGetUI on the system tray": "<PERSON><PERSON><PERSON> thị UniGetUI trên khay hệ thống", "Show UniGetUI's version and build number on the titlebar.": "<PERSON><PERSON><PERSON> thị phiên bản UniGetUI trên thanh tiêu đề", "Show WingetUI": "<PERSON><PERSON><PERSON> thị <PERSON>", "Show a notification when an installation fails": "Hi<PERSON>n thị thông báo khi cài đặt không thành công", "Show a notification when an installation finishes successfully": "<PERSON><PERSON>n thị thông báo khi quá trình cài đặt thành công", "Show a notification when an operation fails": "<PERSON><PERSON><PERSON> thị thông báo khi thao tác thất bại", "Show a notification when an operation finishes successfully": "<PERSON><PERSON><PERSON> thị thông báo khi một thao tác kết thúc thành công", "Show a notification when there are available updates": "<PERSON><PERSON><PERSON> thị thông báo ngay khi có bản cập nh<PERSON>t", "Show a silent notification when an operation is running": "Hi<PERSON>n thị thông báo im lặng khi một thao tác đang chạy", "Show details": "<PERSON><PERSON><PERSON> thị chi tiết", "Show in explorer": "<PERSON><PERSON><PERSON> thị trong trình <PERSON>", "Show info about the package on the Updates tab": "<PERSON><PERSON>n thị thông tin về gói trên bảng <PERSON>h<PERSON>t", "Show missing translation strings": "Hi<PERSON><PERSON> thị các chuỗi dịch bị thiếu", "Show notifications on different events": "<PERSON><PERSON><PERSON> thị thông báo về các sự kiện khác nhau", "Show package details": "<PERSON><PERSON><PERSON> thị chi tiết gói", "Show package icons on package lists": "<PERSON><PERSON>n thị biểu tượng gói trên danh sách gói", "Show similar packages": "<PERSON><PERSON><PERSON> c<PERSON>c gói liên quan", "Show the live output": "<PERSON><PERSON><PERSON> thị đầu ra trự<PERSON> tiếp", "Size": "<PERSON><PERSON><PERSON>", "Skip": "Bỏ qua", "Skip hash check": "Bỏ qua kiểm tra hash", "Skip hash checks": "Bỏ qua kiểm tra hàm băm", "Skip integrity checks": "Bỏ qua kiểm tra tính toàn vẹn", "Skip minor updates for this package": "Bỏ qua các cập nhật nhỏ cho gói này", "Skip the hash check when installing the selected packages": "Bỏ qua kiểm tra hash khi cài đặt các gói đã chọn", "Skip the hash check when updating the selected packages": "Bỏ qua việc kiểm tra hash khi cập nhật các gói đã chọn", "Skip this version": "Bỏ qua phiên bản này", "Software Updates": "<PERSON><PERSON><PERSON> nh<PERSON>t ph<PERSON><PERSON> mềm", "Something went wrong": "<PERSON><PERSON><PERSON><PERSON> g<PERSON> đó <PERSON>", "Something went wrong while launching the updater.": "<PERSON><PERSON> xảy ra sự cố khi khởi động trình cập nhật.", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Source URL:": "URL nguồn:", "Source added successfully": "<PERSON><PERSON><PERSON><PERSON> nguồn thành công", "Source addition failed": "<PERSON><PERSON><PERSON><PERSON> nguồn không thành công", "Source name:": "<PERSON><PERSON><PERSON> ng<PERSON>:", "Source removal failed": "<PERSON><PERSON><PERSON> nguồn không thành công", "Source removed successfully": "Gỡ bỏ nguồn thành công", "Source:": "Nguồn: ", "Sources": "<PERSON><PERSON><PERSON><PERSON>", "Start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Starting daemons...": "B<PERSON>t đầu daemons...", "Starting operation...": "<PERSON><PERSON>t đầu hoạt động...", "Startup options": "<PERSON><PERSON><PERSON> chọn khởi động", "Status": "<PERSON><PERSON><PERSON><PERSON> thái", "Stuck here? Skip initialization": "Bạn bị mắc kẹt ở đây? Bỏ qua việc khởi tạo", "Suport the developer": "Hỗ trợ nhà phát triển", "Support me": "Hỗ trợ tôi", "Support the developer": "Hỗ trợ nhà phát triển", "Systems are now ready to go!": "<PERSON><PERSON><PERSON> hệ thống hiện đã sẵn sàng hoạt động!", "Telemetry": "<PERSON>hu thập dữ liệu từ xa", "Text": "<PERSON><PERSON><PERSON>", "Text file": "<PERSON><PERSON><PERSON> tin văn bản", "Thank you ❤": "<PERSON><PERSON><PERSON> ơn bạn ❤", "Thank you 😉": "<PERSON><PERSON><PERSON> ơn bạn😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Trì<PERSON> quản lý gói của Rust.<br><PERSON><PERSON><PERSON>: <b><PERSON><PERSON><PERSON> thư viện Rust và các chương trình được viết bằng Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Bản sao lưu sẽ KHÔNG bao gồm bất kỳ tệp nhị phân nào hoặc bất kỳ dữ liệu đã lưu của chương trình nào.", "The backup will be performed after login.": "<PERSON><PERSON>n sao lưu sẽ được thực hiện sau khi đăng nhập.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Bản sao lưu sẽ bao gồm danh sách đầy đủ các gói đã cài đặt và các tùy chọn cài đặt của chúng. <PERSON><PERSON><PERSON> bản cập nhật bị bỏ qua và các phiên bản bị bỏ qua cũng sẽ được lưu lại.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON>hóm bạn đang cố tải có vẻ không hợp lệ. <PERSON><PERSON> lòng kiểm tra tập tin và thử lại.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Tổng kiểm tra của quy trình cài đặt không trùng với giá trị dự kiến và không thể xác minh tính xác thực của trình cài đặt. Nếu bạn tin tưởng nhà xuất bản, {0} gói sẽ bỏ qua kiểm tra hàm băm một lần nữa.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Tr<PERSON><PERSON> quản lý gói cổ điển cho Windows. Bạn sẽ tìm thấy mọi thứ ở đó.<br><PERSON><PERSON> gồm: <b><PERSON><PERSON><PERSON> mềm chung</b>", "The cloud backup completed successfully.": "<PERSON><PERSON> l<PERSON>u lên đám mây đã hoàn tất thành công.", "The cloud backup has been loaded successfully.": "<PERSON><PERSON>n sao lưu trên đám mây đã đư<PERSON><PERSON> tải thành công.", "The current bundle has no packages. Add some packages to get started": "Nhóm hiện tại không có gói nào. Thêm một số gói để bắt đầu", "The executable file for {0} was not found": "Tệ<PERSON> thực thi cho {0} không đ<PERSON><PERSON><PERSON> tìm thấy.", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "<PERSON><PERSON><PERSON> tùy chọn sau sẽ được áp dụng theo mặc định mỗi khi một gói {0} đượ<PERSON> cài đặt, nâng cấp hoặc gỡ bỏ.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "<PERSON><PERSON><PERSON> gói sau sẽ được xuất ra tệp JSON. Không có dữ liệu người dùng hoặc tệp nhị phân nào sẽ được lưu lại.", "The following packages are going to be installed on your system.": "<PERSON><PERSON><PERSON> gói sau sẽ được cài đặt vào hệ thống của bạn.", "The following settings may pose a security risk, hence they are disabled by default.": "<PERSON><PERSON><PERSON> thiết lập sau có thể gây rủi ro bả<PERSON> mật, do đó chúng bị vô hiệu hóa theo mặc định.", "The following settings will be applied each time this package is installed, updated or removed.": "<PERSON><PERSON><PERSON> cài đặt sau sẽ được áp dụng mỗi khi gói này được cài đặt, cập nhật hoặc gỡ bỏ.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "<PERSON><PERSON><PERSON> cài đặt sau sẽ được áp dụng mỗi khi gói này được cài đặt, cập nhật hoặc gỡ bỏ. Chúng sẽ được lưu tự động.", "The icons and screenshots are maintained by users like you!": "<PERSON><PERSON><PERSON> biểu tượng và ảnh chụp màn hình được bảo trì bởi những người dùng như bạn!", "The installer authenticity could not be verified.": "<PERSON><PERSON><PERSON><PERSON> thể xác minh tính xác thực của trình cài đặt.", "The installer has an invalid checksum": "<PERSON><PERSON><PERSON><PERSON> cài đặt có tổng kiểm tra không hợp lệ", "The installer hash does not match the expected value.": "<PERSON><PERSON><PERSON> của trình cài đặt không khớp với giá trị mong đợi.", "The local icon cache currently takes {0} MB": "Bộ nhớ đệm biểu tượng cục bộ hiện tại chiếm {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "<PERSON><PERSON><PERSON> tiêu ch<PERSON>h của dự án này là tạo ra một giao diện người dùng trực quan cho các trình quản lý gói dòng lệnh (CLI) phổ biến nhất dành cho <PERSON>, chẳng hạn như Winget và Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói \"{0}\" trên trình quản lý gói \"{1}\"", "The package bundle could not be created due to an error.": "<PERSON><PERSON><PERSON><PERSON> thể tạo nhóm gói do có lỗi.", "The package bundle is not valid": "Nhóm gói không hợp lệ", "The package manager \"{0}\" is disabled": "<PERSON><PERSON><PERSON><PERSON> quản lý gó<PERSON> \"{0}\" bị tắt", "The package manager \"{0}\" was not found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trình quản lý gói \"{0}\"", "The package {0} from {1} was not found.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gói {0} từ {1}.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "<PERSON><PERSON><PERSON> gó<PERSON> được liệt kê ở đây sẽ không được tính đến khi kiểm tra các bản cập nhật. Nhấp đúp vào chúng hoặc nhấp vào nút ở bên phải của chúng để ngừng bỏ qua các cập nhật của chúng.", "The selected packages have been blacklisted": "<PERSON><PERSON><PERSON> gói đã chọn đã đư<PERSON>c đưa vào danh sách đen", "The settings will list, in their descriptions, the potential security issues they may have.": "<PERSON><PERSON><PERSON> thiết lập sẽ liệt kê trong phần mô tả của chúng các vấn đề bảo mật tiềm ẩn.", "The size of the backup is estimated to be less than 1MB.": "<PERSON><PERSON><PERSON> thước của bản sao lưu được ước tính nhỏ hơn 1MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> {source} đ<PERSON> đ<PERSON><PERSON><PERSON> thêm vào {manager} thà<PERSON> công", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> {source} đ<PERSON> được xóa khỏi {manager} thành công", "The system tray icon must be enabled in order for notifications to work": "<PERSON><PERSON><PERSON><PERSON> tượng khay hệ thống phải đư<PERSON><PERSON> bật để các thông báo hoạt động", "The update process has been aborted.": "<PERSON><PERSON><PERSON> trình cập nhật đã bị hủy bỏ.", "The update process will start after closing UniGetUI": "<PERSON>u<PERSON> trình cập nhật sẽ bắt đầu sau khi đóng UniGetUI.", "The update will be installed upon closing WingetUI": "<PERSON><PERSON><PERSON> cậ<PERSON> nhật sẽ được cài đặt khi đóng WingetUI", "The update will not continue.": "<PERSON><PERSON><PERSON><PERSON> cập nhật sẽ không tiếp tục.", "The user has canceled {0}, that was a requirement for {1} to be run": "Ng<PERSON><PERSON>i dùng đã hủy bỏ{0}, đó là yêu cầu để chạy {1}", "There are no new UniGetUI versions to be installed": "<PERSON><PERSON><PERSON>ng có phiên bản UniGetUI mới nào để cài đặt", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "<PERSON><PERSON> các hoạt động đang diễn ra. Thoát UniGetUI có thể khiến chúng bị thất bại. Bạn có muốn tiếp tục không?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "<PERSON><PERSON> một số video tuyệt vời trên YouTube giới thiệu về UniGetUI và các khả năng của nó. Bạn có thể học được những thủ thuật và mẹo hữu ích!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Có hai lý do chính để không chạy WingetUI với tư cách quản trị viên: Lý do đầu tiên là trình quản lý gói Scoop có thể gây ra sự cố với một số lệnh khi chạy với quyền quản trị viên. Điều thứ hai là việc chạy WingetUI với tư cách quản trị viên có nghĩa là bất kỳ gói nào bạn tải xuống sẽ được chạy với tư cách quản trị viên (và điều này không an toàn). Hãy nhớ rằng nếu bạn cần cài đặt một gói cụ thể với tư cách quản trị viên, bạn luôn có thể nhấp chuột phải vào mục đó -> Cài đặt/C<PERSON><PERSON> nhật/Gỡ cài đặt với tư cách quản trị viên.", "There is an error with the configuration of the package manager \"{0}\"": "<PERSON><PERSON> xảy ra lỗi với cấu hình của trình quản lý gói \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Có một quá trình cài đặt đang diễn ra. <PERSON>ếu bạn đóng WingetUI, quá trình cài đặt có thể không thành công và có kết quả không mong muốn. Bạn vẫn muốn thoát khỏi WingetUI?", "They are the programs in charge of installing, updating and removing packages.": "<PERSON><PERSON><PERSON> là những chương trình chịu trách nhiệm cài đặt, cập nhật và gỡ bỏ các gói.", "Third-party licenses": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> bên thứ ba", "This could represent a <b>security risk</b>.": "<PERSON><PERSON><PERSON><PERSON> này có thể biểu thị một <b>rủ<PERSON> ro bảo mật</b>.", "This is not recommended.": "<PERSON><PERSON><PERSON><PERSON> nà<PERSON> không đ<PERSON><PERSON><PERSON> khu<PERSON>ến nghị.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "<PERSON>i<PERSON>u này có thể là do gói bạn được gửi đã bị xóa hoặc được xuất bản trên trình quản lý gói mà bạn chưa bật. ID nhận được là {0}", "This is the <b>default choice</b>.": "<PERSON><PERSON><PERSON> là <b>l<PERSON><PERSON> chọn mặc định</b>.", "This may help if WinGet packages are not shown": "<PERSON><PERSON><PERSON><PERSON> này có thể hữu ích nếu các gói WinGet không đư<PERSON>c hiển thị", "This may help if no packages are listed": "<PERSON><PERSON><PERSON>u này có thể hữu ích nếu không có gói nào được liệt kê", "This may take a minute or two": "<PERSON><PERSON><PERSON><PERSON> này có thể mất một hoặc hai phút", "This operation is running interactively.": "<PERSON><PERSON>t động này đang chạy tương tác.", "This operation is running with administrator privileges.": "<PERSON><PERSON><PERSON> động này đang chạy với quyền quản trị.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "<PERSON><PERSON><PERSON> chọn này SẼ gây sự cố. <PERSON><PERSON><PERSON> thao tác không thể tự nâng quyền SẼ THẤT BẠI. Việc cài đặt/cập nhật/gỡ bỏ với quyền quản trị SẼ KHÔNG HOẠT ĐỘNG", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "<PERSON><PERSON> gói này có một số thiết lập tiềm ẩn nguy hiểm và có thể bị bỏ qua theo mặc định.", "This package can be updated": "<PERSON><PERSON><PERSON> n<PERSON>y có thể cập nhật", "This package can be updated to version {0}": "<PERSON><PERSON><PERSON> này có thể được cập nhật lên phiên bản {0}", "This package can be upgraded to version {0}": "<PERSON><PERSON><PERSON> này có thể được nâng cấp lên phiên bản {0}", "This package cannot be installed from an elevated context.": "<PERSON><PERSON><PERSON> này không thể được cài đặt từ một ngữ cảnh nâng cao.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "G<PERSON><PERSON> này không có ảnh chụp màn hình hoặc bị thiếu biểu tượng? Hãy đóng góp cho UniGetUI bằng cách thêm các biểu tượng và ảnh chụp màn hình bị thiếu vào cơ sở dữ liệu mở và công cộng của chúng tôi.", "This package is already installed": "<PERSON><PERSON><PERSON> này đã được cài đặt", "This package is being processed": "<PERSON><PERSON><PERSON> này đang đ<PERSON> xử lý", "This package is not available": "<PERSON><PERSON><PERSON> này không có sẵn", "This package is on the queue": "<PERSON><PERSON><PERSON> này đang trong hàng đợi", "This process is running with administrator privileges": "Qu<PERSON> trình này đang chạy với quyền quản trị viên", "This project has no connection with the official {0} project — it's completely unofficial.": "Dự án này không liên quan gì đến dự án {0} ch<PERSON>h thức — nó hoàn toàn không chính thức.", "This setting is disabled": "Cài đặt này bị vô hiệu hóa", "This wizard will help you configure and customize WingetUI!": "Trình hướng dẫn này sẽ giúp bạn định cấu hình và tùy chỉnh WingetUI!", "Toggle search filters pane": "Chuyển đổ<PERSON> bảng bộ lọc tìm kiếm", "Translators": "<PERSON><PERSON><PERSON><PERSON>", "Try to kill the processes that refuse to close when requested to": "Cố gắng kết thúc các tiến trình không chịu đóng khi được yêu cầu", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Bật tùy chọn này cho phép thay đổi tệp thực thi được dùng để tương tác với trình quản lý gói. Mặc dù điều này giúp bạn tùy chỉnh quá trình cài đặt một cách chi tiết hơn, nhưng nó cũng có thể gây nguy hiểm", "Type here the name and the URL of the source you want to add, separed by a space.": "<PERSON><PERSON><PERSON><PERSON> tên và đường dẫn nguồn bạn muốn thêm vào đây, cách nhau bởi khoảng trắng", "Unable to find package": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy gói", "Unable to load informarion": "<PERSON><PERSON><PERSON><PERSON> thể tải thông tin", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI thu thập dữ liệu sử dụng ẩn danh để cải thiện trải nghiệm người dùng.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI thu thập dữ liệu sử dụng ẩn danh với mục đích duy nhất là hiểu và cải thiện trải nghiệm người dùng.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI đã phát hiện một phím tắt trên màn hình mới có thể được xóa tự động.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI đã phát hiện các phím tắt trên màn hình sau đây có thể được xóa tự động trong các bản nâng cấp tương lai.", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI đã phát hiện {0} lối tắt trên màn hình mới có thể được xóa tự động.", "UniGetUI is being updated...": "UniGetUI đang đư<PERSON> cập nhật...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI không liên quan đến bất kỳ trình quản lý gói tương thích nào. UniGetUI là một dự án độc lập.", "UniGetUI on the background and system tray": "UniGetUI chạy trong nền và khay hệ thống", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI hoặc một số thành phần của nó đang bị thiếu hoặc bị hỏng.", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI yêu cầu {0} hoạt động nhưng không tìm thấy nó trên hệ thống của bạn.", "UniGetUI startup page:": "Trang khởi động UniGetUI:", "UniGetUI updater": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> nhật UniGetUI", "UniGetUI version {0} is being downloaded.": "<PERSON><PERSON> tải xuống UniGetUI phiên bản {0}", "UniGetUI {0} is ready to be installed.": "UniGetUI phiên bản {0} đã sẵn sàng để cài đặt", "Uninstall": "Gỡ cài đặt", "Uninstall Scoop (and its packages)": "Gỡ cài đặt Scoop (và các gói của nó)", "Uninstall and more": "Gỡ cài đặt và các tùy chọn khác", "Uninstall and remove data": "Gỡ cài đặt và xóa dữ liệu", "Uninstall as administrator": "Gỡ cài đặt với tư cách quản trị viên", "Uninstall canceled by the user!": "Người dùng đã hủy việc gỡ cài đặt!", "Uninstall failed": "Gỡ cài đặt không thành công", "Uninstall options": "<PERSON><PERSON><PERSON> ch<PERSON>n gỡ cài đặt", "Uninstall package": "Gỡ cài đặt gói", "Uninstall package, then reinstall it": "Gỡ rồi cài đặt lại gói", "Uninstall package, then update it": "Gỡ r<PERSON>i cập nhật gói", "Uninstall previous versions when updated": "Gỡ bỏ các phiên bản trước đó khi cập nhật", "Uninstall selected packages": "Gỡ cài đặt các gói đã chọn", "Uninstall selection": "Gỡ bỏ các mục đã chọn", "Uninstall succeeded": "Gỡ cài đặt thành công", "Uninstall the selected packages with administrator privileges": "Gỡ cài đặt các gói đã chọn với quyền quản trị viên", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "<PERSON><PERSON><PERSON> gói không thể cài đặt có nguồn gốc được liệt kê là \"{0}\" không được xuất bản trên bất kỳ trình quản lý gói nào nên không có sẵn thông tin để hiển thị về chúng.", "Unknown": "<PERSON><PERSON><PERSON><PERSON> bi<PERSON>t", "Unknown size": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> không xác đ<PERSON>", "Unset or unknown": "<PERSON><PERSON><PERSON> thiết lập hoặc không xác định", "Up to date": "<PERSON><PERSON> cập nh<PERSON>t", "Update": "<PERSON><PERSON><PERSON>", "Update WingetUI automatically": "<PERSON><PERSON> động cập nh<PERSON>t WingetUI", "Update all": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> bộ", "Update and more": "<PERSON><PERSON><PERSON> nh<PERSON>t và các tùy chọn khác", "Update as administrator": "<PERSON><PERSON><PERSON> nh<PERSON>t với tư cách quản trị viên", "Update check frequency, automatically install updates, etc.": "<PERSON><PERSON><PERSON> su<PERSON>t kiểm tra cập nhật, tự động cài đặt cập nhật, v.v.", "Update date": "<PERSON><PERSON><PERSON> c<PERSON>", "Update failed": "<PERSON><PERSON><PERSON> nhật không thành công", "Update found!": "<PERSON><PERSON><PERSON> cập nhật đ<PERSON><PERSON><PERSON> tìm thấy!", "Update now": "<PERSON><PERSON><PERSON> nh<PERSON>t b<PERSON>y giờ", "Update options": "<PERSON><PERSON><PERSON> ch<PERSON> cập nh<PERSON>t", "Update package indexes on launch": "<PERSON><PERSON><PERSON> nhật chỉ mục gói khi khởi chạy", "Update packages automatically": "<PERSON><PERSON><PERSON> nh<PERSON>t các gói tự động", "Update selected packages": "<PERSON><PERSON><PERSON> nh<PERSON>t các gói đã chọn", "Update selected packages with administrator privileges": "<PERSON><PERSON><PERSON> nh<PERSON>t các gói đã chọn với quyền qtv", "Update selection": "<PERSON><PERSON><PERSON> nh<PERSON>t các mục đã chọn", "Update succeeded": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "Update to version {0}": "<PERSON><PERSON><PERSON> nh<PERSON>t lên phiên bản {0}", "Update to {0} available": "<PERSON><PERSON> c<PERSON> bản cập nhật lên {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "<PERSON><PERSON>p nhật tệp port Git của vcpkg tự động (yêu cầu Git được cài đặt).", "Updates": "<PERSON><PERSON><PERSON>", "Updates available!": "<PERSON><PERSON><PERSON> nhật khả dụng!", "Updates for this package are ignored": "<PERSON><PERSON><PERSON> bản cập nhật cho gói này bị bỏ qua", "Updates found!": "<PERSON><PERSON> tìm thấy bản cập nhật!", "Updates preferences": "<PERSON><PERSON><PERSON> nh<PERSON>t tù<PERSON> chọn", "Updating WingetUI": "<PERSON><PERSON> cập nh<PERSON>t WingetUI", "Url": "Url", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Sử dụng WinGet đi kèm gói kế thừa thay vì PowerShell CMDlets", "Use a custom icon and screenshot database URL": "Sử dụng URL cơ sở dữ liệu biểu tượng và ảnh chụp màn hình tùy chỉnh", "Use bundled WinGet instead of PowerShell CMDlets": "Sử dụng WinGet đi kèm thay vì PowerShell CMDlets", "Use bundled WinGet instead of system WinGet": "Sử dụng WinGet đi kèm thay vì WinGet hệ thống", "Use installed GSudo instead of UniGetUI Elevator": "Sử dụng GSudo đã cài thay cho UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Sử dụng GSudo đã cài đặt thay vì gói đi kèm (yêu cầu khởi động lại ứng dụng)", "Use system Chocolatey": "<PERSON><PERSON> dụng hệ thống <PERSON>", "Use system Chocolatey (Needs a restart)": "<PERSON><PERSON> dụng hệ thống <PERSON> (Cần khởi động lại)", "Use system Winget (Needs a restart)": "<PERSON>ử dụng hệ thống Winget (Cần khởi động lại)", "Use system Winget (System language must be set to english)": "Sử dụng hệ thống Winget (Ngôn ngữ hệ thống phải được đặt thành tiếng <PERSON>h)", "Use the WinGet COM API to fetch packages": "Sử dụng API WinGet COM để tìm nạp gói", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Sử dụng Module WinGet PowerShell thay vì WinGet COM API", "Useful links": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON> dùng", "User interface preferences": "<PERSON><PERSON><PERSON> chọn giao diện người dùng", "User | Local": "<PERSON>ư<PERSON><PERSON> dùng | <PERSON><PERSON><PERSON> ph<PERSON>", "Username": "<PERSON><PERSON><PERSON> dùng", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Việc sử dụng WingetUI ngụ ý việc chấp nhận Gi<PERSON>y phép <PERSON>ông cộng Chung v2.1 GNU Ít hơn", "Using WingetUI implies the acceptation of the MIT License": "<PERSON><PERSON> dụng WingetUI ngụ ý việc chấp nhận <PERSON> ph<PERSON>p <PERSON>", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "<PERSON><PERSON><PERSON><PERSON> tìm thấy gốc vcpkg. <PERSON>ui lòng xác định biến môi trường %VCPKG_ROOT% hoặc xác định nó từ Cài đặt UniGetUI.", "Vcpkg was not found on your system.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vcpkg trên hệ thống của bạn.", "Verbose": "<PERSON> ti<PERSON>", "Version": "<PERSON><PERSON><PERSON>", "Version to install:": "<PERSON><PERSON><PERSON> bản để cài đặt:", "Version:": "<PERSON><PERSON><PERSON> b<PERSON>:", "View GitHub Profile": "<PERSON><PERSON> s<PERSON>", "View WingetUI on GitHub": "Xem WingetUI trên GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Xem mã nguồn của WingetUI. T<PERSON> đó, bạn có thể báo cáo lỗi hoặc đề xuất các tính năng hoặc thậm chí đóng góp trực tiếp cho Dự án WingetUI", "View mode:": "<PERSON><PERSON> độ xem:", "View on UniGetUI": "<PERSON><PERSON> trên <PERSON>iGet<PERSON>", "View page on browser": "<PERSON><PERSON> trang trên trì<PERSON>", "View {0} logs": "<PERSON>em {0} nh<PERSON>t ký", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "<PERSON><PERSON> cho thiết bị kết nối internet trước khi thực hiện các tác vụ yêu cầu kết nối internet.", "Waiting for other installations to finish...": "<PERSON><PERSON> đợi các cài đặt khác hoàn tất...", "Waiting for {0} to complete...": "<PERSON><PERSON> chờ {0} hoàn thành...", "Warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Warning!": "<PERSON><PERSON>nh báo!", "We are checking for updates.": "<PERSON><PERSON><PERSON> tôi đang kiểm tra các bản cập nh<PERSON>t.", "We could not load detailed information about this package, because it was not found in any of your package sources": "<PERSON>úng tôi không thể tải thông tin chi tiết về gói này vì nó không được tìm thấy trong bất kỳ nguồn gói nào của bạn", "We could not load detailed information about this package, because it was not installed from an available package manager.": "<PERSON><PERSON>g tôi không thể tải thông tin chi tiết về gói này vì nó không được cài đặt từ trình quản lý gói có sẵn.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "<PERSON>úng tôi không thể {action} {package}. <PERSON><PERSON> lòng thử lại sau. Nhấp vào \"{showDetails}\" để lấy nhật ký từ trình cài đặt.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "<PERSON>úng tôi không thể {action} {package}. <PERSON><PERSON> lòng thử lại sau. Nhấp vào \"{showDetails}\" để lấy nhật ký từ trình gỡ cài đặt.", "We couldn't find any package": "<PERSON><PERSON>g tôi không thể tìm thấy bất kỳ gói nào", "Welcome to WingetUI": "Chào mừng tới WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": "<PERSON>hi cài đặt hàng loạt các gói từ một bộ, hãy cài cả những gói đã được cài đặt trước đó", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "<PERSON><PERSON> phát hiện các phím tắt mới, hãy tự động xóa chúng thay vì hiển thị hộp thoại này.", "Which backup do you want to open?": "Bạn muốn mở bản sao lưu nào?", "Which package managers do you want to use?": "Bạn muốn sử dụng trình quản lý gói nào?", "Which source do you want to add?": "Bạn muốn thêm nguồn nào?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Mặc dù Winget có thể được sử dụng trong WingetUI nhưng WingetUI có thể được sử dụng với các trình quản lý gói <PERSON>h<PERSON>, điều này có thể gây nhầm lẫn. <PERSON><PERSON><PERSON><PERSON><PERSON> đ<PERSON>, WingetUI được thiết kế để chỉ hoạt động với Winget, nhưng điều này không còn đúng nữa và do đó WingetUI không đại diện cho mục tiêu mà dự án này hướng tới.", "WinGet could not be repaired": "WinGet không thể sửa chữa được", "WinGet malfunction detected": "<PERSON><PERSON> phát hiện sự cố WinGet", "WinGet was repaired successfully": "WinGet đã đư<PERSON><PERSON> sửa chữa thành công", "WingetUI": "WingetUI", "WingetUI - Everything is up to date": "WingetUI - <PERSON><PERSON><PERSON> thứ đều đ<PERSON><PERSON><PERSON> cập nhật", "WingetUI - {0} updates are available": "WingetUI - <PERSON><PERSON> sẵn {0} b<PERSON><PERSON> c<PERSON>p nh<PERSON>t", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "Trang chủ WingetUI", "WingetUI Homepage - Share this link!": "Trang chủ WingetUI - Chia sẻ liên kết này!", "WingetUI License": "Giấy phép WingetU", "WingetUI Log": "Nhật ký WingetUI", "WingetUI Repository": "<PERSON><PERSON> l<PERSON> tr<PERSON> WingetUI", "WingetUI Settings": "Cài đặt WingetUI", "WingetUI Settings File": "<PERSON><PERSON><PERSON> cài đặt WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI Sử dụng các thư viện sau. <PERSON><PERSON><PERSON> không có họ, WingetUI sẽ không thể tồn tại được", "WingetUI Version {0}": "UniGetUI Phi<PERSON> bản {0}", "WingetUI autostart behaviour, application launch settings": "<PERSON><PERSON><PERSON> vi tự khởi động của WingetUI, cài đặt khởi chạy ứng dụng", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI có thể kiểm tra xem phần mềm của bạn có sẵn các bản cập nhật hay không và tự động cài đặt chúng nếu bạn muốn nó", "WingetUI display language:": "<PERSON><PERSON><PERSON> ngữ hiển thị WingetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI đã được chạy với tư cách quản trị viên, điề<PERSON> này không được khuyến khích. Khi chạy WingetUI với tư cách quản trị viên, MỌI thao tác được khởi chạy từ WingetUI sẽ có đặc quyền của quản trị viên. Bạn vẫn có thể sử dụng chương trình nhưng chúng tôi khuyên bạn không nên chạy WingetUI với đặc quyền của quản trị viên.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "WingetUI đã đư<PERSON><PERSON> dịch sang hơn 40 ngôn ngữ nhờ các dịch giả tình nguyện. Cảm ơn bạn 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI chưa được máy dịch. Những người dùng sau đây đã chịu trách nhiệm về các bản dịch:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI là một ứng dụng giúp việc quản lý phần mềm của bạn dễ dàng hơn bằng cách cung cấp giao diện đồ họa tất cả trong một cho trình quản lý gói dòng lệnh của bạn.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI đang được đổi tên để nhấn mạnh sự khác biệt giữa WingetUI (giao diện bạn đang sử dụng) và Winget (trình quản lý gói do Microsoft phát triển mà tôi không liên quan)", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI đang được cập nhật. <PERSON><PERSON> tất, WingetUI sẽ tự khởi động lại", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI là miễn phí và sẽ miễn phí mãi mãi. Không có quảng cáo, không có thẻ tín dụng, không có phiên bản cao cấp. <PERSON><PERSON><PERSON> phí 100%, mãi mãi.", "WingetUI log": "Nhật ký WingetUI", "WingetUI tray application preferences": "<PERSON><PERSON><PERSON> ch<PERSON>n k<PERSON>ng dụng WingetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI sử dụng các thư viện sau. <PERSON><PERSON><PERSON> không có họ, WingetUI sẽ không thể tồn tại được.", "WingetUI version {0} is being downloaded.": "<PERSON><PERSON><PERSON> bản WingetUI {0} đang đư<PERSON><PERSON> tải xuống.", "WingetUI will become {newname} soon!": "WingetUI sẽ sớm được đặt tên là {newname}.", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI sẽ không kiểm tra cập nhật định kỳ. Chúng sẽ vẫn được kiểm tra khi khởi chạy, nhưng bạn sẽ không được cảnh báo về chúng.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI sẽ hiển thị lời nhắc UAC mỗi khi gói yêu cầu cài đặt nâng cao.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI sẽ sớm được đặt tên là {newname}. Điều này sẽ không đại diện cho bất kỳ thay đổi nào trong ứng dụng. T<PERSON><PERSON> (nhà phát triển) sẽ tiếp tục phát triển dự án này như tôi đang làm hiện tại, nhưng dưới một cái tên khác.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI sẽ không thể thực hiện được nếu không có sự giúp đỡ của những người đóng góp thân yêu của chúng tôi. Hãy xem hồ sơ GitHub của họ, WingetUI sẽ không thể thực hiện được nếu không có họ!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI sẽ không thể thực hiện được nếu không có sự giúp đỡ của những người đóng góp. Cảm ơn tất cả các bạn 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} đã sẵn sàng để cài đặt.", "Write here the process names here, separated by commas (,)": "<PERSON><PERSON><PERSON><PERSON> tên các tiến trình vào đây, c<PERSON>ch nhau bằng dấu phẩy (,)", "Yes": "<PERSON><PERSON>", "You are logged in as {0} (@{1})": "Bạn đang đăng nhập với tài k<PERSON>n {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Bạn có thể thay đổi hành vi này trong phần cài đặt bảo mật của UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Bạn có thể xác định các lệnh sẽ được thực thi trước hoặc sau khi gói phần mềm này được cài đặt, cập nhật hoặc gỡ bỏ. Các lệnh này sẽ chạy trong cửa sổ dòng lệnh, vì vậy các script CMD đều hoạt động được ở đây.", "You have currently version {0} installed": "<PERSON>ện tại bạn đã cài đặt phiên bản {0}", "You have installed WingetUI Version {0}": "Bạn đã cài đặt Phiên bản WingetUI {0}", "You may lose unsaved data": "<PERSON><PERSON>n có thể mất dữ liệu chưa đ<PERSON><PERSON><PERSON> lưu", "You may need to install {pm} in order to use it with WingetUI.": "Bạn có thể cần cài đặt {pm} để sử dụng nó với WingetUI.", "You may restart your computer later if you wish": "Bạn có thể khởi động lại máy tính của mình sau nếu muốn", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Bạn sẽ chỉ được nhắc một lần và quyền quản trị viên sẽ được cấp cho các gói yêu cầu chúng.", "You will be prompted only once, and every future installation will be elevated automatically.": "Bạn sẽ chỉ được nhắc một lần và mọi cài đặt trong tương lai sẽ tự động chuyển thành cài đặt nâng cao", "You will likely need to interact with the installer.": "Bạn có khả năng sẽ cần tương tác với trình cài đặt.", "[RAN AS ADMINISTRATOR]": "CHẠY DƯỚI QUYỀN QUẢN TRỊ VIÊN", "buy me a coffee": "Mua tôi một cốc cafe", "extracted": "đ<PERSON> giải nén", "feature": "t<PERSON><PERSON> n<PERSON>ng", "formerly WingetUI": "trước đây là WingetUI", "homepage": "trang chủ", "install": "cài đặt", "installation": "sự cài đặt ", "installed": "Đã cài đặt", "installing": "đang cài", "library": "<PERSON><PERSON> vi<PERSON>n", "mandatory": "b<PERSON><PERSON> b<PERSON>", "option": "l<PERSON><PERSON> ch<PERSON>n", "optional": "t<PERSON><PERSON>n", "uninstall": "gỡ cài đặt", "uninstallation": "Sự gỡ cài đặt", "uninstalled": "đã gỡ thành công", "uninstalling": "đang gỡ", "update(noun)": "c<PERSON><PERSON> n<PERSON>t", "update(verb)": "c<PERSON><PERSON> n<PERSON>t", "updated": "<PERSON><PERSON> cập nh<PERSON>t", "updating": "<PERSON><PERSON> cập nh<PERSON>t", "version {0}": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> {0}", "{0} Install options are currently locked because {0} follows the default install options.": "<PERSON><PERSON><PERSON> tùy chọn cài đặt của {0} hiện đang bị khóa vì {0} đang tuân theo thiết lập cài đặt mặc định.", "{0} Uninstallation": "Gỡ cài đặt {0}", "{0} aborted": "{0} đã hủy bỏ", "{0} can be updated": "{0} có thể cập nhật", "{0} can be updated to version {1}": "{0} có thể được cập nhật lên phiên bản {1}", "{0} days": "{0} ngày", "{0} desktop shortcuts created": "{0} l<PERSON><PERSON> tắt màn hình đã tạo", "{0} failed": "{0} đ<PERSON> thất bại", "{0} has been installed successfully.": "{0} đã được cài đặt thành công.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} đã được cài đặt thành công. Nên khởi động lại UniGetUI để hoàn tất quá trình cài đặt", "{0} has failed, that was a requirement for {1} to be run": "{0} đã thất bại, đó là một yêu cầu cho {1} đ<PERSON><PERSON><PERSON> chạy", "{0} homepage": "<PERSON><PERSON> chủ {0}", "{0} hours": "{0} giờ", "{0} installation": "Cài đặt {0}", "{0} installation options": "<PERSON><PERSON><PERSON> ch<PERSON>n cài đặt {0}", "{0} installer is being downloaded": "<PERSON><PERSON><PERSON><PERSON> cài đặt {0} đang được tải xuống", "{0} is being installed": "{0} đang đ<PERSON> cài đặt", "{0} is being uninstalled": "{0} đang đượ<PERSON> gỡ cài đặt", "{0} is being updated": "{0} <PERSON><PERSON> đ<PERSON><PERSON><PERSON> c<PERSON> nh<PERSON>t", "{0} is being updated to version {1}": "{0} <PERSON><PERSON> đ<PERSON><PERSON><PERSON> cập nhật lên phiên bản {1}", "{0} is disabled": "{0} đ<PERSON> bị vô hiệu hóa", "{0} minutes": "{0} ph<PERSON>t", "{0} months": "{0} tháng", "{0} packages are being updated": "{0} g<PERSON><PERSON> tài nguyên đang đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "{0} packages can be updated": "{0} g<PERSON><PERSON> có thể cập nhật", "{0} packages found": "<PERSON><PERSON> tìm thấy {0} tài nguyên!", "{0} packages were found": "{0} g<PERSON>i tài nguyên đã đư<PERSON><PERSON> tìm thấy", "{0} packages were found, {1} of which match the specified filters.": "<PERSON><PERSON> tìm thấy {0} g<PERSON><PERSON>, {1} trong số đó phù hợp với các bộ lọc được chỉ định.", "{0} settings": "{0} cài đặt", "{0} status": "<PERSON><PERSON><PERSON><PERSON> thái {0}", "{0} succeeded": "{0} đ<PERSON> thành công", "{0} update": "{0} c<PERSON><PERSON> nh<PERSON>t", "{0} updates are available": "<PERSON><PERSON> {0} b<PERSON><PERSON> c<PERSON><PERSON> n<PERSON>t", "{0} was {1} successfully!": "{0}/{1} đã thành công", "{0} weeks": "{0} tu<PERSON>n", "{0} years": "{0} năm", "{0} {1} failed": "{0} {1} thất b<PERSON>i", "{package} Installation": "Cài đặt {package}", "{package} Uninstall": "Gỡ cài đặt {package}", "{package} Update": "<PERSON><PERSON><PERSON> cập nh<PERSON> {package}", "{package} could not be installed": "{package} không thể cài đặt", "{package} could not be uninstalled": "{package} không thể gỡ", "{package} could not be updated": "{package} không thể cập nhật", "{package} installation failed": "Cài đặt {package} thất bại", "{package} installer could not be downloaded": "Tr<PERSON><PERSON> cài đặt {package} không thể tải xuống", "{package} installer download": "{package} trình cài đặt tải xuống", "{package} installer was downloaded successfully": "Trình cài đặt {package} đã được tải xuống thành công", "{package} uninstall failed": "Gỡ cài đặt {package} thất bại", "{package} update failed": "<PERSON><PERSON><PERSON> nhật {package} thất bại", "{package} update failed. Click here for more details.": "<PERSON><PERSON><PERSON> nhật {package} không thành công. Bấm vào đây để biết thêm chi tiết.", "{package} was installed successfully": "\n{package} đã được cài đặt thành công", "{package} was uninstalled successfully": "\n{package} đã được gỡ thành công", "{package} was updated successfully": "{package} đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "{pcName} installed packages": "Gói {pcName} đã cài đặt", "{pm} could not be found": "{pm} không thể tìm thấy", "{pm} found: {state}": "{pm} đ<PERSON><PERSON><PERSON> tìm thấy: {state}", "{pm} is disabled": "{pm} đã bị vô hiệu hóa", "{pm} is enabled and ready to go": "{pm} đã đư<PERSON><PERSON> bật và sẵn sàng hoạt động", "{pm} package manager specific preferences": "{pm} t<PERSON><PERSON> chọn cụ thể của trình quản lý gói", "{pm} preferences": "<PERSON><PERSON><PERSON> {pm}", "{pm} version:": "{pm} p<PERSON><PERSON><PERSON> bản:", "{pm} was not found!": "{pm} không tìm thấy!"}