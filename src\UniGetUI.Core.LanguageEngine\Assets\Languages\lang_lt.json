{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(Paskutinį kartą tikrinta: {0})", "(Number {0} in the queue)": "(<PERSON><PERSON><PERSON> – {0} e<PERSON><PERSON><PERSON>)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@d<PERSON><PERSON><PERSON>1959, <PERSON><PERSON><PERSON><PERSON>, @martyn3z", "0 packages found": "Paketų nerasta", "0 updates found": "Atnaujinimų nerasta", "1 - Errors": "1 <PERSON> <PERSON><PERSON><PERSON>", "1 day": "1 dieną", "1 hour": "1 valandą", "1 month": "1 mėnesį", "1 package was found": "1-as paketas buvo rastas", "1 update is available": "Pasiekiamas 1-as at<PERSON><PERSON><PERSON><PERSON><PERSON>/nau<PERSON><PERSON>", "1 week": "1 savaitę", "1 year": "1 metus", "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": "2 – Įspėjimai", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": "3 – Informacija (mažiau)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": "4 – Informacija (daugiau)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": "5 – Informacija (klaidų šalinimui)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Populiari „C“/„C++“ bibliotekų tvarkyklė/-uvė. Pilna „C“/„C++“ bibliotekų ir kitokių susijusių įrankių<br>Sudaro: <b>„C“/„C++“ bibliotekas ir susijusius įrankius</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "<PERSON><PERSON><PERSON><PERSON>, pilna įrankių ir v<PERSON>jų, sukurti su „Microsoft'o“ „.NET ekosistemą“ mintyje.<br>Sudaro: <b>„.NET“ susijsiais įrankiais ir skriptais</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "<PERSON><PERSON><PERSON><PERSON>, pilna įrankių, sukurt<PERSON> su „Microsoft'o“ „.NET ekosistemą“ mintyje.<br>Sudaro: <b>„.NET“ susijsiais įrankiais</b>", "A restart is required": "Paleid<PERSON>s <PERSON>, yra <PERSON>", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "<PERSON><PERSON>", "About Qt6": "A<PERSON> „Qt6“", "About WingetUI": "Apie „UniGetUI“", "About WingetUI version {0}": "<PERSON><PERSON> „UniGetUI“ versiją – {0}", "About the dev": "<PERSON><PERSON> k<PERSON>", "Accept": "<PERSON><PERSON><PERSON><PERSON>", "Action when double-clicking packages, hide successful installations": "Veiksmas d<PERSON>gu<PERSON>i paspaudus ant paketų, paslėpti sėkmingo įdiegimo metu", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a source to {0}": "<PERSON><PERSON><PERSON><PERSON> šaltinį prie – {0}", "Add a timestamp to the backup file names": "Pridėti laiko žymę prie atsarginių failų pavadinimų", "Add a timestamp to the backup files": "Pridėti laiko žymę prie atsarginių failų", "Add packages or open an existing bundle": "<PERSON><PERSON><PERSON><PERSON> paketus arba atidaryti jau esantį rinkinį", "Add packages or open an existing package bundle": "Pridėkite paketus arba atidarykite jau egzistuojantį paketo/-ų rinkinį", "Add packages to bundle": null, "Add packages to start": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>, <PERSON><PERSON> p<PERSON>", "Add selection to bundle": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> į rinkinį", "Add source": "<PERSON><PERSON><PERSON><PERSON> šaltinį", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": "<PERSON><PERSON><PERSON><PERSON><PERSON> – „{source}“ į „{manager}“", "Addition succeeded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Administrator privileges": "<PERSON><PERSON><PERSON> privilegijos", "Administrator privileges preferences": "Administratoriaus privilegijų parinktys", "Administrator rights": "<PERSON><PERSON><PERSON>", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "V<PERSON> failai", "All versions": "Visos versijos", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "Leisti paketų operacijos atlikti lygiagrečiai", "Allow parallel installs (NOT RECOMMENDED)": "Le<PERSON><PERSON> lygiagrečius įdiegimus (NEREKOMENDUOJAMA)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "<PERSON><PERSON><PERSON> „{pm}“ operacijas atlikti lygiagrečiai", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "<PERSON><PERSON> at<PERSON>, <PERSON><PERSON><PERSON> įdiegti – „{0}“ vykdant šią nurodančią komandą „Windows PowerShell“ lange:", "Always elevate {pm} installations by default": "Visada numatytai <PERSON> „{pm}“ įdiegimus", "Always run {pm} operations with administrator rights": "Visada vyk<PERSON>ti „{pm}“ operacijas su administratoriaus teisėmis", "An error occurred": "Įvyko klaida", "An error occurred when adding the source: ": "Pridėdant šaltinį įvyko klaida:", "An error occurred when attempting to show the package with Id {0}": "Įvyko k<PERSON>, bandant <PERSON><PERSON> paket<PERSON> su <PERSON> – {0}", "An error occurred when checking for updates: ": "Tikrinant ar yra at<PERSON> įvyko klaida:", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "Apdorojant šį paketą įvyko klaida:", "An error occurred:": "Įvyko klaida:", "An interal error occurred. Please view the log for further details.": "Įvyko vidin<PERSON> klaida. <PERSON>raš<PERSON> peržiūrėti žurnalą, norint su<PERSON><PERSON> daug<PERSON>.", "An unexpected error occurred:": "Įvyko netik<PERSON>ta k<PERSON>a:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Įvyko netikėta problema/klaida, kol buvo bandoma taisyti „WinGet“. Prašome bandyti dar kartą, vėlesniu laiku", "An update was found!": "Buvo rastas atnaujinimas!", "Android Subsystem": "„Android“ posistemis", "Another source": "Kitas/-oks <PERSON>", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": "Bet kokie neišsaugoti pakeitimai bus prarasti", "App Name": "Programos pavadinimas", "Appearance": "Išvaizda", "Application theme, startup page, package icons, clear successful installs automatically": "Aplikacijos/Programos <PERSON>, paleisties pusla<PERSON>, paketo/-ų piktogramos ir išvalyti sėkmingus įdiegimus automatiškai.", "Application theme:": "Programos apipavidalinimas:", "Apply": "Pritaikyti", "Architecture to install:": "Architektūra, kurią įdiegti:", "Are these screenshots wron or blurry?": "Ar <PERSON><PERSON> e<PERSON> kopi<PERSON>/i<PERSON><PERSON><PERSON><PERSON> neteisingas ar nery<PERSON>?", "Are you really sure you want to enable this feature?": "Ar <PERSON><PERSON><PERSON> tik<PERSON> norite įjungti/įgalinti šią funkciją?", "Are you sure you want to create a new package bundle? ": "Ar <PERSON><PERSON><PERSON> tikrai norite sukurti naują paketo/-ų rinkinį?", "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "<PERSON>r <PERSON><PERSON><PERSON> es<PERSON> tikras (-a)?", "Ascendant": "Didėjančia tvarką", "Ask for administrator privileges once for each batch of operations": "<PERSON><PERSON> d<PERSON> administratoriaus teisių tik vieną kartą, kiekvienai operacijų partijai (veiksmo)", "Ask for administrator rights when required": "<PERSON><PERSON> d<PERSON>us te<PERSON>, kai to reikalauja/reikalinga", "Ask once or always for administrator rights, elevate installations by default": "Klausti tik vieną kartą ar visada dėl administratoriaus teisių. Išaukštinti įdiegimus numatytai", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Klausti tik vieną kartą dėl administratoriaus teisių (nerekomenduojama)", "Ask to delete desktop shortcuts created during an install or upgrade.": "<PERSON><PERSON>, ar <PERSON><PERSON><PERSON><PERSON>, kurios buvo sukurtas įdiegimo ar at<PERSON><PERSON><PERSON><PERSON> metu.", "Attention required": "<PERSON><PERSON><PERSON><PERSON>", "Authenticate to the proxy with an user and a password": "Patvirtinkite įgaliotojo tapatybę su naudotojo/vartotojo ir slaptaž<PERSON>ž<PERSON> duomenimis", "Author": "Autorius", "Automatic desktop shortcut remover": "Automatinis/-i<PERSON><PERSON> darbalaukio nuorodų nuėmiklis/nuimtuvas/naikiklis/šalintuvas (-ė).\n\n", "Automatically save a list of all your installed packages to easily restore them.": "Automatiškai išsaugoti įdiegtų paketų sąraš<PERSON>, kad lengvai juos atkurtumė<PERSON>.", "Automatically save a list of your installed packages on your computer.": "Automatiškai išsaugoti įdiegtų paketų sąrašą Jū<PERSON> kompiuteryje.", "Autostart WingetUI in the notifications area": "Automatiškai paleisti „UniGetUI“ pranešimų skiltyje", "Available Updates": "Pasieki<PERSON>", "Available updates: {0}": "Pasie<PERSON><PERSON> atnaujinimai: {0}", "Available updates: {0}, not finished yet...": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>: {0}. dar ne<PERSON><PERSON><PERSON>...", "Backing up packages to GitHub Gist...": null, "Backup": "Sukurti atsarginę kopiją", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Sukurti įdiegtų paketų atsarginę kopiją", "Backup location": null, "Become a contributor": "Tapkite talkininku", "Become a translator": "Tapkite vertėju", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "„Beta“ funkcijos ir kitos parinktys, kurios neturėtų būti sąveikaujamos", "Both": "<PERSON>/-i", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Štai čia yra kitų dalykų, kuriuos tu gali sužinoti apie UniGetUI daugiau:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Perjungiant paketų tvarkytuvą/-ę į išgalinta/išjun<PERSON><PERSON> bū<PERSON>, <PERSON><PERSON><PERSON> matyti ar naujinti jo paketus.", "Cache administrator rights and elevate installers by default": "<PERSON><PERSON><PERSON><PERSON><PERSON> (atminties) podėlyje administratoriaus teises ir i<PERSON><PERSON> įdiegiklius numatytai", "Cache administrator rights, but elevate installers only when required": "<PERSON><PERSON><PERSON><PERSON><PERSON> (atminties) podėly<PERSON> <PERSON><PERSON> te<PERSON>, bet <PERSON><PERSON><PERSON><PERSON><PERSON> įdiegiklius tik tada, kai tas reikalinga", "Cache was reset successfully!": "Talpykla/Podėlis buvo sėkmingai atstatytas!", "Can't {0} {1}": "Negalima/-e {0} {1}", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Cancel all operations": "Atšaukti visas operacijas", "Change backup output directory": "Pakeisti atsarginės kopijos išvesties katalogą", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "<PERSON><PERSON><PERSON><PERSON> kaip „UniGetUI“ patikrina ir įdiegia pasiekiamus atnaujinimus Jūsų paketams", "Change how UniGetUI handles install, update and uninstall operations.": "<PERSON><PERSON><PERSON><PERSON> kaip „UniGetUI“ įdiegia, (at-)naujina ir išdiegia.", "Change how UniGetUI installs packages, and checks and installs available updates": "<PERSON><PERSON><PERSON><PERSON> kaip „UniGetUI“ įdiegia paketus ir tikrina, bei įdiegia jų pasiekiamus (at)-naujinimus", "Change how operations request administrator rights": "Pakeisti kaip operacijos prašo administratoriaus teisių", "Change install location": "Pakeisti įdiegimo vietovę", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Periodiškai tikrinti ar yra paketų atnaujinimų", "Check for updates": "<PERSON><PERSON><PERSON><PERSON>, ar yra (at-)nau<PERSON><PERSON><PERSON>", "Check for updates every:": "Tikrinti ar yra at<PERSON>u<PERSON> kas:", "Check for updates periodically": "Periodiškai tikrinti ar yra at<PERSON>ų", "Check for updates regularly, and ask me what to do when updates are found.": "Reguliariai̇̃ tikrinti ar yra atnaujin<PERSON>ų ir klausti manęs ką su jais da<PERSON>ti, jeigu jų yra.", "Check for updates regularly, and automatically install available ones.": "Reguliariai̇̃ tikrinti ar yra atnau<PERSON> ir automatiškai įdiegti pasiekiamus.", "Check out my {0} and my {1}!": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mano – {0} ir {1}!", "Check out some WingetUI overviews": "Peržiūrėkite keletą „UniGetUI“ apžvalgų", "Checking for other running instances...": "Tikrinama dėl kitų vykdomų egzempliorių...", "Checking for updates...": "Tikrinama ar yra at<PERSON>...", "Checking found instace(s)...": "Tikrina<PERSON> rastą/-us egzempliorių/-ius...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": "Išvalyti talpyklą/podėlį", "Clear finished operations": null, "Clear selection": "Atžymėti pasirinktus", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": "Išvalyti sėkmingas operacijas iš operacijų sąrašo po 5-ių sekundžių atidėjimo", "Clear the local icon cache": "Išvalyti vietinę piktogramų talpyklą/podėlį", "Clearing Scoop cache - WingetUI": "<PERSON>š<PERSON><PERSON>/-s „<PERSON><PERSON>“ talpykla/podėlis – „UniGetUI“", "Clearing Scoop cache...": "<PERSON><PERSON><PERSON><PERSON>/-s „<PERSON><PERSON>“ talpykla/podėlis...", "Click here for more details": "Spauskite/Spustelė<PERSON>, norint gauti daugiau i<PERSON>o", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Spauskite – „Įdiegti“ mygtuką, norint pradėti įdiegimo vyksmą. Jeigu Jūs praleisite įdiegimą, „UniGetUI“ galimai neveiks ka<PERSON>. ", "Close": "Uždaryti/Užverti", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Uždaryti/Užverti „UniGetUI“ į pranešimų skiltį", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Command-line to run:": null, "Compare query against": "Palyginti užklausa su", "Compatible with authentication": null, "Compatible with proxy": "Suderintas su įgaliotiniu", "Component Information": "Komponento/-ų informacija", "Concurrency and execution": null, "Connect the internet using a custom proxy": "Prijunkite internetą naudojant pasirinktinį įgaliotąjį serverį", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Prisidėti prie piktogramų ir ekrano kopijų/iškarp<PERSON> saugyklos", "Contributors": "Talkininkai", "Copy": "Ko<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "Kopijuoti į mainų sritį", "Could not add source": "Nepavyko <PERSON>", "Could not add source {source} to {manager}": "<PERSON><PERSON><PERSON><PERSON><PERSON> – „{source}“ prie – „{manager}“", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "Nepavyko sukurti rinkinio", "Could not load announcements - ": "Nepavyko įkelti paskelbimų –", "Could not load announcements - HTTP status code is $CODE": "Nepavyko įkelti paskelbimų – „HTTP“ būsenos/būkl<PERSON>s kodas: „$CODE“", "Could not remove source": "Nepavyko p<PERSON>linti šaltinio", "Could not remove source {source} from {manager}": "Nepavyko <PERSON> – „{source}“ iš – „{manager}“", "Could not remove {source} from {manager}": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{source}“ i<PERSON> „{manager}“", "Credentials": null, "Current Version": "<PERSON><PERSON><PERSON><PERSON> versija", "Current status: Not logged in": null, "Current user": "<PERSON><PERSON><PERSON><PERSON> naudo<PERSON>/vartoto<PERSON>", "Custom arguments:": "Pasirinktinia<PERSON> argumentai:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Pasirinktiniai komandinės eilutės argumentai:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "<PERSON><PERSON><PERSON> „UniGetUI“ – skirta tik programišiams ir pažangiems naudotojams/vartotojams", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "Atsakomyb<PERSON><PERSON> ribo<PERSON> ir neprisi<PERSON>: MES NESAME ATSAKINGI UŽ ATSISIŲSTUS PAKETUS. PRAŠOME BŪTI ATIDIEMS IR ĮDIEGTI TIK PATIKIMUS PAKETUS.", "Dark": "Tamsus", "Decline": "<PERSON><PERSON><PERSON>/<PERSON>", "Default": "Numatyta/-s", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Numaty<PERSON><PERSON> – tinkamos tipiniam (-ei) naudotojui/vartotojui (-ai)", "Default vcpkg triplet": "Numatyta „vcpkg“ trilypė", "Delete?": null, "Dependencies:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "Descendant": "Mažėjančia tvarką", "Description:": "Aprašymas:", "Desktop shortcut created": "<PERSON><PERSON><PERSON> darb<PERSON>o nuoroda", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "<PERSON>i yra sunku, ir ši programa yra nemo<PERSON>ma. <PERSON><PERSON><PERSON> patiko ši programa, <PERSON><PERSON><PERSON> visada galite <b>n<PERSON><PERSON><PERSON> man kavos</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Tiesiogiai įdiegti, kai dukart paspa<PERSON><PERSON> „{discoveryTab}“ skirtuke (vietoj paketo informacijos rodymo)", "Disable new share API (port 7058)": "Išjungti naują <PERSON> „API“ (prievadas – 7058)", "Disable the 1-minute timeout for package-related operations": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>i vienos minutės lauki<PERSON> laik<PERSON>, vyk<PERSON> (susijusio/-ų) paketo/-ų operacijas", "Disclaimer": "Atsakom<PERSON><PERSON><PERSON><PERSON>", "Discover Packages": "<PERSON><PERSON><PERSON> p<PERSON>", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "Atskirti didžiąsias ir mažąsias raides", "Distinguish between uppercase and lowercase": "Atskirti didžiąsias ir mažąsias raides ", "Do NOT check for updates": "NETIKRINTI ar yra at<PERSON>", "Do an interactive install for the selected packages": "Vykdyti interaktyvų įdiegimą pasirinktam/-iems paketui/-ams", "Do an interactive uninstall for the selected packages": "Vykdyti interaktyvų išdiegimą pasirinktam/-iems paketui/-ams", "Do an interactive update for the selected packages": "Vykdyti interaktyvų atnaujinimą pasirinktam/-iems paketui/-ams", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Nesisiųsti naujų programėlės vertimų iš „GitHub“ automatiškai", "Do not ignore updates for this package anymore": "N<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (at-)naujinimų šiam paketui", "Do not remove successful operations from the list automatically": "Nepašalinti sėkmingų operacijų iš sąrašo automatiškai", "Do not show this dialog again for {0}": "Neberodyti šio diologo lango „{0}“", "Do not update package indexes on launch": "<PERSON><PERSON><PERSON><PERSON> pake<PERSON> indeksus", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, kad „UniGetUI“ rinks ir siųs anonimiškus programos naudojimo statinius duomenis, skirtus vien susipažinti ir patobulinti naudotojo/vartotojo patirtį?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "<PERSON><PERSON> <PERSON><PERSON> „UniGetUI“? <PERSON><PERSON><PERSON>, galite pad<PERSON> pala<PERSON>yti mano da<PERSON>, kad ir toliau kurčiau „UniGetUI“ kaip galingiausią paketų tvarkytuvą/-ę.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "<PERSON><PERSON> <PERSON><PERSON> „UniGetUI“ naudingas? Norėtumėte palaikyti kūrėją? Jeigu taip, tada galite – „{0}“, tai labai padeda!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": "<PERSON><PERSON> <PERSON><PERSON><PERSON> t<PERSON> norite išdiegti nurodomą/-us – {0} paketą/-us?", "Do you really want to uninstall {0} packages?": "<PERSON><PERSON> <PERSON><PERSON><PERSON> t<PERSON> nor<PERSON> i<PERSON> {0} paketą/-us?", "Do you really want to uninstall {0}?": "<PERSON><PERSON> <PERSON><PERSON> t<PERSON> nor<PERSON> i<PERSON> – „{0}“?", "Do you want to restart your computer now?": "Ar norite paleisti iš naujo savo kompiuterį dabar?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Norite „UniGetUI“ išversti į savo kalbą? Pažiūrėkite, kaip galite pris<PERSON> – <a style=\"color:{0}\" href=\"{1}\"a>ČIA!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Nenorite paremti finansiškai? Nesijaudinkite, <PERSON><PERSON><PERSON> visada galite pasidalinti UniGetUI su savo draugais. Papasakokite apie UniGetUI kitiems.", "Donate": "<PERSON><PERSON><PERSON>", "Done!": "Atlikta!", "Download failed": null, "Download installer": "Atsisiųsti įdiegiklį", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "Atsisiuntimas buvo sėkmingas", "Download updated language files from GitHub automatically": "Automatiškai atsisiųsti atnaujintus kalbos failus iš „<PERSON><PERSON><PERSON>“", "Downloading": "Atsisiunčiama", "Downloading backup...": null, "Downloading installer for {package}": "Atisiunčiamas įdiekiklis, skirtas – „{package}“", "Downloading package metadata...": "Atsisiunčiami paketo metadúomenys", "Enable Scoop cleanup on launch": "Įjungti/Įgalinti „Scoop“ apvalymą paleidimo metu", "Enable WingetUI notifications": "Įjungti „UniGetUI“ pranešimus", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Įjungti foninį „API“ („UniGetUI“ valdikliai ir bend<PERSON>mas, prievadas – 7058)", "Enable it to install packages from {pm}.": "Įjunkite/Įgalinkite jį, norint įdiegti paketą/-us iš – „{pm}“.", "Enable the automatic WinGet troubleshooter": "Įjungti/Įgalinti automatinį „WinGet“ trikdžių nagrinėjimą", "Enable the new UniGetUI-Branded UAC Elevator": "Įjungti/Įgalinti naująjį „UniGetUI“ įdaguotą vartotojo/naudotojo paskyr<PERSON> valdym<PERSON> („UAC“) lango i<PERSON>ją", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Įjungti/Įgalinti „{pm}“", "Enter proxy URL here": "Įveskite įgaliotojo „URL“ – saitą čia", "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "<PERSON><PERSON><PERSON>", "Everything is up to date": "Viskas yra at<PERSON> (Liuks!)", "Exact match": "Tikslus sutapimas", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": "Išplėsti versiją", "Experimental settings and developer options": "Eksperimentiniai nustatymai ir kūrėjo parinktys", "Export": "Eksportuoti", "Export log as a file": "Eksportuoti žurnalą ka<PERSON>", "Export packages": "Eksport<PERSON><PERSON> pake<PERSON>", "Export selected packages to a file": "Eksportuoti pasirinktus paketus į failą", "Export settings to a local file": "Eksportuoti nustatymus į failą", "Export to a file": "Eksportuoti į failą", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "Gaunami nau<PERSON>i/pask<PERSON><PERSON>i p<PERSON>, pra<PERSON><PERSON> pala<PERSON>...", "Filters": "Filtrai", "Finish": "<PERSON><PERSON><PERSON>", "Follow system color scheme": "Sekti sistemos spalvų schemą/parinktį", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "<PERSON><PERSON><PERSON><PERSON> „ARM“ sukurtai „winget“ versijai (SKIRTA TIK „ARM64“ SISTEMOMS)", "Formerly known as WingetUI": "<PERSON><PERSON><PERSON> kaip „WingetUI“", "Found": "<PERSON><PERSON>", "Found packages: ": "<PERSON><PERSON><PERSON> pake<PERSON>:", "Found packages: {0}": "<PERSON><PERSON><PERSON> p<PERSON>: {0}", "Found packages: {0}, not finished yet...": "<PERSON><PERSON><PERSON> p<PERSON>: {0}, dar ne<PERSON><PERSON><PERSON>...", "General preferences": "<PERSON><PERSON>", "GitHub profile": "„GitHub“ profilis", "Global": "Visuotinis/-ė", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Gera saugykla <PERSON>, bet naudingų įrankių ir kitų įdomių paketų. <br>Sudaro: <b>Įrankiai, koman<PERSON><PERSON><PERSON> eilut<PERSON> programos, bend<PERSON>ios taikomosios programos (papildomiems „bucket“ reikalingas)</b>", "Great! You are on the latest version.": "Sveikiname! Jūs naudojate naujausią/paskiausią versiją!", "Grid": "<PERSON><PERSON><PERSON>", "Help": "Pagalba", "Help and documentation": "Pagalba ir dokumentacija", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> p<PERSON> „UniGetUI“ elgseną su nurodytomis nuorodomis. Pažymint nuorodos parinktį, „UniGetUI“ ištrins ją, jeigu ji bus sukurta po atnaujinimo. (At-/Ne-)pažymint nuorodos parinktį, i<PERSON><PERSON>kys nuorodą ten kur buvo", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON><PERSON>, mano vardas <PERSON>, ir aš esu UniGetUI <i>kū<PERSON><PERSON><PERSON></i>. UniGetUI sukūriau savo laisvalaikio metu!", "Hide details": "Slėpti išsamumą", "Homepage": "Pag<PERSON><PERSON><PERSON> puslapis", "Hooray! No updates were found.": "Sveikinam! Naujinių nerasta.", "How should installations that require administrator privileges be treated?": "<PERSON><PERSON> elgtis su įdiegimais, kurie re<PERSON>iaus privilegijų?", "How to add packages to a bundle": null, "I understand": "<PERSON><PERSON>", "Icons": "Piktogramos", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Ignoruoti paskesnius atnaujinimus šiam paketui", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "Ignoruoti pasirinktus/-ą paketus/-ą", "Ignore special characters": "Nepaisyti specialių rašmenų", "Ignore updates for the selected packages": "I<PERSON>ru<PERSON>i atnaujinimus pasirinktiems/-am paketams/-ui", "Ignore updates for this package": "Ignoruoti atnaujinimus šiam paketui", "Ignored updates": "Ignoruoti at<PERSON>", "Ignored version": "Ignoruota versija", "Import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> paketus", "Import packages from a file": "I<PERSON>rt<PERSON><PERSON> paketus i<PERSON>o", "Import settings from a local file": "Importuoti nustatymus iš vietinio failo", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „UniGetUI“...", "Install": "Įdiegti", "Install Scoop": "Įdiegti „Scoop“", "Install and more": null, "Install and update preferences": "Įdiegimo ir (at-)nau<PERSON><PERSON> parinktys", "Install as administrator": "Įdiegti kaip <PERSON>ių", "Install available updates automatically": "Įdiegti pasiekiamus atnaujinimus automatiškai", "Install location can't be changed for {0} packages": null, "Install location:": "Įdiegimo vietovė:", "Install options": null, "Install packages from a file": "Įdiegti paketus iš failo", "Install prerelease versions of UniGetUI": "Įdiegti i<PERSON><PERSON><PERSON>s „UniGetUI“ versijas", "Install selected packages": "Įdiegti pasirinktus paketus", "Install selected packages with administrator privileges": "Įdiegti pasirinktus paketus su administratoriaus privilegijom", "Install selection": "Įdiegimo atranka", "Install the latest prerelease version": "Įdiegti paskiausią/naujausią išankstinę laidos versiją", "Install updates automatically": "Įdiegti atnaujinimus automatiškai", "Install {0}": "Įdiegti – „{0}“", "Installation canceled by the user!": "Įdiegimas atša<PERSON>tas vartotojo/naudotojo!", "Installation failed": "Įdiegimas nepa<PERSON>ko", "Installation options": "Įdiegimo parinktys", "Installation scope:": "Įdiegimo sritis:", "Installation succeeded": "Įdiegimas buvo s<PERSON>", "Installed Packages": "Įdiegti paketai", "Installed Version": "Įdiegta versija", "Installed packages": "Įdiegti paketai", "Installer SHA256": "Įdiegiklio „SHA256“", "Installer SHA512": "Įdiegiklio „SHA512“", "Installer Type": "Įdiegiklio tipas", "Installer URL": "Įdiegiklio „URL“ – saitas", "Installer not available": "Įdiegiklis nepasiekiamas", "Instance {0} responded, quitting...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> – „{0}“ atsakė, išeinama...", "Instant search": "Akimirksnio (pa-)ieška", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> įdiegimas", "Interactive operation": null, "Interactive uninstall": "Sąveikaujan<PERSON>", "Interactive update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (at-)naujin<PERSON><PERSON>", "Internet connection settings": "Interneto jungties nustatymai", "Is this package missing the icon?": "Ar <PERSON> paketas neturi piktogramos?", "Is your language missing or incomplete?": "Ar <PERSON><PERSON><PERSON><PERSON> kalba nepasiekiama ar neuž<PERSON>ig<PERSON>?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": "<PERSON>ra rekomenduoja<PERSON>i „UniGetUI“ po to, kai „WinGet“ buvo sutaisytas", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "<PERSON><PERSON><PERSON>, kad „WinGet“ neveikia tinkamai. Ar norite bandyti jį ištaisyti?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "<PERSON><PERSON><PERSON>, kad <PERSON><PERSON> vykdėte UniGetUI administratoriaus privilegijomis. Tai nerekomenduojama, tačiau jokių ribojimų Jums nebus. Peržiūrėkite „{showDetails}“ norėdami <PERSON>, kodėl.", "Language": "Kalba", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON>, apipavidalinimas ir kitos pašalinės parinktys", "Last updated:": "Paskutinį kartą atnaujinta:", "Latest": "Paskiausias/Naujausias", "Latest Version": "Paskiausia/Naujausia versija", "Latest Version:": "Paskiausia/Naujausia versija:", "Latest details...": "Paskiausi/Na<PERSON><PERSON><PERSON>...", "Launching subprocess...": "Paleid<PERSON><PERSON>i pãprocesai...", "Leave empty for default": "Numatytai, palikti tuščią", "License": "Licencija", "Licenses": "Licencijos", "Light": "<PERSON><PERSON><PERSON>", "List": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Live command-line output": "Tiesioginė komandin<PERSON>s e<PERSON>", "Live output": "<PERSON><PERSON><PERSON>", "Loading UI components...": "Įkeliami vartotojo/naudotojo są<PERSON> komponentai...", "Loading WingetUI...": "Įkeliamas „UniGetUI“...", "Loading packages": "Įkeliami paketai", "Loading packages, please wait...": "Įkeliami paketai, pra<PERSON><PERSON> palaukti...", "Loading...": "Įkeliama...", "Local": "V<PERSON><PERSON><PERSON>", "Local PC": "Vietinis AK", "Local backup advanced options": null, "Local machine": "Vietinis įrenginys", "Local package backup": null, "Locating {pm}...": "<PERSON><PERSON>ink<PERSON> – {pm}...", "Log in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Log in failed: ": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:", "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "Žurnalo lygis:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Ieškoma paketų...", "Machine | Global": "Įrenginys | Visuotinis", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "Tvarkyti/Valdyti", "Manage UniGetUI settings": "Tvar<PERSON><PERSON> ir valdyti „UniGetUI“ nustatymus", "Manage WingetUI autostart behaviour from the Settings app": "Valdyti/Nurodyti „UniGetUI“ automatinio paleidimo veiksmą per „Windows – Parametrų“ nustatymų programą", "Manage ignored packages": "<PERSON><PERSON><PERSON><PERSON> ignoruotus paketus", "Manage ignored updates": "<PERSON><PERSON><PERSON><PERSON> ignoruotus at<PERSON>", "Manage shortcuts": "<PERSON><PERSON><PERSON><PERSON>/Valdyti nuorodas", "Manage telemetry settings": null, "Manage {0} sources": "<PERSON><PERSON><PERSON><PERSON> <PERSON> „{0}“ <PERSON><PERSON><PERSON><PERSON>", "Manifest": "<PERSON><PERSON><PERSON><PERSON>", "Manifests": "Manifestai", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Oficialus/-i „Microsoft“ paketų tvarkytuvas/-ė. Pilnas gerai žinomų ir patvirtintų paketų<br>Sudaro: <b>Bendrąsias taikomąsias programas ir „Microsoft Store“ programėles</b>", "Missing dependency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "More": "Daugiau", "More details": "<PERSON>ug<PERSON><PERSON>", "More details about the shared data and how it will be processed": null, "More info": "Daugiau informacijos", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "Pastaba: <PERSON><PERSON> t<PERSON> tikrinimas gali būti išjungtas/iš<PERSON>intas iš „UniGetUI“ nustatymų, „WinGet“ skyriuje", "Name": "<PERSON><PERSON><PERSON><PERSON>", "New": null, "New Version": "<PERSON><PERSON><PERSON> versija", "New bundle": "<PERSON><PERSON><PERSON>", "New version": "<PERSON><PERSON><PERSON><PERSON> versiją", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Ne", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": "Nebuvo rasta naujų nuorodų per skenavimą.", "No packages found": "Paketų nerasta", "No packages found matching the input criteria": "<PERSON><PERSON><PERSON> ne<PERSON>, pagal (atitinkančius) įvesties kriterijus", "No packages have been added yet": "Nėra pridėtų paketų (kol kas)", "No packages selected": "Nėra pažymėtų paketų", "No packages were found": "Paketų nerasta", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "<PERSON><PERSON><PERSON><PERSON>ė informacija nėra reikama ar <PERSON>, o surinkti duomenys yra anonimi<PERSON>i (t.y. jie negali būti atsekti atgal, kad jie būtent Jūsų).", "No results were found matching the input criteria": "Jokių rezultatų nebuvo rasta, atitinkančių įvesties kriterijų", "No sources found": "Jokių šaltinių nerasta", "No sources were found": "Jokie šaltiniai nebuvo rasti", "No updates are available": "Nėra pasiekiamų atnaujinimų", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "„Node JS“ paketų tvarkytuvas/-ė. Pilnas įvairių programinių bibliotekų ir įvairių įrankių, k<PERSON><PERSON> a<PERSON><PERSON> „Javascript pasaulį“ <br>Sudaro: <b>„Node Javascript“ bibliotekos ir įvairūs įrankiai</b>", "Not available": "Nepasiekiama/-s", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Nerasta/-s", "Not right now": "<PERSON><PERSON> <PERSON><PERSON>", "Notes:": "Užrašai/Pastabos:", "Notification preferences": "Pranešimų parinktys", "Notification tray options": "Pranešimų juostelės parink<PERSON>s", "Notification types": null, "NuPkg (zipped manifest)": "„NuPkg“ (archyvuotas manifestas)", "OK": "G<PERSON><PERSON>", "Ok": "G<PERSON><PERSON>", "Open": "<PERSON><PERSON><PERSON><PERSON>", "Open GitHub": "Atidaryti „GitHub“", "Open UniGetUI": "Atidaryti „UniGetUI“", "Open UniGetUI security settings": null, "Open WingetUI": "Atidaryti „UniGetUI“", "Open backup location": "Atidaryti at<PERSON>ginės kopijos vietovę", "Open existing bundle": "Atidaryti jau egzi<PERSON><PERSON><PERSON>ti rinkinį", "Open install location": "Atidaryti įdiegimo vietovę", "Open the welcome wizard": "Atidaryti „Sveiki!“ sąranka/vedly", "Operation canceled by user": "Operacija atšaukta naudotojo/vartotojo", "Operation cancelled": "Operacija atšaukta", "Operation history": "Operacijos istorija", "Operation in progress": "Operacija vykdoma", "Operation on queue (position {0})...": "Operacija eilėje (vieta eil<PERSON>je – {0})...", "Operation profile:": null, "Options saved": "Parinkt<PERSON>", "Order by:": "<PERSON><PERSON><PERSON><PERSON>/Rū<PERSON><PERSON>i pagal:", "Other": "Kita/-s/-i", "Other settings": "Kit<PERSON> nustatym<PERSON>", "Package": null, "Package Bundles": "Paketo/-<PERSON>i", "Package ID": "Paketo ID", "Package Manager": "Paket<PERSON> tvarkytuvas/-ė", "Package Manager logs": "Paketų tvarkytuvo/-<PERSON><PERSON>", "Package Managers": "Paketų tvarkytuvai/-ės", "Package Name": "<PERSON><PERSON> p<PERSON>", "Package backup": "Atsarginės paketų kopijos", "Package backup settings": null, "Package bundle": "Paketo/-<PERSON> <PERSON>inys", "Package details": "<PERSON><PERSON>", "Package lists": null, "Package management made easy": "Paketų valdymas ir tvarkymas padarytas lengvu!", "Package manager": "Paket<PERSON> tvarkytuvas/-ė", "Package manager preferences": "Paketų tvarkytuvo/-ė<PERSON> n<PERSON>", "Package managers": "Paketų tvarkytuvai/-ės", "Package not found": "Paketas ne<PERSON>", "Package operation preferences": "Paketų operacijų parinktys ir nuostatos", "Package update preferences": "Paketų (at-)naujinimų parinktys ir nuostatos", "Package {name} from {manager}": "<PERSON><PERSON><PERSON> – „{name}“ iš – „{manager}“", "Package's default": null, "Packages": "Paketa<PERSON>", "Packages found: {0}": "<PERSON><PERSON><PERSON> p<PERSON>: {0}", "Partially": "<PERSON><PERSON><PERSON>", "Password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Paste a valid URL to the database": "Įklijuokite tinkama „URL“ – saitą prie duomenų bazės", "Pause updates for": null, "Perform a backup now": "Atlikti atsarginės kopijos sukū<PERSON>ą dabar", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "Atlieka<PERSON> at<PERSON>gin<PERSON> kop<PERSON>, pra<PERSON><PERSON> pala<PERSON>...", "Periodically perform a backup of the installed packages": "Periodiškai atlikti atsarginės kopijos sukūrimą įdiegtų paketų", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "Prašome patikrinti įdiegimo parinktis šiam paketui ir bandykite dar kartą", "Please click on \"Continue\" to continue": "Praš<PERSON> paspausti – „Tęsti“, norint tęsti (pasiektas pasiekimas: Pirmieji žingsniai!)", "Please enter at least 3 characters": "Prašome įvesti nors tris (3) rašmenis", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Atkreipkite dėmesį, kad kai kurie paketai gali būti neįdiegiami, nes paketų tvarkytuvai/-ės, kurios įjungtos/įgalintos šiame kompiuter<PERSON>je, tai dra<PERSON>.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Atkreipkite dėmesį, kad paketai iš kai kurių šaltinių, gali būti neišeksportuojami. Jie buvo pažymėti pilkai ir nebus išeksportuoti.", "Please run UniGetUI as a regular user and try again.": "Prašome vykdyti „UniGetUI“ kaip paprasta (-s) vartotoją/naudotoją (-as) ir bandykite dar kartą", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Praš<PERSON> perž<PERSON>ūr<PERSON><PERSON> komandinės eilutės išvestį arba telkitės – „Operacijos istorijos“, norint gauti daugiau informacijos apie tam tikrą bėdą.", "Please select how you want to configure WingetUI": "Prašome pasirinkti kaip <PERSON> norite konfigūruoti „UniGetUI“", "Please try again later": "Prašome pabandyti dar kartą, vėlesniu laiku", "Please type at least two characters": "Prašome įvesti nors dvi (2) rašmenis", "Please wait": "<PERSON><PERSON>š<PERSON> p<PERSON>", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Prašome palaukti kol „{0}“ yra įdiegiama/-s. <PERSON><PERSON> la<PERSON> gal<PERSON> atsiras. Prašome palaukti, kol ji/-s už<PERSON><PERSON><PERSON>.", "Please wait...": "Prašome palaukti...", "Portable": "Perkeliama/-s", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "„PowerShell“ paketų tvarkytuvas/-ė. Pilnas programinių bibliotekų ir skriptų<br>Sudaro: <b><PERSON><PERSON><PERSON>, skrip<PERSON> ir „cmdlets“</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Preparing packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pake<PERSON>, pra<PERSON><PERSON> pala<PERSON>ti...", "Proceed at your own risk.": "Tęskite savo nuožiūra.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": "Įgaliotojo/-inio „URL“ – saitas", "Proxy compatibility table": "Įgaliotinių/-<PERSON><PERSON><PERSON> suderini<PERSON> lentel<PERSON>", "Proxy settings": "Įgaliotinių/-<PERSON><PERSON><PERSON> nustatymai", "Proxy settings, etc.": "Įgaliotinių/-<PERSON><PERSON><PERSON> nustatymai ir pan.", "Publication date:": "Publikacijos data:", "Publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "„Python“ programinių bibliotekų tvarkytuvas/-ė. <PERSON><PERSON>as „Python“ bibliotekų ir kitų „Python“ susijusių įrankių<br>Sudaro: <b>„Python“ bibliotekas ir kitus „Python“ susijusius įrankius</b>", "Quit": "<PERSON><PERSON><PERSON><PERSON>", "Quit WingetUI": "Išeiti iš „UniGetUI“", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "<PERSON><PERSON><PERSON><PERSON>", "Reinstall package": "Perdiegti paketą", "Related settings": "Susiję nustatymai", "Release notes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Release notes URL": "Išleidimo <PERSON> „URL“ – saitas", "Release notes URL:": "Išleidimo <PERSON> „URL“ – saitas:", "Release notes:": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "Reload": "<PERSON><PERSON><PERSON>", "Reload log": "Perkrovimų žurnalas", "Removal failed": "<PERSON><PERSON><PERSON><PERSON>", "Removal succeeded": "<PERSON><PERSON><PERSON><PERSON>", "Remove from list": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "Remove permanent data": "<PERSON><PERSON><PERSON><PERSON> nuolatinius duomenis", "Remove selection from bundle": "Pašalinti pasirinkimą i<PERSON>", "Remove successful installs/uninstalls/updates from the installation list": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> įdiegimus/iš<PERSON><PERSON><PERSON>/atnaujin<PERSON>/naujinius iš s<PERSON>o", "Removing source {source}": null, "Removing source {source} from {manager}": "<PERSON><PERSON><PERSON><PERSON> – „{source}“ iš – „{manager}“", "Repair UniGetUI": null, "Repair WinGet": "Sutai<PERSON><PERSON> „WinGet“", "Report an issue or submit a feature request": "Pranešti apie problemą arba pridėti funkcijų pasiūlymą", "Repository": "Saug<PERSON><PERSON>", "Reset": "Atstaty<PERSON>", "Reset Scoop's global app cache": "Atstatyti visuotinį „Scoop“ programos/-<PERSON><PERSON><PERSON><PERSON> tal<PERSON>/podėlį", "Reset UniGetUI": "Atstatyti „UniGetUI“", "Reset WinGet": "Atstatyti „WinGet“", "Reset Winget sources (might help if no packages are listed)": "Atstatyti „Winget“ š<PERSON><PERSON> (gali pad<PERSON>, jeigu nėra paketų sąraše)", "Reset WingetUI": "Atstatyti „UniGetUI“", "Reset WingetUI and its preferences": "Atstatyti UniGetUI ir jo parinktis", "Reset WingetUI icon and screenshot cache": "Atstatyti UniGetUI piktogramą ir ekrano kopijų/iškarpų talpyklą", "Reset list": "Atstatyti sąrašą", "Resetting Winget sources - WingetUI": "Atstatomi WinGet šaltiniai – UniGetUI", "Restart": "Paleisti iš naujo", "Restart UniGetUI": "<PERSON><PERSON> pale<PERSON> – „UniGetUI“", "Restart WingetUI": "<PERSON><PERSON> pale<PERSON> – „UniGetUI“", "Restart WingetUI to fully apply changes": "<PERSON><PERSON> paleisti UniGetUI, kad pilnai prisitaikytų pakeitimams", "Restart later": "Paleisti iš naujo vėliau", "Restart now": "Paleisti iš naujo dabar", "Restart required": "Paleidimas iš naujo <PERSON>", "Restart your PC to finish installation": "Paleiskite iš naujo savo <PERSON>, kad pabaig<PERSON> įdiegimą", "Restart your computer to finish the installation": "Paleiskite iš naujo savo kompiuterį, kad p<PERSON> įdiegimą", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Bandyti dar kartą", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": "<PERSON><PERSON> vėl, pra<PERSON><PERSON> pala<PERSON>...", "Return to top": "Sugrįžti atgal į viršų", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Run as admin": "Vyk<PERSON>ti <PERSON>", "Run cleanup and clear cache": "Vykdyti apvalymą ir išvalyti podėlį/talpyklą", "Run last": "Vykdyti paskutinį", "Run next": "Vykdyti <PERSON>", "Run now": "<PERSON><PERSON><PERSON><PERSON>", "Running the installer...": "Vykdomas įdiegiklis...", "Running the uninstaller...": "Vykdomas išdiegiklis...", "Running the updater...": "Vykdomas naujinys...", "Save": null, "Save File": "Išsaugoti failą", "Save and close": "Išsaugoti ir uždaryti", "Save as": null, "Save bundle as": "Išsaugoti rinkinį kaip", "Save now": "Išsaug<PERSON><PERSON> dabar", "Saving packages, please wait...": "<PERSON><PERSON><PERSON><PERSON><PERSON> pake<PERSON>, pra<PERSON><PERSON> pala<PERSON>...", "Scoop Installer - WingetUI": "„Scoop“ įdiegiklis – „UniGetUI“", "Scoop Uninstaller - WingetUI": "„Scoop“ išdiegiklis – „UniGetUI“", "Scoop package": "„Scoop“ paketas", "Search": "Ieškoti", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Ieškoti kompiuterinių taikomųjų programų, įspėti mane kai atnaujinimai yra pasiekiami ir nedaryti „mėmiškų dalykų“. <PERSON><PERSON>, kad UniGetUI pernelyg daug komplikuotų, o tiesiog kad būtų paprasta <b>taikomųjų programų parduotuvė</b>", "Search for packages": "Ieškoti paketų", "Search for packages to start": "Ieškoti paketų, norint pradė<PERSON>", "Search mode": "(Pa-)Ieškos ve<PERSON>a", "Search on available updates": "Ieškoti pasiekiamų atnaujinimų", "Search on your software": "Ieškoti Jūsų taikomųjų programų", "Searching for installed packages...": "Ieškoma įdiegtų paketų...", "Searching for packages...": "Ieškoma paketų...", "Searching for updates...": "<PERSON><PERSON><PERSON><PERSON>, ar yra na<PERSON>...", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select \"{item}\" to add your custom bucket": "<PERSON><PERSON><PERSON><PERSON><PERSON> – „{item}“, kad p<PERSON>tum<PERSON> į savo pasirinktinį „bucket“", "Select a folder": "Pažymėti aplanką", "Select all": "Paž<PERSON>ė<PERSON> visus", "Select all packages": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus paketus", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "<PERSON><PERSON><PERSON><PERSON><PERSON> – „<b>“, tik tada, jeigu <PERSON><PERSON><PERSON> k<PERSON></b>.", "Select package file": "Pažymėti p<PERSON>ą", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "Pažymėkite šaltinį, kurį norite pridėti:", "Select upgradable packages by default": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON><PERSON> atnau<PERSON>us paketus numatytai", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "<PERSON><PERSON><PERSON><PERSON><PERSON> kurias/-uos <b>pake<PERSON><PERSON> tvarkytuvės/-us</b> naudoti/vartoti ({0}), konfig<PERSON><PERSON><PERSON><PERSON> kaip paketai yra įdiegti, valdyti kaip <PERSON> te<PERSON>s yra išduodamos ir pan.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "(Nu/Iš)-<PERSON><PERSON><PERSON><PERSON> suderini<PERSON>. Laukiama egzemplioriaus klausytojo atsako... ({0}%)", "Set a custom backup file name": "Nustatykite pasirinktinį atsarginė<PERSON> kop<PERSON>, failo pavadin<PERSON>", "Set custom backup file name": "Nustatykite pasirinktinį atsarginė<PERSON> kop<PERSON>, failo pavadin<PERSON>", "Settings": "Nustatymai", "Share": "<PERSON><PERSON><PERSON>", "Share WingetUI": "Bendrinti UniGetUI", "Share anonymous usage data": "<PERSON><PERSON><PERSON>i anoniminius naudojimo duomenis", "Share this package": "Bendrinti šį paketą", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "Rod<PERSON>i „UniGetUI“ sistemos juostelėje", "Show UniGetUI's version and build number on the titlebar.": "<PERSON><PERSON><PERSON> „UniGetUI“ versiją ant lango antra<PERSON> juostos", "Show WingetUI": "Rodyti „UniGetUI“", "Show a notification when an installation fails": "<PERSON><PERSON><PERSON>, kai įdiegimas yra ne<PERSON>", "Show a notification when an installation finishes successfully": "<PERSON><PERSON><PERSON>, kai įdiegimas baigiasi s<PERSON>ai", "Show a notification when an operation fails": "<PERSON><PERSON><PERSON>, kai operacija nepavyksta ar patiria k<PERSON>", "Show a notification when an operation finishes successfully": "<PERSON><PERSON><PERSON>, kai operacija pavyksta ar baigia be problemų", "Show a notification when there are available updates": "<PERSON><PERSON><PERSON>, kai yra pasiekiami atnaujin<PERSON>i", "Show a silent notification when an operation is running": "Rodyti tylų pranešimą, kai operacija yra vykdoma", "Show details": "Rod<PERSON><PERSON>", "Show in explorer": "Rodyti failų naršyklėje", "Show info about the package on the Updates tab": "Rodyti informaciją apie tam tikra paketą; „Atnaujinimų“ skirtuke", "Show missing translation strings": "Rodyti trūkstamų vertimų tekstus", "Show notifications on different events": "<PERSON><PERSON><PERSON>, skirtinguose įvykiuose", "Show package details": "<PERSON><PERSON><PERSON> p<PERSON>", "Show package icons on package lists": "Rodyti paketų piktogramas, paketų sąraše", "Show similar packages": "<PERSON><PERSON><PERSON> paketus", "Show the live output": "Rodyti gyvą išvestį", "Size": "<PERSON><PERSON><PERSON>", "Skip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Skip hash check": "Praleisti ma<PERSON>š<PERSON> pat<PERSON>", "Skip hash checks": "Praleisti ma<PERSON>", "Skip integrity checks": "Praleisti vientisumo patikrinimus", "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "Praleisti ma<PERSON>, diegiant p<PERSON><PERSON><PERSON><PERSON><PERSON> paketus", "Skip the hash check when updating the selected packages": "Praleisti ma<PERSON>, (at-)naujinant pasirinktus/pa<PERSON><PERSON><PERSON><PERSON> paketus", "Skip this version": "Praleisti šią versiją", "Software Updates": "<PERSON><PERSON><PERSON><PERSON>", "Something went wrong": "<PERSON><PERSON><PERSON>, kažkur įvyko blogai", "Something went wrong while launching the updater.": "<PERSON><PERSON><PERSON> įvyko negerai, bandant paleisti naujinį.", "Source": "Šaltinį", "Source URL:": "<PERSON><PERSON><PERSON><PERSON> „URL“ – saitas:", "Source added successfully": "Šal<PERSON>is <PERSON>kming<PERSON> p<PERSON>", "Source addition failed": "<PERSON><PERSON><PERSON><PERSON>", "Source name:": "<PERSON><PERSON><PERSON><PERSON> pava<PERSON>:", "Source removal failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Source removed successfully": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Source:": "Šaltinis:", "Sources": "Šaltiniai", "Start": "<PERSON><PERSON><PERSON><PERSON>", "Starting daemons...": "Paleidžiamos paslaugų teikimo sistemos...", "Starting operation...": "Pradedama operacija...", "Startup options": "<PERSON><PERSON><PERSON><PERSON>", "Status": "Būsena/Būklė", "Stuck here? Skip initialization": "Užstrigę? Praleiskite inicijavimą", "Suport the developer": "Palaikykite kū<PERSON>ėją", "Support me": "Palai<PERSON><PERSON><PERSON> mane", "Support the developer": "Palaikykite kū<PERSON>ėją", "Systems are now ready to go!": "Sistemos yra pasiruošusios!", "Telemetry": "Telemètrija", "Text": "Tekstas", "Text file": "<PERSON><PERSON><PERSON>", "Thank you ❤": "A<PERSON><PERSON><PERSON> ❤", "Thank you 😉": "A<PERSON><PERSON><PERSON> 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "„Rust“ paketų tvarkytuvė/-as.<br>Sudaro: <b> „Rust“ bibliotekos ir programos, kurios sukurtos „Rust“ programavimo kalba</b>", "The backup will NOT include any binary file nor any program's saved data.": "Atsarginė kopija NEĮTRAUKIA jokių dvejetainių failų, nei jokių programų/-<PERSON><PERSON>ų išsaugotų duomenų.", "The backup will be performed after login.": "Atsarginės kopija bus atlikta po prisijungimo.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Atsarginė kopija įtraukia pilnąjį įdiegtų paketų sąrašą su jų įdiegimo parinktys. Ignoruojami atnaujinimai ir praleistos versijos irgi yra iš<PERSON>ugomo<PERSON>.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "<PERSON><PERSON><PERSON><PERSON>, kurį bandot įkrauti (atrodomai) yra negalimas. Prašome patikrinti failą ir bandykite dar kartą.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Įdiegiklio patikros suma nesutampa su numatytąją reikšme ir įdiegiklio autentiškumas negali būti patvirtin<PERSON>. <PERSON><PERSON><PERSON> pasiti<PERSON> le<PERSON>, tada „{0}“ paketą galite įdiegti praleisdami patikros sumos patikrinimą.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "<PERSON><PERSON><PERSON><PERSON><PERSON>/-is paket<PERSON> tvarkytuvė/-as, skirta/-s „Windows“. Jūs rasite viską ten. <br>Sudaro: <b><PERSON><PERSON> taiko<PERSON> programos</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "Dabar<PERSON>is rinkinys neturi paketų. Pridėkite keletą paketų, nor<PERSON> prad<PERSON>", "The executable file for {0} was not found": "Vykdom<PERSON> failas, skirtas – „{0}“, nebuvo rastas", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Toliau esantys paketai bus eksportuoti į „JSON“ failą. Jokie <PERSON>udo<PERSON>/vartotojo duomenys ar dvejetainės nebus išsaugomi.", "The following packages are going to be installed on your system.": "Toliau esantys paktai bus įdiegti Jūsų sistemoje.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "Toliau esantys nustatymai bus pritaikyti kiekvieną kartą, kai šis paketas yra įdiegtas, atnaujintas ar p<PERSON>šalint<PERSON>.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Toliau esantys nustatymai bus pritaikyti kiekvieną kartą, kai šis paketas yra įdiegtas, atnaujintas ar pašalintas. Jie bus automatiškai išsaugoti.", "The icons and screenshots are maintained by users like you!": "<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON>s ir ekrano nuotraukos yra pateikiamos vartotojų/naudotojų kaip ir tu!", "The installer authenticity could not be verified.": "Nebuvo galima patvirtinti įdiegiklio autentiškumą.", "The installer has an invalid checksum": "Įdiegiklis turi netinkamą patikros sumą", "The installer hash does not match the expected value.": "Įdiegiklio maiša nesutampa su numatytąją reikšme.", "The local icon cache currently takes {0} MB": "Vietinė piktogramų talpykla/pod<PERSON><PERSON> – {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Pagrindinis šio projekto tikslas yra – sukurti patrauklią naudotojo/vartoto<PERSON> s<PERSON>, skirta tvarkyti <PERSON> „CLI“ paketų tvarkytuvus/-es, kurie skirti „Windows“; tokie kaip „Winget“ ir „Scoop“.", "The package \"{0}\" was not found on the package manager \"{1}\"": "<PERSON><PERSON><PERSON> – „{0}“ nebuvo rastas paketų tvarkytuvėje – „{1}“", "The package bundle could not be created due to an error.": "Nepavyko sukurti paketo/-<PERSON> <PERSON><PERSON>o d<PERSON>.", "The package bundle is not valid": "Paketo/-<PERSON> <PERSON>inys yra negalimas", "The package manager \"{0}\" is disabled": "<PERSON><PERSON><PERSON> tvarkytuvė/-as – „{0}“ yra išjungta/išgalinta (-s)", "The package manager \"{0}\" was not found": "<PERSON><PERSON><PERSON> tvarkytuvė/-as – „{0}“ yra nerasta/-s", "The package {0} from {1} was not found.": "<PERSON><PERSON><PERSON> – „{0}“ iš – „{1}“ nebuvo rastas.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Paketai pateikti čia nebus įtraukiami, kai tikrinama ar yra atnaujinim<PERSON>. Du kart spaustelėjus ar paspaudus ant mygtuko jo de<PERSON> sustabdys jų atnaujinimus.", "The selected packages have been blacklisted": "Pažymėti paketai buvo įtraukti į draudžiamąjį sąrašą", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Atsarginės kopijos dydis yra <PERSON>, kad bus mažesnis nei – 1MB.", "The source {source} was added to {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> – „{source}“ buvo sėkmingai pridėtas prie – „{manager}", "The source {source} was removed from {manager} successfully": "<PERSON><PERSON><PERSON><PERSON> – „{source}“ buvo sėkming<PERSON> p<PERSON>ša<PERSON> i<PERSON> – „{manager}", "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> buvo nutrauktas.", "The update process will start after closing UniGetUI": "Atnaujinimo <PERSON> p<PERSON> po „UniGetUI“ uždarymo", "The update will be installed upon closing WingetUI": "Atnaujinimas bus įdiegtas, uždarius UniGetUI", "The update will not continue.": "Atnaujinimas nebus tę<PERSON>mas", "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": "Nėra naujų „UniGetUI“ versijų, kurias būtu gal<PERSON> įdiegti", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Yra einančios/vykdomos operacijos. Išeinant iš UniGetUI gali sukelti jų gedimą. Ar <PERSON>ūs norite tęsti?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Yra gerų vaizdo įrašų „Youtube“ platformoje, kurie apibūdina UniGetUI ir parodo jo galimybes. Jūs galėsite sužinoti naudingų triukų ir patarimų!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Yra dvi pagrindinės priežastys dėl ko nederėtų vykdyti „UniGetUI administratoriaus privilegijomis: Pirmoji – „Scoop“ paketų tvarkytuvas/-ė gali sukelti problemų su savo komandomis, kai turi išplėstas privilegijas. Antroji – paketai, atsiųsti per UniGetUI, perteiks šias privilegijas (saugumo rizika). Atminkite, kad visada galite vykdyti tam tikro paketo įdiegimą su išplėstomis privilegijomis -> Įdiegti/Atnaujinti/Įdiegti kaip administratorius.", "There is an error with the configuration of the package manager \"{0}\"": "Yra klaida su konfigūracija, siejan<PERSON><PERSON>ą paketų tvarkytuve/-u – „{0}“", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Yra vykdom<PERSON> įdiegimas. Je<PERSON> Jūs uždarysite UniGetUI, tada įdiegimas gali patirti nesėkmių ir turės netikėtų rezultatų. Ar Jūs vis dar norite uždaryti UniGetUI?", "They are the programs in charge of installing, updating and removing packages.": "<PERSON> yra <PERSON>os, k<PERSON><PERSON> valdo įdiegimo, at<PERSON><PERSON><PERSON><PERSON> ir <PERSON> (nurodytų) paketų.", "Third-party licenses": "Trečios šalies licencija", "This could represent a <b>security risk</b>.": "<PERSON><PERSON> gali su<PERSON> <b>saugomo riziką</b>.", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "<PERSON><PERSON> greičiaus<PERSON>i yra, nes paketas kurį gavote buvo pašalintas arba leidėjas paketų tvarkytuve/-ėje nėra jo pridėję viešai. Gautas ID yra – {0}", "This is the <b>default choice</b>.": "<PERSON><PERSON> yra – <b>numa<PERSON><PERSON><PERSON></b>.", "This may help if WinGet packages are not shown": "<PERSON><PERSON>, j<PERSON> <PERSON> paketai nesirodo", "This may help if no packages are listed": "<PERSON><PERSON>, jei n<PERSON>ra paketų sąraše", "This may take a minute or two": "Šis gali užtrukti minutę ar dvi", "This operation is running interactively.": null, "This operation is running with administrator privileges.": "Ši operacija veikia su administratoriaus privilegijom.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Šis paketas gali būti <PERSON>", "This package can be updated to version {0}": "<PERSON>is paketas gali būti atnau<PERSON> į versiją – {0}", "This package can be upgraded to version {0}": "Šis paketas gali būti aukštutiniškai atnaujintas į versiją – {0}", "This package cannot be installed from an elevated context.": "Šis paketas negali būti įdiegtas su paaukš<PERSON>tom teisėm („UniGetUI“ kontekstas).", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Šis paketas neturi ekrano kopijų, iškarpų ar neturi piktogramos? Prisidėkite prie UniGetUI pridėdami trūkstamus piktogramas, ekrano kopijas ir iškarpas į mūsų atvirą, viešą duomenų bazę.", "This package is already installed": "<PERSON><PERSON> paketas jau įdiegtas", "This package is being processed": "<PERSON><PERSON> paketas yra a<PERSON>", "This package is not available": "Šis paketas nėra pasiek<PERSON>", "This package is on the queue": "Šis paketas yra eilėje", "This process is running with administrator privileges": "<PERSON><PERSON> v<PERSON> ve<PERSON>a su administratoriaus privilegijom", "This project has no connection with the official {0} project — it's completely unofficial.": "Šis projektas neturi jokių ryšių su oficialiu „{0}“ projektu – ji/-s yra visiškai neoficiali/-us.", "This setting is disabled": "Šis nustatymas yra iš<PERSON>/iš<PERSON><PERSON>as", "This wizard will help you configure and customize WingetUI!": "<PERSON><PERSON> vedlys pad<PERSON>s Ju<PERSON> susikonfigūruoti ir pritaikyti UniGetUI!", "Toggle search filters pane": "<PERSON><PERSON><PERSON><PERSON> (pa-)ieškos filtrų skydą", "Translators": "Vertėjai", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Rašykite čia pavadinimą ir „URL“ – sait<PERSON>, kurį <PERSON><PERSON><PERSON> nor<PERSON>, atskirti tarpais.", "Unable to find package": "Nepavyko rasti paketo", "Unable to load informarion": "Nepavyko įkelti informacijos", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "„UniGetUI“ aptiko naują darbalaukio nuorodą, kuri gali būti ištrinta automatiškai.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "„UniGetUI“ aptiko šias darbalaukio nuorodas, kurios gali būti ištrintos automatiškai po (ateities) atnaujinimų.", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": "„UniGetUI“ yra naujinamas...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "„UniGetUI“ nėra susijęs su jokiais/-omis palaiko<PERSON>/-omis paketų tvarkytuvais/-ėmis. „UniGetUI“ yra nepriklausomas projektas.", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "„UniGetUI“ reikalauja „{0}“, kad ve<PERSON>, bet ji/-s nebuvo rasta/-s <PERSON><PERSON><PERSON><PERSON> siste<PERSON>.", "UniGetUI startup page:": "„UniGetUI“ paleisties puslapis:", "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": "„UniGetUI“ versija – {0} yra atsisiunčiama/-s.", "UniGetUI {0} is ready to be installed.": "„UniGetUI“ – {0} yra p<PERSON>/-ę<PERSON> būti įdiegta/-s.", "Uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "<PERSON><PERSON><PERSON><PERSON><PERSON> „Scoop“ (ir jo pake<PERSON>)", "Uninstall and more": null, "Uninstall and remove data": "Išdiegti ir pašalinti duomenis", "Uninstall as administrator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall canceled by the user!": "Išdiegimas buvo atšauktas naudotojo/vartotojo", "Uninstall failed": "Išdiegimas buvo ne<PERSON>ėkmingas", "Uninstall options": null, "Uninstall package": "Išdiegti paketą", "Uninstall package, then reinstall it": "<PERSON><PERSON><PERSON><PERSON><PERSON> paket<PERSON>, tada jį perdiegti", "Uninstall package, then update it": "<PERSON><PERSON><PERSON><PERSON><PERSON> pake<PERSON>, tada jį atnau<PERSON>ti", "Uninstall previous versions when updated": null, "Uninstall selected packages": "<PERSON>šdiegti pažym<PERSON>tus/-ą paketus/-ą", "Uninstall selection": null, "Uninstall succeeded": "Išdiegimas buvo <PERSON>", "Uninstall the selected packages with administrator privileges": "<PERSON>šdiegti pažym<PERSON>tus/-ą paketus/-ą su administratoriaus privilegijom", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Išdiegiami paketai su kilme parašyt<PERSON> kaip – „{0}“ nėra pateikiami jokiuose paketų tvarkytuvuose/-ėse, todėl nėra jokios informacijos apie juos.", "Unknown": "Nežinoma/-s", "Unknown size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unset or unknown": null, "Up to date": "Naudojate nau<PERSON>i<PERSON>/paskiausią versijos leidinį", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Automatiškai (at-)naujinti „UniGetUI“", "Update all": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus", "Update and more": null, "Update as administrator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update check frequency, automatically install updates, etc.": null, "Update date": "Atnaujinimo data", "Update failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update found!": "Ra<PERSON><PERSON> at<PERSON>!", "Update now": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update options": null, "Update package indexes on launch": "<PERSON><PERSON><PERSON><PERSON>, atnaujinti paketų indeksus", "Update packages automatically": "Automatiškai atnaujinti paketus", "Update selected packages": "At<PERSON><PERSON>jin<PERSON> pažym<PERSON>tus/-ą paketus/-ą", "Update selected packages with administrator privileges": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa<PERSON>ym<PERSON>tus/-ą paketus/-ą su administratoriaus privilegijom", "Update selection": "(At-)Na<PERSON>jinti pasirinktus/pa<PERSON><PERSON><PERSON><PERSON>", "Update succeeded": "Sėkmingai atnaujinta", "Update to version {0}": "Atnaujinti į versiją – {0}", "Update to {0} available": "Atnaujinimas į – {0} pasiek<PERSON>as", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Automatiškai atnaujinti „vcpkg's“ „Git-port“ failus (reikalauja, kad „Git“ būtu įdiegtas)", "Updates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Updates available!": "Atnaujinimai pasiekiami!", "Updates for this package are ignored": "Atnaujinimai šiam paketui yra ignoruojami", "Updates found!": "<PERSON><PERSON><PERSON>jin<PERSON>i rasti!", "Updates preferences": "Atnaujinimų parinktys", "Updating WingetUI": "Atnaujinama/-s „UniGetUI“", "Url": "„URL“ – Saitas", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "<PERSON><PERSON><PERSON> pase<PERSON>io standarto įtrauktą „WinGet“ vietoj „PowerShell CMDLets“", "Use a custom icon and screenshot database URL": "Naudoti pasirinktinę piktogramą ir ekranų kopijų/iškarpų duomenų bazės „URL“ – saitą", "Use bundled WinGet instead of PowerShell CMDlets": "<PERSON><PERSON><PERSON> įtraukta „WinGet“ vietoj sistemos „PowerShell CMDlets“", "Use bundled WinGet instead of system WinGet": "Naudoti įtraukta „WinGet“ vietoj sistemos „WinGet“", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Naudo<PERSON> įdiegtą „GSudo“ vietoj susieto su (kartu)", "Use system Chocolatey": "<PERSON><PERSON><PERSON> siste<PERSON> „Chocolatey“", "Use system Chocolatey (Needs a restart)": "<PERSON><PERSON><PERSON> siste<PERSON> „Chocolatey“ (reikalauja paleidimo iš naujo)", "Use system Winget (Needs a restart)": "<PERSON><PERSON><PERSON> si<PERSON><PERSON> „Winget“ (reikalauja paleidimo iš naujo)", "Use system Winget (System language must be set to english)": "<PERSON><PERSON>ti siste<PERSON> „Winget“ (sistemos kalba turi būti – anglų)", "Use the WinGet COM API to fetch packages": "<PERSON><PERSON>ti „WinGet COM API“, kad gautum<PERSON> paketus", "Use the WinGet PowerShell Module instead of the WinGet COM API": "<PERSON><PERSON><PERSON> „WinGet PowerShell“ modulį vietoj „WinGet COM API“", "Useful links": "<PERSON>udingos nuorodos", "User": "Naudotojas/Vartotojas", "User interface preferences": "Naudotojo/Vartotojo sąsajos nuostatos", "User | Local": "Naudotojas/Vartotojos | Vietinis", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "„UniGetUI“ naudojimas, nurodo neišreikštinį „GNU Lesser General Public License v2.1“ licencijos priėmimą", "Using WingetUI implies the acceptation of the MIT License": "„UniGetUI“ naudojimas, nurodo neišreikštinį „MIT“ licencijos priėmimą", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "„Vcpkg root“ nebuvo aptiktas. Prašome apibrėžti – „%VCPKG_ROOT%“ aplinkos kintamąjį arba apibrėžkite jį patį per „UniGetUI“ nustatymus/-uose.", "Vcpkg was not found on your system.": "„Vcpkg“ nebuvo rastas Jūsų sistemoje.", "Verbose": "Plačiai/Išsamiai/Daugiažod<PERSON>š<PERSON>", "Version": "Versiją", "Version to install:": "Versija, kurią įdiegti:", "Version:": "Versija:", "View GitHub Profile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „GitHub“ profilį", "View WingetUI on GitHub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> „UniGetUI“, „GitHub“ svetainėje", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Perž<PERSON><PERSON>rėti pirminį UniGetUI kodą. Ten Jūs galite pranešti apie spragas ar pasiū<PERSON>ti funkcijas, net galite tiesiogiai prisidėti prie UniGetUI projekto.", "View mode:": "Ž<PERSON>ūrėsena:", "View on UniGetUI": null, "View page on browser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> puslapį naršyklėje", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "Laukiama, kol kiti įdiegimai bus baigti...", "Waiting for {0} to complete...": null, "Warning": "Įspėjimas", "Warning!": "Įspėjimas!", "We are checking for updates.": "<PERSON><PERSON><PERSON><PERSON>, ar yra (at-)naujinimų.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Mes negalėjome įkelti išsamios informacijos apie šį paketą, nes ji/-s nebuvo rasta/-s joki<PERSON><PERSON>, i<PERSON> paketų pateiktų šaltinių.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Mes negalėjome įkelti išsamios informacijos apie šį paketą, nes ji/-s nebuvo įdiegta/-s iš pasiekiamo/-s paketų tvarkytuvo/-ės.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Negalėjo {action} „{package}“. Prašome vėlesniu laiku pabandyti dar kartą. Spustelėkite ant – „{showDetails}“, norint gauti žurnalus iš įdiegiklio.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Negalėjo {action} „{package}“. Prašome vėlesniu laiku pabandyti dar kartą. Spustelėkite ant – „{showDetails}“, norint gauti ž<PERSON>al<PERSON> i<PERSON>.", "We couldn't find any package": "Negalėjome surasti jokių paketų", "Welcome to WingetUI": "<PERSON><PERSON><PERSON>, naudojant „UniGetUI“", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Kurį/-ią paketų tvarkytuvą/-ę norite naudoti?", "Which source do you want to add?": "Kokį šaltinį norite prid<PERSON>ti?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "<PERSON><PERSON> „Winget“ gali būti naudojamas „UniGetUI“ programoje; pats „UniGetUI“ tai pat, gali būti naudojamas su kitais/-omis paketų tvarkytuvais/-ėmis. Tai gali sukelti neaiškumų, nes senia<PERSON>, jis tik tam ir buvo skirtas, ta<PERSON><PERSON><PERSON> š<PERSON> nebegalioja. Todėl „WingetUI“ nebeatspindi projekto funkcijų ir tikslų. Dėl šios priežasties jis tapo „UniGetUI“.", "WinGet could not be repaired": "Nebuvo galima sutaisyti „WinGet“", "WinGet malfunction detected": "Aptikta/-s „WinGet“ triktis/sutrikimas", "WinGet was repaired successfully": "„WinGet“ buvo sėkmingai sutaisytas", "WingetUI": "„UniGetUI“", "WingetUI - Everything is up to date": "„UniGetUI“ – Viskas atnaujinta", "WingetUI - {0} updates are available": "UniGetUI – Pasiekiama/-s/-i {0} {0, plural, one {atnaujinimas/naujinys} few {atnaujinimai/naujiniai} other {atnaujinimų/naujinių}", "WingetUI - {0} {1}": "„UniGetUI“ – {0} {1}", "WingetUI Homepage": "<PERSON><PERSON><PERSON><PERSON><PERSON> „UniGetUI“ tinklalapio puslapis", "WingetUI Homepage - Share this link!": "<PERSON><PERSON><PERSON><PERSON><PERSON> „UniGetUI“ tinklalapio puslapis – Bendrinkite ir pasidalinkite šia nuoroda!", "WingetUI License": "„UniGetUI“ licencija", "WingetUI Log": "„UniGetUI“ žurnalas", "WingetUI Repository": "„UniGetUI“ saugykla", "WingetUI Settings": "„UniGetUI“ nustatymai", "WingetUI Settings File": "„UniGetUI“ nustatymų failas", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "„UniGetUI“ naudoja šias bibliotekas. Be jų, „UniGetUI“ nebūtų galimas.", "WingetUI Version {0}": "„UniGetUI“ versija – {0}", "WingetUI autostart behaviour, application launch settings": "„UniGetUI“ automatinio paleidimo elgs<PERSON>, programos paleidimo nustatymai", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI gali pat<PERSON>, ar <PERSON><PERSON><PERSON>ų taikomosios programos turi pasiekiamus atnaujinimus ir įdiegia juos automati<PERSON>, j<PERSON><PERSON> to pageidaujate", "WingetUI display language:": "UniGetUI atvaizdavimo kalba:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI buvo vykdytas kaip administratorius, o tai nėra rekomenduojama. Kai vykdote UniGetUI kaip administratorių, KIEKVIENA jos vykdoma operacija turės šias privilegijas. <PERSON><PERSON>s galite naudotis programa laisvai, bet mes stipriai rekomenduojame dėl saugumo to nedaryti.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "UniGetUI buvo išverstas į daugiau, nei 40 kalbų, savanorių vertėjų dėka. Ačiū/Dėkui/Dieko<PERSON>! 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "„UniGetUI“ nėra išverstas pagalbinėm/paslaugų (t.y. robotų) priemonėm! Šie asmenys yra atsakingi už vertimus:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI yra programa, kuri padeda tvarkyti taikomąsias programas paprasčiau, pateikdama paprastą „viskas viename“ grafinę są<PERSON>, skirtą komandines eilutes vykdatiems paketų tvarkytuvams.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "UniGetUI yra pervadinamas siekiant išryškinti skirtumą tarp UniGetUI (kaip sąsajos) ir „Winget“ (kaip paketų tvarkytuvo/-ės, kuri(s) p<PERSON><PERSON><PERSON> „Microsoft“, o aš su juo nesusijęs", "WingetUI is being updated. When finished, WingetUI will restart itself": "UniGetUI yra atnaujinama/-s. Pasi<PERSON> šiam procesui UniGetUI pasileis iš naujo.", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI yra nemokamas ir visada tokiu bus. Jokių reklamų/skelbimų, jokių mokėjim<PERSON>, joki<PERSON> „premium“ versijų. 100% proc. nemokamas. Visada.", "WingetUI log": "„UniGetUI“ žurnalas", "WingetUI tray application preferences": "„UniGetUI“ programos nuostatos, sisteminėje juostelėje", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "„UniGetUI“ naudoja šias bibliotekas. Be jų, „UniGetUI“ nebūtų įmanomas.", "WingetUI version {0} is being downloaded.": "„UniGetUI“ versija – {0} atsisiunčiama.", "WingetUI will become {newname} soon!": "„WingetUI“ taps – „{newname}“ netrukus!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI periodiškai netikrins, ar yra atnau<PERSON>/naujinių. Jie vis tiek bus tikrinami paleidimo metu, bet <PERSON><PERSON><PERSON> nebūsite įspėti apie juos.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "UniGetUI rodys <PERSON>/naudoto<PERSON> p<PERSON> (UAC) langą kiekvieną kartą, kai paketas pareikalaus aukštesnių teisių diegimui.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "„WingetUI“ neužilgo bus pavadintas – „{newname}“.\nŠis neatspindės jokių pakeitimų programoje. Aš (kūrėjas) toliau tęsiu šio projekto kūrimą, tik kitokiu pavadinimu.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "UniGetUI nebūtų galima sukurti be visų prisidėjusių pagalbos. Peržiūrėkite jų GitHub profilius. UniGetUI be jų būtų neįmanomas!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "UniGetUI nebūtų galima sukurti be visų prisidėjusių pagalbos. Ačiū Jums visiems! 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} paruoštas diegimui.", "Write here the process names here, separated by commas (,)": null, "Yes": "<PERSON><PERSON>", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "<PERSON><PERSON><PERSON><PERSON> įdiegta versija – {0}", "You have installed WingetUI Version {0}": "<PERSON><PERSON><PERSON> esate įdiegę „UniGetUI“ versiją – {0}", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "<PERSON><PERSON>te naudoti UniGetUI, jums gali tekti įdiegti „{pm}“.", "You may restart your computer later if you wish": "<PERSON><PERSON><PERSON> paleisti kompiuterį iš naujo v<PERSON>les<PERSON>u laiku, jeigu to pageidaujate", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Jums bus pateikta tik kartą; ir administrator<PERSON>us te<PERSON> bus suteiktos pake<PERSON>, kurie jų prašo.", "You will be prompted only once, and every future installation will be elevated automatically.": "Jums bus pateikta tik kartą; ir toliau einantys įdiegimai bus išaukštinti automatiškai.", "You will likely need to interact with the installer.": "Jums greičiausiai reikės sąveikauti su įdiegikliu.", "[RAN AS ADMINISTRATOR]": "VYKDĖ KAIP ADMINISTRATORIUS", "buy me a coffee": "nupirkite man kavos", "extracted": "išskleista", "feature": "funkcija", "formerly WingetUI": "se<PERSON><PERSON> <PERSON><PERSON><PERSON> kaip „WingetUI“", "homepage": "pagrin<PERSON>is tinkla<PERSON> puslapis", "install": "įdiegti", "installation": "įdiegimas", "installed": "įdiegta", "installing": "įdiegiama", "library": "biblioteka", "mandatory": null, "option": "par<PERSON><PERSON>", "optional": null, "uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstallation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstalled": "išdiegt<PERSON>", "uninstalling": "<PERSON>š<PERSON><PERSON><PERSON>", "update(noun)": "atnaujinimas/naujinys", "update(verb)": "(at-)na<PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "„{0}“ i<PERSON><PERSON><PERSON><PERSON>", "{0} aborted": "„{0}“ buvo nutrauktas", "{0} can be updated": "„{0}“ gali būti at<PERSON>/-s", "{0} can be updated to version {1}": "„{0}“ gali būti at<PERSON> į – {1}", "{0} days": "{0} {0, plural, one {diena} few {dienas/-os} other {dienų}}", "{0} desktop shortcuts created": null, "{0} failed": "{0} <PERSON><PERSON><PERSON><PERSON>", "{0} has been installed successfully.": "„{0}“ buvo sėkmingai įdiegtas.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "„{0}“ buvo s<PERSON> įdiegtas. <PERSON><PERSON>, kad <PERSON>te „UniGetUI“, kad užbaigtų įdiegimą", "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": "<PERSON><PERSON><PERSON><PERSON><PERSON> tin<PERSON> pu<PERSON> – {0}", "{0} hours": "{0} {0, plural, one {valanda} few {valandas/-os} other {valandų}}", "{0} installation": "„{0}“ įdiegimas", "{0} installation options": "„{0}“ įdiegimo parinktys", "{0} installer is being downloaded": null, "{0} is being installed": "„{0}“ yra įdiegama/-s", "{0} is being uninstalled": "„{0}“ yra iš<PERSON>gama/-s", "{0} is being updated": "„{0}“ yra atnau<PERSON>/-s", "{0} is being updated to version {1}": "„{0}“ yra atnau<PERSON>/-s į versiją – „{1}“", "{0} is disabled": "„{0}“ – yra i<PERSON>/iš<PERSON>a (-s)", "{0} minutes": "{0} {0, plural, one {minutė} few {minutės} other {minučių}}", "{0} months": "{0} mėn.", "{0} packages are being updated": "{0} {0, plural, one {paketas} few {paketai} other {paketų}} yra atnaujin<PERSON>/naujinamas (-inami/-ama)", "{0} packages can be updated": "{0} {0, plural, one {paketas} few {paketai} other {paketų}} gali būti at<PERSON>ti", "{0} packages found": "{0} {0, plural, one {paketas} few {paketai} other {paketų}} rastas/-i/-ų", "{0} packages were found": "{0} {0, plural, one {paketas} few {paketai} other {paketų}} buvo rastas/-i/-ų", "{0} packages were found, {1} of which match the specified filters.": "{0} {0, plural, one {paketas} few {paketai} other {paketų}} buvo rastas/-i/-ų iš kuri<PERSON> {1} sutampa su esamais/pritaikytais filtrais.", "{0} settings": null, "{0} status": null, "{0} succeeded": "„{0}“ buvo s<PERSON>", "{0} update": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{0}“", "{0} updates are available": "Pasiekiama/-s/-i {0} {0, plural, one {atnaujinimas/naujinimas} few {atnaujinimai/naujinimai} other {atnaujinimų/naujinimų}}", "{0} was {1} successfully!": "„{0}“ buvo „{1}“ sėkmingai!", "{0} weeks": "{0} sav.", "{0} years": "{0} m.", "{0} {1} failed": "{0} {1} <PERSON><PERSON><PERSON><PERSON>", "{package} Installation": "„{package}“ įdiegimas", "{package} Uninstall": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{package}“", "{package} Update": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{package}“", "{package} could not be installed": "Nepavyko įdiegti „{package}“", "{package} could not be uninstalled": "Nepavyko išdiegti „{package}“", "{package} could not be updated": "<PERSON><PERSON><PERSON><PERSON><PERSON> „{package}“", "{package} installation failed": "„{package}“ įdiegimas nepavyko", "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": "„{package}“ iš<PERSON>gi<PERSON>", "{package} update failed": "„{package}“ atnaujinimas nepavyko", "{package} update failed. Click here for more details.": "„{package}“ atnaujinimas nepavyko. Paspauskite čia, norint su<PERSON> da<PERSON>.", "{package} was installed successfully": "„{package}“ buvo sėkmingai įdiegtas", "{package} was uninstalled successfully": "„{package}“ buvo sėkmingai išdiegtas", "{package} was updated successfully": "„{package}“ buvo sėkmingai atnaujintas", "{pcName} installed packages": "Įdiegti paketai – {pcName}", "{pm} could not be found": "Nepavyko rasti „{pm}“", "{pm} found: {state}": "„{pm}“ rasta/-s: „{state}“", "{pm} is disabled": "„{pm}“  išjungta/išgalinta (-s)", "{pm} is enabled and ready to go": "„{pm}“ yra įjungta/įgalinta (-s) ir p<PERSON><PERSON><PERSON><PERSON>i/-ęs", "{pm} package manager specific preferences": "„{pm}“ paketų tvarkytuvei/-ui specifin<PERSON>s parink<PERSON>s", "{pm} preferences": "„{pm}“ parinktys", "{pm} version:": "„{pm}“ versija:", "{pm} was not found!": "„{pm}“ nebuvo rasta/-s!"}