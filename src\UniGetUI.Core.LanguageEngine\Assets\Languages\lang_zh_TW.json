{"\"{0}\" is a local package and can't be shared": "\"{0}\"是本機套件，無法共用", "\"{0}\" is a local package and does not have available details": "\"{0}\"是本機套件，沒有可以顯示的詳細資料", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\"是本機套件，與此功能不相容", "(Last checked: {0})": "（最後更新於：{0}）", "(Number {0} in the queue)": "（剩餘 {0} 個項目）", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@yrct<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,@Henryliu880922, @enKl03B,@StarsShine11904", "0 packages found": "找不到任何套件", "0 updates found": "沒有可用的更新", "1 - Errors": "1 - 錯誤", "1 day": "1 天", "1 hour": "1 小時", "1 month": "1個月", "1 package was found": "已發現一個套件", "1 update is available": "一個可用的更新", "1 week": "1 週", "1 year": "1年", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. 導覽至 {0} 或 {1}頁", "2 - Warnings": "2 - 警告", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. 找到想要加入套件包的套件，並選擇左方的核取方塊", "3 - Information (less)": "3 - 資訊 （簡易）", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. 當選擇了想要加入套件包的套件後，請在工具列上找到並點擊 {0} 選項", "4 - Information (more)": "4 - 資訊 （詳細）", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. 您的套件已成功加入套件包。您可以繼續添加套件，或者匯出套件包", "5 - information (debug)": "5 - 資訊 （除錯）", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "一個受歡迎的C/C++庫管理器。包含了大量的C/C++庫和其他C/C++相關的工具<br> 包含：<b> C/C++庫及相關工具</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "一個充滿工具和可執行檔的儲存庫，在設計使用了 Microsoft .NET 生態系。<br>包含：<b>.NET 相關的工具與腳本</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "一個完全為 .NET 生態系統設計的工具套件庫。<br>包含：<b>.NET 相關的工具", "A restart is required": "需要重新啟動", "Abort install if pre-install command fails": "如果預安裝指令失敗，則中止安裝", "Abort uninstall if pre-uninstall command fails": "如果解除安裝前指令失敗，則中止解除安裝", "Abort update if pre-update command fails": "如果更新前指令失敗，則中止更新", "About": "關於", "About Qt6": "關於 Qt6", "About WingetUI": "關於 UniGetUI", "About WingetUI version {0}": "關於 UniGetUI 版本 {0}", "About the dev": "關於開發者", "Accept": "同意", "Action when double-clicking packages, hide successful installations": "當點兩下套件時，隱藏已成功安裝的項目。", "Add": "新增", "Add a source to {0}": "新增來源至 {0}", "Add a timestamp to the backup file names": "新增一個時間戳給備份的檔案", "Add a timestamp to the backup files": "在備份檔案中新增時間戳", "Add packages or open an existing bundle": "新增套件或開啟一個存在的套件組合", "Add packages or open an existing package bundle": "新增套件或打開現有的套件包", "Add packages to bundle": "將套件新增至套件包", "Add packages to start": "新增套件開始", "Add selection to bundle": "新增套件組合選取項目", "Add source": "新增來源", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "將更新失敗並顯示「未找到適用更新」的項目新增至忽略更新清單", "Adding source {source}": "添加源{source}", "Adding source {source} to {manager}": "將來源 {source} 新增到 {manager}", "Addition succeeded": "新增成功", "Administrator privileges": "系統管理員權限", "Administrator privileges preferences": "系統管理員權限設定", "Administrator rights": "系統管理員權限", "Administrator rights and other dangerous settings": "管理員權限和其他危險設定", "Advanced options": "進階選項", "All files": "所有檔案", "All versions": "所有版本", "Allow changing the paths for package manager executables": "允許變更套件管理員執行檔的路徑", "Allow custom command-line arguments": "允許自訂指令列參數", "Allow importing custom command-line arguments when importing packages from a bundle": "從套件包匯入套件時，允許匯入自訂命令列參數", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "從套件中匯入套件時，允許匯入自訂的安裝前和安裝後指令", "Allow package operations to be performed in parallel": "允許套件操作同時進行", "Allow parallel installs (NOT RECOMMENDED)": "允許多個應用程式同時安裝（不建議）", "Allow pre-release versions": "允許預發行版本", "Allow {pm} operations to be performed in parallel": "允許 {pm} 可以同時執行", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "您也可以在 Windows PowerShell 中使用以下指令來安裝 {0}：", "Always elevate {pm} installations by default": "預設總是優先以 {pm} 為安裝方式", "Always run {pm} operations with administrator rights": "一律使用{pm}管理員權限執行", "An error occurred": "發生錯誤", "An error occurred when adding the source: ": "一個執行錯誤於新增來源時", "An error occurred when attempting to show the package with Id {0}": "顯示套件時有錯誤發生，識別碼：{0}", "An error occurred when checking for updates: ": "檢查更新時發生錯誤：", "An error occurred while loading a backup: ": "載入備份時發生錯誤：", "An error occurred while logging in: ": "登入時發生錯誤：", "An error occurred while processing this package": "處理這個套件的時候發生錯誤", "An error occurred:": "發生錯誤：", "An interal error occurred. Please view the log for further details.": "發生一個內部錯誤，請查詢記錄檔以取得詳細資訊。", "An unexpected error occurred:": "一個無法預期的執行錯誤", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "在修復 WinGet 時遭遇未知的錯誤，請稍候再重試一次", "An update was found!": "找到一個更新！", "Android Subsystem": "Android 子系統", "Another source": "另一個來源", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "任何在安裝或更新操作期間建立的新捷徑，都會自動刪除，而不會在第一次檢測到時顯示確認提示", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "任何在 UniGetUI 外建立或修改的捷徑都將被忽略   您可以通過 {0} 按鈕來新增它們", "Any unsaved changes will be lost": "任何未儲存的變更將會遺失", "App Name": "應用程式名稱", "Appearance": "外觀", "Application theme, startup page, package icons, clear successful installs automatically": "應用程式主題、啟動頁面、套件圖示、自動清除成功安裝", "Application theme:": "應用程式佈景主題：", "Apply": "套用", "Architecture to install:": "安裝架構：", "Are these screenshots wron or blurry?": "這些截圖是錯誤的或是模糊的嗎？", "Are you really sure you want to enable this feature?": "您確定真的要啟用此功能嗎？", "Are you sure you want to create a new package bundle? ": "您確定要建立一個新的套件組合嗎？", "Are you sure you want to delete all shortcuts?": "您確定要刪除所有捷徑嗎？", "Are you sure?": "您確定嗎？", "Ascendant": "遞增", "Ask for administrator privileges once for each batch of operations": "需要系統管理員權限來執行每一個批次檔的操作", "Ask for administrator rights when required": "需要系統管理員權限時詢問", "Ask once or always for administrator rights, elevate installations by default": "管理員權限與優先安裝方式調整", "Ask only once for administrator privileges": "只要求一次管理員權限", "Ask only once for administrator privileges (not recommended)": "多個項目需要管理員權限時，只詢問一次（不建議）", "Ask to delete desktop shortcuts created during an install or upgrade.": "是否刪除在套件包安裝或更新過程中建立的桌面捷徑。", "Attention required": "請注意", "Authenticate to the proxy with an user and a password": "使用使用者和密碼認證 Proxy 伺服器", "Author": "作者", "Automatic desktop shortcut remover": "桌面捷徑自動刪除工具", "Automatically save a list of all your installed packages to easily restore them.": "自動儲存已安裝的套件清單以便輕鬆還原它們。", "Automatically save a list of your installed packages on your computer.": "將已安裝的套件清單自動儲存到您的電腦。", "Autostart WingetUI in the notifications area": "開機時啟動 UniGetUI 並顯示系統匣圖示", "Available Updates": "可用更新", "Available updates: {0}": "{0} 個可用的更新", "Available updates: {0}, not finished yet...": "已發現 {0} 個可用的更新，仍在繼續執行中...", "Backing up packages to GitHub Gist...": "備份套件到 GitHub Gist...", "Backup": "備份", "Backup Failed": "備份失敗", "Backup Successful": "備份成功", "Backup and Restore": "備份與還原", "Backup installed packages": "備份已安裝的套件", "Backup location": "備份位置", "Become a contributor": "成為一位協作者", "Become a translator": "成為一位翻譯人員", "Begin the process to select a cloud backup and review which packages to restore": "開始選擇雲端備份的程式，並檢視要還原的套件", "Beta features and other options that shouldn't be touched": "不建議變更的測試版功能及其他選項", "Both": "兩者皆是", "Bundle security report": "綑綁式安全報告", "But here are other things you can do to learn about WingetUI even more:": "但這裡還有其他方式可以讓您更深入了解 UniGetUI：", "By toggling a package manager off, you will no longer be able to see or update its packages.": "關閉套件管理器後，將無法查看或更新其套件。", "Cache administrator rights and elevate installers by default": "預設暫存系統管理員權限，並在安裝程式執行時自動提升權限", "Cache administrator rights, but elevate installers only when required": "暫存系統管理員權限，但在安裝程式有需要時才提升權限", "Cache was reset successfully!": "快取已清除成功！", "Can't {0} {1}": "無法{0} {1}", "Cancel": "取消", "Cancel all operations": "取消所有操作", "Change backup output directory": "變更備份儲存位置", "Change default options": "變更預設選項", "Change how UniGetUI checks and installs available updates for your packages": "變更 UniGetUI 檢查與安裝套件更新的方式", "Change how UniGetUI handles install, update and uninstall operations.": "變更 UniGetUI 處理安裝、更新以及解除安裝操作的方式。", "Change how UniGetUI installs packages, and checks and installs available updates": "變更 UniGetUI 安裝套件，以及檢查和安裝可用更新", "Change how operations request administrator rights": "變更操作要求管理員權限的方式", "Change install location": "變更安裝位置", "Change this": "變更此選項", "Change this and unlock": "變更此選項並解鎖", "Check for package updates periodically": "定期檢查套件更新", "Check for updates": "檢查更新", "Check for updates every:": "檢查更新頻率：", "Check for updates periodically": "依照週期自動檢查更新", "Check for updates regularly, and ask me what to do when updates are found.": "檢查更新，發現有可用的更新時詢問我。", "Check for updates regularly, and automatically install available ones.": "定期檢查更新，並自動安裝可用的更新", "Check out my {0} and my {1}!": "請參閱我的 {0} 與 {1}！", "Check out some WingetUI overviews": "瀏覽一些關於 UniGetUI 的概述", "Checking for other running instances...": "正在檢查其他執行中的實例...", "Checking for updates...": "正在檢查更新...", "Checking found instace(s)...": "正在檢查發現的實例...", "Choose how many operations shouls be performed in parallel": "選擇並行執行的操作數量", "Clear cache": "清除快取", "Clear finished operations": "清除已完成的操作", "Clear selection": "取消選取", "Clear successful operations": "清理成功的操作", "Clear successful operations from the operation list after a 5 second delay": "在 5 秒延遲後清除操作列表中成功的操作。", "Clear the local icon cache": "清除本機上的圖示快取", "Clearing Scoop cache - WingetUI": "清理 Scoop 快取 - UniGetUI", "Clearing Scoop cache...": "正在清除 Scoop 快取資料...", "Click here for more details": "按一下此處以瞭解更多詳細資訊", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "點擊安裝鈕來開始安裝流程。若跳過安裝，UniGetUI 可能無法正常運作。", "Close": "關閉", "Close UniGetUI to the system tray": "將 UniGetUI 關閉至系統匣", "Close WingetUI to the notification area": "關閉並最小化至系統匣", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "雲端備份使用私人 GitHub Gist 來儲存已安裝套件的清單", "Cloud package backup": "雲端套件備份", "Command-line Output": "命令提示列輸出", "Command-line to run:": "命令列執行：", "Compare query against": "比較查詢", "Compatible with authentication": "與認證相容", "Compatible with proxy": "與 Proxy 相容", "Component Information": "元件資訊", "Concurrency and execution": "同時作業與執行方式", "Connect the internet using a custom proxy": "使用自訂 Proxy 連線到網路", "Continue": "繼續", "Contribute to the icon and screenshot repository": "提供圖示與螢幕截圖", "Contributors": "貢獻者", "Copy": "複製", "Copy to clipboard": "複製到剪貼簿", "Could not add source": "無法新增來源", "Could not add source {source} to {manager}": "無法新增來源 {source} 至 {manager}", "Could not back up packages to GitHub Gist: ": "無法將套件備份至 GitHub Gist：", "Could not create bundle": "無法建立組合", "Could not load announcements - ": "無法讀取公告 - ", "Could not load announcements - HTTP status code is $CODE": "無法讀取公告，HTTP 狀態碼為 $CODE", "Could not remove source": "無法移除來源", "Could not remove source {source} from {manager}": "無法從 {manager} 移除來源 {source}", "Could not remove {source} from {manager}": "無法從 {manager} 中移除 {source}", "Credentials": "證書", "Current Version": "目前版本", "Current status: Not logged in": "目前狀態： 尚未登入", "Current user": "目前使用者", "Custom arguments:": "自訂參數：", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "自訂命令列參數可以改變程式安裝、升級或解除安裝的方式，而 UniGetUI 無法控制。使用自訂命令列可能會破壞套件。請謹慎使用。", "Custom command-line arguments:": "自訂命令參數", "Custom install arguments:": "自訂安裝參數：", "Custom uninstall arguments:": "自訂解除安裝參數：", "Custom update arguments:": "自訂更新參數：", "Customize WingetUI - for hackers and advanced users only": "自訂 UniGetUI - 僅適用於進階使用者", "DEBUG BUILD": "偵錯建置", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "聲明：我們不對下載的套件負任何責任。\n請確定安裝的套件是您信任的軟體。", "Dark": "深色", "Decline": "拒絕", "Default": "預設", "Default installation options for {0} packages": "{0}套件安裝的預設選項", "Default preferences - suitable for regular users": "預設偏好設定 - 適合大部分使用者", "Default vcpkg triplet": "預設 Vcpkg 三重組（triplet）", "Delete?": "刪除？", "Dependencies:": "依賴性：", "Descendant": "遞減", "Description:": "描述：", "Desktop shortcut created": "已建立桌面捷徑", "Details of the report:": "報告的詳細內容：", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "開發應用程式有一定難度，而且這個應用程式為無償提供。如果你喜歡這個應用程式，您可以考慮<b>請我喝杯咖啡</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "在「{discoveryTab}」分頁中，點兩下項目來直接安裝套件 （取代原本的顯示套件資訊）", "Disable new share API (port 7058)": "停用新的分享 API (連接埠 7058)", "Disable the 1-minute timeout for package-related operations": "停用與套件相關操作的 1 分鐘超時限制。", "Disclaimer": "免責聲明", "Discover Packages": "瀏覽套件", "Discover packages": "探索套件", "Distinguish between\nuppercase and lowercase": "區分大小寫", "Distinguish between uppercase and lowercase": "區分大小寫", "Do NOT check for updates": "不要自動檢查更新", "Do an interactive install for the selected packages": "互動式安裝已選取的套件", "Do an interactive uninstall for the selected packages": "互動式解除安裝已選取的套件", "Do an interactive update for the selected packages": "互動式更新已選取的套件", "Do not automatically install updates when the battery saver is on": "開啟電池保護模式時，請勿自動安裝更新", "Do not automatically install updates when the network connection is metered": "網路計量連線時，請勿自動安裝更新", "Do not download new app translations from GitHub automatically": "不要從 GitHub 自動下載翻譯檔案", "Do not ignore updates for this package anymore": "不再忽略此套件包的更新。", "Do not remove successful operations from the list automatically": "不要自動從列表中移除成功的操作", "Do not show this dialog again for {0}": "不要再次於 {0} 中顯示此訊息", "Do not update package indexes on launch": "啟動時不要更新套件索引", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "您是否接受 UniGetUI 收集並傳送匿名使用統計資料，其唯一目的在於了解並改善使用者體驗？", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "您覺得 UniGetUI 有用嗎？如果可以的話，您可以支持我的作品，這樣我就可以繼續讓 UniGetUI 成為更好的套件管理介面。", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "您是否發現 UniGetUI 對您有幫助？您是否想要支持專案開發人員？您可以{0}，這對我們很有幫助！", "Do you really want to reset this list? This action cannot be reverted.": "您確定要重置此清單嗎？此操作無法撤銷", "Do you really want to uninstall the following {0} packages?": "您確定要解除安裝以下 {0} 套件嗎？", "Do you really want to uninstall {0} packages?": "您確定要解除安裝 {0} 套件嗎？", "Do you really want to uninstall {0}?": "您是否確定要解除安裝 {0}？", "Do you want to restart your computer now?": "您是否要立即重新啟動電腦？", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "您是否想要協助將 UniGetUI 翻譯成您使用的語言？請點選<a style=\"color:{0}\" href=\"{1}\"a>此處</a>來參閱貢獻方式。", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "沒有意願贊助嗎？沒關係，您可以將 UniGetUI 分享給您的朋友，幫忙宣傳 UniGetUI。", "Donate": "贊助", "Done!": "完成！", "Download failed": "下載失敗", "Download installer": "下載安裝程式", "Download operations are not affected by this setting": "下載作業不受此設定影響", "Download selected installers": "下載選取的安裝程式", "Download succeeded": "下載成功", "Download updated language files from GitHub automatically": "自動從Github下載語言文件", "Downloading": "下載中", "Downloading backup...": "下載備份...", "Downloading installer for {package}": "正在下載 {package} 套件的安裝程式", "Downloading package metadata...": "正在下載套件的中繼資料...", "Enable Scoop cleanup on launch": "啟動時自動清理 Scoop", "Enable WingetUI notifications": "啟用 UniGetUI 通知", "Enable an [experimental] improved WinGet troubleshooter": "啟用 [實驗性] 改良版的 WinGet 疑難排解工具", "Enable and disable package managers, change default install options, etc.": "啟用或停用套件管理員、變更預設安裝選項等。", "Enable background CPU Usage optimizations (see Pull Request #3278)": "啟用背景處理器使用最佳化（請參閱 拉取請求 #3278）", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "啟用背景 API（UniGetUI 小工具和共享，連接埠 7058）", "Enable it to install packages from {pm}.": "從{pm}啟用安裝套件", "Enable the automatic WinGet troubleshooter": "啟用 WinGet 自動疑難排解程式", "Enable the new UniGetUI-Branded UAC Elevator": "啟用新的 UniGetUI 使用者帳戶控制 權限提升程式權限", "Enable the new process input handler (StdIn automated closer)": "啟用新的程序輸入處理程式 (StdIn 自動關閉)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "只有在您完全瞭解下列設定的作用及其可能涉及的影響和危險後，才能啟用這些設定。", "Enable {pm}": "啟用 {pm}", "Enter proxy URL here": "在這裡輸入 Proxy 伺服器網址", "Entries that show in RED will be IMPORTED.": "顯示為紅色的項目將會被輸入。", "Entries that show in YELLOW will be IGNORED.": "顯示黃色的項目將被忽略。", "Error": "錯誤", "Everything is up to date": "每項更新都是最新的", "Exact match": "完全符合", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "您桌面上的現有捷徑將被掃描，您需要選擇要保留的捷徑以及要移除的捷徑", "Expand version": "更多版本", "Experimental settings and developer options": "實驗性設定與開發選項", "Export": "匯出", "Export log as a file": "匯出紀錄檔", "Export packages": "匯出套件", "Export selected packages to a file": "匯出已選取的套件", "Export settings to a local file": "匯出設定為本機檔案", "Export to a file": "匯出為檔案", "Failed": "失敗", "Fetching available backups...": "擷取可用備份...", "Fetching latest announcements, please wait...": "正在取得最新的公告，請稍後...", "Filters": "過濾器", "Finish": "完成", "Follow system color scheme": "跟隨系統色彩模式", "Follow the default options when installing, upgrading or uninstalling this package": "安裝、升級或解除安裝此套件時，請遵循預設選項", "For security reasons, changing the executable file is disabled by default": "基於安全理由，預設停用變更可執行檔案", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "基於安全理由，自訂指令列參數預設為停用。請前往 UniGetUI 安全性設定變更。", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "基於安全理由，預設停用作業前與作業後指令碼。前往 UniGetUI 安全性設定變更此設定。", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "強制使用 ARM 編譯的 winget 版本 （僅適用於 ARM64 系統）", "Formerly known as WingetUI": "前身為 WingetUI", "Found": "已偵測", "Found packages: ": "找到安裝包：", "Found packages: {0}": "發現套件：{0}", "Found packages: {0}, not finished yet...": "正在搜尋中，已發現 {0} 個套件...", "General preferences": "一般設定", "GitHub profile": "GitHub 個人頁面", "Global": "全域設定", "Go to UniGetUI security settings": "前往 UniGetUI 安全設定", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "未知但有用的工具程式和其他有趣套件的存放庫。<br>包含：<b>工具程式、命令列程式、一般軟體 （需要額外的bucket）</b>", "Great! You are on the latest version.": "太棒了！您已經在使用 UniGetUI 的最新版本。", "Grid": "並排", "Help": "說明", "Help and documentation": "說明與文件", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "在這裡，您可以更改 UniGetUI 關於以下快速鍵的行為。勾選某個快速鍵將使 UniGetUI 在未來更新時刪除它。取消勾選則會保留該快速鍵不變。", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "您好，我的名字是 Martí，我是 UniGetUI 專案的<i>開發人員</i>。UniGetUI 完全是在我的空閒時間製作的。", "Hide details": "隱藏詳細資料", "Homepage": "首頁", "Hooray! No updates were found.": "您現在為最新狀態", "How should installations that require administrator privileges be treated?": "應該如何處理需要系統管理員權限的安裝？", "How to add packages to a bundle": "如何將套件加入套件包", "I understand": "我了解", "Icons": "圖示", "Id": "ID", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "如果您已啟用雲端備份，則會以 GitHub Gist 的形式儲存於此帳戶中", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "從套件包匯入套件時，忽略自訂的安裝前和安裝後指令", "Ignore future updates for this package": "略過此套件未來的更新", "Ignore packages from {pm} when showing a notification about updates": "在顯示更新通知時，忽略來自 {pm} 的套件", "Ignore selected packages": "略過已選取的套件", "Ignore special characters": "忽略特殊符號", "Ignore updates for the selected packages": "忽略已選取套件的更新", "Ignore updates for this package": "忽略此套件的更新", "Ignored updates": "已略過的更新", "Ignored version": "已略過的版本", "Import": "匯入", "Import packages": "匯入套件", "Import packages from a file": "從檔案匯入套件", "Import settings from a local file": "從本機檔案匯入", "In order to add packages to a bundle, you will need to: ": "為了將套件加入套件包，您需要：", "Initializing WingetUI...": "正在準備 UniGetUI...", "Install": "安裝", "Install Scoop": "安裝 <PERSON>oop", "Install and more": "安裝與更多", "Install and update preferences": "安裝和更新喜好設定", "Install as administrator": "以系統管理員身分安裝", "Install available updates automatically": "自動安裝可用的更新", "Install location can't be changed for {0} packages": "{0}套件安裝的位置無法變更", "Install location:": "安裝位置：", "Install options": "安裝選項", "Install packages from a file": "從檔案安裝套件", "Install prerelease versions of UniGetUI": "安裝 UniGetUI 的預發布版本", "Install selected packages": "安裝已選取的套件", "Install selected packages with administrator privileges": "以系統管理員身分安裝已選取的套件", "Install selection": "安裝選擇的程式", "Install the latest prerelease version": "安裝最新的預先發布版本", "Install updates automatically": "自動安裝更新", "Install {0}": "安裝 {0}", "Installation canceled by the user!": "安裝已被使用者取消！", "Installation failed": "安裝失敗", "Installation options": "安裝選項", "Installation scope:": "安裝範圍：", "Installation succeeded": "已安裝成功", "Installed Packages": "已安裝的套件", "Installed Version": "已安裝的版本", "Installed packages": "已安裝的套件", "Installer SHA256": "安裝程式 SHA256  值", "Installer SHA512": "安裝程式 SHA512 雜湊值", "Installer Type": "安裝類型", "Installer URL": "安裝來源網址", "Installer not available": "安裝程式無法使用", "Instance {0} responded, quitting...": "實體 {0} 已回應，正在完成...", "Instant search": "即時搜尋", "Integrity checks can be disabled from the Experimental Settings": "可從實驗設定停用完整性檢查。", "Integrity checks skipped": "跳過完整性檢查", "Integrity checks will not be performed during this operation": "此操作期間不會執行完整性檢查", "Interactive installation": "互動式安裝", "Interactive operation": "互動式操作", "Interactive uninstall": "互動式解除安裝", "Interactive update": "互動式更新", "Internet connection settings": "網路連線設定", "Is this package missing the icon?": "這個套件是否缺少圖示？", "Is your language missing or incomplete?": "你的語言遺失或是尚未完成", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "無法保證所提供的憑證會被安全地儲存，因此您可以不使用銀行帳戶的憑證", "It is recommended to restart UniGetUI after WinGet has been repaired": "建議在 WinGet 修復完成後，重新啟動 UniGetUI", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "強烈建議重新安裝 UniGetUI 以解決問題。", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "WinGet 似乎沒有正常運作，您是否想要嘗試修復 WinGet？", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "看起來您以系統管理員身份執行 UniGetUI，但是並不建議這麼做。您仍然可以使用此應用程式，但我們建議您不要以系統管理員身份執行 UniGetUI，點選「{showDetails}」以瞭解原因。", "Language": "語言", "Language, theme and other miscellaneous preferences": "語言、佈景主題與其他設定", "Last updated:": "最近更新：", "Latest": "最新", "Latest Version": "最新版本", "Latest Version:": "最新版本：", "Latest details...": "最新詳細資訊", "Launching subprocess...": "正在啟動子程序...", "Leave empty for default": "預設值為空", "License": "授權", "Licenses": "授權許可", "Light": "淺色", "List": "清單", "Live command-line output": "即時命令列輸出", "Live output": "即時輸出", "Loading UI components...": "正在載入 UI 元件...", "Loading WingetUI...": "正在載入 UniGetUI...", "Loading packages": "正在掃描套件", "Loading packages, please wait...": "正在載入套件，請稍候...", "Loading...": "正在載入...", "Local": "本機", "Local PC": "電腦本機", "Local backup advanced options": "本機備份進階選項", "Local machine": "本機", "Local package backup": "本機套件備份", "Locating {pm}...": "正在定位 {pm}...", "Log in": "登入", "Log in failed: ": "登入失敗：", "Log in to enable cloud backup": "登入以啟用雲端備份", "Log in with GitHub": "從 Github 登入", "Log in with GitHub to enable cloud package backup.": "使用 GitHub 登入，以啟用雲端套件備份。", "Log level:": "紀錄等級：", "Log out": "登出", "Log out failed: ": "登出失敗：", "Log out from GitHub": "從 Github 登出", "Looking for packages...": "正在尋找套件...", "Machine | Global": "本機 | 全域", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "不正確的命令列參數可能會破壞套件，甚至允許惡意使用者取得執行權限。因此，預設關閉了匯入自訂命令列參數的功能", "Manage": "管理", "Manage UniGetUI settings": "管理 UniGetUI 設定", "Manage WingetUI autostart behaviour from the Settings app": "讓 UniGetUI 自動啟動", "Manage ignored packages": "管理忽略的套件", "Manage ignored updates": "管理已略過的更新", "Manage shortcuts": "桌面捷徑管理", "Manage telemetry settings": "管理遙測設定", "Manage {0} sources": "管理 {0} 個來源", "Manifest": "清單", "Manifests": "清單檔案", "Manual scan": "手動掃描", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Microsoft的官方套件管理程式。充滿有名和經過驗證的軟體套件<br>包含：<b>一般軟體、在 Microsoft Store 上架的應用程式</b>", "Missing dependency": "缺少依賴", "More": "更多", "More details": "詳細資料", "More details about the shared data and how it will be processed": "有關共用資料的更多詳細資訊，以及如何處理這些資料", "More info": "詳細資訊", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "備註：此疑難排解程式可以從 UniGetUI 設定中停用，位於 WinGet 設定中。", "Name": "名稱", "New": "新增", "New Version": "新版本", "New bundle": "新的套件組合", "New version": "新版本", "Nice! Backups will be uploaded to a private gist on your account": "非常好！備份會上傳到您帳戶的私人 gist 中", "No": "否", "No applicable installer was found for the package {0}": "未找到適用於套件的安裝程式 {0}", "No dependencies specified": "未指定依賴", "No new shortcuts were found during the scan.": "掃描過程中未發現新的捷徑。", "No packages found": "找不到套件", "No packages found matching the input criteria": "沒有套件符合輸入的條件", "No packages have been added yet": "沒有新增套件", "No packages selected": "沒有套件被選取", "No packages were found": "沒有套件被找到", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "我們不會收集或傳送任何個人資訊，而且所收集的資料都是匿名的，因此無法追溯到您。", "No results were found matching the input criteria": "沒有符合輸入條件的結果", "No sources found": "沒有找到來源", "No sources were found": "找不到來源", "No updates are available": "沒有可安裝的更新", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS 的套件管理程式。圍繞 Javascript 生態的完整套件庫和其他工具程式<br>包含：<b>Node Javascript 函式庫和其他相關工具程式</b>", "Not available": "無法使用", "Not finding the file you are looking for? Make sure it has been added to path.": "找不到您要的檔案？請確定它已加入路徑。", "Not found": "沒有找到", "Not right now": "現在不行", "Notes:": "備註：", "Notification preferences": "通知設定", "Notification tray options": "系統匣圖示選項", "Notification types": "通知類型", "NuPkg (zipped manifest)": "NuPkg （壓縮清單）", "OK": "確定", "Ok": "確定", "Open": "開啟", "Open GitHub": "瀏覽 GitHub", "Open UniGetUI": "啟動 UniGetUI", "Open UniGetUI security settings": "開啟 UniGetUI 安全設定", "Open WingetUI": "開啟 UniGetUI", "Open backup location": "開啟備份檔案位置", "Open existing bundle": "開啟套件組合", "Open install location": "開啟安裝位置", "Open the welcome wizard": "開啟首次設定精靈", "Operation canceled by user": "操作已由使用者取消", "Operation cancelled": "取消操作", "Operation history": "操作歷史記錄", "Operation in progress": "正在執行操作", "Operation on queue (position {0})...": "下個安裝項目（位置 {0}）", "Operation profile:": "操作狀況：", "Options saved": "選項已儲存", "Order by:": "根據排序：", "Other": "其他", "Other settings": "其他設定", "Package": "套件", "Package Bundles": "套件組合", "Package ID": "套件識別碼", "Package Manager": "套件管理器", "Package Manager logs": "套件管理器紀錄", "Package Managers": "套件管理員", "Package Name": "套件名稱", "Package backup": "套件備份", "Package backup settings": "套件備份設定", "Package bundle": "套件組合", "Package details": "套件詳細資料", "Package lists": "套件清單", "Package management made easy": "套件管理變的簡單", "Package manager": "套件管理員", "Package manager preferences": "套件管理平台偏好設定", "Package managers": "套件管理器", "Package not found": "未找到套件", "Package operation preferences": "封裝操作偏好設定", "Package update preferences": "套件更新偏好設定", "Package {name} from {manager}": "{manager} 中的 {name} 套件", "Package's default": "套件的預設值", "Packages": "套件", "Packages found: {0}": "發現套件：{0}", "Partially": "部分", "Password": "密碼", "Paste a valid URL to the database": "貼上可用的資料庫網址", "Pause updates for": "暫停更新至", "Perform a backup now": "立即備份", "Perform a cloud backup now": "立即執行雲端備份", "Perform a local backup now": "立即執行本機備份", "Perform integrity checks at startup": "啟動時執行完整性檢查", "Performing backup, please wait...": "正在備份中,請稍後...", "Periodically perform a backup of the installed packages": "定期備份已安裝的套件", "Periodically perform a cloud backup of the installed packages": "定期執行已安裝套件的雲端備份", "Periodically perform a local backup of the installed packages": "定期執行已安裝套件的本機備份", "Please check the installation options for this package and try again": "請檢查此套件包的安裝選項，然後再試一次。", "Please click on \"Continue\" to continue": "請點擊\"繼續\"以繼續", "Please enter at least 3 characters": "請輸入至少三個字元", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "請注意，由於在本機上啟用了套件管理程式，某些套件可能無法安裝。", "Please note that not all package managers may fully support this feature": "請注意，並非所有套件管理員都完全支援此功能", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "請注意，有些來源的套件無法進行匯出，以灰色標示的套件不支援此功能。", "Please run UniGetUI as a regular user and try again.": "請以一般使用者身份執行 UniGetUI 並再試一次。", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "請參閱命令列輸出或參考操作歷史記錄以取得有關該問題的詳細資訊。", "Please select how you want to configure WingetUI": "請選擇您設定 UniGetUI 的方式", "Please try again later": "請稍後再試", "Please type at least two characters": "請輸入至少兩個字符。", "Please wait": "請稍候...", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "請等候 {0} 安裝，在安裝過程期間可能會出現黑色視窗，請勿關閉它並等待它執行完畢。", "Please wait...": "請稍候...", "Portable": "免安裝程式", "Portable mode": "便攜模式", "Post-install command:": "安裝後指令：", "Post-uninstall command:": "解除安裝後指令：", "Post-update command:": "更新後指令：", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "PowerShell 的套件管理器。尋找庫和腳本以擴充 PowerShell 功能<br>包含：<b>模組、腳本、Cmdlet</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "安裝前和安裝後的指令可能會對您的裝置造成非常惡劣的影響，如果設計是這樣的話。從套件包匯入指令可能非常危險，除非您信任該套件包的來源。", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "安裝前和安裝後指令會在套件安裝、升級或解除安裝前後執行。請注意，除非小心使用，否則這些指令可能會破壞系統。", "Pre-install command:": "預安裝指令：", "Pre-uninstall command:": "預解除安裝前指令：", "Pre-update command:": "更新前指令：", "PreRelease": "預先發行", "Preparing packages, please wait...": "準備套件包中，請稍後...", "Proceed at your own risk.": "請自行承擔風險。", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "阻止透過 UniGetUI Elevator 或 GSudo 進行任何形式的提升", "Proxy URL": "Proxy 網址", "Proxy compatibility table": "Proxy 相容性列表", "Proxy settings": "Proxy 設定", "Proxy settings, etc.": "Proxy 設定等。", "Publication date:": "發佈日期：", "Publisher": "發行者", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Python 的套件管理程式。完整的 Python 套件庫和其他與 Python 相關的工具程式<br>包含：<b>Python 套件和相關工具程式</b>", "Quit": "離開", "Quit WingetUI": "離開 UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "減少使用者帳戶控制提示、預設提升安裝、解鎖某些危險功能等。", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "請參閱 UniGetUI 紀錄，以取得有關受影響檔案的詳細資訊", "Reinstall": "重新安裝", "Reinstall package": "重新安裝套件", "Related settings": "相關設定", "Release notes": "版本更新說明", "Release notes URL": "版本更新說明連結", "Release notes URL:": "版本更新說明連結：", "Release notes:": "版本更新說明：", "Reload": "重新整理", "Reload log": "重新載入記錄", "Removal failed": "移除失敗", "Removal succeeded": "移除成功", "Remove from list": "從清單中移除", "Remove permanent data": "移除永久數據", "Remove selection from bundle": "從套件組合移除選取項目", "Remove successful installs/uninstalls/updates from the installation list": "從安裝套件清單中隱藏已經安裝成功、已解除安裝和已更新套件項目", "Removing source {source}": "移除源{source}", "Removing source {source} from {manager}": "從 {manager} 移除來源 {source}", "Repair UniGetUI": "修復 UniGetUI", "Repair WinGet": "修復 WinGet", "Report an issue or submit a feature request": "回報問題或提交功能需求", "Repository": "存放庫", "Reset": "重設", "Reset Scoop's global app cache": "重置 Scoop 的全域應用程式快取。", "Reset UniGetUI": "重設 UniGetUI", "Reset WinGet": "重設 WinGet", "Reset Winget sources (might help if no packages are listed)": "重設 Winget 安裝來源 （當套件清單沒有項目時可能有幫助）", "Reset WingetUI": "重設 UniGetUI", "Reset WingetUI and its preferences": "重設 UniGetUI 與其設定", "Reset WingetUI icon and screenshot cache": "重設 UniGetUI 圖示與截圖快取", "Reset list": "重設清單", "Resetting Winget sources - WingetUI": "重設 Winget 來源 - UniGetUI", "Restart": "重新啟動", "Restart UniGetUI": "重新啟動 UniGetUI", "Restart WingetUI": "重新啟動 UniGetUI", "Restart WingetUI to fully apply changes": "重新啟動 UniGetUI 來完全套用變更", "Restart later": "稍後重新啟動", "Restart now": "立即重新啟動", "Restart required": "需要重新啟動", "Restart your PC to finish installation": "重新啟動來完成更新", "Restart your computer to finish the installation": "請重新啟動您的電腦來完成更新", "Restore a backup from the cloud": "從雲端還原備份", "Restrictions on package managers": "套件管理員的限制", "Restrictions on package operations": "套件操作的限制", "Restrictions when importing package bundles": "匯入套件包時的限制", "Retry": "重試", "Retry as administrator": "以管理員身份重試", "Retry failed operations": "重試失敗的操作", "Retry interactively": "以互動方式重試", "Retry skipping integrity checks": "重試並跳過完整性檢查", "Retrying, please wait...": "正在重試，請稍候...", "Return to top": "回到最上面", "Run": "執行", "Run as admin": "以系統管理員身分執行", "Run cleanup and clear cache": "執行清理及清除快取", "Run last": "最後執行", "Run next": "執行下一個", "Run now": "立即執行", "Running the installer...": "正在執行安裝程式...", "Running the uninstaller...": "正在解除安裝...", "Running the updater...": "正在執行更新程式...", "Save": "儲存", "Save File": "儲存檔案", "Save and close": "儲存並關閉", "Save as": "另存為", "Save bundle as": "另存套件組合為", "Save now": "立即儲存", "Saving packages, please wait...": "正在儲存套件，請稍候...", "Scoop Installer - WingetUI": "Scoop 安裝程式 - UniGetUI", "Scoop Uninstaller - WingetUI": "解除安裝 Scoop - UniGetUI", "Scoop package": "Scoop 套件", "Search": "搜尋", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "搜尋桌面套件，並且在有可用更新時提醒我，不要做討厭的事情。我不希望 UniGetUI 過於複雜，我只想要一個簡單的<b>套件市集</b>", "Search for packages": "搜尋套件", "Search for packages to start": "搜尋套件來開始", "Search mode": "搜尋模式", "Search on available updates": "在可用的更新中搜尋", "Search on your software": "在已安裝的套件中搜尋", "Searching for installed packages...": "正在搜尋已安裝的套件...", "Searching for packages...": "正在搜尋套件...", "Searching for updates...": "正在檢查更新...", "Select": "選擇", "Select \"{item}\" to add your custom bucket": "選取「{item}」來新增自訂 bucket", "Select a folder": "選擇一個資料夾", "Select all": "全選", "Select all packages": "選取所有套件", "Select backup": "選擇備份", "Select only <b>if you know what you are doing</b>.": "只選取<b>在您的理解範圍內的項目</b>。\n", "Select package file": "已選取的套件名稱", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "選擇您要開啟的備份。稍後，您將可檢視要還原的套件/程式。", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "選擇在安裝、更新或解除安裝此套件前應關閉的進程。", "Select the source you want to add:": "選擇想要新增的來源：", "Select upgradable packages by default": "預設選擇可更新的套件", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "選擇您想要使用的<b>套件管理程式</b>來使用 （{0}），設定套件的安裝方式，調整管理員權限的處理方式等。", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "已送出連線請求，正在等待伺服器回應... （{0}%）", "Set a custom backup file name": "自訂備份的檔案名稱", "Set custom backup file name": "自訂備份檔案名稱", "Settings": "設定", "Share": "分享", "Share WingetUI": "分享 UniGetUI", "Share anonymous usage data": "分享匿名使用資料", "Share this package": "分享這個套件", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "如果您修改了安全設定，您將需要再次打開套件包才能使修改生效。", "Show UniGetUI on the system tray": "在系統匣顯示 UniGetUI", "Show UniGetUI's version and build number on the titlebar.": "在標題列上顯示 UniGetUI 的版本", "Show WingetUI": "顯示 UniGetUI 視窗", "Show a notification when an installation fails": "安裝失敗時顯示通知", "Show a notification when an installation finishes successfully": "安裝成功時顯示通知", "Show a notification when an operation fails": "操作失敗時顯示通知", "Show a notification when an operation finishes successfully": "操作成功完成時顯示通知", "Show a notification when there are available updates": "有可用更新時顯示通知", "Show a silent notification when an operation is running": "操作正在進行時顯示背景通知", "Show details": "顯示詳細資訊", "Show in explorer": "在瀏覽器中顯示", "Show info about the package on the Updates tab": "在更新分頁中顯示套件資訊", "Show missing translation strings": "顯示沒有翻譯的字串", "Show notifications on different events": "顯示不同事件的通知", "Show package details": "顯示套件詳細資料", "Show package icons on package lists": "在套件清單中顯示套件圖示", "Show similar packages": "顯示相似的套件", "Show the live output": "顯示即時輸出", "Size": "大小", "Skip": "略過", "Skip hash check": "略過雜湊值檢查", "Skip hash checks": "略過雜湊值驗證", "Skip integrity checks": "略過完整性驗證", "Skip minor updates for this package": "跳過此套件的次要更新", "Skip the hash check when installing the selected packages": "安裝已選取的套件時略過雜湊值驗證", "Skip the hash check when updating the selected packages": "更新已選取的套件時略過雜湊值驗證", "Skip this version": "略過這個版本", "Software Updates": "套件更新", "Something went wrong": "出現某些錯誤", "Something went wrong while launching the updater.": "啟動更新程式時發生錯誤。", "Source": "來源", "Source URL:": "來源網站：", "Source added successfully": "來源已成功新增", "Source addition failed": "新增來源失敗", "Source name:": "來源名稱：", "Source removal failed": "來源移除失敗", "Source removed successfully": "來源已成功移除", "Source:": "來源：", "Sources": "來源", "Start": "開始", "Starting daemons...": "正在開啟背景程式...", "Starting operation...": "開始操作...", "Startup options": "啟動選項", "Status": "狀態", "Stuck here? Skip initialization": "卡住了嗎？跳過初始化", "Suport the developer": "支持開發人員", "Support me": "支持我", "Support the developer": "支持開發者", "Systems are now ready to go!": "系統現在已準備就緒！", "Telemetry": "搖測", "Text": "文字", "Text file": "純文字檔", "Thank you ❤": "謝謝您 ❤️", "Thank you 😉": "謝謝您 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Rust 套件管理程式，<br>包含：<b>Rust 函式庫與使用 Rust 編寫的程式</b>", "The backup will NOT include any binary file nor any program's saved data.": "備份檔「不會」包含任何執行檔與任何程式儲存的資料", "The backup will be performed after login.": "備份將在登入後進行", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "備份檔將包含完整的已安裝套件清單與它們的安裝設定，已忽略的版本與更新也將會被儲存。", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "您指定的套件組合格式似乎不正確，請檢查該檔案後再重試。", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "安裝程式的雜湊值和與不符合預期，故無法驗證此安裝程式的真偽。如果您信任該發行者，再次 {0} 套件將略過雜湊值檢查。", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "經典的 Windows 套件管理程式。您可以在那裡找到所有東西。<br>包含：<b>一般軟體</b>", "The cloud backup completed successfully.": "雲端備份成功完成。", "The cloud backup has been loaded successfully.": "雲端備份已成功載入。", "The current bundle has no packages. Add some packages to get started": "目前套件包沒有任何套件。新增一些套件開始使用。", "The executable file for {0} was not found": "未找到 {0} 的可執行檔案", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "每次安裝、升級或解除安裝{0}套件時，預設會套用下列選項。", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "以下套件將匯出為 JSON 檔案。不會儲存任何使用者資料或執行檔。", "The following packages are going to be installed on your system.": "以下套件將會安裝到您的系統中。", "The following settings may pose a security risk, hence they are disabled by default.": "下列設定可能會造成安全風險，因此預設為停用。", "The following settings will be applied each time this package is installed, updated or removed.": "以下的設定會被套用到這個套件安裝、更新或移除", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "以下的設定在此套件安裝、更新或移除時將會被套用，也會自動儲存這些設定。", "The icons and screenshots are maintained by users like you!": "圖示與螢幕截圖由與您一樣的使用者來維護！", "The installer authenticity could not be verified.": "無法驗證安裝程式的真實性。", "The installer has an invalid checksum": "安裝程式的雜湊值有問題", "The installer hash does not match the expected value.": "安裝程式的雜湊值與預期不同。", "The local icon cache currently takes {0} MB": "此本機圖示快取使用容量為 {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "此專案的目標是建立一個直覺的使用者介面來管理最常見的 Windows 命令列套件管理平台，例如 Winget 和 Scoop。", "The package \"{0}\" was not found on the package manager \"{1}\"": "無法在「{1}」中取得套件「{0}」\n\n\n\n\n", "The package bundle could not be created due to an error.": "套件組合建立時發生錯誤，故無法完成建立。", "The package bundle is not valid": "此套件組合無效", "The package manager \"{0}\" is disabled": "「{0}」套件管理程式已停用", "The package manager \"{0}\" was not found": "找不到「{0}」套件管理程式", "The package {0} from {1} was not found.": "沒有來自 {1} 的套件 {0}", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "檢查更新時將不會考慮此處列出的套件。按兩下它們或按右鍵來停止略過它們的更新。", "The selected packages have been blacklisted": "選擇的套件已經被加入黑名單中", "The settings will list, in their descriptions, the potential security issues they may have.": "這些設定會在說明中列出其可能具有的潛在安全問題。", "The size of the backup is estimated to be less than 1MB.": "備份檔案預計不會超過 1MB。", "The source {source} was added to {manager} successfully": "來源 {source} 已成功的新增到 {manager}", "The source {source} was removed from {manager} successfully": "來源 {source} 已成功的從 {manager} 移除", "The system tray icon must be enabled in order for notifications to work": "必須啟用系統匣圖示，通知功能才能正常運作", "The update process has been aborted.": "更新過程已被中止。", "The update process will start after closing UniGetUI": "更新過程將在關閉 UniGetUI 後開始", "The update will be installed upon closing WingetUI": "UniGetUI 將會在關閉時安裝更新", "The update will not continue.": "更新將不會繼續。", "The user has canceled {0}, that was a requirement for {1} to be run": "使用者已取消 {0} ，這是執行 {1} 的必要條件", "There are no new UniGetUI versions to be installed": "目前沒有新版的 UniGetUI 可供安裝", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "目前有正在進行的操作。退出 UniGetUI 可能會導致這些操作失敗。您確定要繼續嗎？", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "YouTube 上有一些精彩的影片展示了 UniGetUI 及其功能，您可以從中學到一些有用的技巧和建議！", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "有兩個主要原因不建議以管理員身份執行 UniGetUI：\n1.當以管理員權限執行時，Scoop 套件管理器可能會在執行某些指令時遇到問題。\n2.以管理員身份執行 UniGetUI 代表著您下載的任何套件都將以管理員身份執行（這樣做不安全）。", "There is an error with the configuration of the package manager \"{0}\"": "「{0}」套件管理程式中發現組態錯誤", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "有套件正在進行安裝。 如果現在關閉 UniGetUI，安裝可能會失敗並產生意外結果。是否仍要關閉 UniGetUI？", "They are the programs in charge of installing, updating and removing packages.": "它們是負責安裝、更新和刪除軟體套件的程式。", "Third-party licenses": "第三方授權", "This could represent a <b>security risk</b>.": "這可能帶有<b>安全風險</b>。", "This is not recommended.": "不建議這麼做。", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "這可能是因為您發送的套件已被移除或下架，或者該套件發佈在您未啟用的套件管理平台上。收到的 ID 是 {0}", "This is the <b>default choice</b>.": "這是一個<b>預設選取項目</b>。", "This may help if WinGet packages are not shown": "如果 WinGet 套件沒有顯示，這可能會有所幫助", "This may help if no packages are listed": "這可能對於沒有套件的狀況會有幫助", "This may take a minute or two": "這需要一些時間", "This operation is running interactively.": "此操作以互動方式執行。", "This operation is running with administrator privileges.": "此操作是以管理員權限執行。", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "此選項將導致問題。任何無法自我提升的操作都會失敗。以管理員身份安裝/更新/解除安裝將無法執行。", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "此套件包有一些潛在危險的設定，可能會被預設忽略。", "This package can be updated": "這個套件有可用的更新", "This package can be updated to version {0}": "此套件可以更新到版本 {0}", "This package can be upgraded to version {0}": "此套件可以被更新至版本 {0}", "This package cannot be installed from an elevated context.": "此套件無法從更高權限的環境下安裝。", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "此套件沒有螢幕截圖或圖示嗎？您可以向 UniGetUI 開放資料庫提供這些項目。", "This package is already installed": "此套件已安裝", "This package is being processed": "正在處理此套件", "This package is not available": "目前無法使用此套件", "This package is on the queue": "此套件已排入佇列", "This process is running with administrator privileges": "此程式正在使用系統管理員身份執行", "This project has no connection with the official {0} project — it's completely unofficial.": "此專案與官方的 {0} 專案無關 — 它完全來自第三方社群。", "This setting is disabled": "此選項已停用", "This wizard will help you configure and customize WingetUI!": "此精靈將會協助您設定並自訂 UniGetUI。", "Toggle search filters pane": "切換搜尋過濾器面板", "Translators": "翻譯人員", "Try to kill the processes that refuse to close when requested to": "嘗試停止那些在被要求關閉時拒絕關閉的進程。", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "開啟此功能可變更用於與套件管理員互動的可執行檔案。雖然這可讓您更精準的自訂安裝程式，但也可能會有危險", "Type here the name and the URL of the source you want to add, separed by a space.": "輸入您想要新增的來源名稱與網址，以空白符號分隔。", "Unable to find package": "無法找到套件", "Unable to load informarion": "無法載入資訊", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI 收集匿名使用資料，以改善使用者體驗。", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI 收集匿名使用資料的唯一目的是瞭解並改善使用者體驗。", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI 已偵測到一個新的桌面捷徑，可以自動刪除。", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI 已偵測到以下桌面捷徑，這些捷徑可以在未來的更新中自動刪除。", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI 已偵測到 {0} 個新的桌面捷徑，不需要自動刪除。", "UniGetUI is being updated...": "UniGetUI 正在更新...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI 與任何相容的套件管理器無關。UniGetUI 是一個獨立的專案。", "UniGetUI on the background and system tray": "UniGetUI 在背景和系統匣上", "UniGetUI or some of its components are missing or corrupt.": "UniGetUI 或其某些元件遺失或損毀。", "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI 需要 {0} 協同作業，但未被安裝在您的系統中。", "UniGetUI startup page:": "UniGetUI 啟動頁面：", "UniGetUI updater": "UniGetUI 更新工具", "UniGetUI version {0} is being downloaded.": "正在下載 UnigetUI 版本 {0}", "UniGetUI {0} is ready to be installed.": "UniGetUI 版本 {0} 已準備好安裝", "Uninstall": "解除安裝", "Uninstall Scoop (and its packages)": "解除安裝 Scoop（及其套件）", "Uninstall and more": "解除安裝與更多", "Uninstall and remove data": "解除安裝和移除資料", "Uninstall as administrator": "以系統管理員身分解除安裝", "Uninstall canceled by the user!": "解除安裝已被使用者取消！", "Uninstall failed": "解除安裝失敗", "Uninstall options": "解除安裝選項", "Uninstall package": "解除安裝套件", "Uninstall package, then reinstall it": "解除安裝套件，然後重新安裝", "Uninstall package, then update it": "解除安裝套件，然後更新", "Uninstall previous versions when updated": "更新時解除安裝先前版本", "Uninstall selected packages": "解除安裝已選取的套件", "Uninstall selection": "解除安裝選擇的程式", "Uninstall succeeded": "解除安裝成功", "Uninstall the selected packages with administrator privileges": "以系統管理員權限解除安裝已選取的套件", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "來源為「{0}」的可解除安裝套件未在任何套件管理平台上發布，因此沒有可用於顯示它們的詳細資料。", "Unknown": "未知", "Unknown size": "大小未知", "Unset or unknown": "尚未設定或未知", "Up to date": "為最新版本", "Update": "更新", "Update WingetUI automatically": "自動更新 UniGetUI", "Update all": "更新全部", "Update and more": "更新與更多", "Update as administrator": "以系統管理員身分更新", "Update check frequency, automatically install updates, etc.": "更新檢查頻率、自動安裝更新等。", "Update date": "更新日期", "Update failed": "更新失敗", "Update found!": "已有新的更新！", "Update now": "現在更新", "Update options": "更新選項", "Update package indexes on launch": "啟動時更新套件資訊", "Update packages automatically": "自動更新套件", "Update selected packages": "更新已選取的套件", "Update selected packages with administrator privileges": "以系統管理員權限更新已選取的套件", "Update selection": "更新選擇的程式", "Update succeeded": "已完成更新", "Update to version {0}": "更新版本至 {0}", "Update to {0} available": "{0} 有可用的更新", "Update vcpkg's Git portfiles automatically (requires Git installed)": "自動更新 Vcpkg 的 Git 套件描述檔（需要安裝 Git）。", "Updates": "更新", "Updates available!": "有可安裝的更新！", "Updates for this package are ignored": "此套件已忽略的更新", "Updates found!": "發現更新！", "Updates preferences": "更新設定", "Updating WingetUI": "正在更新 UniGetUI", "Url": "網址", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "使用舊版內建的 WinGet，而非 PowerShell CMDLets", "Use a custom icon and screenshot database URL": "使用自訂的圖示與截圖資料庫的網址", "Use bundled WinGet instead of PowerShell CMDlets": "使用內建的 WinGet，而非 PowerShell CMDLets", "Use bundled WinGet instead of system WinGet": "使用內建的 WinGet，而不是系統中的 WinGet", "Use installed GSudo instead of UniGetUI Elevator": "使用已安裝的 GSudo 取代 UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "以安裝的 GSudo 來取代內建的版本 （需要重新啟動應用程式）", "Use system Chocolatey": "使用系統內的 Chocolatey", "Use system Chocolatey (Needs a restart)": "使用系統內的 Chocolatey （需要重新啟動）", "Use system Winget (Needs a restart)": "使用系統內的 Winget （需要重新啟動）", "Use system Winget (System language must be set to english)": "使用系統內的 Winget （系統語言需要設定成英文）", "Use the WinGet COM API to fetch packages": "使用 WinGet COM API 來取得套件。", "Use the WinGet PowerShell Module instead of the WinGet COM API": "使用 WinGet PowerShell 模組，而不是 WinGet COM API", "Useful links": "分享連結", "User": "使用者", "User interface preferences": "使用者介面偏好設定", "User | Local": "使用者｜語言", "Username": "使用者名稱", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "使用 UniGetUI 代表著您已經接受 GNU Lesser General Public License v2.1 授權", "Using WingetUI implies the acceptation of the MIT License": "使用 UniGetUI 代表著您已接受 MIT 許可證", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "未找到 Vcpkg 根目錄。請自訂 %VCPKG_ROOT% 環境變數，或從 UniGetUI 設定中自訂它。", "Vcpkg was not found on your system.": "您的系統中未找到 Vcpkg。", "Verbose": "詳細資料", "Version": "版本", "Version to install:": "即將安裝的版本：", "Version:": "版本：", "View GitHub Profile": "檢視 GitHub 個人檔案", "View WingetUI on GitHub": "檢視 UniGetUI 專案的 GitHub 頁面", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "檢視 UniGetUI 原始碼。您可以在那裡回報錯誤或建議功能，或直接參與貢獻 UniGetUI 專案", "View mode:": "檢視樣式：", "View on UniGetUI": "在 UniGetUI 上檢視", "View page on browser": "在瀏覽器中顯示頁面", "View {0} logs": "檢視 {0} 記錄", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "在嘗試執行需要網際網路連線的操作之前，請等待裝置連線至網際網路。", "Waiting for other installations to finish...": "正在等待其他項目安裝完成...", "Waiting for {0} to complete...": "正在等待 {0} 完成...", "Warning": "警告", "Warning!": "警告！", "We are checking for updates.": "我們正在檢查更新。", "We could not load detailed information about this package, because it was not found in any of your package sources": "我們無法載入有關此套件的詳細資料，因為在您的任何套件來源中都找不到它", "We could not load detailed information about this package, because it was not installed from an available package manager.": "我們無法載入有關此套件的詳細資料，因為此套件並非從任何可用的套件管理平台安裝。", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "我們無法{action} {package}，請稍候再重試一次。點選「{showDetails}」來檢視安裝程式的紀錄。", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "我們無法{action} {package}，請稍候再重試一次。點選「{showDetails}」來檢視解除安裝程式的紀錄。", "We couldn't find any package": "沒有找到任何套件", "Welcome to WingetUI": "歡迎使用 UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "從套件包批量安裝套件時，也會安裝已經安裝的套件", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "偵測到新的捷徑時，會自動刪除它們，而不是顯示此對話框。", "Which backup do you want to open?": "您要開啟哪個備份？", "Which package managers do you want to use?": "您想要使用哪些套件管理程式？", "Which source do you want to add?": "您想要新增哪一個來源？", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "雖然 Winget 可以在 UniGetUI 中使用，但是 UniGetUI 也能與其他套件管理器一起使用，這可能會讓人感到困惑。過去，UniGetUI 只設計用於與 Winget 配合使用，但現在情況已不再如此，因此 UniGetUI 不再能夠代表這個專案的未來發展方向。", "WinGet could not be repaired": "無法完成修復 WinGet", "WinGet malfunction detected": "已偵測到 WinGet 功能異常", "WinGet was repaired successfully": "WinGet 已修復成功", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - 您現在是最新版本", "WingetUI - {0} updates are available": "UniGetUI - {0} 個可安裝的更新", "WingetUI - {0} {1}": "UniGetUI - {1} {0}", "WingetUI Homepage": "UniGetUI 首頁", "WingetUI Homepage - Share this link!": "UniGetUI 首頁 - 分享這個連結！", "WingetUI License": "UniGetUI 授權", "WingetUI Log": "UniGetUI 紀錄", "WingetUI Repository": "UniGetUI 儲存庫", "WingetUI Settings": "UniGetUI 設定", "WingetUI Settings File": "UniGetUI的設定檔案", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI 使用了以下函式庫。沒有它們，UniGetUI 是沒有辦法實現的。", "WingetUI Version {0}": "UniGetUI 版本 {0}", "WingetUI autostart behaviour, application launch settings": "UniGetUI 自動啟動的行為、應用程式啟動設定", "WingetUI can check if your software has available updates, and install them automatically if you want to": "UniGetUI 可以檢查您的套件是否有可用的更新，並在您同意的情況下自動安裝這些更新。", "WingetUI display language:": "UniGetUI 顯示語言", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "UniGetUI 已以管理員身份執行，但這並不推薦。當以管理員身份執行 UniGetUI 時，從 UniGetUI 啟動的每個操作都將擁有管理員權限。您仍然可以使用此程式，但我們強烈建議不要以管理員身分執行 UniGetUI。", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "感謝貢獻翻譯人員的幫助，UniGetUI 已被翻譯成 40 多種語言。謝謝 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "UniGetUI 沒有使用機器翻譯。翻譯由以下使用者貢獻：", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "UniGetUI 是一款應用程式，透過為命令列套件管理程式提供一體化圖形介面，讓您的套件管理變得更加輕鬆。", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI 正在重新命名，以強調 WingetUI（您現在使用的介面）和 Winget 之間的區別（後者是由 Microsoft 開發的套件管理程式，與我無關）", "WingetUI is being updated. When finished, WingetUI will restart itself": "正在更新 UniGetUI，更新完成後 UniGetUI 將自動重新啟動", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "UniGetUI 是免費軟體，而且將維持永遠免費，不需要提供信用卡，且也沒有付費版本。", "WingetUI log": "UniGetUI 紀錄", "WingetUI tray application preferences": "UniGetUI 系統匣圖示設定", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "UniGetUI 使用以下函式庫：", "WingetUI version {0} is being downloaded.": "正在下載 UniGetUI 版本 {0}", "WingetUI will become {newname} soon!": "WingetUI 即將將重新命名為 {newname}！", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "UniGetUI 不會定期檢查更新。更新仍會在啟動時檢查，但您將不會收到相關通知。", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "每當套件需要提升權限安裝時，UniGetUI 將顯示 使用者帳戶控制 視窗。", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI 很快將改名為{newname}。這不會對應用程式產生任何變化。我（開發者）將繼續如同現在一樣開發此專案，只是變更了名稱。", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "如果沒有我們親愛的貢獻者們的幫助，UniGetUI 就沒有辦法實現。請造訪他們的 GitHub 頁面，沒有他們的支援，UniGetUI 是無法完成的！", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "如果沒有貢獻者的幫助，UniGetUI 就沒辦法實現。感謝各位 🥳", "WingetUI {0} is ready to be installed.": "UniGetUI {0} 已準備好安裝。", "Write here the process names here, separated by commas (,)": "在此輸入程式名稱，並以逗號 (,) 分隔", "Yes": "是", "You are logged in as {0} (@{1})": "您的登入帳號是{0}(@{1})", "You can change this behavior on UniGetUI security settings.": "您可以在 UniGetUI 安全設定中變更此行為。", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "您可以自訂在安裝、更新或解除安裝此套件之前或之後執行的指令。這些指令會在命令提示字元上執行，因此命令提示字元腳本在此也能運作。", "You have currently version {0} installed": "您已安裝版本 {0}", "You have installed WingetUI Version {0}": "您已經安裝 UniGetUI 版本 {0}", "You may lose unsaved data": "您可能會遺失未儲存的資料", "You may need to install {pm} in order to use it with WingetUI.": "您可能需要安裝 {pm} 來與 UniGetUI 一起使用。", "You may restart your computer later if you wish": "您稍後可能需要重新啟動電腦", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "每次安裝套件需要提升權限時，您只會收到一次提示，管理員權限將自動授予請求。", "You will be prompted only once, and every future installation will be elevated automatically.": "您只會收到一次提示，以後的每次套件安裝都會自動提升管理員權限。", "You will likely need to interact with the installer.": "您可能需要與安裝程式進行互動。", "[RAN AS ADMINISTRATOR]": "以系統管理員身份執行", "buy me a coffee": "贊助這個專案", "extracted": "已解壓縮", "feature": "功能", "formerly WingetUI": "前身為 WingetUI", "homepage": "首頁", "install": "安裝", "installation": "安裝", "installed": "已安裝", "installing": "正在安裝", "library": "函式庫", "mandatory": "指定", "option": "選項", "optional": "可選", "uninstall": "解除安裝", "uninstallation": "解除安裝", "uninstalled": "已解除安裝", "uninstalling": "正在解除安裝", "update(noun)": "更新", "update(verb)": "更新", "updated": "已更新", "updating": "正在更新", "version {0}": "版本{0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} 安裝選項目前已鎖定，因為{0}遵循預設安裝選項。", "{0} Uninstallation": "解除安裝 {0}", "{0} aborted": "{0} 已取消", "{0} can be updated": "「{0}」套件有可用的更新", "{0} can be updated to version {1}": "{0} 可以更新到版本 {1}", "{0} days": "{0} 天", "{0} desktop shortcuts created": "已在桌面建立{0}個捷徑。", "{0} failed": "{0}失敗", "{0} has been installed successfully.": "{0} 已成功安裝。", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} 已成功安裝。建議重新啟動 UniGetUI 以便完成安裝。", "{0} has failed, that was a requirement for {1} to be run": "{0} 已失敗，這是執行 {1} 的必要條件", "{0} homepage": "{0} 首頁", "{0} hours": "{0} 小時", "{0} installation": "安裝 {0}", "{0} installation options": "{0} 安裝選項", "{0} installer is being downloaded": "{0} 正在下載安裝程式", "{0} is being installed": "正在安裝 {0}", "{0} is being uninstalled": "正在解除安裝 {0}", "{0} is being updated": "正在更新 {0}", "{0} is being updated to version {1}": "正在更新 {0} 到版本 {1}", "{0} is disabled": "{0} 已停用", "{0} minutes": "{0} 分鐘", "{0} months": "{0} 月", "{0} packages are being updated": "{0} 個套件正在更新", "{0} packages can be updated": "有 {0} 個套件可供更新", "{0} packages found": "發現 {0} 個套件", "{0} packages were found": "{0} 個套件已偵測", "{0} packages were found, {1} of which match the specified filters.": "{0} 個套件已找到，{1} 個項目符合過濾條件。", "{0} settings": "{0} 設定", "{0} status": "狀態{0}", "{0} succeeded": "{0}已完成", "{0} update": "更新 {0}", "{0} updates are available": "有可用的 {0} 個更新", "{0} was {1} successfully!": "{0}項已處理為{1}項！", "{0} weeks": "{0} 星期", "{0} years": "{0} 年", "{0} {1} failed": "{0} {1}失敗", "{package} Installation": "安裝 {package}", "{package} Uninstall": "解除安裝 {package}", "{package} Update": "更新 {package}", "{package} could not be installed": "{package} 無法完成安裝", "{package} could not be uninstalled": "{package} 無法解除安裝", "{package} could not be updated": "{package} 無法完成更新", "{package} installation failed": "{package} 安裝失敗", "{package} installer could not be downloaded": "{package} 無法下載安裝程式", "{package} installer download": "{package} 下載安裝程式", "{package} installer was downloaded successfully": "{package} 安裝程式已成功下載", "{package} uninstall failed": "{package} 解除安裝失敗", "{package} update failed": "{package} 更新失敗", "{package} update failed. Click here for more details.": "{package} 更新失敗，點選此處來取得詳細資訊。", "{package} was installed successfully": "{package} 已安裝完成", "{package} was uninstalled successfully": "{package} 已解除安裝完成", "{package} was updated successfully": "{package} 已更新完成", "{pcName} installed packages": "{pcName} 已安裝的套件", "{pm} could not be found": "找不到 {pm}", "{pm} found: {state}": "{pm} 已發現：{state}", "{pm} is disabled": "{pm} 已停用", "{pm} is enabled and ready to go": "{pm} 已啟用並準備好", "{pm} package manager specific preferences": "{pm} 套件管理平台偏好設定", "{pm} preferences": "{pm} 偏好設定", "{pm} version:": "{pm} 版本：", "{pm} was not found!": "沒有找到 {pm}！"}