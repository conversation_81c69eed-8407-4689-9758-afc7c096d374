{"\"{0}\" is a local package and can't be shared": "\"{0}\" és un paquet local i no es pot compartir", "\"{0}\" is a local package and does not have available details": "\"{0}\" és un paquet local i no té detalls disponibles", "\"{0}\" is a local package and is not compatible with this feature": "\"{0}\" és un paquet local i no és compatible amb aquesta característica", "(Last checked: {0})": "(Comprovat per darrer cop: {0})", "(Number {0} in the queue)": "(Número {0} a la cua)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@marticliment", "0 packages found": "No s'han trobat paquets", "0 updates found": "No s'han trobat actualitzacions", "1 - Errors": "1 - <PERSON><PERSON><PERSON>", "1 day": "1 dia", "1 hour": "1 hora", "1 month": "1 mes", "1 package was found": "S'ha trobat 1 paquet", "1 update is available": "Hi ha 1 actualització disponible", "1 week": "1 setmana", "1 year": "1 any", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Nave<PERSON>u a la pàgina de \"{0}\" o \"{1}\"", "2 - Warnings": "2 - Advertències", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. <PERSON><PERSON><PERSON><PERSON> el(s) paquet(s) que volgueu afegir a la col·lecció, i seleccioneu-ne la casella de l'esquerra.", "3 - Information (less)": "3 - Inform<PERSON><PERSON><PERSON> (menys)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. <PERSON>uan els paqueta que volgueu afegir estiguin seleccionats, cliqueu l'opció \"{0}\" a la barra d'eines.", "4 - Information (more)": "4 - Informació (més)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. <PERSON>s paquets ja s'hauràn afegit a la col·lecció. Podeu exportar la col·lecció o afegir més paquets", "5 - information (debug)": "5 - In<PERSON><PERSON><PERSON><PERSON> (depuració)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Un popular administrador de llibreries de C/C++. <br>Conté llibreries per a C i C++, així com altres utilitats útils durant el procés de desenvolupament en C i C++", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Un repositori ple d'eines i executables dissenyats amb l'ecosistema .NET de Microsoft<br><PERSON><PERSON>: <b>Eines i scripts relacionats amb .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Un repositori ple d'eines dissenyades amb l'ecosistema de Microsoft anomenat .NET<br><PERSON>té: <b>Eines relacionades amb .NET</b>", "A restart is required": "Es requereix un reinici", "Abort install if pre-install command fails": "Avorta la instal·lació si la comanda de pre-instal·lació falla", "Abort uninstall if pre-uninstall command fails": "Avorta la desinstal·lació si la comanda de pre-desinstal·lació falla", "Abort update if pre-update command fails": "Avorta l'actualització si la comanda de pre-actualització falla", "About": "Sobre", "About Qt6": "Sobre Qt6", "About WingetUI": "Sobre l'UniGetUI", "About WingetUI version {0}": "Sobre l'UniGetUI versió {0}", "About the dev": "Sobre el desenvolupador", "Accept": "Acceptar", "Action when double-clicking packages, hide successful installations": "Acció en prémer dos cops els paquets, amaga les instal·lacions satisfactòries", "Add": "Afegeix", "Add a source to {0}": "Afegeix una font al {0}", "Add a timestamp to the backup file names": "Afegeix la data i l'hora als noms de les còpies de seguretat", "Add a timestamp to the backup files": "Afegeix una marca de temps a les còpies de seguretat", "Add packages or open an existing bundle": "Afegiu paquets o obriu una col·lecció existent", "Add packages or open an existing package bundle": "Afegiu paquets o obriu una col·lecció", "Add packages to bundle": "Afegiu paquets a la col·lecció", "Add packages to start": "<PERSON><PERSON><PERSON><PERSON> paquets per a començar", "Add selection to bundle": "Afegeix la selecció a la col·lecció", "Add source": "Afegeix una font", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Afegiu qualsevol actualització que falli amb l'error 'No aplica' a la llista d'acttualitzacions ignorades", "Adding source {source}": "Afegint font {source}", "Adding source {source} to {manager}": "Afegint la font {source} al {manager}", "Addition succeeded": "S'ha afegit correctament", "Administrator privileges": "Drets d'administrador", "Administrator privileges preferences": "Preferències dels drets d'administrador", "Administrator rights": "Drets d'administrador", "Administrator rights and other dangerous settings": "Drets d'administrador i altres configuracions perilloses", "Advanced options": "Opcions avançades", "All files": "<PERSON><PERSON> els fitxers", "All versions": "Totes les versions", "Allow changing the paths for package manager executables": "Permeteu canviar els fitxers executables dels administradors de paquets", "Allow custom command-line arguments": "Permet arguments personalitzats de la línia d'ordres", "Allow importing custom command-line arguments when importing packages from a bundle": "Permet la importació d'arguments personalitzats de la línia d'ordres en importar paquets d'una col·lecció", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Permeteu la importació de comandes de pre-instal·lació i post-instal·lació quan s'importin paquets des d'una col·lecció de paquets", "Allow package operations to be performed in parallel": "Permet que les operacions s'executin en paral·lel", "Allow parallel installs (NOT RECOMMENDED)": "Permet la instal·lació paral·lela (NO RECOMANAT)", "Allow pre-release versions": "Permet versions de prellançament", "Allow {pm} operations to be performed in parallel": "Permet que les operacions del {pm} s'executin en paral·lel", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Alternativament, també es pot instal·lar el {0} executant la següent comanda en una línia de comandes del Windows PowerShell:", "Always elevate {pm} installations by default": "Eleva sempre les instal·lacions del {pm} per defecte", "Always run {pm} operations with administrator rights": "Executa sempre les operacions del {pm} amb drets d'administrador", "An error occurred": "Hi ha hagut un error", "An error occurred when adding the source: ": "Hi ha hagut un error quan s'afegia la font: ", "An error occurred when attempting to show the package with Id {0}": "Hi ha hagut un error en mostrar els detalls del paquet amb Id {0}", "An error occurred when checking for updates: ": "Hi ha hagut un error quan es cercaven les actualitzacions: ", "An error occurred while loading a backup: ": "Hi ha hagut un error en carregar la còpia de seguretat:", "An error occurred while logging in: ": "Hi ha hagut un error en iniciar sessió:", "An error occurred while processing this package": "Hi ha hagut un error al processar aquest paquet", "An error occurred:": "Hi ha hagut un error:", "An interal error occurred. Please view the log for further details.": "Hi ha hagut un error intern. <PERSON>eieu el registre per a més detalls", "An unexpected error occurred:": "Hi ha hagut un error inesperat:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Hi ha hagut un error durant la reparació del WinGet. Proveu-ho més tard", "An update was found!": "S'ha trobat una actualització!", "Android Subsystem": "Subsistema Android", "Another source": "Una altra font", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Qualsevol icona creada durant una instal·lació o una actualització s'eliminarà automàticament, en comptes de mostrar un diàleg de confirmació el primer cop que es detecti.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Qualsevol icona creada o modificada fora de l'UniGetUI s'ignorarà. Les podreu afegir mitjançant el botó d' {0}", "Any unsaved changes will be lost": "Es perdrà qualsevol canvi no desat", "App Name": "Nom de l'app", "Appearance": "Aparença", "Application theme, startup page, package icons, clear successful installs automatically": "<PERSON><PERSON> de l'aplicació, p<PERSON><PERSON><PERSON> d'inici, icones als paque<PERSON>, neteja les instal·lacions exitoses automàticament", "Application theme:": "Tema de l'aplicació:", "Apply": "Aplica", "Architecture to install:": "Arquitectura a instal·lar:", "Are these screenshots wron or blurry?": "Aquestes captures són errònies o estan borroses?", "Are you really sure you want to enable this feature?": "Realment voleu activar aquesta característica?", "Are you sure you want to create a new package bundle? ": "Realment voleu crear una col·lecció nova?", "Are you sure you want to delete all shortcuts?": "Realment voleu eliminar totes les dreceres?", "Are you sure?": "N'esteu segur/a?", "Ascendant": "Ascendent", "Ask for administrator privileges once for each batch of operations": "Demana drets d'administrador per a cada grup d'operacions", "Ask for administrator rights when required": "Demana els drets d'administrador quan es necessitin", "Ask once or always for administrator rights, elevate installations by default": "<PERSON><PERSON><PERSON> sempre o un sol cop per als drets d'administrador, eleva les instalacions per defecte", "Ask only once for administrator privileges": "Pregunta només un cop per als permisos d'administrador", "Ask only once for administrator privileges (not recommended)": "Demana només un cop per a drets d'administrador (no recomanat)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Pregunta si eliminar les noves dreceres de l'escriptori creades durant una instal·lació o actualització.", "Attention required": "<PERSON>s requereix atenció", "Authenticate to the proxy with an user and a password": "Autenticar-se al proxy amb usuari i contrasenya", "Author": "Autor/a", "Automatic desktop shortcut remover": "Eliminador automàtic de dreceres de l'escriptori", "Automatically save a list of all your installed packages to easily restore them.": "Desa automàticament una llista de tots els paquets instal·lats per a restaurar-los fàcilment.", "Automatically save a list of your installed packages on your computer.": "Desa una llista dels paquets instal·lats al vostre ordinador de forma automàtica", "Autostart WingetUI in the notifications area": "Inicia l'UniGetUI a l'àrea de notificacions", "Available Updates": "Actualitzacions disponibles", "Available updates: {0}": "Actualitzacions disponibles: {0}", "Available updates: {0}, not finished yet...": "Actualitzacions disponibles: {0}, encara no hem acabat...", "Backing up packages to GitHub Gist...": "Fent una còpia de seguretat dels paquets a GitHub Gist...", "Backup": "Fes la còpia", "Backup Failed": "La còpia de seguretat ha fallat", "Backup Successful": "S'ha completat la còpia", "Backup and Restore": "Còpia de seguretat i restauració", "Backup installed packages": "Fes una còpia de seguretat dels paquets instal·lats", "Backup location": "Ubicació de la còpia", "Become a contributor": "<PERSON>r-<PERSON> contribuïdor", "Become a translator": "<PERSON><PERSON>-<PERSON> traductor", "Begin the process to select a cloud backup and review which packages to restore": "Comença el procés de selecció i revisió de quins paquets s'ha de restaurar", "Beta features and other options that shouldn't be touched": "Característiques beta i altres opcions que no s'haurien de tocar", "Both": "Ambdós", "Bundle security report": "Informe de seguretat de la col·lecció de paquets", "But here are other things you can do to learn about WingetUI even more:": "Però aquí hi ha altres coses que podeu fer per aprendre encara més sobre l'UniGetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Si es desactiva un administrador de paquets no es mostraran ni els seus paquets ni les seves actualitzacions", "Cache administrator rights and elevate installers by default": "Recorda els drets d'administrador i eleva els instal·ladors de manera predeterminada", "Cache administrator rights, but elevate installers only when required": "Recorda els drets d'administrador, però eleva només els instal·ladors que ho requereixin", "Cache was reset successfully!": "La memòria cau s'ha resetejat correctament", "Can't {0} {1}": "No hem pogut {0} el {1}", "Cancel": "Cancel·la", "Cancel all operations": "Cancel·la-ho tot", "Change backup output directory": "Canvia el directori de sortida de la còpia", "Change default options": "Canvia les opcions per defecte", "Change how UniGetUI checks and installs available updates for your packages": "Canviar com l'UniGetUI comprova i instal·la les actualizacions dels paquets", "Change how UniGetUI handles install, update and uninstall operations.": "Canvieu com l'UniGetUI instal·la, actualitza i desinstal·la els paquets.", "Change how UniGetUI installs packages, and checks and installs available updates": "Canvia com l'UniGetUI instal·la paquets, i comprova si hi ha actualitzacions", "Change how operations request administrator rights": "Canvieu com les operacions demanen drets d'administrador", "Change install location": "Canvia la ubicació d'instal·lació", "Change this": "<PERSON>via<PERSON> a<PERSON>", "Change this and unlock": "Canvia i desbloqueja", "Check for package updates periodically": "Cerca actualitzacions dels paquets periòdicament", "Check for updates": "Cerca actualitzacions", "Check for updates every:": "Cerca actualitzacions cada:", "Check for updates periodically": "Cerca actualitzacions de forma periòdica", "Check for updates regularly, and ask me what to do when updates are found.": "Cerca actualitzacions regularment, i pregunta'm què fer quan se'n trobin.", "Check for updates regularly, and automatically install available ones.": "Cerca actualitzacions regularment, i instal·la-les automàticament", "Check out my {0} and my {1}!": "<PERSON><PERSON>-li una ullada al meu {0} i al  meu {1}!", "Check out some WingetUI overviews": "Consulteu algunes guies generals sobre l'UniGetUI", "Checking for other running instances...": "Comprovant si hi ha altres instàncies en execució...", "Checking for updates...": "Cercant actualitzacions...", "Checking found instace(s)...": "Comprovant instàncies trobades...", "Choose how many operations shouls be performed in parallel": "Màxim d'operacions a executar en paral·lel", "Clear cache": "Neteja la memòria cau", "Clear finished operations": "Neteja les operacions finalitzades", "Clear selection": "Neteja la selecció", "Clear successful operations": "Neteja les operacions exitoses", "Clear successful operations from the operation list after a 5 second delay": "Treu de la llista les operacions exitoses automàticament cap de 5 segons", "Clear the local icon cache": "Neteja la memòria cau d'icones", "Clearing Scoop cache - WingetUI": "Netejant la memòria cau de l'Scoop - UniGetUI", "Clearing Scoop cache...": "Netejant la memòria cau de l'Scoop...", "Click here for more details": "Cliqueu per a més detalls", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Cliqueu a Instal·lar per a començar el procés d'instal·lació. Si no instal·leu la dependència, l'UniGetUI podria no funcionar bé.", "Close": "Tanca", "Close UniGetUI to the system tray": "Tanca l'UniGetUI a la safata del sistema", "Close WingetUI to the notification area": "Tanqueu l'UniGetUI a la safata del sistema", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "La còpia de seguretat al núvol utilitza un GitHub Gist privat per a desar-hi la llista de paquets que teniu instal·lats a l'ordinador", "Cloud package backup": "Còpia de seguretat al núvol", "Command-line Output": "Sortida de la línia de comandes", "Command-line to run:": "Línia de comandes a executar:", "Compare query against": "Compara la cerca amb", "Compatible with authentication": "Compatible amb l'autenticació", "Compatible with proxy": "Compatible amb el proxy", "Component Information": "Informació dels components", "Concurrency and execution": "Concurrència i execució", "Connect the internet using a custom proxy": "Connecta't a internet mitjaçant un proxy o servidor intermediari personalitzats", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Contribute to the icon and screenshot repository": "Contribueix al repositori d'icones i de captures de pantalla", "Contributors": "Contribuï<PERSON><PERSON>", "Copy": "Copia", "Copy to clipboard": "Copia al porta-retalls", "Could not add source": "No s'ha pogut afegir la font", "Could not add source {source} to {manager}": "No s'ha pogut afegir la font {source} al {manager}", "Could not back up packages to GitHub Gist: ": "No s'ha pogut completar la còpia al GitHub Gist", "Could not create bundle": "No s'ha pogut crear la col·lecció", "Could not load announcements - ": "No s'han pogut carregar els anuncis - ", "Could not load announcements - HTTP status code is $CODE": "No s'han pogut carregar els anuncis  - El codi d'estat HTTP és $CODE", "Could not remove source": "No s'ha pogut eliminar la font", "Could not remove source {source} from {manager}": "No s'ha pogut eliminar la font {source} del {manager}", "Could not remove {source} from {manager}": "No s'ha pogut eliminar la font {source} del {manager}", "Credentials": "Credencials", "Current Version": "<PERSON><PERSON><PERSON><PERSON>", "Current status: Not logged in": "Estat: no s'ha iniciat la sessió", "Current user": "<PERSON><PERSON><PERSON> actual", "Custom arguments:": "Paràmetres personalitzats:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Els arguments de línia d'ordres personalitzats poden canviar la manera com s'instal·len, s'actualitzen o es desinstal·len els programes, d'una manera que UniGetUI no pot controlar. L'ús de línies d'ordres personalitzades pot trencar els paquets. Procediu amb precaució.", "Custom command-line arguments:": "Paràmetres de línia de comandes personalitzats:", "Custom install arguments:": "Arguments d'instal·lació personalitzats", "Custom uninstall arguments:": "Arguments de desinstal·lació personalitzats", "Custom update arguments:": "Arguments d'actualització personalitzats", "Customize WingetUI - for hackers and advanced users only": "<PERSON><PERSON>eu l'UniGetUI: només per a hackers i usuaris avançats", "DEBUG BUILD": "COMPILACIÓ DE DEPURACIÓ", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ADVERTÈNCIA: NO ENS FEM RESPONSABLES DELS PAQUETS DESCARREGATS. ASSEGUREU-VOS D'INSTAL·LAR NOMÉS PAQUETS EN ELS QUALS CONFIEU.", "Dark": "Fosc", "Decline": "Dec<PERSON><PERSON>", "Default": "Per defecte", "Default installation options for {0} packages": "Opcions d'instal·lació per defecte pels paquets del {0}", "Default preferences - suitable for regular users": "Carrega les preferències predeterminades: adequades per als usuaris bàsics", "Default vcpkg triplet": "Triplet per defecte del vcpkg", "Delete?": "Eliminar?", "Dependencies:": "Dependències:", "Descendant": "Descendent", "Description:": "Des<PERSON>rip<PERSON>ó:", "Desktop shortcut created": "Drecera creada a l'escriptori", "Details of the report:": "Detalls de l'informe:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "El desenvolupament és difícil i aquesta aplicació és gratuïta. Però si t'ha agradat, sempre pots <b>comprar-me un cafè</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Instal·la directament en comptes de mostrar més informació sobre un paquet a la pestanya de \n{discoveryTab}", "Disable new share API (port 7058)": "Desactiva la nova API de compartició (port 7058)", "Disable the 1-minute timeout for package-related operations": "Desactiva el temps màxim d'1 minut per a les tasques de llistar paquets", "Disclaimer": "Exempció de responsabilitat", "Discover Packages": "Descobrir programari", "Discover packages": "<PERSON><PERSON><PERSON><PERSON> paquets", "Distinguish between\nuppercase and lowercase": "Distingueix entre\nmajúscules i minúscules", "Distinguish between uppercase and lowercase": "Distingeix entre majúscules i minúscules", "Do NOT check for updates": "NO comprovis si hi ha actualitzacions", "Do an interactive install for the selected packages": "Instal·leu de forma interactiva els paquets seleccionats", "Do an interactive uninstall for the selected packages": "Desinstal·leu de forma interactiva els paquets seleccionats", "Do an interactive update for the selected packages": "Actualitzeu de forma interactiva els paquets seleccionats", "Do not automatically install updates when the battery saver is on": "No instal·lar automàticament les actualitzacions quan l'estalviador de bateria estigui activat", "Do not automatically install updates when the network connection is metered": "No instal·lar automàticament les actualitzacions quan la connexió a internet sigui d'ús mesurat", "Do not download new app translations from GitHub automatically": "No descarreguis noves versions de les traduccions de GitHub automàticament", "Do not ignore updates for this package anymore": "No ignoreu més les actualitzacions d'aquest paquet", "Do not remove successful operations from the list automatically": "No esborris automàticament de la llista les operacions completades satisfactòriament", "Do not show this dialog again for {0}": "No mostris mai aquest diàleg per al {0}", "Do not update package indexes on launch": "No actualitzar els índexos de paquets en iniciar l'UniGetUI", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Accepteu que l'UniGetUI reculli i enviï dades d'ús anònimes, amb l'únic propòsit d'entendre i millorar l'experiència de l'ususari?", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Trobeu l'UniGetUI una eina útil? Si voleu, podeu recolzar el desenvolupament per a que pugui continuar fent del l'UniGetUI la interfície d'administració de paquets definitiva.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Heu trobat l'UniGetUI útil? Voleu recolzar el desenvolupador? Si voleu, podeu {0}, ajuda molt!", "Do you really want to reset this list? This action cannot be reverted.": "Realment voleu resetejar aquesta llista? Aquesta acció no es pot desfer.", "Do you really want to uninstall the following {0} packages?": "Voleu desinstal·lar els següents {0} paquets?", "Do you really want to uninstall {0} packages?": "Realment voleu desinstal·lar {0} paquets?", "Do you really want to uninstall {0}?": "Realment voleu desinstal·lar el {0}?", "Do you want to restart your computer now?": "Voleu reiniciar el vostre ordinador ara?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Voleu traduïr l'UniGetUI al vostre idioma? Veieu com <a style=\"color:{0}\" href=\"{1}\"a>AQUÍ!</a>\n", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "No voleu donar? No passa res, sempre podeu compartir l'UniGetUI amb els vostres amics! Doneu a conèixer l'UniGetUI!", "Donate": "Dona'm un cafè!", "Done!": "Fet!", "Download failed": "La descàrrega ha fallat", "Download installer": "Descarrega l'instal·lador", "Download operations are not affected by this setting": "Les operacions de descàrrega no es veuran afectades per aquesta opció", "Download selected installers": "Descarrega els instal·ladors seleccionats", "Download succeeded": "Descàrrega completada amb èxit", "Download updated language files from GitHub automatically": "Descarrega fitxers d'idioma actualitzats automàticament des del GitHub", "Downloading": "Descarregant", "Downloading backup...": "Descarregant la còpia...", "Downloading installer for {package}": "Descarregant l'instal·lador del {package}", "Downloading package metadata...": "Descarregant les metadates dels paquets...", "Enable Scoop cleanup on launch": "Activa la comanda scoop cleanup en iniciar", "Enable WingetUI notifications": "Activa les notificacions de l'UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Activa un solucionador [experimental] millorat per al WunGet", "Enable and disable package managers, change default install options, etc.": "Activa i desactiva administradors de paquets, opcions d'instal·lació per defecte, etc.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Activa les optimitzacions de CPU en rerefons (veieu Pull Request #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Activa la API de rerefons, (Widgets for UniGetUI i Compartició de paquets, port 7058)", "Enable it to install packages from {pm}.": "Activeu-lo per a instal·lar paquets del {pm}.", "Enable the automatic WinGet troubleshooter": "Activa el solucionador de problemes automàtic del WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Activa el nou elevador d'UAC amb el nom de l'UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Activa el nou controlador d'entrada dels processos (Tancador automàtic de l'STDIN)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Activeu les opcions de sota SI I NOMÉS SI enteneu què fan, i les implicacions i perills que poden comportar.", "Enable {pm}": "Activa el {pm}", "Enter proxy URL here": "Escriviu l'URL del proxy aquí", "Entries that show in RED will be IMPORTED.": "Les entrades que es mostren en VERMELL seran IMPORTADES", "Entries that show in YELLOW will be IGNORED.": "Les entrades mostrades en GROC seran IGNORADES", "Error": "Error", "Everything is up to date": "Tot està al dia", "Exact match": "Coincidència exacta", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Les dreceres existents al vostre escriptori s'escanejaran, i podreu escollir quines s'han de mantenir i quines s'han d'eliminar", "Expand version": "Mostra la versió", "Experimental settings and developer options": "Configuracions experimentals i altres opcions de desenvolupador", "Export": "Exporta", "Export log as a file": "Exporta el registre com a fitxer", "Export packages": "Exporta paquets", "Export selected packages to a file": "Exporta els paquets sel·leccionats en un fitxer", "Export settings to a local file": "Exporta les preferències a un fitxer", "Export to a file": "Exporta a un fitxer", "Failed": "Hi ha hagut un error", "Fetching available backups...": "Carregant les còpies disponibles...", "Fetching latest announcements, please wait...": "<PERSON><PERSON>t els anuncis, si us plau espereu...", "Filters": "Filtres", "Finish": "Acaba", "Follow system color scheme": "Segueix l'esquema de colors del sistema", "Follow the default options when installing, upgrading or uninstalling this package": "Segueix les opcions per defecte quan s'instal·li, s'actualitzi o es desinstal·li aquest paquet", "For security reasons, changing the executable file is disabled by default": "Per raons de seguretat, l'opció de canviar el fitxer executable està desactivada", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Per raons de seguretat, els arguments de línia de comandes personalitzats estan desactivats. Aneu a la configuració de seguretat de l'UniGetUI per a canviar això.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Per raons de seguretat, els scripts de pre-operació i post-operació estan desactivats per defecte. Aneu a la configuració de seguretat de l'UniGetUI per a canviar això.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Força la versió ARM del winget (NOMÉS PER A SISTEMES ARM64)", "Formerly known as WingetUI": "Abans conegut com el WingetUI", "Found": "Trobat", "Found packages: ": "Paquets trobats: ", "Found packages: {0}": "S'han trobat {0} paquets", "Found packages: {0}, not finished yet...": "S'han trobat {0} paquets, però encara no hem acabat...", "General preferences": "Preferències generals", "GitHub profile": "perfil a GitHub", "Global": "Global", "Go to UniGetUI security settings": "Configuració de seguretat de l'UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Repositori ple d'utilitats menys populars però immensament útils i d'altres paquets interessants.<br><PERSON>té: <b>Util<PERSON>ts, Programari de línia de comandes, Programari general (requereix el bucket extras)</b>", "Great! You are on the latest version.": "Fantàstic! Esteu a la darrera versió.", "Grid": "<PERSON><PERSON><PERSON>", "Help": "<PERSON><PERSON><PERSON>", "Help and documentation": "Ajuda i documentació", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Aquí podeu canviar el funcionament de l'UniGetUI pel que fa a les següents dreceres. Seleccionar una drecera farà que l'UniGetUI l'elimini si mai es crea durant una actualització. Desseleccionar-la farà que la drecera no s'elimini", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "<PERSON><PERSON>, em dic <PERSON>í, i soc el <i>desenvolupador</i> de l'UniGetUI. L'UniGetUI ha estat fet en estones lliures!", "Hide details": "Amaga els detalls", "Homepage": "Lloc web", "Hooray! No updates were found.": "Visca! No s'han trobat actualitzacions!", "How should installations that require administrator privileges be treated?": "Com s'han de tractar les instal·lacions que requereixin privilegis d'administrador?", "How to add packages to a bundle": "Com afegir paquets a la col·lecció", "I understand": "Ho entenc", "Icons": "Icones", "Id": "Id", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Si heu activat la còpia de seguretat al núvol, aquesta es desarà en un Gist de GitHub privat en aquest compte.", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Ignora les comandes de pre-instal·lació i post-instal·lació quan s'importin paquets des d'una col·lecció", "Ignore future updates for this package": "Ignora les actualitzacions futures d'aquest paquet", "Ignore packages from {pm} when showing a notification about updates": "Ignora els paquets del {pm} quan es notifiquin les actualitzacions disponibles", "Ignore selected packages": "Ignora els paquets seleccionats", "Ignore special characters": "Ignora els caràcters especials", "Ignore updates for the selected packages": "Ignora les actualitzacions dels paquets seleccionats", "Ignore updates for this package": "Ignora'n les actualitzacions", "Ignored updates": "Actualitzacions ignorades", "Ignored version": "<PERSON><PERSON><PERSON><PERSON>", "Import": "Importa", "Import packages": "<PERSON><PERSON><PERSON> paquets", "Import packages from a file": "Importa paquets des d'un fitxer", "Import settings from a local file": "Importa les preferències des d'un fitxer", "In order to add packages to a bundle, you will need to: ": "Per a afegir paquets a la col·lecció, haureu de: ", "Initializing WingetUI...": "Inicialitzant l'UniGetUI...", "Install": "Instal·la", "Install Scoop": "Instal·la l'Scoop", "Install and more": "Instal·la i més", "Install and update preferences": "Perferències d'instal·lació i actualització", "Install as administrator": "Instal·la com a administrador", "Install available updates automatically": "Instal·la de forma automàtica totes les actualitzacions disponibles", "Install location can't be changed for {0} packages": "La ubicació d'instal·lació no es pot canviar per els paquets del {0}", "Install location:": "Ubicació d'instal·lació:", "Install options": "Opcions d'instal·lació", "Install packages from a file": "Instal·la paquets des d'un fitxer", "Install prerelease versions of UniGetUI": "Instal·la versions de previsualització (no estables) de l'UniGetUI", "Install selected packages": "Instal·la els paquets sel·leccionats", "Install selected packages with administrator privileges": "Instal·la els paquets seleccionats amb drets d'administrador", "Install selection": "Instal·la la selecció", "Install the latest prerelease version": "Instal·la la darrera versió de prova", "Install updates automatically": "Instal·la les actualitzacions automàticament", "Install {0}": "Instal·la el {0}", "Installation canceled by the user!": "Instal·lació cancel·lada per l'usuari!", "Installation failed": "La instal·lació ha fallat", "Installation options": "Opcions d'instal·lació", "Installation scope:": "Entorn d'instal·lació:", "Installation succeeded": "La instal·lació s'ha completat amb èxit", "Installed Packages": "Programari instal·lat", "Installed Version": "Versió instal·lada", "Installed packages": "Paquets instal·lats", "Installer SHA256": "SHA256 de l'instal·lador", "Installer SHA512": "SHA512 de l'instal·lador", "Installer Type": "Tipus d'instal·lador", "Installer URL": "Enllaç de l'instal·lador", "Installer not available": "Instal·lador no disponible", "Instance {0} responded, quitting...": "La instància {0} ha respost, sortint...", "Instant search": "Cerca instantània", "Integrity checks can be disabled from the Experimental Settings": "Les comprovacions d'identitat es poden desactivar des de la configuració experimental", "Integrity checks skipped": "Comprovacions d'integritat omeses", "Integrity checks will not be performed during this operation": "No es farà cap comprovació d'integritat durant aquesta operació", "Interactive installation": "Instal·lació interactiva", "Interactive operation": "Operació interactiva", "Interactive uninstall": "Desinstal·lació interactiva", "Interactive update": "Actualització interactiva", "Internet connection settings": "Preferències de la connexió a internet", "Is this package missing the icon?": "A aquest paquet li falta la icona?", "Is your language missing or incomplete?": "Falta el vostre idioma o la traducció està incompleta?", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "No podem garantir que les credencials donades es desin de forma segura, així que millor que no poseu aquí la contrasenya del vostre banc", "It is recommended to restart UniGetUI after WinGet has been repaired": "Es recomana reiniciar l'UniGetUI un cop s'ha reparat el WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Es recomana fortament que reinstal·leu l'UniGetUI per a arreglar la situació.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Sembla que el WinGet no està funcionant correctament. Voleu intentar reparar-lo?", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Sembla que heu executat l'UniGetUI amb permisos d'administrador, fet que no es recomana. Podeu seguir utilitzant el programa, però recomanem no executar l'UniGetUI amb drets d'administrador. Cliqueu a \"{showDetails}\" per a veure el perquè.", "Language": "Idioma", "Language, theme and other miscellaneous preferences": "Idioma, tema i altres preferències miscel·lànies", "Last updated:": "Actualitzat per darrer cop:", "Latest": "<PERSON><PERSON>", "Latest Version": "<PERSON><PERSON> vers<PERSON>", "Latest Version:": "<PERSON><PERSON> versió:", "Latest details...": "Darrers detalls...", "Launching subprocess...": "Executant el subprocés...", "Leave empty for default": "Deixeu-ho buit per al valor predeterminat", "License": "Llicència", "Licenses": "Llicències", "Light": "<PERSON><PERSON>", "List": "Llista", "Live command-line output": "Sortida en viu de l'instal·lador", "Live output": "Sortida en viu", "Loading UI components...": "Carregant els components de la interfície...", "Loading WingetUI...": "Carregant l'UniGetUI...", "Loading packages": "Carregant paquets", "Loading packages, please wait...": "Carregant els paquets...", "Loading...": "Carregant...", "Local": "Local", "Local PC": "Aquest ordinador", "Local backup advanced options": "Opcions avançades de la còpia local", "Local machine": "Ordinador local", "Local package backup": "Còpia de seguretat local", "Locating {pm}...": "Cercant el {pm}...", "Log in": "Inicia la sessió", "Log in failed: ": "Ha fallat l'inici de sessió:", "Log in to enable cloud backup": "Inicieu la sessió per a activar la còpia de seguretat al núvol", "Log in with GitHub": "Inicieu la sessió amb el GitHub", "Log in with GitHub to enable cloud package backup.": "Inicieu la sessió amb el GitHub per a activar la còpia de seguretat al núvol", "Log level:": "Nivell del registre:", "Log out": "Tanca la sessió", "Log out failed: ": "Ha fallat el tancament de sessió:", "Log out from GitHub": "Tanca la sessió del GitHub", "Looking for packages...": "Cercant paquets...", "Machine | Global": "Màquina | Global", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Els arguments de línia d'ordres amb format incorrecte poden trencar els paquets o fins i tot permetre que un actor maliciós obtingui una execució privilegiada. Per tant, la importació d'arguments de línia d'ordres personalitzats està desactivada per defecte.", "Manage": "Administra", "Manage UniGetUI settings": "Administra la configuració de l'UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Administreu el comportament d'inici de l'UniGetUI des de la configuració de l'ordinador", "Manage ignored packages": "Administra els paquets ignorats", "Manage ignored updates": "Administra les actualitzacions ignorades", "Manage shortcuts": "Administrar les dreceres", "Manage telemetry settings": "Administra la configuració de la telemetria", "Manage {0} sources": "Administra les fonts del {0}", "Manifest": "Manifest", "Manifests": "Manifests", "Manual scan": "Escaneig manual", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "L'administrador de paquets de Microsoft. Ple de programari conegut i verificat.<br><PERSON><PERSON>: <b>Programari general, Aplicacions de la Microsoft Store</b>", "Missing dependency": "Fa falta una dependència", "More": "Més", "More details": "<PERSON><PERSON>", "More details about the shared data and how it will be processed": "Més detalls sobre com es processen les dades recollides", "More info": "<PERSON><PERSON>", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "NOTA: Aquest solucionador de problemes es pot desactivar des de la configuració de l'UniGetUI, a la secció del WinGet", "Name": "Nom", "New": "Nou", "New Version": "Nova versió", "New bundle": "Col·lecció nova", "New version": "Nova versió", "Nice! Backups will be uploaded to a private gist on your account": "Fantàstic! Les còpies de seguretat es carregaran en un Gist privat al vostre compte", "No": "No", "No applicable installer was found for the package {0}": "No s'ha trobat cap instal·lador aplicable per al paquet {0}", "No dependencies specified": "No s'han especificat dependències", "No new shortcuts were found during the scan.": "No s'han trobat noves dreceres durant l'escaneig.", "No packages found": "No s'han trobat paquets", "No packages found matching the input criteria": "No s'han trobat paquets amb els criteris actuals", "No packages have been added yet": "Encara no s'han afegit paquets", "No packages selected": "Cap paquet seleccionat", "No packages were found": "No s'han trobat paquets", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "No es recull ni s'envia informació personal, i la informació recollida s'anonimitza, de forma que no es pot relacionar amb tu.", "No results were found matching the input criteria": "No s'han trobat resultats que compleixin amb els filtres establerts", "No sources found": "No s'han trobat fonts", "No sources were found": "No s'han trobat fonts", "No updates are available": "No hi ha actualitzacions disponibles", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "L'administrador de paquets del Node JS. Ple de llibreries i d'altres utilitats que orbiten al voltant del mon de Javascript.<br>Conté: <b>Llibreries de Node i altres utilitats relacionades</b>", "Not available": "No disponible", "Not finding the file you are looking for? Make sure it has been added to path.": "No trobes el que busques? Assegura't que el fitxer està afegit al camí (PATH)", "Not found": "No s'ha trobat", "Not right now": "Ara no", "Notes:": "Notes:", "Notification preferences": "Preferències de les notificacions", "Notification tray options": "Opcions de la safata del sistema", "Notification types": "Tipus de notificacions", "NuPkg (zipped manifest)": "NuPkg (manifest comprimit)", "OK": "D'acord", "Ok": "D'acord", "Open": "Obre", "Open GitHub": "Obre el GitHub", "Open UniGetUI": "Obre l'UniGetUI", "Open UniGetUI security settings": "Obre la configuració de seguretat de l'UniGetUI", "Open WingetUI": "Obre l'UniGetUI", "Open backup location": "Obre la ubicació de la còpia de seguretat", "Open existing bundle": "Obre una col·lecció existent", "Open install location": "Obre la ubicació d'instal·lació", "Open the welcome wizard": "Obre l'assistent de benvinguda", "Operation canceled by user": "Operació cancel·lada per l'usuari", "Operation cancelled": "Operació cancel·lada", "Operation history": "Historial d'operacions", "Operation in progress": "Operacions en progrés", "Operation on queue (position {0})...": "Operació a la cua (posició {0})...", "Operation profile:": "<PERSON><PERSON><PERSON> d'operació:", "Options saved": "Opcions desades", "Order by:": "Ordena per:", "Other": "Una altra", "Other settings": "Altres configuracions", "Package": "<PERSON><PERSON>", "Package Bundles": "Col·leccions de paquets", "Package ID": "Identificador del paquet", "Package Manager": "Administrador <PERSON>", "Package Manager logs": "Registre dels administradors de paquets", "Package Managers": "Admin<PERSON>", "Package Name": "Nom del paquet", "Package backup": "Còpia de seguretat dels paquets", "Package backup settings": "Configuració de la còpia de seguretat", "Package bundle": "Col·lecció de <PERSON>", "Package details": "Detalls del paquet", "Package lists": "<PERSON><PERSON><PERSON>", "Package management made easy": "L'administració de paquets, feta fàcil", "Package manager": "Administrador <PERSON>", "Package manager preferences": "Preferències dels administradors de paquets", "Package managers": "Admin. de p<PERSON>", "Package not found": "No s'ha trobat el paquet", "Package operation preferences": "Preferències de les operacions dels paquets", "Package update preferences": "Preferències d'actualització dels paquets", "Package {name} from {manager}": "Paquet {name} del {manager}", "Package's default": "Predeterminat del paquet", "Packages": "Paquets", "Packages found: {0}": "Paquets trobats: {0}", "Partially": "Parcialment", "Password": "Contrasenya", "Paste a valid URL to the database": "Enganxeu un enllaç vàlid a la base de dades", "Pause updates for": "Atura les actualitzacions durant", "Perform a backup now": "Fes una còpia ara", "Perform a cloud backup now": "Fes una còpia al núvol ara", "Perform a local backup now": "Fes una còpia local ara", "Perform integrity checks at startup": "Fes una comprovació d'identitat a l'inici", "Performing backup, please wait...": "Desant la còpia de seguretat, si us plau espereu...", "Periodically perform a backup of the installed packages": "Fes una còpia de seguretat dels paquets periòdicament", "Periodically perform a cloud backup of the installed packages": "Fes una còpia al núvol periòdica dels paquets instal·lats  ", "Periodically perform a local backup of the installed packages": "Fes una còpia local periòdica dels paquets instal·lats", "Please check the installation options for this package and try again": "Comproveu les opcions d'instal·lació d'aquest paquet i proveu-ho de nou", "Please click on \"Continue\" to continue": "C<PERSON><PERSON> \"Continuar\" per a continuar", "Please enter at least 3 characters": "Escriviu com a mínim 3 caràcters", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Tingueu en compte que alguns paquets poden no ser instal·lables, a causa dels gestors de paquets habilitats en aquest ordinador.", "Please note that not all package managers may fully support this feature": "Nota: alguns administradors de paquets poden no ser del tot compatibles amb aquesta característica", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Tingueu en compte que és possible que els paquets de determinades fonts no es puguin exportar. S'han marcat en gris i no s'exportaran.", "Please run UniGetUI as a regular user and try again.": "Executeu l'UniGetUI com a usuari estàndard i proveu-ho de nou", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Veieu la Sortida de la línia de comandes o l'historial d'operacions per a més detalls sobre l'error.", "Please select how you want to configure WingetUI": "Selecioneu com voleu configurar l'UniGetUI", "Please try again later": "Proveu-ho més tard", "Please type at least two characters": "Escriviu com a mínim dos caràcters", "Please wait": "Si us plau, espereu", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Si us plau, espereu mentre el {0} s'instal·la. Pot aparèixer una finestra negra (o blava). Espereu a que es tanqui.", "Please wait...": "Si us plau espereu...", "Portable": "<PERSON><PERSON><PERSON>", "Portable mode": "Mode portàtil", "Post-install command:": "Comanda de post-instal·lació:", "Post-uninstall command:": "Comanda de post-desinstal·lació:", "Post-update command:": "Comanda de post-actualització:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "L'administrador de paquets del PowerShell. Trobeu llibreries i scripts que us permetran expandir les capacitats del PowerShell<br><PERSON><PERSON>: <b><PERSON><PERSON><PERSON><PERSON>, scripts i Cmdlets</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Les ordres prèvies i posteriors a la instal·lació poden fer coses molt desagradables al vostre dispositiu, si estan dissenyades per fer-ho. Pot ser molt perillós importar les ordres des d'un paquet, tret que confieu en la font d'aquest paquet.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Les ordres prèvies i posteriors a la instal·lació s'executaran abans i després que s'instal·li, s'actualitzi o s'instal·li un paquet. Tingueu en compte que poden trencar coses si no s'utilitzen amb cura.", "Pre-install command:": "Comanda de pre-instal·lació:", "Pre-uninstall command:": "Comanda de pre-desinstal·lació:", "Pre-update command:": "Comanda de pre-actualització:", "PreRelease": "PreLlançament", "Preparing packages, please wait...": "Preparant els paque<PERSON>, si us plau espereu...", "Proceed at your own risk.": "Continueu sota la vostra responsabilitat", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Prohibeix qualsevol tipus d'elevació mitjançant l'UniGetUI Elevator o el GSudo ", "Proxy URL": "URL del proxy", "Proxy compatibility table": "Taula de compatibilitat amb Proxy", "Proxy settings": "Configuració del Proxy", "Proxy settings, etc.": "Configuraci<PERSON> del Proxy, etc.", "Publication date:": "Data de publicació:", "Publisher": "Publicador", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "L'administrador de llibreries de Python. Ple de llibreries i d'altres utilitats relacionades.<br><PERSON>té: <b>Llibreries de Python i altres utilitats</b>", "Quit": "Tanca", "Quit WingetUI": "Tanca l'UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Redueix els missatges d'UAC, eleva instal·lacions per defecte, desbloqueja característiques perilloses de l'UniGetUI, etc.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Veieu el registre de l'UniGetUI per a obtenir més detalls sobre el(s) fitxer(s) afectat(s)", "Reinstall": "Reinstal·la", "Reinstall package": "Reinstal·la el paquet", "Related settings": "Configuració relacionada", "Release notes": "Notes de publicació", "Release notes URL": "Enllaç de les notes de publicació", "Release notes URL:": "Enllaç a les notes de publicació:", "Release notes:": "Notes de publicació:", "Reload": "Recarrega", "Reload log": "Recarrega el registre", "Removal failed": "Eliminació fallida", "Removal succeeded": "L'eliminació s'ha completat amb èxit", "Remove from list": "<PERSON><PERSON>u de la llista", "Remove permanent data": "Esborra les dades permanents", "Remove selection from bundle": "Treu la selecció de la col·lecció", "Remove successful installs/uninstalls/updates from the installation list": "Amaga les instal·lacions satisfactòries de la llista d'instal·lacions", "Removing source {source}": "Eliminant la font {source}", "Removing source {source} from {manager}": "Eliminant la font {source} del {manager}", "Repair UniGetUI": "Repara l'UniGetUI", "Repair WinGet": "Repara el WinGet", "Report an issue or submit a feature request": "Informeu d'un problema o suggeriu una característica nova", "Repository": "Repositori", "Reset": "<PERSON><PERSON><PERSON><PERSON>", "Reset Scoop's global app cache": "Reseteja la memòria cau de les aplicacions globals de l'Scoop", "Reset UniGetUI": "Reseteja l'UniGetUI", "Reset WinGet": "Reseteja el WinGet", "Reset Winget sources (might help if no packages are listed)": "Reseteja les fonts del Winget (pot ajudar si no es mostra cap paquet)", "Reset WingetUI": "Reseteja l'UniGetUI", "Reset WingetUI and its preferences": "Reseteja l'UniGetUI i les seves preferències", "Reset WingetUI icon and screenshot cache": "Reseteja la memòria cau d'icones de l'UniGetUI", "Reset list": "Reseteja la llista", "Resetting Winget sources - WingetUI": "Resetejant les fonts del WinGet - UniGetUI", "Restart": "Reinicia", "Restart UniGetUI": "Reinicia l'UniGetUI", "Restart WingetUI": "Reinicia l'UniGetUI", "Restart WingetUI to fully apply changes": "Cal reiniciar l'UniGetUI per a aplicar els canvis", "Restart later": "Reincia més tard", "Restart now": "Reinicia ara", "Restart required": "Re<PERSON>ci requerit", "Restart your PC to finish installation": "Reinicieu el vostre ordinador per a finalitzar la instal·lació", "Restart your computer to finish the installation": "Reinicieu el vostre ordinador per a acabar la instal·lació", "Restore a backup from the cloud": "Restaura una còpia de seguretat del núvol", "Restrictions on package managers": "Restriccions als administradors de paquets", "Restrictions on package operations": "Restriccions a les operacions amb paquets", "Restrictions when importing package bundles": "Restriccions a l'importar col·leccions de paquets", "Retry": "Reintentar", "Retry as administrator": "<PERSON>na a provar-ho com a administrador", "Retry failed operations": "Torna a provar les operacions que han fallat", "Retry interactively": "Torna a provar-ho de forma interactiva", "Retry skipping integrity checks": "Torna a provar-ho sense comprovacions d'integritat", "Retrying, please wait...": "Reintentant, si us plau espereu...", "Return to top": "Retorna a dalt", "Run": "Executa", "Run as admin": "Executa com a administrador", "Run cleanup and clear cache": "Executa la neteja i buida la memòria cau", "Run last": "Executa la darrera", "Run next": "Executa a continuació", "Run now": "Executa ara", "Running the installer...": "Executant l'instal·lador...", "Running the uninstaller...": "Executant el desinstal·lador...", "Running the updater...": "Executant l'actualitzador...", "Save": "<PERSON><PERSON>", "Save File": "Desa el fitxer", "Save and close": "Desa i tanca", "Save as": "Desa com", "Save bundle as": "Desa la col·lecció com", "Save now": "<PERSON><PERSON>", "Saving packages, please wait...": "Desant els paque<PERSON>, espereu si us plau...", "Scoop Installer - WingetUI": "Instal·lador de l'Scoop - UniGetUI", "Scoop Uninstaller - WingetUI": "Desinstal·lador de l'Scoop - UniGetUI", "Scoop package": "<PERSON>quet <PERSON> l'Scoop", "Search": "Cerca", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Cerca programari d'escriptori, avisa'm quan hi hagi actualitzacions disponibles i no facis coses massa tècniques. No vull que l'UniGetUI es compliqui massa, només vull una <b>botiga de programari</b> senzilla.", "Search for packages": "<PERSON><PERSON><PERSON> paquets", "Search for packages to start": "<PERSON><PERSON><PERSON> paquets per a començar", "Search mode": "Mode de cerca", "Search on available updates": "Cerqueu a les actualitzacions trobades", "Search on your software": "Cercqueu al vostre programari", "Searching for installed packages...": "Cerqueu al programari instal·lat...", "Searching for packages...": "Cercant paquets...", "Searching for updates...": "Cercant actualitzacions...", "Select": "Sel·lecciona", "Select \"{item}\" to add your custom bucket": "Seleccioneu \"{item}\" per a afegir un bucket personalitzat", "Select a folder": "Sel·leccioneu una carpeta", "Select all": "Selecciona-ho tot", "Select all packages": "Selecciona tots els paquets", "Select backup": "Seleccioneu una còpia", "Select only <b>if you know what you are doing</b>.": "Seleccioneu només <b>si sabeu el que esteu fent</b>.", "Select package file": "Selecciona el fitxer de paquets", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Seleccioneu la còpia que voleu obrir. Més endavant podreu revisar quins paquets/programes voleu restaurar.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Seleccioneu els processos que s'haurien de tancar abans que aquest paquet s'instal·li, s'actualitzi o es desinstal·li.", "Select the source you want to add:": "Seleccioneu la font a afegir:", "Select upgradable packages by default": "Selecciona els paquets actualitzables per defecte", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Seleccioneu quins <b>gestors de paquets</b> voleu utilitz<PERSON> ({0}), configureu com s'instal·laran els paquets, gestioneu com funcionaran els drets d'administrador, etc.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "S'ha enviat el handshake. Esperant resposta de la instància... ({0}%)", "Set a custom backup file name": "Nom del fitxer de la còpia de seguretat", "Set custom backup file name": "Canvia el nom del fitxer de sortida", "Settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Share": "Comparteix", "Share WingetUI": "Compartiu l'UniGetUI", "Share anonymous usage data": "Comparteix dades d'ús anònimes", "Share this package": "Comparteix aquest paquet", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Si modifiqueu alguna de les opcions de seguretat haureu de tornar a obrir la col·lecció per a que els canvis facin efecte.", "Show UniGetUI on the system tray": "Mostra l'UniGetUI a la safata del sistema", "Show UniGetUI's version and build number on the titlebar.": "Mostra la versió de l'UniGetUI a la barra de títol", "Show WingetUI": "Mostra l'UniGetUI", "Show a notification when an installation fails": "Mostra una notificació quan una instal·lació falli", "Show a notification when an installation finishes successfully": "Mostra una notificació quan una instal·lació es completi satisfactòriament", "Show a notification when an operation fails": "Mostra una notificació quan una operació falli", "Show a notification when an operation finishes successfully": "Mostra una notificació quan una operació acabi satisfactòriament", "Show a notification when there are available updates": "Mostra una notificació quan s'hagin trobat actualitzacions", "Show a silent notification when an operation is running": "Mostra una notificació silenciosa quan s'estigui executant una operació", "Show details": "Mostra els detalls", "Show in explorer": "Mostra a l'explorador", "Show info about the package on the Updates tab": "Mostra informació sobre el paquet a la secció d'actualitzacions en ser clicat dos cops", "Show missing translation strings": "Mostra el text que falta per a ser traduït", "Show notifications on different events": "Mostra notificacions en diferents situacions", "Show package details": "Mostra els detalls del paquet", "Show package icons on package lists": "Mostra les icones dels paquets a la llista dels paquets", "Show similar packages": "Mostra paquets similars", "Show the live output": "Mostra la sortida en viu", "Size": "Mida", "Skip": "Salta", "Skip hash check": "No comprovis el hash", "Skip hash checks": "Ignora les verificacions de hash", "Skip integrity checks": "Salta les comprovacions d'integritat", "Skip minor updates for this package": "Salta't les actualitzacions menors d'aquest paquet", "Skip the hash check when installing the selected packages": "Instal·la els paquets seleccionats sense comprovar-ne la integritat", "Skip the hash check when updating the selected packages": "Actualitza els paquets seleccionats sense comprovar-ne la integritat", "Skip this version": "Salta aquesta versió", "Software Updates": "Actualitzacions", "Something went wrong": "Alguna cosa no ha anat bé", "Something went wrong while launching the updater.": "Quelcom ha anat malament en executar l'actualitzador.", "Source": "Origen", "Source URL:": "<PERSON><PERSON><PERSON> font:", "Source added successfully": "S'ha afegit la font correctament", "Source addition failed": "No s'ha pogut afegir la font", "Source name:": "Nom de la font:", "Source removal failed": "No s'ha pogut eliminar la font", "Source removed successfully": "S'ha eliminat la font correctament", "Source:": "Font:", "Sources": "Fonts", "Start": "Comença", "Starting daemons...": "Iniciant fils de rerefons...", "Starting operation...": "Començant operació...", "Startup options": "Opcions d'inici", "Status": "Estat", "Stuck here? Skip initialization": "Encallat? Salta aquest tros", "Suport the developer": "Suporta el desenvolupador", "Support me": "Recolza'm", "Support the developer": "Recolza al desenvolupador", "Systems are now ready to go!": "Tot és a punt!", "Telemetry": "Telemetria", "Text": "Text", "Text file": "Fitxer de text", "Thank you ❤": "Gràcies ❤", "Thank you 😉": "Gràcies 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "L'administrador de paquets del Rust.<br><PERSON><PERSON>: <b>Llibreries i binaris escrits en rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "La còpia no inclourà cap tipus de fitxers binaris o dades de cap programa.", "The backup will be performed after login.": "La còpia es realitzarà després de l'inici de sessió", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "La còpia inclourà una llista completa dels paquets instal·lats i de les seves respectives opcions d'instal·lació. Les actualitzacions ignorades i les versions saltades també es desaran.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "La col·lecció que esteu intentant obrir sembla invàlida. Comproveu el fitxer i torneu-ho a provar.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "El hash de l'instal·lador no coincideix amb el valor esperat, pel que no es pot vericifivar l'autenticitat de l'instal·lador. Si realment confieu en el publicador, {0} el paquet un altre cop ometent la compovació del hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "L'administrador de paquets clàssic del Windows. Hi trobareu de tot. <br><PERSON><PERSON>: <b>Programari general</b>", "The cloud backup completed successfully.": "La còpia de seguretat s'ha completat correctament.", "The cloud backup has been loaded successfully.": "La còpia de seguretat s'ha carregat correctament", "The current bundle has no packages. Add some packages to get started": "La col·lecció actual no té paquets. Afegiu-ne per a començar", "The executable file for {0} was not found": "El fitxer executable del {0} no s'ha trobat", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Les opcions següents s'aplicaran per defecte cada cop que un paquet del {0} s'instal·li, s'acualitzi o es desinstal·li.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "<PERSON>s paquets següents s'exportaran a un fitxer JSON. No es desaran ni dades d'usuari ni fitxers executables.", "The following packages are going to be installed on your system.": "<PERSON><PERSON> paquets següents s'instal·laran al vostre sistema.", "The following settings may pose a security risk, hence they are disabled by default.": "Les opcions següents poden representar un risc de seguretat, raó per la qual s'han desactivat per seguretat", "The following settings will be applied each time this package is installed, updated or removed.": "Les següents opcions s'aplicaran cada cop que aquest paquet s'instal·li, s'actualitzi o es desinstal·li.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Les configuracions següents s'aplicaran cada cop que aquest paquet s'instal·li, s'actualitzi o s'elimini; i es desaran automàticament", "The icons and screenshots are maintained by users like you!": "Les icones i captures de pantalla estan mantingudes per usuaris com tu!", "The installer authenticity could not be verified.": "No s'ha pogut verificar l'autenticitat de l'instal·lador.", "The installer has an invalid checksum": "L'instal·lador té un hash invàlid", "The installer hash does not match the expected value.": "El hash de l'instal·lador no es correspon amb el valor esperat", "The local icon cache currently takes {0} MB": "La memòria cau d'icones actualment ocupa {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "L'objectiu principal d'aquesta aplicació és de proveir a l'usuari d'una forma ràpida d'administrar el programari disponible als administradors de paquets més comuns per al Windows, com per exemple el Winget o l'Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "No s'ha trobat el paquet \"{0}\" a l'administrador de paquets \"{1}\"", "The package bundle could not be created due to an error.": "No s'ha pogut crear la col·lecció a causa d'un error.", "The package bundle is not valid": "La col·lecció de paquets no és vàlida", "The package manager \"{0}\" is disabled": "L'administrador de paquets \"{0}\" està desactivat", "The package manager \"{0}\" was not found": "No s'ha trobat l'administrador de paquets \"{0}\"", "The package {0} from {1} was not found.": "No s'ha trobat el paquet {0} de {1}.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Els paquets mostrats aquí no es tindran en compte quan es comprovi si hi ha actualitzacions disponibles. Cliqueu-los dos cops o premeu el botó de la seva dreta per a deixar d'ignorar-ne les actualitzacions.", "The selected packages have been blacklisted": "Les actualitzacions dels paquets seleccionats s'ignoraran a partir d'ara", "The settings will list, in their descriptions, the potential security issues they may have.": "Les opcions mostraran, en les seves descripcions, els perills potencials que pot representar activar-les.", "The size of the backup is estimated to be less than 1MB.": "La mida estimada de la còpia serà de menys d'1 MB.", "The source {source} was added to {manager} successfully": "La font {source} s'ha afegit al {manager} correctament", "The source {source} was removed from {manager} successfully": "La font {source} s'ha eliminat del {manager} correctament", "The system tray icon must be enabled in order for notifications to work": "L'icona de la safata del sistema ha d'estar activada per a que funcionin les notificacions", "The update process has been aborted.": "S'ha avortat l'actualització", "The update process will start after closing UniGetUI": "El procés d'actualització començarà en tancar l'UniGetUI", "The update will be installed upon closing WingetUI": "L'actualització s'instal·larà en tancar l'UniGetUI", "The update will not continue.": "L'actualització no continuarà.", "The user has canceled {0}, that was a requirement for {1} to be run": "L'usuari ha cancel·lat la {0}, que era un requeriment per a que s'executés la {1}", "There are no new UniGetUI versions to be installed": "No hi ha noves versions de l'UniGetUI disponibles", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Hi ha operacions en execució. Tancar l'UniGetUI pot provocar que fallin o es cancel·lin. Voleu continuar?", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Hi ha vídeos fantàstics a YouTube que mostren l'UniGetUI i les seves capacitats. Podríeu aprendre trucs i consells útils!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Hi ha dues raons principals per a no executar el l'UniGetUI com a administrador:\n La primera és que l'Scoop pot causar problemes si s'executa com a administrador\n La segona és que executant l'UniGetUI amb drets d'administrador vol dir que qualsevol paquet que s'instal·li i/o actualitzi a través de l'UniGetUI s'executarà amb drets d'administrador automàticament (i això no és segur).\nRecordeu que sempre podeu clicar amb un clic dret a un programa -> Instal·la/Actualitza/Desinstal·la com a administrador.\n", "There is an error with the configuration of the package manager \"{0}\"": "Hi ha un error a la configuració de l'administrador de paquets \"{0}\"", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Hi ha una instal·lació en curs. Si tanqueu l'UniGetUI, aquesta podria fallar i provocar resultats inesperats. Segur que voleu continuar?", "They are the programs in charge of installing, updating and removing packages.": "Són els programes encarregats d'instal·lar, actualitzar i eliminar paquets.", "Third-party licenses": "Llicències de tercers", "This could represent a <b>security risk</b>.": "<PERSON><PERSON>ò podria representar un <b>risc de seguretat</b>.", "This is not recommended.": "Això no es recomana.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Això probablement és degut al fet que el paquet que se us ha enviat s'ha eliminat o està publicat en un gestor de paquets que no teniu habilitat. L'identificador rebut és {0}", "This is the <b>default choice</b>.": "Aquesta és l'<b>opció predeterminada</b>.", "This may help if WinGet packages are not shown": "Això pot ajudar si no es mostren els paquets del WinGet", "This may help if no packages are listed": "Això pot ajudar si no es mostren paquetsa les llistes", "This may take a minute or two": "Això pot trigar un parell de minuts", "This operation is running interactively.": "Aquesta operació s'està executant de forma interactiva.", "This operation is running with administrator privileges.": "Aquesta operació s'està executant amb privilegis d'administrador.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Aquesta opció causarà problemes. Qualsevol operació que no es pugui autoelevar FALLARÀ. Les opcions instal·la/actualitza/desinstal·la com a administrador NO FUNCIONARAN.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Aquesta col·lecció de paquets té algunes configuracions que són potencialment perilloses, i és possible que aquestes siguin ignorades.", "This package can be updated": "<PERSON>s pot actualitzar aquest paquet", "This package can be updated to version {0}": "Aquest paquet es pot actualitzar a la versió {0}", "This package can be upgraded to version {0}": "Aquest paquet es pot actualitzar a la versió {0}", "This package cannot be installed from an elevated context.": "Aquest paquet no es pot instal·lar des d'un context d'Administrador", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Aquest paquet no té icona o li falten imatges? Contribuïu a l'UniGetUI afegint la icona i imatges que faltin a la base de dades pública i oberta de l'UniGetUI.", "This package is already installed": "Aquest paquet ja està instal·lat", "This package is being processed": "S'està processant aquest paquet", "This package is not available": "Aquest paquet no està disponible", "This package is on the queue": "Aquest paquet està a la cua", "This process is running with administrator privileges": "Aquest procés s'està executant amb privilegs d'administrador", "This project has no connection with the official {0} project — it's completely unofficial.": "Aquest projecte no té cap connexió amb el {0} — és completament no oficial.", "This setting is disabled": "Aquesta configuració està desactivada", "This wizard will help you configure and customize WingetUI!": "Aquest assistent us ajudarà a configurar i personalitzar l'UniGetUI", "Toggle search filters pane": "Mostra/Amaga el panell de filtres de cerca", "Translators": "Traductors", "Try to kill the processes that refuse to close when requested to": "Intenta matar els processos que refusen tancar-se quan se'ls demani.", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Activar això permetrà canviar el fitxer executable a través del qual l'UniGetUI interactua i es comunica amb els administradors de paquets. Mentre que això permet modificar millor els processos d'instal·lació, també pot resultar perillós.", "Type here the name and the URL of the source you want to add, separed by a space.": "Escriviu aquí el nom i l'enllaç de la font que voleu afegir, separats per un espai.", "Unable to find package": "No hem pogut trobar el paquet", "Unable to load informarion": "No es pot carregar la informació", "UniGetUI collects anonymous usage data in order to improve the user experience.": "L'UniGetUI recull dades d'ús anònimes amb la finalitat de millorar l'experiència de l'usuari.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "L'UniGetUI recull dades d'ús anònimes amb la única finalitat d'entendre i millorar l'experiència de l'usuari.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "L'UniGetUI ha detectat que s'ha creat 1 nova drecera a l'escriptori que es pot eliminar automàticament.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "L'UniGetUI ha detectat les següents dreceres a l'escriptori que es poden eliminar automàticament durant les actualitzacions", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "L'UniGetUI ha detectat que s'han creat {0} noves dreceres a l'escriptori que es poden eliminar automàticament.", "UniGetUI is being updated...": "Estem actualitzant l'UniGetUI...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "L'UniGetUI no està relacionat amb els administradors de paquets compatibles. L'UniGetUI és un projecte independent.", "UniGetUI on the background and system tray": "L'UniGetUI al rerefons i safata del sistema", "UniGetUI or some of its components are missing or corrupt.": "L'UniGetUI o algun dels seus components falta o s'ha corromput.", "UniGetUI requires {0} to operate, but it was not found on your system.": "L'UniGetUI necessita el {0} per a funcionar, però aquest no ha estat trobat al sistema.", "UniGetUI startup page:": "Pàgina d'inici de l'UniGetUI", "UniGetUI updater": "Actualitzador de l'UniGetUI", "UniGetUI version {0} is being downloaded.": "Estem descarregant l'UniGetUI versió {0}", "UniGetUI {0} is ready to be installed.": "L'UniGetUI {0} està llest per a ser instal·lat", "Uninstall": "Desinstal·la", "Uninstall Scoop (and its packages)": "Desinstal·la l'Scoop (i les seves aplicacions)", "Uninstall and more": "Desinstal·la i més", "Uninstall and remove data": "Desinstal·la i elimina'n les dades", "Uninstall as administrator": "Desinstal·la com a administrador", "Uninstall canceled by the user!": "Desinstal·lació cancel·lada per l'usuari!", "Uninstall failed": "La desinstal·lació ha fallat", "Uninstall options": "Opcions de desinstal·lació", "Uninstall package": "Desinstal·la el paquet", "Uninstall package, then reinstall it": "Desinstal·la el paquet, després reinstal·la'l", "Uninstall package, then update it": "Desinstal·la el paquet, després actualitza'l", "Uninstall previous versions when updated": "Desinstal·la les versions anteriors en actualitzar", "Uninstall selected packages": "Desinstal·la els paquets seleccionats", "Uninstall selection": "Desinstal·la la selecció", "Uninstall succeeded": "La desinstal·lació s'ha completat correctament", "Uninstall the selected packages with administrator privileges": "Desinatal·la els paquets seleccionats amb drets d'administrador", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Els paquets desinstal·lables que provenen de la font \"{0}\" no estan publicats a cap administrador de paquets, i, per tant, no tenen informació publicada disponible.", "Unknown": "Desconegut", "Unknown size": "Mida desconeguda", "Unset or unknown": "No especificada o desconeguda", "Up to date": "Al dia", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Actualitza l'UniGetUI automàticament", "Update all": "Actualitz<PERSON>-ho tot", "Update and more": "Actualitza i més", "Update as administrator": "Actualitza com a administrador", "Update check frequency, automatically install updates, etc.": "Frequència de comprovació de les actualitzacions, instal·la automàticament les actualitzacions, etc.", "Update date": "Data d'actualització", "Update failed": "L'actualització ha fallat", "Update found!": "Actualització disponible!", "Update now": "Actualitza ara", "Update options": "Opcions d'actualització", "Update package indexes on launch": "Actualitza els índexos dels paquets en carregar l'UniGetUI", "Update packages automatically": "Actualitza els paquets automàticament", "Update selected packages": "Actualitza els paquets sel·leccionats", "Update selected packages with administrator privileges": "Actualitza els paquets seleccionats amb drets d'administrador", "Update selection": "Actualitza la selecció", "Update succeeded": "L'actualització s'ha completat correctament", "Update to version {0}": "Actualitza a la versió {0}", "Update to {0} available": "Actualització a {0} disponible", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Actualitza els portfiles de git del vcpkg automàticament (requereix el GIT)", "Updates": "Actualitzacions", "Updates available!": "Actualitzacions disponibles!", "Updates for this package are ignored": "S'ignoren les actualitzacions d'aquest paquet", "Updates found!": "S'han trobat actualitzacions!", "Updates preferences": "Preferències de les actualitzacions", "Updating WingetUI": "Actualitzant l'UniGetUI", "Url": "<PERSON><PERSON><PERSON>", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Utilitza el WinGet incorporat (llegat) en comptes dels CMDLets de PowerShell", "Use a custom icon and screenshot database URL": "Utilitza una base de dades d'icones i captures de pantalla personalitzades", "Use bundled WinGet instead of PowerShell CMDlets": "Utilitza el WinGet empaquetat en comptes dels CMDlets del PowerShell ", "Use bundled WinGet instead of system WinGet": "Utilitza el WinGet inclòs a l'UniGetUI en comptes del WinGet del sistema", "Use installed GSudo instead of UniGetUI Elevator": "Utilitza el GSudo instal·lat en comptes de l'UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Utilitza el GSudo present al sistema en comptes del que inclou l'aplicació", "Use system Chocolatey": "Utilitza el Chocolatey del sistema", "Use system Chocolatey (Needs a restart)": "Utilitza el Chocolatey del sistema (Requereix un reinici)", "Use system Winget (Needs a restart)": "Utilitza el Winget del sistema (Requereix un reinici)", "Use system Winget (System language must be set to english)": "Utilitza el Winget del sistema (L'idioma del dispositiu ha de ser l'anglès)", "Use the WinGet COM API to fetch packages": "Utilitza la API COM del WinGet per a carregar els paquets", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Utilitza el mòdul PowerShell del WinGet en comptes de l'API COM", "Useful links": "Enllaços útils", "User": "<PERSON><PERSON><PERSON>", "User interface preferences": "Preferències de l'interfície d'usuari", "User | Local": "Usuari | Local", "Username": "Nom d'usuari", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Utilitzar l'UniGetUI implica l'acceptació de la llicència GNU Lesser General Public License v2.1", "Using WingetUI implies the acceptation of the MIT License": "Usar l'UniGetUI implica l'acceptació de la llicència MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "No s'ha pogut trobar el vcpkg. Definiu la variable d'entorn %VCPKG_ROOT% o definiu l'arrel del vcpkg a la configuració de l'UniGetUI", "Vcpkg was not found on your system.": "No s'ha pogut trobar el vcpkg al vostre sistema.", "Verbose": "<PERSON><PERSON><PERSON><PERSON>", "Version": "<PERSON><PERSON><PERSON><PERSON>", "Version to install:": "Versió a instal·lar:", "Version:": "Versió:", "View GitHub Profile": "<PERSON><PERSON><PERSON> de GitHub", "View WingetUI on GitHub": "Mostra l'UniGetUI a GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Veieu el codi font de l'UniGetUI. A partir d'aquí, podeu informar d'errors o suggerir funcions, o fins i tot contribuïr directament al codi de l'UniGetUI.", "View mode:": "Mode de visualització", "View on UniGetUI": "Mostra a l'UniGetUI", "View page on browser": "Mostra al navegador", "View {0} logs": "Mostra el registre del {0}", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Espereu a que el dispositiu estigui connectat a internet abans d'intentar cap tasca que requereixi connexió a internet.", "Waiting for other installations to finish...": "Esperant a que acabin les altres instal·lacions...", "Waiting for {0} to complete...": "S'està esperant a que la {0} acabi...", "Warning": "<PERSON><PERSON><PERSON><PERSON>", "Warning!": "Atenció!", "We are checking for updates.": "Estem cercant actualitzacions.", "We could not load detailed information about this package, because it was not found in any of your package sources": "No hemos podido cargar información detallada sobre este paquete, ya que ha sido encontrado en ninguna de las fuentes de paquetes disponibles.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "No hem pogut carregar els detalls sobre aquest paquet, ja que no està disponible des de cap administrador de paquets.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "No hem pogut {action} el {package}. Proveu-ho més tard. Cliqueu a \"{showDetails}\" per a veure el registre de l'instal·lador.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "No hem pogut {action} {package}. Proveu-ho més tard. Cliqueu \"{showDetails}\" per a veure el registre del desinstal·lador.", "We couldn't find any package": "No s'ha trobat cap paquet", "Welcome to WingetUI": "Benvingut/da a l'UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Quan s'instal·lin paquets en bloc des d'una col·lecció, instal·la també els paquets que ja estiguin instal·lats. ", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Quan es trobin noves icones, elimina-les automàticament en comptes de mostrar aquest quadre de diàleg.", "Which backup do you want to open?": "Quina còpia de seguretat voleu obrir?", "Which package managers do you want to use?": "Quins gestors de paquets voleu utilitzar?", "Which source do you want to add?": "Quina font voleu afegir?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Encara que el WinGet es pot utilitzar a través de l'UniGetUI, avui en dia l'UniGetUI també es pot utilitzar amb altres administradors de paquets, fet que pot dur a confusions. <PERSON><PERSON><PERSON>, el WingetUI estava dissenyat per a funcionar només amb el WinGet, però això ja no és cert, i llavors WingetUI no representa el que aquest projecte vol aconseguir.", "WinGet could not be repaired": "No s'ha pogut reparar el WinGet", "WinGet malfunction detected": "S'ha detectat un funcionament incorrecte del WinGet", "WinGet was repaired successfully": "S'ha reparat el WinGet correctament.", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "UniGetUI - Tot en ordre", "WingetUI - {0} updates are available": "UniGetUI - s'han trobat {0} actualitzacions", "WingetUI - {0} {1}": "UniGetUI - {1} del {0}", "WingetUI Homepage": "Lloc web de l'UniGetUI", "WingetUI Homepage - Share this link!": "Lloc web de l'UniGetUI - Compartiu aquest enllaç!", "WingetUI License": "Llicència de l'UniGetUI", "WingetUI Log": "Registre de l'UniGetUI", "WingetUI Repository": "Repositori de l'UniGetUI", "WingetUI Settings": "Configuració de l'UniGetUI", "WingetUI Settings File": "Fitxer de configuració de l'UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "L'UniGetUI utilitza les següents llibreries. Sense elles, l'UniGetUI no hagués estat possible. ", "WingetUI Version {0}": "UniGetUI Versió {0}", "WingetUI autostart behaviour, application launch settings": "Comportament d'autoarrencada de l'UniGetUI, preferències de l'inici de l'aplicació", "WingetUI can check if your software has available updates, and install them automatically if you want to": "l'UniGetUI pot comprovar si el programari instal·lat té actualitzacions disponibles, i les pot instal·lar automàticament.", "WingetUI display language:": "Idioma de l'UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "Heu executat l'UniGetUI amb drets d'administrador, cosa que no es recomana. Quan l'UniGetUI s'executa amb drets d'administrador, TOTES les operacions executades des de l'UniGetUI també tindran drets d'administrador. Podeu seguir usant el prorgama, però us recomanem que no executeu el WingetUI amb drets d'administrador.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "L'UniGetUI ha estat traduit a més de 40 idiomes gràcies als traductors voluntaris. Gràcies 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "L'UniGetUI no ha estat traduït a màquina. Els següents usuaris s'han encarregat de les traduccions:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "L'UniGetUI és una aplicació que facilita l'administració de software, oferint una interfície gràfica unificada per als administradors de paquets més coneguts.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "El WingetUI canviarà el nom per a emfatitzar la diferència entre el WingetUI (l'aplicació que esteu utilitzant ara mateix) i el WinGet (un administrador de paquets desenvolupat per Microsoft, i amb el qual no hi tinc res a veure)", "WingetUI is being updated. When finished, WingetUI will restart itself": "S'està actualitzant l'UniGetUI. Quan a<PERSON>bi, l'UniGetUI es reiniciarà automàticament", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "L'UniGetUI és gratuït, i ho serà sempre. Sense anuncis, sense targetes de crèdit, sense versions prèmium. 100% gratuït, per sempre.", "WingetUI log": "Registre de l'UniGetUI", "WingetUI tray application preferences": "Preferències de la icona de la safata del sistema de l'UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "L'UniGetUI utilitza les següents llibreries. Sense elles, l'UniGetUI no hagués estat possible.", "WingetUI version {0} is being downloaded.": "S'està descarregant l'UniGetUI versió {0}.", "WingetUI will become {newname} soon!": "D'aquí poc el WingetUI es dirà {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "L'UniGetUI no comprovarà si hi ha actualitzacions periòdicament. Encara es comprovaran durant l'inici, però s'emetrà cap avís si se'n troben.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "L'UniGetUI mostrarà una finestra de l'UAC cada cop que un paquet requereixi drets d'administrador per a instal·lar-se.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "El WingetUI d'aquí poc passarà a dir-se {newname}. Això no representarà cap canvi en l'aplicació. Jo (el desenvolupador) continuaré el desenvolupament d'aquest projecte tal i com ho estic fent ara, però sota un altre nom.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "L'UniGetUI no hauria estat possible sense l'ajuda dels contribuïdors. Doneu-l'hi una ullada al seu perfil a GitHub, ja que l'UniGetUI no seria el mateix sense ells!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "L'UniGetUI no hagués estat possible sense l'ajuda dels contribuïdors. Moltes gràcies a tots 🥳", "WingetUI {0} is ready to be installed.": "L'UniGetUI {0} està llest per a ser instal·lat.", "Write here the process names here, separated by commas (,)": "Escriviu aquí els noms dels processos, separats per comes (,)", "Yes": "Sí", "You are logged in as {0} (@{1})": "<PERSON>u iniciat la sessió com a {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Podeu canviar aquest comportament a la configuració de seguretat de l'UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Podeu definir les comandes que s'executaran abans i/o després de que s'instal·li, s'actualitzi o es desinstal·li aquest paquet. Les comandes s'executaran en un Command Prompt, pel que els scripts de CMD funcionaran aquí.", "You have currently version {0} installed": "Actualment teniu instal·lada la versió {0}", "You have installed WingetUI Version {0}": "Teniu instal·lat l'UniGetUI versió {0}", "You may lose unsaved data": "És possible que es perdin dades no desades.", "You may need to install {pm} in order to use it with WingetUI.": "Potser heu d'instal·lar el {pm} si el voleu utilitzar a través l'UniGetUI.", "You may restart your computer later if you wish": "Podeu reiniciar el vostre ordinador més tard si així ho preferiu", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Només se us demanaran una cop i es concediran drets d'administrador als paquets que en sol·licitin.", "You will be prompted only once, and every future installation will be elevated automatically.": "Només se us demanaran un cop i totes les instal·lacions futures s'elevaran automàticament.", "You will likely need to interact with the installer.": "Segurament haureu d'interactuar amb l'instal·lador.", "[RAN AS ADMINISTRATOR]": "EXECUTAT COM A ADMINISTRADOR", "buy me a coffee": "comprar-me un cafè", "extracted": "extret", "feature": "característica", "formerly WingetUI": "abans WingetUI", "homepage": "lloc web", "install": "instal·la", "installation": "instal·lació", "installed": "instal·lat", "installing": "instal·lant", "library": "llibreria", "mandatory": "obligatori", "option": "opci<PERSON>", "optional": "opcional", "uninstall": "desinstal·la", "uninstallation": "desinstal·lació", "uninstalled": "desinstal·lat", "uninstalling": "desinstal·lant", "update(noun)": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update(verb)": "<PERSON><PERSON><PERSON>", "updated": "actualitzat", "updating": "actualitzant", "version {0}": "versió {0}", "{0} Install options are currently locked because {0} follows the default install options.": "Les opcions d'instal·lació del {0} estan bloquejades perquè el {0} segueix les opcions d'instal·lació per defecte.", "{0} Uninstallation": "Desinstal·lació del {0}", "{0} aborted": "{0} a<PERSON><PERSON><PERSON>", "{0} can be updated": "{0} es pot actualitzar", "{0} can be updated to version {1}": "{0} es pot actualitzar a la versió {1}", "{0} days": "{0} dies", "{0} desktop shortcuts created": "S'han creat {0} dreceres a l'escriptori", "{0} failed": "{0} fallida", "{0} has been installed successfully.": "El {0} s'ha instal·lat correctament.", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "El {0} s'ha instal·lat correctament. Es recomana reiniciar l'UniGetUI per a acabar la instal·lació", "{0} has failed, that was a requirement for {1} to be run": "La {0} ha fallat, i era un requisit per a que s'executés la {1}", "{0} homepage": "lloc web de {0}", "{0} hours": "{0} hores", "{0} installation": "Instal·lació de {0}", "{0} installation options": "Opcions d'instal·lació del {0}", "{0} installer is being downloaded": "S'està descarregant l'instal·lador del {0}", "{0} is being installed": "S'està instal·lant el {0}", "{0} is being uninstalled": "S'està desinstal·lant el {0}", "{0} is being updated": "{0} s'està actualitzant", "{0} is being updated to version {1}": "{0} s'està actualitzant a la versió {1}", "{0} is disabled": "El {0} està desactivat", "{0} minutes": "{0} minuts", "{0} months": "{0} mesos", "{0} packages are being updated": "{0} estan sent actualitzats", "{0} packages can be updated": "<PERSON>s poden actualitzar {0} paquets", "{0} packages found": "{0} paquets trobats", "{0} packages were found": "s'han trobat {0} paquets", "{0} packages were found, {1} of which match the specified filters.": "S'han trobat {0} paquets, {1} dels quals s'ajusten als filtres establerts.", "{0} settings": "Confi<PERSON><PERSON><PERSON><PERSON> {0}", "{0} status": "Estat del {0}", "{0} succeeded": "{0} satisfactòria", "{0} update": "<PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "{0} updates are available": "Hi ha {0} actualitzacions disponibles", "{0} was {1} successfully!": "{0} s'ha {1} correctament!", "{0} weeks": "{0} setmanes", "{0} years": "{0} anys", "{0} {1} failed": "La {1} de {0} ha fallat", "{package} Installation": "Instal·lació del {package}", "{package} Uninstall": "Desinstal·lació del {package}", "{package} Update": "Actualització del {package}", "{package} could not be installed": "No s'ha pogut instal·lar el {package}", "{package} could not be uninstalled": "No s'ha pogut desinstal·lar el {package}", "{package} could not be updated": "No s'ha pogut actuailtzar el {package}", "{package} installation failed": "La instal·lació del {package} ha fallat", "{package} installer could not be downloaded": "No s'ha pogut descarregar l'instal·lador del {package}", "{package} installer download": "Descàrrega de l'instal·lador del {package}", "{package} installer was downloaded successfully": "L'instal·lador del {package} s'ha descarregar correctament", "{package} uninstall failed": "La desinstal·lació del {package} ha fallat", "{package} update failed": "L'actuailtzació del {package} ha fallat", "{package} update failed. Click here for more details.": "L'actualització del {package} ha fallat. Cliqueu aquí per a més detalls.", "{package} was installed successfully": "{package} s'ha instal·lat correctament", "{package} was uninstalled successfully": "{package} s'ha desinstal·lat correctament", "{package} was updated successfully": "{package} s'ha actualitzat correctament", "{pcName} installed packages": "Paquets instal·lats de {pcName}", "{pm} could not be found": "El {pm} no s'ha pogut trobar", "{pm} found: {state}": "{pm} trobat: {state}", "{pm} is disabled": "{pm} està desactivat", "{pm} is enabled and ready to go": "{pm} està activat i a punt", "{pm} package manager specific preferences": "Preferències específiques de l'administrador de paquets {pm}", "{pm} preferences": "Preferències del {pm}", "{pm} version:": "<PERSON><PERSON><PERSON><PERSON> {pm}: ", "{pm} was not found!": "No s'ha trobat el {pm}!"}