{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": "(آخری چیک: {0})", "(Number {0} in the queue)": "(قطار میں {0} نمبر)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@hamzaharoon1314,\n@digitpk,\n@digitio", "0 packages found": "0 پیک<PERSON>ز ملے", "0 updates found": "0 اپ ڈیٹس ملے", "1 - Errors": "۱ - غلطیاں", "1 day": "۱ دن", "1 hour": "۱ گھنٹہ", "1 month": "1 مہینہ", "1 package was found": "۱ پیکج ملا", "1 update is available": "۱ اپ ڈیٹ دستیاب ہے", "1 week": "۱ ہفتہ", "1 year": "1 سال", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. \"{0}\" یا \"{1}\" صفحہ پر جائیں۔", "2 - Warnings": "۲ - انتباہات", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. وہ پیکج (پیکیجز) تلاش کریں جسے آپ بنڈل میں شامل کرنا چاہتے ہیں، اور ان کا سب سے بائیں چیک باکس منتخب کریں۔", "3 - Information (less)": "۳ - معلومات (کم)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. جب پیکجز جو آپ بنڈل میں شامل کرنا چاہتے ہیں منتخب ہو جائیں، تو ٹول بار پر \"{0}\" آپشن تلاش کریں اور کلک کریں۔", "4 - Information (more)": "۴ - معلومات (زیادہ)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. آپ کے پیکجز کو بنڈل میں شامل کر دیا جائے گا۔ آپ پیکجز شامل کرنا جاری رکھ سکتے ہیں، یا بنڈل برآمد کر سکتے ہیں۔", "5 - information (debug)": "۵ - معلومات (ڈی بگ)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "ایک مشہور C/C++ لائبریری مینیجر۔ C/C++ لائبریریوں اور دیگر C/C++ سے متعلقہ یوٹیلیٹیز<br>پر مشتمل ہے: <b>C/C++ لائبریریاں اور متعلقہ یوٹیلیٹیز</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "مائیکروسافٹ کے .NET ایکو سسٹم کے لئے تیار کردہ ٹولز اور ایگزیکیوشن فائلز سے بھرا ہوا ریپوزیٹری۔<br>اس میں شامل ہے: <b>.NET سے متعلق ٹولز اور سکرپٹس</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "مائیکروسافٹ کے .NET ایکو سسٹم کے لئے تیار کردہ ٹولز سے بھرا ہوا ریپوزیٹری۔<br>اس میں شامل ہے: b>.NET>سے متعلق ٹولز</b>", "A restart is required": "دوبارہ شروع کرنا ضروری ہے", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": "کے بارے میں", "About Qt6": "Qt6 کے بارے میں", "About WingetUI": "WingetUI کے بارے میں", "About WingetUI version {0}": "WingetUI ورژن {0} کے بارے میں", "About the dev": "ڈویلپر کے بارے میں", "Accept": "قبول کریں۔", "Action when double-clicking packages, hide successful installations": "پیکجز پر ڈبل کلک کرنے پر کارروائی، کامیاب انسٹالیشنز کو چھپائیں", "Add": "شامل کریں", "Add a source to {0}": "{0} میں ایک ذریعہ شامل کریں", "Add a timestamp to the backup file names": "بیک اپ فائل کے ناموں میں ٹائم اسٹیمپ شامل کریں", "Add a timestamp to the backup files": "بیک اپ فائلوں میں ٹائم اسٹیمپ شامل کریں", "Add packages or open an existing bundle": "پیکجز شامل کریں یا موجودہ بنڈل کھولیں", "Add packages or open an existing package bundle": "پیکیجز شامل کریں یا موجودہ پیکیج بنڈل کھولیں۔", "Add packages to bundle": "بنڈل میں پیکجز شامل کریں۔", "Add packages to start": "شروع کرنے کے لیے پیکجز شامل کریں۔", "Add selection to bundle": "انتخاب کو بنڈل میں شامل کریں", "Add source": "ذریعہ شامل کریں", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "نظر انداز کردہ اپ ڈیٹس کی فہرست میں 'کوئی قابل اطلاق اپ ڈیٹ نہیں ملا' کے ساتھ ناکام ہونے والی اپ ڈیٹس شامل کریں۔", "Adding source {source}": "ماخذ شامل کرنا {source}", "Adding source {source} to {manager}": "ذریعہ {source} کو {manager} میں شامل کیا جا رہا ہے", "Addition succeeded": "شمولیت کامیاب ہوگئی", "Administrator privileges": "ایڈمنسٹریٹر کی مراعات", "Administrator privileges preferences": "ایڈمنسٹریٹر کی مراعات کی ترجیحات", "Administrator rights": "ایڈمنسٹریٹر کے حقوق", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "تمام فائلیں", "All versions": "تمام ورژنز", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": "پیکیج آپریشنز کو متوازی انجام دینے کی اجازت دیں", "Allow parallel installs (NOT RECOMMENDED)": "متوازی تنصیب کی اجازت دیں (سفارش نہیں کی جاتی)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": "{pm} آپریشنز کو متوازی انجام دینے کی اجازت دیں", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "متبادل طور پر، آپ Windows PowerShell پرامپٹ میں درج ذیل کمانڈ کو چلا کر بھی {0} کو انسٹال کر سکتے ہیں:", "Always elevate {pm} installations by default": "ڈیفالٹ کے طور پر ہمیشہ {pm} تنصیبات کو بلند کریں", "Always run {pm} operations with administrator rights": "ہمیشہ {pm} آپریشنز کو ایڈمنسٹریٹر حقوق کے ساتھ چلائیں", "An error occurred": "ایک خرابی پیش آگئی", "An error occurred when adding the source: ": "ماخذ شامل کرتے وقت ایک خرابی پیش آئی: ", "An error occurred when attempting to show the package with Id {0}": "Id {0} کے ساتھ پیکیج کو دکھانے کی کوشش کرتے وقت ایک خرابی پیش آگئی", "An error occurred when checking for updates: ": "اپ ڈیٹس کی جانچ کرتے وقت ایک خرابی پیش آئی: ", "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "اس پیکیج کو پروسیس کرتے وقت ایک خرابی پیش آئی", "An error occurred:": "ایک خرابی پیش آئی:", "An interal error occurred. Please view the log for further details.": "ایک اندرونی خرابی پیش آگئی۔ مزید تفصیلات کے لیے براہ کرم لاگ دیکھیں۔", "An unexpected error occurred:": "ایک غیر متوقع خرابی پیش آئی:", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "WinGet کو ٹھیک کرنے کی کوشش کے دوران ایک غیر متوقع مسئلہ پیش آیا۔ براہ کرم بعد میں دوبارہ کوشش کریں۔", "An update was found!": "ایک اپ ڈیٹ ملا!", "Android Subsystem": "اینڈرائیڈ سب سسٹم", "Another source": "ایک اور ماخذ", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "کسی انسٹال یا اپ ڈیٹ آپریشن کے دوران بنائے گئے کوئی بھی نئے شارٹ کٹس کو پہلی بار پتہ چلنے پر تصدیقی اشارہ دکھانے کے بجائے خود بخود حذف کر دیا جائے گا۔", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "UniGetUI کے باہر تخلیق یا ترمیم شدہ کسی بھی شارٹ کو نظر انداز کر دیا جائے گا۔ آپ انہیں {0} بٹن کے ذریعے شامل کر سکیں گے۔", "Any unsaved changes will be lost": "کوئی بھی غیر محفوظ شدہ تبدیلیاں ضائع ہو جائیں گی۔", "App Name": "ایپ کا نام", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": "ایپلیکیشن تھیم، سٹارٹ اپ پیج، پیکج آئیکنز، خود بخود کامیاب انسٹال صاف ہو جاتے ہیں۔", "Application theme:": "ایپلیکیشن تھیم:", "Apply": null, "Architecture to install:": "تنصیب کے لئے فن تعمیر:", "Are these screenshots wron or blurry?": "کیا یہ اسکرین شاٹس غلط ہیں یا دھندلے؟", "Are you really sure you want to enable this feature?": "کیا آپ واقعی اس خصوصیت کو فعال کرنا چاہتے ہیں؟", "Are you sure you want to create a new package bundle? ": "کیا آپ واقعی ایک نیا پیکج بنڈل بنانا چاہتے ہیں؟ ", "Are you sure you want to delete all shortcuts?": "کیا آپ واقعی تمام شارٹ کٹس کو حذف کرنا چاہتے ہیں؟", "Are you sure?": "کیا آپ یقین رکھتے ہیں؟", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": "ہر بیچ کے آپریشنز کے لئے ایک بار ایڈمنسٹریٹر مراعات طلب کریں", "Ask for administrator rights when required": "جب ضرورت ہو ایڈمنسٹریٹر حقوق طلب کریں", "Ask once or always for administrator rights, elevate installations by default": "ایک بار یا ہمیشہ ایڈمنسٹریٹر حقوق طلب کریں، ڈیفالٹ کے طور پر تنصیبات کو بلند کریں", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "صرف ایک بار ایڈمنسٹریٹر مراعات طلب کریں (سفارش نہیں کی جاتی)", "Ask to delete desktop shortcuts created during an install or upgrade.": "انسٹال یا اپ گریڈ کے دوران بنائے گئے ڈیسک ٹاپ شارٹ کٹس کو حذف کرنے کو کہیں۔", "Attention required": "توجہ درکار ہے", "Authenticate to the proxy with an user and a password": null, "Author": "مصنف", "Automatic desktop shortcut remover": "خودکار ڈیسک ٹاپ شارٹ کٹ ہٹانے والا", "Automatically save a list of all your installed packages to easily restore them.": "تمام تنصیب شدہ پیکیجز کی فہرست خودکار طور پر محفوظ کریں تاکہ انہیں آسانی سے بحال کیا جا سکے۔", "Automatically save a list of your installed packages on your computer.": "اپنے کمپیوٹر پر اپنے تنصیب شدہ پیکیجز کی فہرست خودکار طور پر محفوظ کریں۔", "Autostart WingetUI in the notifications area": "نوٹیفیکیشن علاقے میں WingetUI کو خودکار طور پر شروع کریں", "Available Updates": "دستیاب اپ ڈیٹس", "Available updates: {0}": "دستیاب اپ ڈیٹس: {0}", "Available updates: {0}, not finished yet...": "دستیاب اپ ڈیٹس: {0}, ابھی ختم نہیں ہوئی...", "Backing up packages to GitHub Gist...": null, "Backup": "بیک اپ", "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "تنصیب شدہ پیکیجز کا بیک اپ", "Backup location": null, "Become a contributor": "شراکت دار بنیں", "Become a translator": "مترجم بنیں", "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "بیٹا فیچرز اور دیگر اختیارات جنہیں نہیں چھیڑنا چاہئے", "Both": "دونوں", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "لیکن یہاں کچھ اور چیزیں ہیں جو آپ WingetUI کے بارے میں مزید جاننے کے لئے کر سکتے ہیں:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "پیکیج مینیجر کو بند کرنے سے، آپ اس کے پیکیجز دیکھنے یا اپ ڈیٹ کرنے کے قابل نہیں ہوں گے۔", "Cache administrator rights and elevate installers by default": "ایڈمنسٹریٹر حقوق کو کیش کریں اور انسٹالرز کو ڈیفالٹ کے طور پر بلند کریں", "Cache administrator rights, but elevate installers only when required": "ایڈمنسٹریٹر حقوق کو کیش کریں، لیکن انسٹالرز کو صرف ضرورت کے وقت بلند کریں", "Cache was reset successfully!": "کیش کامیابی سے ری سیٹ ہو گیا!", "Can't {0} {1}": "{0} {1} نہیں کر سکتے", "Cancel": "منسوخ کریں", "Cancel all operations": "تمام آپریشنز منسوخ کریں۔", "Change backup output directory": "بیک اپ آؤٹ پٹ ڈائریکٹری تبدیل کریں", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": "یہ تبدیل کریں کہ UniGetUI آپ کے پیکیجز کے لئے دستیاب اپ ڈیٹس کو کیسے چیک اور انسٹال کرتا ہے", "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": "تبدیل کریں کہ UniGetUI کس طرح پیکجز کو انسٹال کرتا ہے، اور دستیاب اپ ڈیٹس کو چیک اور انسٹال کرتا ہے۔", "Change how operations request administrator rights": null, "Change install location": "تنصیب کی جگہ تبدیل کریں", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "پیکیج اپ ڈیٹس کو وقتاً فوقتاً چیک کریں", "Check for updates": "اپ ڈیٹس کے لیے چیک کریں۔", "Check for updates every:": "ہر ایک کے لئے اپ ڈیٹس چیک کریں:", "Check for updates periodically": "وقتاً فوقتاً اپ ڈیٹس چیک کریں", "Check for updates regularly, and ask me what to do when updates are found.": "باقاعدگی سے اپ ڈیٹس چیک کریں، اور اپ ڈیٹس ملنے پر مجھ سے پوچھیں کہ کیا کرنا ہے۔", "Check for updates regularly, and automatically install available ones.": "باقاعدگی سے اپ ڈیٹس چیک کریں، اور دستیاب اپ ڈیٹس کو خود بخود انسٹال کریں۔", "Check out my {0} and my {1}!": "میرا {0} اور میرا {1} دیکھیں!", "Check out some WingetUI overviews": "کچھ WingetUI جائزے دیکھیں", "Checking for other running instances...": "دیگر چلنے والے انسٹینسز کی جانچ ہو رہی ہے...", "Checking for updates...": "اپ ڈیٹس کی جانچ ہو رہی ہے...", "Checking found instace(s)...": "ملنے والی مثالوں کی جانچ ہو رہی ہے...", "Choose how many operations shouls be performed in parallel": "منتخب کریں کہ متوازی طور پر کتنے آپریشن کیے جائیں۔", "Clear cache": "کیشے صاف کریں۔", "Clear finished operations": null, "Clear selection": "انتخاب صاف کریں", "Clear successful operations": "کلین کامیاب آپریشنز", "Clear successful operations from the operation list after a 5 second delay": "5 سیکنڈ کی تاخیر کے بعد آپریشن لسٹ سے کامیاب آپریشنز کو صاف کریں۔", "Clear the local icon cache": "مقامی آئیکن کیشے کو صاف کریں۔", "Clearing Scoop cache - WingetUI": "اسکوپ کیشے کو صاف کرنا - WingetUI", "Clearing Scoop cache...": "سکوپ کیشے کو صاف کیا جا رہا ہے...", "Click here for more details": "مزید تفصیلات کے لیے یہاں کلک کریں۔", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "تنصیب کا عمل شروع کرنے کے لیے انسٹال پر کلک کریں۔ اگر آپ انسٹالیشن کو چھوڑ دیتے ہیں تو ہو سکتا ہے UniGetUI توقع کے مطابق کام نہ کرے۔", "Close": "بند کریں", "Close UniGetUI to the system tray": "UniGetUI کو سسٹم ٹرے میں بند کریں۔", "Close WingetUI to the notification area": "نوٹیفکیشن کے علاقے میں WingetUI کو بند کریں۔", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": "کمانڈ لائن آؤٹ پٹ", "Command-line to run:": null, "Compare query against": "استفسار کے خلاف موازنہ کریں۔", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "اجزاء کی معلومات", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": "جاری رکھیں", "Contribute to the icon and screenshot repository": "آئیکن اور اسکرین شاٹ ریپوزٹری میں تعاون کریں۔", "Contributors": "تعاون کرنے والے", "Copy": "کاپی کریں", "Copy to clipboard": "کلپ بورڈ پر کاپی کریں۔", "Could not add source": "ماخذ شامل نہیں کیا جا سکا", "Could not add source {source} to {manager}": "ماخذ {source} کو {manager} میں شامل نہیں کیا جا سکا", "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": "بنڈل نہیں بنایا جا سکا", "Could not load announcements - ": "اعلانات کو لوڈ نہیں کیا جا سکا - ", "Could not load announcements - HTTP status code is $CODE": "اعلانات کو لوڈ نہیں کیا جا سکا - HTTP اسٹیٹس کوڈ $CODE ہے۔", "Could not remove source": "ماخذ کو ہٹایا نہیں جا سکا", "Could not remove source {source} from {manager}": "{manager} سے ماخذ {source} کو ہٹایا نہیں جا سکا", "Could not remove {source} from {manager}": "{manager} سے {source} کو ہٹایا نہیں جا سکا", "Credentials": null, "Current Version": "موجودہ ورژن", "Current status: Not logged in": null, "Current user": "موجودہ صارف", "Custom arguments:": "حسب ضرورت دلائل:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "حسب ضرورت کمانڈ لائن دلائل:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "WingetUI کو حسب ضرورت بنائیں - صرف ہیکرز اور جدید صارفین کے لیے", "DEBUG BUILD": "ڈیبگ بلڈ", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ڈس کلیمر: ہم ڈاؤن لوڈ کردہ پیکجز کے لیے ذمہ دار نہیں ہیں۔ براہ کرم صرف قابل بھروسہ سافٹ ویئر انسٹال کرنا یقینی بنائیں۔", "Dark": "اندھیرا", "Decline": "رد کرنا", "Default": "طے شدہ", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "پہلے سے طے شدہ ترجیحات - باقاعدہ صارفین کے لیے موزوں", "Default vcpkg triplet": "ڈیفالٹ vcpkg ٹرپلٹ", "Delete?": "حذف کریں؟", "Dependencies:": null, "Descendant": null, "Description:": "تفصیل:", "Desktop shortcut created": "ڈیسک ٹاپ شارٹ کٹ بنایا گیا۔", "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "تیار کرنا مشکل ہے، اور یہ ایپلیکیشن مفت ہے۔ لیکن اگر آپ کو ایپلی کیشن پسند آئی تو آپ ہمیشہ <b>مجھے ایک کافی خرید سکتے ہیں</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "\"{discoveryTab}\" ٹیب پر کسی آئٹم پر ڈبل کلک کرنے پر براہ راست انسٹال کریں (پیکج کی معلومات دکھانے کے بجائے)", "Disable new share API (port 7058)": "نیا شیئر API (پورٹ 7058) کو غیر فعال کریں", "Disable the 1-minute timeout for package-related operations": "پیکیج سے متعلق کارروائیوں کے لیے 1 منٹ کا ٹائم آؤٹ غیر فعال کریں۔", "Disclaimer": "دستبرداری", "Discover Packages": "پیکیجز دریافت کریں۔", "Discover packages": "پیکیجز دریافت کریں۔", "Distinguish between\nuppercase and lowercase": "بڑے حروف اور چھوٹے حروف میں فرق کریں۔", "Distinguish between uppercase and lowercase": "بڑے اور چھوٹے کے درمیان فرق کریں۔", "Do NOT check for updates": "اپ ڈیٹس کی جانچ نہ کریں۔", "Do an interactive install for the selected packages": "منتخب پیکجوں کے لیے ایک انٹرایکٹو انسٹال کریں۔", "Do an interactive uninstall for the selected packages": "منتخب پیکجوں کے لیے ایک انٹرایکٹو ان انسٹال کریں۔", "Do an interactive update for the selected packages": "منتخب پیکجوں کے لیے ایک انٹرایکٹو اپ ڈیٹ کریں۔", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "GitHub سے نئے ایپ کے ترجمے خود بخود ڈاؤن لوڈ نہ کریں۔", "Do not ignore updates for this package anymore": "اس پیکج کے لیے اپ ڈیٹس کو مزید نظر انداز نہ کریں۔", "Do not remove successful operations from the list automatically": "فہرست سے کامیاب آپریشنز کو خود بخود نہ ہٹائیں۔", "Do not show this dialog again for {0}": "{0} کے لیے یہ ڈائیلاگ دوبارہ مت دکھائیں", "Do not update package indexes on launch": "لانچ ہونے پر پیکیج انڈیکس کو اپ ڈیٹ نہ کریں۔", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "کیا آپ قبول کرتے ہیں کہ UniGetUI استعمال کے گمنام اعدادوشمار جمع اور بھیجتا ہے، جس کا واحد مقصد صارف کے تجربے کو سمجھنا اور بہتر بنانا ہے؟", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "کیا آپ کو WingetUI مفید لگتا ہے؟ اگر آپ کر سکتے ہیں تو، آپ میرے کام کی حمایت کرنا چاہیں گے، اس لیے میں WingetUI کو حتمی پیکیج مینیجنگ انٹرفیس بنانا جاری رکھ سکتا ہوں۔", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "کیا آپ کو WingetUI مفید لگتا ہے؟ آپ ڈویلپر کو سپورٹ کرنا چاہیں گے؟ اگر ایسا ہے تو، آپ {0} کر سکتے ہیں، یہ بہت مدد کرتا ہے!", "Do you really want to reset this list? This action cannot be reverted.": "کیا آپ واقعی اس فہرست کو دوبارہ ترتیب دینا چاہتے ہیں؟ اس کارروائی کو واپس نہیں لیا جا سکتا۔", "Do you really want to uninstall the following {0} packages?": "کیا آپ واقعی درج ذیل {0} پیکجوں کو اَن انسٹال کرنا چاہتے ہیں؟", "Do you really want to uninstall {0} packages?": "کیا آپ واقعی {0} پیکجوں کو اَن انسٹال کرنا چاہتے ہیں؟", "Do you really want to uninstall {0}?": "کیا آپ واقعی {0} کو اَن انسٹال کرنا چاہتے ہیں؟", "Do you want to restart your computer now?": "کیا آپ ابھی اپنا کمپیوٹر دوبارہ شروع کرنا چاہتے ہیں؟", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "کیا آپ WingetUI کا اپنی زبان میں ترجمہ کرنا چاہتے ہیں؟ <a style=\"color:{0}\" href=\"{1}\"a>یہاں!</a> تعاون کرنے کا طریقہ دیکھیں", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "عطیہ کرنے کی طرح محسوس نہیں کرتے؟ پریشان نہ ہوں، آپ ہمیشہ اپنے دوستوں کے ساتھ WingetUI کا اشتراک کر سکتے ہیں۔ WingetUI کے بارے میں بات پھیلائیں۔", "Donate": "عطیہ کریں۔", "Done!": null, "Download failed": "ڈاؤن لوڈ ناکام ہو گیا۔", "Download installer": "انسٹالر ڈاؤن لوڈ کریں", "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": "ڈاؤن لوڈ کامیاب ہو گیا۔", "Download updated language files from GitHub automatically": "<PERSON><PERSON><PERSON><PERSON> سے اپ ڈیٹ شدہ زبان کی فائلیں خود بخود ڈاؤن لوڈ کریں۔", "Downloading": "ڈاؤن لوڈ ہو رہا ہے۔", "Downloading backup...": null, "Downloading installer for {package}": "{package} کے لیے انسٹالر ڈاؤن لوڈ ہو رہا ہے", "Downloading package metadata...": "پیکیج میٹا ڈیٹا ڈاؤن لوڈ ہو رہا ہے...", "Enable Scoop cleanup on launch": "لانچ پر اسکوپ کلین اپ کو فعال کریں۔", "Enable WingetUI notifications": "WingetUI اطلاعات کو فعال کریں۔", "Enable an [experimental] improved WinGet troubleshooter": "ایک [تجرباتی] بہتر WinGet ٹربل شوٹر کو فعال کریں۔", "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": "پس منظر CPU کے استعمال کی اصلاح کو فعال کریں (دیکھیں پل کی درخواست #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "بیک گراؤنڈ API کو فعال کریں (WingetUI وجیٹس اور شیئرنگ، پورٹ 7058)", "Enable it to install packages from {pm}.": "{pm} سے پیکجز انسٹال کرنے کے لیے اسے فعال کریں۔", "Enable the automatic WinGet troubleshooter": "خودکار WinGet ٹربل شوٹر کو فعال کریں۔", "Enable the new UniGetUI-Branded UAC Elevator": "نئے UniGetUI-Branded UAC ایلیویٹر کو فعال کریں۔", "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "{pm} کو فعال کریں", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "خرا<PERSON>ی", "Everything is up to date": "سب کچھ تازہ ترین ہے", "Exact match": "عین مطابق میچ", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "آپ کے ڈیسک ٹاپ پر موجودہ شارٹ کٹس کو اسکین کیا جائے گا، اور آپ کو یہ منتخب کرنے کی ضرورت ہوگی کہ کن کو رکھنا ہے اور کون سے ہٹانا ہے۔", "Expand version": "ورژن کو پھیلائیں۔", "Experimental settings and developer options": "تجرباتی ترتیبات اور ڈویلپر کے اختیارات", "Export": "بر<PERSON>مد کریں", "Export log as a file": "لاگ کو فائل کے طور پر ایکسپورٹ کریں", "Export packages": "پیکجز کو ایکسپورٹ کریں", "Export selected packages to a file": "منتخب کردہ پیکجز کو فائل میں ایکسپورٹ کریں", "Export settings to a local file": "ترتیبات کو مقامی فائل میں ایکسپورٹ کریں", "Export to a file": "فائل میں ایکسپورٹ کریں", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": "تازہ ترین اعلانات حاصل کیے جا رہے ہیں، براہ کرم انتظار کریں...", "Filters": "فل<PERSON><PERSON><PERSON>", "Finish": "ختم کریں", "Follow system color scheme": "سسٹم کلر سکیم پر عمل کریں۔", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "زبردستی ARM مرتب شدہ ونگٹ ورژن (صرف ARM64 سسٹمز کے لیے)", "Formerly known as WingetUI": "پہلے WingetUI کے نام سے جانا جاتا تھا۔", "Found": "مل گیا", "Found packages: ": "ملے پیکجز: ", "Found packages: {0}": "ملے پیکجز: {0}", "Found packages: {0}, not finished yet...": "ملے پیکجز: {0}, ابھی ختم نہیں ہوا...", "General preferences": "عمومی ترجیحات", "GitHub profile": "گٹ ہب پروفائل", "Global": "عالمی", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "نامعلوم لیکن کارآمد یوٹیلیٹیز اور دیگر دلچسپ پیکجز کا زبردست ذخیرہ۔<br>مشتمل ہے: <b>یوٹیلٹیز، کمانڈ لائن پروگرام، جنرل سافٹ ویئر (اضافی بالٹی درکار)</b>", "Great! You are on the latest version.": "بہت اچھا! آپ تازہ ترین ورژن پر ہیں۔", "Grid": null, "Help": "مد<PERSON>", "Help and documentation": "مدد اور دستاویزات", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "یہاں آپ درج ذیل شارٹ کٹس کے حوالے سے UniGetUI کے رویے کو تبدیل کر سکتے ہیں۔ شارٹ کٹ چیک کرنے سے UniGetUI اسے حذف کر دے گا اگر مستقبل میں اپ گریڈ بنایا جاتا ہے۔ اسے غیر چیک کرنے سے شارٹ کٹ برقرار رہے گا۔", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "ہائے، میرا نام مارٹی ہے، اور میں WingetUI کا <i>ڈویلپر</i> ہوں۔ WingetUI مکمل طور پر میرے فارغ وقت پر بنایا گیا ہے!", "Hide details": "تفصیلات چھپائیں", "Homepage": "<PERSON>و<PERSON> پیج", "Hooray! No updates were found.": "واہ! کوئی اپ ڈیٹس نہیں ملی۔", "How should installations that require administrator privileges be treated?": "ایڈمنسٹریٹر کے حقوق کی ضرورت والے انسٹالیشنز کو کیسے ہینڈل کیا جائے؟", "How to add packages to a bundle": "پیکجوں کو بنڈل میں کیسے شامل کریں۔", "I understand": "مجھے سمجھ آگیا", "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "اس پیکیج کے مستقبل کے اپ ڈیٹس کو نظرانداز کریں", "Ignore packages from {pm} when showing a notification about updates": "اپ ڈیٹس کے بارے میں اطلاع دکھاتے وقت {pm} کے پیکجز کو نظر انداز کریں۔", "Ignore selected packages": "منتخب کردہ پیکیجز کو نظرانداز کریں", "Ignore special characters": "خاص کریکٹرز کو نظرانداز کریں", "Ignore updates for the selected packages": "منتخب کردہ پیکیجز کے اپ ڈیٹس کو نظرانداز کریں", "Ignore updates for this package": "اس پیکیج کے اپ ڈیٹس کو نظرانداز کریں", "Ignored updates": "نظرانداز کردہ اپ ڈیٹس", "Ignored version": "نظرانداز کردہ ورژن", "Import": "درآ<PERSON>د کریں", "Import packages": "پیکیجز درآمد کریں", "Import packages from a file": "فائل سے پیکیجز درآمد کریں", "Import settings from a local file": "مقامی فائل سے ترتیبات درآمد کریں", "In order to add packages to a bundle, you will need to: ": "پیکجوں کو بنڈل میں شامل کرنے کے لیے، آپ کو یہ کرنے کی ضرورت ہوگی: ", "Initializing WingetUI...": "WingetUI کو ابتدائی شکل دی جا رہی ہے...", "Install": "انسٹال کریں", "Install Scoop": "سکوپ انسٹال کریں۔", "Install and more": null, "Install and update preferences": "ترجیحات کو انسٹال اور اپ ڈیٹ کریں۔", "Install as administrator": "ایڈمنسٹریٹر کے طور پر انسٹال کریں", "Install available updates automatically": "دستیاب اپ ڈیٹس خود بخود انسٹال کریں", "Install location can't be changed for {0} packages": null, "Install location:": "انسٹال مقام:", "Install options": null, "Install packages from a file": "فائل سے پیکجز انسٹال کریں", "Install prerelease versions of UniGetUI": "UniGetUI کے پری ریلیز ورژن انسٹال کریں۔", "Install selected packages": "منتخب کردہ پیکجز انسٹال کریں", "Install selected packages with administrator privileges": "منتخب کردہ پیکجز ایڈمنسٹریٹر کی مراعات کے ساتھ انسٹال کریں", "Install selection": "انتخاب انسٹال کریں", "Install the latest prerelease version": "تازہ ترین پری ریلیز ورژن انسٹال کریں", "Install updates automatically": "<PERSON>و<PERSON> بخود اپ ڈیٹس انسٹال کریں", "Install {0}": "انسٹال کریں {0}", "Installation canceled by the user!": "انسٹالیشن صارف نے منسوخ کر دی!", "Installation failed": "انسٹالیشن ناکام ہوگئی", "Installation options": "انسٹالیشن کے اختیارات", "Installation scope:": "انسٹالیشن کا دائرہ:", "Installation succeeded": "انسٹالیشن کامیاب ہوگئی", "Installed Packages": "انسٹال شدہ پیکجز", "Installed Version": "انسٹال شدہ ورژن", "Installed packages": "انسٹال شدہ پیکجز", "Installer SHA256": "انسٹالر SHA256", "Installer SHA512": "انسٹالر SHA512", "Installer Type": "انسٹالر کی قسم", "Installer URL": "انسٹالر یو آر ایل", "Installer not available": "انسٹالر دستیاب نہیں ہے۔", "Instance {0} responded, quitting...": "مثال {0} نے جواب دیا، چھوڑ رہا ہے...", "Instant search": "فوری تلاش", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": "سالمیت کی جانچ کو چھوڑ دیا گیا۔", "Integrity checks will not be performed during this operation": "اس آپریشن کے دوران سالمیت کی جانچ نہیں کی جائے گی۔", "Interactive installation": "انٹرایکٹو تنصیب", "Interactive operation": "انٹرایکٹو آپریشن", "Interactive uninstall": "انٹرایکٹو ان انسٹال", "Interactive update": "انٹرایکٹو اپ ڈیٹ", "Internet connection settings": null, "Is this package missing the icon?": "کیا اس پیکیج میں آئیکن غائب ہے؟", "Is your language missing or incomplete?": "کیا آپ کی زبان غائب ہے یا نامکمل؟", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": "WinGet کی مرمت کے بعد UniGetUI کو دوبارہ شروع کرنے کی سفارش کی جاتی ہے۔", "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "ایسا لگتا ہے کہ WinGet ٹھیک سے کام نہیں کر رہا ہے۔ کیا آپ WinGet کو ٹھیک کرنے کی کوشش کرنا چاہتے ہیں؟", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "ایسا لگتا ہے کہ آپ نے بطور ایڈمنسٹریٹر WingetUI چلایا، جس کی سفارش نہیں کی جاتی ہے۔ آپ اب بھی پروگرام استعمال کر سکتے ہیں، لیکن ہم ایڈمنسٹریٹر کی مراعات کے ساتھ WingetUI نہ چلانے کی انتہائی سفارش کرتے ہیں۔ وجہ دیکھنے کے لیے \"{showDetails}\" پر کلک کریں۔", "Language": null, "Language, theme and other miscellaneous preferences": "زبان، تھیم اور دیگر متفرق ترجیحات", "Last updated:": "آخری بار اپ ڈیٹ کیا گیا:", "Latest": "تازہ ترین", "Latest Version": "تازہ ترین ورژن", "Latest Version:": ":تازہ ترین ورژن", "Latest details...": "تازہ ترین تفصیلات...", "Launching subprocess...": "ذیلی عمل شروع کیا جا رہا ہے...", "Leave empty for default": "ڈیفالٹ کے لئے خالی چھوڑ دیں", "License": "لائسنس", "Licenses": "لا<PERSON><PERSON><PERSON><PERSON>ز", "Light": "ہلکا", "List": null, "Live command-line output": "براہ راست کمانڈ لائن آؤٹ پٹ", "Live output": "براہ راست آؤٹ پٹ", "Loading UI components...": "UI اجزاء لوڈ ہو رہے ہیں...", "Loading WingetUI...": "WingetUI لوڈ ہو رہا ہے...", "Loading packages": "پیکیجز لوڈ ہو رہے ہیں", "Loading packages, please wait...": "پیکیجز لوڈ ہو رہے ہیں، براہ کرم انتظار کریں...", "Loading...": "لوڈ ہو رہا ہے...", "Local": "مقا<PERSON>ی", "Local PC": "مقامی پی سی", "Local backup advanced options": null, "Local machine": "مقامی مشین", "Local package backup": null, "Locating {pm}...": "{pm} کی تلاش جاری ہے...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": "لاگ کی سطح:", "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "پیکیجز کی تلاش جاری ہے...", "Machine | Global": "مشین | عالمی", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": "انتظام کریں۔", "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": "سیٹنگز ایپ سے WingetUI آٹو اسٹارٹ کے رویے کو منظم کریں", "Manage ignored packages": "نظرانداز کردہ پیکیجز کو منظم کریں", "Manage ignored updates": "نظرانداز کردہ اپ ڈیٹس کو منظم کریں", "Manage shortcuts": "شارٹ کٹس کا نظم کریں۔", "Manage telemetry settings": "ٹیلی میٹری کی ترتیبات کا نظم کریں۔", "Manage {0} sources": "{0} ماخذ کو منظم کریں", "Manifest": "منصو<PERSON><PERSON>", "Manifests": "منصوبے", "Manual scan": "دستی اسکین", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "مائیکروسافٹ کا آفیشل پیکیج مینیجر۔ معروف اور تصدیق شدہ پیکجوں سے بھرا<br>مشتمل ہے: <b>جنرل سافٹ ویئر، مائیکروسافٹ اسٹور ایپس</b>", "Missing dependency": "ضروریات غائب ہیں", "More": "مزی<PERSON>", "More details": "مزید تفصیلات", "More details about the shared data and how it will be processed": "مشترکہ ڈیٹا کے بارے میں مزید تفصیلات اور اس پر کارروائی کیسے کی جائے گی۔", "More info": "مزید معلومات", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "نوٹ: اس ٹربل شوٹر کو WinGet سیکشن پر UniGetUI سیٹنگز سے غیر فعال کیا جا سکتا ہے۔", "Name": "نام", "New": null, "New Version": "نیا ورژن", "New bundle": "نیا بنڈل", "New version": "نیا ورژن", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "نہیں", "No applicable installer was found for the package {0}": "پیکیج {0} کے لیے کوئی قابل اطلاق انسٹالر نہیں ملا", "No dependencies specified": null, "No new shortcuts were found during the scan.": "اسکین کے دوران کوئی نیا شارٹ کٹ نہیں ملا۔", "No packages found": "کوئی پیکجز نہیں ملے", "No packages found matching the input criteria": "ان پٹ معیار کے مطابق کوئی پیکجز نہیں ملے", "No packages have been added yet": "ابھی تک کوئی پیکجز شامل نہیں کیے گئے ہیں", "No packages selected": "کوئی پیکجز منتخب نہیں کیے گئے", "No packages were found": "کوئی پیکجز نہیں ملے", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "کوئی ذاتی معلومات اکٹھی نہیں کی جاتی اور نہ ہی بھیجی جاتی ہے، اور جمع کردہ ڈیٹا کو گمنام کر دیا جاتا ہے، اس لیے اسے آپ کو بیک ٹریک نہیں کیا جا سکتا۔", "No results were found matching the input criteria": "ان پٹ معیار کے مطابق کوئی نتائج نہیں ملے", "No sources found": "کوئی ذرائع نہیں ملے", "No sources were found": "کوئی ذرائع نہیں ملے", "No updates are available": "کوئی اپ ڈیٹس دستیاب نہیں ہیں", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "نوڈ جے ایس کا پیکیج مینیجر۔ لائبریریوں اور دیگر افادیت سے بھری ہوئی ہے جو جاوا اسکرپٹ کی دنیا کا چکر لگاتی ہے<br>پر مشتمل ہے: <b>نوڈ جاوا اسکرپٹ لائبریریاں اور دیگر متعلقہ یوٹیلیٹیز</b>", "Not available": "دستیاب نہیں", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "نہیں ملا", "Not right now": "ابھی نہیں", "Notes:": "نوٹس:", "Notification preferences": "اطلاع کی ترجیحات", "Notification tray options": "نوٹیفکیشن ٹرے کے اختیارات", "Notification types": null, "NuPkg (zipped manifest)": "NuPkg (زپ شدہ منیفیسٹ)", "OK": "ٹھیک ہے", "Ok": "ٹھیک ہے", "Open": "کھولیں", "Open GitHub": "GitHub کھولیں", "Open UniGetUI": "UniGetUI کھولیں۔", "Open UniGetUI security settings": null, "Open WingetUI": "WingetUI کھولیں", "Open backup location": "بیک اپ مقام کھولیں", "Open existing bundle": "موجودہ بنڈل کھولیں", "Open install location": "انسٹال لوکیشن کھولیں۔", "Open the welcome wizard": "خوش آمدید وزرڈ کھولیں", "Operation canceled by user": "صارف کے ذریعے آپریشن منسوخ کر دیا گیا۔", "Operation cancelled": "آپریشن منسوخ", "Operation history": "آپریشن کی تاریخ", "Operation in progress": "آپریشن جاری ہے", "Operation on queue (position {0})...": "قطار میں آپریشن (پوزیشن {0}...)", "Operation profile:": null, "Options saved": "اختیارات محفوظ ہوگئے", "Order by:": null, "Other": "دیگر", "Other settings": null, "Package": null, "Package Bundles": "پیکیج بنڈلز", "Package ID": "پیکیج آئی ڈی", "Package Manager": "پیکیج منیجر", "Package Manager logs": "پیکیج منیجر لاگز", "Package Managers": "پیکیج منیجرز", "Package Name": "پیکیج کا نام", "Package backup": "پیکیج بیک اپ", "Package backup settings": null, "Package bundle": "پیکیج بنڈل", "Package details": "پیکیج کی تفصیلات", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "پیکیج منیجر کی ترجیحات", "Package managers": "پیکیج مینیجرز", "Package not found": "پیکیج نہیں ملا", "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": "{manager} سے پیکیج {name}", "Package's default": null, "Packages": "پی<PERSON><PERSON><PERSON>ز", "Packages found: {0}": "پیکیجز ملے: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "ڈیٹا بیس میں ایک درست یو آر ایل چسپاں کریں", "Pause updates for": null, "Perform a backup now": "ابھی بیک اپ کریں", "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": "بیک اپ ہو رہا ہے، براہ کرم انتظار کریں...", "Periodically perform a backup of the installed packages": "وقتاً فوقتاً انسٹال شدہ پیکیجز کا بیک اپ کریں", "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": "براہ کرم اس پیکیج کے لیے انسٹالیشن کے اختیارات چیک کریں اور دوبارہ کوشش کریں۔", "Please click on \"Continue\" to continue": "جاری رکھنے کے لئے براہ کرم \"Continue\" پر کلک کریں", "Please enter at least 3 characters": "براہ کرم کم از کم 3 حروف درج کریں", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "براہ کرم نوٹ کریں کہ اس مشین پر فعال پیکیج مینیجرز کی وجہ سے کچھ پیکجز انسٹال نہیں ہوسکتے ہیں۔", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "براہ کرم نوٹ کریں کہ کچھ ذرائع سے پیکج برآمد نہیں ہوسکتے ہیں۔ انہیں خاکستر کر دیا گیا ہے اور برآمد نہیں کیا جائے گا۔", "Please run UniGetUI as a regular user and try again.": "براہ کرم UniGetUI کو باقاعدہ صارف کے طور پر چلائیں اور دوبارہ کوشش کریں۔", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "براہ کرم کمانڈ لائن آؤٹ پٹ دیکھیں یا مسئلے کے بارے میں مزید معلومات کے لیے آپریشن کی تاریخ دیکھیں۔", "Please select how you want to configure WingetUI": "براہ کرم منتخب کریں کہ آپ WingetUI کو کس طرح ترتیب دینا چاہتے ہیں۔", "Please try again later": "براہ کرم بعد میں دوبارہ کوشش کریں۔", "Please type at least two characters": "براہ کرم کم از کم دو حروف ٹائپ کریں۔", "Please wait": "برائے مہربانی انتظار کریں۔", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "براہ کرم {0} کے انسٹال ہونے تک انتظار کریں۔ ایک سیاہ ونڈو ظاہر ہوسکتی ہے۔ براہ کرم اس کے بند ہونے تک انتظار کریں۔", "Please wait...": "برائے مہربانی انتظار کریں...", "Portable": "پورٹیبل", "Portable mode": "پورٹ ایبل موڈ", "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "پاور شیل کا پیکیج مینیجر۔ PowerShell کی صلاحیتوں کو بڑھانے کے لیے لائبریریاں اور اسکرپٹ تلاش کریں<br>مشتمل ہیں: <b>ماڈیولز، اسکرپٹس، Cmdlets</b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": "پری ریلیز", "Preparing packages, please wait...": "پیکجز تیار ہو رہے ہیں، براہ کرم انتظار کریں...", "Proceed at your own risk.": "اپنی ذمہ داری پر آگے بڑھیں۔", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "اشاعت کی تاریخ:", "Publisher": "پبلشر", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "ازگر کی لائبریری مینیجر۔ ازگر کی لائبریریوں اور ازگر سے متعلق دیگر یوٹیلیٹیز سے بھرا ہوا<br>مشتمل ہے: <b>پائیتھن لائبریریز اور متعلقہ یوٹیلیٹیز</b>", "Quit": "بند کریں", "Quit WingetUI": "WingetUI بند کریں", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": "دوبارہ انسٹال کریں۔", "Reinstall package": "پیکیج دوبارہ انسٹال کریں", "Related settings": null, "Release notes": "ریلیز نوٹس", "Release notes URL": "ریلیز نوٹس یو آر ایل", "Release notes URL:": "ریلیز نوٹس یو آر ایل:", "Release notes:": "ریلیز نوٹس:", "Reload": "دوبارہ لوڈ کریں", "Reload log": "لاگ دوبارہ لوڈ کریں", "Removal failed": "ہٹانا ناکام ہو گیا", "Removal succeeded": "ہٹانا کامیاب ہو گیا", "Remove from list": "فہرست سے ہٹائیں", "Remove permanent data": "مستقل ڈیٹا ہٹائیں", "Remove selection from bundle": "بُنڈل سے انتخاب ہٹائیں", "Remove successful installs/uninstalls/updates from the installation list": "انسٹالیشن لسٹ سے کامیاب انسٹال/ان انسٹال/اپ ڈیٹس کو ہٹا دیں۔", "Removing source {source}": "ماخذ کو ہٹایا جا رہا ہے {source}", "Removing source {source} from {manager}": "{source} کو {manager} سے ہٹایا جا رہا ہے", "Repair UniGetUI": null, "Repair WinGet": "WinGet کی مرمت کریں۔", "Report an issue or submit a feature request": "مسئلہ رپورٹ کریں یا فیچر کی درخواست جمع کروائیں", "Repository": "ریپوزیٹری", "Reset": "ری سیٹ کریں", "Reset Scoop's global app cache": "Scoop کے عالمی ایپ کیشے کو ری سیٹ کریں", "Reset UniGetUI": "UniGetUI کو دوبارہ ترتیب دیں۔", "Reset WinGet": "WinGet کو دوبارہ ترتیب دیں۔", "Reset Winget sources (might help if no packages are listed)": "Winget ذرائع کو ری سیٹ کریں (اگر کوئی پیکیجز نہیں دکھائی دیتے تو یہ مدد کر سکتا ہے)", "Reset WingetUI": "WingetUI کو ری سیٹ کریں", "Reset WingetUI and its preferences": "WingetUI اور اس کی ترجیحات کو ری سیٹ کریں", "Reset WingetUI icon and screenshot cache": "WingetUI آئیکن اور اسکرین شاٹ کیشے کو ری سیٹ کریں", "Reset list": "فہرست کو دوبارہ ترتیب دیں۔", "Resetting Winget sources - WingetUI": "Winget ذرائع کو ری سیٹ کیا جا رہا ہے - WingetUI", "Restart": "دوبارہ شروع کریں۔", "Restart UniGetUI": "UniGetUI دوبارہ شروع کریں", "Restart WingetUI": "WingetUI دوبارہ شروع کریں", "Restart WingetUI to fully apply changes": "تبدیلیوں کو مکمل طور پر نافذ کرنے کے لیے WingetUI دوبارہ شروع کریں", "Restart later": "بعد میں دوبارہ شروع کریں", "Restart now": "ابھی دوبارہ شروع کریں", "Restart required": "دوبارہ شروع کرنا ضروری ہے", "Restart your PC to finish installation": "انسٹالیشن مکمل کرنے کے لیے اپنے پی سی کو دوبارہ شروع کریں", "Restart your computer to finish the installation": "انسٹالیشن مکمل کرنے کے لیے اپنے کمپیوٹر کو دوبارہ شروع کریں", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "دوبارہ کوشش کریں", "Retry as administrator": "بطور منتظم دوبارہ کوشش کریں۔", "Retry failed operations": "ناکام آپریشنز کی دوبارہ کوشش کریں۔", "Retry interactively": "انٹرایکٹو دوبارہ کوشش کریں۔", "Retry skipping integrity checks": "سالمیت کی جانچ کو چھوڑنے کی دوبارہ کوشش کریں۔", "Retrying, please wait...": "دوبارہ کوشش کی جا رہی ہے، براہ کرم انتظار کریں...", "Return to top": "اوپر واپس جائیں", "Run": "چلائیں", "Run as admin": "ایڈمن کے طور پر چلائیں", "Run cleanup and clear cache": "صفائی کریں اور کیشے صاف کریں", "Run last": "آخری چلائیں۔", "Run next": "اگلا چلائیں۔", "Run now": "اب دوڑو", "Running the installer...": "انسٹالر چل رہا ہے...", "Running the uninstaller...": "ان انسٹالر چل رہا ہے...", "Running the updater...": "اپ ڈیٹر چل رہا ہے...", "Save": null, "Save File": "فائل محفوظ کریں", "Save and close": "محفوظ کریں اور بند کریں", "Save as": null, "Save bundle as": "بُنڈل کو اس طور پر محفوظ کریں", "Save now": "ابھی محفوظ کریں", "Saving packages, please wait...": "پیکیجز محفوظ کیے جا رہے ہیں، براہ کرم انتظار کریں...", "Scoop Installer - WingetUI": "Scoop انسٹالر - WingetUI", "Scoop Uninstaller - WingetUI": "Scoop ان انسٹالر - WingetUI", "Scoop package": "<PERSON><PERSON> پیکیج", "Search": "تلاش کریں", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "ڈیسک ٹاپ سافٹ ویئر تلاش کریں، اپ ڈیٹس دستیاب ہونے پر مجھے تنبیہ کریں اور بیہودہ کام نہ کریں۔ میں نہیں چاہتا کہ WingetUI زیادہ پیچیدہ ہو، مجھے صرف ایک سادہ <b>سافٹ ویئر اسٹور</b> چاہیے", "Search for packages": "پیکیجز تلاش کریں", "Search for packages to start": "شروع کرنے کے لئے پیکیجز تلاش کریں", "Search mode": "تلاش کا موڈ", "Search on available updates": "دستیاب اپ ڈیٹس پر تلاش کریں", "Search on your software": "اپنے سافٹ ویئر پر تلاش کریں", "Searching for installed packages...": "نصب شدہ پیکیجز کی تلاش...", "Searching for packages...": "پیکیجز کی تلاش...", "Searching for updates...": "اپ ڈیٹس کی تلاش...", "Select": "منتخب کریں", "Select \"{item}\" to add your custom bucket": "اپنی حسب ضرورت بالٹی شامل کرنے کے لئے \"{item}\" منتخب کریں", "Select a folder": "ایک فولڈر منتخب کریں", "Select all": "سب منتخب کریں", "Select all packages": "سب پیکیجز منتخب کریں", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "صرف منتخب کریں <b>اگر آپ کو معلوم ہے کہ آپ کیا کر رہے ہیں</b>.", "Select package file": "پیکیج فائل منتخب کریں", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": "جس ماخذ کو آپ شامل کرنا چاہتے ہیں اسے منتخب کریں:", "Select upgradable packages by default": "ڈیفالٹ کے لحاظ سے اپ گریڈ ایبل پیکجز کو منتخب کریں۔", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "منتخب کریں کہ کون سے <b>پیکیج مینیجرز</b> کو استعمال کرنا ہے ({0})، ترتیب دیں کہ پیکجز کیسے انسٹال ہوتے ہیں، منتظم کے حقوق کو کیسے ہینڈل کیا جاتا ہے، وغیرہ۔", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "مصافحہ بھیجا۔ مثال کے سامعین کے جواب کا انتظار ہے... ({0}%)", "Set a custom backup file name": "اپنی مرضی کا بیک اپ فائل نام سیٹ کریں", "Set custom backup file name": "اپنی مرضی کا بیک اپ فائل نام سیٹ کریں", "Settings": "ترتیبات", "Share": "شیئر کریں", "Share WingetUI": "WingetUI شیئر کریں", "Share anonymous usage data": "گمنام استعمال کا ڈیٹا شیئر کریں۔", "Share this package": "اس پیکیج کو شیئر کریں", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": "یونی گیٹ یوآئی کو سسٹم ٹرے پر دکھائیں", "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "WingetUI دکھائیں", "Show a notification when an installation fails": "جب انسٹالیشن ناکام ہو تو نوٹیفکیشن دکھائیں", "Show a notification when an installation finishes successfully": "جب انسٹالیشن کامیابی سے مکمل ہو تو نوٹیفکیشن دکھائیں", "Show a notification when an operation fails": "آپریشن ناکام ہونے پر اطلاع دکھائیں۔", "Show a notification when an operation finishes successfully": "جب آپریشن کامیابی سے ختم ہو جائے تو اطلاع دکھائیں۔", "Show a notification when there are available updates": "جب اپ ڈیٹس دستیاب ہوں تو نوٹیفکیشن دکھائیں", "Show a silent notification when an operation is running": "جب کوئی آپریشن چل رہا ہو تو خاموش اطلاع دکھائیں۔", "Show details": "تفصیلات دکھائیں", "Show in explorer": "ایکسپلورر میں دکھائیں۔", "Show info about the package on the Updates tab": "اپ ڈیٹس ٹیب پر پیکیج کے بارے میں معلومات دکھائیں", "Show missing translation strings": "غیر موجود ترجمہ کی سٹرنگز دکھائیں", "Show notifications on different events": "مختلف واقعات پر نوٹیفکیشن دکھائیں", "Show package details": "پیکیج کی تفصیلات دکھائیں", "Show package icons on package lists": "پیکیج کی فہرستوں پر پیکیج کی شبیہیں دکھائیں۔", "Show similar packages": "مشابہ پیکیجز دکھائیں", "Show the live output": "براہ راست آؤٹ پٹ دکھائیں", "Size": "سا<PERSON>ز", "Skip": "چھوڑیں", "Skip hash check": "ہیش چیک چھوڑیں", "Skip hash checks": "ہیش چیکس کو چھوڑ دیں۔", "Skip integrity checks": "سالمیت کی جانچ چھوڑیں", "Skip minor updates for this package": "اس پیکیج کے لیے معمولی اپ ڈیٹس کو چھوڑ دیں۔", "Skip the hash check when installing the selected packages": "منتخب پیکیجز کو انسٹال کرتے وقت ہیش چیک چھوڑیں", "Skip the hash check when updating the selected packages": "منتخب پیکیجز کو اپ ڈیٹ کرتے وقت ہیش چیک چھوڑیں", "Skip this version": "اس ورژن کو چھوڑیں", "Software Updates": "سافٹ ویئر اپ ڈیٹس", "Something went wrong": "کچھ غلط ہو گیا", "Something went wrong while launching the updater.": "اپڈیٹر لانچ کرتے وقت کچھ غلط ہو گیا۔", "Source": "ذریعہ", "Source URL:": "ذریعہ یو آر ایل:", "Source added successfully": "ماخذ کامیابی کے ساتھ شامل ہو گیا۔", "Source addition failed": "ذریعہ شامل کرنا ناکام ہو گیا", "Source name:": "ذریعہ کا نام:", "Source removal failed": "ذریعہ ہٹانا ناکام ہو گیا", "Source removed successfully": "ماخذ کامیابی کے ساتھ ہٹا دیا گیا۔", "Source:": "ذریعہ:", "Sources": "ذرائع", "Start": "شروع کریں", "Starting daemons...": "ڈیمونز شروع ہو رہے ہیں...", "Starting operation...": "آپریشن شروع ہو رہا ہے...", "Startup options": "اسٹارٹ اپ اختیارات", "Status": "حالت", "Stuck here? Skip initialization": "یہاں پھنس گئے؟ ابتدائی سازی چھوڑ دیں", "Suport the developer": "ڈویلپر کی مدد کریں", "Support me": "میری مدد کریں", "Support the developer": "ڈویلپر کی مدد کریں", "Systems are now ready to go!": "سسٹمز اب تیار ہیں!", "Telemetry": null, "Text": "متن", "Text file": "متن کی فائل", "Thank you ❤": "شکریہ ❤", "Thank you 😉": "شکریہ 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "رسٹ پیکیج مینیجر۔<br>پر مشتمل ہے: <b>رسٹ لائبریریز اور پروگرامز جو زنگ میں لکھے گئے ہیں</b>", "The backup will NOT include any binary file nor any program's saved data.": "بیک اپ میں کوئی بائنری فائل شامل نہیں ہوگی اور نہ ہی کسی پروگرام کا محفوظ کردہ ڈیٹا۔", "The backup will be performed after login.": "لاگ ان کے بعد بیک اپ لیا جائے گا۔", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "بیک اپ میں انسٹال شدہ پیکجوں کی مکمل فہرست اور ان کی تنصیب کے اختیارات شامل ہوں گے۔ نظر انداز کیے گئے اپ ڈیٹس اور چھوڑے گئے ورژن بھی محفوظ کیے جائیں گے۔", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "آپ جس بنڈل کو لوڈ کرنے کی کوشش کر رہے ہیں وہ غلط معلوم ہوتا ہے۔ براہ کرم فائل چیک کریں اور دوبارہ کوشش کریں۔", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "انسٹالر کا چیک سم متوقع قیمت سے میل نہیں کھاتا، اور انسٹالر کی صداقت کی تصدیق نہیں کی جا سکتی۔ اگر آپ پبلشر پر بھروسہ کرتے ہیں تو، {0} پیکیج دوبارہ ہیش چیک کو چھوڑ رہا ہے۔", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "ونڈوز کے لیے کلاسیکل پیکیج مینیجر۔ آپ کو وہاں سب کچھ مل جائے گا۔ <br>پر مشتمل ہے: <b>جنرل سافٹ ویئر</b>", "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": "موجودہ بنڈل میں کوئی پیکیج نہیں ہے۔ شروع کرنے کے لیے کچھ پیکجز شامل کریں۔", "The executable file for {0} was not found": "{0} کے لیے قابل عمل فائل نہیں ملی", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "مندرجہ ذیل پیکیجز JSON فائل میں ایکسپورٹ کیے جائیں گے۔ صارف کا کوئی ڈیٹا یا بائنریز محفوظ نہیں کیا جائے گا۔", "The following packages are going to be installed on your system.": "درج ذیل پیکیجز آپ کے سسٹم پر انسٹال ہونے جا رہے ہیں۔", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": "ہر بار جب یہ پیکیج انسٹال، اپ ڈیٹ یا ہٹایا جائے گا تو درج ذیل ترتیبات لاگو ہوں گی۔", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "ہر بار جب یہ پیکیج انسٹال، اپ ڈیٹ یا ہٹایا جائے گا تو درج ذیل ترتیبات لاگو ہوں گی۔ وہ خود بخود محفوظ ہو جائیں گے۔", "The icons and screenshots are maintained by users like you!": "شبیہیں اور اسکرین شاٹس آپ جیسے صارفین کے ذریعہ برقرار رکھے جاتے ہیں!", "The installer authenticity could not be verified.": "انسٹالر کی صداقت کی تصدیق نہیں ہو سکی۔", "The installer has an invalid checksum": "انسٹالر کے پاس ایک غلط چیکسم ہے۔", "The installer hash does not match the expected value.": "انسٹالر ہیش متوقع قدر سے مماثل نہیں ہے۔", "The local icon cache currently takes {0} MB": "مقامی آئیکن کیش فی الحال {0} MB لیتا ہے۔", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "اس پروجیکٹ کا بنیادی ہدف ونڈوز کے لیے سب سے عام CLI پیکیج مینیجرز جیسے ونگیٹ اور اسکوپ کو منظم کرنے کے لیے ایک بدیہی UI بنانا ہے۔", "The package \"{0}\" was not found on the package manager \"{1}\"": "پیکیج مینیجر \"{1}\" پر پیکیج \"{0}\" نہیں ملا", "The package bundle could not be created due to an error.": "ایک خرابی کی وجہ سے پیکیج بنڈل نہیں بنایا جا سکا۔", "The package bundle is not valid": "پیکیج بنڈل درست نہیں ہے۔", "The package manager \"{0}\" is disabled": "پیکیج مینیجر \"{0}\" غیر فعال ہے۔", "The package manager \"{0}\" was not found": "پیکیج مینیجر \"{0}\" نہیں ملا", "The package {0} from {1} was not found.": "{1} سے {0} پیکیج نہیں ملا۔", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "اپ ڈیٹس کی جانچ کرتے وقت یہاں درج پیکیجز کو مدنظر نہیں رکھا جائے گا۔ ان پر ڈبل کلک کریں یا ان کے دائیں جانب کے بٹن پر کلک کریں تاکہ ان کی اپ ڈیٹس کو نظر انداز کرنا بند کر دیں۔", "The selected packages have been blacklisted": "منتخب پیکجز کو بلیک لسٹ کر دیا گیا ہے۔", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "بیک اپ کے سائز کا تخمینہ 1MB سے کم ہے۔", "The source {source} was added to {manager} successfully": "ماخذ {source} کو کامیابی کے ساتھ {manager} میں شامل کر دیا گیا۔", "The source {source} was removed from {manager} successfully": "ماخذ {source} کو {manager} سے کامیابی کے ساتھ ہٹا دیا گیا تھا۔", "The system tray icon must be enabled in order for notifications to work": "اطلاعات کے کام کرنے کے لیے سسٹم ٹرے آئیکن کا فعال ہونا ضروری ہے۔", "The update process has been aborted.": "اپ ڈیٹ کا عمل روک دیا گیا ہے۔", "The update process will start after closing UniGetUI": "اپ ڈیٹ کا عمل UniGetUI کو بند کرنے کے بعد شروع ہوگا۔", "The update will be installed upon closing WingetUI": "WingetUI کو بند کرنے پر اپ ڈیٹ انسٹال ہو جائے گا۔", "The update will not continue.": "اپ ڈیٹ جاری نہیں رہے گا۔", "The user has canceled {0}, that was a requirement for {1} to be run": "صارف نے {0} کو منسوخ کر دیا ہے، جو کہ {1} کو چلانے کی ضرورت تھی۔", "There are no new UniGetUI versions to be installed": "انسٹال کرنے کے لیے کوئی نیا UniGetUI ورژن نہیں ہے۔", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "آپریشنز جاری ہیں۔ WingetUI کو چھوڑنا ان کے ناکام ہونے کا سبب بن سکتا ہے۔ کیا آپ جاری رکھنا چاہتے ہیں؟", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "یوٹیوب پر کچھ بہترین ویڈیوز ہیں جو ونگیٹ یو آئی اور اس کی صلاحیتوں کو ظاہر کرتی ہیں۔ آپ مفید چالیں اور تجاویز سیکھ سکتے ہیں!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "بطور ایڈمنسٹریٹر ونگیٹ یو آئی کو نہ چلانے کی دو اہم وجوہات ہیں۔ پہلا یہ کہ Scoop پیکیج مینیجر منتظم کے حقوق کے ساتھ چلنے پر کچھ کمانڈز کے ساتھ مسائل پیدا کر سکتا ہے۔ دوسرا یہ کہ WingetUI کو بطور ایڈمنسٹریٹر چلانے کا مطلب یہ ہے کہ آپ جو بھی پیکیج ڈاؤن لوڈ کرتے ہیں وہ بطور ایڈمنسٹریٹر چلایا جائے گا (جو محفوظ نہیں ہے)۔ یاد رکھیں کہ اگر آپ کو بطور ایڈمنسٹریٹر ایک مخصوص پیکج انسٹال کرنے کی ضرورت ہے، تو آپ ہمیشہ آئٹم پر دائیں کلک کر سکتے ہیں -> انسٹال/اپ ڈیٹ/این انسٹال بطور ایڈمنسٹریٹر۔", "There is an error with the configuration of the package manager \"{0}\"": "پیکیج مینیجر \"{0}\" کی ترتیب میں ایک خرابی ہے۔", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "ایک تنصیب جاری ہے۔ اگر آپ WingetUI کو بند کرتے ہیں، تو انسٹالیشن ناکام ہو سکتی ہے اور اس کے غیر متوقع نتائج نکل سکتے ہیں۔ کیا آپ اب بھی WingetUI چھوڑنا چاہتے ہیں؟", "They are the programs in charge of installing, updating and removing packages.": "وہ پیکیجز کو انسٹال کرنے، اپ ڈیٹ کرنے اور ہٹانے کے انچارج پروگرام ہیں۔", "Third-party licenses": "تیسری پارٹی کے لائسنس", "This could represent a <b>security risk</b>.": "یہ ایک <b>سیکیورٹی رسک</b> کی نمائندگی کر سکتا ہے۔", "This is not recommended.": "اس کی سفارش نہیں کی جاتی ہے۔", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "یہ شاید اس حقیقت کی وجہ سے ہے کہ آپ کو جو پیکیج بھیجا گیا تھا اسے ہٹا دیا گیا تھا، یا کسی ایسے پیکیج مینیجر پر شائع کیا گیا تھا جسے آپ نے فعال نہیں کیا تھا۔ موصولہ ID ہے {0}", "This is the <b>default choice</b>.": "یہ <b>پہلے سے طے شدہ انتخاب</b> ہے۔", "This may help if WinGet packages are not shown": "اگر WinGet پیکجز نہیں دکھائے گئے ہیں تو اس سے مدد مل سکتی ہے۔", "This may help if no packages are listed": "اس سے مدد مل سکتی ہے اگر کوئی پیکیج درج نہ ہو۔", "This may take a minute or two": "اس میں ایک یا دو منٹ لگ سکتے ہیں۔", "This operation is running interactively.": "یہ آپریشن انٹرایکٹو چل رہا ہے۔", "This operation is running with administrator privileges.": "یہ آپریشن ایڈمنسٹریٹر کی مراعات کے ساتھ چل رہا ہے۔", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "اس پیکج کو اپ ڈیٹ کیا جا سکتا ہے۔", "This package can be updated to version {0}": "اس پیکیج کو ورژن {0} میں اپ ڈیٹ کیا جا سکتا ہے", "This package can be upgraded to version {0}": "اس پیکیج کو ورژن {0} میں اپ گریڈ کیا جا سکتا ہے", "This package cannot be installed from an elevated context.": "اس پیکج کو بلند سیاق و سباق سے انسٹال نہیں کیا جا سکتا۔", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "اس پیکیج میں کوئی اسکرین شاٹس نہیں ہیں یا آئیکن غائب ہے؟ ہمارے کھلے، عوامی ڈیٹا بیس میں گمشدہ آئیکنز اور اسکرین شاٹس کو شامل کرکے WingetUI سے تعاون کریں۔", "This package is already installed": "یہ پیکیج پہلے سے انسٹال ہے۔", "This package is being processed": "اس پیکج پر کارروائی ہو رہی ہے۔", "This package is not available": "یہ پیکیج دستیاب نہیں ہے۔", "This package is on the queue": "یہ پیکج قطار میں ہے۔", "This process is running with administrator privileges": "یہ عمل منتظم کے استحقاق کے ساتھ چل رہا ہے۔", "This project has no connection with the official {0} project — it's completely unofficial.": "اس پروجیکٹ کا آفیشل {0} پروجیکٹ سے کوئی تعلق نہیں ہے — یہ مکمل طور پر غیر سرکاری ہے۔", "This setting is disabled": "یہ ترتیب غیر فعال ہے۔", "This wizard will help you configure and customize WingetUI!": "یہ وزرڈ آپ کو WingetUI کو ترتیب دینے اور اپنی مرضی کے مطابق بنانے میں مدد کرے گا!", "Toggle search filters pane": "تلاش کے فلٹرز پین کو ٹوگل کریں۔", "Translators": "مترجم", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "یہاں نام اور اس سورس کا URL ٹائپ کریں جسے آپ شامل کرنا چاہتے ہیں، اسپیس سے الگ کر کے۔", "Unable to find package": "پیکیج تلاش کرنے سے قاصر", "Unable to load informarion": "معلومات لوڈ کرنے سے قاصر", "UniGetUI collects anonymous usage data in order to improve the user experience.": "UniGetUI صارف کے تجربے کو بہتر بنانے کے لیے گمنام استعمال کا ڈیٹا اکٹھا کرتا ہے۔", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "UniGetUI صارف کے تجربے کو سمجھنے اور بہتر بنانے کے واحد مقصد کے ساتھ گمنام استعمال کا ڈیٹا اکٹھا کرتا ہے۔", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "UniGetUI نے ایک نئے ڈیسک ٹاپ شارٹ کٹ کا پتہ لگایا ہے جسے خود بخود حذف کیا جا سکتا ہے۔", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "UniGetUI نے مندرجہ ذیل ڈیسک ٹاپ شارٹ کٹس کا پتہ لگایا ہے جنہیں مستقبل کے اپ گریڈ پر خود بخود ہٹایا جا سکتا ہے۔", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "UniGetUI نے {0} نئے ڈیسک ٹاپ شارٹ کٹس کا پتہ لگایا ہے جو خود بخود حذف ہو سکتے ہیں۔", "UniGetUI is being updated...": "UniGetUI کو اپ ڈیٹ کیا جا رہا ہے...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "UniGetUI کا تعلق کسی بھی مطابقت پذیر پیکیج مینیجرز سے نہیں ہے۔ UniGetUI ایک آزاد منصوبہ ہے۔", "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": "UniGetUI کو آپریٹ کرنے کے لیے {0} کی ضرورت ہے، لیکن یہ آپ کے سسٹم پر نہیں ملا۔", "UniGetUI startup page:": "UniGetUI آغاز صفحہ:", "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": "UniGetUI ورژن {0} ڈاؤن لوڈ کیا جا رہا ہے۔", "UniGetUI {0} is ready to be installed.": "UniGetUI {0} انسٹال ہونے کے لیے تیار ہے۔", "Uninstall": "ان انسٹال کریں۔", "Uninstall Scoop (and its packages)": "اسکوپ کو ان انسٹال کریں (اور اس کے پیکجز)", "Uninstall and more": null, "Uninstall and remove data": "ان انسٹال کریں اور ڈیٹا کو ہٹا دیں۔", "Uninstall as administrator": "بطور ایڈمنسٹریٹر ان انسٹال کریں۔", "Uninstall canceled by the user!": "ان انسٹال کو صارف نے منسوخ کر دیا!", "Uninstall failed": "اَن انسٹال ناکام ہو گیا۔", "Uninstall options": null, "Uninstall package": "پیکیج کو ان انسٹال کریں۔", "Uninstall package, then reinstall it": "پیکیج کو ان انسٹال کریں، پھر اسے دوبارہ انسٹال کریں۔", "Uninstall package, then update it": "پیکیج کو ان انسٹال کریں، پھر اسے اپ ڈیٹ کریں۔", "Uninstall previous versions when updated": null, "Uninstall selected packages": "منتخب پیکجز کو ان انسٹال کریں۔", "Uninstall selection": null, "Uninstall succeeded": "اَن انسٹال کرنا کامیاب ہو گیا۔", "Uninstall the selected packages with administrator privileges": "ایڈمنسٹریٹر کی مراعات کے ساتھ منتخب پیکجز کو ان انسٹال کریں۔", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "\"{0}\" کے طور پر درج اصل کے ساتھ اَن انسٹال کیے جانے والے پیکیجز کسی بھی پیکیج مینیجر پر شائع نہیں کیے گئے ہیں، اس لیے ان کے بارے میں دکھانے کے لیے کوئی معلومات دستیاب نہیں ہے۔", "Unknown": "نامعلوم", "Unknown size": "نامعلوم سائز", "Unset or unknown": "غیر سیٹ یا نامعلوم", "Up to date": "تازہ ترین", "Update": "اپ ڈیٹ", "Update WingetUI automatically": "WingetUI کو خود بخود اپ ڈیٹ کریں", "Update all": "سب کو اپ ڈیٹ کریں", "Update and more": null, "Update as administrator": "ایڈمنسٹریٹر کے طور پر اپ ڈیٹ کریں", "Update check frequency, automatically install updates, etc.": null, "Update date": "اپ ڈیٹ کی تاریخ", "Update failed": "اپ ڈیٹ ناکام ہو گیا", "Update found!": "اپ ڈیٹ مل گیا!", "Update now": "ابھی اپ ڈیٹ کریں", "Update options": null, "Update package indexes on launch": "لانچ پر پیکیج انڈیکسز کو اپ ڈیٹ کریں", "Update packages automatically": "پیکیجز کو خود بخود اپ ڈیٹ کریں", "Update selected packages": "منتخب پیکیجز کو اپ ڈیٹ کریں", "Update selected packages with administrator privileges": "منتخب پیکیجز کو ایڈمنسٹریٹر کے حقوق کے ساتھ اپ ڈیٹ کریں", "Update selection": null, "Update succeeded": "اپ ڈیٹ کامیاب ہو گیا", "Update to version {0}": "ورژن {0} میں اپ ڈیٹ کریں", "Update to {0} available": "{0} کے لئے اپ ڈیٹ دستیاب ہے", "Update vcpkg's Git portfiles automatically (requires Git installed)": "vcpkg کی Git پورٹ فائلز کو خود بخود اپ ڈیٹ کریں (Git انسٹال کرنے کی ضرورت ہے)", "Updates": "اپ ڈیٹس", "Updates available!": "اپ ڈیٹس دستیاب ہیں!", "Updates for this package are ignored": "اس پیکیج کے لئے اپ ڈیٹس کو نظر انداز کیا گیا ہے", "Updates found!": "اپ ڈیٹس مل گئیں!", "Updates preferences": "اپ ڈیٹس کی ترجیحات", "Updating WingetUI": "WingetUI کو اپ ڈیٹ کیا جا رہا ہے", "Url": "یو آر ایل", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "PowerShell CMDLets کی بجائے پرانی بندھی ہوئی WinGet کا استعمال کریں", "Use a custom icon and screenshot database URL": "ایک حسب ضرورت آئیکن اور اسکرین شاٹ ڈیٹا بیس یو آر ایل کا استعمال کریں", "Use bundled WinGet instead of PowerShell CMDlets": "PowerShell CMDlets کی بجائے بندھی ہوئی WinGet کا استعمال کریں", "Use bundled WinGet instead of system WinGet": "سسٹم WinGet کے بجائے بنڈل WinGet استعمال کریں۔", "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "بندھی ہوئی کی بجائے نصب شدہ GSudo کا استعمال کریں", "Use system Chocolatey": "سسٹم Chocolatey کا استعمال کریں", "Use system Chocolatey (Needs a restart)": "سسٹم Chocolatey کا استعمال کریں (دوبارہ شروع کرنے کی ضرورت ہے)", "Use system Winget (Needs a restart)": "سسٹم Winget کا استعمال کریں (دوبارہ شروع کرنے کی ضرورت ہے)", "Use system Winget (System language must be set to english)": "سسٹم Winget کا استعمال کریں (سسٹم کی زبان انگریزی پر سیٹ ہونی چاہیے)", "Use the WinGet COM API to fetch packages": "پیکیجز کو حاصل کرنے کے لئے WinGet COM API کا استعمال کریں", "Use the WinGet PowerShell Module instead of the WinGet COM API": "WinGet COM API کے بجائے WinGet PowerShell ماڈیول استعمال کریں۔", "Useful links": "م<PERSON><PERSON><PERSON> لنکس", "User": "صارف", "User interface preferences": "صارف انٹرفیس کی ترجیحات", "User | Local": "صارف | مقامی", "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "WingetUI کا استعمال کرنے کا مطلب ہے کہ آپ نے GNU Lesser General Public License v2.1 لائسنس کو قبول کر لیا ہے", "Using WingetUI implies the acceptation of the MIT License": "WingetUI کا استعمال کرنے کا مطلب ہے کہ آپ نے MIT لائسنس کو قبول کر لیا ہے", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Vcpkg جڑ نہیں ملی۔ براہ کرم %VCPKG_ROOT% ماحولیاتی متغیر کی وضاحت کریں یا UniGetUI ترتیبات سے اس کی وضاحت کریں", "Vcpkg was not found on your system.": "آپ کے سسٹم پر Vcpkg نہیں ملا۔", "Verbose": "تفصیل سے", "Version": "ورژن", "Version to install:": "انسٹال کرنے کے لئے ورژن:", "Version:": null, "View GitHub Profile": "GitHub پروفائل دیکھیں", "View WingetUI on GitHub": "GitHub پر WingetUI دیکھیں", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "WingetUI کا سورس کوڈ دیکھیں۔ یہاں سے، آپ بگز رپورٹ کر سکتے ہیں، فیچرز تجویز کر سکتے ہیں، یا WingetUI پروجیکٹ میں براہ راست حصہ ڈال سکتے ہیں", "View mode:": null, "View on UniGetUI": "UniGetUI پر دیکھیں", "View page on browser": "براؤزر میں صفحہ دیکھیں", "View {0} logs": "{0} لاگز دیکھیں", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "انٹرنیٹ کنیکٹیویٹی کی ضرورت کے کاموں کو کرنے کی کوشش کرنے سے پہلے ڈیوائس کے انٹرنیٹ سے منسلک ہونے کا انتظار کریں۔", "Waiting for other installations to finish...": "دوسری انسٹالیشنز کے ختم ہونے کا انتظار کر رہے ہیں...", "Waiting for {0} to complete...": "{0} کے مکمل ہونے کا انتظار ہے...", "Warning": "انتباہ", "Warning!": "وارننگ!", "We are checking for updates.": "ہم اپ ڈیٹس کی جانچ کر رہے ہیں۔", "We could not load detailed information about this package, because it was not found in any of your package sources": "ہم اس پیکیج کے بارے میں تفصیلی معلومات لوڈ نہیں کر سکے، کیونکہ یہ آپ کے پیکیج کے کسی بھی ذرائع میں نہیں ملی", "We could not load detailed information about this package, because it was not installed from an available package manager.": "ہم اس پیکیج کے بارے میں تفصیلی معلومات لوڈ نہیں کر سکے، کیونکہ یہ دستیاب پیکیج مینیجر سے انسٹال نہیں ہوا تھا۔", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "ہم {action} {package} نہیں کر سکے۔ براہ کرم بعد میں دوبارہ کوشش کریں۔ انسٹالر سے لاگز حاصل کرنے کے لیے \"{showDetails}\" پر کلک کریں۔", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "ہم {action} {package} نہیں کر سکے۔ براہ کرم بعد میں دوبارہ کوشش کریں۔ ان انسٹالر سے لاگز حاصل کرنے کے لیے \"{showDetails}\" پر کلک کریں۔", "We couldn't find any package": "ہمیں کوئی پیکج نہیں مل سکا", "Welcome to WingetUI": "WingetUI میں خوش آمدید", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "آپ کون سے پیکیج مینیجرز کو استعمال کرنا چاہتے ہیں؟", "Which source do you want to add?": "آپ کون سا ذریعہ شامل کرنا چاہتے ہیں؟", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "جب کہ Winget کو WingetUI کے اندر استعمال کیا جا سکتا ہے، WingetUI کو دوسرے پیکیج مینیجرز کے ساتھ استعمال کیا جا سکتا ہے، جو مبہم ہو سکتا ہے۔ ماضی میں، ونگیٹ یو آئی کو صرف وِنگٹ کے ساتھ کام کرنے کے لیے ڈیزائن کیا گیا تھا، لیکن اب یہ سچ نہیں ہے، اور اس لیے وِنگیٹ یو آئی اس بات کی نمائندگی نہیں کرتا ہے کہ یہ پروجیکٹ کیا بننا چاہتا ہے۔", "WinGet could not be repaired": "WinGet کی مرمت نہیں ہو سکی", "WinGet malfunction detected": "WinGet کی خرابی کا پتہ چلا", "WinGet was repaired successfully": "WinGet کو کامیابی کے ساتھ ٹھیک کیا گیا۔", "WingetUI": "ونگیٹ یو آئی", "WingetUI - Everything is up to date": "WingetUI - سب کچھ تازہ ترین ہے", "WingetUI - {0} updates are available": "WingetUI - {0} اپ ڈیٹس دستیاب ہیں", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "WingetUI ہوم پیج", "WingetUI Homepage - Share this link!": "WingetUI ہوم پیج - اس لنک کو شیئر کریں!", "WingetUI License": "WingetUI لائسنس", "WingetUI Log": "WingetUI لاگ", "WingetUI Repository": "WingetUI ریپوزیٹری", "WingetUI Settings": "WingetUI سیٹنگز", "WingetUI Settings File": "WingetUI سیٹنگز فائل", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI درج ذیل لائبریریوں کا استعمال کرتا ہے۔ ان کے بغیر، WingetUI ممکن نہیں ہوتا۔", "WingetUI Version {0}": "WingetUI ورژن {0}", "WingetUI autostart behaviour, application launch settings": "WingetUI آٹو اسٹارٹ رویہ، ایپلیکیشن لانچ کی ترتیبات", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI چیک کر سکتا ہے کہ آیا آپ کے سافٹ ویئر میں اپ ڈیٹس دستیاب ہیں، اور اگر آپ چاہیں تو انہیں خود بخود انسٹال کر سکتے ہیں۔", "WingetUI display language:": "WingetUI ڈسپلے زبان:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "WingetUI کو بطور ایڈمنسٹریٹر چلایا گیا ہے، جس کی سفارش نہیں کی جاتی ہے۔ WingetUI کو بطور ایڈمنسٹریٹر چلاتے وقت، WingetUI سے شروع کیے گئے ہر آپریشن میں ایڈمنسٹریٹر کی مراعات ہوں گی۔ آپ اب بھی پروگرام استعمال کر سکتے ہیں، لیکن ہم ایڈمنسٹریٹر کی مراعات کے ساتھ WingetUI نہ چلانے کی انتہائی سفارش کرتے ہیں۔", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "رضاکار مترجمین کی بدولت WingetUI کا 40 سے زیادہ زبانوں میں ترجمہ ہو چکا ہے۔ شکریہ 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI کا مشینی ترجمہ نہیں کیا گیا ہے۔ درج ذیل صارفین ترجمے کے ذمہ دار رہے ہیں:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "WingetUI ایک ایسی ایپلی کیشن ہے جو آپ کے کمانڈ لائن پیکج مینیجرز کے لیے ایک آل ان ون گرافیکل انٹرفیس فراہم کر کے آپ کے سافٹ ویئر کا انتظام آسان بناتی ہے۔", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "WingetUI (جو انٹرفیس آپ ابھی استعمال کر رہے ہیں) اور وِنگیٹ (مائیکروسافٹ کے ذریعہ تیار کردہ ایک پیکیج مینیجر جس سے میرا کوئی تعلق نہیں ہے) کے درمیان فرق پر زور دینے کے لیے WingetUI کا نام تبدیل کیا جا رہا ہے۔", "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI کو اپ ڈیٹ کیا جا رہا ہے۔ ختم ہونے پر، WingetUI خود کو دوبارہ شروع کر دے گا۔", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "WingetUI مفت ہے، اور یہ ہمیشہ کے لیے مفت رہے گا۔ کوئی اشتہار نہیں، کوئی کریڈٹ کارڈ نہیں، کوئی پریمیم ورژن نہیں۔ 100% مفت، ہمیشہ کے لیے۔", "WingetUI log": "WingetUI لاگ", "WingetUI tray application preferences": "WingetUI ٹرے ایپلیکیشن کی ترجیحات", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "WingetUI درج ذیل لائبریریوں کا استعمال کرتا ہے۔ ان کے بغیر، WingetUI ممکن نہیں ہوتا۔", "WingetUI version {0} is being downloaded.": "WingetUI ورژن {0} ڈاؤن لوڈ ہو رہا ہے۔", "WingetUI will become {newname} soon!": "WingetUI جلد ہی {newname} بن جائے گا!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI وقتا فوقتا اپ ڈیٹس کی جانچ نہیں کرے گا۔ لانچ کے وقت بھی ان کی جانچ پڑتال کی جائے گی، لیکن آپ کو ان کے بارے میں خبردار نہیں کیا جائے گا۔", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "ہر بار جب پیکج کو انسٹال کرنے کے لیے بلندی کی ضرورت ہوتی ہے تو WingetUI UAC پرامپٹ دکھائے گا۔", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "WingetUI کو جلد ہی {newname} کا نام دیا جائے گا۔ یہ درخواست میں کسی تبدیلی کی نمائندگی نہیں کرے گا۔ میں (ڈیولپر) اس پروجیکٹ کی ترقی جاری رکھوں گا جیسا کہ میں ابھی کر رہا ہوں، لیکن ایک مختلف نام سے۔", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "ونگیٹ یو آئی ہمارے پیارے شراکت داروں کی مدد سے ممکن نہیں تھا۔ ان کا GitHub پروفائل دیکھیں، WingetUI ان کے بغیر ممکن نہیں ہوگا!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "WingetUI تعاون کرنے والوں کی مدد کے بغیر ممکن نہیں تھا۔ آپ سب کا شکریہ 🥳", "WingetUI {0} is ready to be installed.": "WingetUI {0} انسٹال ہونے کے لیے تیار ہے۔", "Write here the process names here, separated by commas (,)": null, "Yes": "جی ہاں", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": "آپ کے پاس فی الحال ورژن {0} انسٹال ہے۔", "You have installed WingetUI Version {0}": "آپ نے WingetUI ورژن {0} انسٹال کیا ہے", "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": "WingetUI کے ساتھ استعمال کرنے کے لیے آپ کو {pm} انسٹال کرنے کی ضرورت پڑ سکتی ہے۔", "You may restart your computer later if you wish": "اگر آپ چاہیں تو آپ اپنے کمپیوٹر کو بعد میں دوبارہ شروع کر سکتے ہیں۔", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "آپ کو صرف ایک بار اشارہ کیا جائے گا، اور ایڈمنسٹریٹر کے حقوق ان پیکجوں کو دیے جائیں گے جو ان کی درخواست کرتے ہیں۔", "You will be prompted only once, and every future installation will be elevated automatically.": "آپ کو صرف ایک بار اشارہ کیا جائے گا، اور ہر مستقبل کی تنصیب خود بخود بلند ہو جائے گی۔", "You will likely need to interact with the installer.": "آپ کو ممکنہ طور پر انسٹالر کے ساتھ بات چیت کرنے کی ضرورت ہوگی۔", "[RAN AS ADMINISTRATOR]": "[ایڈمنسٹریٹر کے طور پر چلایا گیا]", "buy me a coffee": "مجھے کافی خریدیں", "extracted": "نکالا گیا", "feature": "خصوصیت", "formerly WingetUI": "پہلے WingetUI", "homepage": "<PERSON>و<PERSON> پیج", "install": "انسٹال کریں", "installation": "انسٹالیشن", "installed": "انسٹال ہو چکا ہے", "installing": "انسٹال ہو رہا ہے", "library": "لائبریری", "mandatory": null, "option": "اختیار", "optional": null, "uninstall": "ان انسٹال کریں", "uninstallation": "ان انسٹالیشن", "uninstalled": "ان انسٹال ہو چکا ہے", "uninstalling": "ان انسٹال ہو رہا ہے", "update(noun)": "اپ ڈیٹ (اسم)", "update(verb)": "اپ ڈیٹ (فعل)", "updated": "اپ ڈیٹ ہو چکا ہے", "updating": "اپ ڈیٹ ہو رہا ہے", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} ان انسٹالیشن", "{0} aborted": "{0} منسوخ ہو گیا", "{0} can be updated": "{0} کو اپ ڈیٹ کیا جا سکتا ہے", "{0} can be updated to version {1}": "{0} کو ورژن {1} میں اپ ڈیٹ کیا جا سکتا ہے", "{0} days": "{0} دن", "{0} desktop shortcuts created": "{0} ڈیسک ٹاپ شارٹ کٹس بنائے گئے۔", "{0} failed": "{0} ناکام ہو گیا", "{0} has been installed successfully.": "{0} کامیابی سے انسٹال ہو گیا ہے۔", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "{0} کامیابی سے انسٹال ہو گیا ہے۔ تنصیب کو مکمل کرنے کے لیے UniGetUI کو دوبارہ شروع کرنے کی سفارش کی جاتی ہے۔", "{0} has failed, that was a requirement for {1} to be run": "{0} ناکام ہو گیا ہے، یہ {1} کو چلانے کی ضرورت تھی۔", "{0} homepage": "{0} <PERSON>و<PERSON> پیج", "{0} hours": "{0} گھنٹے", "{0} installation": "{0} انسٹالیشن", "{0} installation options": "{0} انسٹالیشن کے اختیارات", "{0} installer is being downloaded": "{0} انسٹالر ڈاؤن لوڈ ہو رہا ہے۔", "{0} is being installed": "{0} انسٹال ہو رہا ہے۔", "{0} is being uninstalled": "{0} کو اَن انسٹال کیا جا رہا ہے۔", "{0} is being updated": "{0} اپ ڈیٹ ہو رہا ہے", "{0} is being updated to version {1}": "{0} ورژن {1} میں اپ ڈیٹ ہو رہا ہے", "{0} is disabled": "{0} غیر فعال ہے", "{0} minutes": "{0} من<PERSON>", "{0} months": "{0} م<PERSON><PERSON>نے", "{0} packages are being updated": "{0} پیکیجز اپ ڈیٹ ہو رہے ہیں", "{0} packages can be updated": "{0} پیکیجز کو اپ ڈیٹ کیا جا سکتا ہے", "{0} packages found": "{0} پیکیجز ملے", "{0} packages were found": "{0} پیکیجز ملے", "{0} packages were found, {1} of which match the specified filters.": "{0} پیکیجز ملے، جن میں سے {1} مخصوص فلٹرز کے مطابق ہیں", "{0} settings": "{0} ترتیبات", "{0} status": null, "{0} succeeded": "{0} کامیاب ہو گیا", "{0} update": "{0} اپ ڈیٹ", "{0} updates are available": "{0} اپ ڈیٹس دستیاب ہیں", "{0} was {1} successfully!": "{0} {1} کامیابی سے ہوا!", "{0} weeks": "{0} ہ<PERSON><PERSON>ے", "{0} years": "{0} سال", "{0} {1} failed": "{0} {1} ناکام ہو گیا", "{package} Installation": "{package} انسٹالیشن", "{package} Uninstall": "{package} ان انسٹال", "{package} Update": "{package} اپ ڈیٹ", "{package} could not be installed": "{package} انسٹال نہیں ہو سکا", "{package} could not be uninstalled": "{package} ان انسٹال نہیں ہو سکا", "{package} could not be updated": "{package} اپ ڈیٹ نہیں ہو سکا", "{package} installation failed": "{package} انسٹالیشن ناکام ہو گئی", "{package} installer could not be downloaded": "{package} انسٹالر ڈاؤن لوڈ نہیں ہو سکا", "{package} installer download": "{package} انسٹالر ڈاؤن لوڈ", "{package} installer was downloaded successfully": "{package} انسٹالر کامیابی سے ڈاؤن لوڈ ہو گیا۔", "{package} uninstall failed": "{package} ان انسٹال ناکام ہو گیا", "{package} update failed": "{package} اپ ڈیٹ ناکام ہو گیا", "{package} update failed. Click here for more details.": "{package} اپ ڈیٹ ناکام ہو گیا۔ مزید تفصیلات کے لیے یہاں کلک کریں۔", "{package} was installed successfully": "{package} کامیابی سے انسٹال ہو گیا", "{package} was uninstalled successfully": "{package} کامیابی سے ان انسٹال ہو گیا", "{package} was updated successfully": "{package} کامیابی سے اپ ڈیٹ ہو گیا", "{pcName} installed packages": "{pcName} پر انسٹال شدہ پیکیجز", "{pm} could not be found": "{pm} نہیں ملا", "{pm} found: {state}": "{pm} ملا: {state}", "{pm} is disabled": "{pm} غیر فعال ہے", "{pm} is enabled and ready to go": "{pm} فعال ہے اور تیار ہے", "{pm} package manager specific preferences": "{pm} پیکیج منیجر کی مخصوص ترجیحات", "{pm} preferences": "{pm} کی ترجیحات", "{pm} version:": "{pm} ورژن:", "{pm} was not found!": "{pm} نہیں ملا!"}