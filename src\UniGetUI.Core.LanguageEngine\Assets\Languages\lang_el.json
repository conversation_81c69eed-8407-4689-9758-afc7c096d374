{"\"{0}\" is a local package and can't be shared": "Το «{0}» ε<PERSON><PERSON><PERSON><PERSON> ένα τοπικό πακέτο και δεν μπορεί να κοινοποιηθεί", "\"{0}\" is a local package and does not have available details": "Το «{0}» ε<PERSON>ν<PERSON><PERSON> ένα τοπικό πακέτο και δεν έχει διαθέσιμες λεπτομέρειες", "\"{0}\" is a local package and is not compatible with this feature": "Το «{0}» ε<PERSON><PERSON><PERSON><PERSON> ένα τοπικό πακέτο και δεν είναι συμβατό με αυτό το χαρακτηριστικό", "(Last checked: {0})": "(Τελευ<PERSON><PERSON><PERSON><PERSON> έλεγχος: {0})", "(Number {0} in the queue)": "(Αρι<PERSON><PERSON><PERSON><PERSON> {0} στην ουρά)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "@antwnhsx, @wobblerrrgg, @thunderstrike116, @seijind, @panos78", "0 packages found": "Βρέθηκαν 0 πακέτα", "0 updates found": "Βρέθηκαν 0 ενημερώσεις", "1 - Errors": "1 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>α", "1 day": "1 ημέρα", "1 hour": "1 ώρα", "1 month": "1 μήνας", "1 package was found": "Βρέθηκε 1 πακέτο", "1 update is available": "1 ενημέρωση είναι διαθέσιμη", "1 week": "1 εβδομάδα", "1 year": "1 έτος", "1. Navigate to the \"{0}\" or \"{1}\" page.": "1. Πλοηγειθείτε στη σελίδα «{0}» ή στη «{1}».", "2 - Warnings": "2 - Προειδοποιήσεις", "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": "2. Εντ<PERSON><PERSON><PERSON>στε το(α) πακέτο(α) που θέλετε να προσθέσετε στη συλλογή και επιλογή του(ων) με το αριστερό πλαίσιο ελέγχου", "3 - Information (less)": "3 - Πληροφορί<PERSON><PERSON> (λιγότερες)", "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": "3. Όταν τα πακέτα που θέλετε να προσθέσετε στη συλλογή επιλέγονται, βρείτε και πατήστε την επιλογή «{0}» στη γραμμή εργαλείων.", "4 - Information (more)": "4 - Πληροφορ<PERSON><PERSON><PERSON> (περισσότερες)", "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": "4. Τα πακέτα σας θα έχουν προστεθεί στη συλλογή. Μπορείτε να συνεχίσετε να προσθέτετε πακέτα ή να εξάγετε τη συλλογή.", "5 - information (debug)": "5 - πληροφορ<PERSON><PERSON><PERSON> (εντοπισμ<PERSON>ς σφαλμάτων)", "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": "Ενας δημοφιλής διαχειριστής βιβλιοθήκης C/C++. Πλήρεις βιβλιοθήκες C/C++ και άλλα βοηθητικά προγράμματα που σχετίζονται με C/C++<br>Περιέχει: <b>Βιβλιοθήκες C/C++ και σχετικά βοηθητικά προγράμματα</b>", "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": "Ενα αποθετήριο γεμάτο εργαλεία και εκτελέσιμα αρχεία σχεδιασμένο με βάση το οικοσύστημα .NET της Microsoft.<br>Περιέχει: <b>Εργαλεί<PERSON> και κώδικες σχετικά με το .NET</b>", "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Ενα αποθετήριο γεμάτο εργαλεία σχεδιασμένο με βάση το οικοσύστημα .NET της Microsoft.<br>Περιέχει: <b>Εργαλεία σχετικά με το .NET</b>", "A restart is required": "Απαιτείτ<PERSON><PERSON> επανεκκίνηση", "Abort install if pre-install command fails": "Διακο<PERSON>ή εγκατάστασης εάν η εντολή πριν την εγκατάσταση αποτύχει", "Abort uninstall if pre-uninstall command fails": "Διακοπή απεγκατάστα<PERSON>ης εάν η εντολή πριν την απεγκατάσταση αποτύχει", "Abort update if pre-update command fails": "Διακο<PERSON><PERSON> εγκατάστασης εάν η εντολή πριν την ενημέρωση αποτύχει", "About": "Σχετικά", "About Qt6": "Σχετικά με το Qt6", "About WingetUI": "Σχετικά με το UniGetUI", "About WingetUI version {0}": "Σχετικά με την έκδοση {0} του UniGetUI", "About the dev": "Σχετικά με τον δημιουργό", "Accept": "Αποδοχή", "Action when double-clicking packages, hide successful installations": "Ενέργεια διπλού πατήματος σε πακέτα, απόκρυψη επιτυχών εγκαταστάσεων", "Add": "Προσθήκη", "Add a source to {0}": "Προσθήκη μιας προέλευσης στο {0}", "Add a timestamp to the backup file names": "Προσθήκη χρονοσφραγίδας στα ονόματα των αρχείων αντιγράφων ασφαλείας", "Add a timestamp to the backup files": "Προσθήκη χρονοσφραγίδας στα αρχεία αντιγράφων ασφαλείας", "Add packages or open an existing bundle": "Προσθήκη πακέτων ή άνοιγμα υπάρχουσας συλλογής", "Add packages or open an existing package bundle": "Προσθήκη πακέτων ή άνοιγμα υπάρχουσας συλλογής πακέτων", "Add packages to bundle": "Προσθήκη πακέτων στη συλλογή", "Add packages to start": "Προσθήκη πακέτων για εκκίνηση", "Add selection to bundle": "Προσθήκη επιλογής στη συλλογή", "Add source": "Προσθήκη προέλευσης", "Add updates that fail with a 'no applicable update found' to the ignored updates list": "Προσθήκη ενημερώσεων που απέτυχαν με την ετικέτα «δε βρέθηκε καμιά ενημέρωση εφαρμογής» στη λίστα αγνόησης ενημερώσεων", "Adding source {source}": "Προσθήκη προέλευσης {source}", "Adding source {source} to {manager}": "Προσθήκη προέλευσης {source} στο {manager}", "Addition succeeded": "Η προσθήκη πραγματοποιήθηκε", "Administrator privileges": "Προνόμια διαχειριστή", "Administrator privileges preferences": "Προτιμήσεις προνομίων διαχειριστή", "Administrator rights": "Δικαιώματα διαχειριστή", "Administrator rights and other dangerous settings": "Δικαιώματα διαχειριστή και άλλες επικίνδυνες ρυθμίσεις", "Advanced options": "Επιλογ<PERSON>ς για προχωρημένους", "All files": "Ολα τα αρχεία", "All versions": "Ολες οι εκδόσεις", "Allow changing the paths for package manager executables": "Να επιτρέπεται αλλαγή των διαδρομών για τα εκτελέσιμα αρχεία του διαχειριστή πακέτων", "Allow custom command-line arguments": "Να επιτρέπονται προσαρμοσμένα ορίσματα γραμμής εντολών", "Allow importing custom command-line arguments when importing packages from a bundle": "Να επιτρέπεται η εισαγωγή προσαρμοσμένων ορισμάτων γραμμής εντολών κατά την εισαγωγή πακέτων από μια συλλογή", "Allow importing custom pre-install and post-install commands when importing packages from a bundle": "Επιτρέψτε την εισαγωγή προσαρμοσμένων εντολών πριν και μετά την εγκατάσταση κατά την εισαγωγή πακέτων από μια συλλογή", "Allow package operations to be performed in parallel": "Δυνατότητα παράλληλης εκτέλεσης λειτουργιών πακέτου", "Allow parallel installs (NOT RECOMMENDED)": "Δυνατότητα παράλληλων εγκαταστάσεων (ΔΕΝ ΣΥΝΙΣΤΑΤΑΙ)", "Allow pre-release versions": "Να επιτρέπονται pre-release εκδόσεις ", "Allow {pm} operations to be performed in parallel": "Δυνατότητα παράλληλης εκτέλεσης εργασιών {pm}", "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": "Εναλ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, μπορείτε να εγκαταστήσετε το {0} εκτελόντας την ακόλουθη εντολή στο Windows PowerShell:", "Always elevate {pm} installations by default": "Να γίνεται πάντα αύξηση δικαιωμάτων εγκαταστάσεων {pm} από προεπιλογή", "Always run {pm} operations with administrator rights": "Πάντα εκτέλεση εργασιών {pm} με δικαιώματα διαχειριστή", "An error occurred": "Παρου<PERSON><PERSON>ά<PERSON>τηκε σφάλμα", "An error occurred when adding the source: ": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON><PERSON> ένα σφάλμα κατά την προσθήκη της προέλευσης:", "An error occurred when attempting to show the package with Id {0}": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON>ε σφάλμα κατά την προσπάθεια προβολής του πακέτου με την ταυτότητα {0}", "An error occurred when checking for updates: ": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά τον έλεγχο για ενημερώσεις:", "An error occurred while loading a backup: ": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>ηκε σφάλμα κατά τη φόρτωση ενός αντιγράφου ασφαλείας:", "An error occurred while logging in: ": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά τη σύνδεση:", "An error occurred while processing this package": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON>ε σφάλμα κατά την επεξεργασία αυτού του πακέτου", "An error occurred:": "Παρου<PERSON><PERSON>ά<PERSON>τηκε σφάλμα:", "An interal error occurred. Please view the log for further details.": "Παρουσ<PERSON>ά<PERSON>τηκε εσωτερικό σφάλμα. Δείτε το αρχείο καταγραφής για περισσότερες λεπτομέρειες.", "An unexpected error occurred:": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε μη αναμενόμενο σφάλμα: ", "An unexpected issue occurred while attempting to repair WinGet. Please try again later": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε μη αναμενόμενο θέμα κατά την προσπάθεια επιδιόρθωσης του WinGet. Δοκιμάστε ξανά αργότερα", "An update was found!": "Βρέθηκε ενημέρωση!", "Android Subsystem": "Υποσύστημα Android", "Another source": "Αλλη προέλευση", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": "Οποιαδήποτε νέα συντόμευση που δημιουργήθηκε σε εγκατάσταση ή ενημέρωση θα διαγραφεί αυτόματα αντί της εμφάνισης επιβεβαίωσης την πρώτη φορά που θα ανιχνευτούν.", "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": "Οποιεσδήποτε συντομεύσεις που δημιουργήθηκαν ή τροποποιήθηκαν εκτός του UniGetUI θα αγνοηθούν. Θα μπορέσετε να τις προσθέσετε μέσω του κουμπιού {0}.", "Any unsaved changes will be lost": "Όλες οι μη αποθηκευμένες αλλαγές θα χαθούν", "App Name": "Ονομα Εφαρμογής", "Appearance": "Εμφάνιση", "Application theme, startup page, package icons, clear successful installs automatically": "Θέμα εφαρμογής, σελί<PERSON><PERSON> εκκίνηση<PERSON>, εικονίδ<PERSON>α πακέτου, εκκαθάριση επιτυχών εγκαταστάσεων αυτόματα", "Application theme:": "Θέμα εφαρμογής:", "Apply": "Εφαρμογή", "Architecture to install:": "Αρχιτεκτονική για εγκατάσταση:", "Are these screenshots wron or blurry?": "Τα στιγμιότυπα οθόνης είναι εσφαλμένα ή θολά;", "Are you really sure you want to enable this feature?": "Θέλετε πραγματικά να ενεργοποιήσετε αυτό το χαρακτηριστικό;", "Are you sure you want to create a new package bundle? ": "Θέλετε να δημιουργήσετε νέα συλλογή πακέτων;", "Are you sure you want to delete all shortcuts?": "Θέλετε να διαγράψετε όλες τις συντομεύσεις;", "Are you sure?": "Σίγουρα;", "Ascendant": "Πρωτεύων", "Ask for administrator privileges once for each batch of operations": "Ερώτημα για δικαιώματα διαχειριστή μία φορά για κάθε δέσμη εργασιών", "Ask for administrator rights when required": "Ερώτημα για δικαιώματα διαχειριστή όταν απαιτούνται", "Ask once or always for administrator rights, elevate installations by default": "Ερώτηση μια φορά ή πάντα για δικαιώματα διαχειριστή, αύξηση δικαιωμάτων των εγκαταστάσεων από προεπιλογή", "Ask only once for administrator privileges": "Ερώτηση μόνο μια φορά για δικαιώματα διαχειριστή", "Ask only once for administrator privileges (not recommended)": "Ερώτηση μόνο μια φορά για δικαιώματα διαχειριστή (δεν συνιστάται)", "Ask to delete desktop shortcuts created during an install or upgrade.": "Ερώτηση πριν τη διαγραφή των συντομεύσεων επιφάνειας εργασίας που δημιουργήθηκαν κατά την εγκατάσταση ή την αναβάθμιση.", "Attention required": "Απαιτείται προσοχή", "Authenticate to the proxy with an user and a password": "Πιστοποίηση στο διακομιστή με όνομα χρήστη και κωδικό πρόσβασης", "Author": "Εκδότης", "Automatic desktop shortcut remover": "Αυτόματο πρόγραμμα απομάκρυνσης συντόμευσης επιφάνειας εργασίας", "Automatically save a list of all your installed packages to easily restore them.": "Αυτόματη αποθήκευση λίστας με όλα τα εγκατεστημένα πακέτα σας για εύκολη επαναφορά τους.", "Automatically save a list of your installed packages on your computer.": "Αυτόματη αποθήκευση λίστας με τα εγκατεστημένα πακέτα στον υπολογιστή σας.", "Autostart WingetUI in the notifications area": "Αυτόματη εκκίνηση του UniGetUI στην περιοχή ειδοποιήσεων", "Available Updates": "Διαθέσιμες Ενημερώσεις", "Available updates: {0}": "Διαθέσιμες ενημερώσεις: {0}", "Available updates: {0}, not finished yet...": "Διαθέσιμες ενημερώσεις: {0}, δεν έχουν ολοκληρωθεί ακόμα...", "Backing up packages to GitHub Gist...": "Δημιουργ<PERSON>α αντιγράφων ασφαλείας πακέτων στο GitHub Gist...", "Backup": "Aντίγραφο ασφαλείας", "Backup Failed": "Η δημιουργία αντιγράφων ασφαλείας απέτυχε", "Backup Successful": "Η δημιουργία αντιγράφων ασφαλείας ολοκληρώθηκε με επιτυχία", "Backup and Restore": "Αντίγραφα ασφαλείας και επαναφορά", "Backup installed packages": "Δημιουργ<PERSON><PERSON> αντιγράφου ασφαλείας εγκατεστημένων πακέτων ", "Backup location": "Θέση αντιγράφου ασφαλείας", "Become a contributor": "Γίνετε συνεισφέρων", "Become a translator": "Γίνετε μεταφραστής", "Begin the process to select a cloud backup and review which packages to restore": "Ξεκινήστε τη διαδικασία επιλογής ενός αντιγράφου ασφαλείας στο cloud και ελέγξτε ποια πακέτα θα επαναφέρετε", "Beta features and other options that shouldn't be touched": "Δοκι<PERSON>αστι<PERSON><PERSON>ς λειτουργίες και άλλες ρυθμίσεις που δε θα έπρεπε να αγγιχτούν", "Both": "Και τα δύο", "Bundle security report": "Αναφορά ασφαλείας συλλογής", "But here are other things you can do to learn about WingetUI even more:": "Ομως εδώ είναι άλλα πράγματα που μπορείτε να κάνετε για να μάθετε περισσότερα για το UnigetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": "Με την απενεργοποίηση ενός διαχειριστή πακέτων, δε θα μπορείτε πλέον να βλέπετε ή να ενημερώνετε τα πακέτα του.", "Cache administrator rights and elevate installers by default": "Αποθήκευση δικαιωμάτων διαχειριστή στη μνήμη cache και αύξηση δικαιωμάτων των προγραμμάτων εγκατάστασης από προεπιλογή", "Cache administrator rights, but elevate installers only when required": "Αποθήκευση δικαιωμάτων διαχειριστή στη μνήμη cache, αλλά αύξηση δικαιωμάτων των προγραμμάτων εγκατάστασης μόνο όταν απαιτείται", "Cache was reset successfully!": "Η επαναφορά της μνήμης cache έγινε με επιτυχία!", "Can't {0} {1}": "Αδύνατη η {0} του {1}", "Cancel": "Άκυρο", "Cancel all operations": "Ακύρωση όλων των λειτουργιών", "Change backup output directory": "Αλλαγή φακέλου εξαγωγής αντιγράφου ασφαλείας", "Change default options": "Αλλαγή προεπιλεγμένων επιλογών", "Change how UniGetUI checks and installs available updates for your packages": "Αλλαγή του τρόπου με τον οποίο το UniGetUI ελέγχει και εγκαθιστά τις διαθέσιμες ενημερώσεις για τα πακέτα σας", "Change how UniGetUI handles install, update and uninstall operations.": "Αλλαγή του χειρισμού της εγκατάστασης, της ενημέρωσης και της απεγκατάστασης από το UniGetUI.", "Change how UniGetUI installs packages, and checks and installs available updates": "Αλλαγή του τρόπου με τον οποίο το UniGetUI εγκαθιστά πακέτα και ελέγχει τις διαθέσιμες ενημερώσεις", "Change how operations request administrator rights": "Αλλαγή του τρόπου αίτησης για δικαιώματα διαχειριστή", "Change install location": "Αλλαγή τοποθεσίας εγκατάστασης", "Change this": "Αλλαγ<PERSON> αυτού", "Change this and unlock": "Αλλαγ<PERSON> αυτού και ξεκλείδωμα", "Check for package updates periodically": "Περιοδι<PERSON><PERSON><PERSON> έλεγχος για ενημερώσεις πακέτων", "Check for updates": "Έλεγχος για ενημερώσεις", "Check for updates every:": "Έλεγχος για ενημερώσεις κάθε:", "Check for updates periodically": "Περιο<PERSON><PERSON><PERSON><PERSON><PERSON> έλεγχος για ενημερώσεις.", "Check for updates regularly, and ask me what to do when updates are found.": "Τακτι<PERSON><PERSON><PERSON> έλεγχος για ενημερώσεις και ερώτηση για περαιτέρω ενέργειες όταν υπάρχουν ενημερώσεις.", "Check for updates regularly, and automatically install available ones.": "Τακτι<PERSON><PERSON><PERSON> έλεγχος για ενημερώσεις και αυτόματη εγκατάσταση των διαθέσιμων.", "Check out my {0} and my {1}!": "Ελέγξτε το {0} μου και το {1} μου!", "Check out some WingetUI overviews": "Ελέγξτε ορισμένες  αξιολογήσεις του UniGetUI", "Checking for other running instances...": "Έλεγχος αν εκτελούνται αντίγραφα της εφαρμογής...", "Checking for updates...": "Έλεγχος για ενημερώσεις...", "Checking found instace(s)...": "Έλεγχος του(ων) αντιγράφου(ων) της εφαρμογής που βρέθηκαν...", "Choose how many operations shouls be performed in parallel": "Επιλέξτε πόσες λειτουργίες πρέπει να εκτελούνται παράλληλα", "Clear cache": "Εκκαθάριση μνήμης cache", "Clear finished operations": "Εκκαθάριση ολοκληρωμένων λειτουργιών", "Clear selection": "Εκκαθάριση επιλογής", "Clear successful operations": "Εκκαθάριση επιτυχώς ολοκληρωμένων λειτουργιών", "Clear successful operations from the operation list after a 5 second delay": "Εκκαθάριση επιτυχώς ολοκληρωμένων λειτουργιών από τη λίστα λειτουργιών μετά από καθυστέρηση 5 δευτερολέπτων", "Clear the local icon cache": "Εκκαθάριση της τοπικής μνήμης cache εικονιδίων", "Clearing Scoop cache - WingetUI": "Εκκαθάριση μνήμης cache του Scoop - UniGetUI", "Clearing Scoop cache...": "Εκκαθάριση μνήμης cache του Scoop...", "Click here for more details": "Πατήστε εδώ για περισσότερες λεπτομέρειες", "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": "Πατήστε στο κουμπί Εγκατάσταση για έναρξη της διαδικασίας εγκατάστασης. Αν παραλείψετε την εγκατάσταση, το UniGetUI ίσως δεν λειτουργεί όπως αναμένεται.", "Close": "Κλείσιμο", "Close UniGetUI to the system tray": "Κλείσιμο του UniGetUI στη γραμμή εργασιών", "Close WingetUI to the notification area": "Κλείσιμο του UniGetUI στην περιοχή ειδοποιήσεων", "Cloud backup uses a private GitHub Gist to store a list of installed packages": "Το backup στο cloud χρησιμοποιεί ένα ιδιωτικό GitHub Gist για την αποθήκευση μιας λίστας εγκατεστημένων πακέτων.", "Cloud package backup": "Αντίγραφο ασφαλείας πακέτων στο cloud", "Command-line Output": "Κώδικας γραμμής εντολών", "Command-line to run:": "Γραμ<PERSON>ή εντολών για εκτέλεση:", "Compare query against": "Σύγκριση ερωτή<PERSON><PERSON><PERSON><PERSON>τι", "Compatible with authentication": "Συμβατό με πιστοποίηση", "Compatible with proxy": "Συμβατό με διακομιστή", "Component Information": "Πληροφορ<PERSON><PERSON><PERSON>τατικών", "Concurrency and execution": "Συγχρον<PERSON>σ<PERSON><PERSON>ς και εκτέλεση", "Connect the internet using a custom proxy": "Σύνδεση στο διαδίκτυο με χρήση προσαρμοσμένου διακομιστή", "Continue": "Συνέχεια", "Contribute to the icon and screenshot repository": "Συνεισφο<PERSON><PERSON> στο αποθετήριο εικονιδίων και στιγμιότυπων οθόνης", "Contributors": "Συνεισφέροντες", "Copy": "Αντιγραφή", "Copy to clipboard": "Αντιγρα<PERSON><PERSON> στο πρόχειρο", "Could not add source": "Δεν ήταν δυνατή η προσθήκη προέλευσης", "Could not add source {source} to {manager}": "Αδύνατη η προσθήκη προέλευσης {source} στο {manager}", "Could not back up packages to GitHub Gist: ": "Δεν ήταν δυνατή η δημιουργία αντιγράφων ασφαλείας πακέτων στο GitHub Gist:", "Could not create bundle": "Αδύνατη η δημιουργία της συλλογής", "Could not load announcements - ": "Αδύνατη η φόρτωση ανακοινώσεων -", "Could not load announcements - HTTP status code is $CODE": "Αδύνατη η φόρτωση ανακοινώσεων - Ο κωδικός κατάστασης HTTP είναι: $CODE", "Could not remove source": "Αδύνατη η απομάκρυνση προέλευσης", "Could not remove source {source} from {manager}": "Αδύνατη η απομάκρυνση της προέλευσης {source} από το {manager}", "Could not remove {source} from {manager}": "Αδύνατη η απομάκρυνση της {source} από το {manager}", "Credentials": "Διαπιστευτήρια", "Current Version": "Τρέχουσα Έκδοση", "Current status: Not logged in": "Τρέχουσα κατάσταση: Δεν έχετε συνδεθεί", "Current user": "Τρέχων χρήστης", "Custom arguments:": "Προσαρμοσμένα ορίσματα:", "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": "Τα προσαρμοσμένα ορίσματα γραμμής εντολών μπορούν να αλλάξουν τον τρόπο με τον οποίο εγκαθίστανται, α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ονται ή απεγκαθίστανται τα προγράμματα, με τρόπο που το UniGetUI δεν μπορεί να ελέγξει. Η χρήση προσαρμοσμένων γραμμών εντολών μπορεί να προκαλέσει προβλήματα στα πακέτα. Προχωρήστε με προσοχή.", "Custom command-line arguments:": "Προσαρμοσμένα ορίσματα γραμμής εντολών:", "Custom install arguments:": "Προσαρμοσμένα ορίσματα εγκατάστασης:", "Custom uninstall arguments:": "Προσαρμοσμένα ορίσματα απεγκατάστασης:", "Custom update arguments:": "Προσαρμοσμένα ορίσματα ενημέρωσης:", "Customize WingetUI - for hackers and advanced users only": "Προσαρμογή του UniGetUI - μόνο για χάκερ και προχωρημένους χρήστες", "DEBUG BUILD": "ΔΟΜΗ ΑΠΟΣΦΑΛΜΑΤΩΣΗΣ", "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ΔΗΛΩΣΗ ΑΠΟΠΟΙΗΣΗ ΕΥΘΥΝΗΣ: ΔΕΝ ΕΙΜΑΣΤΕ ΥΠΕΥΘΥΝΟΙ ΓΙΑ ΤΑ ΛΗΦΘΕΝΤΑ ΠΑΚΕΤΑ. ΣΙΓΟΥΡΕΥΤΕΙΤΕ ΟΤΙ ΚΑΝΕΤΕ ΕΓΚΑΤΑΣΤΑΣΗ ΜΟΝΟ ΑΞΙΟΠΙΣΤΟΥ ΛΟΓΙΣΜΙΚΟΥ.", "Dark": "Σκοτεινό", "Decline": "Απόρριψη", "Default": "Προεπιλογή", "Default installation options for {0} packages": "Προεπιλεγμένες επιλογές εγκατάστασης για {0} πακέτα", "Default preferences - suitable for regular users": "Προεπιλεγμένες ρυθμίσεις - ιδανικό για απλούς χρήστες", "Default vcpkg triplet": "Προεπιλεγμένο vcpkg triplet", "Delete?": "Διαγραφή;", "Dependencies:": "Εξαρτήσεις:", "Descendant": "Απόγονος", "Description:": "Περιγραφή:", "Desktop shortcut created": "Η συντόμευση στην επιφάνεια εργασίας δημιουργήθηκε", "Details of the report:": "Λεπτομέρειες της αναφοράς:", "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Η ανάπτυξη είναι δύσκολη και αυτή η εφαρμογή είναι δωρεάν. Αλλ<PERSON> αν σας άρεσε η εφαρμογή, μπορείτε πάντα να <b>με κεράσετε έναν καφέ</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": "Απευθείας εγκατάσταση με διπλό πάτημα σε ένα στοιχείο στην καρτέλα «{discoveryTab}» (αντί να εμφανίζονται οι πληροφορίες του πακέτου)", "Disable new share API (port 7058)": "Απενεργοποίηση νέου κοινόχρηστου API (θύρα 7058)", "Disable the 1-minute timeout for package-related operations": "Απενεργοποίηση χρονικού ορίου 1 λεπτού για λειτουργίες που σχετίζονται με πακέτα", "Disclaimer": "Δήλωση αποποίησης ευθυνών", "Discover Packages": "Εξερεύνηση Πακέτων", "Discover packages": "Εξερεύνηση πακέτων", "Distinguish between\nuppercase and lowercase": "Διάκριση\nπεζών - κεφαλαίων", "Distinguish between uppercase and lowercase": "Διάκριση πεζών - κεφαλαίων", "Do NOT check for updates": "Να ΜΗΝ γίνεται έλεγχος για ενημερώσεις", "Do an interactive install for the selected packages": "Διαδραστική εγκατάσταση επιλεγμένων πακέτων", "Do an interactive uninstall for the selected packages": "Διαδραστική απεγκατάσταση επιλεγμένων πακέτων", "Do an interactive update for the selected packages": "Διαδραστική ενημέρωση επιλεγμένων πακέτων", "Do not automatically install updates when the battery saver is on": "Μη αυτόματη εγκατάσταση ενημερώσεων όταν η εξοικονόμηση μπαταρίας είναι ενεργή", "Do not automatically install updates when the network connection is metered": "Μη αυτόματη εγκατάσταση ενημερώσεων όταν η σύνδεση στο διαδίκτυο είναι περιορισμένη", "Do not download new app translations from GitHub automatically": "Να μην γίνεται αυτόματη λήψη νέων μεταφράσεων απο το GitHub", "Do not ignore updates for this package anymore": "Μην αγνοείτε πλέον ενημερώσεις για αυτό το πακέτο", "Do not remove successful operations from the list automatically": "Να μην αφαιρούνται αυτόματα οι επιτυχημένες εργασίες από τη λίστα", "Do not show this dialog again for {0}": "Μην εμφαν<PERSON><PERSON><PERSON><PERSON><PERSON> ξανά αυτό το παράθυρο για το {0}", "Do not update package indexes on launch": "Να μη γίνεται ενημέρωση ευρετηρίων πακέτων κατά την εκκίνηση", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": "Αποδέχεστε ότι το UniGetUI συλλέγει και αποστέλλει ανώνυμα στατιστικά στοιχεία χρήσης, με μοναδικ<PERSON> σκοπό την κατανόηση και τη βελτίωση της εμπειρίας χρήστη;", "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": "Θεωρείτε χρήσιμο το UniGetUI; <PERSON><PERSON><PERSON> μπορείτε, ίσως να θέλετε να υποστηρίξετε τη δουλειά μου, ώστε να συνεχίσω να κάνω το UniGetUI το απόλυτο περιβάλλον εργασίας διαχείρισης πακέτων.", "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Θεωρείτε χρήσιμο το UniGetUI; Θέλετε να υποστηρίξετε τον δημιουργό; Αν ναι, μπορείτε να {0}, βοηθάει πολύ!", "Do you really want to reset this list? This action cannot be reverted.": "Θέλετε να επαναφέρεται αυτή τη λίστα; Αυτή η ενέργεια δεν είναι αναστρέψιμη.", "Do you really want to uninstall the following {0} packages?": "Θέλετε πραγματικά να απεγκαταστήσετε τα ακόλουθα {0} πακέτα;", "Do you really want to uninstall {0} packages?": "Θέλετε σιγουρα να απεγκαταστήσετε {0} πακέτα;", "Do you really want to uninstall {0}?": "Θέλετε σίγουρα να απεγκαταστήσετε το {0};", "Do you want to restart your computer now?": "Θέλετε να επανεκκινήσετε τον υπολογιστή σας τώρα;", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Θέλετε να μεταφράσετε το UniGetUI στη μητρική σας γλώσσα; Δείτε πως να συνεισφέρετε <a style=\"color:{0}\" href=\"{1}\"a>ΕΔΩ!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": "Δεν επιθυμείτε να προσφέρετε δωρεά; Μην ανησυχείτε, μπορείτε πάντα να μοιράζεστε το UniGetUI με τους φίλους σας. Πείτε σε όλους για το UniGetUI.", "Donate": "Δωρεά", "Done!": "Εγινε!", "Download failed": "Η λήψη δεν ολοκληρώθηκε", "Download installer": "Λήψη προγράμ<PERSON>α<PERSON>ος εγκατάστασης", "Download operations are not affected by this setting": "Οι λειτουργ<PERSON>ες λήψης δεν επηρεάζονται από αυτή τη ρύθμιση", "Download selected installers": "Λήψη επιλεγμένων προγραμμάτων εγκατάστασης", "Download succeeded": "Η λήψη ολοκληρώθηκε επιτυχώς", "Download updated language files from GitHub automatically": "Αυτόματη λήψη ενημερωμένων αρχείων γλώσσας από το GitHub", "Downloading": "Λή<PERSON>η", "Downloading backup...": "Λή<PERSON>η αντιγράφου ασφαλείας...", "Downloading installer for {package}": "Λήψη προγράμ<PERSON>α<PERSON>ος εγκατάστασης για το πακέτο {package}", "Downloading package metadata...": "Λή<PERSON>η μεταδεδομένων πακέτου...", "Enable Scoop cleanup on launch": "Ενεργοποίηση εκκαθάριση<PERSON> <PERSON><PERSON> κατά την εκκίνηση", "Enable WingetUI notifications": "Ενεργοποίηση ειδοποιήσεων UniGetUI", "Enable an [experimental] improved WinGet troubleshooter": "Ενεργοποίηση ενός [εμπειρικού] βελτιωμένου προγράμματος αντιμετώπισης προβλημάτων του UniGetUI", "Enable and disable package managers, change default install options, etc.": "Ενεργοποίηση και απενεργοποίηση διαχειριστών πακέτων, αλλαγή προεπιλεγμένων επιλογών εγκατάστασης κ.λπ.", "Enable background CPU Usage optimizations (see Pull Request #3278)": "Ενεργοποίηση βελτιστοποιήσεων χρήσης CPU υποβάθρου (δείτε το Αίτημα #3278)", "Enable background api (WingetUI Widgets and Sharing, port 7058)": "Ενεργοποίη<PERSON>η API παρασκηνίου (Widgets για UniGetUI και Κοινή Χρήση, θύρα 7058)", "Enable it to install packages from {pm}.": "Ενεργοποίηση για εγκατάσταση πακέτων από {pm}.", "Enable the automatic WinGet troubleshooter": "Ενεργοποίηση του προγράμματος αυτόματης αντιμετώπισης προβλημάτων του WinGet", "Enable the new UniGetUI-Branded UAC Elevator": "Ενεργοποίηση του νέου UAC Elevator του UniGetUI", "Enable the new process input handler (StdIn automated closer)": "Ενεργοποίηση του νέου χειριστή εισόδου διεργασίας (StdIn automated closer)", "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": "Ενεργοποιήστε τις παρακάτω ρυθμίσεις αν και μόνο αν κατανοείτε πλήρως τι κάνουν και τις πιθανές επιπτώσεις τους.", "Enable {pm}": "Ενεργοποίηση {pm}", "Enter proxy URL here": "Εισαγωγή URL διακομιστή εδώ", "Entries that show in RED will be IMPORTED.": "Οι καταχω<PERSON>ή<PERSON>εις που εμφανίζονται με ΚΟΚΚΙΝΟ θα ΕΙΣΑΓΟΝΤΑΙ.", "Entries that show in YELLOW will be IGNORED.": "Οι καταχωρήσεις που εμφανίζονται με ΚΙΤΡΙΝΟ θα ΑΓΝΟΗΘΟΥΝ.", "Error": "Σφάλμα", "Everything is up to date": "Ολα είναι ενημερωμένα", "Exact match": "Ακριβές ταίριασμα", "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": "Οι υπάρχουσες συντομεύσεις στην επιφάνεια εργασίας σας θα σαρωθούν και θα πρέπει να επιλέξετε ποιες θα διατηρήσετε και ποιες θα απομακρύνετε.", "Expand version": "Έκδοση επέκτασης", "Experimental settings and developer options": "Πειραματικές ρυθμίσεις και επιλογές για προγραμματιστές", "Export": "Εξαγωγή", "Export log as a file": "Εξαγωγή καταγραφής ως αρχείο", "Export packages": "Εξαγωγή πακέτων", "Export selected packages to a file": "Εξαγωγή επιλεγμένων πακέτων σε αρχείο", "Export settings to a local file": "Εξαγωγή ρυθμίσεων σε τοπικό αρχείο", "Export to a file": "Εξαγωγή σε αρχείο", "Failed": "Απέτυχε", "Fetching available backups...": "Ανάκτηση διαθέσιμων αντιγράφων ασφαλείας...", "Fetching latest announcements, please wait...": "Λήψη τελευτ<PERSON><PERSON><PERSON><PERSON> ανακ<PERSON><PERSON>νώσεων, παρα<PERSON><PERSON><PERSON><PERSON> περιμένετε...", "Filters": "Φίλτρα", "Finish": "Ολοκλήρωση", "Follow system color scheme": "Χρήση χρωμάτων συστήματος", "Follow the default options when installing, upgrading or uninstalling this package": "Παρακ<PERSON>λούθηση των προεπιλεγμένων επιλογών κατά την εγκατάσταση, την αναβάθμιση ή την απεγκατάσταση αυτού του πακέτου", "For security reasons, changing the executable file is disabled by default": "Για λόγους ασφαλε<PERSON>ας, η αλλαγή του εκτελέσιμου αρχείου είναι απενεργοποιημένη από προεπιλογή.", "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": "Για λόγους ασφαλείας, τα προσαρμοσμένα ορίσματα γραμμής εντολών είναι απενεργοποιημένα από προεπιλογή. Μεταβείτε στις ρυθμίσεις ασφαλείας του UniGetUI για να το αλλάξετε αυτό.", "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": "Για λόγους ασφαλείας, τα scripts πριν και μετά τη λειτουργία είναι απενεργοποιημένα από προεπιλογή. Μεταβείτε στις ρυθμίσεις ασφαλείας του UniGetUI για να το αλλάξετε αυτό.", "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Εξαναγ<PERSON><PERSON><PERSON><PERSON><PERSON>ς έκδοσης winget για ARM (ΜΟΝΟ ΓΙΑ ΣΥΣΤΗΜΑΤΑ ARM64)", "Formerly known as WingetUI": "Παλαιότερα γνωστό ως WingetUI", "Found": "Βρέθηκαν", "Found packages: ": "Πακέτα που βρέθηκαν:", "Found packages: {0}": "Πακέτα που βρέθηκαν: {0}", "Found packages: {0}, not finished yet...": "Πακέτα που βρέθηκαν: {0}, δεν ολοκληρώθηκε ακόμα...", "General preferences": "Γενι<PERSON><PERSON>ς προτιμήσεις", "GitHub profile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Global": "Καθολικό", "Go to UniGetUI security settings": "Μετάβαση στις ρυθμίσεις ασφαλείας του UniGetUI", "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": "Εξαιρετι<PERSON><PERSON> αποθετήρ<PERSON><PERSON> άγνωστων αλλά χρήσιμων βοηθητικών προγραμμάτων και άλλων ενδιαφερόντων πακέτων.<br>Περιέχει: <b>Β<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> προγράμματα, Προγράμματα γραμμής εντολών, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> λογισμικό (απαιτείται επιπλέον πακέτο)</b>", "Great! You are on the latest version.": "Τέλεια! Έχετε την πιο πρόσφατη έκδοση.", "Grid": "Πλέγμα", "Help": "Βοήθεια", "Help and documentation": "Βοήθεια και τεκμηρίωση", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": "Εδώ μπορείτε να αλλάξετε τη συμπεριφορά του UniGetUI αναφορικά με τις ακόλουθες συντομεύσεις. Ο έλεγχος μιας συντόμευσης θα κάνει το UniGetUI να τη διαγράψει αν δημιουργηθεί σε μελλοντική αναβάθμιση. Ο μή έλεγχός της θα διατηρήσεις τη συντόμευση ανέπαφη", "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Γει<PERSON> σα<PERSON>, ον<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, και είμαι ο <i>προγραμματιστής</i> του UniGetUI. Το UniGetUI κατασκευάστηκε εξολοκλήρου στον ελεύθερό μου χρόνο!", "Hide details": "Απόκρυψη λεπτομερειών", "Homepage": "Ιστοσελίδα", "Hooray! No updates were found.": "Συγχαρητήρια! Δε βρέθηκαν ενημερώσεις!", "How should installations that require administrator privileges be treated?": "Πώς θα θέλατε να αντιμετωπίζονται οι εγκαταστάσεις που απαιτούν προνόμια διαχειριστή;", "How to add packages to a bundle": "Πώς να προσθέσετε πακέτα σε συλλογή", "I understand": "Το κατάλαβα", "Icons": "Εικονίδια", "Id": "Ταυτότητα", "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": "Εάν έχετε ενεργοποιήσει τη δημιουργία αντιγράφων ασφαλείας στο cloud, θα αποθηκευτεί ως GitHub Gist σε αυτόν τον λογαριασμό.", "Ignore custom pre-install and post-install commands when importing packages from a bundle": "Αγνοήστε τις προσαρμοσμένες εντολές πριν και μετά την εγκατάσταση κατά την εισαγωγή πακέτων από μια συλλογή", "Ignore future updates for this package": "Αγνόηση μελλοντικ<PERSON>ν ενημερώσεων για αυτό το πακέτο", "Ignore packages from {pm} when showing a notification about updates": "Αγνόηση πακέτων από το {pm} όταν εμφανίζεται ειδοποίηση για ενημερώσεις", "Ignore selected packages": "Αγνόηση επιλεγμένων πακέτων", "Ignore special characters": "Αγνόηση ειδικών χαρακτήρων", "Ignore updates for the selected packages": "Αγνόηση ενημερώσεων για τα επιλεγμένα πακέτα", "Ignore updates for this package": "Αγνόηση ενημερώσεων για αυτό το πακέτο", "Ignored updates": "Αγνοημένες ενημερώσεις", "Ignored version": "Αγνοημένη εκδοση", "Import": "Εισαγωγή", "Import packages": "Εισαγωγ<PERSON> πακέτων", "Import packages from a file": "Εισαγω<PERSON><PERSON> πακέτων απο αρχείο", "Import settings from a local file": "Εισαγωγή ρυθμίσεων απο τοπικό αρχείο", "In order to add packages to a bundle, you will need to: ": "Για την προσθήκη πακέτων σε συλλογή, θα χρειαστείτε:", "Initializing WingetUI...": "Εκκίνηση του WingetUI...", "Install": "Εγκατάσταση", "Install Scoop": "Εγκατά<PERSON><PERSON><PERSON><PERSON><PERSON> Scoop", "Install and more": "Εγκατάστα<PERSON>η και άλλα", "Install and update preferences": "Προτιμήσεις εγκατάστασης και ενημερώσεων", "Install as administrator": "Εγκατάσταση ως διαχειριστής", "Install available updates automatically": "Αυτόματη εγκατάσταση διαθέσιμων ενημερώσεων", "Install location can't be changed for {0} packages": "Η τοποθεσία εγκατάστασης δεν μπορεί να αλλάξει για {0} πακέτα", "Install location:": "Τοποθεσία εγκατάστασης:", "Install options": "Επιλο<PERSON><PERSON><PERSON> εγκατάστασης", "Install packages from a file": "Εγκατάστα<PERSON>η πακέτων από αρχείο", "Install prerelease versions of UniGetUI": "Εγκατάσταση προδημοσιευμένων εκδόσεων του UniGetUI", "Install selected packages": "Εγκατάσταση επιλεγμένων πακέτων", "Install selected packages with administrator privileges": "Εγκατάσταση επιλεγμένων πακέτων με προνόμια διαχειριστή", "Install selection": "Εγκατάσταση επιλογής", "Install the latest prerelease version": "Εγκατάσταση της τελευταίας προδημοσιευμένης έκδοσης", "Install updates automatically": "Αυτόματη εγκατάσταση ενημερώσεων", "Install {0}": "Εγκατάσταση {0} ", "Installation canceled by the user!": "Η εγκατάσταση ακυρώθηκε απο τον χρήστη!", "Installation failed": "Η εγκατάσταση απέτυχε", "Installation options": "Επιλο<PERSON><PERSON><PERSON> εγκατάστασης", "Installation scope:": "Σκο<PERSON><PERSON><PERSON> εγκατάστασης:", "Installation succeeded": "Η εγκατάσταση ολοκληρώθηκε επιτυχώς", "Installed Packages": "Εγκατεστημένα Πακέτα", "Installed Version": "Εγκατεστημένη Έκδοση", "Installed packages": "Εγκατεστημένα πακέτα", "Installer SHA256": "Πρόγραμμα εγκατάστασης SHA256", "Installer SHA512": "Πρόγραμμα εγκατάστασης SHA512", "Installer Type": "Τύπος προγράμμα<PERSON>ος εγκατάστασης", "Installer URL": "URL προγράμμα<PERSON>ος εγκατάστασης", "Installer not available": "Το πρόγραμμα εγκατάστασης δεν είναι διαθέσιμο", "Instance {0} responded, quitting...": "Η εφαρμογή {0} ανταποκρίθηκε, κλείσιμο...", "Instant search": "Αμεση αναζήτηση", "Integrity checks can be disabled from the Experimental Settings": "Οι έλεγχοι ακεραιότητας μπορούν να απενεργοποιηθούν από τις πειραματικές ρυθμίσεις", "Integrity checks skipped": "Οι έλεγχοι ακεραιότητας αγνοήθηκαν", "Integrity checks will not be performed during this operation": "Οι έλεγχοι ακεραιότητας δε θα πραγματοποιηθούν σε αυτή τη λειτουργία", "Interactive installation": "Διαδραστική εγκατάσταση", "Interactive operation": "Διαδραστική λειτουργία", "Interactive uninstall": "Διαδραστική απεγκατάσταση", "Interactive update": "Διαδραστική ενημέρωση", "Internet connection settings": "Ρυθμίσεις σύνδεσης στο διαδίκτυο", "Is this package missing the icon?": "Λείπει το εικονίδιο του πακέτου;", "Is your language missing or incomplete?": "Η γλώσσα σας λείπει ή είναι ημιτελής;", "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": "Δεν υπάρχει εγγύηση ότι τα παρεχόμενα διαπιστευτήρια θα αποθηκευτούν ασφαλώς, έτσι μπορείτε να μην χρησιμοποιήσετε τα διαπιστευτήρια του τραπεζικού σας λογαριασμού", "It is recommended to restart UniGetUI after WinGet has been repaired": "Προτείνεται η επανεκκίνηση του UniGetUI μετά την επισκευή του WinGet", "It is strongly recommended to reinstall UniGetUI to adress the situation.": "Συνιστάται ανεπιφύλακτα να επανεγκαταστήσετε το UniGetUI για να αντιμετωπίσετε το πρόβλημα.", "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": "Φαίνετα<PERSON> ότι το WinGet δεν λειτουργ<PERSON><PERSON> σωστά. Θέλετε να δοκιμάσετε να επιδιορθώσετε το WinGet;", "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Φαίνετα<PERSON> ότι εκτελέσατε το UniGetUI ως διαχειριστής, κάτι που δε συνιστάται. Μπορείτε ακόμα να χρησιμοποιήσετε το πρόγραμμα, α<PERSON><PERSON><PERSON> συνιστούμε ανεπιφύλακτα να μην εκτελείτε το UniGetUI με δικαιώματα διαχειριστή. Κάντε κλικ στο «{showDetails}» για να δείτε γιατί.", "Language": "Γλώσσα", "Language, theme and other miscellaneous preferences": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, θέμα και διάφορες άλλες προτιμήσεις", "Last updated:": "Τελευταία ενημέρωση:", "Latest": "Τελευταία", "Latest Version": "Τελευτα<PERSON><PERSON> έκδοση", "Latest Version:": "Τελευτα<PERSON>α έκδοση:", "Latest details...": "Τελευταίες λεπτομέρειες...", "Launching subprocess...": "Εκτέλεση υποδιεργασίας...", "Leave empty for default": "Αφήστε το κενό για προεπιλογή", "License": "Άδεια", "Licenses": "Άδειες", "Light": "Φωτεινό", "List": "Λίστα", "Live command-line output": "Κώδικας γραμμής εντολών σε πραγματικό χρόνο", "Live output": "Κώδικας σε πραγματικό χρόνο", "Loading UI components...": "Φόρτωση στοιχείων περιβάλλοντος χρήστη...", "Loading WingetUI...": "Φόρτωση UniGetUI...", "Loading packages": "Φόρτωση πακέτων", "Loading packages, please wait...": "Φόρτωση πακέτων, παρακ<PERSON><PERSON><PERSON> περιμένετε...", "Loading...": "Φόρτωση...", "Local": "Τοπικό", "Local PC": "Το<PERSON><PERSON><PERSON><PERSON>ς υπολογιστής", "Local backup advanced options": "Προηγμένες επιλογές τοπικών αντιγράφων ασφαλείας", "Local machine": "Τοπικό μηχάνημα", "Local package backup": "Τοπικ<PERSON> αντίγραφο ασφαλείας πακέτων", "Locating {pm}...": "Εντοπισμός {pm}...", "Log in": "Σύνδεση", "Log in failed: ": "Η σύνδεση απέτυχε:", "Log in to enable cloud backup": "Συνδεθείτε για να ενεργοποιήσετε τη δημιουργία αντιγράφων ασφαλείας στο cloud", "Log in with GitHub": "Σύνδεση με GitHub", "Log in with GitHub to enable cloud package backup.": "Συνδεθείτε με το GitHub για να ενεργοποιήσετε τη δημιουργία αντιγράφων ασφαλείας πακέτων στο cloud.", "Log level:": "Επί<PERSON>εδ<PERSON> καταγραφής:", "Log out": "Αποσύνδεση", "Log out failed: ": "Η αποσύνδεση απέτυχε:", "Log out from GitHub": "Αποσύνδεση από το GitHub", "Looking for packages...": "Αναζήτηση για πακέτα...", "Machine | Global": "Μηχανή | Καθολικό", "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": "Τα λανθασμένα μορφοποιημένα ορίσματα γραμμής εντολών μπορούν να προκαλέσουν βλάβη στα πακέτα ή ακόμα και να επιτρέψουν σε έναν κακόβουλο παράγοντα να αποκτήσει προνομιακή εκτέλεση. Επομένως, η εισαγωγή προσαρμοσμένων ορισμάτων γραμμής εντολών είναι απενεργοποιημένη από προεπιλογή.", "Manage": "Διαχείριση", "Manage UniGetUI settings": "Διαχείριση ρυθμίσεων UniGetUI", "Manage WingetUI autostart behaviour from the Settings app": "Διαχειριστείτε τη συμπεριφορά αυτόματης εκκίνησης του UniGetUI από την εφαρμογή Ρυθμίσεις", "Manage ignored packages": "Διαχείριση αγνοημένων πακέτων", "Manage ignored updates": "Διαχείριση αγνοημένων ενημερώσεων", "Manage shortcuts": "Διαχείριση συντομεύσεων", "Manage telemetry settings": "Διαχείριση ρυθμίσεων τηλεμετρίας", "Manage {0} sources": "Διαχείριση προελεύσεων {0}", "Manifest": "Δηλωτικό", "Manifests": "Δηλωτικά", "Manual scan": "Χειρ<PERSON>κίνητη σάρωση", "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Επίσημος διαχειριστής πακέτων της Microsoft. Γεμάτο γνωστά και επαληθευμένα πακέτα<br>Περιέχει: <b>Γενικ<PERSON> Λογισμικό, εφαρμογές Microsoft Store</b>", "Missing dependency": "Απολεσθείσα εξάρτηση", "More": "Περισσότερα", "More details": "Περισσότερες λεπτομέρειες", "More details about the shared data and how it will be processed": "Περισσότερες λεπτομέρειες σχετικά με τα κοινόχρηστα δεδομένα και πως θα επεξεργαστούν", "More info": "Περισσότερες πληροφορίες", "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": "ΣΗΜΕΙΩΣΗ: Το πρόγραμμα αντιμετώπισης προβλημάτων μπορεί να απενεργοποιηθεί από τις ρυθμίσεις του UniGetUI, στον τομέα WinGet", "Name": "Όνομα", "New": "Νέο", "New Version": "Νέα Έκδοση", "New bundle": "Νέα συλλογή", "New version": "Νέα έκδοση", "Nice! Backups will be uploaded to a private gist on your account": "Ωραία! Τα αντίγραφα ασφαλείας θα μεταφορτωθούν σε ένα ιδιωτικό gist στον λογαριασμό σας.", "No": "Όχι", "No applicable installer was found for the package {0}": "Δεν βρέθηκε κατάλληλο πρόγραμμα εγκατάστασης για το πακέτο {0}", "No dependencies specified": "Δεν έχουν καθοριστεί εξαρτήσεις", "No new shortcuts were found during the scan.": "Δεν βρέθηκαν νέες συντομεύσεις κατά τη σάρωση.", "No packages found": "Δε βρέθηκαν πακέτα", "No packages found matching the input criteria": "Δε βρέθηκαν πακέτα με βάση τα κριτήρια αναζήτησης", "No packages have been added yet": "Δεν έχουν προστεθεί πακέτα ακόμα", "No packages selected": "Δεν επιλέχτηκαν πακέτα", "No packages were found": "Δε βρέθηκαν πακέτα", "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": "Καμιά προσωπική πληροφορία δε συλλέγεται ούτε αποστέλλεται και τα συλλεχθέντα δεδομένα είναι ανώνυμα, έτσι δεν μπορούν να αντιστοιχιστούν με εσάς.", "No results were found matching the input criteria": "Δε βρέθηκαν αποτελέσματα που να ταιριάζουν με αυτά τα κριτήρια", "No sources found": "Δε βρέθηκαν προελεύσεις", "No sources were found": "Δε βρέθηκαν προελεύσεις", "No updates are available": "Δεν υπάρχουν διαθέσιμες ενημερώσεις", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Διαχειριστής πακέτων του Node JS. Γεμάτος βιβλιοθήκες και άλλα βοηθητικά προγράμματα σε τροχιά γύρω από τον κόσμο της javascript<br>Περιέχει: <b>Βιβλιοθήκες Node javascript  και άλλα σχετικά βοηθητικά προγράμματα</b>", "Not available": "Μη διαθέσιμο", "Not finding the file you are looking for? Make sure it has been added to path.": "Δεν βρίσκετε το αρχείο που ψάχνετε; Βεβαιωθείτε ότι έχει προστεθεί στη διαδρομή.", "Not found": "Δεν βρέθηκε", "Not right now": "Οχι ακριβώς τώρα", "Notes:": "Σημειώσεις:", "Notification preferences": "Προτιμήσεις ειδοποιήσεων", "Notification tray options": "Ρυθμίσεις περιοχής ειδοποιήσεων", "Notification types": "Τύποι ειδοποιήσεων", "NuPkg (zipped manifest)": "NuPkg (συμπιεσμένο δηλωτικό)", "OK": "ΕΝΤΆΞΕΙ", "Ok": "Εντάξει", "Open": "Άνοιγμα", "Open GitHub": "Άνοιγμα GitHub", "Open UniGetUI": "Άνοιγμα UniGetUI", "Open UniGetUI security settings": "Ανοιγμα ρυθμίσεων ασφαλείας UniGetUI", "Open WingetUI": "Άνοιγμα UniGetUI", "Open backup location": "Άνοιγμα τοποθεσίας αντιγράφου ασφαλείας", "Open existing bundle": "Άνοιγμα υπάρχουσας συλλογής", "Open install location": "Ανοιγμα τοποθεσίας εγκατάστασης", "Open the welcome wizard": "Άνοιγμα του οδηγού υποδοχής", "Operation canceled by user": "Η λειτουργία ακυρώθηκε από τον χρήστη", "Operation cancelled": "Η εργασία ακυρώθηκε", "Operation history": "Ιστορικ<PERSON> εργασιών", "Operation in progress": "Η εργασία είναι σε εξέλιξη", "Operation on queue (position {0})...": "Η εργασία είναι σε σειρά προτεραιότητας (θέση {0})...", "Operation profile:": "Προ<PERSON><PERSON><PERSON> λειτουργίας:", "Options saved": "Οι ρυθμίσεις αποθηκεύτηκαν", "Order by:": "Ταξινόμηση κατά:", "Other": "Άλλο", "Other settings": "Άλλες ρυθμίσεις", "Package": "Πακ<PERSON>το", "Package Bundles": "Συλλογές πακέτων", "Package ID": "Ταυτότητα Πακέτου", "Package Manager": "Διαχειριστής πακέτων", "Package Manager logs": "Αρχεία καταγραφής διαχειριστή πακέτων", "Package Managers": "Διαχειριστές πακέτων", "Package Name": "Όνομα Πακέτου", "Package backup": "Αντίγραφο ασφαλείας πακέτου", "Package backup settings": "Ρυθμίσεις αντιγράφου ασφαλείας πακέτου", "Package bundle": "Συλλογή πακέτων", "Package details": "Λεπτομέρειες πακέτου", "Package lists": "Λίστες πακέτων", "Package management made easy": "Η διαχείριση πακέτων έγινε εύκολη", "Package manager": "Διαχειριστής πακέτων", "Package manager preferences": "Προτιμήσεις διαχειριστή πακέτων", "Package managers": "Διαχειριστές πακέτων", "Package not found": "Το πακέτο δε βρέθηκε", "Package operation preferences": "Προτιμήσεις λειτουργ<PERSON>ας πακέτου", "Package update preferences": "Προτιμήσεις ενημέρωσης πακέτου", "Package {name} from {manager}": "<PERSON>α<PERSON><PERSON><PERSON><PERSON> {name} απ<PERSON> {manager}", "Package's default": "Προεπιλογ<PERSON> πακέτου", "Packages": "Πακέτα", "Packages found: {0}": "Ευρεθέντα πακέτα: {0}", "Partially": "Τμηματικά", "Password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "Paste a valid URL to the database": "Επικολλήστε μια έγκυρη διεύθυνση URL προς τη βάση δεδομένων", "Pause updates for": "Παύση ενημερώσεων για", "Perform a backup now": "Δημιουρ<PERSON><PERSON><PERSON> ενός αντιγράφου ασφαλείας τώρα ", "Perform a cloud backup now": "Δημιουρ<PERSON><PERSON><PERSON> ενός αντιγράφου ασφαλείας στο cloud τώρα ", "Perform a local backup now": "Δημιουρ<PERSON><PERSON><PERSON> ενός τοπικού αντιγράφου ασφαλείας τώρα ", "Perform integrity checks at startup": "Εκτέλεση ελέγχων ακεραιότητας κατά την εκκίνηση", "Performing backup, please wait...": "Δημιουργ<PERSON><PERSON> αντιγράφου ασφαλείας, παρακαλ<PERSON> περιμένετε...", "Periodically perform a backup of the installed packages": "Περιοδική δημιουργ<PERSON>α αντιγράφου ασφαλείας των εγκατεστημένων πακέτων", "Periodically perform a cloud backup of the installed packages": "Περιοδική δημιουργ<PERSON>α αντιγράφων ασφαλείας των εγκατεστημένων πακέτων στο cloud", "Periodically perform a local backup of the installed packages": "Περιοδική δημιουργία τοπικών αντιγράφων ασφαλείας των εγκατεστημένων πακέτων", "Please check the installation options for this package and try again": "Ελέγξτε τις επιλογές εγκατάστασης για αυτό το πακέτο και δοκιμάστε ξανά", "Please click on \"Continue\" to continue": "Πατήστε στο «Συνέχεια» για να συνεχίσετε", "Please enter at least 3 characters": "Εισάγετε τουλάχιστον 3 χαρακτήρες", "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Λάβετε υπόψη ότι ορισμένα πακέτα ενδέχεται να μην μπορούν να εγκατασταθούν, λόγω των διαχειριστών πακέτων που είναι ενεργοποιημένοι σε αυτόν τον υπολογιστή.", "Please note that not all package managers may fully support this feature": "Σημειώστε ότι αυτό το το χαρακτηριστικό δεν υποστηρίζεται πλήρως από όλους τους διαχειριστές πακέτων", "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Λάβετε υπόψη ότι τα πακέτα από συγκεκριμένες προελεύσεις ενδέχεται να μην μπορούν να εξαχθούν. Έχουν γκριζάριστεί και δεν θα εξαχθούν.", "Please run UniGetUI as a regular user and try again.": "Εκτελέστε το UniGetUI ως κανονικ<PERSON>ς χρήστης και δοκιμάστε ξανά.", "Please see the Command-line Output or refer to the Operation History for further information about the issue.": "Δείτε τον Κώδικα της Γραμμής <PERSON>τολών ή ανατρέξτε στο Ιστορικό Εργασιών για περισσότερες πληροφορίες σχετικά με το ζήτημα.", "Please select how you want to configure WingetUI": "Επιλέξτε πώς θέλετε να διαμορφώσετε το UniGetUI", "Please try again later": "Δοκιμάστ<PERSON> ξανά αργότερα", "Please type at least two characters": "Πληκτρολογήστε τουλάχιστον δύο χαρακτήρες", "Please wait": "Παρα<PERSON><PERSON><PERSON><PERSON> περιμένετε", "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": "Περιμένετε όσο γίνεται εγκατάσταση του {0}. Μπορεί να εμφανιστεί ένα μαύρο παράθυρο. Περιμένετε μέχρι να κλείσει.", "Please wait...": "Παρα<PERSON><PERSON><PERSON><PERSON> περιμένετε...", "Portable": "Φορητό", "Portable mode": "Λειτουρ<PERSON><PERSON><PERSON> Portable (φορητή)", "Post-install command:": "Εντολή μετά την εγκατάσταση:", "Post-uninstall command:": "Εντολή μετά την απεγκατάσταση:", "Post-update command:": "Εντολή μετά την ενημέρωση:", "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": "Διαχειριστής πακέτων του PowerShell. Βρείτε βιβλιοθήκες και κώδικες για να επεκτείνετε τις δυνατότητες του PowerShell<br>Περιέχει: <b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εντολώ<PERSON></b>", "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": "Οι εντολές πριν και μετά την εγκατάσταση μπορούν να κάνουν πολύ άσχημα πράγματα στη συσκευή σας, εάν έχουν σχεδιαστεί για αυτό. Μπορεί να είναι πολύ επικίνδυνο να εισάγετε τις εντολές από μια συλλογή, εκτός αν εμπιστεύεστε την πηγή αυτής της συλλογής.", "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": "Οι εντολές πριν και μετά την εγκατάσταση θα εκτελούνται πριν και μετά την εγκατάσταση, την αναβάθμιση ή την απεγκατάσταση ενός πακέτου. Λάβετε υπόψη ότι ενδέχεται να προκαλέσουν βλάβη, εκτός εάν χρησιμοποιηθούν προσεκτικά.", "Pre-install command:": "Εντολή πριν την εγκατάσταση:", "Pre-uninstall command:": "Εντολή πριν την απεγκατάσταση:", "Pre-update command:": "Εντολή πριν την ενημέρωση:", "PreRelease": "Προδημοσίευση", "Preparing packages, please wait...": "Προετοιμασία πακέτων, παρακ<PERSON>λ<PERSON> περιμένετε...", "Proceed at your own risk.": "Συνεχίστε με δική σας ευθύνη.", "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": "Αποτροπή οποιουδήποτε είδους αύξησης των δικαιωμάτων μέσω του UniGetUI Elevator ή του GSudo", "Proxy URL": "URL διακομιστή", "Proxy compatibility table": "Πίνακας συμβατότητας διακομιστή", "Proxy settings": "Ρυθμίσεις διακομιστή", "Proxy settings, etc.": "Ρυθμίσεις διακομιστή κλπ.", "Publication date:": "Ημερομηνία έκδοσης:", "Publisher": "Εκδότης", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Διαχειριστής βιβλιοθηκών της Python. Γε<PERSON>άτος με βιβλιοθήκες της python και άλλα βοηθητικά προγράμματα που σχετίζονται με την python<br>Περιέχει: <b>Βιβλιοθήκες Python και σχετικά βοηθητικά προγράμματα</b>", "Quit": "Έξοδος", "Quit WingetUI": "Εξοδος από το UniGetUI", "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": "Μείωση των προτρο<PERSON><PERSON><PERSON>, αύξηση των δικαιωμάτων των εγκαταστάσεων από προεπιλογή, ξεκλείδωμα ορισμένων επικίνδυνων λειτουργιών κ.λπ.", "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": "Ανατρέξτε στα αρχεία καταγραφής UniGetUI για να λάβετε περισσότερες λεπτομέρειες σχετικά με τα αρχεία που επηρεάζονται.", "Reinstall": "Επανεγ<PERSON>ατάσταση", "Reinstall package": "Επανεγκατάσταση πακέτου", "Related settings": "Σχετικές ρυθμίσεις", "Release notes": "Σημειώσεις έκδοσης", "Release notes URL": "Διεύθυνση URL σημειώσεων έκδοσης", "Release notes URL:": "Διεύθυνση URL σημειώσεων έκδοσης:", "Release notes:": "Σημειώσεις έκδοσης:", "Reload": "Επαναφόρτωση", "Reload log": "Επαναφόρτωση αρχείου καταγραφής", "Removal failed": "Η απομάκρυνση απέτυχε", "Removal succeeded": "Η απομάκρυνση ολοκληρώθηκε με επιτυχία", "Remove from list": "Απομάκρυνση από τη λίστα", "Remove permanent data": "Απομάκρυνση μόνιμων δεδομένων", "Remove selection from bundle": "Απομάκρυνση επιλογής από τη συλλογή", "Remove successful installs/uninstalls/updates from the installation list": "Απομάκρυνση επιτυχημένων εγκαταστάσεων /απεγκαταστάσεων / ενημερώσεων από τη λίστα εγκατάστασης", "Removing source {source}": "Απομάκρυνση προέλευσης {source}", "Removing source {source} from {manager}": "Απομάκρυνση της πηγής {source} από το {manager}", "Repair UniGetUI": "Επισκευή του UniGetUI", "Repair WinGet": "Επισκευή του WinGet", "Report an issue or submit a feature request": "Αναφέρετε ένα πρόβλημα ή υποβάλετε ένα αίτημα νέου χαρακτηριστικού", "Repository": "Αποθετήριο", "Reset": "Επαναφορά", "Reset Scoop's global app cache": "Επαναφ<PERSON>ρ<PERSON> καθολικής μνήμης cache εφαρμογών του Scoop", "Reset UniGetUI": "Επαναφορά του UniGetUI", "Reset WinGet": "Επαναφορά του WinGet", "Reset Winget sources (might help if no packages are listed)": "Επαναφορά προελεύσεων <PERSON> (μπορεί να βοηθήσει εάν δεν εμφανίζονται πακέτα)", "Reset WingetUI": "Επαναφορά του UniGetUI", "Reset WingetUI and its preferences": "Επαναφορά του UniGetUI και των προτιμήσεών του", "Reset WingetUI icon and screenshot cache": "Επαναφ<PERSON><PERSON><PERSON> μνήμης cache εικονιδίων και στιγμιότυπων στο UniGetUI", "Reset list": "Επαναφο<PERSON><PERSON> λίστας", "Resetting Winget sources - WingetUI": "Επαναφορά προελεύσεων Winget - UniGetUI", "Restart": "Επανεκκίνηση", "Restart UniGetUI": "Επανεκκίνηση του UniGetUI", "Restart WingetUI": "Επανεκκίνηση του UniGetUI", "Restart WingetUI to fully apply changes": "Επανεκκίνηση του UniGetUI για πλήρη εφαρμογή των αλλαγών", "Restart later": "Επανεκκίνηση αργότερα", "Restart now": "Επανεκκίνηση τώρα", "Restart required": "Απαιτείτ<PERSON><PERSON> επανεκκίνηση", "Restart your PC to finish installation": "Επανεκκίνηση του υπολογιστή σας για ολοκλήρωση της εγκατάστασης", "Restart your computer to finish the installation": "Επανεκκίνηση του υπολογιστή σας για ολοκλήρωση της εγκατάστασης", "Restore a backup from the cloud": "Επανα<PERSON><PERSON><PERSON><PERSON> ενός αντιγράφου ασφαλείας από το cloud", "Restrictions on package managers": "Περιορισμοί στους διαχειριστές πακέτων", "Restrictions on package operations": "Περιορισμοί στις λειτουργίες πακέτων", "Restrictions when importing package bundles": "Περιορισμ<PERSON><PERSON> κατά την εισαγωγή συλλογών", "Retry": "Επανάληψη", "Retry as administrator": "Επανάληψη ως διαχειριστής", "Retry failed operations": "Αποτυχημένες λειτουργίες επανάληψης", "Retry interactively": "Διαδραστική επανάληψη", "Retry skipping integrity checks": "Επανάληψη αγνοώντας ελέγχους ακεραιότητας", "Retrying, please wait...": "Επανάλη<PERSON>η, παρα<PERSON><PERSON><PERSON><PERSON> περιμένετε...", "Return to top": "Επιστροφή στην κορυφή", "Run": "Εκτέλεση", "Run as admin": "Εκτέλεση ως διαχειριστής", "Run cleanup and clear cache": "Εκτέλεση εκκαθάρισης και διαγραφή της μνήμης cache", "Run last": "Εκτέλεση τελευταίας", "Run next": "Εκτέλεσης επόμενης", "Run now": "Εκτέλεση τώρα", "Running the installer...": "Εκτέλεση εγκατάστασης...", "Running the uninstaller...": "Εκτέλεση απεγκατάστασης...", "Running the updater...": "Εκτέλεση ενημέρωσης...", "Save": "Αποθήκευση", "Save File": "Αποθήκευση Αρχείου", "Save and close": "Αποθήκευση και κλείσιμο", "Save as": "Αποθήκευση ως", "Save bundle as": "Αποθήκευση συλλογής ως", "Save now": "Αποθήκευση τώρα", "Saving packages, please wait...": "Αποθήκευση πακέτων, παρακ<PERSON><PERSON><PERSON> περιμένετε...", "Scoop Installer - WingetUI": "Πρόγραμμα εγκατάστα<PERSON>η<PERSON> - UniGetUI", "Scoop Uninstaller - WingetUI": "Πρόγραμμα απεγκατά<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - UniGetUI", "Scoop package": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Search": "Αναζήτηση", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Αναζητήστε λογισμικό για επιτραπέζιους υπολογιστές, προειδοποίηση όταν υπάρχουν διαθέσιμες ενημερώσεις χωρίς εκτέλεση αψυχολόγητων κινήσεων. Δε θέλω το UniGetUI να περιπλέκεται υπερβολικά, θέλω μόνο ένα απλό <b>κατάστημα λογισμικού</b>", "Search for packages": "Αναζήτηση πακέτων", "Search for packages to start": "Αναζήτηση πακέτων για εκκίνηση", "Search mode": "Λειτουρ<PERSON><PERSON><PERSON> αναζήτησης", "Search on available updates": "Αναζήτηση στις διαθέσιμες ενημερώσεις", "Search on your software": "Αναζήτηση στις εφαρμογές σας", "Searching for installed packages...": "Αναζήτηση για εγκατεστημένα πακέτα...", "Searching for packages...": "Αναζήτηση πακέτων...", "Searching for updates...": "Αναζήτηση για ενημερώσεις...", "Select": "Επιλογή", "Select \"{item}\" to add your custom bucket": "Επιλογή «{item}» για προσθήκη στο προσαρμοσμένο καλάθι σας", "Select a folder": "Επιλέξτε έναν φάκελο", "Select all": "Επιλογ<PERSON> όλων", "Select all packages": "Επιλογή όλων των πακέτων", "Select backup": "Επιλέξτε αντίγραφο ασφαλείας", "Select only <b>if you know what you are doing</b>.": "Επιλογή <b>μ<PERSON><PERSON><PERSON> αν γνωρίζετε τι κάνετε</b>.", "Select package file": "Επιλογή αρχείου πακέτου", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": "Επιλέξτε το αντίγραφο ασφαλείας που θέλετε να ανοίξετε. Αργότερα, θα μπορείτε να ελέγξετε ποια πακέτα θέλετε να εγκαταστήσετε.", "Select the processes that should be closed before this package is installed, updated or uninstalled.": "Επιλέξτε τις διεργασίες που θα πρέπει να κλείσουν πριν από την εγκατάσταση, την ενημέρωση ή την απεγκατάσταση αυτού του πακέτου.", "Select the source you want to add:": "Επιλογή προέλευσης που θέλετε να προσθέσετε:", "Select upgradable packages by default": "Επιλογή αναβαθμίσιμων πακέτων από προεπιλογή", "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": "Επιλέξτε ποιους <b>διαχει<PERSON>ιστ<PERSON>ς πακέτων</b> θα χρησιμοποιήσετε ({0}), διαμορφώστε τον τρόπο εγκατάστασης των πακέτων, διαχ<PERSON><PERSON>ριστείτε τον τρόπο διαχείρισης των δικαιωμάτων διαχειριστή κλπ.", "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Η χειραψία εστάλη. Αναμονή για την απάντηση του ακροατή του αντιγράφου της εφαρμογής... ({0}%)", "Set a custom backup file name": "Ορίστε ένα προσαρμοσμένο όνομα αρχείου αντιγράφου ασφαλείας", "Set custom backup file name": "Ορίστε προσαρμοσμένο όνομα αρχείου αντιγράφου ασφαλείας", "Settings": "Ρυθμίσεις", "Share": "Κοινή χρήση", "Share WingetUI": "Κοινή χρήση του UniGetUI", "Share anonymous usage data": "Κοινή χρήση ανώνυμων δεδομένων χρήσης", "Share this package": "Κοινή χρήση αυτού του πακέτου", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": "Σε περίπτωση που τροποποιήσετε τις ρυθμίσεις ασφαλείας, θα χρειαστεί να ανοίξετε ξανά τη συλλογή για να ισχύσουν οι αλλαγές.", "Show UniGetUI on the system tray": "Εμφάνιση του UniGetUI στη γραμμή εργασιών", "Show UniGetUI's version and build number on the titlebar.": "Προβολή της <PERSON>δοσης του UniGetUI στη γραμμή τίτλου", "Show WingetUI": "Εμφάνιση του UniGetUI", "Show a notification when an installation fails": "Εμφάνιση ειδοποίησης όταν μια εγκατάσταση αποτυγχάνει", "Show a notification when an installation finishes successfully": "Εμφάνιση ειδοποίησης όταν μια εγκατάσταση ολοκληρώνεται επιτυχώς", "Show a notification when an operation fails": "Εμφάνιση ειδοποίησης όταν μια λειτουργία αποτυγχάνει", "Show a notification when an operation finishes successfully": "Εμφάνιση ειδοποίησης όταν μια λειτουργία ολοκληρώνεται επιτυχώς", "Show a notification when there are available updates": "Εμφάνιση ειδοποίη<PERSON>ης όταν υπάρχουν διαθέσιμες ενημερώσεις", "Show a silent notification when an operation is running": "Εμφάνιση σιωπηλής ειδοποίησης όταν εκτελείται μια λειτουργία", "Show details": "Εμφάνιση λεπτομερειών", "Show in explorer": "Εμφάνιση στον εξερευνητή", "Show info about the package on the Updates tab": "Εμφάνιση πληροφορι<PERSON><PERSON> σχετι<PERSON><PERSON> με το πακέτο στην καρτέλα Ενημερώσεις", "Show missing translation strings": "Εμφάνιση κειμένου που λείπει η μετάφραση", "Show notifications on different events": "Εμφάνιση ειδοποιήσεων για διαφορετικά συμβάντα", "Show package details": "Εμφάνιση λεπτομερειών πακέτου", "Show package icons on package lists": "Εμφάνιση εικονιδίων πακέτων στις λίστες πακέτων", "Show similar packages": "Εμφάνιση παρόμοιων πακέτων", "Show the live output": "Εμφάνιση του κώδικα σε πραγματικό χρόνο", "Size": "Μέγεθος", "Skip": "Αγνόηση", "Skip hash check": "Αγνόηση ελέγχου hash", "Skip hash checks": "Αγνόηση ελέγχων hash", "Skip integrity checks": "Αγνόηση ελέγχων ακεραιότητας", "Skip minor updates for this package": "Αγνόηση μικροενημερώσεων για αυτό το πακέτο", "Skip the hash check when installing the selected packages": "Αγνόηση του ελέγχου hash κατά την εγκατάσταση των επιλεγμένων πακέτων", "Skip the hash check when updating the selected packages": "Αγνόηση του ελέγχου hash κατά την ενημέρωση των επιλεγμένων πακέτων", "Skip this version": "Αγνόηση αυτής της έκδοσης", "Software Updates": "Ενημερώσεις Ε<PERSON>α<PERSON>μο<PERSON>ών", "Something went wrong": "Κάτι πήγε στραβά", "Something went wrong while launching the updater.": "Κάτι πήγε στραβά κατά το άνοιγμα του προγράμματος ενημέρωσης.", "Source": "Προέλευση", "Source URL:": "Διεύθυνση URL προέλευσης:", "Source added successfully": "Η προέλευση προστέθηκε επιτυχώς", "Source addition failed": "Η προσθήκη της προέλευσης απέτυχε", "Source name:": "Όνομα προέλευσης:", "Source removal failed": "Η απομάκρυνση της προέλευσης απέτυχε", "Source removed successfully": "Η απομάκρυνση της προέλευσης ολοκληρώθηκε επιτυχώς", "Source:": "Προέλευση:", "Sources": "Προελεύσεις", "Start": "Εκκίνηση", "Starting daemons...": "Εκκίνηση δαιμόνων...", "Starting operation...": "Εκκίνηση λειτουργίας...", "Startup options": "Επιλο<PERSON><PERSON>ς εκκίνησης", "Status": "Κατάσταση", "Stuck here? Skip initialization": "Εχετε κολλή<PERSON><PERSON>ι εδώ; Αγνοήστε την αρχικοποίηση", "Suport the developer": "Υποστηρίξτε τον προγραμματιστή", "Support me": "Υποστηρίξτε με", "Support the developer": "Υποστηρίξτε τον προγραμματιστή", "Systems are now ready to go!": "Τα συστήματα είναι έτοιμα για χρήση!", "Telemetry": "Τηλεμετρία", "Text": "Κείμενο", "Text file": "Αρχ<PERSON><PERSON><PERSON> κειμένου", "Thank you ❤": "Σας ευχαριστώ ❤", "Thank you 😉": "Σας ευχαριστώ 😉", "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": "Ο διαχειριστής πακέτων Rust.<br>Περιέχει: <b>βιβλιοθήκες και προγράμματα Rust γραμμένα σε Rust</b>", "The backup will NOT include any binary file nor any program's saved data.": "Το αντίγραφο ασφαλείας ΔΕΝ θα περιλαμβάνει κανένα δυαδικό αρχείο ούτε αποθηκευμένα δεδομένα οποιουδήποτε προγράμματος.", "The backup will be performed after login.": "Το αντίγραφο ασφαλείας θα δημιουργηθεί κατά τη σύνδεση.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": "Το αντίγραφο ασφαλείας θα περιλαμβάνει την πλήρη λίστα των εγκατεστημένων πακέτων και τις επιλογές εγκατάστασής τους. Οι ενημερώσεις που αγνοήθηκαν και οι εκδόσεις που παραλήφθηκαν θα αποθηκευτούν επίσης.", "The bundle you are trying to load appears to be invalid. Please check the file and try again.": "Η συλλογή που προσπαθείτε να φορτώσετε φαίνεται να μην είναι έγκυρη. Ελέγξτε το αρχείο και δοκιμάστε ξανά.", "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": "Το άθροισμα ελέγχου του προγράμματος εγκατάστασης δε συμπίπτει με την αναμενόμενη τιμή και η αυθεντικότητα του προγράμματος εγκατάστασης δεν μπορεί να επαληθευτεί. Εάν εμπιστεύεστε τον εκδότη, {0} το πακέτο παραλείποντας ξανά τον έλεγχο hash.", "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": "Ο κλασικός διαχειριστής πακέτων για windows. Θα βρείτε τα πάντα εκεί. <br>Περιέχει: <b><PERSON>ε<PERSON><PERSON><PERSON><PERSON> λογισμικό</b>", "The cloud backup completed successfully.": "Η δημιουργία αντιγράφων ασφαλείας στο cloud ολοκληρώθηκε με επιτυχία.", "The cloud backup has been loaded successfully.": "Το αντίγραφο ασφαλείας από το cloud φορτώθηκε με επιτυχία.", "The current bundle has no packages. Add some packages to get started": "Η τρέχουσα συλλογή δεν έχει πακέτα. Προσθέστε ορισμένα πακέτα για να ξεκινήσετε", "The executable file for {0} was not found": "Το εκτελέσιμο αρχείο για {0} δεν βρέθηκε", "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": "Οι ακόλουθες επιλογές θα εφαρμόζονται από προεπιλογή κάθε φορά που εγκαθίσταται, αν<PERSON>β<PERSON><PERSON>μίζεται ή απεγκαθίσταται ένα πακέτο {0}.", "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Τα ακόλουθα πακέτα πρόκειται να εξαχθούν σε αρχείο JSON. Δεν πρόκειται να αποθηκευτούν δεδομένα χρήστη ή δυαδικά αρχεία.", "The following packages are going to be installed on your system.": "Τα ακόλουθα πακέτα πρόκειται να εγκατασταθούν στο σύστημά σας.", "The following settings may pose a security risk, hence they are disabled by default.": "Οι ακόλουθες ρυθμίσεις ενδέχεται να θέτουν σε κίνδυνο την ασφάλεια, επομένως είναι απενεργοποιημένες από προεπιλογή.", "The following settings will be applied each time this package is installed, updated or removed.": "Οι ακόλουθες ρυθμίσεις θα εφαρμόζονται κάθε φορά που αυτό το πακέτο εγκαθίσταται, ενημ<PERSON>ρώνεται ή απομακρύνεται.", "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": "Οι ακόλουθες ρυθμίσεις θα εφαρμόζονται κάθε φορά που αυτό το πακέτο εγκαθίσταται, ενημ<PERSON>ρώνεται ή καταργείται. Θα αποθηκευτούν αυτόματα.", "The icons and screenshots are maintained by users like you!": "Τα εικονίδια και τα στιγμιότυπα οθόνης συντηρούνται απο χρήστες όπως εσείς!", "The installer authenticity could not be verified.": "Η αυθεντικότητα του προγράμματος εγκατάστασης δεν επιβεβαιώνεται.", "The installer has an invalid checksum": "Το πρόγραμμα εγκατάστα<PERSON>ης έχει μη έγκυρο άθροισμα ελέγχου", "The installer hash does not match the expected value.": "Το hash του προγρά<PERSON><PERSON>α<PERSON>ος εγκατ<PERSON>στασης δεν ταιριάζει με την αναμενόμενη τιμή.", "The local icon cache currently takes {0} MB": "Η τρέχουσα τοπική μνήμη cache εικονιδίων καταλαμβάνει {0} MB", "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": "Ο κύριος στόχος αυτού του έργου είναι να δημιουργήσει μια διαισθητική διεπαφή χρήστη για τη διαχείριση των πιο κοινών διαχειριστών πακέτων CLI για Windows, όπως το Winget και το Scoop.", "The package \"{0}\" was not found on the package manager \"{1}\"": "Το πακέτο «{0}» δεν βρέθηκε στον διαχειριστή πακέτων «{1}»", "The package bundle could not be created due to an error.": "Η συλλογή πακέτων δεν μπορεί να δημιουργηθεί λόγω σφάλματος.", "The package bundle is not valid": "Η συλλογή πακέτων δεν είναι έγκυρη", "The package manager \"{0}\" is disabled": "Ο διαχειριστής πακέτων «{0}» είναι απενεργοποιημένος", "The package manager \"{0}\" was not found": "Ο διαχειριστής πακέτων «{0}» δεν βρέθηκε", "The package {0} from {1} was not found.": "Το πακέτο {0} από {1} δεν βρέθηκε.", "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Τα πακέτα στη λίστα δε θα ληφθούν υπόψη κατά τον έλεγχο για ενημερώσεις. Πατήστε τα διπλά ή πατήστε στο κουμπί στα δεξιά τους για να σταματήσετε να αγνοείτε τις ενημερώσεις τους.", "The selected packages have been blacklisted": "Τα επιλεγμένα πακέτα έχουν μπει στη μαύρη λίστα", "The settings will list, in their descriptions, the potential security issues they may have.": "Οι ρυθμίσεις θα αναφέρουν, στις περιγραφές τους, τα πιθανά προβλήματα ασφαλείας που ενδέχεται να έχουν.", "The size of the backup is estimated to be less than 1MB.": "Το μέγεθος του αντιγράφου ασφαλείας εκτιμάται ότι είναι μικρότερο από 1 MB.", "The source {source} was added to {manager} successfully": "Η προέλευση {source} προστέθηκε στο {manager} με επιτυχία", "The source {source} was removed from {manager} successfully": "Η προέλευση {source} αφαιρέθηκε από το {manager} με επιτυχία", "The system tray icon must be enabled in order for notifications to work": "Το εικονίδιο στη γραμμή εργασιών πρέπει να ενεργοποιηθεί ώστε να δουλεύουν οι ειδοποιήσεις", "The update process has been aborted.": "Η διαδικασία ενημέρωση έχει ακυρωθεί.", "The update process will start after closing UniGetUI": "Η διαδικασία ενημέρωσης θα εκκινήσει μετά το κλείσιμο του UniGetUI", "The update will be installed upon closing WingetUI": "Η ενημέρωση θα εγκατασταθεί με το κλείσιμο του UniGetUI", "The update will not continue.": "Η ενημέρωση δεν θα συνεχιστεί.", "The user has canceled {0}, that was a requirement for {1} to be run": "Ο χρήστης ακύρωσε το {0} που ήταν προαπαιτούμενο για την εκτέλεση του {1}", "There are no new UniGetUI versions to be installed": "Δεν υπάρχουν νέες εκδόσεις UniGetUI για εγκατάσταση", "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": "Υπάρχουν εργασίες σε εξέλιξη. Η διακοπή του UniGetUI μπορεί να προκαλέσει την αποτυχία τους. Θέλετε να συνεχίσετε;", "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Υπάρχουν μερικά υπέροχα βίντεο στο YouTube που παρουσιάζουν το UniGetUI και τις δυνατότητές του. Θα μπορούσατε να μάθετε χρήσιμα κόλπα και συμβουλές!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Υπάρχουν δύο βασικοί λόγοι για να μην εκτελείτε το UniGetUI ως διαχειριστής: Ο πρώτος είναι ότι ο διαχειριστής πακέτων Scoop ενδέχεται να προκαλέσει προβλήματα με ορισμένες εντολές όταν εκτελείται με δικαιώματα διαχειριστή. Ο δεύτερος είναι ότι η εκτέλεση του UniGetUI ως διαχειριστής σημαίνει ότι οποιοδήποτε πακέτο που κατεβάζετε θα εκτελείται με δικαιώματα διαχειριστή (και αυτό δεν είναι ασφαλές). Να θυμάστε ότι εάν πρέπει να εγκαταστήσετε ένα συγκεκριμένο πακέτο ως διαχειριστής, μπορείτε πάντα να κάνετε δεξί κλικ στο στοιχείο -> Εγκατάσταση/Ενημέρωση/Κατάργηση εγκατάστασης ως διαχειριστής.", "There is an error with the configuration of the package manager \"{0}\"": "Υπάρχει ένα σφάλμα με τις ρυθμίσεις του διαχειριστή πακέτων «{0}»", "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": "Μια εγκατάσταση είναι σε εξέλιξη. Ε<PERSON>ν κλείσετε το UniGetUI, η εγκατάσταση μπορεί να αποτύχει και να έχει απροσδόκητα αποτελέσματα. Εξακολουθείτε να θέλετε να τερματίσετε το UniGetUI;", "They are the programs in charge of installing, updating and removing packages.": "Είναι τα προγράμματα που είναι υπεύθυνα για την εγκατάσταση, την ενημέρωση και την απομάκρυνση πακέτων.", "Third-party licenses": "Αδειες τρίτων", "This could represent a <b>security risk</b>.": "Αυτό θα μπορούσε να αποτελέσει <b>κίνδυνο ασφαλείας</b>.", "This is not recommended.": "Αυτό δεν προτείνεται.", "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": "Αυτό οφείλεται πιθανώς στο γεγονός ότι το πακέτο που σας εστάλη αφαιρέθηκε ή δημοσιεύτηκε σε έναν διαχειριστή πακέτων που δεν έχετε ενεργοποιήσει. Η ταυτότητα που ελήφθη είναι {0}", "This is the <b>default choice</b>.": "Αυτή είναι η <b>προεπιλεγμένη επιλογή</b>.", "This may help if WinGet packages are not shown": "Αυτό μπορεί να βοηθήσει αν τα πακέτα WinGet δεν προβάλονται", "This may help if no packages are listed": "Αυτό μπορεί να βοηθήσει αν κανένα πακέτο δεν εμφανίζεται", "This may take a minute or two": "Αυτό μπορεί να πάρει ένα ή δύο λεπτά", "This operation is running interactively.": "Αυτή η λειτουργία εκτελείται διαδραστικά.", "This operation is running with administrator privileges.": "Αυτή η λειτουργία εκτελείται με προνόμια διαχειριστή.", "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": "Αυτή η επιλογή ΘΑ προκαλέσει προβλήματα. Οποιαδήποτε λειτουργία δεν μπορεί να λάβει αυξημένα δικαιώματα μόνη της ΘΑ ΑΠΟΤΥΧΕΙ. Η εγκατάσταση/ενημέρωση/απεγκατάσταση ως διαχειριστής ΔΕΝ θα λειτουργήσει.", "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": "Αυτή η συλλογή είχε ορισμένες ρυθμίσεις που είναι δυνητικά επικίνδυνες και ενδέχεται να αγνοηθεί από προεπιλογή.", "This package can be updated": "Αυτό το πακέτο μπορεί να ενημερωθεί", "This package can be updated to version {0}": "Αυτό το πακέτο μπορεί να ενημερωθεί στην έκδοση {0}", "This package can be upgraded to version {0}": "Αυτό το πακέτο μπορεί να αναβαθμιστεί στην έκδοση {0}", "This package cannot be installed from an elevated context.": "Αυτό το πακέτο δεν μπορεί να εγκατασταθεί από περιεχόμενο με αυξημένα δικαιώματα.", "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": "Αυτό το πακέτο δεν έχει στιγμιότυπα οθόνης ή λείπει το εικονίδιο; Συνεισφέρετε στο UniGetUI προσθέτοντας τα εικονίδια και τα στιγμιότυπα οθόνης που λείπουν στην ανοιχτή, δημόσια βάση δεδομένων μας.", "This package is already installed": "Αυτό το πακέτο είναι ήδη εγκατεστημένο", "This package is being processed": "Αυτό το πακέτο είναι υπο επεξεργασία", "This package is not available": "Αυτό το πακέτο δεν είναι διαθέσιμο", "This package is on the queue": "Αυτό το πακέτο είναι στην ουρά", "This process is running with administrator privileges": "Η διεργασία εκτελείται με προνόμια διαχειριστή", "This project has no connection with the official {0} project — it's completely unofficial.": "Αυτό το έργο δεν έχει καμία σύνδεση με το επίσημο έργο {0} — είναι εντελώς ανεπίσημο.", "This setting is disabled": "Αυτή η ρύθμιση είναι απενεργοποιημένη", "This wizard will help you configure and customize WingetUI!": "Αυτ<PERSON><PERSON> ο οδηγός θα σας βοηθήσει να διαμορφώσετε και να προσαρμόσετε το UniGetUI!", "Toggle search filters pane": "Εναλλαγή παραθύρου φίλτρων αναζήτησης", "Translators": "Μετα<PERSON><PERSON><PERSON><PERSON>τ<PERSON>ς", "Try to kill the processes that refuse to close when requested to": "Προσπάθεια αναγκαστικού τερματισμού των διεργασιών που αρνούνται να κλείσουν όταν τους ζητηθεί", "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": "Η ενεργοποίηση αυτής της επιλογής επιτρέπει την αλλαγή του εκτελέσιμου αρχείου που χρησιμοποιείται για την αλληλεπίδραση με τους διαχειριστές πακέτων. Ενώ αυτό επιτρέπει την πιο λεπτομερή προσαρμογή των διαδικασιών εγκατάστασης, μπορεί επίσης να είναι επικίνδυνο.", "Type here the name and the URL of the source you want to add, separed by a space.": "Πληκτρολογήστε εδώ το όνομα και τη διεύθυνση URL της προέλευσης που θέλετε να προσθέσετε, διαχωρισμένα με ένα κενό.", "Unable to find package": "Αδύνατη η εύρεση του πακέτου", "Unable to load informarion": "Αδύνατη η φόρτωση πληροφοριών", "UniGetUI collects anonymous usage data in order to improve the user experience.": "Το UniGetUI συλλέγει ανώνυμα δεδομένα χρήσης ώστε να βελτιώσει την εμπειρία του χρήστη.", "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": "Το UniGetUI συλλέγει ανώνυμα δεδομένα χρήσης με μοναδικ<PERSON> σκοπό κατανόησης και βελτίωσης της εμπειρίας χρήσης.", "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": "Το UniGetUI ανίχνευσε μια νέα συντόμευση επιφάνειας εργασίας που μπορεί να διαγραφεί αυτόματα.", "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": "Το UniGetUI ανίχνευσε τις ακόλουθες συντομεύσεις επιφάνειας εργασίας που μπορούν να απομακρυνθούν αυτόματα σε μελλοντικές αναβαθμίσεις", "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": "Το UniGetUI ανίχνευσε {0} νέες συντομεύσεις επιφάνειας εργασίας που μπορούν να διαγραφούν αυτόματα.", "UniGetUI is being updated...": "Το UniGetUI έχει ενημερωθεί...", "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": "Το UniGetUI δε σχετίζεται με κανέναν από τους συμβατούς διαχειριστές πακέτων. Το UniGetUI είναι ένα ανεξάρτητο έργο.", "UniGetUI on the background and system tray": "Το UniGetUI στο υπόβαθρο και στη γραμμή εργασιών", "UniGetUI or some of its components are missing or corrupt.": "Το UniGetUI ή ορισμένα από τα στοιχεία του λείπουν ή είναι κατεστραμμένα.", "UniGetUI requires {0} to operate, but it was not found on your system.": "Το UniGetUI απαιτεί {0} για να λειτουργήσει αλλά δεν βρέθηκε στο σύστημά σας.", "UniGetUI startup page:": "Σελίδα έναρξης του UniGetUI:", "UniGetUI updater": "Αναβαθμιστής UniGetUI", "UniGetUI version {0} is being downloaded.": "Η έκδοση {0} του UniGetUI λαμβάνεται.", "UniGetUI {0} is ready to be installed.": "Το UniGetUI {0} ε<PERSON><PERSON><PERSON><PERSON> έτοιμο για εγκατάσταση.", "Uninstall": "Απεγκατάσταση", "Uninstall Scoop (and its packages)": "Απεγκατάστα<PERSON>η του <PERSON><PERSON> (και των πακέτων του)", "Uninstall and more": "Απεγκατάστα<PERSON>η και άλλα", "Uninstall and remove data": "Απεγκ<PERSON><PERSON><PERSON><PERSON>τα<PERSON>η και κατάργηση δεδομένων", "Uninstall as administrator": "Απεγκατάσταση ως διαχειριστής", "Uninstall canceled by the user!": "Η απεγκατάσταση ακυρώθηκε απο τον χρήστη!", "Uninstall failed": "Η απεγκατάσταση απέτυχε", "Uninstall options": "Επιλ<PERSON><PERSON><PERSON><PERSON> απεγκατάστασης", "Uninstall package": "Απεγκατάσταση πακέτου", "Uninstall package, then reinstall it": "Απεγκατάσταση πακέτου, μετ<PERSON> εγκατάσταση αυτού ξανά", "Uninstall package, then update it": "Απεγκ<PERSON>τάστα<PERSON>η πακέτου, μετά ενημέρωση αυτού", "Uninstall previous versions when updated": "Απεγκ<PERSON><PERSON>άσταση προηγούμενων εκδόσεων κατά την ενημέρωση", "Uninstall selected packages": "Απεγκατάσταση επιλεγμένων πακέτων", "Uninstall selection": "Απεγκατάσταση επιλεγμένων", "Uninstall succeeded": "Η απεγκατάσταση έγινε με επιτυχία", "Uninstall the selected packages with administrator privileges": "Απεγκ<PERSON><PERSON><PERSON>σταση των επιλεγμένων πακέτων ως διαχειριστής", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": "Τα πακέτα που δεν μπορούν να απεγκατασταθούν με την προέλευση που αναφέρεται ως «{0}» δε δημοσιεύονται σε κανέναν διαχειριστή πακέτων, επομένως δεν υπάρχουν διαθέσιμες πληροφορίες σχετικά με αυτά για να εμφανιστούν.", "Unknown": "Άγνωστο", "Unknown size": "Αγνωστο μέγεθος", "Unset or unknown": "Μη ορισμένο ή άγνωστο", "Up to date": "Ενημερωμένο", "Update": "Ενημέρωση", "Update WingetUI automatically": "Αυτόματη ενημέρωση UniGetUI", "Update all": "Ενημέρωση όλων", "Update and more": "Ενημέρωση και άλλα", "Update as administrator": "Ενημέρωση ως διαχειριστής", "Update check frequency, automatically install updates, etc.": "Συχνότητα ελέγχου για ενημερώσεις, αυτόματη εγκατάσταση ενημερώσεων κλπ.", "Update date": "Ημερομηνία ενημέρωσης", "Update failed": "Η ενημέρωση απέτυχε", "Update found!": "Βρέθηκε ενημέρωση!", "Update now": "Ενημέρωση τώρα", "Update options": "Επιλο<PERSON><PERSON>ς ενημέρωσης", "Update package indexes on launch": "Ενημέρωση ευρετηρ<PERSON>ων πακέτων κατά την εκκίνηση", "Update packages automatically": "Αυτόματη ενημέρωση πακέτων", "Update selected packages": "Ενημέρωση επιλεγμένων πακέτων", "Update selected packages with administrator privileges": "Ενημέρωση επιλεγμένων πακέτων με προνόμια διαχειριστή", "Update selection": "Ενημέρωση επιλεγμένων", "Update succeeded": "Η ενημέρωση έγινε με επιτυχία", "Update to version {0}": "Ενημέρωση στην έκδοση {0}", "Update to {0} available": "Υπάρχει διαθέσιμη ενημέρωση για το {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": "Αυτόματη ενημέρωση αρχείων θύρας Αποθετηρίου vcpkg (απαιτεί εγκατεστημένο Αποθετήριο)", "Updates": "Ενημερώσεις", "Updates available!": "Διαθέσιμες ενημερώσεις!", "Updates for this package are ignored": "Οι ενημερώσεις γι' αυτό το πακέτο αγνοούνται", "Updates found!": "Βρέθηκαν ενημερώσεις!", "Updates preferences": "Προτιμήσεις ενημερώσεων", "Updating WingetUI": "Ενημέρωση του UniGetUI", "Url": "Διεύθυνση URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": "Χρήση των πακέτων παλαιού τύπου WinGet αντί για τις Συλλογές Εντολών PowerShell", "Use a custom icon and screenshot database URL": "Χρήση διεύθυνσης URL προσαρμοσμένης βάσης δεδομένων για εικονίδια και στιγμιότυπα οθόνης", "Use bundled WinGet instead of PowerShell CMDlets": "Χρήση των πακέτων WinGet αντί για τις Συλλογές Εντολών PowerShell", "Use bundled WinGet instead of system WinGet": "Χρήση συλλογών WinGet αντί για το σύστημα WinGet", "Use installed GSudo instead of UniGetUI Elevator": "Χρήση του εγκατεστημένου GSudo αντί για το UniGetUI Elevator", "Use installed GSudo instead of the bundled one": "Χρήση του εγκατεστημένου G<PERSON>udo αντί του ενσωματωμένου", "Use system Chocolatey": "Χρήση συστή<PERSON>α<PERSON><PERSON>", "Use system Chocolatey (Needs a restart)": "Χρήση συστή<PERSON><PERSON><PERSON><PERSON> (Απαιτείται επανεκκίνηση)", "Use system Winget (Needs a restart)": "Χρή<PERSON><PERSON> συστ<PERSON><PERSON><PERSON><PERSON><PERSON> (Απαιτείται επανεκκίνηση)", "Use system Winget (System language must be set to english)": "Χρήση συστή<PERSON><PERSON><PERSON><PERSON> (η γλώσσα συστήματος πρέπει να οριστεί στα Αγγλικά)", "Use the WinGet COM API to fetch packages": "Χρήση του WinGet COM API για λήψη πακέτων", "Use the WinGet PowerShell Module instead of the WinGet COM API": "Χρήση του WinGet PowerShell Module αντί του WinGet COM API", "Useful links": "Χρήσιμοι σύνδεσμοι", "User": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "User interface preferences": "Προτιμήσεις περιβάλλοντος χρήστη", "User | Local": "Χρήστης | Τοπικό", "Username": "Όνομα χρήστη", "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": "Η χρήση του UniGetUI συνεπάγεται την αποδοχή της Γενικής Δημόσιας Άδειας GNU Lesser v2.1", "Using WingetUI implies the acceptation of the MIT License": "Η χρήση του UniGetUI συνεπάγεται την αποδοχή της άδειας MIT", "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": "Η ρίζα Vcpkg δεν βρέθηκε. Ορίστε τη μεταβλητή περιβάλλοντος %VCPKG_ROOT% ή ορίστε την από τις Ρυθμίσεις του UniGetUI", "Vcpkg was not found on your system.": "Το Vcpkg δεν βρέθηκε στο σύστημά σας.", "Verbose": "Πολύλογος", "Version": "Εκδοση", "Version to install:": "Έκδοση για εγκατάσταση:", "Version:": "Εκδοση:", "View GitHub Profile": "Προβολή προφίλ στο <PERSON>", "View WingetUI on GitHub": "Δείτε το UniGetUI στο GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": "Δείτε τον κώδικα προέλευσης του UniGetUI. Απ<PERSON> εκεί, μπορείτε να αναφέρετε σφάλματα ή να προτείνετε λειτουργίες ή ακόμα και να συμβάλετε απευθείας στο έργο UniGetUI ", "View mode:": "Κατάσταση προβολής:", "View on UniGetUI": "Δείτε το στο UniGetUI", "View page on browser": "Προβολή σελίδας στο φυλλομετρητή", "View {0} logs": "Δείτε {0} καταγραφές", "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": "Περιμένετε να συνδεθεί η συσκευή στο διαδίκτυο πριν επιχειρήσετε να κάνετε εργασίες που απαιτούν σύνδεση στο Διαδίκτυο.", "Waiting for other installations to finish...": "Αναμονή ολοκλήρωσης άλλων εγκαταστάσεων...", "Waiting for {0} to complete...": "Αναμονή για το {0} να ολοκληρωθεί...", "Warning": "Προειδοποίηση", "Warning!": "Προειδοποίηση!", "We are checking for updates.": "Ελέγχουμε για αναβαθμίσεις.", "We could not load detailed information about this package, because it was not found in any of your package sources": "Δεν ήταν δυνα<PERSON>ή η φόρτωση λεπτομερών πληροφοριών σχετικά με αυτό το πακέτο, επειδή δε βρέθηκε σε καμία από τις προελεύσεις πακέτου σας.", "We could not load detailed information about this package, because it was not installed from an available package manager.": "Δεν μπορέσαμε να φορτώσουμε λεπτομέρειες για το συγκεκριμένο πακέτο, διότι δεν εγκαταστάθηκε από τους διαθέσιμους διαχειριστές πακέτων.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Δεν ήταν δυνατή η {action} του {package}. Δοκιμάστε ξανά αργότερα. Πατήστε στο «{showDetails}» για να λάβετε τα αρχεία καταγραφής από το πρόγραμμα εγκατάστασης.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Δεν ήταν δυνατή η {action} του {package}. Δοκιμάστε ξανά αργότερα. Πατήστε στο «{showDetails}» για να λάβετε τα αρχεία καταγραφής από το πρόγραμμα απεγκατάστασης.", "We couldn't find any package": "Δεν βρέθηκε κανένα πακέτο", "Welcome to WingetUI": "Καλωσορί<PERSON>ατε στο UniGetUI", "When batch installing packages from a bundle, install also packages that are already installed": "Κατά την μαζική εγκατάσταση πακέτων από μια συλλογή, εγκαταστήστε επίσης πακέτα που είναι ήδη εγκατεστημένα", "When new shortcuts are detected, delete them automatically instead of showing this dialog.": "Όταν νέες συντομεύσεις ανιχνεύονται θα διαγράφονται αυτόματα αντί για εμφάνιση αυτού του παραθύρου.", "Which backup do you want to open?": "Ποι<PERSON> αντί<PERSON><PERSON>α<PERSON><PERSON> ασφαλείας θέλετε να ανοίξετε;", "Which package managers do you want to use?": "Ποιους διαχειριστές πακέτων θέλετε να χρησιμοποιήσετε;", "Which source do you want to add?": "Ποια προέλευση θέλετε να προσθέσετε;", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": "Ενώ το Winget μπορεί να χρησιμοποιηθεί στο UniGetUI, το UniGetUI μπορεί να χρησιμοποιηθεί με άλλους διαχειριστές πακέτων, κάτι που μπορεί να προκαλέσει σύγχυση. Στο παρελθόν, το UniGetUI είχε σχεδιαστεί για να λειτουργεί μόνο με το Winget, αλλά αυτό δεν ισχύει πλέον και επομένως το UniGetUI δεν αντιπροσωπεύει τον στόχο αυτού του έργου.", "WinGet could not be repaired": "Το WinGet δεν μπορεί να επισκευαστεί", "WinGet malfunction detected": "Ανιχνεύτηκε δυσλειτουργία του WinGet", "WinGet was repaired successfully": "Το WinGet επισκευάστηκε επιτυχώς", "WingetUI": "UniGetUI", "WingetUI - Everything is up to date": "WingetUI - <PERSON><PERSON><PERSON> είναι ενημερωμένα", "WingetUI - {0} updates are available": "WingetUI - {0} διαθέσιμες ενημερώσεις", "WingetUI - {0} {1}": "WingetUI - {0} {1}", "WingetUI Homepage": "Αρχική σελίδα UniGetUI", "WingetUI Homepage - Share this link!": "Αρχική σελίδα UniGetUI - Κοινή χρήση αυτού του συνδέσμου!", "WingetUI License": "Αδεια UniGetUI", "WingetUI Log": "Αρχε<PERSON><PERSON> καταγραφής UniGetUI", "WingetUI Repository": "Αποθετήριο UniGetUI", "WingetUI Settings": "Ρυθμίσεις UniGetUI", "WingetUI Settings File": "Αρχείο ρυθμίσεων UniGetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": "Το UniGetUI χρησιμοποιεί τις ακόλουθες βιβλιοθήκες. Χ<PERSON><PERSON><PERSON>ς αυτές, το UniGetUI δε θα υπήρχε.", "WingetUI Version {0}": "UniGetUI Εκδοση {0}", "WingetUI autostart behaviour, application launch settings": "Συμπεριφο<PERSON><PERSON> αυτόματης εκκίνησης UniGetUI, ρυθμίσεις εκκίνησης εφαρμογής", "WingetUI can check if your software has available updates, and install them automatically if you want to": "Το UniGetUI μπορεί να ελέγχει εάν το λογισμικό σας έχει διαθέσιμες ενημερώσεις και να τις εγκαθιστά αυτόματα αν θέλετε", "WingetUI display language:": "Γλώσσα εμφάνισης UniGetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": "Το UniGetUI έχει εκτελεστεί με δικαιώματα διαχειριστή, κάτι που δεν συνιστάται. Οταν εκτελείτε το UniGetUI ως διαχειριστής, ΚΑΘΕ λειτουργία που ξεκινά από το UniGetUI θα έχει δικαιώματα διαχειριστή. Μπορείτε ακόμα να χρησιμοποιήσετε το πρόγραμμα, αλλ<PERSON> συνιστούμε ανεπιφύλακτα να μην εκτελείτε το WingetUI με δικαιώματα διαχειριστή.", "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": "Το UniGetUI έχει μεταφραστεί σε περισσότερες από 40 γλώσσες χάρη στους εθελοντές μεταφραστές. Ευχαριστώ 🤝", "WingetUI has not been machine translated. The following users have been in charge of the translations:": "Το UniGetUI δεν έχει μεταφραστεί αυτόματα. Οι ακόλουθοι χρήστες έχουν αναλάβει τις μεταφράσεις:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": "Το UniGetUI είναι μια εφαρμογή που διευκολύνει τη διαχείριση του λογισμικού σας, παρέχοντας ένα γραφικό περιβάλλον χρήστη όλα σε ένα για τους διαχειριστές πακέτων γραμμής εντολών σας.", "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": "Το UniGetUI αλλάζει όνομα για να τονιστεί η διαφορά μεταξύ του UniGetUI (το περιβάλλον χρήστη που χρησιμοποιείτε αυτήν τη στιγμή) και του Winget (ένας διαχειριστής πακέτων που αναπτύχθηκε από τη Microsoft με την οποία δεν έχω καμία σχέση)", "WingetUI is being updated. When finished, WingetUI will restart itself": "Το UniGetUI ενημερώνεται. Όταν η ενημέρωση ολοκληρωθεί, το UniGetUI θα κάνει αυτόματα επανεκκίνηση", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": "Το UniGetUI είναι και θα είναι πάντα δωρεάν. Χωρίς διαφημίσεις, χωρ<PERSON>ς πιστωτική κάρτα, χω<PERSON><PERSON><PERSON> έκδοση premium. 100% δωρεάν, για πάντα.", "WingetUI log": "Αρχε<PERSON><PERSON> καταγραφής UniGetUI", "WingetUI tray application preferences": "Προτιμήσεις περιοχής ειδοποιήσεων UniGetUI", "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": "Το UniGetUI χρησιμοποιεί τις ακόλουθες βιβλιοθήκες. Χ<PERSON><PERSON><PERSON>ς αυτές, το UniGetUI δε θα υπήρχε.", "WingetUI version {0} is being downloaded.": "Γίνεται λήψη της έκδοσης {0} του UniGetUI", "WingetUI will become {newname} soon!": "Το UniGetUI θα γίνει σύντομα {newname}!", "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "Το UniGetUI δε θα ελέγχει περιοδικά για ενημερώσεις. Θα εξακολουθήσουν να ελέγχονται κατά την εκκίνηση, αλλά δε θα ειδοποιηθείτε για αυτές.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "Το UniGetUI θα εμφανίζει μια προτροπή UAC κάθε φορά που ένα πακέτο απαιτεί αύξηση δικαιωμάτων για να εγκατασταθεί.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": "Το UniGetUI θα ονομαστεί σύντομα {newname}. Αυτό δεν θα αντιπροσωπεύει καμία αλλαγή στην εφαρμογή. <PERSON><PERSON><PERSON> (ο προγραμματιστής) θα συνεχίσω την ανάπτυξη αυτού του έργου όπως κάνω αυτή τη στιγμή, αλλά με διαφορετικό όνομα.", "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "Το UniGetUI δε θα υπήρχε χωρίς τη βοήθεια των αγαπημένων μας συνεισφερόντων. Ρίξτε μια ματιά στο προφίλ τους στο <PERSON>, το UniGetUI δε θα μπορούσε να υπάρχει χωρίς αυτούς!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": "Το UniGetUI δε θα ήταν δυνατό χωρίς τη βοήθεια των συνεισφερόντων. Σας ευχαριστώ όλους 🥳", "WingetUI {0} is ready to be installed.": "Το UniGetUI {0} ε<PERSON><PERSON><PERSON><PERSON> έτοιμο για εγκατάσταση.", "Write here the process names here, separated by commas (,)": "Γρά<PERSON><PERSON><PERSON> εδώ τα ονόματα των διεργα<PERSON>ιών, διαχωρισμένα με κόμματα (,)", "Yes": "Ναι", "You are logged in as {0} (@{1})": "Εχετε συνδεθεί ως {0} (@{1})", "You can change this behavior on UniGetUI security settings.": "Μπορείτε να αλλάξετε αυτήν τη συμπεριφορά στις ρυθμίσεις ασφαλείας του UniGetUI.", "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": "Μπορείτε να ορίσετε τις εντολές που θα εκτελούνται πριν ή μετά την εγκατάσταση, την ενημέρωση ή την απεγκατάσταση αυτού του πακέτου. Θα εκτελούνται σε μια γραμμή εντολών, επομένως τα CMD scripts θα λειτουργούν εδώ.", "You have currently version {0} installed": "Αυτήν τη στιγμή έχετε εγκαταστήσει την έκδοση {0}", "You have installed WingetUI Version {0}": "Έχετε εγκατεστημένη την έκδοση {0} του UniGetUI", "You may lose unsaved data": "Ενδέχεται να χάσετε μη αποθηκευμένα δεδομένα", "You may need to install {pm} in order to use it with WingetUI.": "Ίσως χρειαστεί να εγκαταστήσετε το {pm} για να το χρησιμοποιήσετε με το UniGetUI.", "You may restart your computer later if you wish": "Μπορείτε αν θέλετε να κάνετε επανεκκίνηση του υπολογιστή σας αργότερα", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Θα ερωτηθείτε μόνο μια φορά, και τα δικαιώματα διαχειριστή θα παραχωρούνται αυτόματα σε πακέτα που τα χρειάζονται.", "You will be prompted only once, and every future installation will be elevated automatically.": "Θα ερωτηθείτε μόνο μία φορά και σε κάθε μελλοντική εγκατάσταση τα δικαιώματα θα αυξάνονται αυτόματα.", "You will likely need to interact with the installer.": "Πι<PERSON><PERSON><PERSON><PERSON><PERSON>, να χρειαστείτε να διαδράσετε με το πρόγραμμα εγκατάστασης", "[RAN AS ADMINISTRATOR]": "ΕΚΤΕΛΕΣΗ ΩΣ ΔΙΑΧΕΙΡΙΣΤΗΣ", "buy me a coffee": "αγοράστε μου έναν καφέ", "extracted": "αποσυμπιεσμένο", "feature": "χαρακτηριστικό", "formerly WingetUI": "πρώην WingetUI", "homepage": "ιστοσελίδα", "install": "εγκατάσταση", "installation": "εγκατάσταση", "installed": "εγκατεστημένο", "installing": "εγκατάσταση", "library": "βιβλιοθήκη", "mandatory": "επιτακτικό", "option": "επιλογή", "optional": "προαιρετικό", "uninstall": "απεγκατάσταση", "uninstallation": "απεγκατάσταση", "uninstalled": "απεγκατεστημένο", "uninstalling": "απεγκατάσταση", "update(noun)": "ενημέρωση", "update(verb)": "ενημερώνω", "updated": "ενημερωμένο", "updating": "ενημέρωση", "version {0}": "έκδοση {0}", "{0} Install options are currently locked because {0} follows the default install options.": "{0} Οι επιλογές εγκατάστασης είναι αυτήν τη στιγμή κλειδωμένες επειδή το {0} ακολουθεί τις προεπιλεγμένες επιλογές εγκατάστασης.", "{0} Uninstallation": "Απεγ<PERSON><PERSON>τάσταση του {0}", "{0} aborted": "{0} ματαιωμένο", "{0} can be updated": "Το {0} μπορεί να ενημερωθεί", "{0} can be updated to version {1}": "Το {0} μπορεί να ενημερωθεί στην έκδοση {1}", "{0} days": "{0} ημέρες", "{0} desktop shortcuts created": "{0} συντομεύσεις επιφάνειας εργασίας δημιουργήθηκαν", "{0} failed": "{0} αποτυχημένο", "{0} has been installed successfully.": "Το {0} εγκα<PERSON><PERSON>στάθηκε με επιτυχία", "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": "Το {0} εγκα<PERSON><PERSON><PERSON>τάθηκε με επιτυχία. Συνιστάται η επανεκκίνηση του UniGetUI για την ολοκλήρωση της εγκατάστασης", "{0} has failed, that was a requirement for {1} to be run": "{0} α<PERSON><PERSON><PERSON>υ<PERSON><PERSON> που ήταν προαπαιτούμενο για το {1} για να εκτελεστεί", "{0} homepage": "Αρχική σελίδα {0}", "{0} hours": "{0} ώρες", "{0} installation": "{0} εγκατ<PERSON>σταση", "{0} installation options": "{0} επιλο<PERSON><PERSON><PERSON> εγκατάστασης", "{0} installer is being downloaded": "{0} πρόγραμμα εγκατάστασης λαμβάνεται", "{0} is being installed": "Το {0} εγκαθίσταται", "{0} is being uninstalled": "Το {0} απεγ<PERSON><PERSON><PERSON>ίσταται", "{0} is being updated": "Το {0} ενημερώνεται", "{0} is being updated to version {1}": "Το {0} ενημ<PERSON>ρών<PERSON>ται στην έκδοση {1}", "{0} is disabled": "Το {0} είν<PERSON><PERSON> απενεργοποιημένο", "{0} minutes": "{0} λεπτά", "{0} months": "{0} μήνες", "{0} packages are being updated": "{0} πακ<PERSON><PERSON><PERSON> ενημερώνονται", "{0} packages can be updated": "{0} πακέτα μπορούν να ενημερωθούν", "{0} packages found": "Βρέθηκαν {0} πακέτα", "{0} packages were found": "Βρέθηκαν {0} πακέτα", "{0} packages were found, {1} of which match the specified filters.": "Βρέθηκαν {0} πα<PERSON><PERSON><PERSON><PERSON>, {1} από τα οποία ταιριάζουν με τα καθορισμένα φίλτρα.", "{0} settings": "{0} ρυθμίσεις", "{0} status": "{0} κατάσταση", "{0} succeeded": "{0} επιτυχήμένα", "{0} update": "{0} ενημέρωση", "{0} updates are available": "{0} ενημερώσεις είναι διαθέσιμες", "{0} was {1} successfully!": "Το {0} {1} με επιτυχία!", "{0} weeks": "{0} εβδομάδες", "{0} years": "{0} έτη", "{0} {1} failed": "{0} {1} απέτυχε", "{package} Installation": "Εγκατάσταση {package}", "{package} Uninstall": "Απεγκατάσταση {package}", "{package} Update": "Ενημέρωση {package}", "{package} could not be installed": "Δεν ήταν δυνατή η εγκατάσταση του {package}", "{package} could not be uninstalled": "Δεν ήταν δυνατή η απεγκατάσταση του {package}", "{package} could not be updated": "Δεν ήταν δυνατή η ενημέρωση του {package}", "{package} installation failed": "Η εγκατάσταση του {package} απέτυχε", "{package} installer could not be downloaded": "Το πρόγραμμα εγκατάστασης {package} δεν ελήφθει", "{package} installer download": "Το πρόγραμμα εγκατάστασης {package} λαμβάνεται", "{package} installer was downloaded successfully": "Το πρόγραμμα εγκατάστασης {package} ελήφθη επιτυχώς", "{package} uninstall failed": "Η απεγκατάσταση του {package} απέτυχε", "{package} update failed": "Η ενημέρωση του {package} απέτυχε", "{package} update failed. Click here for more details.": "Η εγκατάσταση του {package} απέτυχε. Πατήστε εδώ για περισσότερες λεπτομέρειες.", "{package} was installed successfully": "Το {package} εγκαταστάθηκε με επιτυχία", "{package} was uninstalled successfully": "Το {package} απεγκ<PERSON><PERSON>αστάθηκε με επιτυχία", "{package} was updated successfully": "Το {package} ενημερώθηκε με επιτυχία", "{pcName} installed packages": "Εγκατεστημένα πακέτα στο {pcName}", "{pm} could not be found": "Αποτυχία εύρεσης του {pm}", "{pm} found: {state}": "Το {pm} βρέθηκε: {state}", "{pm} is disabled": "Το {pm} είναι απενεργοποιημένο", "{pm} is enabled and ready to go": "Το {pm} είναι ενεργοποιημένο και έτοιμο για χρήση", "{pm} package manager specific preferences": "{pm} προτιμήσεις διαχειρίσεων πακέτων", "{pm} preferences": "{pm} προτιμήσεις", "{pm} version:": "Εκδοση {pm}:", "{pm} was not found!": "Το {pm} δεν βρέθηκε!"}