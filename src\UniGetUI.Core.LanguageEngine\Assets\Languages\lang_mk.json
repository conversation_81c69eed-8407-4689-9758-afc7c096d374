{"\"{0}\" is a local package and can't be shared": null, "\"{0}\" is a local package and does not have available details": null, "\"{0}\" is a local package and is not compatible with this feature": null, "(Last checked: {0})": null, "(Number {0} in the queue)": "(Број {0} во редицата)", "0 0 0 Contributors, please add your names/usernames separated by comas (for credit purposes). DO NOT Translate this entry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "0 packages found": "Пронајдени се 0 пакети", "0 updates found": "Пронајдени се 0 ажурирања", "1 - Errors": null, "1 day": "1 ден", "1 hour": "1 час", "1 month": null, "1 package was found": "Пронајден е 1 пакет", "1 update is available": null, "1 week": "1 недела", "1 year": null, "1. Navigate to the \"{0}\" or \"{1}\" page.": null, "2 - Warnings": null, "2. Locate the package(s) you want to add to the bundle, and select their leftmost checkbox.": null, "3 - Information (less)": null, "3. When the packages you want to add to the bundle are selected, find and click the option \"{0}\" on the toolbar.": null, "4 - Information (more)": null, "4. Your packages will have been added to the bundle. You can continue adding packages, or export the bundle.": null, "5 - information (debug)": null, "A popular C/C++ library manager. Full of C/C++ libraries and other C/C++-related utilities<br>Contains: <b>C/C++ libraries and related utilities</b>": null, "A repository full of tools and executables designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related tools and scripts</b>": null, "A repository full of tools designed with Microsoft's .NET ecosystem in mind.<br>Contains: <b>.NET related Tools</b>": "Репозиториум полн со алатки дизајнирани со .NET екосистемот на Microsoft во ум.<br>Содржи: <b>Алатки поврзани со .NET</b>", "A restart is required": "Потребно е рестартирање", "Abort install if pre-install command fails": null, "Abort uninstall if pre-uninstall command fails": null, "Abort update if pre-update command fails": null, "About": null, "About Qt6": "За Qt6", "About WingetUI": "За WingetUI", "About WingetUI version {0}": "За WingetUI верзија {0}", "About the dev": "За девелоперот", "Accept": null, "Action when double-clicking packages, hide successful installations": "Дејство при двоен клик на пакети, сокриј успешни инсталации", "Add": null, "Add a source to {0}": "Додај извор на {0}", "Add a timestamp to the backup file names": null, "Add a timestamp to the backup files": "Додадете временска ознака во резервните датотеки", "Add packages or open an existing bundle": null, "Add packages or open an existing package bundle": null, "Add packages to bundle": null, "Add packages to start": null, "Add selection to bundle": null, "Add source": "Додај извор", "Add updates that fail with a 'no applicable update found' to the ignored updates list": null, "Adding source {source}": null, "Adding source {source} to {manager}": null, "Addition succeeded": null, "Administrator privileges": "Администраторски привилегии", "Administrator privileges preferences": "Преференци за администраторски привилегии", "Administrator rights": "Администраторски права", "Administrator rights and other dangerous settings": null, "Advanced options": null, "All files": "Сите датотеки", "All versions": "Сите верзии", "Allow changing the paths for package manager executables": null, "Allow custom command-line arguments": null, "Allow importing custom command-line arguments when importing packages from a bundle": null, "Allow importing custom pre-install and post-install commands when importing packages from a bundle": null, "Allow package operations to be performed in parallel": null, "Allow parallel installs (NOT RECOMMENDED)": "Дозволи паралелни инсталации (НЕ СЕ ПРЕПОРАЧУВА)", "Allow pre-release versions": null, "Allow {pm} operations to be performed in parallel": null, "Alternatively, you can also install {0} by running the following command in a Windows PowerShell prompt:": null, "Always elevate {pm} installations by default": null, "Always run {pm} operations with administrator rights": null, "An error occurred": null, "An error occurred when adding the source: ": null, "An error occurred when attempting to show the package with Id {0}": null, "An error occurred when checking for updates: ": null, "An error occurred while loading a backup: ": null, "An error occurred while logging in: ": null, "An error occurred while processing this package": "Настана грешка при обработката на овој пакет", "An error occurred:": null, "An interal error occurred. Please view the log for further details.": null, "An unexpected error occurred:": null, "An unexpected issue occurred while attempting to repair WinGet. Please try again later": null, "An update was found!": null, "Android Subsystem": "Андроид подсистем", "Another source": "Друг извор", "Any new shorcuts created during an install or an update operation will be deleted automatically, instead of showing a confirmation prompt the first time they are detected.": null, "Any shorcuts created or modified outside of UniGetUI will be ignored. You will be able to add them via the {0} button.": null, "Any unsaved changes will be lost": null, "App Name": "Име на апликацијата", "Appearance": null, "Application theme, startup page, package icons, clear successful installs automatically": null, "Application theme:": "Тема на апликацијата:", "Apply": null, "Architecture to install:": "Архитектура за инсталирање:", "Are these screenshots wron or blurry?": "Дали овие слики од екранот се погрешни или заматени?", "Are you really sure you want to enable this feature?": null, "Are you sure you want to create a new package bundle? ": null, "Are you sure you want to delete all shortcuts?": null, "Are you sure?": "Дали сте сигурни?", "Ascendant": null, "Ask for administrator privileges once for each batch of operations": null, "Ask for administrator rights when required": "Побарај администраторски права кога е потребно", "Ask once or always for administrator rights, elevate installations by default": "Побарајте еднаш или секогаш за администраторски права, подигнете ги инсталациите стандардно", "Ask only once for administrator privileges": null, "Ask only once for administrator privileges (not recommended)": "Побарај администраторски привилегии само еднаш (не е препорачано)", "Ask to delete desktop shortcuts created during an install or upgrade.": null, "Attention required": "Потребно е внимание", "Authenticate to the proxy with an user and a password": null, "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatic desktop shortcut remover": null, "Automatically save a list of all your installed packages to easily restore them.": "Автоматски зачувајте листа со сите ваши инсталирани пакети за лесно да ги вратите.", "Automatically save a list of your installed packages on your computer.": "Автоматски зачувајте листа со инсталираните пакети на вашиот компјутер.", "Autostart WingetUI in the notifications area": "Автоматско стартување на WingetUI во областа за известувања", "Available Updates": null, "Available updates: {0}": "Достапни ажурирања: {0}", "Available updates: {0}, not finished yet...": "Достапни ажурирања: {0}, сè уште не е завршено...", "Backing up packages to GitHub Gist...": null, "Backup": null, "Backup Failed": null, "Backup Successful": null, "Backup and Restore": null, "Backup installed packages": "Резервна копија на инсталираните пакети", "Backup location": null, "Become a contributor": null, "Become a translator": null, "Begin the process to select a cloud backup and review which packages to restore": null, "Beta features and other options that shouldn't be touched": "Бета функции и други опции што не треба да се допираат", "Both": "Двете", "Bundle security report": null, "But here are other things you can do to learn about WingetUI even more:": "Но, еве други работи што можете да ги направите за да научите повеќе за WingetUI:", "By toggling a package manager off, you will no longer be able to see or update its packages.": null, "Cache administrator rights and elevate installers by default": null, "Cache administrator rights, but elevate installers only when required": null, "Cache was reset successfully!": "Кешот е успешно ресетиран!", "Can't {0} {1}": "Не е возможно {0} на {1}", "Cancel": "Откажи", "Cancel all operations": null, "Change backup output directory": "Променете ја папката за резервни копии", "Change default options": null, "Change how UniGetUI checks and installs available updates for your packages": null, "Change how UniGetUI handles install, update and uninstall operations.": null, "Change how UniGetUI installs packages, and checks and installs available updates": null, "Change how operations request administrator rights": null, "Change install location": "Променете ја локацијата за инсталирање", "Change this": null, "Change this and unlock": null, "Check for package updates periodically": "Повремено проверувајте за ажурирања на пакетите", "Check for updates": null, "Check for updates every:": "Проверувајте за ажурирања секој:", "Check for updates periodically": "Повремено проверувајте за ажурирања", "Check for updates regularly, and ask me what to do when updates are found.": "Редовно проверувај за ажурирања и прашајте ме што да правам кога ќе се најдат ажурирања.", "Check for updates regularly, and automatically install available ones.": "Редовно проверувајте за ажурирања и автоматски инсталирај ги достапните.", "Check out my {0} and my {1}!": "Погледнете го мојот {0} и мојот {1}!", "Check out some WingetUI overviews": "Погледнете некои прегледи на WingetUI", "Checking for other running instances...": "Се проверува за други пуштени инстанци...", "Checking for updates...": "Се проверува за ажурирања...", "Checking found instace(s)...": "Се проверуваат пронајдените инстаци...", "Choose how many operations shouls be performed in parallel": null, "Clear cache": null, "Clear finished operations": null, "Clear selection": "Исчисти го изборот", "Clear successful operations": null, "Clear successful operations from the operation list after a 5 second delay": null, "Clear the local icon cache": null, "Clearing Scoop cache - WingetUI": null, "Clearing Scoop cache...": "Се чисти Scoop кешот...", "Click here for more details": null, "Click on Install to begin the installation process. If you skip the installation, UniGetUI may not work as expected.": null, "Close": "Затвори", "Close UniGetUI to the system tray": null, "Close WingetUI to the notification area": "Затвори го WingetUI во областа за известување", "Cloud backup uses a private GitHub Gist to store a list of installed packages": null, "Cloud package backup": null, "Command-line Output": null, "Command-line to run:": null, "Compare query against": "Спореди го барањето со", "Compatible with authentication": null, "Compatible with proxy": null, "Component Information": "Информации за компонентите", "Concurrency and execution": null, "Connect the internet using a custom proxy": null, "Continue": null, "Contribute to the icon and screenshot repository": "Придонесете во репозиториумот за икони и слики од екранот", "Contributors": "Придонесувачи", "Copy": "Коп<PERSON><PERSON><PERSON><PERSON>", "Copy to clipboard": "Копирај во таблата со исечоци", "Could not add source": null, "Could not add source {source} to {manager}": null, "Could not back up packages to GitHub Gist: ": null, "Could not create bundle": null, "Could not load announcements - ": null, "Could not load announcements - HTTP status code is $CODE": null, "Could not remove source": null, "Could not remove source {source} from {manager}": null, "Could not remove {source} from {manager}": null, "Credentials": null, "Current Version": "Тековна верзија", "Current status: Not logged in": null, "Current user": "Тековен корисник", "Custom arguments:": null, "Custom command-line arguments can change the way in which programs are installed, upgraded or uninstalled, in a way UniGetUI cannot control. Using custom command-lines can break packages. Proceed with caution.": null, "Custom command-line arguments:": "Приспособени аргументи на командната линија:", "Custom install arguments:": null, "Custom uninstall arguments:": null, "Custom update arguments:": null, "Customize WingetUI - for hackers and advanced users only": "Прилагодете го WingetUI - само за хакери и напредни корисници", "DEBUG BUILD": null, "DISCLAIMER: WE ARE NOT RESPONSIBLE FOR THE DOWNLOADED PACKAGES. PLEASE MAKE SURE TO INSTALL ONLY TRUSTED SOFTWARE.": "ОДГОВОРУВАЊЕ: НИЕ НЕ СМЕ ОДГОВОРНИ ЗА ПРЕЗЕМАНИТЕ ПАКЕТИ. ВЕ МОЛИМЕ ОСИГУРАЈТЕ СЕ ДА ИНСТАЛИРАТЕ САМО ДОВЕРЛИВ СОФТВЕР.", "Dark": "Темна", "Decline": null, "Default": "Стандардно", "Default installation options for {0} packages": null, "Default preferences - suitable for regular users": "Стандардни поставки - погодни за обични корисници", "Default vcpkg triplet": null, "Delete?": null, "Dependencies:": null, "Descendant": null, "Description:": "Опис:", "Desktop shortcut created": null, "Details of the report:": null, "Developing is hard, and this application is free. But if you liked the application, you can always <b>buy me a coffee</b> :)": "Развивањето е тешко, а оваа апликација е бесплатна. Но, ако ви се допадна апликацијата, секогаш можете <b>да ми купите кафе</b> :)", "Directly install when double-clicking an item on the \"{discoveryTab}\" tab (instead of showing the package info)": null, "Disable new share API (port 7058)": "Оневозможи ново API за споделување (порта 7058)", "Disable the 1-minute timeout for package-related operations": null, "Disclaimer": null, "Discover Packages": "Откријте пакети", "Discover packages": null, "Distinguish between\nuppercase and lowercase": "Разликувај помеѓу големи и мали букви", "Distinguish between uppercase and lowercase": null, "Do NOT check for updates": "НЕ проверувајте за ажурирања", "Do an interactive install for the selected packages": "Направи интерактивна инсталација за избраните пакети", "Do an interactive uninstall for the selected packages": "Направи интерактивно деинсталирање на избраните пакети", "Do an interactive update for the selected packages": "Направи интерактивно ажурирање за избраните пакети", "Do not automatically install updates when the battery saver is on": null, "Do not automatically install updates when the network connection is metered": null, "Do not download new app translations from GitHub automatically": "Не преземајте автоматски нови преводи на апликацијата од GitHub", "Do not ignore updates for this package anymore": null, "Do not remove successful operations from the list automatically": null, "Do not show this dialog again for {0}": null, "Do not update package indexes on launch": "Не ги ажурирајте индексите на пакетите при стартување", "Do you accept that UniGetUI collects and sends anonymous usage statistics, with the sole purpose of understanding and improving the user experience?": null, "Do you find WingetUI useful? If you can, you may want to support my work, so I can continue making WingetUI the ultimate package managing interface.": null, "Do you find WingetUI useful? You'd like to support the developer? If so, you can {0}, it helps a lot!": "Дали ви е кориснен WingetUI? Сакатге да го поддржите програмерот? Ако е така, можете да ми {0}, тоа многу помага!", "Do you really want to reset this list? This action cannot be reverted.": null, "Do you really want to uninstall the following {0} packages?": null, "Do you really want to uninstall {0} packages?": "Дали навистина сакате да деинсталирате {0} пакети?", "Do you really want to uninstall {0}?": "Дали навистина сакате да деинсталирате {0}?", "Do you want to restart your computer now?": "Дали сакате да го рестартирате компјутерот сега?", "Do you want to translate WingetUI to your language? See how to contribute <a style=\"color:{0}\" href=\"{1}\"a>HERE!</a>": "Сакате да го преведете WingetUI на вашиот јазик? Погледнете како да придонесете <a style=\"color:{0}\" href=\"{1}\"a>ТУКА!</a>", "Don't feel like donating? Don't worry, you can always share WingetUI with your friends. Spread the word about WingetUI.": null, "Donate": "Дони<PERSON><PERSON><PERSON>те", "Done!": null, "Download failed": null, "Download installer": null, "Download operations are not affected by this setting": null, "Download selected installers": null, "Download succeeded": null, "Download updated language files from GitHub automatically": null, "Downloading": null, "Downloading backup...": null, "Downloading installer for {package}": null, "Downloading package metadata...": "Се преземаат метаподатоци за пакетот...", "Enable Scoop cleanup on launch": "Овозможи чистење на Scoop при стартување", "Enable WingetUI notifications": "Овозможи известувања од WingetUI", "Enable an [experimental] improved WinGet troubleshooter": null, "Enable and disable package managers, change default install options, etc.": null, "Enable background CPU Usage optimizations (see Pull Request #3278)": null, "Enable background api (WingetUI Widgets and Sharing, port 7058)": null, "Enable it to install packages from {pm}.": null, "Enable the automatic WinGet troubleshooter": null, "Enable the new UniGetUI-Branded UAC Elevator": null, "Enable the new process input handler (StdIn automated closer)": null, "Enable the settings below if and only if you fully understand what they do, and the implications they may have.": null, "Enable {pm}": "Овозможи {pm}", "Enter proxy URL here": null, "Entries that show in RED will be IMPORTED.": null, "Entries that show in YELLOW will be IGNORED.": null, "Error": "Грешка", "Everything is up to date": null, "Exact match": null, "Existing shortcuts on your desktop will be scanned, and you will need to pick which ones to keep and which ones to remove.": null, "Expand version": null, "Experimental settings and developer options": "Експериментални поставки и опции за програмери", "Export": "Експортир<PERSON><PERSON>", "Export log as a file": "Експортирај го дневникот како датотека", "Export packages": "Експортирај пакети", "Export selected packages to a file": "Експортирај ги избраните пакети во датотека", "Export settings to a local file": "Експортирај ги поставките во локална датотека", "Export to a file": "Експортирај во датотека", "Failed": null, "Fetching available backups...": null, "Fetching latest announcements, please wait...": null, "Filters": "Филтри", "Finish": "Заврши", "Follow system color scheme": "Следете ја системската шема на бои", "Follow the default options when installing, upgrading or uninstalling this package": null, "For security reasons, changing the executable file is disabled by default": null, "For security reasons, custom command-line arguments are disabled by default. Go to UniGetUI security settings to change this. ": null, "For security reasons, pre-operation and post-operation scripts are disabled by default. Go to UniGetUI security settings to change this. ": null, "Force ARM compiled winget version (ONLY FOR ARM64 SYSTEMS)": "Форсир<PERSON>ј ARM компајлирана winget верзија (САМО ЗА ARM64 СИСТЕМИ)", "Formerly known as WingetUI": null, "Found": "Пронајдено", "Found packages: ": null, "Found packages: {0}": "Пронајдени пакети: {0}", "Found packages: {0}, not finished yet...": "Пронајдени пакети: {0}, сè уште не е завршено...", "General preferences": "Општи поставки", "GitHub profile": "GitHub профил", "Global": "Глобално", "Go to UniGetUI security settings": null, "Great repository of unknown but useful utilities and other interesting packages.<br>Contains: <b>Utilities, Command-line programs, General Software (extras bucket required)</b>": null, "Great! You are on the latest version.": null, "Grid": null, "Help": "Помош", "Help and documentation": "Помош и документација", "Here you can change UniGetUI's behaviour regarding the following shortcuts. Checking a shortcut will make UniGetUI delete it if if gets created on a future upgrade. Unchecking it will keep the shortcut intact": null, "Hi, my name is Martí, and i am the <i>developer</i> of WingetUI. WingetUI has been entirely made on my free time!": "Здраво, моето име е Марти, и јас сум <i>програмерот</i> на WingetUI. WingetUI е целосно направен во моето слободно време!", "Hide details": "Сокриј детали", "Homepage": "Почетна страница", "Hooray! No updates were found.": "Ура! Не беа пронајдени ажурирања!", "How should installations that require administrator privileges be treated?": "Како да се третираат инсталациите кои бараат администраторски привилегии?", "How to add packages to a bundle": null, "I understand": null, "Icons": null, "Id": null, "If you have cloud backup enabled, it will be saved as a GitHub Gist on this account": null, "Ignore custom pre-install and post-install commands when importing packages from a bundle": null, "Ignore future updates for this package": "Игнорирај идни ажурирања за овој пакет", "Ignore packages from {pm} when showing a notification about updates": null, "Ignore selected packages": "Игнорирај ги избраните пакети", "Ignore special characters": "Игнорирај специјални знаци", "Ignore updates for the selected packages": "Игнорирај ажурирања за избраните пакети", "Ignore updates for this package": "Игнорирај ажурирања за овој пакет", "Ignored updates": "Игнорирани ажурирања", "Ignored version": "Игнорирана верзија", "Import": "Импортирање", "Import packages": "Импортирај пакети", "Import packages from a file": "Импортирајте пакети од датотека", "Import settings from a local file": "Импортирајте подесувања од локална датотека", "In order to add packages to a bundle, you will need to: ": null, "Initializing WingetUI...": "Иницијализирање на WingetUI...", "Install": "Инста<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install Scoop": "Инста<PERSON><PERSON><PERSON><PERSON><PERSON>", "Install and more": null, "Install and update preferences": null, "Install as administrator": "Инстали<PERSON><PERSON><PERSON> како администратор", "Install available updates automatically": null, "Install location can't be changed for {0} packages": null, "Install location:": null, "Install options": null, "Install packages from a file": "Инсталирајте пакети од датотека", "Install prerelease versions of UniGetUI": null, "Install selected packages": "Инстали<PERSON><PERSON><PERSON> ги избраните пакети", "Install selected packages with administrator privileges": "Инсталирај ги избраните пакети со администраторски привилегии", "Install selection": null, "Install the latest prerelease version": "Инсталирај ја најновата предиздавачка верзија", "Install updates automatically": "Автоматски инсталирај ажурирања", "Install {0}": null, "Installation canceled by the user!": "Инсталацијата е откажана од корисникот!", "Installation failed": null, "Installation options": "Опции за инсталација", "Installation scope:": "Опсег на инсталација:", "Installation succeeded": null, "Installed Packages": "Инсталирани пакети", "Installed Version": "Инстали<PERSON><PERSON>на верзија", "Installed packages": null, "Installer SHA256": "SHA256 инсталатер", "Installer SHA512": "SHA512 инсталатер", "Installer Type": "Тип на инсталатер", "Installer URL": "URL инсталатер", "Installer not available": null, "Instance {0} responded, quitting...": "Инстанцата {0} одговори, откажување...", "Instant search": "Инстант пребарување", "Integrity checks can be disabled from the Experimental Settings": null, "Integrity checks skipped": null, "Integrity checks will not be performed during this operation": null, "Interactive installation": "Интера<PERSON>тивна инсталација", "Interactive operation": null, "Interactive uninstall": "Интерактивна деинсталација", "Interactive update": "Интерактивно ажурирање", "Internet connection settings": null, "Is this package missing the icon?": "Дали на овој пакет му недостасува икона?", "Is your language missing or incomplete?": null, "It is not guaranteed that the provided credentials will be stored safely, so you may as well not use the credentials of your bank account": null, "It is recommended to restart UniGetUI after WinGet has been repaired": null, "It is strongly recommended to reinstall UniGetUI to adress the situation.": null, "It looks like WinGet is not working properly. Do you want to attempt to repair WinGet?": null, "It looks like you ran WingetUI as administrator, which is not recommended. You can still use the program, but we highly recommend not running WingetUI with administrator privileges. Click on \"{showDetails}\" to see why.": "Изгледа дека го имате пуштено WingetUI како администратор, што не се препорачува. Сè уште можете да ја користите програмата, но топло ви препорачуваме да не го стартувате WingetUI со администраторски привилегии. Кликнете на „{showDetails}“ за да видите зошто.", "Language": null, "Language, theme and other miscellaneous preferences": "Ја<PERSON><PERSON>к, тема и останати поставки", "Last updated:": "Последно ажурирање:", "Latest": "Најнова", "Latest Version": "Најнова верзија", "Latest Version:": "Најнова верзија:", "Latest details...": "Најнови детали...", "Launching subprocess...": null, "Leave empty for default": null, "License": "Лиценца", "Licenses": "Лиценци", "Light": "Светла", "List": null, "Live command-line output": "Излез од командна линија во живо", "Live output": null, "Loading UI components...": "Се вчитуваат UI компонентите...", "Loading WingetUI...": "Се вчитува WingetUI...", "Loading packages": null, "Loading packages, please wait...": null, "Loading...": "Се вчитува...", "Local": "Локално", "Local PC": "Локален компјутер", "Local backup advanced options": null, "Local machine": "Локална машина", "Local package backup": null, "Locating {pm}...": "Се лоцира {pm}...", "Log in": null, "Log in failed: ": null, "Log in to enable cloud backup": null, "Log in with GitHub": null, "Log in with GitHub to enable cloud package backup.": null, "Log level:": null, "Log out": null, "Log out failed: ": null, "Log out from GitHub": null, "Looking for packages...": "Се бараат пакети...", "Machine | Global": null, "Malformed command-line arguments can break packages, or even allow a malicious actor to gain privileged execution. Therefore, importing custom command-line arguments is disabled by default": null, "Manage": null, "Manage UniGetUI settings": null, "Manage WingetUI autostart behaviour from the Settings app": null, "Manage ignored packages": "Управувајте со игнорирани пакети", "Manage ignored updates": "Управувајте со игнорираните ажурирања", "Manage shortcuts": null, "Manage telemetry settings": null, "Manage {0} sources": "Управувајте со {0} извори", "Manifest": "Манифест", "Manifests": "Манифести", "Manual scan": null, "Microsoft's official package manager. Full of well-known and verified packages<br>Contains: <b>General Software, Microsoft Store apps</b>": "Официјален менаџер на пакети на Microsoft. Полн со добро познати и проверени пакети<br>Содржи: <b>Општ софтвер, апликации од Microsoft Store</b>", "Missing dependency": null, "More": null, "More details": "Повеќе детали", "More details about the shared data and how it will be processed": null, "More info": null, "NOTE: This troubleshooter can be disabled from UniGetUI Settings, on the WinGet section": null, "Name": "Име", "New": null, "New Version": "Нова верзија", "New bundle": null, "New version": "Нова верзија", "Nice! Backups will be uploaded to a private gist on your account": null, "No": "Не", "No applicable installer was found for the package {0}": null, "No dependencies specified": null, "No new shortcuts were found during the scan.": null, "No packages found": "Не се пронајдени пакети", "No packages found matching the input criteria": "Не се пронајдени пакети што одговараат на внесените критериуми", "No packages have been added yet": null, "No packages selected": "Нема избрани пакети", "No packages were found": null, "No personal information is collected nor sent, and the collected data is anonimized, so it can't be back-tracked to you.": null, "No results were found matching the input criteria": null, "No sources found": null, "No sources were found": "Не беа пронајдени извори", "No updates are available": "Нема достапни ажурирања", "Node JS's package manager. Full of libraries and other utilities that orbit the javascript world<br>Contains: <b>Node javascript libraries and other related utilities</b>": "Node JS управувач со пакети. Полн со библиотеки и други алатки што орбитираат околу светот на Javascript<br>Содржи: <b>Библиотеки на Node javascript и други поврзани алатки</b>", "Not available": "Не е достапно", "Not finding the file you are looking for? Make sure it has been added to path.": null, "Not found": "Не е пронајдено", "Not right now": null, "Notes:": "Белешки:", "Notification preferences": null, "Notification tray options": "Опции за фиоката за известувања", "Notification types": null, "NuPkg (zipped manifest)": null, "OK": "Во ред", "Ok": "Во ред", "Open": "Отвори", "Open GitHub": "Отв<PERSON><PERSON><PERSON>", "Open UniGetUI": null, "Open UniGetUI security settings": null, "Open WingetUI": null, "Open backup location": "Отвори ја локацијата за резервни копии", "Open existing bundle": null, "Open install location": null, "Open the welcome wizard": "Отвори го волшебникот за добредојде", "Operation canceled by user": null, "Operation cancelled": null, "Operation history": "Историја на операции", "Operation in progress": "Операција во тек", "Operation on queue (position {0})...": null, "Operation profile:": null, "Options saved": "Опциите се зачувани", "Order by:": null, "Other": null, "Other settings": null, "Package": null, "Package Bundles": null, "Package ID": "ИД на пакетот", "Package Manager": null, "Package Manager logs": "Дневници на менаџерот на пакети", "Package Managers": null, "Package Name": "Име на пакетот", "Package backup": null, "Package backup settings": null, "Package bundle": null, "Package details": "Детали за пакетот", "Package lists": null, "Package management made easy": null, "Package manager": null, "Package manager preferences": "Поставки на менаџерот на пакети", "Package managers": null, "Package not found": null, "Package operation preferences": null, "Package update preferences": null, "Package {name} from {manager}": null, "Package's default": null, "Packages": "Пакети", "Packages found: {0}": "Пронајдени пакети: {0}", "Partially": null, "Password": null, "Paste a valid URL to the database": "Залепете валидно URL во базата на податоци", "Pause updates for": null, "Perform a backup now": null, "Perform a cloud backup now": null, "Perform a local backup now": null, "Perform integrity checks at startup": null, "Performing backup, please wait...": null, "Periodically perform a backup of the installed packages": null, "Periodically perform a cloud backup of the installed packages": null, "Periodically perform a local backup of the installed packages": null, "Please check the installation options for this package and try again": null, "Please click on \"Continue\" to continue": null, "Please enter at least 3 characters": null, "Please note that certain packages might not be installable, due to the package managers that are enabled on this machine.": "Имајте во предвид дека одредени пакети можеби нема да можат да се инсталираат, поради менаџерите на пакети што се овозможени на оваа машина.", "Please note that not all package managers may fully support this feature": null, "Please note that packages from certain sources may be not exportable. They have been greyed out and won't be exported.": "Имајте во предвид дека пакети од одредени извори можеби не можат да се експортират. Тие се затемнети и нема да бидат експортирани.", "Please run UniGetUI as a regular user and try again.": null, "Please see the Command-line Output or refer to the Operation History for further information about the issue.": null, "Please select how you want to configure WingetUI": "Ве молиме изберете како сакате да го конфигурирате WingetUI", "Please try again later": null, "Please type at least two characters": "Ве молиме внесете најмалку два знака", "Please wait": null, "Please wait while {0} is being installed. A black window may show up. Please wait until it closes.": null, "Please wait...": "Ве молиме почекајте...", "Portable": "Преносливо", "Portable mode": null, "Post-install command:": null, "Post-uninstall command:": null, "Post-update command:": null, "PowerShell's package manager. Find libraries and scripts to expand PowerShell capabilities<br>Contains: <b>Modules, Scripts, Cmdlets</b>": null, "Pre and post install commands can do very nasty things to your device, if designed to do so. It can be very dangerous to import the commands from a bundle, unless you trust the source of that package bundle": null, "Pre and post install commands will be run before and after a package gets installed, upgraded or uninstalled. Be aware that they may break things unless used carefully": null, "Pre-install command:": null, "Pre-uninstall command:": null, "Pre-update command:": null, "PreRelease": null, "Preparing packages, please wait...": null, "Proceed at your own risk.": null, "Prohibit any kind of Elevation via UniGetUI Elevator or GSudo": null, "Proxy URL": null, "Proxy compatibility table": null, "Proxy settings": null, "Proxy settings, etc.": null, "Publication date:": "Датум на објавување:", "Publisher": "Издавач", "Python's library manager. Full of python libraries and other python-related utilities<br>Contains: <b>Python libraries and related utilities</b>": "Менаџер со библиотека на Python. Полн со библиотеки на Python и други алатки поврзани со Python<br>Содржи: <b>библиотеки на Python и поврзани алатки</b>", "Quit": "Затвори", "Quit WingetUI": null, "Reduce UAC prompts, elevate installations by default, unlock certain dangerous features, etc.": null, "Refer to the UniGetUI Logs to get more details regarding the affected file(s)": null, "Reinstall": null, "Reinstall package": "Реинсталирај го пакетот", "Related settings": null, "Release notes": "Белешки за изданието", "Release notes URL": null, "Release notes URL:": "URL на белешки за издавање:", "Release notes:": "Белешки за изданието:", "Reload": "Вчитај повторно", "Reload log": "Повторно вчитајте го дневникот", "Removal failed": null, "Removal succeeded": null, "Remove from list": null, "Remove permanent data": "Отстрани ги трајните податоци", "Remove selection from bundle": null, "Remove successful installs/uninstalls/updates from the installation list": "Отстранете ги успешните инсталации/деинсталации/ажурирања од списокот за инсталација", "Removing source {source}": null, "Removing source {source} from {manager}": null, "Repair UniGetUI": null, "Repair WinGet": null, "Report an issue or submit a feature request": null, "Repository": "Репозиториум", "Reset": "Ресетир<PERSON>ј", "Reset Scoop's global app cache": "Ресетирај го глобалниот апликациски кеш на Scoop", "Reset UniGetUI": null, "Reset WinGet": null, "Reset Winget sources (might help if no packages are listed)": "Ресетирај ги изворите на Winget (може да помогне ако нема наведени пакети)", "Reset WingetUI": "Ресетирај го WingetUI", "Reset WingetUI and its preferences": "Ресетирај го WingetUI и неговите поставки", "Reset WingetUI icon and screenshot cache": "Ресетирајте го WingetUI кешот за икони и слики од екранот", "Reset list": null, "Resetting Winget sources - WingetUI": null, "Restart": null, "Restart UniGetUI": null, "Restart WingetUI": "Рестартир<PERSON>ј го WingetUI", "Restart WingetUI to fully apply changes": "Рестартирајте го WingetUI за целосно применување на промените", "Restart later": "Рестартир<PERSON><PERSON> подоцна", "Restart now": "Рестартир<PERSON>ј сега", "Restart required": "Потребно е рестартирање", "Restart your PC to finish installation": "Рестартирајте го компјутерот за да ја завршите инсталацијата", "Restart your computer to finish the installation": "Рестартирајте го компјутерот за да ја завршите инсталацијата", "Restore a backup from the cloud": null, "Restrictions on package managers": null, "Restrictions on package operations": null, "Restrictions when importing package bundles": null, "Retry": "Обидете се повторно", "Retry as administrator": null, "Retry failed operations": null, "Retry interactively": null, "Retry skipping integrity checks": null, "Retrying, please wait...": null, "Return to top": "Врати се горе", "Run": null, "Run as admin": "Стартувај како администратор", "Run cleanup and clear cache": null, "Run last": null, "Run next": null, "Run now": null, "Running the installer...": "Се извршува инсталатерот...", "Running the uninstaller...": "Се извршува деинсталаторот...", "Running the updater...": "Се извршува ажурирачот...", "Save": null, "Save File": "Зачувај датотека", "Save and close": null, "Save as": null, "Save bundle as": null, "Save now": "Зачувај сега", "Saving packages, please wait...": null, "Scoop Installer - WingetUI": null, "Scoop Uninstaller - WingetUI": null, "Scoop package": "Scoop пакет", "Search": "Пребарување", "Search for desktop software, warn me when updates are available and do not do nerdy things. I don't want WingetUI to overcomplicate, I just want a simple <b>software store</b>": "Пребарај софтвер за десктоп, предупреди ме кога има достапни ажурирања и не прави чудни работи. Не сакам WingetUI да прекомплицира, сакам само едноставна <b>продавница за софтвер</b>", "Search for packages": "Пребарај пакети", "Search for packages to start": "Пребарај пакети за да започнете", "Search mode": null, "Search on available updates": "Пребарувај на достапни ажурирања", "Search on your software": "Пребарај во вашиот софтвер", "Searching for installed packages...": "Се пребаруваат инсталирани пакети...", "Searching for packages...": "Се бараат пакети...", "Searching for updates...": "Се бараат ажурирања...", "Select": "Избери", "Select \"{item}\" to add your custom bucket": "Изберете „{item}“ за да ја додадете вашата прилагодена кофа", "Select a folder": "Изберете папка", "Select all": "Избери сè", "Select all packages": "Избери ги сите пакети", "Select backup": null, "Select only <b>if you know what you are doing</b>.": "Изберете само <b>ако знаете што правите</b>.", "Select package file": "Изберете пакет датотека", "Select the backup you want to open. Later, you will be able to review which packages you want to install.": null, "Select the processes that should be closed before this package is installed, updated or uninstalled.": null, "Select the source you want to add:": null, "Select upgradable packages by default": null, "Select which <b>package managers</b> to use ({0}), configure how packages are installed, manage how administrator rights are handled, etc.": null, "Sent handshake. Waiting for instance listener's answer... ({0}%)": "Испратено ракување. Се чека одговор од слушателот на инстанцата... ({0}%)", "Set a custom backup file name": null, "Set custom backup file name": "Поставете име на резервната датотека", "Settings": null, "Share": null, "Share WingetUI": null, "Share anonymous usage data": null, "Share this package": "Споделете го овој пакет", "Should you modify the security settings, you will need to open the bundle again for the changes to take effect.": null, "Show UniGetUI on the system tray": null, "Show UniGetUI's version and build number on the titlebar.": null, "Show WingetUI": "Прикажи го WingetUI", "Show a notification when an installation fails": "Прикажи известување кога инсталација не успее", "Show a notification when an installation finishes successfully": null, "Show a notification when an operation fails": null, "Show a notification when an operation finishes successfully": null, "Show a notification when there are available updates": "Прикажи известување кога има достапни ажурирања", "Show a silent notification when an operation is running": null, "Show details": "Прикажи детали", "Show in explorer": null, "Show info about the package on the Updates tab": "Прикажи информации за пакетот на јазичето Ажурирања", "Show missing translation strings": "Прикажи ги преводите што недостасуваат", "Show notifications on different events": null, "Show package details": "Прикажи детали за пакетот", "Show package icons on package lists": null, "Show similar packages": null, "Show the live output": "Прикажи го излезот во живо", "Size": null, "Skip": "Прескокни", "Skip hash check": "Прескокни ја проверката на хашот", "Skip hash checks": null, "Skip integrity checks": null, "Skip minor updates for this package": null, "Skip the hash check when installing the selected packages": "Прескокни ја проверката на хашот при инсталирање на избраните пакети", "Skip the hash check when updating the selected packages": "Прескокни ја проверката на хашот при ажурирање на избраните пакети", "Skip this version": "Прескокни ја оваа верзија", "Software Updates": "Софтверски ажурирања", "Something went wrong": null, "Something went wrong while launching the updater.": null, "Source": "Извор", "Source URL:": null, "Source added successfully": null, "Source addition failed": null, "Source name:": null, "Source removal failed": null, "Source removed successfully": null, "Source:": "Извор:", "Sources": "Извори", "Start": "Почни", "Starting daemons...": "Се креваат позадински сервиси...", "Starting operation...": null, "Startup options": "Поставки при стартување", "Status": "Статус", "Stuck here? Skip initialization": "Заглавени сте тука? Прескокнете ја иницијализацијата", "Suport the developer": "Поддржете го програмерот", "Support me": null, "Support the developer": null, "Systems are now ready to go!": "Системите се сега подготвени за работа!", "Telemetry": null, "Text": null, "Text file": "Текстуална датотека", "Thank you ❤": null, "Thank you 😉": null, "The Rust package manager.<br>Contains: <b>Rust libraries and programs written in Rust</b>": null, "The backup will NOT include any binary file nor any program's saved data.": "Резервната копија НЕМА да вклучува никаква бинарна датотека ниту зачувани податоци од било која програма.", "The backup will be performed after login.": "Резервната копија ќе биде креирана по најавување.", "The backup will include the complete list of the installed packages and their installation options. Ignored updates and skipped versions will also be saved.": null, "The bundle you are trying to load appears to be invalid. Please check the file and try again.": null, "The checksum of the installer does not coincide with the expected value, and the authenticity of the installer can't be verified. If you trust the publisher, {0} the package again skipping the hash check.": null, "The classical package manager for windows. You'll find everything there. <br>Contains: <b>General Software</b>": null, "The cloud backup completed successfully.": null, "The cloud backup has been loaded successfully.": null, "The current bundle has no packages. Add some packages to get started": null, "The executable file for {0} was not found": null, "The following options will be applied by default each time a {0} package is installed, upgraded or uninstalled.": null, "The following packages are going to be exported to a JSON file. No user data or binaries are going to be saved.": "Следниве пакети ќе се експортираат во JSON датотека. Нема да се зачуваат кориснички податоци или бинарни датотеки.", "The following packages are going to be installed on your system.": "Следните пакети ќе бидат инсталирани на вашиот систем.", "The following settings may pose a security risk, hence they are disabled by default.": null, "The following settings will be applied each time this package is installed, updated or removed.": null, "The following settings will be applied each time this package is installed, updated or removed. They will be saved automatically.": null, "The icons and screenshots are maintained by users like you!": "Иконите и сликите од екранот се одржуваат од корисници како вас!", "The installer authenticity could not be verified.": null, "The installer has an invalid checksum": "Инсталаторот има невалидна контролна сума", "The installer hash does not match the expected value.": null, "The local icon cache currently takes {0} MB": null, "The main goal of this project is to create an intuitive UI to manage the most common CLI package managers for Windows, such as Winget and Scoop.": null, "The package \"{0}\" was not found on the package manager \"{1}\"": null, "The package bundle could not be created due to an error.": null, "The package bundle is not valid": null, "The package manager \"{0}\" is disabled": null, "The package manager \"{0}\" was not found": null, "The package {0} from {1} was not found.": null, "The packages listed here won't be taken in account when checking for updates. Double-click them or click the button on their right to stop ignoring their updates.": "Пакетите наведени овде нема да се земат во предвид при проверка за ажурирања. Кликнете двапати на нив или кликнете на копчето на нивната десна страна за да престанете да ги игнорирате нивните ажурирања.", "The selected packages have been blacklisted": "Избраните пакети се ставени на црна листа", "The settings will list, in their descriptions, the potential security issues they may have.": null, "The size of the backup is estimated to be less than 1MB.": "Големината на резервната копија се проценува дека е помала од 1MB.", "The source {source} was added to {manager} successfully": null, "The source {source} was removed from {manager} successfully": null, "The system tray icon must be enabled in order for notifications to work": null, "The update process has been aborted.": null, "The update process will start after closing UniGetUI": null, "The update will be installed upon closing WingetUI": null, "The update will not continue.": null, "The user has canceled {0}, that was a requirement for {1} to be run": null, "There are no new UniGetUI versions to be installed": null, "There are ongoing operations. Quitting WingetUI may cause them to fail. Do you want to continue?": null, "There are some great videos on YouTube that showcase WingetUI and its capabilities. You could learn useful tricks and tips!": "Има неколку одлични видеа на YouTube кои го прикажуваат WingetUI и неговите способности. Може да научите корисни трикови и совети!", "There are two main reasons to not run WingetUI as administrator:\n The first one is that the Scoop package manager might cause problems with some commands when ran with administrator rights.\n The second one is that running WingetUI as administrator means that any package that you download will be ran as administrator (and this is not safe).\n Remeber that if you need to install a specific package as administrator, you can always right-click the item -> Install/Update/Uninstall as administrator.": "Постојат две главни причини да не се извршува WingetUI како администратор: Првата е дека Scoop менаџерот на пакети може да предизвика проблеми со некои команди кога работи со администраторски права. Вториот е дека извршувањето на WingetUI како администратор значи дека секој пакет што ќе го преземете ќе се извршува како администратор (а ова не е безбедно). Запомнете дека ако треба да инсталирате одреден пакет како администратор, секогаш можете да кликнете со десното копче на ставката -> Инсталирај/Ажурирај/Деинсталирај како администратор.", "There is an error with the configuration of the package manager \"{0}\"": null, "There is an installation in progress. If you close WingetUI, the installation may fail and have unexpected results. Do you still want to quit WingetUI?": null, "They are the programs in charge of installing, updating and removing packages.": "Тие се програмите задолжени за инсталирање, ажурирање и отстранување на пакети.", "Third-party licenses": null, "This could represent a <b>security risk</b>.": "Ова може да претставува <b>безбедносен ризик</b>.", "This is not recommended.": null, "This is probably due to the fact that the package you were sent was removed, or published on a package manager that you don't have enabled. The received ID is {0}": null, "This is the <b>default choice</b>.": "Ова е <b>стандардниот избор</b>.", "This may help if WinGet packages are not shown": null, "This may help if no packages are listed": null, "This may take a minute or two": null, "This operation is running interactively.": null, "This operation is running with administrator privileges.": null, "This option WILL cause issues. Any operation incapable of elevating itself WILL FAIL. Install/update/uninstall as administrator will NOT WORK.": null, "This package bundle had some settings that are potentially dangerous, and may be ignored by default.": null, "This package can be updated": "Овој пакет може да се ажурира", "This package can be updated to version {0}": "Овој пакет може да се ажурира на верзија {0}", "This package can be upgraded to version {0}": null, "This package cannot be installed from an elevated context.": null, "This package has no screenshots or is missing the icon? Contrbute to WingetUI by adding the missing icons and screenshots to our open, public database.": null, "This package is already installed": "Овој пакет е веќе инсталиран", "This package is being processed": "Овој пакет се обработува", "This package is not available": null, "This package is on the queue": null, "This process is running with administrator privileges": "Овој процес се извршува со администраторски права", "This project has no connection with the official {0} project — it's completely unofficial.": "Овој проект нема никаква поврзаност со официјалниот {0} проект — целосно е неофицијален.", "This setting is disabled": "Оваа поставка е оневозможена", "This wizard will help you configure and customize WingetUI!": "Овој волшебник ќе ви помогне да го конфигурирате и приспособите WingetUI!", "Toggle search filters pane": "Промени го приказот на панелот за филтри за пребарување", "Translators": "Преведувачи", "Try to kill the processes that refuse to close when requested to": null, "Turning this on enables changing the executable file used to interact with package managers. While this allows finer-grained customization of your install processes, it may also be dangerous": null, "Type here the name and the URL of the source you want to add, separed by a space.": "Внесете го овде името и URL-от на изворот што сакате да го додадете, одделени со празно место.", "Unable to find package": "Не може да се пронајде пакетот", "Unable to load informarion": "Не може да се вчитаат информациите", "UniGetUI collects anonymous usage data in order to improve the user experience.": null, "UniGetUI collects anonymous usage data with the sole purpose of understanding and improving the user experience.": null, "UniGetUI has detected a new desktop shortcut that can be deleted automatically.": null, "UniGetUI has detected the following desktop shortcuts which can be removed automatically on future upgrades": null, "UniGetUI has detected {0} new desktop shortcuts that can be deleted automatically.": null, "UniGetUI is being updated...": null, "UniGetUI is not related to any of the compatible package managers. UniGetUI is an independent project.": null, "UniGetUI on the background and system tray": null, "UniGetUI or some of its components are missing or corrupt.": null, "UniGetUI requires {0} to operate, but it was not found on your system.": null, "UniGetUI startup page:": null, "UniGetUI updater": null, "UniGetUI version {0} is being downloaded.": null, "UniGetUI {0} is ready to be installed.": null, "Uninstall": "Деинста<PERSON><PERSON><PERSON><PERSON><PERSON>", "Uninstall Scoop (and its packages)": "Деинста<PERSON><PERSON><PERSON><PERSON><PERSON> го Scoop (и неговите пакети)", "Uninstall and more": null, "Uninstall and remove data": null, "Uninstall as administrator": "Деинстали<PERSON><PERSON>ј како администратор", "Uninstall canceled by the user!": "Деинсталацијата е откажана од корисникот!", "Uninstall failed": null, "Uninstall options": null, "Uninstall package": "Деинсталирај го пакетот", "Uninstall package, then reinstall it": "Деинсталирај го пакетот, а потоа повторно го инсталирај", "Uninstall package, then update it": "Деинсталирај го пакетот, а потоа го ажурирај", "Uninstall previous versions when updated": null, "Uninstall selected packages": "Деинсталир<PERSON>ј ги избраните пакети", "Uninstall selection": null, "Uninstall succeeded": null, "Uninstall the selected packages with administrator privileges": "Деинсталирај ги избраните пакети со администраторски привилегии", "Uninstallable packages with the origin listed as \"{0}\" are not published on any package manager, so there's no information available to show about them.": null, "Unknown": "Непознато", "Unknown size": null, "Unset or unknown": null, "Up to date": null, "Update": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Update WingetUI automatically": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> го WingetUI автоматски", "Update all": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> сè", "Update and more": null, "Update as administrator": "Аж<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> како администратор", "Update check frequency, automatically install updates, etc.": null, "Update date": "Датум на ажурирање", "Update failed": null, "Update found!": "Пронајдено е ажурирање!", "Update now": null, "Update options": null, "Update package indexes on launch": null, "Update packages automatically": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> ги пакетите автоматски", "Update selected packages": "Ажу<PERSON><PERSON><PERSON><PERSON><PERSON> ги избраните пакети", "Update selected packages with administrator privileges": "Ажурирајте ги избраните пакети со администраторски привилегии", "Update selection": null, "Update succeeded": null, "Update to version {0}": null, "Update to {0} available": "Достапно е ажурирање на {0}", "Update vcpkg's Git portfiles automatically (requires Git installed)": null, "Updates": "Ажурирања", "Updates available!": "Достапни се ажурирања!", "Updates for this package are ignored": "Ажурирањата за овој пакет се игнорираат", "Updates found!": "Најдени се ажурирањата!", "Updates preferences": null, "Updating WingetUI": "Ажур<PERSON>рање на WingetUI", "Url": "URL", "Use Legacy bundled WinGet instead of PowerShell CMDLets": null, "Use a custom icon and screenshot database URL": null, "Use bundled WinGet instead of PowerShell CMDlets": null, "Use bundled WinGet instead of system WinGet": null, "Use installed GSudo instead of UniGetUI Elevator": null, "Use installed GSudo instead of the bundled one": "Користи го инсталираниот GSudo наместо вградениот (потребно е рестартирање на апликацијата)", "Use system Chocolatey": null, "Use system Chocolatey (Needs a restart)": "Користете го системскиот Chocolatey (Потребно е рестартирање)", "Use system Winget (Needs a restart)": "Користете го системскиот Winget (Потребно е рестартирање)", "Use system Winget (System language must be set to english)": null, "Use the WinGet COM API to fetch packages": null, "Use the WinGet PowerShell Module instead of the WinGet COM API": null, "Useful links": null, "User": "Корисник", "User interface preferences": "Поставки за корисничкиот интерфејс", "User | Local": null, "Username": null, "Using WingetUI implies the acceptation of the GNU Lesser General Public License v2.1 License": null, "Using WingetUI implies the acceptation of the MIT License": null, "Vcpkg root was not found. Please define the %VCPKG_ROOT% environment variable or define it from UniGetUI Settings": null, "Vcpkg was not found on your system.": null, "Verbose": null, "Version": "Верзија", "Version to install:": "Верзија за инсталирање:", "Version:": null, "View GitHub Profile": null, "View WingetUI on GitHub": "Погледнете го WingetUI на GitHub", "View WingetUI's source code. From there, you can report bugs or suggest features, or even contribute direcly to The WingetUI Project": null, "View mode:": null, "View on UniGetUI": null, "View page on browser": "Прикажи ја страницата во прелистувач", "View {0} logs": null, "Wait for the device to be connected to the internet before attempting to do tasks that require internet connectivity.": null, "Waiting for other installations to finish...": "Се чека да завршат другите инсталации...", "Waiting for {0} to complete...": null, "Warning": "Предупредување", "Warning!": null, "We are checking for updates.": null, "We could not load detailed information about this package, because it was not found in any of your package sources": null, "We could not load detailed information about this package, because it was not installed from an available package manager.": "Не можевме да вчитаме детални информации за овој пакет, бидејќи не беше инсталиран од достапен менаџер на пакети.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the installer.": "Не можевме да го {action} {package}. Ве молиме обидете се повторно подоцна. Кликнете на „{showDetails}“ за да ги добиете дневниците од инсталатерот.", "We could not {action} {package}. Please try again later. Click on \"{showDetails}\" to get the logs from the uninstaller.": "Не можевме да го {action} {package}. Ве молиме обидете се повторно подоцна. Кликнете на „{showDetails}“ за да ги добиете дневниците од деинсталаторот.", "We couldn't find any package": null, "Welcome to WingetUI": "Добредојдовте во WingetUI", "When batch installing packages from a bundle, install also packages that are already installed": null, "When new shortcuts are detected, delete them automatically instead of showing this dialog.": null, "Which backup do you want to open?": null, "Which package managers do you want to use?": "Кои менаџери на пакети сакате да ги користите?", "Which source do you want to add?": "Кој извор сакате да го додадете?", "While Winget can be used within WingetUI, WingetUI can be used with other package managers, which can be confusing. In the past, WingetUI was designed to work only with Winget, but this is not true anymore, and therefore WingetUI does not represent what this project aims to become.": null, "WinGet could not be repaired": null, "WinGet malfunction detected": null, "WinGet was repaired successfully": null, "WingetUI": null, "WingetUI - Everything is up to date": "WingetUI - Сè е ажурирано", "WingetUI - {0} updates are available": "WingetUI - {0} ажурирања се достапни", "WingetUI - {0} {1}": null, "WingetUI Homepage": null, "WingetUI Homepage - Share this link!": null, "WingetUI License": null, "WingetUI Log": null, "WingetUI Repository": null, "WingetUI Settings": "Поставки за WingetUI", "WingetUI Settings File": "Датотека со поставки на WingetUI", "WingetUI Uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI Version {0}": null, "WingetUI autostart behaviour, application launch settings": "Однесување на автоматско стартување на WingetUI, поставки за стартување на апликацијата", "WingetUI can check if your software has available updates, and install them automatically if you want to": "WingetUI може да провери дали вашиот софтвер има достапни ажурирања и да ги инсталира автоматски ако сакате", "WingetUI display language:": "Јазик на приказ на WingetUI:", "WingetUI has been ran as administrator, which is not recommended. When running WingetUI as administrator, EVERY operation launched from WingetUI will have administrator privileges. You can still use the program, but we highly recommend not running WingetUI with administrator privileges.": null, "WingetUI has been translated to more than 40 languages thanks to the volunteer translators. Thank you 🤝": null, "WingetUI has not been machine translated. The following users have been in charge of the translations:": "WingetUI не е преведен машински. За преводите беа задолжени следните корисници:", "WingetUI is an application that makes managing your software easier, by providing an all-in-one graphical interface for your command-line package managers.": null, "WingetUI is being renamed in order to emphasize the difference between WingetUI (the interface you are using right now) and Winget (a package manager developed by Microsoft with which I am not related)": null, "WingetUI is being updated. When finished, WingetUI will restart itself": "WingetUI се ажурира. Кога ќе заврши, WingetUI ќе се рестартира самиот", "WingetUI is free, and it will be free forever. No ads, no credit card, no premium version. 100% free, forever.": null, "WingetUI log": "WingetUI дневник", "WingetUI tray application preferences": null, "WingetUI uses the following libraries. Without them, WingetUI wouldn't have been possible.": null, "WingetUI version {0} is being downloaded.": null, "WingetUI will become {newname} soon!": null, "WingetUI will not check for updates periodically. They will still be checked at launch, but you won't be warned about them.": "WingetUI нема повремено да проверува за ажурирања. Тие сепак ќе бидат проверени при стартување, но нема да бидете предупредени за нив.", "WingetUI will show a UAC prompt every time a package requires elevation to be installed.": "WingetUI ќе прикаже UAC барање секогаш кога пакетот бара подигање за да се инсталира.", "WingetUI will soon be named {newname}. This will not represent any change in the application. I (the developer) will continue the development of this project as I am doing right now, but under a different name.": null, "WingetUI wouldn't have been possible with the help of our dear contributors. Check out their GitHub profile, WingetUI wouldn't be possible without them!": "WingetUI не би бил возможен без помошта од нашите драги соработници. Проверете ги нивните GitHub профили, WingetUI не би бил возможен без нив!", "WingetUI wouldn't have been possible without the help of the contributors. Thank you all 🥳": null, "WingetUI {0} is ready to be installed.": null, "Write here the process names here, separated by commas (,)": null, "Yes": "Да", "You are logged in as {0} (@{1})": null, "You can change this behavior on UniGetUI security settings.": null, "You can define the commands that will be run before or after this package is installed, updated or uninstalled. They will be run on a command prompt, so CMD scripts will work here.": null, "You have currently version {0} installed": null, "You have installed WingetUI Version {0}": null, "You may lose unsaved data": null, "You may need to install {pm} in order to use it with WingetUI.": null, "You may restart your computer later if you wish": "Може да го рестартирате компјутерот подоцна ако сакате", "You will be prompted only once, and administrator rights will be granted to packages that request them.": "Ќе бидете прашани само еднаш, и администраторски права ќе им бидат доделени на пакетите што ќе ги побараат.", "You will be prompted only once, and every future installation will be elevated automatically.": "Ќе бидете прашани само еднаш, и секоја идна инсталација ќе се подигне автоматски.", "You will likely need to interact with the installer.": null, "[RAN AS ADMINISTRATOR]": null, "buy me a coffee": "купете кафе", "extracted": null, "feature": null, "formerly WingetUI": null, "homepage": "страна", "install": "инстал<PERSON><PERSON><PERSON><PERSON>", "installation": "инсталација", "installed": "инсталирано", "installing": "инсталирање", "library": null, "mandatory": null, "option": null, "optional": null, "uninstall": "деинста<PERSON><PERSON><PERSON><PERSON><PERSON>", "uninstallation": "деинсталација", "uninstalled": "деинсталирано", "uninstalling": "деинстал<PERSON>р<PERSON>ње", "update(noun)": "ажур<PERSON>р<PERSON><PERSON>е", "update(verb)": "а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updated": "ажу<PERSON><PERSON><PERSON><PERSON>но", "updating": "ажур<PERSON>р<PERSON><PERSON>е", "version {0}": null, "{0} Install options are currently locked because {0} follows the default install options.": null, "{0} Uninstallation": "{0} Деинсталација", "{0} aborted": "{0} е прекинат", "{0} can be updated": "{0} може да се ажурира", "{0} can be updated to version {1}": null, "{0} days": "{0} дена", "{0} desktop shortcuts created": null, "{0} failed": "{0} не успеа", "{0} has been installed successfully.": null, "{0} has been installed successfully. It is recommended to restart UniGetUI to finish the installation": null, "{0} has failed, that was a requirement for {1} to be run": null, "{0} homepage": null, "{0} hours": "{0} часа", "{0} installation": "{0} инсталација", "{0} installation options": null, "{0} installer is being downloaded": null, "{0} is being installed": null, "{0} is being uninstalled": null, "{0} is being updated": "{0} се ажурира", "{0} is being updated to version {1}": null, "{0} is disabled": "{0} е оневозможен", "{0} minutes": "{0} минути", "{0} months": null, "{0} packages are being updated": "{0} пакети се ажурираат", "{0} packages can be updated": "{0} пакети може да се ажурираат", "{0} packages found": "Пронајдени се {0} пакети", "{0} packages were found": "Беа пронајдени {0} пакети", "{0} packages were found, {1} of which match the specified filters.": null, "{0} settings": null, "{0} status": null, "{0} succeeded": "{0} успеа", "{0} update": "{0} ажурирање", "{0} updates are available": null, "{0} was {1} successfully!": "{0} беше успешно {1}!", "{0} weeks": null, "{0} years": null, "{0} {1} failed": null, "{package} Installation": null, "{package} Uninstall": null, "{package} Update": null, "{package} could not be installed": null, "{package} could not be uninstalled": null, "{package} could not be updated": null, "{package} installation failed": null, "{package} installer could not be downloaded": null, "{package} installer download": null, "{package} installer was downloaded successfully": null, "{package} uninstall failed": null, "{package} update failed": null, "{package} update failed. Click here for more details.": null, "{package} was installed successfully": null, "{package} was uninstalled successfully": null, "{package} was updated successfully": null, "{pcName} installed packages": null, "{pm} could not be found": "Не можеше да се најде {pm}", "{pm} found: {state}": "Пронајдено {pm}: {state}", "{pm} is disabled": null, "{pm} is enabled and ready to go": null, "{pm} package manager specific preferences": "Поставки специфични за {pm} менаџерот на пакети", "{pm} preferences": "Поставки за {pm}", "{pm} version:": null, "{pm} was not found!": null}